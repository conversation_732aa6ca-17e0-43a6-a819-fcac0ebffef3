
plugins {
    id("com.android.application") version "8.2.2" apply false
    id("org.jetbrains.kotlin.android") version "1.9.22" apply false
//    id("io.sentry.android.gradle") version "4.5.0" apply false
    id("com.google.devtools.ksp") version "1.9.22-1.0.16" apply false
    id("com.kanyun.kace") version "1.9.20-1.2.0" apply false
    id("androidx.room") version "2.6.1" apply false
    id("com.google.dagger.hilt.android") version "2.48" apply false // Hilt 插件
//    id("privacy-sentry-plugin") version "1.3.1" apply false
}

//buildscript {
//    dependencies {
//        // 添加插件依赖
//        classpath ("com.github.allenymt.PrivacySentry:plugin-sentry:1.3.1")
//    }
//}

