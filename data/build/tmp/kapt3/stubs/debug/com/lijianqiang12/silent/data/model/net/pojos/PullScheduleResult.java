package com.lijianqiang12.silent.data.model.net.pojos;

@kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000(\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0000\n\u0002\u0010\t\n\u0000\n\u0002\u0010\u000e\n\u0002\b\u0003\n\u0002\u0010\b\n\u0002\b\u0002\n\u0002\u0010\u000b\n\u0002\bv\b\u0086\b\u0018\u00002\u00020\u0001B\u00c5\u0002\u0012\u0006\u0010\u0002\u001a\u00020\u0003\u0012\u0006\u0010\u0004\u001a\u00020\u0005\u0012\u0006\u0010\u0006\u001a\u00020\u0005\u0012\u0006\u0010\u0007\u001a\u00020\u0005\u0012\u0006\u0010\b\u001a\u00020\t\u0012\u0006\u0010\n\u001a\u00020\t\u0012\u0006\u0010\u000b\u001a\u00020\f\u0012\u0006\u0010\r\u001a\u00020\f\u0012\u0006\u0010\u000e\u001a\u00020\f\u0012\u0006\u0010\u000f\u001a\u00020\f\u0012\u0006\u0010\u0010\u001a\u00020\f\u0012\u0006\u0010\u0011\u001a\u00020\f\u0012\u0006\u0010\u0012\u001a\u00020\f\u0012\u0006\u0010\u0013\u001a\u00020\f\u0012\u0006\u0010\u0014\u001a\u00020\f\u0012\u0006\u0010\u0015\u001a\u00020\t\u0012\u0006\u0010\u0016\u001a\u00020\t\u0012\u0006\u0010\u0017\u001a\u00020\f\u0012\u0006\u0010\u0018\u001a\u00020\f\u0012\u0006\u0010\u0019\u001a\u00020\t\u0012\u0006\u0010\u001a\u001a\u00020\u0005\u0012\u0006\u0010\u001b\u001a\u00020\f\u0012\u0006\u0010\u001c\u001a\u00020\f\u0012\u0006\u0010\u001d\u001a\u00020\t\u0012\u0006\u0010\u001e\u001a\u00020\t\u0012\u0006\u0010\u001f\u001a\u00020\u0003\u0012\u0006\u0010 \u001a\u00020\u0003\u0012\u0006\u0010!\u001a\u00020\f\u0012\u0006\u0010\"\u001a\u00020\f\u0012\u0006\u0010#\u001a\u00020\f\u0012\u0006\u0010$\u001a\u00020\f\u0012\u0006\u0010%\u001a\u00020\f\u0012\u0006\u0010&\u001a\u00020\f\u0012\u0006\u0010\'\u001a\u00020\f\u0012\u0006\u0010(\u001a\u00020\f\u0012\u0006\u0010)\u001a\u00020\u0005\u0012\u0006\u0010*\u001a\u00020\t\u0012\u0006\u0010+\u001a\u00020\t\u0012\u0006\u0010,\u001a\u00020\u0003\u0012\u0006\u0010-\u001a\u00020\t\u00a2\u0006\u0002\u0010.J\t\u0010U\u001a\u00020\u0003H\u00c6\u0003J\t\u0010V\u001a\u00020\fH\u00c6\u0003J\t\u0010W\u001a\u00020\fH\u00c6\u0003J\t\u0010X\u001a\u00020\fH\u00c6\u0003J\t\u0010Y\u001a\u00020\fH\u00c6\u0003J\t\u0010Z\u001a\u00020\fH\u00c6\u0003J\t\u0010[\u001a\u00020\fH\u00c6\u0003J\t\u0010\\\u001a\u00020\tH\u00c6\u0003J\t\u0010]\u001a\u00020\tH\u00c6\u0003J\t\u0010^\u001a\u00020\fH\u00c6\u0003J\t\u0010_\u001a\u00020\fH\u00c6\u0003J\t\u0010`\u001a\u00020\u0005H\u00c6\u0003J\t\u0010a\u001a\u00020\tH\u00c6\u0003J\t\u0010b\u001a\u00020\u0005H\u00c6\u0003J\t\u0010c\u001a\u00020\fH\u00c6\u0003J\t\u0010d\u001a\u00020\fH\u00c6\u0003J\t\u0010e\u001a\u00020\tH\u00c6\u0003J\t\u0010f\u001a\u00020\tH\u00c6\u0003J\t\u0010g\u001a\u00020\u0003H\u00c6\u0003J\t\u0010h\u001a\u00020\u0003H\u00c6\u0003J\t\u0010i\u001a\u00020\fH\u00c6\u0003J\t\u0010j\u001a\u00020\fH\u00c6\u0003J\t\u0010k\u001a\u00020\u0005H\u00c6\u0003J\t\u0010l\u001a\u00020\fH\u00c6\u0003J\t\u0010m\u001a\u00020\fH\u00c6\u0003J\t\u0010n\u001a\u00020\fH\u00c6\u0003J\t\u0010o\u001a\u00020\fH\u00c6\u0003J\t\u0010p\u001a\u00020\fH\u00c6\u0003J\t\u0010q\u001a\u00020\fH\u00c6\u0003J\t\u0010r\u001a\u00020\u0005H\u00c6\u0003J\t\u0010s\u001a\u00020\tH\u00c6\u0003J\t\u0010t\u001a\u00020\tH\u00c6\u0003J\t\u0010u\u001a\u00020\u0003H\u00c6\u0003J\t\u0010v\u001a\u00020\u0005H\u00c6\u0003J\t\u0010w\u001a\u00020\tH\u00c6\u0003J\t\u0010x\u001a\u00020\tH\u00c6\u0003J\t\u0010y\u001a\u00020\tH\u00c6\u0003J\t\u0010z\u001a\u00020\fH\u00c6\u0003J\t\u0010{\u001a\u00020\fH\u00c6\u0003J\t\u0010|\u001a\u00020\fH\u00c6\u0003J\u0099\u0003\u0010}\u001a\u00020\u00002\b\b\u0002\u0010\u0002\u001a\u00020\u00032\b\b\u0002\u0010\u0004\u001a\u00020\u00052\b\b\u0002\u0010\u0006\u001a\u00020\u00052\b\b\u0002\u0010\u0007\u001a\u00020\u00052\b\b\u0002\u0010\b\u001a\u00020\t2\b\b\u0002\u0010\n\u001a\u00020\t2\b\b\u0002\u0010\u000b\u001a\u00020\f2\b\b\u0002\u0010\r\u001a\u00020\f2\b\b\u0002\u0010\u000e\u001a\u00020\f2\b\b\u0002\u0010\u000f\u001a\u00020\f2\b\b\u0002\u0010\u0010\u001a\u00020\f2\b\b\u0002\u0010\u0011\u001a\u00020\f2\b\b\u0002\u0010\u0012\u001a\u00020\f2\b\b\u0002\u0010\u0013\u001a\u00020\f2\b\b\u0002\u0010\u0014\u001a\u00020\f2\b\b\u0002\u0010\u0015\u001a\u00020\t2\b\b\u0002\u0010\u0016\u001a\u00020\t2\b\b\u0002\u0010\u0017\u001a\u00020\f2\b\b\u0002\u0010\u0018\u001a\u00020\f2\b\b\u0002\u0010\u0019\u001a\u00020\t2\b\b\u0002\u0010\u001a\u001a\u00020\u00052\b\b\u0002\u0010\u001b\u001a\u00020\f2\b\b\u0002\u0010\u001c\u001a\u00020\f2\b\b\u0002\u0010\u001d\u001a\u00020\t2\b\b\u0002\u0010\u001e\u001a\u00020\t2\b\b\u0002\u0010\u001f\u001a\u00020\u00032\b\b\u0002\u0010 \u001a\u00020\u00032\b\b\u0002\u0010!\u001a\u00020\f2\b\b\u0002\u0010\"\u001a\u00020\f2\b\b\u0002\u0010#\u001a\u00020\f2\b\b\u0002\u0010$\u001a\u00020\f2\b\b\u0002\u0010%\u001a\u00020\f2\b\b\u0002\u0010&\u001a\u00020\f2\b\b\u0002\u0010\'\u001a\u00020\f2\b\b\u0002\u0010(\u001a\u00020\f2\b\b\u0002\u0010)\u001a\u00020\u00052\b\b\u0002\u0010*\u001a\u00020\t2\b\b\u0002\u0010+\u001a\u00020\t2\b\b\u0002\u0010,\u001a\u00020\u00032\b\b\u0002\u0010-\u001a\u00020\tH\u00c6\u0001J\u0013\u0010~\u001a\u00020\f2\b\u0010\u007f\u001a\u0004\u0018\u00010\u0001H\u00d6\u0003J\n\u0010\u0080\u0001\u001a\u00020\tH\u00d6\u0001J\n\u0010\u0081\u0001\u001a\u00020\u0005H\u00d6\u0001R\u0011\u0010\u001a\u001a\u00020\u0005\u00a2\u0006\b\n\u0000\u001a\u0004\b/\u00100R\u0011\u0010\"\u001a\u00020\f\u00a2\u0006\b\n\u0000\u001a\u0004\b1\u00102R\u0011\u0010\u0019\u001a\u00020\t\u00a2\u0006\b\n\u0000\u001a\u0004\b3\u00104R\u0011\u0010\u0015\u001a\u00020\t\u00a2\u0006\b\n\u0000\u001a\u0004\b5\u00104R\u0011\u0010\u0016\u001a\u00020\t\u00a2\u0006\b\n\u0000\u001a\u0004\b6\u00104R\u0011\u0010 \u001a\u00020\u0003\u00a2\u0006\b\n\u0000\u001a\u0004\b7\u00108R\u0011\u0010(\u001a\u00020\f\u00a2\u0006\b\n\u0000\u001a\u0004\b9\u00102R\u0011\u0010\u001e\u001a\u00020\t\u00a2\u0006\b\n\u0000\u001a\u0004\b:\u00104R\u0011\u0010&\u001a\u00020\f\u00a2\u0006\b\n\u0000\u001a\u0004\b;\u00102R\u0011\u0010\u0012\u001a\u00020\f\u00a2\u0006\b\n\u0000\u001a\u0004\b<\u00102R\u0011\u0010\u0018\u001a\u00020\f\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0018\u00102R\u0011\u0010\u0017\u001a\u00020\f\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0017\u00102R\u0011\u0010\u001b\u001a\u00020\f\u00a2\u0006\b\n\u0000\u001a\u0004\b\u001b\u00102R\u0011\u0010#\u001a\u00020\f\u00a2\u0006\b\n\u0000\u001a\u0004\b#\u00102R\u0011\u0010\u001c\u001a\u00020\f\u00a2\u0006\b\n\u0000\u001a\u0004\b\u001c\u00102R\u0011\u0010$\u001a\u00020\f\u00a2\u0006\b\n\u0000\u001a\u0004\b$\u00102R\u0011\u0010)\u001a\u00020\u0005\u00a2\u0006\b\n\u0000\u001a\u0004\b=\u00100R\u0011\u0010\u000e\u001a\u00020\f\u00a2\u0006\b\n\u0000\u001a\u0004\b>\u00102R\u0011\u0010\u0013\u001a\u00020\f\u00a2\u0006\b\n\u0000\u001a\u0004\b?\u00102R\u0011\u0010\u0007\u001a\u00020\u0005\u00a2\u0006\b\n\u0000\u001a\u0004\b@\u00100R\u0011\u0010\b\u001a\u00020\t\u00a2\u0006\b\n\u0000\u001a\u0004\bA\u00104R\u0011\u0010\n\u001a\u00020\t\u00a2\u0006\b\n\u0000\u001a\u0004\bB\u00104R\u0011\u0010\u001f\u001a\u00020\u0003\u00a2\u0006\b\n\u0000\u001a\u0004\bC\u00108R\u0011\u0010\'\u001a\u00020\f\u00a2\u0006\b\n\u0000\u001a\u0004\bD\u00102R\u0011\u0010\u001d\u001a\u00020\t\u00a2\u0006\b\n\u0000\u001a\u0004\bE\u00104R\u0011\u0010%\u001a\u00020\f\u00a2\u0006\b\n\u0000\u001a\u0004\bF\u00102R\u0011\u0010\r\u001a\u00020\f\u00a2\u0006\b\n\u0000\u001a\u0004\bG\u00102R\u0011\u0010+\u001a\u00020\t\u00a2\u0006\b\n\u0000\u001a\u0004\bH\u00104R\u0011\u0010,\u001a\u00020\u0003\u00a2\u0006\b\n\u0000\u001a\u0004\bI\u00108R\u0011\u0010\u0011\u001a\u00020\f\u00a2\u0006\b\n\u0000\u001a\u0004\bJ\u00102R\u0011\u0010\u0004\u001a\u00020\u0005\u00a2\u0006\b\n\u0000\u001a\u0004\bK\u00100R\u0011\u0010\u0006\u001a\u00020\u0005\u00a2\u0006\b\n\u0000\u001a\u0004\bL\u00100R\u0011\u0010*\u001a\u00020\t\u00a2\u0006\b\n\u0000\u001a\u0004\bM\u00104R\u0011\u0010\u000f\u001a\u00020\f\u00a2\u0006\b\n\u0000\u001a\u0004\bN\u00102R\u0011\u0010\u0014\u001a\u00020\f\u00a2\u0006\b\n\u0000\u001a\u0004\bO\u00102R\u0011\u0010\u0002\u001a\u00020\u0003\u00a2\u0006\b\n\u0000\u001a\u0004\bP\u00108R\u0011\u0010\u000b\u001a\u00020\f\u00a2\u0006\b\n\u0000\u001a\u0004\bQ\u00102R\u0011\u0010-\u001a\u00020\t\u00a2\u0006\b\n\u0000\u001a\u0004\bR\u00104R\u0011\u0010\u0010\u001a\u00020\f\u00a2\u0006\b\n\u0000\u001a\u0004\bS\u00102R\u0011\u0010!\u001a\u00020\f\u00a2\u0006\b\n\u0000\u001a\u0004\bT\u00102\u00a8\u0006\u0082\u0001"}, d2 = {"Lcom/lijianqiang12/silent/data/model/net/pojos/PullScheduleResult;", "", "uuid", "", "title", "", "tomatoIndexId", "scheduleIndexId", "startHour", "", "startMinute", "validate", "", "sunday", "monday", "tuesday", "wednesday", "thursday", "friday", "saturday", "useTomato", "endHour", "endMinute", "isRecycle", "isDenyChange", "denyChangeLength", "bgUrl", "isRemoveNotification", "isSilent", "startVoiceNotify", "endVoiceNotify", "startShakeNotify", "endShakeNotify", "whiteFollowGlobal", "bgUrlFollowGlobal", "isRemoveNotificationFollowGlobal", "isSilentFollowGlobal", "startVoiceNotifyFollowGlobal", "endVoiceNotifyFollowGlobal", "startShakeNotifyFollowGlobal", "endShakeNotifyFollowGlobal", "jumpDate", "trend", "syncState", "syncTime", "version", "(JLjava/lang/String;Ljava/lang/String;Ljava/lang/String;IIZZZZZZZZZIIZZILjava/lang/String;ZZIIJJZZZZZZZZLjava/lang/String;IIJI)V", "getBgUrl", "()Ljava/lang/String;", "getBgUrlFollowGlobal", "()Z", "getDenyChangeLength", "()I", "getEndHour", "getEndMinute", "getEndShakeNotify", "()J", "getEndShakeNotifyFollowGlobal", "getEndVoiceNotify", "getEndVoiceNotifyFollowGlobal", "getFriday", "getJumpDate", "getMonday", "getSaturday", "getScheduleIndexId", "getStartHour", "getStartMinute", "getStartShakeNotify", "getStartShakeNotifyFollowGlobal", "getStartVoiceNotify", "getStartVoiceNotifyFollowGlobal", "getSunday", "getSyncState", "getSyncTime", "getThursday", "getTitle", "getTomatoIndexId", "getTrend", "getTuesday", "getUseTomato", "getUuid", "getValidate", "getVersion", "getWednesday", "getWhiteFollowGlobal", "component1", "component10", "component11", "component12", "component13", "component14", "component15", "component16", "component17", "component18", "component19", "component2", "component20", "component21", "component22", "component23", "component24", "component25", "component26", "component27", "component28", "component29", "component3", "component30", "component31", "component32", "component33", "component34", "component35", "component36", "component37", "component38", "component39", "component4", "component40", "component5", "component6", "component7", "component8", "component9", "copy", "equals", "other", "hashCode", "toString", "data_debug"})
public final class PullScheduleResult {
    private final long uuid = 0L;
    @org.jetbrains.annotations.NotNull()
    private final java.lang.String title = null;
    @org.jetbrains.annotations.NotNull()
    private final java.lang.String tomatoIndexId = null;
    @org.jetbrains.annotations.NotNull()
    private final java.lang.String scheduleIndexId = null;
    private final int startHour = 0;
    private final int startMinute = 0;
    private final boolean validate = false;
    private final boolean sunday = false;
    private final boolean monday = false;
    private final boolean tuesday = false;
    private final boolean wednesday = false;
    private final boolean thursday = false;
    private final boolean friday = false;
    private final boolean saturday = false;
    private final boolean useTomato = false;
    private final int endHour = 0;
    private final int endMinute = 0;
    private final boolean isRecycle = false;
    private final boolean isDenyChange = false;
    private final int denyChangeLength = 0;
    @org.jetbrains.annotations.NotNull()
    private final java.lang.String bgUrl = null;
    private final boolean isRemoveNotification = false;
    private final boolean isSilent = false;
    private final int startVoiceNotify = 0;
    private final int endVoiceNotify = 0;
    private final long startShakeNotify = 0L;
    private final long endShakeNotify = 0L;
    private final boolean whiteFollowGlobal = false;
    private final boolean bgUrlFollowGlobal = false;
    private final boolean isRemoveNotificationFollowGlobal = false;
    private final boolean isSilentFollowGlobal = false;
    private final boolean startVoiceNotifyFollowGlobal = false;
    private final boolean endVoiceNotifyFollowGlobal = false;
    private final boolean startShakeNotifyFollowGlobal = false;
    private final boolean endShakeNotifyFollowGlobal = false;
    @org.jetbrains.annotations.NotNull()
    private final java.lang.String jumpDate = null;
    private final int trend = 0;
    private final int syncState = 0;
    private final long syncTime = 0L;
    private final int version = 0;
    
    public PullScheduleResult(long uuid, @org.jetbrains.annotations.NotNull()
    java.lang.String title, @org.jetbrains.annotations.NotNull()
    java.lang.String tomatoIndexId, @org.jetbrains.annotations.NotNull()
    java.lang.String scheduleIndexId, int startHour, int startMinute, boolean validate, boolean sunday, boolean monday, boolean tuesday, boolean wednesday, boolean thursday, boolean friday, boolean saturday, boolean useTomato, int endHour, int endMinute, boolean isRecycle, boolean isDenyChange, int denyChangeLength, @org.jetbrains.annotations.NotNull()
    java.lang.String bgUrl, boolean isRemoveNotification, boolean isSilent, int startVoiceNotify, int endVoiceNotify, long startShakeNotify, long endShakeNotify, boolean whiteFollowGlobal, boolean bgUrlFollowGlobal, boolean isRemoveNotificationFollowGlobal, boolean isSilentFollowGlobal, boolean startVoiceNotifyFollowGlobal, boolean endVoiceNotifyFollowGlobal, boolean startShakeNotifyFollowGlobal, boolean endShakeNotifyFollowGlobal, @org.jetbrains.annotations.NotNull()
    java.lang.String jumpDate, int trend, int syncState, long syncTime, int version) {
        super();
    }
    
    public final long getUuid() {
        return 0L;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final java.lang.String getTitle() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final java.lang.String getTomatoIndexId() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final java.lang.String getScheduleIndexId() {
        return null;
    }
    
    public final int getStartHour() {
        return 0;
    }
    
    public final int getStartMinute() {
        return 0;
    }
    
    public final boolean getValidate() {
        return false;
    }
    
    public final boolean getSunday() {
        return false;
    }
    
    public final boolean getMonday() {
        return false;
    }
    
    public final boolean getTuesday() {
        return false;
    }
    
    public final boolean getWednesday() {
        return false;
    }
    
    public final boolean getThursday() {
        return false;
    }
    
    public final boolean getFriday() {
        return false;
    }
    
    public final boolean getSaturday() {
        return false;
    }
    
    public final boolean getUseTomato() {
        return false;
    }
    
    public final int getEndHour() {
        return 0;
    }
    
    public final int getEndMinute() {
        return 0;
    }
    
    public final boolean isRecycle() {
        return false;
    }
    
    public final boolean isDenyChange() {
        return false;
    }
    
    public final int getDenyChangeLength() {
        return 0;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final java.lang.String getBgUrl() {
        return null;
    }
    
    public final boolean isRemoveNotification() {
        return false;
    }
    
    public final boolean isSilent() {
        return false;
    }
    
    public final int getStartVoiceNotify() {
        return 0;
    }
    
    public final int getEndVoiceNotify() {
        return 0;
    }
    
    public final long getStartShakeNotify() {
        return 0L;
    }
    
    public final long getEndShakeNotify() {
        return 0L;
    }
    
    public final boolean getWhiteFollowGlobal() {
        return false;
    }
    
    public final boolean getBgUrlFollowGlobal() {
        return false;
    }
    
    public final boolean isRemoveNotificationFollowGlobal() {
        return false;
    }
    
    public final boolean isSilentFollowGlobal() {
        return false;
    }
    
    public final boolean getStartVoiceNotifyFollowGlobal() {
        return false;
    }
    
    public final boolean getEndVoiceNotifyFollowGlobal() {
        return false;
    }
    
    public final boolean getStartShakeNotifyFollowGlobal() {
        return false;
    }
    
    public final boolean getEndShakeNotifyFollowGlobal() {
        return false;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final java.lang.String getJumpDate() {
        return null;
    }
    
    public final int getTrend() {
        return 0;
    }
    
    public final int getSyncState() {
        return 0;
    }
    
    public final long getSyncTime() {
        return 0L;
    }
    
    public final int getVersion() {
        return 0;
    }
    
    public final long component1() {
        return 0L;
    }
    
    public final boolean component10() {
        return false;
    }
    
    public final boolean component11() {
        return false;
    }
    
    public final boolean component12() {
        return false;
    }
    
    public final boolean component13() {
        return false;
    }
    
    public final boolean component14() {
        return false;
    }
    
    public final boolean component15() {
        return false;
    }
    
    public final int component16() {
        return 0;
    }
    
    public final int component17() {
        return 0;
    }
    
    public final boolean component18() {
        return false;
    }
    
    public final boolean component19() {
        return false;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final java.lang.String component2() {
        return null;
    }
    
    public final int component20() {
        return 0;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final java.lang.String component21() {
        return null;
    }
    
    public final boolean component22() {
        return false;
    }
    
    public final boolean component23() {
        return false;
    }
    
    public final int component24() {
        return 0;
    }
    
    public final int component25() {
        return 0;
    }
    
    public final long component26() {
        return 0L;
    }
    
    public final long component27() {
        return 0L;
    }
    
    public final boolean component28() {
        return false;
    }
    
    public final boolean component29() {
        return false;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final java.lang.String component3() {
        return null;
    }
    
    public final boolean component30() {
        return false;
    }
    
    public final boolean component31() {
        return false;
    }
    
    public final boolean component32() {
        return false;
    }
    
    public final boolean component33() {
        return false;
    }
    
    public final boolean component34() {
        return false;
    }
    
    public final boolean component35() {
        return false;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final java.lang.String component36() {
        return null;
    }
    
    public final int component37() {
        return 0;
    }
    
    public final int component38() {
        return 0;
    }
    
    public final long component39() {
        return 0L;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final java.lang.String component4() {
        return null;
    }
    
    public final int component40() {
        return 0;
    }
    
    public final int component5() {
        return 0;
    }
    
    public final int component6() {
        return 0;
    }
    
    public final boolean component7() {
        return false;
    }
    
    public final boolean component8() {
        return false;
    }
    
    public final boolean component9() {
        return false;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final com.lijianqiang12.silent.data.model.net.pojos.PullScheduleResult copy(long uuid, @org.jetbrains.annotations.NotNull()
    java.lang.String title, @org.jetbrains.annotations.NotNull()
    java.lang.String tomatoIndexId, @org.jetbrains.annotations.NotNull()
    java.lang.String scheduleIndexId, int startHour, int startMinute, boolean validate, boolean sunday, boolean monday, boolean tuesday, boolean wednesday, boolean thursday, boolean friday, boolean saturday, boolean useTomato, int endHour, int endMinute, boolean isRecycle, boolean isDenyChange, int denyChangeLength, @org.jetbrains.annotations.NotNull()
    java.lang.String bgUrl, boolean isRemoveNotification, boolean isSilent, int startVoiceNotify, int endVoiceNotify, long startShakeNotify, long endShakeNotify, boolean whiteFollowGlobal, boolean bgUrlFollowGlobal, boolean isRemoveNotificationFollowGlobal, boolean isSilentFollowGlobal, boolean startVoiceNotifyFollowGlobal, boolean endVoiceNotifyFollowGlobal, boolean startShakeNotifyFollowGlobal, boolean endShakeNotifyFollowGlobal, @org.jetbrains.annotations.NotNull()
    java.lang.String jumpDate, int trend, int syncState, long syncTime, int version) {
        return null;
    }
    
    @java.lang.Override()
    public boolean equals(@org.jetbrains.annotations.Nullable()
    java.lang.Object other) {
        return false;
    }
    
    @java.lang.Override()
    public int hashCode() {
        return 0;
    }
    
    @java.lang.Override()
    @org.jetbrains.annotations.NotNull()
    public java.lang.String toString() {
        return null;
    }
}