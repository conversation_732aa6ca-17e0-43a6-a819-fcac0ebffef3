package com.lijianqiang12.silent.data.model.repository;

@kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000\u00b8\u0001\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0010\t\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0010\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0003\n\u0002\u0018\u0002\n\u0002\b\u0003\n\u0002\u0018\u0002\n\u0002\b\u0003\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0010 \n\u0000\n\u0002\u0010!\n\u0002\b\f\n\u0002\u0010\u000e\n\u0002\b\u0007\n\u0002\u0010\b\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0002\b\u0004\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\b\r\n\u0002\u0018\u0002\n\u0002\b\u0003\n\u0002\u0018\u0002\n\u0002\b\u0004\n\u0002\u0018\u0002\n\u0002\b\u0006\n\u0002\u0010\u000b\n\u0002\b\u0004\n\u0002\u0018\u0002\n\u0002\b\u000b\u0018\u0000 q2\u00020\u0001:\u0001qB/\b\u0002\u0012\u0006\u0010\u0002\u001a\u00020\u0003\u0012\u0006\u0010\u0004\u001a\u00020\u0005\u0012\u0006\u0010\u0006\u001a\u00020\u0007\u0012\u0006\u0010\b\u001a\u00020\t\u0012\u0006\u0010\n\u001a\u00020\u000b\u00a2\u0006\u0002\u0010\fJ\u0016\u0010\r\u001a\u00020\u000e2\u0006\u0010\u000f\u001a\u00020\u0010H\u0086@\u00a2\u0006\u0002\u0010\u0011J\u0016\u0010\u0012\u001a\u00020\u00132\u0006\u0010\u0014\u001a\u00020\u0015H\u0086@\u00a2\u0006\u0002\u0010\u0016J\u0016\u0010\u0017\u001a\u00020\u000e2\u0006\u0010\u0018\u001a\u00020\u0019H\u0086@\u00a2\u0006\u0002\u0010\u001aJ\u0016\u0010\u001b\u001a\u00020\u000e2\u0006\u0010\u001c\u001a\u00020\u001dH\u0086@\u00a2\u0006\u0002\u0010\u001eJ\u0016\u0010\u001f\u001a\u00020\u000e2\u0006\u0010 \u001a\u00020!H\u0086@\u00a2\u0006\u0002\u0010\"J\"\u0010#\u001a\b\u0012\u0004\u0012\u00020\u000e0$2\f\u0010%\u001a\b\u0012\u0004\u0012\u00020!0&H\u0086@\u00a2\u0006\u0002\u0010\'J\u0016\u0010(\u001a\u00020\u00132\u0006\u0010\u000f\u001a\u00020\u0010H\u0086@\u00a2\u0006\u0002\u0010\u0011J\u0016\u0010)\u001a\u00020\u00132\u0006\u0010\u0014\u001a\u00020\u0015H\u0086@\u00a2\u0006\u0002\u0010\u0016J\u0016\u0010*\u001a\u00020\u00132\u0006\u0010\u0018\u001a\u00020\u0019H\u0086@\u00a2\u0006\u0002\u0010\u001aJ\u0016\u0010+\u001a\u00020\u00132\u0006\u0010\u001c\u001a\u00020\u001dH\u0086@\u00a2\u0006\u0002\u0010\u001eJ\u0016\u0010,\u001a\u00020\u00132\u0006\u0010 \u001a\u00020!H\u0086@\u00a2\u0006\u0002\u0010\"J\u001c\u0010-\u001a\u00020\u00132\f\u0010%\u001a\b\u0012\u0004\u0012\u00020!0&H\u0086@\u00a2\u0006\u0002\u0010\'J\u0014\u0010.\u001a\b\u0012\u0004\u0012\u00020/0&H\u0086@\u00a2\u0006\u0002\u00100J\u001c\u00101\u001a\b\u0012\u0004\u0012\u00020/0&2\u0006\u00102\u001a\u000203H\u0086@\u00a2\u0006\u0002\u00104J\u0014\u00105\u001a\b\u0012\u0004\u0012\u00020\u00100&H\u0086@\u00a2\u0006\u0002\u00100J\u0014\u00106\u001a\b\u0012\u0004\u0012\u00020\u00190&H\u0086@\u00a2\u0006\u0002\u00100J\u0014\u00107\u001a\b\u0012\u0004\u0012\u00020\u001d0&H\u0086@\u00a2\u0006\u0002\u00100J\u0014\u00108\u001a\b\u0012\u0004\u0012\u00020!0&H\u0086@\u00a2\u0006\u0002\u00100J\u001c\u00109\u001a\b\u0012\u0004\u0012\u00020\u00100&2\u0006\u0010:\u001a\u00020;H\u0086@\u00a2\u0006\u0002\u0010<J\u0012\u0010=\u001a\u000e\u0012\n\u0012\b\u0012\u0004\u0012\u00020\u00100&0>J\u0014\u0010?\u001a\b\u0012\u0004\u0012\u00020!0&H\u0086@\u00a2\u0006\u0002\u00100J\u001a\u0010@\u001a\u000e\u0012\n\u0012\b\u0012\u0004\u0012\u00020!0&0>2\u0006\u0010A\u001a\u00020;J2\u0010B\u001a\u000e\u0012\n\u0012\b\u0012\u0004\u0012\u00020D0$0C2\u0006\u0010E\u001a\u00020;2\u0006\u0010F\u001a\u00020\u000e2\u0006\u0010G\u001a\u00020;H\u0086@\u00a2\u0006\u0002\u0010HJ\u0010\u0010I\u001a\u0004\u0018\u00010\u0010H\u0086@\u00a2\u0006\u0002\u00100J\u0010\u0010J\u001a\u0004\u0018\u00010\u0019H\u0086@\u00a2\u0006\u0002\u00100J\u0010\u0010K\u001a\u0004\u0018\u00010\u001dH\u0086@\u00a2\u0006\u0002\u00100J \u0010L\u001a\u0004\u0018\u00010!2\u0006\u0010M\u001a\u0002032\u0006\u0010N\u001a\u000203H\u0086@\u00a2\u0006\u0002\u0010OJ\u001c\u0010P\u001a\b\u0012\u0004\u0012\u00020\u00190&2\u0006\u0010:\u001a\u00020;H\u0086@\u00a2\u0006\u0002\u0010<J\u0018\u0010Q\u001a\u0004\u0018\u00010R2\u0006\u0010N\u001a\u000203H\u0086@\u00a2\u0006\u0002\u00104J\u001a\u0010S\u001a\u000e\u0012\n\u0012\b\u0012\u0004\u0012\u00020R0&0>2\u0006\u0010A\u001a\u00020;J\u001c\u0010T\u001a\b\u0012\u0004\u0012\u00020\u001d0&2\u0006\u0010:\u001a\u00020;H\u0086@\u00a2\u0006\u0002\u0010<J\u0018\u0010U\u001a\u0004\u0018\u00010V2\u0006\u0010M\u001a\u000203H\u0086@\u00a2\u0006\u0002\u00104J\u0012\u0010W\u001a\u000e\u0012\n\u0012\b\u0012\u0004\u0012\u00020V0&0>J\u0014\u0010X\u001a\b\u0012\u0004\u0012\u00020\u00150&H\u0086@\u00a2\u0006\u0002\u00100J\u000e\u0010Y\u001a\n\u0012\u0006\u0012\u0004\u0018\u00010\u00150>J\u0014\u0010Z\u001a\b\u0012\u0004\u0012\u00020[0CH\u0086@\u00a2\u0006\u0002\u00100J\u001a\u0010\\\u001a\u000e\u0012\n\u0012\b\u0012\u0004\u0012\u00020!0&0>2\u0006\u0010N\u001a\u000203J\u0014\u0010]\u001a\b\u0012\u0004\u0012\u00020!0&2\u0006\u0010N\u001a\u000203J\u001c\u0010^\u001a\b\u0012\u0004\u0012\u00020!0&2\u0006\u0010:\u001a\u00020;H\u0086@\u00a2\u0006\u0002\u0010<J\u001a\u0010_\u001a\u000e\u0012\n\u0012\b\u0012\u0004\u0012\u00020!0&0>2\u0006\u0010M\u001a\u000203J\u0014\u0010`\u001a\b\u0012\u0004\u0012\u00020!0&2\u0006\u0010M\u001a\u000203J.\u0010a\u001a\u00020b2\u0006\u0010M\u001a\u0002032\u0006\u0010N\u001a\u0002032\u0006\u0010c\u001a\u0002032\u0006\u0010d\u001a\u000203H\u0086@\u00a2\u0006\u0002\u0010eJ\u001c\u0010f\u001a\b\u0012\u0004\u0012\u00020g0C2\u0006\u0010h\u001a\u000203H\u0086@\u00a2\u0006\u0002\u00104J\u0016\u0010i\u001a\u00020\u00132\u0006\u0010\u000f\u001a\u00020\u0010H\u0086@\u00a2\u0006\u0002\u0010\u0011J\u0016\u0010j\u001a\u00020\u00132\u0006\u0010\u0014\u001a\u00020\u0015H\u0086@\u00a2\u0006\u0002\u0010\u0016J\u0016\u0010k\u001a\u00020\u00132\u0006\u0010\u0018\u001a\u00020\u0019H\u0086@\u00a2\u0006\u0002\u0010\u001aJ\u0016\u0010l\u001a\u00020\u00132\u0006\u0010\u001c\u001a\u00020\u001dH\u0086@\u00a2\u0006\u0002\u0010\u001eJ\u0016\u0010m\u001a\u00020\u00132\u0006\u0010 \u001a\u00020!H\u0086@\u00a2\u0006\u0002\u0010\"J\u001c\u0010n\u001a\b\u0012\u0004\u0012\u00020\u00010C2\u0006\u0010o\u001a\u00020;H\u0086@\u00a2\u0006\u0002\u0010<J\u001c\u0010p\u001a\b\u0012\u0004\u0012\u00020\u00010C2\u0006\u0010o\u001a\u00020;H\u0086@\u00a2\u0006\u0002\u0010<R\u000e\u0010\u0002\u001a\u00020\u0003X\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u000e\u0010\n\u001a\u00020\u000bX\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0006\u001a\u00020\u0007X\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0004\u001a\u00020\u0005X\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u000e\u0010\b\u001a\u00020\tX\u0082\u0004\u00a2\u0006\u0002\n\u0000\u00a8\u0006r"}, d2 = {"Lcom/lijianqiang12/silent/data/model/repository/LockRepository;", "", "fastDao", "Lcom/lijianqiang12/silent/data/model/db/FastDao;", "tomatoDao", "Lcom/lijianqiang12/silent/data/model/db/TomatoDao;", "scheduleDao", "Lcom/lijianqiang12/silent/data/model/db/ScheduleDao;", "whiteAppDao", "Lcom/lijianqiang12/silent/data/model/db/WhiteAppDao;", "lockHistoryDao", "Lcom/lijianqiang12/silent/data/model/db/LockHistoryDao;", "(Lcom/lijianqiang12/silent/data/model/db/FastDao;Lcom/lijianqiang12/silent/data/model/db/TomatoDao;Lcom/lijianqiang12/silent/data/model/db/ScheduleDao;Lcom/lijianqiang12/silent/data/model/db/WhiteAppDao;Lcom/lijianqiang12/silent/data/model/db/LockHistoryDao;)V", "createFast", "", "fast", "Lcom/lijianqiang12/silent/data/model/db/Fast;", "(Lcom/lijianqiang12/silent/data/model/db/Fast;Lkotlin/coroutines/Continuation;)Ljava/lang/Object;", "createLockHistory", "", "lockHistory", "Lcom/lijianqiang12/silent/data/model/db/LockHistory;", "(Lcom/lijianqiang12/silent/data/model/db/LockHistory;Lkotlin/coroutines/Continuation;)Ljava/lang/Object;", "createSchedule", "schedule", "Lcom/lijianqiang12/silent/data/model/db/Schedule;", "(Lcom/lijianqiang12/silent/data/model/db/Schedule;Lkotlin/coroutines/Continuation;)Ljava/lang/Object;", "createTomato", "tomato", "Lcom/lijianqiang12/silent/data/model/db/Tomato;", "(Lcom/lijianqiang12/silent/data/model/db/Tomato;Lkotlin/coroutines/Continuation;)Ljava/lang/Object;", "createWhiteApp", "whiteApp", "Lcom/lijianqiang12/silent/data/model/db/WhiteApp;", "(Lcom/lijianqiang12/silent/data/model/db/WhiteApp;Lkotlin/coroutines/Continuation;)Ljava/lang/Object;", "createWhiteApps", "", "whiteApps", "", "(Ljava/util/List;Lkotlin/coroutines/Continuation;)Ljava/lang/Object;", "deleteFast", "deleteLockHistory", "deleteSchedule", "deleteTomato", "deleteWhiteApp", "deleteWhiteApps", "getAllAppInfo", "error/NonExistentClass", "(Lkotlin/coroutines/Continuation;)Ljava/lang/Object;", "getAllAppInfoWithText", "text", "", "(Ljava/lang/String;Lkotlin/coroutines/Continuation;)Ljava/lang/Object;", "getAllFastList", "getAllScheduleList", "getAllTomatoList", "getAllWhiteAppList", "getFastWithState", "state", "", "(ILkotlin/coroutines/Continuation;)Ljava/lang/Object;", "getFasts", "Landroidx/lifecycle/LiveData;", "getGlobalWhiteAppList", "getGlobalWhiteApps", "userId", "getImages", "Lcom/lijianqiang12/silent/data/model/net/pojos/ApiResponse;", "Lcom/lijianqiang12/silent/data/model/net/pojos/LockBg;", "style", "lastId", "limit", "(IJILkotlin/coroutines/Continuation;)Ljava/lang/Object;", "getLastFast", "getLastSchedule", "getLastTomato", "getLastWhiteApp", "tomatoId", "scheduleId", "(Ljava/lang/String;Ljava/lang/String;Lkotlin/coroutines/Continuation;)Ljava/lang/Object;", "getScheduleWithState", "getScheduleWithSub", "Lcom/lijianqiang12/silent/data/model/db/ScheduleWithSub;", "getSchedulesWithSub", "getTomatoWithState", "getTomatoWithSub", "Lcom/lijianqiang12/silent/data/model/db/TomatoWithSub;", "getTomatoesWithSub", "getUnUploadHistory", "getUnfinishedLockHistory", "getWellKnowWord", "Lcom/lijianqiang12/silent/data/model/net/pojos/WellKnowWord;", "getWhiteAppsWithScheduleId", "getWhiteAppsWithScheduleIdAtOnce", "getWhiteAppsWithState", "getWhiteAppsWithTomatoId", "getWhiteAppsWithTomatoIdAtOnce", "isWhiteAppExist", "", "pkg", "mainActivity", "(Ljava/lang/String;Ljava/lang/String;Ljava/lang/String;Ljava/lang/String;Lkotlin/coroutines/Continuation;)Ljava/lang/Object;", "refreshForceUnlockPwd", "Lcom/lijianqiang12/silent/data/model/net/pojos/ForceUnlockPwd;", "pwd", "updateFast", "updateLockHistory", "updateSchedule", "updateTomato", "updateWhiteApp", "wellKnowWordShare", "wordId", "wellKnowWordStar", "Companion", "data_debug"})
public final class LockRepository {
    @org.jetbrains.annotations.NotNull()
    private final com.lijianqiang12.silent.data.model.db.FastDao fastDao = null;
    @org.jetbrains.annotations.NotNull()
    private final com.lijianqiang12.silent.data.model.db.TomatoDao tomatoDao = null;
    @org.jetbrains.annotations.NotNull()
    private final com.lijianqiang12.silent.data.model.db.ScheduleDao scheduleDao = null;
    @org.jetbrains.annotations.NotNull()
    private final com.lijianqiang12.silent.data.model.db.WhiteAppDao whiteAppDao = null;
    @org.jetbrains.annotations.NotNull()
    private final com.lijianqiang12.silent.data.model.db.LockHistoryDao lockHistoryDao = null;
    @kotlin.jvm.Volatile()
    @org.jetbrains.annotations.Nullable()
    private static volatile com.lijianqiang12.silent.data.model.repository.LockRepository instance;
    @org.jetbrains.annotations.NotNull()
    public static final com.lijianqiang12.silent.data.model.repository.LockRepository.Companion Companion = null;
    
    private LockRepository(com.lijianqiang12.silent.data.model.db.FastDao fastDao, com.lijianqiang12.silent.data.model.db.TomatoDao tomatoDao, com.lijianqiang12.silent.data.model.db.ScheduleDao scheduleDao, com.lijianqiang12.silent.data.model.db.WhiteAppDao whiteAppDao, com.lijianqiang12.silent.data.model.db.LockHistoryDao lockHistoryDao) {
        super();
    }
    
    @org.jetbrains.annotations.Nullable()
    public final java.lang.Object getWellKnowWord(@org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super com.lijianqiang12.silent.data.model.net.pojos.ApiResponse<com.lijianqiang12.silent.data.model.net.pojos.WellKnowWord>> $completion) {
        return null;
    }
    
    @org.jetbrains.annotations.Nullable()
    public final java.lang.Object wellKnowWordStar(int wordId, @org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super com.lijianqiang12.silent.data.model.net.pojos.ApiResponse<? extends java.lang.Object>> $completion) {
        return null;
    }
    
    @org.jetbrains.annotations.Nullable()
    public final java.lang.Object wellKnowWordShare(int wordId, @org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super com.lijianqiang12.silent.data.model.net.pojos.ApiResponse<? extends java.lang.Object>> $completion) {
        return null;
    }
    
    @org.jetbrains.annotations.Nullable()
    public final java.lang.Object getAllWhiteAppList(@org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super java.util.List<com.lijianqiang12.silent.data.model.db.WhiteApp>> $completion) {
        return null;
    }
    
    @org.jetbrains.annotations.Nullable()
    public final java.lang.Object getWhiteAppsWithState(int state, @org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super java.util.List<com.lijianqiang12.silent.data.model.db.WhiteApp>> $completion) {
        return null;
    }
    
    @org.jetbrains.annotations.Nullable()
    public final java.lang.Object getAllFastList(@org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super java.util.List<com.lijianqiang12.silent.data.model.db.Fast>> $completion) {
        return null;
    }
    
    @org.jetbrains.annotations.Nullable()
    public final java.lang.Object getFastWithState(int state, @org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super java.util.List<com.lijianqiang12.silent.data.model.db.Fast>> $completion) {
        return null;
    }
    
    @org.jetbrains.annotations.Nullable()
    public final java.lang.Object getAllTomatoList(@org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super java.util.List<com.lijianqiang12.silent.data.model.db.Tomato>> $completion) {
        return null;
    }
    
    @org.jetbrains.annotations.Nullable()
    public final java.lang.Object getTomatoWithState(int state, @org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super java.util.List<com.lijianqiang12.silent.data.model.db.Tomato>> $completion) {
        return null;
    }
    
    @org.jetbrains.annotations.Nullable()
    public final java.lang.Object getAllScheduleList(@org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super java.util.List<com.lijianqiang12.silent.data.model.db.Schedule>> $completion) {
        return null;
    }
    
    @org.jetbrains.annotations.Nullable()
    public final java.lang.Object getScheduleWithState(int state, @org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super java.util.List<com.lijianqiang12.silent.data.model.db.Schedule>> $completion) {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final androidx.lifecycle.LiveData<java.util.List<com.lijianqiang12.silent.data.model.db.Fast>> getFasts() {
        return null;
    }
    
    @org.jetbrains.annotations.Nullable()
    public final java.lang.Object createFast(@org.jetbrains.annotations.NotNull()
    com.lijianqiang12.silent.data.model.db.Fast fast, @org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super java.lang.Long> $completion) {
        return null;
    }
    
    @org.jetbrains.annotations.Nullable()
    public final java.lang.Object updateFast(@org.jetbrains.annotations.NotNull()
    com.lijianqiang12.silent.data.model.db.Fast fast, @org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super kotlin.Unit> $completion) {
        return null;
    }
    
    @org.jetbrains.annotations.Nullable()
    public final java.lang.Object deleteFast(@org.jetbrains.annotations.NotNull()
    com.lijianqiang12.silent.data.model.db.Fast fast, @org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super kotlin.Unit> $completion) {
        return null;
    }
    
    @org.jetbrains.annotations.Nullable()
    public final java.lang.Object getLastFast(@org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super com.lijianqiang12.silent.data.model.db.Fast> $completion) {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final androidx.lifecycle.LiveData<java.util.List<com.lijianqiang12.silent.data.model.db.TomatoWithSub>> getTomatoesWithSub() {
        return null;
    }
    
    @org.jetbrains.annotations.Nullable()
    public final java.lang.Object getTomatoWithSub(@org.jetbrains.annotations.NotNull()
    java.lang.String tomatoId, @org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super com.lijianqiang12.silent.data.model.db.TomatoWithSub> $completion) {
        return null;
    }
    
    @org.jetbrains.annotations.Nullable()
    public final java.lang.Object createTomato(@org.jetbrains.annotations.NotNull()
    com.lijianqiang12.silent.data.model.db.Tomato tomato, @org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super java.lang.Long> $completion) {
        return null;
    }
    
    @org.jetbrains.annotations.Nullable()
    public final java.lang.Object updateTomato(@org.jetbrains.annotations.NotNull()
    com.lijianqiang12.silent.data.model.db.Tomato tomato, @org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super kotlin.Unit> $completion) {
        return null;
    }
    
    @org.jetbrains.annotations.Nullable()
    public final java.lang.Object deleteTomato(@org.jetbrains.annotations.NotNull()
    com.lijianqiang12.silent.data.model.db.Tomato tomato, @org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super kotlin.Unit> $completion) {
        return null;
    }
    
    @org.jetbrains.annotations.Nullable()
    public final java.lang.Object getLastTomato(@org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super com.lijianqiang12.silent.data.model.db.Tomato> $completion) {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final androidx.lifecycle.LiveData<java.util.List<com.lijianqiang12.silent.data.model.db.ScheduleWithSub>> getSchedulesWithSub(int userId) {
        return null;
    }
    
    @org.jetbrains.annotations.Nullable()
    public final java.lang.Object getScheduleWithSub(@org.jetbrains.annotations.NotNull()
    java.lang.String scheduleId, @org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super com.lijianqiang12.silent.data.model.db.ScheduleWithSub> $completion) {
        return null;
    }
    
    @org.jetbrains.annotations.Nullable()
    public final java.lang.Object createSchedule(@org.jetbrains.annotations.NotNull()
    com.lijianqiang12.silent.data.model.db.Schedule schedule, @org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super java.lang.Long> $completion) {
        return null;
    }
    
    @org.jetbrains.annotations.Nullable()
    public final java.lang.Object updateSchedule(@org.jetbrains.annotations.NotNull()
    com.lijianqiang12.silent.data.model.db.Schedule schedule, @org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super kotlin.Unit> $completion) {
        return null;
    }
    
    @org.jetbrains.annotations.Nullable()
    public final java.lang.Object deleteSchedule(@org.jetbrains.annotations.NotNull()
    com.lijianqiang12.silent.data.model.db.Schedule schedule, @org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super kotlin.Unit> $completion) {
        return null;
    }
    
    @org.jetbrains.annotations.Nullable()
    public final java.lang.Object getLastSchedule(@org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super com.lijianqiang12.silent.data.model.db.Schedule> $completion) {
        return null;
    }
    
    @org.jetbrains.annotations.Nullable()
    public final java.lang.Object getGlobalWhiteAppList(@org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super java.util.List<com.lijianqiang12.silent.data.model.db.WhiteApp>> $completion) {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final androidx.lifecycle.LiveData<java.util.List<com.lijianqiang12.silent.data.model.db.WhiteApp>> getGlobalWhiteApps(int userId) {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final androidx.lifecycle.LiveData<java.util.List<com.lijianqiang12.silent.data.model.db.WhiteApp>> getWhiteAppsWithTomatoId(@org.jetbrains.annotations.NotNull()
    java.lang.String tomatoId) {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final androidx.lifecycle.LiveData<java.util.List<com.lijianqiang12.silent.data.model.db.WhiteApp>> getWhiteAppsWithScheduleId(@org.jetbrains.annotations.NotNull()
    java.lang.String scheduleId) {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final java.util.List<com.lijianqiang12.silent.data.model.db.WhiteApp> getWhiteAppsWithTomatoIdAtOnce(@org.jetbrains.annotations.NotNull()
    java.lang.String tomatoId) {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final java.util.List<com.lijianqiang12.silent.data.model.db.WhiteApp> getWhiteAppsWithScheduleIdAtOnce(@org.jetbrains.annotations.NotNull()
    java.lang.String scheduleId) {
        return null;
    }
    
    @org.jetbrains.annotations.Nullable()
    public final java.lang.Object createWhiteApp(@org.jetbrains.annotations.NotNull()
    com.lijianqiang12.silent.data.model.db.WhiteApp whiteApp, @org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super java.lang.Long> $completion) {
        return null;
    }
    
    @org.jetbrains.annotations.Nullable()
    public final java.lang.Object createWhiteApps(@org.jetbrains.annotations.NotNull()
    java.util.List<com.lijianqiang12.silent.data.model.db.WhiteApp> whiteApps, @org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super java.util.List<java.lang.Long>> $completion) {
        return null;
    }
    
    @org.jetbrains.annotations.Nullable()
    public final java.lang.Object deleteWhiteApp(@org.jetbrains.annotations.NotNull()
    com.lijianqiang12.silent.data.model.db.WhiteApp whiteApp, @org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super kotlin.Unit> $completion) {
        return null;
    }
    
    @org.jetbrains.annotations.Nullable()
    public final java.lang.Object updateWhiteApp(@org.jetbrains.annotations.NotNull()
    com.lijianqiang12.silent.data.model.db.WhiteApp whiteApp, @org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super kotlin.Unit> $completion) {
        return null;
    }
    
    @org.jetbrains.annotations.Nullable()
    public final java.lang.Object deleteWhiteApps(@org.jetbrains.annotations.NotNull()
    java.util.List<com.lijianqiang12.silent.data.model.db.WhiteApp> whiteApps, @org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super kotlin.Unit> $completion) {
        return null;
    }
    
    @org.jetbrains.annotations.Nullable()
    public final java.lang.Object getLastWhiteApp(@org.jetbrains.annotations.NotNull()
    java.lang.String tomatoId, @org.jetbrains.annotations.NotNull()
    java.lang.String scheduleId, @org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super com.lijianqiang12.silent.data.model.db.WhiteApp> $completion) {
        return null;
    }
    
    @org.jetbrains.annotations.Nullable()
    public final java.lang.Object isWhiteAppExist(@org.jetbrains.annotations.NotNull()
    java.lang.String tomatoId, @org.jetbrains.annotations.NotNull()
    java.lang.String scheduleId, @org.jetbrains.annotations.NotNull()
    java.lang.String pkg, @org.jetbrains.annotations.NotNull()
    java.lang.String mainActivity, @org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super java.lang.Boolean> $completion) {
        return null;
    }
    
    @org.jetbrains.annotations.Nullable()
    public final java.lang.Object getAllAppInfo(@org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super java.util.List<error.NonExistentClass>> $completion) {
        return null;
    }
    
    @org.jetbrains.annotations.Nullable()
    public final java.lang.Object getAllAppInfoWithText(@org.jetbrains.annotations.NotNull()
    java.lang.String text, @org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super java.util.List<error.NonExistentClass>> $completion) {
        return null;
    }
    
    @org.jetbrains.annotations.Nullable()
    public final java.lang.Object getUnUploadHistory(@org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super java.util.List<com.lijianqiang12.silent.data.model.db.LockHistory>> $completion) {
        return null;
    }
    
    @org.jetbrains.annotations.Nullable()
    public final java.lang.Object createLockHistory(@org.jetbrains.annotations.NotNull()
    com.lijianqiang12.silent.data.model.db.LockHistory lockHistory, @org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super kotlin.Unit> $completion) {
        return null;
    }
    
    @org.jetbrains.annotations.Nullable()
    public final java.lang.Object updateLockHistory(@org.jetbrains.annotations.NotNull()
    com.lijianqiang12.silent.data.model.db.LockHistory lockHistory, @org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super kotlin.Unit> $completion) {
        return null;
    }
    
    @org.jetbrains.annotations.Nullable()
    public final java.lang.Object deleteLockHistory(@org.jetbrains.annotations.NotNull()
    com.lijianqiang12.silent.data.model.db.LockHistory lockHistory, @org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super kotlin.Unit> $completion) {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final androidx.lifecycle.LiveData<com.lijianqiang12.silent.data.model.db.LockHistory> getUnfinishedLockHistory() {
        return null;
    }
    
    @org.jetbrains.annotations.Nullable()
    public final java.lang.Object getImages(int style, long lastId, int limit, @org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super com.lijianqiang12.silent.data.model.net.pojos.ApiResponse<? extends java.util.List<com.lijianqiang12.silent.data.model.net.pojos.LockBg>>> $completion) {
        return null;
    }
    
    @org.jetbrains.annotations.Nullable()
    public final java.lang.Object refreshForceUnlockPwd(@org.jetbrains.annotations.NotNull()
    java.lang.String pwd, @org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super com.lijianqiang12.silent.data.model.net.pojos.ApiResponse<com.lijianqiang12.silent.data.model.net.pojos.ForceUnlockPwd>> $completion) {
        return null;
    }
    
    @kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u00002\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\b\u0086\u0003\u0018\u00002\u00020\u0001B\u0007\b\u0002\u00a2\u0006\u0002\u0010\u0002J.\u0010\u0005\u001a\u00020\u00042\u0006\u0010\u0006\u001a\u00020\u00072\u0006\u0010\b\u001a\u00020\t2\u0006\u0010\n\u001a\u00020\u000b2\u0006\u0010\f\u001a\u00020\r2\u0006\u0010\u000e\u001a\u00020\u000fR\u0010\u0010\u0003\u001a\u0004\u0018\u00010\u0004X\u0082\u000e\u00a2\u0006\u0002\n\u0000\u00a8\u0006\u0010"}, d2 = {"Lcom/lijianqiang12/silent/data/model/repository/LockRepository$Companion;", "", "()V", "instance", "Lcom/lijianqiang12/silent/data/model/repository/LockRepository;", "getInstance", "fastDao", "Lcom/lijianqiang12/silent/data/model/db/FastDao;", "tomatoDao", "Lcom/lijianqiang12/silent/data/model/db/TomatoDao;", "scheduleDao", "Lcom/lijianqiang12/silent/data/model/db/ScheduleDao;", "whiteAppDao", "Lcom/lijianqiang12/silent/data/model/db/WhiteAppDao;", "lockHistoryDao", "Lcom/lijianqiang12/silent/data/model/db/LockHistoryDao;", "data_debug"})
    public static final class Companion {
        
        private Companion() {
            super();
        }
        
        @org.jetbrains.annotations.NotNull()
        public final com.lijianqiang12.silent.data.model.repository.LockRepository getInstance(@org.jetbrains.annotations.NotNull()
        com.lijianqiang12.silent.data.model.db.FastDao fastDao, @org.jetbrains.annotations.NotNull()
        com.lijianqiang12.silent.data.model.db.TomatoDao tomatoDao, @org.jetbrains.annotations.NotNull()
        com.lijianqiang12.silent.data.model.db.ScheduleDao scheduleDao, @org.jetbrains.annotations.NotNull()
        com.lijianqiang12.silent.data.model.db.WhiteAppDao whiteAppDao, @org.jetbrains.annotations.NotNull()
        com.lijianqiang12.silent.data.model.db.LockHistoryDao lockHistoryDao) {
            return null;
        }
    }
}