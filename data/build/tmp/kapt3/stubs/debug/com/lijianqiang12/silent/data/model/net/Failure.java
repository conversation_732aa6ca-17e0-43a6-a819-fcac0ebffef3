package com.lijianqiang12.silent.data.model.net;

@kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000*\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0006\n\u0002\u0010\u000b\n\u0000\n\u0002\u0010\u0000\n\u0000\n\u0002\u0010\b\n\u0000\n\u0002\u0010\u000e\n\u0000\b\u0086\b\u0018\u00002\u00020\u0001B\r\u0012\u0006\u0010\u0002\u001a\u00020\u0003\u00a2\u0006\u0002\u0010\u0004J\t\u0010\u0007\u001a\u00020\u0003H\u00c6\u0003J\u0013\u0010\b\u001a\u00020\u00002\b\b\u0002\u0010\u0002\u001a\u00020\u0003H\u00c6\u0001J\u0013\u0010\t\u001a\u00020\n2\b\u0010\u000b\u001a\u0004\u0018\u00010\fH\u00d6\u0003J\t\u0010\r\u001a\u00020\u000eH\u00d6\u0001J\t\u0010\u000f\u001a\u00020\u0010H\u00d6\u0001R\u0011\u0010\u0002\u001a\u00020\u0003\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0005\u0010\u0006\u00a8\u0006\u0011"}, d2 = {"Lcom/lijianqiang12/silent/data/model/net/Failure;", "Lcom/lijianqiang12/silent/data/model/net/HttpResponse;", "error", "Lcom/lijianqiang12/silent/data/model/net/HttpError;", "(Lcom/lijianqiang12/silent/data/model/net/HttpError;)V", "getError", "()Lcom/lijianqiang12/silent/data/model/net/HttpError;", "component1", "copy", "equals", "", "other", "", "hashCode", "", "toString", "", "data_debug"})
public final class Failure extends com.lijianqiang12.silent.data.model.net.HttpResponse {
    @org.jetbrains.annotations.NotNull()
    private final com.lijianqiang12.silent.data.model.net.HttpError error = null;
    
    public Failure(@org.jetbrains.annotations.NotNull()
    com.lijianqiang12.silent.data.model.net.HttpError error) {
    }
    
    @org.jetbrains.annotations.NotNull()
    public final com.lijianqiang12.silent.data.model.net.HttpError getError() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final com.lijianqiang12.silent.data.model.net.HttpError component1() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final com.lijianqiang12.silent.data.model.net.Failure copy(@org.jetbrains.annotations.NotNull()
    com.lijianqiang12.silent.data.model.net.HttpError error) {
        return null;
    }
    
    @java.lang.Override()
    public boolean equals(@org.jetbrains.annotations.Nullable()
    java.lang.Object other) {
        return false;
    }
    
    @java.lang.Override()
    public int hashCode() {
        return 0;
    }
    
    @java.lang.Override()
    @org.jetbrains.annotations.NotNull()
    public java.lang.String toString() {
        return null;
    }
}