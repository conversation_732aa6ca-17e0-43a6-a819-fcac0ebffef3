package com.lijianqiang12.silent.data.model.net.pojos;

@kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000(\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0000\n\u0002\u0010\u000e\n\u0000\n\u0002\u0018\u0002\n\u0002\b\r\n\u0002\u0010\u000b\n\u0002\b\u0002\n\u0002\u0010\b\n\u0002\b\u0002\b\u0086\b\u0018\u00002\u00020\u0001B\u0019\u0012\b\b\u0002\u0010\u0002\u001a\u00020\u0003\u0012\b\b\u0002\u0010\u0004\u001a\u00020\u0005\u00a2\u0006\u0002\u0010\u0006J\t\u0010\u000f\u001a\u00020\u0003H\u00c6\u0003J\t\u0010\u0010\u001a\u00020\u0005H\u00c6\u0003J\u001d\u0010\u0011\u001a\u00020\u00002\b\b\u0002\u0010\u0002\u001a\u00020\u00032\b\b\u0002\u0010\u0004\u001a\u00020\u0005H\u00c6\u0001J\u0013\u0010\u0012\u001a\u00020\u00132\b\u0010\u0014\u001a\u0004\u0018\u00010\u0001H\u00d6\u0003J\t\u0010\u0015\u001a\u00020\u0016H\u00d6\u0001J\t\u0010\u0017\u001a\u00020\u0003H\u00d6\u0001R\u001a\u0010\u0002\u001a\u00020\u0003X\u0086\u000e\u00a2\u0006\u000e\n\u0000\u001a\u0004\b\u0007\u0010\b\"\u0004\b\t\u0010\nR\u001a\u0010\u0004\u001a\u00020\u0005X\u0086\u000e\u00a2\u0006\u000e\n\u0000\u001a\u0004\b\u000b\u0010\f\"\u0004\b\r\u0010\u000e\u00a8\u0006\u0018"}, d2 = {"Lcom/lijianqiang12/silent/data/model/net/pojos/AllQQUserInfo;", "", "openid", "", "qqUserInfo", "Lcom/lijianqiang12/silent/data/model/net/pojos/QQUserInfo;", "(Ljava/lang/String;Lcom/lijianqiang12/silent/data/model/net/pojos/QQUserInfo;)V", "getOpenid", "()Ljava/lang/String;", "setOpenid", "(Ljava/lang/String;)V", "getQqUserInfo", "()Lcom/lijianqiang12/silent/data/model/net/pojos/QQUserInfo;", "setQqUserInfo", "(Lcom/lijianqiang12/silent/data/model/net/pojos/QQUserInfo;)V", "component1", "component2", "copy", "equals", "", "other", "hashCode", "", "toString", "data_debug"})
public final class AllQQUserInfo {
    @org.jetbrains.annotations.NotNull()
    private java.lang.String openid;
    @org.jetbrains.annotations.NotNull()
    private com.lijianqiang12.silent.data.model.net.pojos.QQUserInfo qqUserInfo;
    
    public AllQQUserInfo(@org.jetbrains.annotations.NotNull()
    java.lang.String openid, @org.jetbrains.annotations.NotNull()
    com.lijianqiang12.silent.data.model.net.pojos.QQUserInfo qqUserInfo) {
        super();
    }
    
    @org.jetbrains.annotations.NotNull()
    public final java.lang.String getOpenid() {
        return null;
    }
    
    public final void setOpenid(@org.jetbrains.annotations.NotNull()
    java.lang.String p0) {
    }
    
    @org.jetbrains.annotations.NotNull()
    public final com.lijianqiang12.silent.data.model.net.pojos.QQUserInfo getQqUserInfo() {
        return null;
    }
    
    public final void setQqUserInfo(@org.jetbrains.annotations.NotNull()
    com.lijianqiang12.silent.data.model.net.pojos.QQUserInfo p0) {
    }
    
    public AllQQUserInfo() {
        super();
    }
    
    @org.jetbrains.annotations.NotNull()
    public final java.lang.String component1() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final com.lijianqiang12.silent.data.model.net.pojos.QQUserInfo component2() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final com.lijianqiang12.silent.data.model.net.pojos.AllQQUserInfo copy(@org.jetbrains.annotations.NotNull()
    java.lang.String openid, @org.jetbrains.annotations.NotNull()
    com.lijianqiang12.silent.data.model.net.pojos.QQUserInfo qqUserInfo) {
        return null;
    }
    
    @java.lang.Override()
    public boolean equals(@org.jetbrains.annotations.Nullable()
    java.lang.Object other) {
        return false;
    }
    
    @java.lang.Override()
    public int hashCode() {
        return 0;
    }
    
    @java.lang.Override()
    @org.jetbrains.annotations.NotNull()
    public java.lang.String toString() {
        return null;
    }
}