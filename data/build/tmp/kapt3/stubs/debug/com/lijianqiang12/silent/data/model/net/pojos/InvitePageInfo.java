package com.lijianqiang12.silent.data.model.net.pojos;

@kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u00000\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0000\n\u0002\u0010!\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\b\n\u0000\n\u0002\u0010\u000e\n\u0002\b\u000e\n\u0002\u0010\u000b\n\u0002\b\u0004\b\u0086\b\u0018\u00002\u00020\u0001B1\u0012\f\u0010\u0002\u001a\b\u0012\u0004\u0012\u00020\u00040\u0003\u0012\f\u0010\u0005\u001a\b\u0012\u0004\u0012\u00020\u00060\u0003\u0012\u0006\u0010\u0007\u001a\u00020\b\u0012\u0006\u0010\t\u001a\u00020\n\u00a2\u0006\u0002\u0010\u000bJ\u000f\u0010\u0013\u001a\b\u0012\u0004\u0012\u00020\u00040\u0003H\u00c6\u0003J\u000f\u0010\u0014\u001a\b\u0012\u0004\u0012\u00020\u00060\u0003H\u00c6\u0003J\t\u0010\u0015\u001a\u00020\bH\u00c6\u0003J\t\u0010\u0016\u001a\u00020\nH\u00c6\u0003J=\u0010\u0017\u001a\u00020\u00002\u000e\b\u0002\u0010\u0002\u001a\b\u0012\u0004\u0012\u00020\u00040\u00032\u000e\b\u0002\u0010\u0005\u001a\b\u0012\u0004\u0012\u00020\u00060\u00032\b\b\u0002\u0010\u0007\u001a\u00020\b2\b\b\u0002\u0010\t\u001a\u00020\nH\u00c6\u0001J\u0013\u0010\u0018\u001a\u00020\u00192\b\u0010\u001a\u001a\u0004\u0018\u00010\u0001H\u00d6\u0003J\t\u0010\u001b\u001a\u00020\bH\u00d6\u0001J\t\u0010\u001c\u001a\u00020\nH\u00d6\u0001R\u0011\u0010\u0007\u001a\u00020\b\u00a2\u0006\b\n\u0000\u001a\u0004\b\f\u0010\rR\u0011\u0010\t\u001a\u00020\n\u00a2\u0006\b\n\u0000\u001a\u0004\b\u000e\u0010\u000fR\u0017\u0010\u0002\u001a\b\u0012\u0004\u0012\u00020\u00040\u0003\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0010\u0010\u0011R\u0017\u0010\u0005\u001a\b\u0012\u0004\u0012\u00020\u00060\u0003\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0012\u0010\u0011\u00a8\u0006\u001d"}, d2 = {"Lcom/lijianqiang12/silent/data/model/net/pojos/InvitePageInfo;", "", "resultInviteExampleList", "", "Lcom/lijianqiang12/silent/data/model/net/pojos/ResultInviteExample;", "resultMyInviteHistoryList", "Lcom/lijianqiang12/silent/data/model/net/pojos/ResultMyInviteHistory;", "myInviteDays", "", "myInviteDaysLeft", "", "(Ljava/util/List;Ljava/util/List;ILjava/lang/String;)V", "getMyInviteDays", "()I", "getMyInviteDaysLeft", "()Ljava/lang/String;", "getResultInviteExampleList", "()Ljava/util/List;", "getResultMyInviteHistoryList", "component1", "component2", "component3", "component4", "copy", "equals", "", "other", "hashCode", "toString", "data_debug"})
public final class InvitePageInfo {
    @org.jetbrains.annotations.NotNull()
    private final java.util.List<com.lijianqiang12.silent.data.model.net.pojos.ResultInviteExample> resultInviteExampleList = null;
    @org.jetbrains.annotations.NotNull()
    private final java.util.List<com.lijianqiang12.silent.data.model.net.pojos.ResultMyInviteHistory> resultMyInviteHistoryList = null;
    private final int myInviteDays = 0;
    @org.jetbrains.annotations.NotNull()
    private final java.lang.String myInviteDaysLeft = null;
    
    public InvitePageInfo(@org.jetbrains.annotations.NotNull()
    java.util.List<com.lijianqiang12.silent.data.model.net.pojos.ResultInviteExample> resultInviteExampleList, @org.jetbrains.annotations.NotNull()
    java.util.List<com.lijianqiang12.silent.data.model.net.pojos.ResultMyInviteHistory> resultMyInviteHistoryList, int myInviteDays, @org.jetbrains.annotations.NotNull()
    java.lang.String myInviteDaysLeft) {
        super();
    }
    
    @org.jetbrains.annotations.NotNull()
    public final java.util.List<com.lijianqiang12.silent.data.model.net.pojos.ResultInviteExample> getResultInviteExampleList() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final java.util.List<com.lijianqiang12.silent.data.model.net.pojos.ResultMyInviteHistory> getResultMyInviteHistoryList() {
        return null;
    }
    
    public final int getMyInviteDays() {
        return 0;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final java.lang.String getMyInviteDaysLeft() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final java.util.List<com.lijianqiang12.silent.data.model.net.pojos.ResultInviteExample> component1() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final java.util.List<com.lijianqiang12.silent.data.model.net.pojos.ResultMyInviteHistory> component2() {
        return null;
    }
    
    public final int component3() {
        return 0;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final java.lang.String component4() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final com.lijianqiang12.silent.data.model.net.pojos.InvitePageInfo copy(@org.jetbrains.annotations.NotNull()
    java.util.List<com.lijianqiang12.silent.data.model.net.pojos.ResultInviteExample> resultInviteExampleList, @org.jetbrains.annotations.NotNull()
    java.util.List<com.lijianqiang12.silent.data.model.net.pojos.ResultMyInviteHistory> resultMyInviteHistoryList, int myInviteDays, @org.jetbrains.annotations.NotNull()
    java.lang.String myInviteDaysLeft) {
        return null;
    }
    
    @java.lang.Override()
    public boolean equals(@org.jetbrains.annotations.Nullable()
    java.lang.Object other) {
        return false;
    }
    
    @java.lang.Override()
    public int hashCode() {
        return 0;
    }
    
    @java.lang.Override()
    @org.jetbrains.annotations.NotNull()
    public java.lang.String toString() {
        return null;
    }
}