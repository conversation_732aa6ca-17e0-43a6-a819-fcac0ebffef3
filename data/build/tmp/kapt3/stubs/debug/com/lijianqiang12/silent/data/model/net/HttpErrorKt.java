package com.lijianqiang12.silent.data.model.net;

@kotlin.Metadata(mv = {1, 9, 0}, k = 2, xi = 48, d1 = {"\u00004\n\u0000\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0010\u0002\n\u0002\b\u0005\n\u0002\u0010\u0003\n\u0002\b\u0003\n\u0002\u0018\u0002\n\u0002\b\u0004\n\u0002\u0010\u0000\n\u0002\u0018\u0002\n\u0000\u001a\u000e\u0010\t\u001a\u00020\u00062\u0006\u0010\n\u001a\u00020\u0002\u001a\u000e\u0010\u000b\u001a\u00020\u00062\u0006\u0010\n\u001a\u00020\f\u001a^\u0010\r\u001a\u00020\u0006\"\u0004\b\u0000\u0010\u000e2\u0006\u0010\u000f\u001a\u00020\u00102!\u0010\u0011\u001a\u001d\u0012\u0013\u0012\u0011H\u000e\u00a2\u0006\f\b\u0003\u0012\b\b\u0004\u0012\u0004\b\b(\u0012\u0012\u0004\u0012\u00020\u00060\u00012%\b\u0002\u0010\u0013\u001a\u001f\u0012\u0013\u0012\u00110\u0002\u00a2\u0006\f\b\u0003\u0012\b\b\u0004\u0012\u0004\b\b(\u0005\u0012\u0004\u0012\u00020\u0006\u0018\u00010\u0001\u001a\u001a\u0010\u0014\u001a\u00020\u0010\"\b\b\u0000\u0010\u000e*\u00020\u0015*\b\u0012\u0004\u0012\u0002H\u000e0\u0016\",\u0010\u0000\u001a\u001d\u0012\u0013\u0012\u00110\u0002\u00a2\u0006\f\b\u0003\u0012\b\b\u0004\u0012\u0004\b\b(\u0005\u0012\u0004\u0012\u00020\u00060\u0001\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0007\u0010\b\u00a8\u0006\u0017"}, d2 = {"defaultErrorBlock", "Lkotlin/Function1;", "Lcom/lijianqiang12/silent/data/model/net/HttpError;", "Lkotlin/ParameterName;", "name", "error", "", "getDefaultErrorBlock", "()Lkotlin/jvm/functions/Function1;", "handlingApiExceptions", "e", "handlingExceptions", "", "handlingHttpResponse", "T", "res", "Lcom/lijianqiang12/silent/data/model/net/HttpResponse;", "successBlock", "data", "failureBlock", "convertHttpRes", "", "Lcom/lijianqiang12/silent/data/model/net/pojos/ApiResponse;", "data_debug"})
public final class HttpErrorKt {
    @org.jetbrains.annotations.NotNull()
    private static final kotlin.jvm.functions.Function1<com.lijianqiang12.silent.data.model.net.HttpError, kotlin.Unit> defaultErrorBlock = null;
    
    /**
     * 处理请求层的错误,对可能的已知的错误进行处理
     */
    public static final void handlingExceptions(@org.jetbrains.annotations.NotNull()
    java.lang.Throwable e) {
    }
    
    /**
     * 处理响应层的错误
     */
    public static final void handlingApiExceptions(@org.jetbrains.annotations.NotNull()
    com.lijianqiang12.silent.data.model.net.HttpError e) {
    }
    
    /**
     * 处理HttpResponse
     * @param res
     * @param successBlock 成功
     * @param failureBlock 失败
     */
    public static final <T extends java.lang.Object>void handlingHttpResponse(@org.jetbrains.annotations.NotNull()
    com.lijianqiang12.silent.data.model.net.HttpResponse res, @org.jetbrains.annotations.NotNull()
    kotlin.jvm.functions.Function1<? super T, kotlin.Unit> successBlock, @org.jetbrains.annotations.Nullable()
    kotlin.jvm.functions.Function1<? super com.lijianqiang12.silent.data.model.net.HttpError, kotlin.Unit> failureBlock) {
    }
    
    @org.jetbrains.annotations.NotNull()
    public static final kotlin.jvm.functions.Function1<com.lijianqiang12.silent.data.model.net.HttpError, kotlin.Unit> getDefaultErrorBlock() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public static final <T extends java.lang.Object>com.lijianqiang12.silent.data.model.net.HttpResponse convertHttpRes(@org.jetbrains.annotations.NotNull()
    com.lijianqiang12.silent.data.model.net.pojos.ApiResponse<? extends T> $this$convertHttpRes) {
        return null;
    }
}