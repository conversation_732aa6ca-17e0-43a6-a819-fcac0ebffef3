package com.lijianqiang12.silent.data.model.db;

@kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000B\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0010\u000e\n\u0000\n\u0002\u0010\u000b\n\u0002\b\u0002\n\u0002\u0010\b\n\u0002\b\u0002\n\u0002\u0010\t\n\u0002\b=\n\u0002\u0010\u0002\n\u0002\b\u0004\n\u0002\u0010\u0000\n\u0002\b\u0006\b\u0086\b\u0018\u0000 W2\u00020\u0001:\u0001WB\u0007\b\u0016\u00a2\u0006\u0002\u0010\u0002B\u000f\b\u0016\u0012\u0006\u0010\u0003\u001a\u00020\u0004\u00a2\u0006\u0002\u0010\u0005B}\u0012\u0006\u0010\u0006\u001a\u00020\u0007\u0012\u0006\u0010\b\u001a\u00020\t\u0012\u0006\u0010\n\u001a\u00020\t\u0012\u0006\u0010\u000b\u001a\u00020\f\u0012\u0006\u0010\r\u001a\u00020\f\u0012\u0006\u0010\u000e\u001a\u00020\u000f\u0012\u0006\u0010\u0010\u001a\u00020\u000f\u0012\u0006\u0010\u0011\u001a\u00020\t\u0012\u0006\u0010\u0012\u001a\u00020\t\u0012\u0006\u0010\u0013\u001a\u00020\t\u0012\u0006\u0010\u0014\u001a\u00020\t\u0012\u0006\u0010\u0015\u001a\u00020\t\u0012\u0006\u0010\u0016\u001a\u00020\t\u0012\u0006\u0010\u0017\u001a\u00020\t\u0012\u0006\u0010\u0018\u001a\u00020\t\u00a2\u0006\u0002\u0010\u0019J\t\u0010<\u001a\u00020\u0007H\u00c6\u0003J\t\u0010=\u001a\u00020\tH\u00c6\u0003J\t\u0010>\u001a\u00020\tH\u00c6\u0003J\t\u0010?\u001a\u00020\tH\u00c6\u0003J\t\u0010@\u001a\u00020\tH\u00c6\u0003J\t\u0010A\u001a\u00020\tH\u00c6\u0003J\t\u0010B\u001a\u00020\tH\u00c6\u0003J\t\u0010C\u001a\u00020\tH\u00c6\u0003J\t\u0010D\u001a\u00020\tH\u00c6\u0003J\t\u0010E\u001a\u00020\fH\u00c6\u0003J\t\u0010F\u001a\u00020\fH\u00c6\u0003J\t\u0010G\u001a\u00020\u000fH\u00c6\u0003J\t\u0010H\u001a\u00020\u000fH\u00c6\u0003J\t\u0010I\u001a\u00020\tH\u00c6\u0003J\t\u0010J\u001a\u00020\tH\u00c6\u0003J\u0006\u0010K\u001a\u00020\u0000J\u009f\u0001\u0010K\u001a\u00020\u00002\b\b\u0002\u0010\u0006\u001a\u00020\u00072\b\b\u0002\u0010\b\u001a\u00020\t2\b\b\u0002\u0010\n\u001a\u00020\t2\b\b\u0002\u0010\u000b\u001a\u00020\f2\b\b\u0002\u0010\r\u001a\u00020\f2\b\b\u0002\u0010\u000e\u001a\u00020\u000f2\b\b\u0002\u0010\u0010\u001a\u00020\u000f2\b\b\u0002\u0010\u0011\u001a\u00020\t2\b\b\u0002\u0010\u0012\u001a\u00020\t2\b\b\u0002\u0010\u0013\u001a\u00020\t2\b\b\u0002\u0010\u0014\u001a\u00020\t2\b\b\u0002\u0010\u0015\u001a\u00020\t2\b\b\u0002\u0010\u0016\u001a\u00020\t2\b\b\u0002\u0010\u0017\u001a\u00020\t2\b\b\u0002\u0010\u0018\u001a\u00020\tH\u00c6\u0001J\u000e\u0010L\u001a\u00020M2\u0006\u0010N\u001a\u00020\u0000J\b\u0010O\u001a\u00020\fH\u0016J\u0013\u0010P\u001a\u00020\t2\b\u0010Q\u001a\u0004\u0018\u00010RH\u00d6\u0003J\t\u0010S\u001a\u00020\fH\u00d6\u0001J\b\u0010T\u001a\u00020\u0007H\u0016J\u0018\u0010U\u001a\u00020M2\u0006\u0010\u0003\u001a\u00020\u00042\u0006\u0010V\u001a\u00020\fH\u0016R\u001a\u0010\u0006\u001a\u00020\u0007X\u0086\u000e\u00a2\u0006\u000e\n\u0000\u001a\u0004\b\u001a\u0010\u001b\"\u0004\b\u001c\u0010\u001dR\u001a\u0010\u0012\u001a\u00020\tX\u0086\u000e\u00a2\u0006\u000e\n\u0000\u001a\u0004\b\u001e\u0010\u001f\"\u0004\b \u0010!R\u001a\u0010\u0010\u001a\u00020\u000fX\u0086\u000e\u00a2\u0006\u000e\n\u0000\u001a\u0004\b\"\u0010#\"\u0004\b$\u0010%R\u001a\u0010\u0018\u001a\u00020\tX\u0086\u000e\u00a2\u0006\u000e\n\u0000\u001a\u0004\b&\u0010\u001f\"\u0004\b\'\u0010!R\u001a\u0010\r\u001a\u00020\fX\u0086\u000e\u00a2\u0006\u000e\n\u0000\u001a\u0004\b(\u0010)\"\u0004\b*\u0010+R\u001a\u0010\u0016\u001a\u00020\tX\u0086\u000e\u00a2\u0006\u000e\n\u0000\u001a\u0004\b,\u0010\u001f\"\u0004\b-\u0010!R\u001a\u0010\b\u001a\u00020\tX\u0086\u000e\u00a2\u0006\u000e\n\u0000\u001a\u0004\b\b\u0010\u001f\"\u0004\b.\u0010!R\u001a\u0010\u0013\u001a\u00020\tX\u0086\u000e\u00a2\u0006\u000e\n\u0000\u001a\u0004\b\u0013\u0010\u001f\"\u0004\b/\u0010!R\u001a\u0010\n\u001a\u00020\tX\u0086\u000e\u00a2\u0006\u000e\n\u0000\u001a\u0004\b\n\u0010\u001f\"\u0004\b0\u0010!R\u001a\u0010\u0014\u001a\u00020\tX\u0086\u000e\u00a2\u0006\u000e\n\u0000\u001a\u0004\b\u0014\u0010\u001f\"\u0004\b1\u0010!R\u001a\u0010\u000e\u001a\u00020\u000fX\u0086\u000e\u00a2\u0006\u000e\n\u0000\u001a\u0004\b2\u0010#\"\u0004\b3\u0010%R\u001a\u0010\u0017\u001a\u00020\tX\u0086\u000e\u00a2\u0006\u000e\n\u0000\u001a\u0004\b4\u0010\u001f\"\u0004\b5\u0010!R\u001a\u0010\u000b\u001a\u00020\fX\u0086\u000e\u00a2\u0006\u000e\n\u0000\u001a\u0004\b6\u0010)\"\u0004\b7\u0010+R\u001a\u0010\u0015\u001a\u00020\tX\u0086\u000e\u00a2\u0006\u000e\n\u0000\u001a\u0004\b8\u0010\u001f\"\u0004\b9\u0010!R\u001a\u0010\u0011\u001a\u00020\tX\u0086\u000e\u00a2\u0006\u000e\n\u0000\u001a\u0004\b:\u0010\u001f\"\u0004\b;\u0010!\u00a8\u0006X"}, d2 = {"Lcom/lijianqiang12/silent/data/model/db/LockConfig;", "Landroid/os/Parcelable;", "()V", "parcel", "Landroid/os/Parcel;", "(Landroid/os/Parcel;)V", "bgUrl", "", "isRemoveNotification", "", "isSilent", "startVoiceNotify", "", "endVoiceNotify", "startShakeNotify", "", "endShakeNotify", "whiteFollowGlobal", "bgUrlFollowGlobal", "isRemoveNotificationFollowGlobal", "isSilentFollowGlobal", "startVoiceNotifyFollowGlobal", "endVoiceNotifyFollowGlobal", "startShakeNotifyFollowGlobal", "endShakeNotifyFollowGlobal", "(Ljava/lang/String;ZZIIJJZZZZZZZZ)V", "getBgUrl", "()Ljava/lang/String;", "setBgUrl", "(Ljava/lang/String;)V", "getBgUrlFollowGlobal", "()Z", "setBgUrlFollowGlobal", "(Z)V", "getEndShakeNotify", "()J", "setEndShakeNotify", "(J)V", "getEndShakeNotifyFollowGlobal", "setEndShakeNotifyFollowGlobal", "getEndVoiceNotify", "()I", "setEndVoiceNotify", "(I)V", "getEndVoiceNotifyFollowGlobal", "setEndVoiceNotifyFollowGlobal", "setRemoveNotification", "setRemoveNotificationFollowGlobal", "setSilent", "setSilentFollowGlobal", "getStartShakeNotify", "setStartShakeNotify", "getStartShakeNotifyFollowGlobal", "setStartShakeNotifyFollowGlobal", "getStartVoiceNotify", "setStartVoiceNotify", "getStartVoiceNotifyFollowGlobal", "setStartVoiceNotifyFollowGlobal", "getWhiteFollowGlobal", "setWhiteFollowGlobal", "component1", "component10", "component11", "component12", "component13", "component14", "component15", "component2", "component3", "component4", "component5", "component6", "component7", "component8", "component9", "copy", "copyFrom", "", "fromWho", "describeContents", "equals", "other", "", "hashCode", "toString", "writeToParcel", "flags", "CREATOR", "data_debug"})
public final class LockConfig implements android.os.Parcelable {
    @org.jetbrains.annotations.NotNull()
    private java.lang.String bgUrl;
    private boolean isRemoveNotification;
    private boolean isSilent;
    private int startVoiceNotify;
    private int endVoiceNotify;
    private long startShakeNotify;
    private long endShakeNotify;
    private boolean whiteFollowGlobal;
    private boolean bgUrlFollowGlobal;
    private boolean isRemoveNotificationFollowGlobal;
    private boolean isSilentFollowGlobal;
    private boolean startVoiceNotifyFollowGlobal;
    private boolean endVoiceNotifyFollowGlobal;
    private boolean startShakeNotifyFollowGlobal;
    private boolean endShakeNotifyFollowGlobal;
    @org.jetbrains.annotations.NotNull()
    public static final com.lijianqiang12.silent.data.model.db.LockConfig.CREATOR CREATOR = null;
    
    public LockConfig(@org.jetbrains.annotations.NotNull()
    java.lang.String bgUrl, boolean isRemoveNotification, boolean isSilent, int startVoiceNotify, int endVoiceNotify, long startShakeNotify, long endShakeNotify, boolean whiteFollowGlobal, boolean bgUrlFollowGlobal, boolean isRemoveNotificationFollowGlobal, boolean isSilentFollowGlobal, boolean startVoiceNotifyFollowGlobal, boolean endVoiceNotifyFollowGlobal, boolean startShakeNotifyFollowGlobal, boolean endShakeNotifyFollowGlobal) {
        super();
    }
    
    @org.jetbrains.annotations.NotNull()
    public final java.lang.String getBgUrl() {
        return null;
    }
    
    public final void setBgUrl(@org.jetbrains.annotations.NotNull()
    java.lang.String p0) {
    }
    
    public final boolean isRemoveNotification() {
        return false;
    }
    
    public final void setRemoveNotification(boolean p0) {
    }
    
    public final boolean isSilent() {
        return false;
    }
    
    public final void setSilent(boolean p0) {
    }
    
    public final int getStartVoiceNotify() {
        return 0;
    }
    
    public final void setStartVoiceNotify(int p0) {
    }
    
    public final int getEndVoiceNotify() {
        return 0;
    }
    
    public final void setEndVoiceNotify(int p0) {
    }
    
    public final long getStartShakeNotify() {
        return 0L;
    }
    
    public final void setStartShakeNotify(long p0) {
    }
    
    public final long getEndShakeNotify() {
        return 0L;
    }
    
    public final void setEndShakeNotify(long p0) {
    }
    
    public final boolean getWhiteFollowGlobal() {
        return false;
    }
    
    public final void setWhiteFollowGlobal(boolean p0) {
    }
    
    public final boolean getBgUrlFollowGlobal() {
        return false;
    }
    
    public final void setBgUrlFollowGlobal(boolean p0) {
    }
    
    public final boolean isRemoveNotificationFollowGlobal() {
        return false;
    }
    
    public final void setRemoveNotificationFollowGlobal(boolean p0) {
    }
    
    public final boolean isSilentFollowGlobal() {
        return false;
    }
    
    public final void setSilentFollowGlobal(boolean p0) {
    }
    
    public final boolean getStartVoiceNotifyFollowGlobal() {
        return false;
    }
    
    public final void setStartVoiceNotifyFollowGlobal(boolean p0) {
    }
    
    public final boolean getEndVoiceNotifyFollowGlobal() {
        return false;
    }
    
    public final void setEndVoiceNotifyFollowGlobal(boolean p0) {
    }
    
    public final boolean getStartShakeNotifyFollowGlobal() {
        return false;
    }
    
    public final void setStartShakeNotifyFollowGlobal(boolean p0) {
    }
    
    public final boolean getEndShakeNotifyFollowGlobal() {
        return false;
    }
    
    public final void setEndShakeNotifyFollowGlobal(boolean p0) {
    }
    
    public LockConfig() {
        super();
    }
    
    public LockConfig(@org.jetbrains.annotations.NotNull()
    android.os.Parcel parcel) {
        super();
    }
    
    @org.jetbrains.annotations.NotNull()
    public final com.lijianqiang12.silent.data.model.db.LockConfig copy() {
        return null;
    }
    
    public final void copyFrom(@org.jetbrains.annotations.NotNull()
    com.lijianqiang12.silent.data.model.db.LockConfig fromWho) {
    }
    
    @java.lang.Override()
    public void writeToParcel(@org.jetbrains.annotations.NotNull()
    android.os.Parcel parcel, int flags) {
    }
    
    @java.lang.Override()
    public int describeContents() {
        return 0;
    }
    
    @java.lang.Override()
    @org.jetbrains.annotations.NotNull()
    public java.lang.String toString() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final java.lang.String component1() {
        return null;
    }
    
    public final boolean component10() {
        return false;
    }
    
    public final boolean component11() {
        return false;
    }
    
    public final boolean component12() {
        return false;
    }
    
    public final boolean component13() {
        return false;
    }
    
    public final boolean component14() {
        return false;
    }
    
    public final boolean component15() {
        return false;
    }
    
    public final boolean component2() {
        return false;
    }
    
    public final boolean component3() {
        return false;
    }
    
    public final int component4() {
        return 0;
    }
    
    public final int component5() {
        return 0;
    }
    
    public final long component6() {
        return 0L;
    }
    
    public final long component7() {
        return 0L;
    }
    
    public final boolean component8() {
        return false;
    }
    
    public final boolean component9() {
        return false;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final com.lijianqiang12.silent.data.model.db.LockConfig copy(@org.jetbrains.annotations.NotNull()
    java.lang.String bgUrl, boolean isRemoveNotification, boolean isSilent, int startVoiceNotify, int endVoiceNotify, long startShakeNotify, long endShakeNotify, boolean whiteFollowGlobal, boolean bgUrlFollowGlobal, boolean isRemoveNotificationFollowGlobal, boolean isSilentFollowGlobal, boolean startVoiceNotifyFollowGlobal, boolean endVoiceNotifyFollowGlobal, boolean startShakeNotifyFollowGlobal, boolean endShakeNotifyFollowGlobal) {
        return null;
    }
    
    @java.lang.Override()
    public boolean equals(@org.jetbrains.annotations.Nullable()
    java.lang.Object other) {
        return false;
    }
    
    @java.lang.Override()
    public int hashCode() {
        return 0;
    }
    
    @kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000$\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\b\u0003\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\u0011\n\u0000\n\u0002\u0010\b\n\u0002\b\u0002\b\u0086\u0003\u0018\u00002\b\u0012\u0004\u0012\u00020\u00020\u0001B\u0007\b\u0002\u00a2\u0006\u0002\u0010\u0003J\u0010\u0010\u0004\u001a\u00020\u00022\u0006\u0010\u0005\u001a\u00020\u0006H\u0016J\u001d\u0010\u0007\u001a\n\u0012\u0006\u0012\u0004\u0018\u00010\u00020\b2\u0006\u0010\t\u001a\u00020\nH\u0016\u00a2\u0006\u0002\u0010\u000b\u00a8\u0006\f"}, d2 = {"Lcom/lijianqiang12/silent/data/model/db/LockConfig$CREATOR;", "Landroid/os/Parcelable$Creator;", "Lcom/lijianqiang12/silent/data/model/db/LockConfig;", "()V", "createFromParcel", "parcel", "Landroid/os/Parcel;", "newArray", "", "size", "", "(I)[Lcom/lijianqiang12/silent/data/model/db/LockConfig;", "data_debug"})
    public static final class CREATOR implements android.os.Parcelable.Creator<com.lijianqiang12.silent.data.model.db.LockConfig> {
        
        private CREATOR() {
            super();
        }
        
        @java.lang.Override()
        @org.jetbrains.annotations.NotNull()
        public com.lijianqiang12.silent.data.model.db.LockConfig createFromParcel(@org.jetbrains.annotations.NotNull()
        android.os.Parcel parcel) {
            return null;
        }
        
        @java.lang.Override()
        @org.jetbrains.annotations.NotNull()
        public com.lijianqiang12.silent.data.model.db.LockConfig[] newArray(int size) {
            return null;
        }
    }
}