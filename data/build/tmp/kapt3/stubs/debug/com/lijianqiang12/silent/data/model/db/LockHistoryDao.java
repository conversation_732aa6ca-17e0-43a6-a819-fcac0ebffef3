package com.lijianqiang12.silent.data.model.db;

@kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000.\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0000\n\u0002\u0010\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0010!\n\u0002\b\u0003\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\t\n\u0002\b\u0002\bg\u0018\u00002\u00020\u0001J\u0016\u0010\u0002\u001a\u00020\u00032\u0006\u0010\u0004\u001a\u00020\u0005H\u00a7@\u00a2\u0006\u0002\u0010\u0006J\u0014\u0010\u0007\u001a\b\u0012\u0004\u0012\u00020\u00050\bH\u00a7@\u00a2\u0006\u0002\u0010\tJ\u0010\u0010\n\u001a\u0004\u0018\u00010\u0005H\u00a7@\u00a2\u0006\u0002\u0010\tJ\u0010\u0010\u000b\u001a\n\u0012\u0006\u0012\u0004\u0018\u00010\u00050\fH\'J\u0016\u0010\r\u001a\u00020\u000e2\u0006\u0010\u0004\u001a\u00020\u0005H\u00a7@\u00a2\u0006\u0002\u0010\u0006J\u0016\u0010\u000f\u001a\u00020\u00032\u0006\u0010\u0004\u001a\u00020\u0005H\u00a7@\u00a2\u0006\u0002\u0010\u0006\u00a8\u0006\u0010"}, d2 = {"Lcom/lijianqiang12/silent/data/model/db/LockHistoryDao;", "", "deleteLockHistory", "", "lockHistory", "Lcom/lijianqiang12/silent/data/model/db/LockHistory;", "(Lcom/lijianqiang12/silent/data/model/db/LockHistory;Lkotlin/coroutines/Continuation;)Ljava/lang/Object;", "getUnUploadHistory", "", "(Lkotlin/coroutines/Continuation;)Ljava/lang/Object;", "getUnfinishedLastLockHistory", "getUnfinishedLockHistory", "Landroidx/lifecycle/LiveData;", "insertLockHistory", "", "updateLockHistory", "data_debug"})
@androidx.room.Dao()
public abstract interface LockHistoryDao {
    
    @androidx.room.Query(value = "select * From LockHistory where isFinish = 1 and isSynced = 0 order by id")
    @org.jetbrains.annotations.Nullable()
    public abstract java.lang.Object getUnUploadHistory(@org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super java.util.List<com.lijianqiang12.silent.data.model.db.LockHistory>> $completion);
    
    @androidx.room.Query(value = "select * From LockHistory where isFinish = 0 order by id desc")
    @org.jetbrains.annotations.NotNull()
    public abstract androidx.lifecycle.LiveData<com.lijianqiang12.silent.data.model.db.LockHistory> getUnfinishedLockHistory();
    
    @androidx.room.Insert(onConflict = 5)
    @org.jetbrains.annotations.Nullable()
    public abstract java.lang.Object insertLockHistory(@org.jetbrains.annotations.NotNull()
    com.lijianqiang12.silent.data.model.db.LockHistory lockHistory, @org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super java.lang.Long> $completion);
    
    @androidx.room.Update()
    @org.jetbrains.annotations.Nullable()
    public abstract java.lang.Object updateLockHistory(@org.jetbrains.annotations.NotNull()
    com.lijianqiang12.silent.data.model.db.LockHistory lockHistory, @org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super kotlin.Unit> $completion);
    
    @androidx.room.Delete()
    @org.jetbrains.annotations.Nullable()
    public abstract java.lang.Object deleteLockHistory(@org.jetbrains.annotations.NotNull()
    com.lijianqiang12.silent.data.model.db.LockHistory lockHistory, @org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super kotlin.Unit> $completion);
    
    @androidx.room.Query(value = "select * from LockHistory where isFinish = 0 order by id desc limit 1")
    @org.jetbrains.annotations.Nullable()
    public abstract java.lang.Object getUnfinishedLastLockHistory(@org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super com.lijianqiang12.silent.data.model.db.LockHistory> $completion);
}