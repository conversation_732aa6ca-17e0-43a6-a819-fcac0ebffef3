package com.lijianqiang12.silent.data.model.repository;

@kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000(\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\u000e\n\u0002\b\u000b\n\u0002\u0018\u0002\n\u0002\b\u000e\u0018\u0000 !2\u00020\u0001:\u0001!B\u000f\b\u0002\u0012\u0006\u0010\u0002\u001a\u00020\u0003\u00a2\u0006\u0002\u0010\u0004J\u001c\u0010\u0005\u001a\b\u0012\u0004\u0012\u00020\u00010\u00062\u0006\u0010\u0007\u001a\u00020\bH\u0086@\u00a2\u0006\u0002\u0010\tJ\u001c\u0010\n\u001a\b\u0012\u0004\u0012\u00020\u00010\u00062\u0006\u0010\u000b\u001a\u00020\bH\u0086@\u00a2\u0006\u0002\u0010\tJ\u001c\u0010\f\u001a\b\u0012\u0004\u0012\u00020\u00010\u00062\u0006\u0010\r\u001a\u00020\bH\u0086@\u00a2\u0006\u0002\u0010\tJ\u0014\u0010\u000e\u001a\b\u0012\u0004\u0012\u00020\u00010\u0006H\u0086@\u00a2\u0006\u0002\u0010\u000fJ\u0014\u0010\u0010\u001a\b\u0012\u0004\u0012\u00020\u00010\u0006H\u0086@\u00a2\u0006\u0002\u0010\u000fJ\u0014\u0010\u0011\u001a\b\u0012\u0004\u0012\u00020\u00010\u0006H\u0086@\u00a2\u0006\u0002\u0010\u000fJ\u0014\u0010\u0012\u001a\b\u0012\u0004\u0012\u00020\u00010\u0006H\u0086@\u00a2\u0006\u0002\u0010\u000fJ\u0014\u0010\u0013\u001a\b\u0012\u0004\u0012\u00020\u00140\u0006H\u0086@\u00a2\u0006\u0002\u0010\u000fJ\u001c\u0010\u0015\u001a\b\u0012\u0004\u0012\u00020\u00010\u00062\u0006\u0010\u0016\u001a\u00020\bH\u0086@\u00a2\u0006\u0002\u0010\tJ\u001c\u0010\u0017\u001a\b\u0012\u0004\u0012\u00020\u00010\u00062\u0006\u0010\u0018\u001a\u00020\bH\u0086@\u00a2\u0006\u0002\u0010\tJ$\u0010\u0019\u001a\b\u0012\u0004\u0012\u00020\u00010\u00062\u0006\u0010\u001a\u001a\u00020\b2\u0006\u0010\u001b\u001a\u00020\bH\u0086@\u00a2\u0006\u0002\u0010\u001cJ\u001c\u0010\u001d\u001a\b\u0012\u0004\u0012\u00020\u00010\u00062\u0006\u0010\u001e\u001a\u00020\bH\u0086@\u00a2\u0006\u0002\u0010\tJ\u001c\u0010\u001f\u001a\b\u0012\u0004\u0012\u00020\u00010\u00062\u0006\u0010 \u001a\u00020\bH\u0086@\u00a2\u0006\u0002\u0010\tR\u000e\u0010\u0002\u001a\u00020\u0003X\u0082\u0004\u00a2\u0006\u0002\n\u0000\u00a8\u0006\""}, d2 = {"Lcom/lijianqiang12/silent/data/model/repository/AccountRepository;", "", "appContext", "Landroid/content/Context;", "(Landroid/content/Context;)V", "bindQQ", "Lcom/lijianqiang12/silent/data/model/net/pojos/ApiResponse;", "qqId", "", "(Ljava/lang/String;Lkotlin/coroutines/Continuation;)Ljava/lang/Object;", "bindSINA", "sinaId", "bindWX", "wxId", "deleteAllAccount", "(Lkotlin/coroutines/Continuation;)Ljava/lang/Object;", "deleteQQ", "deleteSINA", "deleteWX", "queryUserInfo", "Lcom/lijianqiang12/silent/data/model/net/pojos/UserInfo;", "updateAvatar", "avatar", "updateGender", "gender", "updateMobile", "mobile", "code", "(Ljava/lang/String;Ljava/lang/String;Lkotlin/coroutines/Continuation;)Ljava/lang/Object;", "updateUsername", "username", "updateWord", "word", "Companion", "data_debug"})
public final class AccountRepository {
    @org.jetbrains.annotations.NotNull()
    private final android.content.Context appContext = null;
    @kotlin.jvm.Volatile()
    @org.jetbrains.annotations.Nullable()
    private static volatile com.lijianqiang12.silent.data.model.repository.AccountRepository instance;
    @org.jetbrains.annotations.NotNull()
    public static final com.lijianqiang12.silent.data.model.repository.AccountRepository.Companion Companion = null;
    
    private AccountRepository(android.content.Context appContext) {
        super();
    }
    
    @org.jetbrains.annotations.Nullable()
    public final java.lang.Object queryUserInfo(@org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super com.lijianqiang12.silent.data.model.net.pojos.ApiResponse<com.lijianqiang12.silent.data.model.net.pojos.UserInfo>> $completion) {
        return null;
    }
    
    @org.jetbrains.annotations.Nullable()
    public final java.lang.Object updateAvatar(@org.jetbrains.annotations.NotNull()
    java.lang.String avatar, @org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super com.lijianqiang12.silent.data.model.net.pojos.ApiResponse<? extends java.lang.Object>> $completion) {
        return null;
    }
    
    @org.jetbrains.annotations.Nullable()
    public final java.lang.Object updateUsername(@org.jetbrains.annotations.NotNull()
    java.lang.String username, @org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super com.lijianqiang12.silent.data.model.net.pojos.ApiResponse<? extends java.lang.Object>> $completion) {
        return null;
    }
    
    @org.jetbrains.annotations.Nullable()
    public final java.lang.Object updateGender(@org.jetbrains.annotations.NotNull()
    java.lang.String gender, @org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super com.lijianqiang12.silent.data.model.net.pojos.ApiResponse<? extends java.lang.Object>> $completion) {
        return null;
    }
    
    @org.jetbrains.annotations.Nullable()
    public final java.lang.Object updateWord(@org.jetbrains.annotations.NotNull()
    java.lang.String word, @org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super com.lijianqiang12.silent.data.model.net.pojos.ApiResponse<? extends java.lang.Object>> $completion) {
        return null;
    }
    
    @org.jetbrains.annotations.Nullable()
    public final java.lang.Object updateMobile(@org.jetbrains.annotations.NotNull()
    java.lang.String mobile, @org.jetbrains.annotations.NotNull()
    java.lang.String code, @org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super com.lijianqiang12.silent.data.model.net.pojos.ApiResponse<? extends java.lang.Object>> $completion) {
        return null;
    }
    
    @org.jetbrains.annotations.Nullable()
    public final java.lang.Object bindQQ(@org.jetbrains.annotations.NotNull()
    java.lang.String qqId, @org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super com.lijianqiang12.silent.data.model.net.pojos.ApiResponse<? extends java.lang.Object>> $completion) {
        return null;
    }
    
    @org.jetbrains.annotations.Nullable()
    public final java.lang.Object bindWX(@org.jetbrains.annotations.NotNull()
    java.lang.String wxId, @org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super com.lijianqiang12.silent.data.model.net.pojos.ApiResponse<? extends java.lang.Object>> $completion) {
        return null;
    }
    
    @org.jetbrains.annotations.Nullable()
    public final java.lang.Object bindSINA(@org.jetbrains.annotations.NotNull()
    java.lang.String sinaId, @org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super com.lijianqiang12.silent.data.model.net.pojos.ApiResponse<? extends java.lang.Object>> $completion) {
        return null;
    }
    
    @org.jetbrains.annotations.Nullable()
    public final java.lang.Object deleteQQ(@org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super com.lijianqiang12.silent.data.model.net.pojos.ApiResponse<? extends java.lang.Object>> $completion) {
        return null;
    }
    
    @org.jetbrains.annotations.Nullable()
    public final java.lang.Object deleteWX(@org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super com.lijianqiang12.silent.data.model.net.pojos.ApiResponse<? extends java.lang.Object>> $completion) {
        return null;
    }
    
    @org.jetbrains.annotations.Nullable()
    public final java.lang.Object deleteSINA(@org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super com.lijianqiang12.silent.data.model.net.pojos.ApiResponse<? extends java.lang.Object>> $completion) {
        return null;
    }
    
    @org.jetbrains.annotations.Nullable()
    public final java.lang.Object deleteAllAccount(@org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super com.lijianqiang12.silent.data.model.net.pojos.ApiResponse<? extends java.lang.Object>> $completion) {
        return null;
    }
    
    @kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000\u001a\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0000\b\u0086\u0003\u0018\u00002\u00020\u0001B\u0007\b\u0002\u00a2\u0006\u0002\u0010\u0002J\u000e\u0010\u0005\u001a\u00020\u00042\u0006\u0010\u0006\u001a\u00020\u0007R\u0010\u0010\u0003\u001a\u0004\u0018\u00010\u0004X\u0082\u000e\u00a2\u0006\u0002\n\u0000\u00a8\u0006\b"}, d2 = {"Lcom/lijianqiang12/silent/data/model/repository/AccountRepository$Companion;", "", "()V", "instance", "Lcom/lijianqiang12/silent/data/model/repository/AccountRepository;", "getInstance", "appContext", "Landroid/content/Context;", "data_debug"})
    public static final class Companion {
        
        private Companion() {
            super();
        }
        
        @org.jetbrains.annotations.NotNull()
        public final com.lijianqiang12.silent.data.model.repository.AccountRepository getInstance(@org.jetbrains.annotations.NotNull()
        android.content.Context appContext) {
            return null;
        }
    }
}