package com.lijianqiang12.silent.data.model.net.api;

@kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000\u00e6\u0002\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0000\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\u000e\n\u0002\b\u0002\n\u0002\u0010\u000b\n\u0000\n\u0002\u0010\t\n\u0002\b\u000f\n\u0002\u0010\b\n\u0002\b0\n\u0002\u0010 \n\u0002\u0018\u0002\n\u0002\b\u0005\n\u0002\u0018\u0002\n\u0002\b%\n\u0002\u0010!\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0005\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0003\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0004\n\u0002\u0018\u0002\n\u0002\b\u0005\n\u0002\u0018\u0002\n\u0002\b\u000b\n\u0002\u0018\u0002\n\u0002\b\u0003\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0002\b\u0006\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\b\n\u0002\u0018\u0002\n\u0002\b\u0005\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0014\n\u0002\u0018\u0002\n\u0002\b\u000f\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0004\n\u0002\u0018\u0002\n\u0002\b\u0006\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0002\b\u0003\n\u0002\u0018\u0002\n\u0002\b\r\bf\u0018\u0000 \u00af\u00022\u00020\u0001:\u0002\u00af\u0002J\u00d2\u0001\u0010\u0002\u001a\b\u0012\u0004\u0012\u00020\u00040\u00032\b\b\u0001\u0010\u0005\u001a\u00020\u00062\b\b\u0001\u0010\u0007\u001a\u00020\u00062\b\b\u0001\u0010\b\u001a\u00020\t2\b\b\u0001\u0010\n\u001a\u00020\u000b2\b\b\u0001\u0010\f\u001a\u00020\u000b2\b\b\u0001\u0010\r\u001a\u00020\u000b2\b\b\u0001\u0010\u000e\u001a\u00020\u00062\b\b\u0001\u0010\u000f\u001a\u00020\t2\b\b\u0001\u0010\u0010\u001a\u00020\t2\b\b\u0001\u0010\u0011\u001a\u00020\t2\b\b\u0001\u0010\u0012\u001a\u00020\t2\b\b\u0001\u0010\u0013\u001a\u00020\t2\b\b\u0001\u0010\u0014\u001a\u00020\t2\b\b\u0001\u0010\u0015\u001a\u00020\t2\b\b\u0001\u0010\u0016\u001a\u00020\u000b2\b\b\u0001\u0010\u0017\u001a\u00020\u000b2\b\b\u0001\u0010\u0018\u001a\u00020\u000b2\b\b\u0001\u0010\u0019\u001a\u00020\t2\b\b\u0001\u0010\u001a\u001a\u00020\u001bH\u00a7@\u00a2\u0006\u0002\u0010\u001cJ2\u0010\u001d\u001a\b\u0012\u0004\u0012\u00020\u00040\u00032\b\b\u0001\u0010\u001e\u001a\u00020\u00062\b\b\u0001\u0010\u001f\u001a\u00020\u001b2\b\b\u0001\u0010\u001a\u001a\u00020\u001bH\u00a7@\u00a2\u0006\u0002\u0010 J\u00fc\u0002\u0010!\u001a\b\u0012\u0004\u0012\u00020\u00040\u00032\b\b\u0001\u0010\u000e\u001a\u00020\u00062\b\b\u0001\u0010\"\u001a\u00020\u00062\b\b\u0001\u0010#\u001a\u00020\u00062\b\b\u0001\u0010$\u001a\u00020\u001b2\b\b\u0001\u0010%\u001a\u00020\u001b2\b\b\u0001\u0010&\u001a\u00020\t2\b\b\u0001\u0010\u000f\u001a\u00020\t2\b\b\u0001\u0010\u0010\u001a\u00020\t2\b\b\u0001\u0010\u0011\u001a\u00020\t2\b\b\u0001\u0010\u0012\u001a\u00020\t2\b\b\u0001\u0010\u0013\u001a\u00020\t2\b\b\u0001\u0010\u0014\u001a\u00020\t2\b\b\u0001\u0010\u0015\u001a\u00020\t2\b\b\u0001\u0010\'\u001a\u00020\t2\b\b\u0001\u0010(\u001a\u00020\u001b2\b\b\u0001\u0010)\u001a\u00020\u001b2\b\b\u0001\u0010*\u001a\u00020\t2\b\b\u0001\u0010+\u001a\u00020\t2\b\b\u0001\u0010,\u001a\u00020\u001b2\b\b\u0001\u0010-\u001a\u00020\u00062\b\b\u0001\u0010.\u001a\u00020\t2\b\b\u0001\u0010/\u001a\u00020\t2\b\b\u0001\u00100\u001a\u00020\u001b2\b\b\u0001\u00101\u001a\u00020\u001b2\b\b\u0001\u00102\u001a\u00020\u000b2\b\b\u0001\u00103\u001a\u00020\u000b2\b\b\u0001\u00104\u001a\u00020\t2\b\b\u0001\u00105\u001a\u00020\t2\b\b\u0001\u00106\u001a\u00020\t2\b\b\u0001\u00107\u001a\u00020\t2\b\b\u0001\u00108\u001a\u00020\t2\b\b\u0001\u00109\u001a\u00020\t2\b\b\u0001\u0010:\u001a\u00020\t2\b\b\u0001\u0010;\u001a\u00020\t2\b\b\u0001\u0010<\u001a\u00020\u00062\b\b\u0001\u0010\u001a\u001a\u00020\u001bH\u00a7@\u00a2\u0006\u0002\u0010=J\u00fa\u0001\u0010>\u001a\b\u0012\u0004\u0012\u00020\u00040\u00032\b\b\u0001\u0010\"\u001a\u00020\u00062\b\b\u0001\u0010\u000e\u001a\u00020\u00062\b\b\u0001\u0010?\u001a\u00020\u001b2\b\b\u0001\u0010@\u001a\u00020\u001b2\b\b\u0001\u0010A\u001a\u00020\u001b2\b\b\u0001\u0010B\u001a\u00020\u001b2\b\b\u0001\u0010C\u001a\u00020\u001b2\b\b\u0001\u0010-\u001a\u00020\u00062\b\b\u0001\u0010.\u001a\u00020\t2\b\b\u0001\u0010/\u001a\u00020\t2\b\b\u0001\u00100\u001a\u00020\u001b2\b\b\u0001\u00101\u001a\u00020\u001b2\b\b\u0001\u00102\u001a\u00020\u000b2\b\b\u0001\u00103\u001a\u00020\u000b2\b\b\u0001\u00104\u001a\u00020\t2\b\b\u0001\u00105\u001a\u00020\t2\b\b\u0001\u00106\u001a\u00020\t2\b\b\u0001\u00107\u001a\u00020\t2\b\b\u0001\u00108\u001a\u00020\t2\b\b\u0001\u00109\u001a\u00020\t2\b\b\u0001\u0010:\u001a\u00020\t2\b\b\u0001\u0010;\u001a\u00020\t2\b\b\u0001\u0010\u001a\u001a\u00020\u001bH\u00a7@\u00a2\u0006\u0002\u0010DJZ\u0010E\u001a\b\u0012\u0004\u0012\u00020\u00040\u00032\b\b\u0001\u0010F\u001a\u00020\u00062\b\b\u0001\u0010\"\u001a\u00020\u00062\b\b\u0001\u0010#\u001a\u00020\u00062\b\b\u0001\u0010G\u001a\u00020\u00062\b\b\u0001\u0010H\u001a\u00020\u00062\b\b\u0001\u0010I\u001a\u00020\u001b2\b\b\u0001\u0010\u001a\u001a\u00020\u001bH\u00a7@\u00a2\u0006\u0002\u0010JJ8\u0010K\u001a\u000e\u0012\n\u0012\b\u0012\u0004\u0012\u00020M0L0\u00032\b\b\u0001\u0010N\u001a\u00020\u001b2\b\b\u0001\u0010O\u001a\u00020\u000b2\b\b\u0001\u0010P\u001a\u00020\u000bH\u00a7@\u00a2\u0006\u0002\u0010QJ\u0014\u0010R\u001a\b\u0012\u0004\u0012\u00020S0\u0003H\u00a7@\u00a2\u0006\u0002\u0010TJ2\u0010U\u001a\b\u0012\u0004\u0012\u00020\u00010\u00032\b\b\u0001\u0010V\u001a\u00020\u00062\b\b\u0001\u0010W\u001a\u00020\u00062\b\b\u0001\u0010X\u001a\u00020\u0006H\u00a7@\u00a2\u0006\u0002\u0010YJ\u001e\u0010Z\u001a\b\u0012\u0004\u0012\u00020\u00010\u00032\b\b\u0001\u0010[\u001a\u00020\u0006H\u00a7@\u00a2\u0006\u0002\u0010\\J\u001e\u0010]\u001a\b\u0012\u0004\u0012\u00020\u00010\u00032\b\b\u0001\u0010^\u001a\u00020\u0006H\u00a7@\u00a2\u0006\u0002\u0010\\J\u001e\u0010_\u001a\b\u0012\u0004\u0012\u00020\u00010\u00032\b\b\u0001\u0010`\u001a\u00020\u0006H\u00a7@\u00a2\u0006\u0002\u0010\\J\u0014\u0010a\u001a\b\u0012\u0004\u0012\u00020\u00010\u0003H\u00a7@\u00a2\u0006\u0002\u0010TJ2\u0010b\u001a\b\u0012\u0004\u0012\u00020\u00010\u00032\b\b\u0001\u0010c\u001a\u00020\u000b2\b\b\u0001\u0010\u001a\u001a\u00020\u001b2\b\b\u0001\u0010d\u001a\u00020\u000bH\u00a7@\u00a2\u0006\u0002\u0010eJ2\u0010f\u001a\b\u0012\u0004\u0012\u00020\u00010\u00032\b\b\u0001\u0010c\u001a\u00020\u000b2\b\b\u0001\u0010\u001a\u001a\u00020\u001b2\b\b\u0001\u0010d\u001a\u00020\u000bH\u00a7@\u00a2\u0006\u0002\u0010eJ\u0014\u0010g\u001a\b\u0012\u0004\u0012\u00020\u00010\u0003H\u00a7@\u00a2\u0006\u0002\u0010TJ\u001e\u0010h\u001a\b\u0012\u0004\u0012\u00020\u00010\u00032\b\b\u0001\u0010i\u001a\u00020\u001bH\u00a7@\u00a2\u0006\u0002\u0010jJ\u0014\u0010k\u001a\b\u0012\u0004\u0012\u00020\u00010\u0003H\u00a7@\u00a2\u0006\u0002\u0010TJ2\u0010l\u001a\b\u0012\u0004\u0012\u00020\u00010\u00032\b\b\u0001\u0010c\u001a\u00020\u000b2\b\b\u0001\u0010\u001a\u001a\u00020\u001b2\b\b\u0001\u0010d\u001a\u00020\u000bH\u00a7@\u00a2\u0006\u0002\u0010eJ2\u0010m\u001a\b\u0012\u0004\u0012\u00020\u00010\u00032\b\b\u0001\u0010c\u001a\u00020\u000b2\b\b\u0001\u0010\u001a\u001a\u00020\u001b2\b\b\u0001\u0010d\u001a\u00020\u000bH\u00a7@\u00a2\u0006\u0002\u0010eJ\u0014\u0010n\u001a\b\u0012\u0004\u0012\u00020\u00010\u0003H\u00a7@\u00a2\u0006\u0002\u0010TJ2\u0010o\u001a\b\u0012\u0004\u0012\u00020\u00010\u00032\b\b\u0001\u0010c\u001a\u00020\u000b2\b\b\u0001\u0010\u001a\u001a\u00020\u001b2\b\b\u0001\u0010d\u001a\u00020\u000bH\u00a7@\u00a2\u0006\u0002\u0010eJ\u001e\u0010p\u001a\b\u0012\u0004\u0012\u00020\u00010\u00032\b\b\u0001\u0010q\u001a\u00020\u000bH\u00a7@\u00a2\u0006\u0002\u0010rJ\u001e\u0010s\u001a\b\u0012\u0004\u0012\u00020\u00010\u00032\b\b\u0001\u0010s\u001a\u00020\u0006H\u00a7@\u00a2\u0006\u0002\u0010\\J(\u0010t\u001a\b\u0012\u0004\u0012\u00020\u00010\u00032\b\b\u0001\u0010u\u001a\u00020\u00062\b\b\u0001\u0010v\u001a\u00020\u0006H\u00a7@\u00a2\u0006\u0002\u0010wJ\u001a\u0010x\u001a\u000e\u0012\n\u0012\b\u0012\u0004\u0012\u00020z0y0\u0003H\u00a7@\u00a2\u0006\u0002\u0010TJ\u001a\u0010{\u001a\u000e\u0012\n\u0012\b\u0012\u0004\u0012\u00020|0y0\u0003H\u00a7@\u00a2\u0006\u0002\u0010TJ\u0014\u0010}\u001a\b\u0012\u0004\u0012\u00020~0\u0003H\u00a7@\u00a2\u0006\u0002\u0010TJ\u001f\u0010\u007f\u001a\t\u0012\u0005\u0012\u00030\u0080\u00010\u00032\b\b\u0001\u0010q\u001a\u00020\u000bH\u00a7@\u00a2\u0006\u0002\u0010rJ\u001c\u0010\u0081\u0001\u001a\u000f\u0012\u000b\u0012\t\u0012\u0005\u0012\u00030\u0082\u00010y0\u0003H\u00a7@\u00a2\u0006\u0002\u0010TJ>\u0010\u0083\u0001\u001a\u000f\u0012\u000b\u0012\t\u0012\u0005\u0012\u00030\u0084\u00010L0\u00032\t\b\u0001\u0010\u0085\u0001\u001a\u00020\u001b2\t\b\u0001\u0010\u0086\u0001\u001a\u00020\u000b2\t\b\u0001\u0010\u0087\u0001\u001a\u00020\u001bH\u00a7@\u00a2\u0006\u0003\u0010\u0088\u0001J\u0016\u0010\u0089\u0001\u001a\t\u0012\u0005\u0012\u00030\u008a\u00010\u0003H\u00a7@\u00a2\u0006\u0002\u0010TJ\u0016\u0010\u008b\u0001\u001a\t\u0012\u0005\u0012\u00030\u008c\u00010\u0003H\u00a7@\u00a2\u0006\u0002\u0010TJ\u0016\u0010\u008d\u0001\u001a\t\u0012\u0005\u0012\u00030\u008e\u00010\u0003H\u00a7@\u00a2\u0006\u0002\u0010TJ\u0016\u0010\u008f\u0001\u001a\t\u0012\u0005\u0012\u00030\u0090\u00010\u0003H\u00a7@\u00a2\u0006\u0002\u0010TJ3\u0010\u0091\u0001\u001a\u000f\u0012\u000b\u0012\t\u0012\u0005\u0012\u00030\u0092\u00010y0\u00032\t\b\u0001\u0010\u0086\u0001\u001a\u00020\u000b2\t\b\u0001\u0010\u0093\u0001\u001a\u00020\tH\u00a7@\u00a2\u0006\u0003\u0010\u0094\u0001J2\u0010\u0095\u0001\u001a\u000f\u0012\u000b\u0012\t\u0012\u0005\u0012\u00030\u0096\u00010y0\u00032\t\b\u0001\u0010\u0086\u0001\u001a\u00020\u000b2\b\b\u0001\u0010i\u001a\u00020\u000bH\u00a7@\u00a2\u0006\u0003\u0010\u0097\u0001J \u0010\u0098\u0001\u001a\t\u0012\u0005\u0012\u00030\u0099\u00010\u00032\b\b\u0001\u0010i\u001a\u00020\u001bH\u00a7@\u00a2\u0006\u0002\u0010jJ!\u0010\u009a\u0001\u001a\t\u0012\u0005\u0012\u00030\u009b\u00010\u00032\t\b\u0001\u0010\u009c\u0001\u001a\u00020\u0006H\u00a7@\u00a2\u0006\u0002\u0010\\J2\u0010\u009d\u0001\u001a\u000f\u0012\u000b\u0012\t\u0012\u0005\u0012\u00030\u009e\u00010y0\u00032\t\b\u0001\u0010\u0086\u0001\u001a\u00020\u000b2\b\b\u0001\u0010i\u001a\u00020\u000bH\u00a7@\u00a2\u0006\u0003\u0010\u0097\u0001J2\u0010\u009f\u0001\u001a\u000f\u0012\u000b\u0012\t\u0012\u0005\u0012\u00030\u0092\u00010y0\u00032\t\b\u0001\u0010\u0086\u0001\u001a\u00020\u000b2\b\b\u0001\u0010i\u001a\u00020\u000bH\u00a7@\u00a2\u0006\u0003\u0010\u0097\u0001J\u001c\u0010\u00a0\u0001\u001a\u000f\u0012\u000b\u0012\t\u0012\u0005\u0012\u00030\u00a1\u00010y0\u0003H\u00a7@\u00a2\u0006\u0002\u0010TJ \u0010\u00a2\u0001\u001a\t\u0012\u0005\u0012\u00030\u00a3\u00010\u00032\b\b\u0001\u0010u\u001a\u00020\u0006H\u00a7@\u00a2\u0006\u0002\u0010\\J\u0016\u0010\u00a4\u0001\u001a\t\u0012\u0005\u0012\u00030\u00a5\u00010\u0003H\u00a7@\u00a2\u0006\u0002\u0010TJ+\u0010\u00a6\u0001\u001a\b\u0012\u0004\u0012\u00020\u00060\u00032\t\b\u0001\u0010\u00a7\u0001\u001a\u00020\u001b2\b\b\u0001\u0010W\u001a\u00020\u0006H\u00a7@\u00a2\u0006\u0003\u0010\u00a8\u0001J\u0016\u0010\u00a9\u0001\u001a\t\u0012\u0005\u0012\u00030\u00aa\u00010\u0003H\u00a7@\u00a2\u0006\u0002\u0010TJ+\u0010\u00ab\u0001\u001a\b\u0012\u0004\u0012\u00020\u00010\u00032\b\b\u0001\u0010i\u001a\u00020\u001b2\t\b\u0001\u0010\u00ac\u0001\u001a\u00020\u0006H\u00a7@\u00a2\u0006\u0003\u0010\u00a8\u0001J&\u0010\u00ad\u0001\u001a\u000e\u0012\n\u0012\b\u0012\u0004\u0012\u00020\u001b0y0\u00032\t\b\u0001\u0010\u00ae\u0001\u001a\u00020\u001bH\u00a7@\u00a2\u0006\u0002\u0010jJK\u0010\u00af\u0001\u001a\t\u0012\u0005\u0012\u00030\u00b0\u00010\u00032\t\b\u0001\u0010\u00b1\u0001\u001a\u00020\u001b2\t\b\u0001\u0010\u00b2\u0001\u001a\u00020\u001b2\b\b\u0001\u0010V\u001a\u00020\u00062\b\b\u0001\u0010W\u001a\u00020\u00062\b\b\u0001\u0010X\u001a\u00020\u0006H\u00a7@\u00a2\u0006\u0003\u0010\u00b3\u0001J7\u0010\u00b4\u0001\u001a\t\u0012\u0005\u0012\u00030\u00b0\u00010\u00032\t\b\u0001\u0010\u00b5\u0001\u001a\u00020\u001b2\t\b\u0001\u0010\u00b6\u0001\u001a\u00020\u000b2\t\b\u0001\u0010\u00b7\u0001\u001a\u00020\u000bH\u00a7@\u00a2\u0006\u0002\u0010QJ8\u0010\u00b8\u0001\u001a\t\u0012\u0005\u0012\u00030\u00b0\u00010\u00032\t\b\u0001\u0010\u00b5\u0001\u001a\u00020\u001b2\t\b\u0001\u0010\u00b6\u0001\u001a\u00020\u001b2\t\b\u0001\u0010\u00b9\u0001\u001a\u00020\u0006H\u00a7@\u00a2\u0006\u0003\u0010\u00ba\u0001JK\u0010\u00bb\u0001\u001a\t\u0012\u0005\u0012\u00030\u00bc\u00010\u00032\t\b\u0001\u0010\u00b1\u0001\u001a\u00020\u001b2\t\b\u0001\u0010\u00b2\u0001\u001a\u00020\u001b2\b\b\u0001\u0010V\u001a\u00020\u00062\b\b\u0001\u0010W\u001a\u00020\u00062\b\b\u0001\u0010X\u001a\u00020\u0006H\u00a7@\u00a2\u0006\u0003\u0010\u00b3\u0001J7\u0010\u00bd\u0001\u001a\t\u0012\u0005\u0012\u00030\u00bc\u00010\u00032\t\b\u0001\u0010\u00b5\u0001\u001a\u00020\u001b2\t\b\u0001\u0010\u00b6\u0001\u001a\u00020\u000b2\t\b\u0001\u0010\u00b7\u0001\u001a\u00020\u000bH\u00a7@\u00a2\u0006\u0002\u0010QJ8\u0010\u00be\u0001\u001a\t\u0012\u0005\u0012\u00030\u00bc\u00010\u00032\t\b\u0001\u0010\u00b5\u0001\u001a\u00020\u001b2\t\b\u0001\u0010\u00b6\u0001\u001a\u00020\u001b2\t\b\u0001\u0010\u00b9\u0001\u001a\u00020\u0006H\u00a7@\u00a2\u0006\u0003\u0010\u00ba\u0001J\'\u0010\u00bf\u0001\u001a\u000f\u0012\u000b\u0012\t\u0012\u0005\u0012\u00030\u00c0\u00010y0\u00032\t\b\u0001\u0010\u0086\u0001\u001a\u00020\u000bH\u00a7@\u00a2\u0006\u0002\u0010rJ\u001c\u0010\u00c1\u0001\u001a\u000f\u0012\u000b\u0012\t\u0012\u0005\u0012\u00030\u00c2\u00010L0\u0003H\u00a7@\u00a2\u0006\u0002\u0010TJ!\u0010\u00c3\u0001\u001a\t\u0012\u0005\u0012\u00030\u00c4\u00010\u00032\t\b\u0001\u0010\u00c5\u0001\u001a\u00020\u001bH\u00a7@\u00a2\u0006\u0002\u0010jJ3\u0010\u00c6\u0001\u001a\u000f\u0012\u000b\u0012\t\u0012\u0005\u0012\u00030\u00c7\u00010L0\u00032\t\b\u0001\u0010\u0087\u0001\u001a\u00020\u001b2\t\b\u0001\u0010\u00c5\u0001\u001a\u00020\u001bH\u00a7@\u00a2\u0006\u0003\u0010\u00c8\u0001J\u0015\u0010\u00c9\u0001\u001a\b\u0012\u0004\u0012\u00020\u00010\u0003H\u00a7@\u00a2\u0006\u0002\u0010TJ+\u0010\u00ca\u0001\u001a\b\u0012\u0004\u0012\u00020\u00010\u00032\b\b\u0001\u0010i\u001a\u00020\u000b2\t\b\u0001\u0010\u00cb\u0001\u001a\u00020\u0006H\u00a7@\u00a2\u0006\u0003\u0010\u00cc\u0001J\'\u0010\u00cd\u0001\u001a\u000f\u0012\u000b\u0012\t\u0012\u0005\u0012\u00030\u00ce\u00010L0\u00032\t\b\u0001\u0010\u00cf\u0001\u001a\u00020\u000bH\u00a7@\u00a2\u0006\u0002\u0010rJ\'\u0010\u00d0\u0001\u001a\u000f\u0012\u000b\u0012\t\u0012\u0005\u0012\u00030\u00d1\u00010L0\u00032\t\b\u0001\u0010\u00cf\u0001\u001a\u00020\u000bH\u00a7@\u00a2\u0006\u0002\u0010rJ3\u0010\u00d2\u0001\u001a\u000f\u0012\u000b\u0012\t\u0012\u0005\u0012\u00030\u00d3\u00010L0\u00032\t\b\u0001\u0010\u00cf\u0001\u001a\u00020\u000b2\t\b\u0001\u0010\u00d4\u0001\u001a\u00020\tH\u00a7@\u00a2\u0006\u0003\u0010\u0094\u0001J\'\u0010\u00d5\u0001\u001a\u000f\u0012\u000b\u0012\t\u0012\u0005\u0012\u00030\u00d6\u00010L0\u00032\t\b\u0001\u0010\u00cf\u0001\u001a\u00020\u000bH\u00a7@\u00a2\u0006\u0002\u0010rJ\'\u0010\u00d7\u0001\u001a\u000f\u0012\u000b\u0012\t\u0012\u0005\u0012\u00030\u00d8\u00010L0\u00032\t\b\u0001\u0010\u00cf\u0001\u001a\u00020\u000bH\u00a7@\u00a2\u0006\u0002\u0010rJW\u0010\u00d9\u0001\u001a\b\u0012\u0004\u0012\u00020\u00010\u00032\t\b\u0001\u0010\u00da\u0001\u001a\u00020\u001b2\t\b\u0001\u0010\u00db\u0001\u001a\u00020\u001b2\t\b\u0001\u0010\u00dc\u0001\u001a\u00020\u000b2\b\b\u0001\u0010\u001f\u001a\u00020\u000b2\t\b\u0001\u0010\u00dd\u0001\u001a\u00020\u00062\t\b\u0001\u0010\u00cb\u0001\u001a\u00020\u0006H\u00a7@\u00a2\u0006\u0003\u0010\u00de\u0001J\u0015\u0010\u00df\u0001\u001a\b\u0012\u0004\u0012\u00020\u00010\u0003H\u00a7@\u00a2\u0006\u0002\u0010TJ\u0016\u0010\u00e0\u0001\u001a\t\u0012\u0005\u0012\u00030\u00e1\u00010\u0003H\u00a7@\u00a2\u0006\u0002\u0010TJ\u001f\u0010\u00e2\u0001\u001a\b\u0012\u0004\u0012\u00020\u00010\u00032\b\b\u0001\u0010i\u001a\u00020\u001bH\u00a7@\u00a2\u0006\u0002\u0010jJ \u0010\u00e3\u0001\u001a\b\u0012\u0004\u0012\u00020\u00010\u00032\t\b\u0001\u0010\u00e4\u0001\u001a\u00020\u000bH\u00a7@\u00a2\u0006\u0002\u0010rJ\u0015\u0010\u00e5\u0001\u001a\b\u0012\u0004\u0012\u00020\u00010\u0003H\u00a7@\u00a2\u0006\u0002\u0010TJ!\u0010\u00e6\u0001\u001a\t\u0012\u0005\u0012\u00030\u00e7\u00010\u00032\t\b\u0001\u0010\u00ac\u0001\u001a\u00020\u0006H\u00a7@\u00a2\u0006\u0002\u0010\\J\u0016\u0010\u00e8\u0001\u001a\t\u0012\u0005\u0012\u00030\u00e9\u00010\u0003H\u00a7@\u00a2\u0006\u0002\u0010TJ+\u0010\u00ea\u0001\u001a\b\u0012\u0004\u0012\u00020\u00010\u00032\b\b\u0001\u0010i\u001a\u00020\u001b2\t\b\u0001\u0010\u00eb\u0001\u001a\u00020\u001bH\u00a7@\u00a2\u0006\u0003\u0010\u00c8\u0001J+\u0010\u00ec\u0001\u001a\b\u0012\u0004\u0012\u00020\u00010\u00032\b\b\u0001\u0010i\u001a\u00020\u001b2\t\b\u0001\u0010\u00ed\u0001\u001a\u00020\u001bH\u00a7@\u00a2\u0006\u0003\u0010\u00c8\u0001J \u0010\u00ee\u0001\u001a\b\u0012\u0004\u0012\u00020\u00010\u00032\t\b\u0001\u0010\u00ef\u0001\u001a\u00020\u000bH\u00a7@\u00a2\u0006\u0002\u0010rJa\u0010\u00f0\u0001\u001a\b\u0012\u0004\u0012\u00020\u00010\u00032\b\b\u0001\u0010i\u001a\u00020\u001b2\t\b\u0001\u0010\u00f1\u0001\u001a\u00020\u001b2\b\b\u0001\u0010-\u001a\u00020\u00062\t\b\u0001\u0010\u00f2\u0001\u001a\u00020\u00062\t\b\u0001\u0010\u00f3\u0001\u001a\u00020\u00062\t\b\u0001\u0010\u00f4\u0001\u001a\u00020\u00062\t\b\u0001\u0010\u00f5\u0001\u001a\u00020\u001bH\u00a7@\u00a2\u0006\u0003\u0010\u00f6\u0001J7\u0010\u00f7\u0001\u001a\b\u0012\u0004\u0012\u00020\u00010\u00032\t\b\u0001\u0010\u00f8\u0001\u001a\u00020\u000b2\t\b\u0001\u0010\u00f9\u0001\u001a\u00020\u001b2\t\b\u0001\u0010\u00fa\u0001\u001a\u00020\u0006H\u00a7@\u00a2\u0006\u0003\u0010\u00fb\u0001J\u0015\u0010\u00fc\u0001\u001a\b\u0012\u0004\u0012\u00020\u00010\u0003H\u00a7@\u00a2\u0006\u0002\u0010TJY\u0010\u00fd\u0001\u001a\t\u0012\u0005\u0012\u00030\u00fe\u00010\u00032\t\b\u0001\u0010\u00ff\u0001\u001a\u00020\u001b2\t\b\u0001\u0010\u0080\u0002\u001a\u00020\u00062\t\b\u0001\u0010\u0081\u0002\u001a\u00020\u00062\t\b\u0001\u0010\u0082\u0002\u001a\u00020\u00062\t\b\u0001\u0010\u0083\u0002\u001a\u00020\u00062\t\b\u0001\u0010\u0084\u0002\u001a\u00020\u001bH\u00a7@\u00a2\u0006\u0003\u0010\u0085\u0002J`\u0010\u0086\u0002\u001a\b\u0012\u0004\u0012\u00020\u00010\u00032\b\b\u0001\u0010\n\u001a\u00020\u000b2\t\b\u0001\u0010\u0087\u0002\u001a\u00020\u000b2\t\b\u0001\u0010\u0088\u0002\u001a\u00020\u000b2\t\b\u0001\u0010\u0089\u0002\u001a\u00020\u000b2\t\b\u0001\u0010\u008a\u0002\u001a\u00020\u001b2\b\b\u0001\u0010\"\u001a\u00020\u00062\b\b\u0001\u0010#\u001a\u00020\u0006H\u00a7@\u00a2\u0006\u0003\u0010\u008b\u0002J\u0015\u0010\u008c\u0002\u001a\b\u0012\u0004\u0012\u00020\u001b0\u0003H\u00a7@\u00a2\u0006\u0002\u0010TJ\u0016\u0010\u008d\u0002\u001a\t\u0012\u0005\u0012\u00030\u008e\u00020\u0003H\u00a7@\u00a2\u0006\u0002\u0010TJ\u00ea\u0001\u0010\u008f\u0002\u001a\t\u0012\u0005\u0012\u00030\u0090\u00020\u00032\b\b\u0001\u0010\u0007\u001a\u00020\u00062\b\b\u0001\u0010\b\u001a\u00020\t2\b\b\u0001\u0010\n\u001a\u00020\u000b2\b\b\u0001\u0010\f\u001a\u00020\u000b2\b\b\u0001\u0010\r\u001a\u00020\u000b2\b\b\u0001\u0010\u000e\u001a\u00020\u00062\b\b\u0001\u0010\u000f\u001a\u00020\t2\b\b\u0001\u0010\u0010\u001a\u00020\t2\b\b\u0001\u0010\u0011\u001a\u00020\t2\b\b\u0001\u0010\u0012\u001a\u00020\t2\b\b\u0001\u0010\u0013\u001a\u00020\t2\b\b\u0001\u0010\u0014\u001a\u00020\t2\b\b\u0001\u0010\u0015\u001a\u00020\t2\b\b\u0001\u0010\u0016\u001a\u00020\u000b2\b\b\u0001\u0010\u0017\u001a\u00020\u000b2\b\b\u0001\u0010\u0018\u001a\u00020\u000b2\b\b\u0001\u0010\u0019\u001a\u00020\t2\t\b\u0001\u0010\u0091\u0002\u001a\u00020\u001b2\b\b\u0001\u0010d\u001a\u00020\u000b2\b\b\u0001\u0010c\u001a\u00020\u000b2\b\b\u0001\u0010\u001a\u001a\u00020\u001bH\u00a7@\u00a2\u0006\u0003\u0010\u0092\u0002J \u0010\u0093\u0002\u001a\b\u0012\u0004\u0012\u00020\u00010\u00032\t\b\u0001\u0010\u0082\u0002\u001a\u00020\u0006H\u00a7@\u00a2\u0006\u0002\u0010\\JJ\u0010\u0094\u0002\u001a\t\u0012\u0005\u0012\u00030\u0095\u00020\u00032\b\b\u0001\u0010\u001f\u001a\u00020\u001b2\t\b\u0001\u0010\u0091\u0002\u001a\u00020\u001b2\b\b\u0001\u0010d\u001a\u00020\u000b2\b\b\u0001\u0010c\u001a\u00020\u000b2\b\b\u0001\u0010\u001a\u001a\u00020\u001bH\u00a7@\u00a2\u0006\u0003\u0010\u0096\u0002J \u0010\u0097\u0002\u001a\b\u0012\u0004\u0012\u00020\u00010\u00032\t\b\u0001\u0010\u0083\u0002\u001a\u00020\u0006H\u00a7@\u00a2\u0006\u0002\u0010\\J+\u0010\u0098\u0002\u001a\b\u0012\u0004\u0012\u00020\u00010\u00032\t\b\u0001\u0010\u0099\u0002\u001a\u00020\u00062\t\b\u0001\u0010\u009a\u0002\u001a\u00020\u0006H\u00a7@\u00a2\u0006\u0002\u0010wJ\u009e\u0003\u0010\u009b\u0002\u001a\t\u0012\u0005\u0012\u00030\u009c\u00020\u00032\b\b\u0001\u0010\u000e\u001a\u00020\u00062\b\b\u0001\u0010\"\u001a\u00020\u00062\b\b\u0001\u0010#\u001a\u00020\u00062\b\b\u0001\u0010$\u001a\u00020\u001b2\b\b\u0001\u0010%\u001a\u00020\u001b2\b\b\u0001\u0010&\u001a\u00020\t2\b\b\u0001\u0010\u000f\u001a\u00020\t2\b\b\u0001\u0010\u0010\u001a\u00020\t2\b\b\u0001\u0010\u0011\u001a\u00020\t2\b\b\u0001\u0010\u0012\u001a\u00020\t2\b\b\u0001\u0010\u0013\u001a\u00020\t2\b\b\u0001\u0010\u0014\u001a\u00020\t2\b\b\u0001\u0010\u0015\u001a\u00020\t2\b\b\u0001\u0010\'\u001a\u00020\t2\b\b\u0001\u0010(\u001a\u00020\u001b2\b\b\u0001\u0010)\u001a\u00020\u001b2\b\b\u0001\u0010*\u001a\u00020\t2\b\b\u0001\u0010+\u001a\u00020\t2\b\b\u0001\u0010,\u001a\u00020\u001b2\b\b\u0001\u0010-\u001a\u00020\u00062\b\b\u0001\u0010.\u001a\u00020\t2\b\b\u0001\u0010/\u001a\u00020\t2\b\b\u0001\u00100\u001a\u00020\u001b2\b\b\u0001\u00101\u001a\u00020\u001b2\b\b\u0001\u00102\u001a\u00020\u000b2\b\b\u0001\u00103\u001a\u00020\u000b2\b\b\u0001\u00104\u001a\u00020\t2\b\b\u0001\u00105\u001a\u00020\t2\b\b\u0001\u00106\u001a\u00020\t2\b\b\u0001\u00107\u001a\u00020\t2\b\b\u0001\u00108\u001a\u00020\t2\b\b\u0001\u00109\u001a\u00020\t2\b\b\u0001\u0010:\u001a\u00020\t2\b\b\u0001\u0010;\u001a\u00020\t2\b\b\u0001\u0010<\u001a\u00020\u00062\t\b\u0001\u0010\u0091\u0002\u001a\u00020\u001b2\b\b\u0001\u0010d\u001a\u00020\u000b2\b\b\u0001\u0010c\u001a\u00020\u000b2\b\b\u0001\u0010\u001a\u001a\u00020\u001bH\u00a7@\u00a2\u0006\u0003\u0010\u009d\u0002J\u009c\u0002\u0010\u009e\u0002\u001a\t\u0012\u0005\u0012\u00030\u009f\u00020\u00032\b\b\u0001\u0010\"\u001a\u00020\u00062\b\b\u0001\u0010\u000e\u001a\u00020\u00062\b\b\u0001\u0010?\u001a\u00020\u001b2\b\b\u0001\u0010@\u001a\u00020\u001b2\b\b\u0001\u0010A\u001a\u00020\u001b2\b\b\u0001\u0010B\u001a\u00020\u001b2\b\b\u0001\u0010C\u001a\u00020\u001b2\b\b\u0001\u0010-\u001a\u00020\u00062\b\b\u0001\u0010.\u001a\u00020\t2\b\b\u0001\u0010/\u001a\u00020\t2\b\b\u0001\u00100\u001a\u00020\u001b2\b\b\u0001\u00101\u001a\u00020\u001b2\b\b\u0001\u00102\u001a\u00020\u000b2\b\b\u0001\u00103\u001a\u00020\u000b2\b\b\u0001\u00104\u001a\u00020\t2\b\b\u0001\u00105\u001a\u00020\t2\b\b\u0001\u00106\u001a\u00020\t2\b\b\u0001\u00107\u001a\u00020\t2\b\b\u0001\u00108\u001a\u00020\t2\b\b\u0001\u00109\u001a\u00020\t2\b\b\u0001\u0010:\u001a\u00020\t2\b\b\u0001\u0010;\u001a\u00020\t2\t\b\u0001\u0010\u0091\u0002\u001a\u00020\u001b2\b\b\u0001\u0010d\u001a\u00020\u000b2\b\b\u0001\u0010c\u001a\u00020\u000b2\b\b\u0001\u0010\u001a\u001a\u00020\u001bH\u00a7@\u00a2\u0006\u0003\u0010\u00a0\u0002J \u0010\u00a1\u0002\u001a\b\u0012\u0004\u0012\u00020\u00010\u00032\t\b\u0001\u0010\u0081\u0002\u001a\u00020\u0006H\u00a7@\u00a2\u0006\u0002\u0010\\Jr\u0010\u00a2\u0002\u001a\t\u0012\u0005\u0012\u00030\u00a3\u00020\u00032\b\b\u0001\u0010\"\u001a\u00020\u00062\b\b\u0001\u0010#\u001a\u00020\u00062\b\b\u0001\u0010G\u001a\u00020\u00062\b\b\u0001\u0010H\u001a\u00020\u00062\b\b\u0001\u0010I\u001a\u00020\u001b2\t\b\u0001\u0010\u0091\u0002\u001a\u00020\u001b2\b\b\u0001\u0010d\u001a\u00020\u000b2\b\b\u0001\u0010c\u001a\u00020\u000b2\b\b\u0001\u0010\u001a\u001a\u00020\u001bH\u00a7@\u00a2\u0006\u0003\u0010\u00a4\u0002J \u0010\u00a5\u0002\u001a\b\u0012\u0004\u0012\u00020\u00010\u00032\t\b\u0001\u0010\u00cb\u0001\u001a\u00020\u0006H\u00a7@\u00a2\u0006\u0002\u0010\\J5\u0010\u00a6\u0002\u001a\b\u0012\u0004\u0012\u00020\u00010\u00032\t\b\u0001\u0010\u00a7\u0002\u001a\u00020\u00062\b\b\u0001\u0010\u0007\u001a\u00020\u00062\t\b\u0001\u0010\u00a8\u0002\u001a\u00020\u0006H\u00a7@\u00a2\u0006\u0002\u0010YJ7\u0010\u00a9\u0002\u001a\t\u0012\u0005\u0012\u00030\u00fe\u00010\u00032\b\b\u0001\u0010W\u001a\u00020\u00062\t\b\u0001\u0010\u009a\u0002\u001a\u00020\u00062\t\b\u0001\u0010\u0084\u0002\u001a\u00020\u001bH\u00a7@\u00a2\u0006\u0003\u0010\u00aa\u0002J+\u0010\u00ab\u0002\u001a\b\u0012\u0004\u0012\u00020\u00010\u00032\b\b\u0001\u0010i\u001a\u00020\u000b2\t\b\u0001\u0010\u00ac\u0001\u001a\u00020\u0006H\u00a7@\u00a2\u0006\u0003\u0010\u00cc\u0001J \u0010\u00ac\u0002\u001a\b\u0012\u0004\u0012\u00020\u00010\u00032\t\b\u0001\u0010\u00ad\u0002\u001a\u00020\u001bH\u00a7@\u00a2\u0006\u0002\u0010jJ \u0010\u00ae\u0002\u001a\b\u0012\u0004\u0012\u00020\u00010\u00032\t\b\u0001\u0010\u00ad\u0002\u001a\u00020\u001bH\u00a7@\u00a2\u0006\u0002\u0010j\u00a8\u0006\u00b0\u0002"}, d2 = {"Lcom/lijianqiang12/silent/data/model/net/api/Api;", "", "addAppLimit", "Lcom/lijianqiang12/silent/data/model/net/pojos/ApiResponse;", "Lcom/lijianqiang12/silent/data/model/net/pojos/AddSyncResult;", "appLimitIndexId", "", "appPkg", "ifAllDay", "", "startTime", "", "endTime", "limitLength", "title", "sunday", "monday", "tuesday", "wednesday", "thursday", "friday", "saturday", "editStartTime", "editEndTime", "editMoney", "valid", "version", "", "(Ljava/lang/String;Ljava/lang/String;ZJJJLjava/lang/String;ZZZZZZZJJJZILkotlin/coroutines/Continuation;)Ljava/lang/Object;", "addFast", "fastIndexId", "length", "(Ljava/lang/String;IILkotlin/coroutines/Continuation;)Ljava/lang/Object;", "addSchedule", "tomatoIndexId", "scheduleIndexId", "startHour", "startMinute", "validate", "useTomato", "endHour", "endMinute", "isRecycle", "isDenyChange", "denyChangeLength", "bgUrl", "isRemoveNotification", "isSilent", "startVoiceNotify", "endVoiceNotify", "startShakeNotify", "endShakeNotify", "whiteFollowGlobal", "bgUrlFollowGlobal", "isRemoveNotificationFollowGlobal", "isSilentFollowGlobal", "startVoiceNotifyFollowGlobal", "endVoiceNotifyFollowGlobal", "startShakeNotifyFollowGlobal", "endShakeNotifyFollowGlobal", "jumpDate", "(Ljava/lang/String;Ljava/lang/String;Ljava/lang/String;IIZZZZZZZZZIIZZILjava/lang/String;ZZIIJJZZZZZZZZLjava/lang/String;ILkotlin/coroutines/Continuation;)Ljava/lang/Object;", "addTomato", "tomatoWorkLength", "tomatoRestLength", "tomatoCount", "tomatoLongRestPerCount", "tomatoLongRestLength", "(Ljava/lang/String;Ljava/lang/String;IIIIILjava/lang/String;ZZIIJJZZZZZZZZILkotlin/coroutines/Continuation;)Ljava/lang/Object;", "addWhiteApp", "whiteAppIndexId", "pkg", "mainActivity", "maxLen", "(Ljava/lang/String;Ljava/lang/String;Ljava/lang/String;Ljava/lang/String;Ljava/lang/String;IILkotlin/coroutines/Continuation;)Ljava/lang/Object;", "allRooms", "", "Lcom/lijianqiang12/silent/data/model/net/pojos/AllRoom;", "type", "lastRoomId", "lastLevelCount", "(IJJLkotlin/coroutines/Continuation;)Ljava/lang/Object;", "appUpdate", "Lcom/lijianqiang12/silent/data/model/net/pojos/AppUpdate;", "(Lkotlin/coroutines/Continuation;)Ljava/lang/Object;", "applyDelivery", "name", "phone", "address", "(Ljava/lang/String;Ljava/lang/String;Ljava/lang/String;Lkotlin/coroutines/Continuation;)Ljava/lang/Object;", "bindQQ", "qqId", "(Ljava/lang/String;Lkotlin/coroutines/Continuation;)Ljava/lang/Object;", "bindSINA", "sinaId", "bindWX", "wxId", "deleteAllAccount", "deleteAppLimit", "uuid", "syncTime", "(JIJLkotlin/coroutines/Continuation;)Ljava/lang/Object;", "deleteFast", "deleteQQ", "deleteRoom", "roomId", "(ILkotlin/coroutines/Continuation;)Ljava/lang/Object;", "deleteSINA", "deleteSchedule", "deleteTomato", "deleteWX", "deleteWhiteApp", "developerUnlock", "unlockUserId", "(JLkotlin/coroutines/Continuation;)Ljava/lang/Object;", "duihuanma", "friendUnlock", "userCode", "unlockCode", "(Ljava/lang/String;Ljava/lang/String;Lkotlin/coroutines/Continuation;)Ljava/lang/Object;", "getBaozang", "", "Lcom/lijianqiang12/silent/data/model/net/pojos/BaoZangApp;", "getBuyHistory", "Lcom/lijianqiang12/silent/data/model/net/pojos/BuyHistory;", "getConfig", "Lcom/lijianqiang12/silent/data/model/net/pojos/MyConfig;", "getDeveloperUnlockInfo", "Lcom/lijianqiang12/silent/data/model/net/pojos/DeveloperUnlockUserInfo;", "getFastDenyPageExample", "Lcom/lijianqiang12/silent/data/model/net/pojos/FastDenyPageExample;", "getImages", "Lcom/lijianqiang12/silent/data/model/net/pojos/LockBg;", "imgStyle", "lastId", "limit", "(IJILkotlin/coroutines/Continuation;)Ljava/lang/Object;", "getInvitePageInfo", "Lcom/lijianqiang12/silent/data/model/net/pojos/InvitePageInfo;", "getLaunchDialog", "Lcom/lijianqiang12/silent/data/model/net/pojos/LaunchDialog;", "getMoney", "Lcom/lijianqiang12/silent/data/model/net/pojos/VIPMoney;", "getPunchCardMsg", "Lcom/lijianqiang12/silent/data/model/net/pojos/PunchCardMsg;", "getPunchCards", "Lcom/lijianqiang12/silent/data/model/net/pojos/ThePunchCard;", "isMyself", "(JZLkotlin/coroutines/Continuation;)Ljava/lang/Object;", "getRoomBoard", "Lcom/lijianqiang12/silent/data/model/net/pojos/RoomDetailBoard;", "(JJLkotlin/coroutines/Continuation;)Ljava/lang/Object;", "getRoomDesc", "Lcom/lijianqiang12/silent/data/model/net/pojos/RoomDetailData;", "getRoomFromCode", "Lcom/lijianqiang12/silent/data/model/net/pojos/RoomInfoFromCode;", "roomCode", "getRoomMemberInfo", "Lcom/lijianqiang12/silent/data/model/net/pojos/RoomDetailMember;", "getRoomMemberPunch", "getRoomRequestList", "Lcom/lijianqiang12/silent/data/model/net/pojos/RoomRequestBean;", "getUnlockCode", "Lcom/lijianqiang12/silent/data/model/net/pojos/SubUnlockCode;", "getUserCode", "Lcom/lijianqiang12/silent/data/model/net/pojos/UserCode;", "getVerifyCode", "codeType", "(ILjava/lang/String;Lkotlin/coroutines/Continuation;)Ljava/lang/Object;", "getWellKnowWord", "Lcom/lijianqiang12/silent/data/model/net/pojos/WellKnowWord;", "joinRoom", "pwd", "lockLength", "deltaWeek", "makeAlipayOrder", "Lcom/lijianqiang12/silent/data/model/net/pojos/AlipayOrder;", "currentPrice", "originalPrice", "(IILjava/lang/String;Ljava/lang/String;Ljava/lang/String;Lkotlin/coroutines/Continuation;)Ljava/lang/Object;", "makeAlipayOrderAppLimit", "typeOrder", "forceAmount", "appLimitUuid", "makeAlipayOrderForceUnlock", "forceUnlockInfo", "(IILjava/lang/String;Lkotlin/coroutines/Continuation;)Ljava/lang/Object;", "makeWXOrder", "Lcom/lijianqiang12/silent/data/model/net/pojos/WxOrder;", "makeWXOrderAppLimit", "makeWXOrderForceUnlock", "msgList", "Lcom/lijianqiang12/silent/data/model/net/pojos/MyMsg;", "myJoinedRooms", "Lcom/lijianqiang12/silent/data/model/net/pojos/MyJoinedRoom;", "myTrendNo", "Lcom/lijianqiang12/silent/data/model/net/pojos/MyTrend;", "deltaDay", "offTimeTop", "Lcom/lijianqiang12/silent/data/model/net/pojos/OffTimeDetail;", "(IILkotlin/coroutines/Continuation;)Ljava/lang/Object;", "offline", "postBoard", "word", "(JLjava/lang/String;Lkotlin/coroutines/Continuation;)Ljava/lang/Object;", "pullAppLimit", "Lcom/lijianqiang12/silent/data/model/net/pojos/PullAppLimitResult;", "latestTime", "pullFast", "Lcom/lijianqiang12/silent/data/model/net/pojos/PullFastResult;", "pullSchedule", "Lcom/lijianqiang12/silent/data/model/net/pojos/PullScheduleResult;", "isUpdateDb", "pullTomato", "Lcom/lijianqiang12/silent/data/model/net/pojos/PullTomatoResult;", "pullWhiteApp", "Lcom/lijianqiang12/silent/data/model/net/pojos/PullWhiteResult;", "punchCard", "lockNumber", "goOnDays", "totalLength", "imgUrl", "(IIJJLjava/lang/String;Ljava/lang/String;Lkotlin/coroutines/Continuation;)Ljava/lang/Object;", "queryOrder", "queryUserInfo", "Lcom/lijianqiang12/silent/data/model/net/pojos/UserInfo;", "quitRoom", "readLaunchDialogMsg", "launchDialogMsgId", "readMsg", "refreshForceUnlockPwd", "Lcom/lijianqiang12/silent/data/model/net/pojos/ForceUnlockPwd;", "refreshState", "Lcom/lijianqiang12/silent/data/model/net/pojos/RefreshStateResponse;", "removeBoard", "boardId", "removeMember", "memberId", "removePunchCard", "punchId", "requestRoom", "roomType", "roomName", "roomDesc", "roomPwd", "changeType", "(IILjava/lang/String;Ljava/lang/String;Ljava/lang/String;Ljava/lang/String;ILkotlin/coroutines/Continuation;)Ljava/lang/Object;", "requestRoomOK", "requestRoomId", "result", "reason", "(JILjava/lang/String;Lkotlin/coroutines/Continuation;)Ljava/lang/Object;", "shareCard", "socialAccountLogin", "Lcom/lijianqiang12/silent/data/model/net/pojos/LoginResponse;", "socialType", "uid", "username", "avatar", "gender", "inviteCode", "(ILjava/lang/String;Ljava/lang/String;Ljava/lang/String;Ljava/lang/String;ILkotlin/coroutines/Continuation;)Ljava/lang/Object;", "syncHistory", "trueStartTime", "timeLength", "trueTimeLength", "lockType", "(JJJJILjava/lang/String;Ljava/lang/String;Lkotlin/coroutines/Continuation;)Ljava/lang/Object;", "syncOnline", "unreadMsgCount", "Lcom/lijianqiang12/silent/data/model/net/pojos/UnReadMsg;", "updateAppLimit", "Lcom/lijianqiang12/silent/data/model/net/pojos/UpdateAppLimitResult;", "trend", "(Ljava/lang/String;ZJJJLjava/lang/String;ZZZZZZZJJJZIJJILkotlin/coroutines/Continuation;)Ljava/lang/Object;", "updateAvatar", "updateFast", "Lcom/lijianqiang12/silent/data/model/net/pojos/UpdateFastResult;", "(IIJJILkotlin/coroutines/Continuation;)Ljava/lang/Object;", "updateGender", "updateMobile", "mobile", "code", "updateSchedule", "Lcom/lijianqiang12/silent/data/model/net/pojos/UpdateScheduleResult;", "(Ljava/lang/String;Ljava/lang/String;Ljava/lang/String;IIZZZZZZZZZIIZZILjava/lang/String;ZZIIJJZZZZZZZZLjava/lang/String;IJJILkotlin/coroutines/Continuation;)Ljava/lang/Object;", "updateTomato", "Lcom/lijianqiang12/silent/data/model/net/pojos/UpdateTomatoResult;", "(Ljava/lang/String;Ljava/lang/String;IIIIILjava/lang/String;ZZIIJJZZZZZZZZIJJILkotlin/coroutines/Continuation;)Ljava/lang/Object;", "updateUsername", "updateWhiteApp", "Lcom/lijianqiang12/silent/data/model/net/pojos/UpdateWhiteResult;", "(Ljava/lang/String;Ljava/lang/String;Ljava/lang/String;Ljava/lang/String;IIJJILkotlin/coroutines/Continuation;)Ljava/lang/Object;", "updateWord", "uploadDenyPkgInfo", "appName", "appMainActivity", "verifyCode", "(Ljava/lang/String;Ljava/lang/String;ILkotlin/coroutines/Continuation;)Ljava/lang/Object;", "verifyRoomPwd", "wellKnowWordShare", "wordId", "wellKnowWordStar", "Companion", "data_debug"})
public abstract interface Api {
    @org.jetbrains.annotations.NotNull()
    public static final java.lang.String HOST = "shuge888.com";
    public static final int PORT = 33333;
    @org.jetbrains.annotations.NotNull()
    public static final java.lang.String BASE_URL = "https://offphone.shuge888.com:33333";
    @org.jetbrains.annotations.NotNull()
    public static final com.lijianqiang12.silent.data.model.net.api.Api.Companion Companion = null;
    
    @retrofit2.http.FormUrlEncoded()
    @retrofit2.http.POST(value = "/account/getVerifyCode")
    @org.jetbrains.annotations.Nullable()
    public abstract java.lang.Object getVerifyCode(@retrofit2.http.Field(value = "codeType")
    int codeType, @retrofit2.http.Field(value = "phone")
    @org.jetbrains.annotations.NotNull()
    java.lang.String phone, @org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super com.lijianqiang12.silent.data.model.net.pojos.ApiResponse<java.lang.String>> $completion);
    
    @retrofit2.http.FormUrlEncoded()
    @retrofit2.http.POST(value = "/account/verifyCode")
    @org.jetbrains.annotations.Nullable()
    public abstract java.lang.Object verifyCode(@retrofit2.http.Field(value = "phone")
    @org.jetbrains.annotations.NotNull()
    java.lang.String phone, @retrofit2.http.Field(value = "code")
    @org.jetbrains.annotations.NotNull()
    java.lang.String code, @retrofit2.http.Field(value = "inviteCode")
    int inviteCode, @org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super com.lijianqiang12.silent.data.model.net.pojos.ApiResponse<com.lijianqiang12.silent.data.model.net.pojos.LoginResponse>> $completion);
    
    @retrofit2.http.FormUrlEncoded()
    @retrofit2.http.POST(value = "/account/socialAccountLogin")
    @org.jetbrains.annotations.Nullable()
    public abstract java.lang.Object socialAccountLogin(@retrofit2.http.Field(value = "socialType")
    int socialType, @retrofit2.http.Field(value = "uid")
    @org.jetbrains.annotations.NotNull()
    java.lang.String uid, @retrofit2.http.Field(value = "username")
    @org.jetbrains.annotations.NotNull()
    java.lang.String username, @retrofit2.http.Field(value = "avatar")
    @org.jetbrains.annotations.NotNull()
    java.lang.String avatar, @retrofit2.http.Field(value = "gender")
    @org.jetbrains.annotations.NotNull()
    java.lang.String gender, @retrofit2.http.Field(value = "inviteCode")
    int inviteCode, @org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super com.lijianqiang12.silent.data.model.net.pojos.ApiResponse<com.lijianqiang12.silent.data.model.net.pojos.LoginResponse>> $completion);
    
    @retrofit2.http.GET(value = "/account/refreshState")
    @org.jetbrains.annotations.Nullable()
    public abstract java.lang.Object refreshState(@org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super com.lijianqiang12.silent.data.model.net.pojos.ApiResponse<com.lijianqiang12.silent.data.model.net.pojos.RefreshStateResponse>> $completion);
    
    @retrofit2.http.GET(value = "/lock/getImages")
    @org.jetbrains.annotations.Nullable()
    public abstract java.lang.Object getImages(@retrofit2.http.Query(value = "imgStyle")
    int imgStyle, @retrofit2.http.Query(value = "lastId")
    long lastId, @retrofit2.http.Query(value = "limit")
    int limit, @org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super com.lijianqiang12.silent.data.model.net.pojos.ApiResponse<? extends java.util.List<com.lijianqiang12.silent.data.model.net.pojos.LockBg>>> $completion);
    
    @retrofit2.http.GET(value = "/account/getInvitePageInfo")
    @org.jetbrains.annotations.Nullable()
    public abstract java.lang.Object getInvitePageInfo(@org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super com.lijianqiang12.silent.data.model.net.pojos.ApiResponse<com.lijianqiang12.silent.data.model.net.pojos.InvitePageInfo>> $completion);
    
    @retrofit2.http.GET(value = "/offtime/offTimeTop")
    @org.jetbrains.annotations.Nullable()
    public abstract java.lang.Object offTimeTop(@retrofit2.http.Query(value = "limit")
    int limit, @retrofit2.http.Query(value = "deltaDay")
    int deltaDay, @org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super com.lijianqiang12.silent.data.model.net.pojos.ApiResponse<? extends java.util.List<com.lijianqiang12.silent.data.model.net.pojos.OffTimeDetail>>> $completion);
    
    @retrofit2.http.GET(value = "/room/myJoinedRooms")
    @org.jetbrains.annotations.Nullable()
    public abstract java.lang.Object myJoinedRooms(@org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super com.lijianqiang12.silent.data.model.net.pojos.ApiResponse<? extends java.util.List<com.lijianqiang12.silent.data.model.net.pojos.MyJoinedRoom>>> $completion);
    
    @retrofit2.http.GET(value = "/room/allRooms")
    @org.jetbrains.annotations.Nullable()
    public abstract java.lang.Object allRooms(@retrofit2.http.Query(value = "type")
    int type, @retrofit2.http.Query(value = "lastRoomId")
    long lastRoomId, @retrofit2.http.Query(value = "lastLevelCount")
    long lastLevelCount, @org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super com.lijianqiang12.silent.data.model.net.pojos.ApiResponse<? extends java.util.List<com.lijianqiang12.silent.data.model.net.pojos.AllRoom>>> $completion);
    
    @retrofit2.http.FormUrlEncoded()
    @retrofit2.http.POST(value = "/room/requestRoom")
    @org.jetbrains.annotations.Nullable()
    public abstract java.lang.Object requestRoom(@retrofit2.http.Field(value = "roomId")
    int roomId, @retrofit2.http.Field(value = "roomType")
    int roomType, @retrofit2.http.Field(value = "bgUrl")
    @org.jetbrains.annotations.NotNull()
    java.lang.String bgUrl, @retrofit2.http.Field(value = "roomName")
    @org.jetbrains.annotations.NotNull()
    java.lang.String roomName, @retrofit2.http.Field(value = "roomDesc")
    @org.jetbrains.annotations.NotNull()
    java.lang.String roomDesc, @retrofit2.http.Field(value = "roomPwd")
    @org.jetbrains.annotations.NotNull()
    java.lang.String roomPwd, @retrofit2.http.Field(value = "changeType")
    int changeType, @org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super com.lijianqiang12.silent.data.model.net.pojos.ApiResponse<? extends java.lang.Object>> $completion);
    
    @retrofit2.http.GET(value = "/room/getRoomDesc")
    @org.jetbrains.annotations.Nullable()
    public abstract java.lang.Object getRoomDesc(@retrofit2.http.Query(value = "roomId")
    int roomId, @org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super com.lijianqiang12.silent.data.model.net.pojos.ApiResponse<com.lijianqiang12.silent.data.model.net.pojos.RoomDetailData>> $completion);
    
    @retrofit2.http.FormUrlEncoded()
    @retrofit2.http.POST(value = "/vip/makeWXOrder/v1")
    @org.jetbrains.annotations.Nullable()
    public abstract java.lang.Object makeWXOrder(@retrofit2.http.Field(value = "currentPrice")
    int currentPrice, @retrofit2.http.Field(value = "originalPrice")
    int originalPrice, @retrofit2.http.Field(value = "name")
    @org.jetbrains.annotations.NotNull()
    java.lang.String name, @retrofit2.http.Field(value = "phone")
    @org.jetbrains.annotations.NotNull()
    java.lang.String phone, @retrofit2.http.Field(value = "address")
    @org.jetbrains.annotations.NotNull()
    java.lang.String address, @org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super com.lijianqiang12.silent.data.model.net.pojos.ApiResponse<com.lijianqiang12.silent.data.model.net.pojos.WxOrder>> $completion);
    
    @retrofit2.http.FormUrlEncoded()
    @retrofit2.http.POST(value = "/vip/makeWXOrder/v1")
    @org.jetbrains.annotations.Nullable()
    public abstract java.lang.Object makeWXOrderForceUnlock(@retrofit2.http.Field(value = "typeOrder")
    int typeOrder, @retrofit2.http.Field(value = "forceAmount")
    int forceAmount, @retrofit2.http.Field(value = "forceUnlockInfo")
    @org.jetbrains.annotations.NotNull()
    java.lang.String forceUnlockInfo, @org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super com.lijianqiang12.silent.data.model.net.pojos.ApiResponse<com.lijianqiang12.silent.data.model.net.pojos.WxOrder>> $completion);
    
    @retrofit2.http.FormUrlEncoded()
    @retrofit2.http.POST(value = "/vip/makeWXOrder/v1")
    @org.jetbrains.annotations.Nullable()
    public abstract java.lang.Object makeWXOrderAppLimit(@retrofit2.http.Field(value = "typeOrder")
    int typeOrder, @retrofit2.http.Field(value = "forceAmount")
    long forceAmount, @retrofit2.http.Field(value = "appLimitUuid")
    long appLimitUuid, @org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super com.lijianqiang12.silent.data.model.net.pojos.ApiResponse<com.lijianqiang12.silent.data.model.net.pojos.WxOrder>> $completion);
    
    @retrofit2.http.FormUrlEncoded()
    @retrofit2.http.POST(value = "/vip/makeAlipayOrder/v1")
    @org.jetbrains.annotations.Nullable()
    public abstract java.lang.Object makeAlipayOrder(@retrofit2.http.Field(value = "currentPrice")
    int currentPrice, @retrofit2.http.Field(value = "originalPrice")
    int originalPrice, @retrofit2.http.Field(value = "name")
    @org.jetbrains.annotations.NotNull()
    java.lang.String name, @retrofit2.http.Field(value = "phone")
    @org.jetbrains.annotations.NotNull()
    java.lang.String phone, @retrofit2.http.Field(value = "address")
    @org.jetbrains.annotations.NotNull()
    java.lang.String address, @org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super com.lijianqiang12.silent.data.model.net.pojos.ApiResponse<com.lijianqiang12.silent.data.model.net.pojos.AlipayOrder>> $completion);
    
    @retrofit2.http.FormUrlEncoded()
    @retrofit2.http.POST(value = "/vip/makeAlipayOrder/v1")
    @org.jetbrains.annotations.Nullable()
    public abstract java.lang.Object makeAlipayOrderForceUnlock(@retrofit2.http.Field(value = "typeOrder")
    int typeOrder, @retrofit2.http.Field(value = "forceAmount")
    int forceAmount, @retrofit2.http.Field(value = "forceUnlockInfo")
    @org.jetbrains.annotations.NotNull()
    java.lang.String forceUnlockInfo, @org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super com.lijianqiang12.silent.data.model.net.pojos.ApiResponse<com.lijianqiang12.silent.data.model.net.pojos.AlipayOrder>> $completion);
    
    @retrofit2.http.FormUrlEncoded()
    @retrofit2.http.POST(value = "/vip/makeAlipayOrder/v1")
    @org.jetbrains.annotations.Nullable()
    public abstract java.lang.Object makeAlipayOrderAppLimit(@retrofit2.http.Field(value = "typeOrder")
    int typeOrder, @retrofit2.http.Field(value = "forceAmount")
    long forceAmount, @retrofit2.http.Field(value = "appLimitUuid")
    long appLimitUuid, @org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super com.lijianqiang12.silent.data.model.net.pojos.ApiResponse<com.lijianqiang12.silent.data.model.net.pojos.AlipayOrder>> $completion);
    
    @retrofit2.http.GET(value = "/vip/queryOrder/v1")
    @org.jetbrains.annotations.Nullable()
    public abstract java.lang.Object queryOrder(@org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super com.lijianqiang12.silent.data.model.net.pojos.ApiResponse<? extends java.lang.Object>> $completion);
    
    @retrofit2.http.GET(value = "/lock/getWellKnowWord/v1")
    @org.jetbrains.annotations.Nullable()
    public abstract java.lang.Object getWellKnowWord(@org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super com.lijianqiang12.silent.data.model.net.pojos.ApiResponse<com.lijianqiang12.silent.data.model.net.pojos.WellKnowWord>> $completion);
    
    @retrofit2.http.FormUrlEncoded()
    @retrofit2.http.POST(value = "/lock/refreshForceUnlockPwd")
    @org.jetbrains.annotations.Nullable()
    public abstract java.lang.Object refreshForceUnlockPwd(@retrofit2.http.Field(value = "pwd")
    @org.jetbrains.annotations.NotNull()
    java.lang.String pwd, @org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super com.lijianqiang12.silent.data.model.net.pojos.ApiResponse<com.lijianqiang12.silent.data.model.net.pojos.ForceUnlockPwd>> $completion);
    
    @retrofit2.http.GET(value = "/vip/getMoney/v3")
    @org.jetbrains.annotations.Nullable()
    public abstract java.lang.Object getMoney(@org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super com.lijianqiang12.silent.data.model.net.pojos.ApiResponse<com.lijianqiang12.silent.data.model.net.pojos.VIPMoney>> $completion);
    
    @retrofit2.http.GET(value = "/vip/getBuyHistory/v1")
    @org.jetbrains.annotations.Nullable()
    public abstract java.lang.Object getBuyHistory(@org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super com.lijianqiang12.silent.data.model.net.pojos.ApiResponse<? extends java.util.List<com.lijianqiang12.silent.data.model.net.pojos.BuyHistory>>> $completion);
    
    @retrofit2.http.FormUrlEncoded()
    @retrofit2.http.POST(value = "/vip/applyDelivery/v2")
    @org.jetbrains.annotations.Nullable()
    public abstract java.lang.Object applyDelivery(@retrofit2.http.Field(value = "name")
    @org.jetbrains.annotations.NotNull()
    java.lang.String name, @retrofit2.http.Field(value = "phone")
    @org.jetbrains.annotations.NotNull()
    java.lang.String phone, @retrofit2.http.Field(value = "address")
    @org.jetbrains.annotations.NotNull()
    java.lang.String address, @org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super com.lijianqiang12.silent.data.model.net.pojos.ApiResponse<? extends java.lang.Object>> $completion);
    
    @retrofit2.http.GET(value = "/account/queryUserInfo/v1")
    @org.jetbrains.annotations.Nullable()
    public abstract java.lang.Object queryUserInfo(@org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super com.lijianqiang12.silent.data.model.net.pojos.ApiResponse<com.lijianqiang12.silent.data.model.net.pojos.UserInfo>> $completion);
    
    @retrofit2.http.FormUrlEncoded()
    @retrofit2.http.POST(value = "/account/updateAvatar/v1")
    @org.jetbrains.annotations.Nullable()
    public abstract java.lang.Object updateAvatar(@retrofit2.http.Field(value = "avatar")
    @org.jetbrains.annotations.NotNull()
    java.lang.String avatar, @org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super com.lijianqiang12.silent.data.model.net.pojos.ApiResponse<? extends java.lang.Object>> $completion);
    
    @retrofit2.http.FormUrlEncoded()
    @retrofit2.http.POST(value = "/account/updateUsername/v1")
    @org.jetbrains.annotations.Nullable()
    public abstract java.lang.Object updateUsername(@retrofit2.http.Field(value = "username")
    @org.jetbrains.annotations.NotNull()
    java.lang.String username, @org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super com.lijianqiang12.silent.data.model.net.pojos.ApiResponse<? extends java.lang.Object>> $completion);
    
    @retrofit2.http.FormUrlEncoded()
    @retrofit2.http.POST(value = "/account/updateGender/v1")
    @org.jetbrains.annotations.Nullable()
    public abstract java.lang.Object updateGender(@retrofit2.http.Field(value = "gender")
    @org.jetbrains.annotations.NotNull()
    java.lang.String gender, @org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super com.lijianqiang12.silent.data.model.net.pojos.ApiResponse<? extends java.lang.Object>> $completion);
    
    @retrofit2.http.FormUrlEncoded()
    @retrofit2.http.POST(value = "/account/updateWord/v1")
    @org.jetbrains.annotations.Nullable()
    public abstract java.lang.Object updateWord(@retrofit2.http.Field(value = "word")
    @org.jetbrains.annotations.NotNull()
    java.lang.String word, @org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super com.lijianqiang12.silent.data.model.net.pojos.ApiResponse<? extends java.lang.Object>> $completion);
    
    @retrofit2.http.FormUrlEncoded()
    @retrofit2.http.POST(value = "/account/updateMobile/v1")
    @org.jetbrains.annotations.Nullable()
    public abstract java.lang.Object updateMobile(@retrofit2.http.Field(value = "mobile")
    @org.jetbrains.annotations.NotNull()
    java.lang.String mobile, @retrofit2.http.Field(value = "code")
    @org.jetbrains.annotations.NotNull()
    java.lang.String code, @org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super com.lijianqiang12.silent.data.model.net.pojos.ApiResponse<? extends java.lang.Object>> $completion);
    
    @retrofit2.http.FormUrlEncoded()
    @retrofit2.http.POST(value = "/account/bindQQ/v1")
    @org.jetbrains.annotations.Nullable()
    public abstract java.lang.Object bindQQ(@retrofit2.http.Field(value = "qqId")
    @org.jetbrains.annotations.NotNull()
    java.lang.String qqId, @org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super com.lijianqiang12.silent.data.model.net.pojos.ApiResponse<? extends java.lang.Object>> $completion);
    
    @retrofit2.http.FormUrlEncoded()
    @retrofit2.http.POST(value = "/account/bindWX/v1")
    @org.jetbrains.annotations.Nullable()
    public abstract java.lang.Object bindWX(@retrofit2.http.Field(value = "wxId")
    @org.jetbrains.annotations.NotNull()
    java.lang.String wxId, @org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super com.lijianqiang12.silent.data.model.net.pojos.ApiResponse<? extends java.lang.Object>> $completion);
    
    @retrofit2.http.FormUrlEncoded()
    @retrofit2.http.POST(value = "/account/bindSINA/v1")
    @org.jetbrains.annotations.Nullable()
    public abstract java.lang.Object bindSINA(@retrofit2.http.Field(value = "sinaId")
    @org.jetbrains.annotations.NotNull()
    java.lang.String sinaId, @org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super com.lijianqiang12.silent.data.model.net.pojos.ApiResponse<? extends java.lang.Object>> $completion);
    
    @retrofit2.http.POST(value = "/account/deleteQQ/v1")
    @org.jetbrains.annotations.Nullable()
    public abstract java.lang.Object deleteQQ(@org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super com.lijianqiang12.silent.data.model.net.pojos.ApiResponse<? extends java.lang.Object>> $completion);
    
    @retrofit2.http.POST(value = "/account/deleteWX/v1")
    @org.jetbrains.annotations.Nullable()
    public abstract java.lang.Object deleteWX(@org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super com.lijianqiang12.silent.data.model.net.pojos.ApiResponse<? extends java.lang.Object>> $completion);
    
    @retrofit2.http.POST(value = "/account/deleteSINA/v1")
    @org.jetbrains.annotations.Nullable()
    public abstract java.lang.Object deleteSINA(@org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super com.lijianqiang12.silent.data.model.net.pojos.ApiResponse<? extends java.lang.Object>> $completion);
    
    @retrofit2.http.POST(value = "/account/deleteAllAccount/v1")
    @org.jetbrains.annotations.Nullable()
    public abstract java.lang.Object deleteAllAccount(@org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super com.lijianqiang12.silent.data.model.net.pojos.ApiResponse<? extends java.lang.Object>> $completion);
    
    @retrofit2.http.FormUrlEncoded()
    @retrofit2.http.POST(value = "/lock/wellKnowWordStar")
    @org.jetbrains.annotations.Nullable()
    public abstract java.lang.Object wellKnowWordStar(@retrofit2.http.Field(value = "wordId")
    int wordId, @org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super com.lijianqiang12.silent.data.model.net.pojos.ApiResponse<? extends java.lang.Object>> $completion);
    
    @retrofit2.http.FormUrlEncoded()
    @retrofit2.http.POST(value = "/lock/wellKnowWordShare")
    @org.jetbrains.annotations.Nullable()
    public abstract java.lang.Object wellKnowWordShare(@retrofit2.http.Field(value = "wordId")
    int wordId, @org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super com.lijianqiang12.silent.data.model.net.pojos.ApiResponse<? extends java.lang.Object>> $completion);
    
    @retrofit2.http.GET(value = "/console/appUpdate/v1")
    @org.jetbrains.annotations.Nullable()
    public abstract java.lang.Object appUpdate(@org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super com.lijianqiang12.silent.data.model.net.pojos.ApiResponse<com.lijianqiang12.silent.data.model.net.pojos.AppUpdate>> $completion);
    
    @retrofit2.http.FormUrlEncoded()
    @retrofit2.http.POST(value = "/sync/addWhiteApp")
    @org.jetbrains.annotations.Nullable()
    public abstract java.lang.Object addWhiteApp(@retrofit2.http.Field(value = "whiteAppIndexId")
    @org.jetbrains.annotations.NotNull()
    java.lang.String whiteAppIndexId, @retrofit2.http.Field(value = "tomatoIndexId")
    @org.jetbrains.annotations.NotNull()
    java.lang.String tomatoIndexId, @retrofit2.http.Field(value = "scheduleIndexId")
    @org.jetbrains.annotations.NotNull()
    java.lang.String scheduleIndexId, @retrofit2.http.Field(value = "pkg")
    @org.jetbrains.annotations.NotNull()
    java.lang.String pkg, @retrofit2.http.Field(value = "mainActivity")
    @org.jetbrains.annotations.NotNull()
    java.lang.String mainActivity, @retrofit2.http.Field(value = "maxLen")
    int maxLen, @retrofit2.http.Field(value = "version")
    int version, @org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super com.lijianqiang12.silent.data.model.net.pojos.ApiResponse<com.lijianqiang12.silent.data.model.net.pojos.AddSyncResult>> $completion);
    
    @retrofit2.http.FormUrlEncoded()
    @retrofit2.http.POST(value = "/sync/updateWhiteApp")
    @org.jetbrains.annotations.Nullable()
    public abstract java.lang.Object updateWhiteApp(@retrofit2.http.Field(value = "tomatoIndexId")
    @org.jetbrains.annotations.NotNull()
    java.lang.String tomatoIndexId, @retrofit2.http.Field(value = "scheduleIndexId")
    @org.jetbrains.annotations.NotNull()
    java.lang.String scheduleIndexId, @retrofit2.http.Field(value = "pkg")
    @org.jetbrains.annotations.NotNull()
    java.lang.String pkg, @retrofit2.http.Field(value = "mainActivity")
    @org.jetbrains.annotations.NotNull()
    java.lang.String mainActivity, @retrofit2.http.Field(value = "maxLen")
    int maxLen, @retrofit2.http.Field(value = "trend")
    int trend, @retrofit2.http.Field(value = "syncTime")
    long syncTime, @retrofit2.http.Field(value = "uuid")
    long uuid, @retrofit2.http.Field(value = "version")
    int version, @org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super com.lijianqiang12.silent.data.model.net.pojos.ApiResponse<com.lijianqiang12.silent.data.model.net.pojos.UpdateWhiteResult>> $completion);
    
    @retrofit2.http.FormUrlEncoded()
    @retrofit2.http.POST(value = "/sync/deleteWhiteApp")
    @org.jetbrains.annotations.Nullable()
    public abstract java.lang.Object deleteWhiteApp(@retrofit2.http.Field(value = "uuid")
    long uuid, @retrofit2.http.Field(value = "version")
    int version, @retrofit2.http.Field(value = "syncTime")
    long syncTime, @org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super com.lijianqiang12.silent.data.model.net.pojos.ApiResponse<? extends java.lang.Object>> $completion);
    
    @retrofit2.http.FormUrlEncoded()
    @retrofit2.http.POST(value = "/sync/pullWhiteApp")
    @org.jetbrains.annotations.Nullable()
    public abstract java.lang.Object pullWhiteApp(@retrofit2.http.Field(value = "latestTime")
    long latestTime, @org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super com.lijianqiang12.silent.data.model.net.pojos.ApiResponse<? extends java.util.List<com.lijianqiang12.silent.data.model.net.pojos.PullWhiteResult>>> $completion);
    
    @retrofit2.http.FormUrlEncoded()
    @retrofit2.http.POST(value = "/sync/addFast")
    @org.jetbrains.annotations.Nullable()
    public abstract java.lang.Object addFast(@retrofit2.http.Field(value = "fastIndexId")
    @org.jetbrains.annotations.NotNull()
    java.lang.String fastIndexId, @retrofit2.http.Field(value = "length")
    int length, @retrofit2.http.Field(value = "version")
    int version, @org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super com.lijianqiang12.silent.data.model.net.pojos.ApiResponse<com.lijianqiang12.silent.data.model.net.pojos.AddSyncResult>> $completion);
    
    @retrofit2.http.FormUrlEncoded()
    @retrofit2.http.POST(value = "/sync/updateFast")
    @org.jetbrains.annotations.Nullable()
    public abstract java.lang.Object updateFast(@retrofit2.http.Field(value = "length")
    int length, @retrofit2.http.Field(value = "trend")
    int trend, @retrofit2.http.Field(value = "syncTime")
    long syncTime, @retrofit2.http.Field(value = "uuid")
    long uuid, @retrofit2.http.Field(value = "version")
    int version, @org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super com.lijianqiang12.silent.data.model.net.pojos.ApiResponse<com.lijianqiang12.silent.data.model.net.pojos.UpdateFastResult>> $completion);
    
    @retrofit2.http.FormUrlEncoded()
    @retrofit2.http.POST(value = "/sync/deleteFast")
    @org.jetbrains.annotations.Nullable()
    public abstract java.lang.Object deleteFast(@retrofit2.http.Field(value = "uuid")
    long uuid, @retrofit2.http.Field(value = "version")
    int version, @retrofit2.http.Field(value = "syncTime")
    long syncTime, @org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super com.lijianqiang12.silent.data.model.net.pojos.ApiResponse<? extends java.lang.Object>> $completion);
    
    @retrofit2.http.FormUrlEncoded()
    @retrofit2.http.POST(value = "/sync/pullFast")
    @org.jetbrains.annotations.Nullable()
    public abstract java.lang.Object pullFast(@retrofit2.http.Field(value = "latestTime")
    long latestTime, @org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super com.lijianqiang12.silent.data.model.net.pojos.ApiResponse<? extends java.util.List<com.lijianqiang12.silent.data.model.net.pojos.PullFastResult>>> $completion);
    
    @retrofit2.http.FormUrlEncoded()
    @retrofit2.http.POST(value = "/sync/addTomato")
    @org.jetbrains.annotations.Nullable()
    public abstract java.lang.Object addTomato(@retrofit2.http.Field(value = "tomatoIndexId")
    @org.jetbrains.annotations.NotNull()
    java.lang.String tomatoIndexId, @retrofit2.http.Field(value = "title")
    @org.jetbrains.annotations.NotNull()
    java.lang.String title, @retrofit2.http.Field(value = "tomatoWorkLength")
    int tomatoWorkLength, @retrofit2.http.Field(value = "tomatoRestLength")
    int tomatoRestLength, @retrofit2.http.Field(value = "tomatoCount")
    int tomatoCount, @retrofit2.http.Field(value = "tomatoLongRestPerCount")
    int tomatoLongRestPerCount, @retrofit2.http.Field(value = "tomatoLongRestLength")
    int tomatoLongRestLength, @retrofit2.http.Field(value = "bgUrl")
    @org.jetbrains.annotations.NotNull()
    java.lang.String bgUrl, @retrofit2.http.Field(value = "isRemoveNotification")
    boolean isRemoveNotification, @retrofit2.http.Field(value = "isSilent")
    boolean isSilent, @retrofit2.http.Field(value = "startVoiceNotify")
    int startVoiceNotify, @retrofit2.http.Field(value = "endVoiceNotify")
    int endVoiceNotify, @retrofit2.http.Field(value = "startShakeNotify")
    long startShakeNotify, @retrofit2.http.Field(value = "endShakeNotify")
    long endShakeNotify, @retrofit2.http.Field(value = "whiteFollowGlobal")
    boolean whiteFollowGlobal, @retrofit2.http.Field(value = "bgUrlFollowGlobal")
    boolean bgUrlFollowGlobal, @retrofit2.http.Field(value = "isRemoveNotificationFollowGlobal")
    boolean isRemoveNotificationFollowGlobal, @retrofit2.http.Field(value = "isSilentFollowGlobal")
    boolean isSilentFollowGlobal, @retrofit2.http.Field(value = "startVoiceNotifyFollowGlobal")
    boolean startVoiceNotifyFollowGlobal, @retrofit2.http.Field(value = "endVoiceNotifyFollowGlobal")
    boolean endVoiceNotifyFollowGlobal, @retrofit2.http.Field(value = "startShakeNotifyFollowGlobal")
    boolean startShakeNotifyFollowGlobal, @retrofit2.http.Field(value = "endShakeNotifyFollowGlobal")
    boolean endShakeNotifyFollowGlobal, @retrofit2.http.Field(value = "version")
    int version, @org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super com.lijianqiang12.silent.data.model.net.pojos.ApiResponse<com.lijianqiang12.silent.data.model.net.pojos.AddSyncResult>> $completion);
    
    @retrofit2.http.FormUrlEncoded()
    @retrofit2.http.POST(value = "/sync/updateTomato")
    @org.jetbrains.annotations.Nullable()
    public abstract java.lang.Object updateTomato(@retrofit2.http.Field(value = "tomatoIndexId")
    @org.jetbrains.annotations.NotNull()
    java.lang.String tomatoIndexId, @retrofit2.http.Field(value = "title")
    @org.jetbrains.annotations.NotNull()
    java.lang.String title, @retrofit2.http.Field(value = "tomatoWorkLength")
    int tomatoWorkLength, @retrofit2.http.Field(value = "tomatoRestLength")
    int tomatoRestLength, @retrofit2.http.Field(value = "tomatoCount")
    int tomatoCount, @retrofit2.http.Field(value = "tomatoLongRestPerCount")
    int tomatoLongRestPerCount, @retrofit2.http.Field(value = "tomatoLongRestLength")
    int tomatoLongRestLength, @retrofit2.http.Field(value = "bgUrl")
    @org.jetbrains.annotations.NotNull()
    java.lang.String bgUrl, @retrofit2.http.Field(value = "isRemoveNotification")
    boolean isRemoveNotification, @retrofit2.http.Field(value = "isSilent")
    boolean isSilent, @retrofit2.http.Field(value = "startVoiceNotify")
    int startVoiceNotify, @retrofit2.http.Field(value = "endVoiceNotify")
    int endVoiceNotify, @retrofit2.http.Field(value = "startShakeNotify")
    long startShakeNotify, @retrofit2.http.Field(value = "endShakeNotify")
    long endShakeNotify, @retrofit2.http.Field(value = "whiteFollowGlobal")
    boolean whiteFollowGlobal, @retrofit2.http.Field(value = "bgUrlFollowGlobal")
    boolean bgUrlFollowGlobal, @retrofit2.http.Field(value = "isRemoveNotificationFollowGlobal")
    boolean isRemoveNotificationFollowGlobal, @retrofit2.http.Field(value = "isSilentFollowGlobal")
    boolean isSilentFollowGlobal, @retrofit2.http.Field(value = "startVoiceNotifyFollowGlobal")
    boolean startVoiceNotifyFollowGlobal, @retrofit2.http.Field(value = "endVoiceNotifyFollowGlobal")
    boolean endVoiceNotifyFollowGlobal, @retrofit2.http.Field(value = "startShakeNotifyFollowGlobal")
    boolean startShakeNotifyFollowGlobal, @retrofit2.http.Field(value = "endShakeNotifyFollowGlobal")
    boolean endShakeNotifyFollowGlobal, @retrofit2.http.Field(value = "trend")
    int trend, @retrofit2.http.Field(value = "syncTime")
    long syncTime, @retrofit2.http.Field(value = "uuid")
    long uuid, @retrofit2.http.Field(value = "version")
    int version, @org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super com.lijianqiang12.silent.data.model.net.pojos.ApiResponse<com.lijianqiang12.silent.data.model.net.pojos.UpdateTomatoResult>> $completion);
    
    @retrofit2.http.FormUrlEncoded()
    @retrofit2.http.POST(value = "/sync/deleteTomato")
    @org.jetbrains.annotations.Nullable()
    public abstract java.lang.Object deleteTomato(@retrofit2.http.Field(value = "uuid")
    long uuid, @retrofit2.http.Field(value = "version")
    int version, @retrofit2.http.Field(value = "syncTime")
    long syncTime, @org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super com.lijianqiang12.silent.data.model.net.pojos.ApiResponse<? extends java.lang.Object>> $completion);
    
    @retrofit2.http.FormUrlEncoded()
    @retrofit2.http.POST(value = "/sync/pullTomato")
    @org.jetbrains.annotations.Nullable()
    public abstract java.lang.Object pullTomato(@retrofit2.http.Field(value = "latestTime")
    long latestTime, @org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super com.lijianqiang12.silent.data.model.net.pojos.ApiResponse<? extends java.util.List<com.lijianqiang12.silent.data.model.net.pojos.PullTomatoResult>>> $completion);
    
    @retrofit2.http.FormUrlEncoded()
    @retrofit2.http.POST(value = "/sync/addSchedule")
    @org.jetbrains.annotations.Nullable()
    public abstract java.lang.Object addSchedule(@retrofit2.http.Field(value = "title")
    @org.jetbrains.annotations.NotNull()
    java.lang.String title, @retrofit2.http.Field(value = "tomatoIndexId")
    @org.jetbrains.annotations.NotNull()
    java.lang.String tomatoIndexId, @retrofit2.http.Field(value = "scheduleIndexId")
    @org.jetbrains.annotations.NotNull()
    java.lang.String scheduleIndexId, @retrofit2.http.Field(value = "startHour")
    int startHour, @retrofit2.http.Field(value = "startMinute")
    int startMinute, @retrofit2.http.Field(value = "validate")
    boolean validate, @retrofit2.http.Field(value = "sunday")
    boolean sunday, @retrofit2.http.Field(value = "monday")
    boolean monday, @retrofit2.http.Field(value = "tuesday")
    boolean tuesday, @retrofit2.http.Field(value = "wednesday")
    boolean wednesday, @retrofit2.http.Field(value = "thursday")
    boolean thursday, @retrofit2.http.Field(value = "friday")
    boolean friday, @retrofit2.http.Field(value = "saturday")
    boolean saturday, @retrofit2.http.Field(value = "useTomato")
    boolean useTomato, @retrofit2.http.Field(value = "endHour")
    int endHour, @retrofit2.http.Field(value = "endMinute")
    int endMinute, @retrofit2.http.Field(value = "isRecycle")
    boolean isRecycle, @retrofit2.http.Field(value = "isDenyChange")
    boolean isDenyChange, @retrofit2.http.Field(value = "denyChangeLength")
    int denyChangeLength, @retrofit2.http.Field(value = "bgUrl")
    @org.jetbrains.annotations.NotNull()
    java.lang.String bgUrl, @retrofit2.http.Field(value = "isRemoveNotification")
    boolean isRemoveNotification, @retrofit2.http.Field(value = "isSilent")
    boolean isSilent, @retrofit2.http.Field(value = "startVoiceNotify")
    int startVoiceNotify, @retrofit2.http.Field(value = "endVoiceNotify")
    int endVoiceNotify, @retrofit2.http.Field(value = "startShakeNotify")
    long startShakeNotify, @retrofit2.http.Field(value = "endShakeNotify")
    long endShakeNotify, @retrofit2.http.Field(value = "whiteFollowGlobal")
    boolean whiteFollowGlobal, @retrofit2.http.Field(value = "bgUrlFollowGlobal")
    boolean bgUrlFollowGlobal, @retrofit2.http.Field(value = "isRemoveNotificationFollowGlobal")
    boolean isRemoveNotificationFollowGlobal, @retrofit2.http.Field(value = "isSilentFollowGlobal")
    boolean isSilentFollowGlobal, @retrofit2.http.Field(value = "startVoiceNotifyFollowGlobal")
    boolean startVoiceNotifyFollowGlobal, @retrofit2.http.Field(value = "endVoiceNotifyFollowGlobal")
    boolean endVoiceNotifyFollowGlobal, @retrofit2.http.Field(value = "startShakeNotifyFollowGlobal")
    boolean startShakeNotifyFollowGlobal, @retrofit2.http.Field(value = "endShakeNotifyFollowGlobal")
    boolean endShakeNotifyFollowGlobal, @retrofit2.http.Field(value = "jumpDate")
    @org.jetbrains.annotations.NotNull()
    java.lang.String jumpDate, @retrofit2.http.Field(value = "version")
    int version, @org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super com.lijianqiang12.silent.data.model.net.pojos.ApiResponse<com.lijianqiang12.silent.data.model.net.pojos.AddSyncResult>> $completion);
    
    @retrofit2.http.FormUrlEncoded()
    @retrofit2.http.POST(value = "/sync/updateSchedule")
    @org.jetbrains.annotations.Nullable()
    public abstract java.lang.Object updateSchedule(@retrofit2.http.Field(value = "title")
    @org.jetbrains.annotations.NotNull()
    java.lang.String title, @retrofit2.http.Field(value = "tomatoIndexId")
    @org.jetbrains.annotations.NotNull()
    java.lang.String tomatoIndexId, @retrofit2.http.Field(value = "scheduleIndexId")
    @org.jetbrains.annotations.NotNull()
    java.lang.String scheduleIndexId, @retrofit2.http.Field(value = "startHour")
    int startHour, @retrofit2.http.Field(value = "startMinute")
    int startMinute, @retrofit2.http.Field(value = "validate")
    boolean validate, @retrofit2.http.Field(value = "sunday")
    boolean sunday, @retrofit2.http.Field(value = "monday")
    boolean monday, @retrofit2.http.Field(value = "tuesday")
    boolean tuesday, @retrofit2.http.Field(value = "wednesday")
    boolean wednesday, @retrofit2.http.Field(value = "thursday")
    boolean thursday, @retrofit2.http.Field(value = "friday")
    boolean friday, @retrofit2.http.Field(value = "saturday")
    boolean saturday, @retrofit2.http.Field(value = "useTomato")
    boolean useTomato, @retrofit2.http.Field(value = "endHour")
    int endHour, @retrofit2.http.Field(value = "endMinute")
    int endMinute, @retrofit2.http.Field(value = "isRecycle")
    boolean isRecycle, @retrofit2.http.Field(value = "isDenyChange")
    boolean isDenyChange, @retrofit2.http.Field(value = "denyChangeLength")
    int denyChangeLength, @retrofit2.http.Field(value = "bgUrl")
    @org.jetbrains.annotations.NotNull()
    java.lang.String bgUrl, @retrofit2.http.Field(value = "isRemoveNotification")
    boolean isRemoveNotification, @retrofit2.http.Field(value = "isSilent")
    boolean isSilent, @retrofit2.http.Field(value = "startVoiceNotify")
    int startVoiceNotify, @retrofit2.http.Field(value = "endVoiceNotify")
    int endVoiceNotify, @retrofit2.http.Field(value = "startShakeNotify")
    long startShakeNotify, @retrofit2.http.Field(value = "endShakeNotify")
    long endShakeNotify, @retrofit2.http.Field(value = "whiteFollowGlobal")
    boolean whiteFollowGlobal, @retrofit2.http.Field(value = "bgUrlFollowGlobal")
    boolean bgUrlFollowGlobal, @retrofit2.http.Field(value = "isRemoveNotificationFollowGlobal")
    boolean isRemoveNotificationFollowGlobal, @retrofit2.http.Field(value = "isSilentFollowGlobal")
    boolean isSilentFollowGlobal, @retrofit2.http.Field(value = "startVoiceNotifyFollowGlobal")
    boolean startVoiceNotifyFollowGlobal, @retrofit2.http.Field(value = "endVoiceNotifyFollowGlobal")
    boolean endVoiceNotifyFollowGlobal, @retrofit2.http.Field(value = "startShakeNotifyFollowGlobal")
    boolean startShakeNotifyFollowGlobal, @retrofit2.http.Field(value = "endShakeNotifyFollowGlobal")
    boolean endShakeNotifyFollowGlobal, @retrofit2.http.Field(value = "jumpDate")
    @org.jetbrains.annotations.NotNull()
    java.lang.String jumpDate, @retrofit2.http.Field(value = "trend")
    int trend, @retrofit2.http.Field(value = "syncTime")
    long syncTime, @retrofit2.http.Field(value = "uuid")
    long uuid, @retrofit2.http.Field(value = "version")
    int version, @org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super com.lijianqiang12.silent.data.model.net.pojos.ApiResponse<com.lijianqiang12.silent.data.model.net.pojos.UpdateScheduleResult>> $completion);
    
    @retrofit2.http.FormUrlEncoded()
    @retrofit2.http.POST(value = "/sync/deleteSchedule")
    @org.jetbrains.annotations.Nullable()
    public abstract java.lang.Object deleteSchedule(@retrofit2.http.Field(value = "uuid")
    long uuid, @retrofit2.http.Field(value = "version")
    int version, @retrofit2.http.Field(value = "syncTime")
    long syncTime, @org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super com.lijianqiang12.silent.data.model.net.pojos.ApiResponse<? extends java.lang.Object>> $completion);
    
    @retrofit2.http.FormUrlEncoded()
    @retrofit2.http.POST(value = "/sync/pullSchedule")
    @org.jetbrains.annotations.Nullable()
    public abstract java.lang.Object pullSchedule(@retrofit2.http.Field(value = "latestTime")
    long latestTime, @retrofit2.http.Field(value = "isUpdateDb")
    boolean isUpdateDb, @org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super com.lijianqiang12.silent.data.model.net.pojos.ApiResponse<? extends java.util.List<com.lijianqiang12.silent.data.model.net.pojos.PullScheduleResult>>> $completion);
    
    @retrofit2.http.FormUrlEncoded()
    @retrofit2.http.POST(value = "/sync/addAppLimit")
    @org.jetbrains.annotations.Nullable()
    public abstract java.lang.Object addAppLimit(@retrofit2.http.Field(value = "appLimitIndexId")
    @org.jetbrains.annotations.NotNull()
    java.lang.String appLimitIndexId, @retrofit2.http.Field(value = "appPkg")
    @org.jetbrains.annotations.NotNull()
    java.lang.String appPkg, @retrofit2.http.Field(value = "ifAllDay")
    boolean ifAllDay, @retrofit2.http.Field(value = "startTime")
    long startTime, @retrofit2.http.Field(value = "endTime")
    long endTime, @retrofit2.http.Field(value = "limitLength")
    long limitLength, @retrofit2.http.Field(value = "title")
    @org.jetbrains.annotations.NotNull()
    java.lang.String title, @retrofit2.http.Field(value = "sunday")
    boolean sunday, @retrofit2.http.Field(value = "monday")
    boolean monday, @retrofit2.http.Field(value = "tuesday")
    boolean tuesday, @retrofit2.http.Field(value = "wednesday")
    boolean wednesday, @retrofit2.http.Field(value = "thursday")
    boolean thursday, @retrofit2.http.Field(value = "friday")
    boolean friday, @retrofit2.http.Field(value = "saturday")
    boolean saturday, @retrofit2.http.Field(value = "editStartTime")
    long editStartTime, @retrofit2.http.Field(value = "editEndTime")
    long editEndTime, @retrofit2.http.Field(value = "editMoney")
    long editMoney, @retrofit2.http.Field(value = "valid")
    boolean valid, @retrofit2.http.Field(value = "version")
    int version, @org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super com.lijianqiang12.silent.data.model.net.pojos.ApiResponse<com.lijianqiang12.silent.data.model.net.pojos.AddSyncResult>> $completion);
    
    @retrofit2.http.FormUrlEncoded()
    @retrofit2.http.POST(value = "/sync/updateAppLimit")
    @org.jetbrains.annotations.Nullable()
    public abstract java.lang.Object updateAppLimit(@retrofit2.http.Field(value = "appPkg")
    @org.jetbrains.annotations.NotNull()
    java.lang.String appPkg, @retrofit2.http.Field(value = "ifAllDay")
    boolean ifAllDay, @retrofit2.http.Field(value = "startTime")
    long startTime, @retrofit2.http.Field(value = "endTime")
    long endTime, @retrofit2.http.Field(value = "limitLength")
    long limitLength, @retrofit2.http.Field(value = "title")
    @org.jetbrains.annotations.NotNull()
    java.lang.String title, @retrofit2.http.Field(value = "sunday")
    boolean sunday, @retrofit2.http.Field(value = "monday")
    boolean monday, @retrofit2.http.Field(value = "tuesday")
    boolean tuesday, @retrofit2.http.Field(value = "wednesday")
    boolean wednesday, @retrofit2.http.Field(value = "thursday")
    boolean thursday, @retrofit2.http.Field(value = "friday")
    boolean friday, @retrofit2.http.Field(value = "saturday")
    boolean saturday, @retrofit2.http.Field(value = "editStartTime")
    long editStartTime, @retrofit2.http.Field(value = "editEndTime")
    long editEndTime, @retrofit2.http.Field(value = "editMoney")
    long editMoney, @retrofit2.http.Field(value = "valid")
    boolean valid, @retrofit2.http.Field(value = "trend")
    int trend, @retrofit2.http.Field(value = "syncTime")
    long syncTime, @retrofit2.http.Field(value = "uuid")
    long uuid, @retrofit2.http.Field(value = "version")
    int version, @org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super com.lijianqiang12.silent.data.model.net.pojos.ApiResponse<com.lijianqiang12.silent.data.model.net.pojos.UpdateAppLimitResult>> $completion);
    
    @retrofit2.http.FormUrlEncoded()
    @retrofit2.http.POST(value = "/sync/deleteAppLimit")
    @org.jetbrains.annotations.Nullable()
    public abstract java.lang.Object deleteAppLimit(@retrofit2.http.Field(value = "uuid")
    long uuid, @retrofit2.http.Field(value = "version")
    int version, @retrofit2.http.Field(value = "syncTime")
    long syncTime, @org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super com.lijianqiang12.silent.data.model.net.pojos.ApiResponse<? extends java.lang.Object>> $completion);
    
    @retrofit2.http.FormUrlEncoded()
    @retrofit2.http.POST(value = "/sync/pullAppLimit")
    @org.jetbrains.annotations.Nullable()
    public abstract java.lang.Object pullAppLimit(@retrofit2.http.Field(value = "latestTime")
    long latestTime, @org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super com.lijianqiang12.silent.data.model.net.pojos.ApiResponse<? extends java.util.List<com.lijianqiang12.silent.data.model.net.pojos.PullAppLimitResult>>> $completion);
    
    @retrofit2.http.FormUrlEncoded()
    @retrofit2.http.POST(value = "/console/uploadDenyPkgInfo/v1")
    @org.jetbrains.annotations.Nullable()
    public abstract java.lang.Object uploadDenyPkgInfo(@retrofit2.http.Field(value = "appName")
    @org.jetbrains.annotations.NotNull()
    java.lang.String appName, @retrofit2.http.Field(value = "appPkg")
    @org.jetbrains.annotations.NotNull()
    java.lang.String appPkg, @retrofit2.http.Field(value = "appMainActivity")
    @org.jetbrains.annotations.NotNull()
    java.lang.String appMainActivity, @org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super com.lijianqiang12.silent.data.model.net.pojos.ApiResponse<? extends java.lang.Object>> $completion);
    
    @retrofit2.http.FormUrlEncoded()
    @retrofit2.http.POST(value = "/offtime/syncHistory")
    @org.jetbrains.annotations.Nullable()
    public abstract java.lang.Object syncHistory(@retrofit2.http.Field(value = "startTime")
    long startTime, @retrofit2.http.Field(value = "trueStartTime")
    long trueStartTime, @retrofit2.http.Field(value = "timeLength")
    long timeLength, @retrofit2.http.Field(value = "trueTimeLength")
    long trueTimeLength, @retrofit2.http.Field(value = "lockType")
    int lockType, @retrofit2.http.Field(value = "tomatoIndexId")
    @org.jetbrains.annotations.NotNull()
    java.lang.String tomatoIndexId, @retrofit2.http.Field(value = "scheduleIndexId")
    @org.jetbrains.annotations.NotNull()
    java.lang.String scheduleIndexId, @org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super com.lijianqiang12.silent.data.model.net.pojos.ApiResponse<? extends java.lang.Object>> $completion);
    
    @retrofit2.http.GET(value = "/offtime/myTrendNo")
    @org.jetbrains.annotations.Nullable()
    public abstract java.lang.Object myTrendNo(@retrofit2.http.Query(value = "deltaDay")
    int deltaDay, @org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super com.lijianqiang12.silent.data.model.net.pojos.ApiResponse<com.lijianqiang12.silent.data.model.net.pojos.MyTrend>> $completion);
    
    @retrofit2.http.GET(value = "/offtime/lockLength/v2")
    @org.jetbrains.annotations.Nullable()
    public abstract java.lang.Object lockLength(@retrofit2.http.Query(value = "deltaWeek")
    int deltaWeek, @org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super com.lijianqiang12.silent.data.model.net.pojos.ApiResponse<? extends java.util.List<java.lang.Integer>>> $completion);
    
    @retrofit2.http.FormUrlEncoded()
    @retrofit2.http.POST(value = "/vip/duihuanma/v1")
    @org.jetbrains.annotations.Nullable()
    public abstract java.lang.Object duihuanma(@retrofit2.http.Field(value = "duihuanma")
    @org.jetbrains.annotations.NotNull()
    java.lang.String duihuanma, @org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super com.lijianqiang12.silent.data.model.net.pojos.ApiResponse<? extends java.lang.Object>> $completion);
    
    @retrofit2.http.GET(value = "/console/getConfig/v1")
    @org.jetbrains.annotations.Nullable()
    public abstract java.lang.Object getConfig(@org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super com.lijianqiang12.silent.data.model.net.pojos.ApiResponse<com.lijianqiang12.silent.data.model.net.pojos.MyConfig>> $completion);
    
    @retrofit2.http.POST(value = "/offtime/syncOnline/v2")
    @org.jetbrains.annotations.Nullable()
    public abstract java.lang.Object syncOnline(@org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super com.lijianqiang12.silent.data.model.net.pojos.ApiResponse<java.lang.Integer>> $completion);
    
    @retrofit2.http.POST(value = "/offtime/offline/v1")
    @org.jetbrains.annotations.Nullable()
    public abstract java.lang.Object offline(@org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super com.lijianqiang12.silent.data.model.net.pojos.ApiResponse<? extends java.lang.Object>> $completion);
    
    @retrofit2.http.GET(value = "/punchCard/getPunchCardMsg")
    @org.jetbrains.annotations.Nullable()
    public abstract java.lang.Object getPunchCardMsg(@org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super com.lijianqiang12.silent.data.model.net.pojos.ApiResponse<com.lijianqiang12.silent.data.model.net.pojos.PunchCardMsg>> $completion);
    
    @retrofit2.http.FormUrlEncoded()
    @retrofit2.http.POST(value = "/punchCard/punchCard")
    @org.jetbrains.annotations.Nullable()
    public abstract java.lang.Object punchCard(@retrofit2.http.Field(value = "lockNumber")
    int lockNumber, @retrofit2.http.Field(value = "goOnDays")
    int goOnDays, @retrofit2.http.Field(value = "totalLength")
    long totalLength, @retrofit2.http.Field(value = "length")
    long length, @retrofit2.http.Field(value = "imgUrl")
    @org.jetbrains.annotations.NotNull()
    java.lang.String imgUrl, @retrofit2.http.Field(value = "word")
    @org.jetbrains.annotations.NotNull()
    java.lang.String word, @org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super com.lijianqiang12.silent.data.model.net.pojos.ApiResponse<? extends java.lang.Object>> $completion);
    
    @retrofit2.http.GET(value = "/punchCard/getPunchCards")
    @org.jetbrains.annotations.Nullable()
    public abstract java.lang.Object getPunchCards(@retrofit2.http.Query(value = "lastId")
    long lastId, @retrofit2.http.Query(value = "isMyself")
    boolean isMyself, @org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super com.lijianqiang12.silent.data.model.net.pojos.ApiResponse<? extends java.util.List<com.lijianqiang12.silent.data.model.net.pojos.ThePunchCard>>> $completion);
    
    @retrofit2.http.FormUrlEncoded()
    @retrofit2.http.POST(value = "/punchCard/removePunchCard")
    @org.jetbrains.annotations.Nullable()
    public abstract java.lang.Object removePunchCard(@retrofit2.http.Field(value = "punchId")
    long punchId, @org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super com.lijianqiang12.silent.data.model.net.pojos.ApiResponse<? extends java.lang.Object>> $completion);
    
    @retrofit2.http.GET(value = "/room/getRoomMemberPunch")
    @org.jetbrains.annotations.Nullable()
    public abstract java.lang.Object getRoomMemberPunch(@retrofit2.http.Query(value = "lastId")
    long lastId, @retrofit2.http.Query(value = "roomId")
    long roomId, @org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super com.lijianqiang12.silent.data.model.net.pojos.ApiResponse<? extends java.util.List<com.lijianqiang12.silent.data.model.net.pojos.ThePunchCard>>> $completion);
    
    @retrofit2.http.GET(value = "/room/getRoomMemberInfo")
    @org.jetbrains.annotations.Nullable()
    public abstract java.lang.Object getRoomMemberInfo(@retrofit2.http.Query(value = "lastId")
    long lastId, @retrofit2.http.Query(value = "roomId")
    long roomId, @org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super com.lijianqiang12.silent.data.model.net.pojos.ApiResponse<? extends java.util.List<com.lijianqiang12.silent.data.model.net.pojos.RoomDetailMember>>> $completion);
    
    @retrofit2.http.GET(value = "/room/getRoomBoard")
    @org.jetbrains.annotations.Nullable()
    public abstract java.lang.Object getRoomBoard(@retrofit2.http.Query(value = "lastId")
    long lastId, @retrofit2.http.Query(value = "roomId")
    long roomId, @org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super com.lijianqiang12.silent.data.model.net.pojos.ApiResponse<? extends java.util.List<com.lijianqiang12.silent.data.model.net.pojos.RoomDetailBoard>>> $completion);
    
    @retrofit2.http.GET(value = "/room/getRoomFromCode")
    @org.jetbrains.annotations.Nullable()
    public abstract java.lang.Object getRoomFromCode(@retrofit2.http.Query(value = "roomCode")
    @org.jetbrains.annotations.NotNull()
    java.lang.String roomCode, @org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super com.lijianqiang12.silent.data.model.net.pojos.ApiResponse<com.lijianqiang12.silent.data.model.net.pojos.RoomInfoFromCode>> $completion);
    
    @retrofit2.http.FormUrlEncoded()
    @retrofit2.http.POST(value = "/room/joinRoom")
    @org.jetbrains.annotations.Nullable()
    public abstract java.lang.Object joinRoom(@retrofit2.http.Field(value = "roomId")
    int roomId, @retrofit2.http.Field(value = "pwd")
    @org.jetbrains.annotations.NotNull()
    java.lang.String pwd, @org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super com.lijianqiang12.silent.data.model.net.pojos.ApiResponse<? extends java.lang.Object>> $completion);
    
    @retrofit2.http.FormUrlEncoded()
    @retrofit2.http.POST(value = "/room/deleteRoom")
    @org.jetbrains.annotations.Nullable()
    public abstract java.lang.Object deleteRoom(@retrofit2.http.Field(value = "roomId")
    int roomId, @org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super com.lijianqiang12.silent.data.model.net.pojos.ApiResponse<? extends java.lang.Object>> $completion);
    
    @retrofit2.http.FormUrlEncoded()
    @retrofit2.http.POST(value = "/room/quitRoom")
    @org.jetbrains.annotations.Nullable()
    public abstract java.lang.Object quitRoom(@retrofit2.http.Field(value = "roomId")
    int roomId, @org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super com.lijianqiang12.silent.data.model.net.pojos.ApiResponse<? extends java.lang.Object>> $completion);
    
    @retrofit2.http.FormUrlEncoded()
    @retrofit2.http.POST(value = "/room/removeMember")
    @org.jetbrains.annotations.Nullable()
    public abstract java.lang.Object removeMember(@retrofit2.http.Field(value = "roomId")
    int roomId, @retrofit2.http.Field(value = "memberId")
    int memberId, @org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super com.lijianqiang12.silent.data.model.net.pojos.ApiResponse<? extends java.lang.Object>> $completion);
    
    @retrofit2.http.FormUrlEncoded()
    @retrofit2.http.POST(value = "/room/removeBoard")
    @org.jetbrains.annotations.Nullable()
    public abstract java.lang.Object removeBoard(@retrofit2.http.Field(value = "roomId")
    int roomId, @retrofit2.http.Field(value = "boardId")
    int boardId, @org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super com.lijianqiang12.silent.data.model.net.pojos.ApiResponse<? extends java.lang.Object>> $completion);
    
    @retrofit2.http.FormUrlEncoded()
    @retrofit2.http.POST(value = "/room/postBoard")
    @org.jetbrains.annotations.Nullable()
    public abstract java.lang.Object postBoard(@retrofit2.http.Field(value = "roomId")
    long roomId, @retrofit2.http.Field(value = "word")
    @org.jetbrains.annotations.NotNull()
    java.lang.String word, @org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super com.lijianqiang12.silent.data.model.net.pojos.ApiResponse<? extends java.lang.Object>> $completion);
    
    @retrofit2.http.FormUrlEncoded()
    @retrofit2.http.POST(value = "/room/verifyRoomPwd")
    @org.jetbrains.annotations.Nullable()
    public abstract java.lang.Object verifyRoomPwd(@retrofit2.http.Field(value = "roomId")
    long roomId, @retrofit2.http.Field(value = "pwd")
    @org.jetbrains.annotations.NotNull()
    java.lang.String pwd, @org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super com.lijianqiang12.silent.data.model.net.pojos.ApiResponse<? extends java.lang.Object>> $completion);
    
    @retrofit2.http.POST(value = "/punchCard/shareCard")
    @org.jetbrains.annotations.Nullable()
    public abstract java.lang.Object shareCard(@org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super com.lijianqiang12.silent.data.model.net.pojos.ApiResponse<? extends java.lang.Object>> $completion);
    
    @retrofit2.http.POST(value = "/console/readMsg")
    @org.jetbrains.annotations.Nullable()
    public abstract java.lang.Object readMsg(@org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super com.lijianqiang12.silent.data.model.net.pojos.ApiResponse<? extends java.lang.Object>> $completion);
    
    @retrofit2.http.GET(value = "/console/msgList")
    @org.jetbrains.annotations.Nullable()
    public abstract java.lang.Object msgList(@retrofit2.http.Query(value = "lastId")
    long lastId, @org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super com.lijianqiang12.silent.data.model.net.pojos.ApiResponse<? extends java.util.List<com.lijianqiang12.silent.data.model.net.pojos.MyMsg>>> $completion);
    
    @retrofit2.http.GET(value = "/console/unreadMsgCount")
    @org.jetbrains.annotations.Nullable()
    public abstract java.lang.Object unreadMsgCount(@org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super com.lijianqiang12.silent.data.model.net.pojos.ApiResponse<com.lijianqiang12.silent.data.model.net.pojos.UnReadMsg>> $completion);
    
    @retrofit2.http.GET(value = "/room/getRoomRequestList")
    @org.jetbrains.annotations.Nullable()
    public abstract java.lang.Object getRoomRequestList(@org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super com.lijianqiang12.silent.data.model.net.pojos.ApiResponse<? extends java.util.List<com.lijianqiang12.silent.data.model.net.pojos.RoomRequestBean>>> $completion);
    
    @retrofit2.http.FormUrlEncoded()
    @retrofit2.http.POST(value = "/room/requestRoomOK")
    @org.jetbrains.annotations.Nullable()
    public abstract java.lang.Object requestRoomOK(@retrofit2.http.Field(value = "requestRoomId")
    long requestRoomId, @retrofit2.http.Field(value = "result")
    int result, @retrofit2.http.Field(value = "reason")
    @org.jetbrains.annotations.NotNull()
    java.lang.String reason, @org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super com.lijianqiang12.silent.data.model.net.pojos.ApiResponse<? extends java.lang.Object>> $completion);
    
    @retrofit2.http.GET(value = "/console/getBaozang")
    @org.jetbrains.annotations.Nullable()
    public abstract java.lang.Object getBaozang(@org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super com.lijianqiang12.silent.data.model.net.pojos.ApiResponse<? extends java.util.List<com.lijianqiang12.silent.data.model.net.pojos.BaoZangApp>>> $completion);
    
    @retrofit2.http.GET(value = "/console/launchDialog/v2")
    @org.jetbrains.annotations.Nullable()
    public abstract java.lang.Object getLaunchDialog(@org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super com.lijianqiang12.silent.data.model.net.pojos.ApiResponse<com.lijianqiang12.silent.data.model.net.pojos.LaunchDialog>> $completion);
    
    @retrofit2.http.FormUrlEncoded()
    @retrofit2.http.POST(value = "/console/readLaunchDialogMsg/v2")
    @org.jetbrains.annotations.Nullable()
    public abstract java.lang.Object readLaunchDialogMsg(@retrofit2.http.Field(value = "launchDialogMsgId")
    long launchDialogMsgId, @org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super com.lijianqiang12.silent.data.model.net.pojos.ApiResponse<? extends java.lang.Object>> $completion);
    
    @retrofit2.http.GET(value = "/other/getFastDenyPageExample/v2")
    @org.jetbrains.annotations.Nullable()
    public abstract java.lang.Object getFastDenyPageExample(@org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super com.lijianqiang12.silent.data.model.net.pojos.ApiResponse<? extends java.util.List<com.lijianqiang12.silent.data.model.net.pojos.FastDenyPageExample>>> $completion);
    
    @retrofit2.http.GET(value = "/offtime/getUserCode/v1")
    @org.jetbrains.annotations.Nullable()
    public abstract java.lang.Object getUserCode(@org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super com.lijianqiang12.silent.data.model.net.pojos.ApiResponse<com.lijianqiang12.silent.data.model.net.pojos.UserCode>> $completion);
    
    @retrofit2.http.FormUrlEncoded()
    @retrofit2.http.POST(value = "/offtime/friendUnlock/v1")
    @org.jetbrains.annotations.Nullable()
    public abstract java.lang.Object friendUnlock(@retrofit2.http.Field(value = "userCode")
    @org.jetbrains.annotations.NotNull()
    java.lang.String userCode, @retrofit2.http.Field(value = "unlockCode")
    @org.jetbrains.annotations.NotNull()
    java.lang.String unlockCode, @org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super com.lijianqiang12.silent.data.model.net.pojos.ApiResponse<? extends java.lang.Object>> $completion);
    
    @retrofit2.http.FormUrlEncoded()
    @retrofit2.http.POST(value = "/offtime/getUnlockCode/v1")
    @org.jetbrains.annotations.Nullable()
    public abstract java.lang.Object getUnlockCode(@retrofit2.http.Field(value = "userCode")
    @org.jetbrains.annotations.NotNull()
    java.lang.String userCode, @org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super com.lijianqiang12.silent.data.model.net.pojos.ApiResponse<com.lijianqiang12.silent.data.model.net.pojos.SubUnlockCode>> $completion);
    
    @retrofit2.http.GET(value = "/console/getDeveloperUnlockInfo/v1")
    @org.jetbrains.annotations.Nullable()
    public abstract java.lang.Object getDeveloperUnlockInfo(@retrofit2.http.Query(value = "unlockUserId")
    long unlockUserId, @org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super com.lijianqiang12.silent.data.model.net.pojos.ApiResponse<com.lijianqiang12.silent.data.model.net.pojos.DeveloperUnlockUserInfo>> $completion);
    
    @retrofit2.http.FormUrlEncoded()
    @retrofit2.http.POST(value = "/console/developerUnlock/v1")
    @org.jetbrains.annotations.Nullable()
    public abstract java.lang.Object developerUnlock(@retrofit2.http.Field(value = "unlockUserId")
    long unlockUserId, @org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super com.lijianqiang12.silent.data.model.net.pojos.ApiResponse<? extends java.lang.Object>> $completion);
    
    @kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000\u001a\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0002\b\u0002\n\u0002\u0010\u000e\n\u0002\b\u0002\n\u0002\u0010\b\n\u0000\b\u0086\u0003\u0018\u00002\u00020\u0001B\u0007\b\u0002\u00a2\u0006\u0002\u0010\u0002R\u000e\u0010\u0003\u001a\u00020\u0004X\u0086T\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0005\u001a\u00020\u0004X\u0086T\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0006\u001a\u00020\u0007X\u0086T\u00a2\u0006\u0002\n\u0000\u00a8\u0006\b"}, d2 = {"Lcom/lijianqiang12/silent/data/model/net/api/Api$Companion;", "", "()V", "BASE_URL", "", "HOST", "PORT", "", "data_debug"})
    public static final class Companion {
        @org.jetbrains.annotations.NotNull()
        public static final java.lang.String HOST = "shuge888.com";
        public static final int PORT = 33333;
        @org.jetbrains.annotations.NotNull()
        public static final java.lang.String BASE_URL = "https://offphone.shuge888.com:33333";
        
        private Companion() {
            super();
        }
    }
}