package com.lijianqiang12.silent.data.model.db;

@kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000D\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0000\n\u0002\u0010\u0002\n\u0000\n\u0002\u0010\b\n\u0002\b\u0003\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0010!\n\u0002\b\u0005\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\u000e\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\t\n\u0002\b\u0004\bg\u0018\u00002\u00020\u0001J\u0016\u0010\u0002\u001a\u00020\u00032\u0006\u0010\u0004\u001a\u00020\u0005H\u00a7@\u00a2\u0006\u0002\u0010\u0006J\u0016\u0010\u0007\u001a\u00020\u00032\u0006\u0010\b\u001a\u00020\tH\u00a7@\u00a2\u0006\u0002\u0010\nJ\u001c\u0010\u000b\u001a\b\u0012\u0004\u0012\u00020\t0\f2\u0006\u0010\u0004\u001a\u00020\u0005H\u00a7@\u00a2\u0006\u0002\u0010\u0006J\u0018\u0010\r\u001a\u0004\u0018\u00010\t2\u0006\u0010\u0004\u001a\u00020\u0005H\u00a7@\u00a2\u0006\u0002\u0010\u0006J$\u0010\u000e\u001a\b\u0012\u0004\u0012\u00020\t0\f2\u0006\u0010\u0004\u001a\u00020\u00052\u0006\u0010\u000f\u001a\u00020\u0005H\u00a7@\u00a2\u0006\u0002\u0010\u0010J\u0018\u0010\u0011\u001a\u0004\u0018\u00010\u00122\u0006\u0010\u0013\u001a\u00020\u0014H\u00a7@\u00a2\u0006\u0002\u0010\u0015J\u001c\u0010\u0016\u001a\u000e\u0012\n\u0012\b\u0012\u0004\u0012\u00020\u00120\f0\u00172\u0006\u0010\u0004\u001a\u00020\u0005H\'J\u0016\u0010\u0018\u001a\u00020\u00192\u0006\u0010\b\u001a\u00020\tH\u00a7@\u00a2\u0006\u0002\u0010\nJ\u0016\u0010\u001a\u001a\u00020\u00032\u0006\u0010\b\u001a\u00020\tH\u00a7@\u00a2\u0006\u0002\u0010\nJ\u0010\u0010\u001b\u001a\u00020\u00032\u0006\u0010\u001c\u001a\u00020\u0005H\'\u00a8\u0006\u001d"}, d2 = {"Lcom/lijianqiang12/silent/data/model/db/TomatoDao;", "", "deleteAll", "", "userId", "", "(ILkotlin/coroutines/Continuation;)Ljava/lang/Object;", "deleteTomato", "tomato", "Lcom/lijianqiang12/silent/data/model/db/Tomato;", "(Lcom/lijianqiang12/silent/data/model/db/Tomato;Lkotlin/coroutines/Continuation;)Ljava/lang/Object;", "getAllTomatoList", "", "getLastTomato", "getTomatoWithState", "state", "(IILkotlin/coroutines/Continuation;)Ljava/lang/Object;", "getTomatoWithSub", "Lcom/lijianqiang12/silent/data/model/db/TomatoWithSub;", "tomatoIndexId", "", "(Ljava/lang/String;Lkotlin/coroutines/Continuation;)Ljava/lang/Object;", "getTomatoesWithSub", "Landroidx/lifecycle/LiveData;", "insertTomato", "", "updateTomato", "updateUserId", "newUserId", "data_debug"})
@androidx.room.Dao()
public abstract interface TomatoDao {
    
    @androidx.room.Query(value = "select * From Tomato Where userId = :userId and syncState = :state order by trend")
    @org.jetbrains.annotations.Nullable()
    public abstract java.lang.Object getTomatoWithState(int userId, int state, @org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super java.util.List<com.lijianqiang12.silent.data.model.db.Tomato>> $completion);
    
    @androidx.room.Query(value = "select * From Tomato Where userId = :userId order by trend")
    @org.jetbrains.annotations.Nullable()
    public abstract java.lang.Object getAllTomatoList(int userId, @org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super java.util.List<com.lijianqiang12.silent.data.model.db.Tomato>> $completion);
    
    @androidx.room.Transaction()
    @androidx.room.Query(value = "select * From Tomato where userId = :userId and syncState>=0 order by trend")
    @org.jetbrains.annotations.NotNull()
    public abstract androidx.lifecycle.LiveData<java.util.List<com.lijianqiang12.silent.data.model.db.TomatoWithSub>> getTomatoesWithSub(int userId);
    
    @androidx.room.Transaction()
    @androidx.room.Query(value = "select * from Tomato where tomatoIndexId = :tomatoIndexId and  syncState>=0 limit 1")
    @org.jetbrains.annotations.Nullable()
    public abstract java.lang.Object getTomatoWithSub(@org.jetbrains.annotations.NotNull()
    java.lang.String tomatoIndexId, @org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super com.lijianqiang12.silent.data.model.db.TomatoWithSub> $completion);
    
    @androidx.room.Query(value = "select * from Tomato Where userId = :userId order by trend desc limit 1")
    @org.jetbrains.annotations.Nullable()
    public abstract java.lang.Object getLastTomato(int userId, @org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super com.lijianqiang12.silent.data.model.db.Tomato> $completion);
    
    @androidx.room.Insert(onConflict = 5)
    @org.jetbrains.annotations.Nullable()
    public abstract java.lang.Object insertTomato(@org.jetbrains.annotations.NotNull()
    com.lijianqiang12.silent.data.model.db.Tomato tomato, @org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super java.lang.Long> $completion);
    
    @androidx.room.Update()
    @org.jetbrains.annotations.Nullable()
    public abstract java.lang.Object updateTomato(@org.jetbrains.annotations.NotNull()
    com.lijianqiang12.silent.data.model.db.Tomato tomato, @org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super kotlin.Unit> $completion);
    
    @androidx.room.Delete()
    @org.jetbrains.annotations.Nullable()
    public abstract java.lang.Object deleteTomato(@org.jetbrains.annotations.NotNull()
    com.lijianqiang12.silent.data.model.db.Tomato tomato, @org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super kotlin.Unit> $completion);
    
    @androidx.room.Query(value = "delete from Tomato where userId = :userId")
    @org.jetbrains.annotations.Nullable()
    public abstract java.lang.Object deleteAll(int userId, @org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super kotlin.Unit> $completion);
    
    @androidx.room.Query(value = "UPDATE Tomato SET userId = :newUserId WHERE userId = -1")
    public abstract void updateUserId(int newUserId);
}