package com.lijianqiang12.silent.data.model.db;

@kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000N\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0000\n\u0002\u0010\u0002\n\u0000\n\u0002\u0010\b\n\u0002\b\u0003\n\u0002\u0018\u0002\n\u0002\b\u0003\n\u0002\u0010!\n\u0002\b\u0004\n\u0002\u0018\u0002\n\u0002\b\u0003\n\u0002\u0010\u000e\n\u0002\b\n\n\u0002\u0010\t\n\u0002\b\u0002\n\u0002\u0010 \n\u0000\n\u0002\u0010\u000b\n\u0002\b\u0007\bg\u0018\u00002\u00020\u0001J\u0016\u0010\u0002\u001a\u00020\u00032\u0006\u0010\u0004\u001a\u00020\u0005H\u00a7@\u00a2\u0006\u0002\u0010\u0006J\u0016\u0010\u0007\u001a\u00020\u00032\u0006\u0010\b\u001a\u00020\tH\u00a7@\u00a2\u0006\u0002\u0010\nJ\u001c\u0010\u000b\u001a\u00020\u00032\f\u0010\f\u001a\b\u0012\u0004\u0012\u00020\t0\rH\u00a7@\u00a2\u0006\u0002\u0010\u000eJ\u001c\u0010\u000f\u001a\b\u0012\u0004\u0012\u00020\t0\r2\u0006\u0010\u0004\u001a\u00020\u0005H\u00a7@\u00a2\u0006\u0002\u0010\u0006J\u001c\u0010\u0010\u001a\b\u0012\u0004\u0012\u00020\t0\r2\u0006\u0010\u0004\u001a\u00020\u0005H\u00a7@\u00a2\u0006\u0002\u0010\u0006J\u001c\u0010\u0011\u001a\u000e\u0012\n\u0012\b\u0012\u0004\u0012\u00020\t0\r0\u00122\u0006\u0010\u0004\u001a\u00020\u0005H\'J\u0016\u0010\u0013\u001a\b\u0012\u0004\u0012\u00020\t0\r2\u0006\u0010\u0004\u001a\u00020\u0005H\'J \u0010\u0014\u001a\u0004\u0018\u00010\t2\u0006\u0010\u0015\u001a\u00020\u00162\u0006\u0010\u0017\u001a\u00020\u0016H\u00a7@\u00a2\u0006\u0002\u0010\u0018J\u001c\u0010\u0019\u001a\u000e\u0012\n\u0012\b\u0012\u0004\u0012\u00020\t0\r0\u00122\u0006\u0010\u0017\u001a\u00020\u0016H\'J\u0016\u0010\u001a\u001a\b\u0012\u0004\u0012\u00020\t0\r2\u0006\u0010\u0017\u001a\u00020\u0016H\'J$\u0010\u001b\u001a\b\u0012\u0004\u0012\u00020\t0\r2\u0006\u0010\u0004\u001a\u00020\u00052\u0006\u0010\u001c\u001a\u00020\u0005H\u00a7@\u00a2\u0006\u0002\u0010\u001dJ\u001c\u0010\u001e\u001a\u000e\u0012\n\u0012\b\u0012\u0004\u0012\u00020\t0\r0\u00122\u0006\u0010\u0015\u001a\u00020\u0016H\'J\u0016\u0010\u001f\u001a\b\u0012\u0004\u0012\u00020\t0\r2\u0006\u0010\u0015\u001a\u00020\u0016H\'J\u0010\u0010 \u001a\u00020!2\u0006\u0010\b\u001a\u00020\tH\'J\u0010\u0010\"\u001a\u00020!2\u0006\u0010\b\u001a\u00020\tH\'J\u001c\u0010#\u001a\b\u0012\u0004\u0012\u00020!0$2\f\u0010\f\u001a\b\u0012\u0004\u0012\u00020\t0\rH\'J6\u0010%\u001a\u00020&2\u0006\u0010\u0004\u001a\u00020\u00052\u0006\u0010\u0015\u001a\u00020\u00162\u0006\u0010\u0017\u001a\u00020\u00162\u0006\u0010\'\u001a\u00020\u00162\u0006\u0010(\u001a\u00020\u0016H\u00a7@\u00a2\u0006\u0002\u0010)J\u0010\u0010*\u001a\u00020\u00032\u0006\u0010+\u001a\u00020\u0005H\'J\u0016\u0010,\u001a\u00020\u00032\u0006\u0010\b\u001a\u00020\tH\u00a7@\u00a2\u0006\u0002\u0010\n\u00a8\u0006-"}, d2 = {"Lcom/lijianqiang12/silent/data/model/db/WhiteAppDao;", "", "deleteAll", "", "userId", "", "(ILkotlin/coroutines/Continuation;)Ljava/lang/Object;", "deleteWhiteApp", "whiteApp", "Lcom/lijianqiang12/silent/data/model/db/WhiteApp;", "(Lcom/lijianqiang12/silent/data/model/db/WhiteApp;Lkotlin/coroutines/Continuation;)Ljava/lang/Object;", "deleteWhiteApps", "whiteApps", "", "(Ljava/util/List;Lkotlin/coroutines/Continuation;)Ljava/lang/Object;", "getAllWhiteAppList", "getGlobalWhiteAppList", "getGlobalWhiteApps", "Landroidx/lifecycle/LiveData;", "getGlobalWhiteAppsImmediate", "getLastWhiteApp", "tomatoIndexId", "", "scheduleIndexId", "(Ljava/lang/String;Ljava/lang/String;Lkotlin/coroutines/Continuation;)Ljava/lang/Object;", "getWhiteAppsWithScheduleId", "getWhiteAppsWithScheduleIdAtOnce", "getWhiteAppsWithState", "state", "(IILkotlin/coroutines/Continuation;)Ljava/lang/Object;", "getWhiteAppsWithTomatoId", "getWhiteAppsWithTomatoIdAtOnce", "insertWhiteApp", "", "insertWhiteAppImmediate", "insertWhiteApps", "", "isWhiteAppExist", "", "pkg", "mainActivity", "(ILjava/lang/String;Ljava/lang/String;Ljava/lang/String;Ljava/lang/String;Lkotlin/coroutines/Continuation;)Ljava/lang/Object;", "updateUserId", "newUserId", "updateWhiteApp", "data_debug"})
@androidx.room.Dao()
public abstract interface WhiteAppDao {
    
    @androidx.room.Query(value = "select * From WhiteApp Where userId = :userId and tomatoIndexId = \'\' and scheduleIndexId = \'\' and syncState>=0 order by trend")
    @org.jetbrains.annotations.Nullable()
    public abstract java.lang.Object getGlobalWhiteAppList(int userId, @org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super java.util.List<com.lijianqiang12.silent.data.model.db.WhiteApp>> $completion);
    
    @androidx.room.Query(value = "select * From WhiteApp Where userId = :userId order by trend")
    @org.jetbrains.annotations.Nullable()
    public abstract java.lang.Object getAllWhiteAppList(int userId, @org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super java.util.List<com.lijianqiang12.silent.data.model.db.WhiteApp>> $completion);
    
    @androidx.room.Query(value = "select * From WhiteApp Where userId = :userId and tomatoIndexId = \'\' and scheduleIndexId = \'\' and syncState>=0 order by trend")
    @org.jetbrains.annotations.NotNull()
    public abstract androidx.lifecycle.LiveData<java.util.List<com.lijianqiang12.silent.data.model.db.WhiteApp>> getGlobalWhiteApps(int userId);
    
    @androidx.room.Query(value = "select * From WhiteApp Where userId = :userId and tomatoIndexId = \'\' and scheduleIndexId = \'\' and syncState>=0 order by trend")
    @org.jetbrains.annotations.NotNull()
    public abstract java.util.List<com.lijianqiang12.silent.data.model.db.WhiteApp> getGlobalWhiteAppsImmediate(int userId);
    
    @androidx.room.Query(value = "select * From WhiteApp Where userId = :userId and syncState = :state order by trend")
    @org.jetbrains.annotations.Nullable()
    public abstract java.lang.Object getWhiteAppsWithState(int userId, int state, @org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super java.util.List<com.lijianqiang12.silent.data.model.db.WhiteApp>> $completion);
    
    @androidx.room.Query(value = "select * From WhiteApp Where tomatoIndexId = :tomatoIndexId and syncState>=0 order by trend")
    @org.jetbrains.annotations.NotNull()
    public abstract androidx.lifecycle.LiveData<java.util.List<com.lijianqiang12.silent.data.model.db.WhiteApp>> getWhiteAppsWithTomatoId(@org.jetbrains.annotations.NotNull()
    java.lang.String tomatoIndexId);
    
    @androidx.room.Query(value = "select * From WhiteApp Where scheduleIndexId = :scheduleIndexId and syncState>=0 order by trend")
    @org.jetbrains.annotations.NotNull()
    public abstract androidx.lifecycle.LiveData<java.util.List<com.lijianqiang12.silent.data.model.db.WhiteApp>> getWhiteAppsWithScheduleId(@org.jetbrains.annotations.NotNull()
    java.lang.String scheduleIndexId);
    
    @androidx.room.Query(value = "select * From WhiteApp Where tomatoIndexId = :tomatoIndexId and syncState>=0 order by trend")
    @org.jetbrains.annotations.NotNull()
    public abstract java.util.List<com.lijianqiang12.silent.data.model.db.WhiteApp> getWhiteAppsWithTomatoIdAtOnce(@org.jetbrains.annotations.NotNull()
    java.lang.String tomatoIndexId);
    
    @androidx.room.Query(value = "select * From WhiteApp Where scheduleIndexId = :scheduleIndexId and syncState>=0 order by trend")
    @org.jetbrains.annotations.NotNull()
    public abstract java.util.List<com.lijianqiang12.silent.data.model.db.WhiteApp> getWhiteAppsWithScheduleIdAtOnce(@org.jetbrains.annotations.NotNull()
    java.lang.String scheduleIndexId);
    
    @androidx.room.Query(value = "select * from WhiteApp Where tomatoIndexId = :tomatoIndexId and scheduleIndexId = :scheduleIndexId  order by trend desc limit 1")
    @org.jetbrains.annotations.Nullable()
    public abstract java.lang.Object getLastWhiteApp(@org.jetbrains.annotations.NotNull()
    java.lang.String tomatoIndexId, @org.jetbrains.annotations.NotNull()
    java.lang.String scheduleIndexId, @org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super com.lijianqiang12.silent.data.model.db.WhiteApp> $completion);
    
    @androidx.room.Query(value = "SELECT EXISTS(select 1 from WhiteApp Where userId = :userId and tomatoIndexId = :tomatoIndexId and scheduleIndexId = :scheduleIndexId and pkg=:pkg and mainActivity=:mainActivity and syncState>=0 limit 1)")
    @org.jetbrains.annotations.Nullable()
    public abstract java.lang.Object isWhiteAppExist(int userId, @org.jetbrains.annotations.NotNull()
    java.lang.String tomatoIndexId, @org.jetbrains.annotations.NotNull()
    java.lang.String scheduleIndexId, @org.jetbrains.annotations.NotNull()
    java.lang.String pkg, @org.jetbrains.annotations.NotNull()
    java.lang.String mainActivity, @org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super java.lang.Boolean> $completion);
    
    @androidx.room.Insert(onConflict = 5)
    public abstract long insertWhiteApp(@org.jetbrains.annotations.NotNull()
    com.lijianqiang12.silent.data.model.db.WhiteApp whiteApp);
    
    @androidx.room.Insert(onConflict = 5)
    public abstract long insertWhiteAppImmediate(@org.jetbrains.annotations.NotNull()
    com.lijianqiang12.silent.data.model.db.WhiteApp whiteApp);
    
    @androidx.room.Delete()
    @org.jetbrains.annotations.Nullable()
    public abstract java.lang.Object deleteWhiteApp(@org.jetbrains.annotations.NotNull()
    com.lijianqiang12.silent.data.model.db.WhiteApp whiteApp, @org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super kotlin.Unit> $completion);
    
    @androidx.room.Delete()
    @org.jetbrains.annotations.Nullable()
    public abstract java.lang.Object deleteWhiteApps(@org.jetbrains.annotations.NotNull()
    java.util.List<com.lijianqiang12.silent.data.model.db.WhiteApp> whiteApps, @org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super kotlin.Unit> $completion);
    
    @androidx.room.Update()
    @org.jetbrains.annotations.Nullable()
    public abstract java.lang.Object updateWhiteApp(@org.jetbrains.annotations.NotNull()
    com.lijianqiang12.silent.data.model.db.WhiteApp whiteApp, @org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super kotlin.Unit> $completion);
    
    @androidx.room.Insert(onConflict = 5)
    @org.jetbrains.annotations.NotNull()
    public abstract java.util.List<java.lang.Long> insertWhiteApps(@org.jetbrains.annotations.NotNull()
    java.util.List<com.lijianqiang12.silent.data.model.db.WhiteApp> whiteApps);
    
    @androidx.room.Query(value = "delete from WhiteApp where userId = :userId")
    @org.jetbrains.annotations.Nullable()
    public abstract java.lang.Object deleteAll(int userId, @org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super kotlin.Unit> $completion);
    
    @androidx.room.Query(value = "UPDATE WhiteApp SET userId = :newUserId WHERE userId = -1")
    public abstract void updateUserId(int newUserId);
}