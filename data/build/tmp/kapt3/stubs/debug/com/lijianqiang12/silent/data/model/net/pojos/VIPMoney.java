package com.lijianqiang12.silent.data.model.net.pojos;

@kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u00004\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0000\n\u0002\u0010!\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\u000e\n\u0002\b\u0002\n\u0002\u0010\u000b\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0002\b\u0003\n\u0002\u0010\b\n\u0002\b&\b\u0086\b\u0018\u00002\u00020\u0001Bq\u0012\f\u0010\u0002\u001a\b\u0012\u0004\u0012\u00020\u00040\u0003\u0012\u0006\u0010\u0005\u001a\u00020\u0006\u0012\u0006\u0010\u0007\u001a\u00020\u0006\u0012\u0006\u0010\b\u001a\u00020\t\u0012\u0006\u0010\n\u001a\u00020\u0006\u0012\f\u0010\u000b\u001a\b\u0012\u0004\u0012\u00020\f0\u0003\u0012\u0006\u0010\r\u001a\u00020\t\u0012\u0006\u0010\u000e\u001a\u00020\t\u0012\u0006\u0010\u000f\u001a\u00020\u0010\u0012\u0006\u0010\u0011\u001a\u00020\u0010\u0012\u0006\u0010\u0012\u001a\u00020\t\u0012\u0006\u0010\u0013\u001a\u00020\t\u00a2\u0006\u0002\u0010\u0014J\u000f\u0010%\u001a\b\u0012\u0004\u0012\u00020\u00040\u0003H\u00c6\u0003J\t\u0010&\u001a\u00020\u0010H\u00c6\u0003J\t\u0010\'\u001a\u00020\tH\u00c6\u0003J\t\u0010(\u001a\u00020\tH\u00c6\u0003J\t\u0010)\u001a\u00020\u0006H\u00c6\u0003J\t\u0010*\u001a\u00020\u0006H\u00c6\u0003J\t\u0010+\u001a\u00020\tH\u00c6\u0003J\t\u0010,\u001a\u00020\u0006H\u00c6\u0003J\u000f\u0010-\u001a\b\u0012\u0004\u0012\u00020\f0\u0003H\u00c6\u0003J\t\u0010.\u001a\u00020\tH\u00c6\u0003J\t\u0010/\u001a\u00020\tH\u00c6\u0003J\t\u00100\u001a\u00020\u0010H\u00c6\u0003J\u008d\u0001\u00101\u001a\u00020\u00002\u000e\b\u0002\u0010\u0002\u001a\b\u0012\u0004\u0012\u00020\u00040\u00032\b\b\u0002\u0010\u0005\u001a\u00020\u00062\b\b\u0002\u0010\u0007\u001a\u00020\u00062\b\b\u0002\u0010\b\u001a\u00020\t2\b\b\u0002\u0010\n\u001a\u00020\u00062\u000e\b\u0002\u0010\u000b\u001a\b\u0012\u0004\u0012\u00020\f0\u00032\b\b\u0002\u0010\r\u001a\u00020\t2\b\b\u0002\u0010\u000e\u001a\u00020\t2\b\b\u0002\u0010\u000f\u001a\u00020\u00102\b\b\u0002\u0010\u0011\u001a\u00020\u00102\b\b\u0002\u0010\u0012\u001a\u00020\t2\b\b\u0002\u0010\u0013\u001a\u00020\tH\u00c6\u0001J\u0013\u00102\u001a\u00020\t2\b\u00103\u001a\u0004\u0018\u00010\u0001H\u00d6\u0003J\t\u00104\u001a\u00020\u0010H\u00d6\u0001J\t\u00105\u001a\u00020\u0006H\u00d6\u0001R\u0011\u0010\u0011\u001a\u00020\u0010\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0015\u0010\u0016R\u0011\u0010\u0007\u001a\u00020\u0006\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0017\u0010\u0018R\u0017\u0010\u0002\u001a\b\u0012\u0004\u0012\u00020\u00040\u0003\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0019\u0010\u001aR\u0011\u0010\u0005\u001a\u00020\u0006\u00a2\u0006\b\n\u0000\u001a\u0004\b\u001b\u0010\u0018R\u0011\u0010\n\u001a\u00020\u0006\u00a2\u0006\b\n\u0000\u001a\u0004\b\u001c\u0010\u0018R\u0017\u0010\u000b\u001a\b\u0012\u0004\u0012\u00020\f0\u0003\u00a2\u0006\b\n\u0000\u001a\u0004\b\u001d\u0010\u001aR\u0011\u0010\u000e\u001a\u00020\t\u00a2\u0006\b\n\u0000\u001a\u0004\b\u001e\u0010\u001fR\u0011\u0010\r\u001a\u00020\t\u00a2\u0006\b\n\u0000\u001a\u0004\b \u0010\u001fR\u0011\u0010\u000f\u001a\u00020\u0010\u00a2\u0006\b\n\u0000\u001a\u0004\b!\u0010\u0016R\u0011\u0010\b\u001a\u00020\t\u00a2\u0006\b\n\u0000\u001a\u0004\b\"\u0010\u001fR\u0011\u0010\u0013\u001a\u00020\t\u00a2\u0006\b\n\u0000\u001a\u0004\b#\u0010\u001fR\u0011\u0010\u0012\u001a\u00020\t\u00a2\u0006\b\n\u0000\u001a\u0004\b$\u0010\u001f\u00a8\u00066"}, d2 = {"Lcom/lijianqiang12/silent/data/model/net/pojos/VIPMoney;", "", "giftList", "", "Lcom/lijianqiang12/silent/data/model/net/pojos/Gift;", "giftResult", "", "giftAddress", "show100", "", "giftRule", "moneyList", "Lcom/lijianqiang12/silent/data/model/net/pojos/Money;", "notifyForever", "notifyDeliveryInfo", "selectIndex", "", "daojishi", "showGift", "showChangeAddress", "(Ljava/util/List;Ljava/lang/String;Ljava/lang/String;ZLjava/lang/String;Ljava/util/List;ZZIIZZ)V", "getDaojishi", "()I", "getGiftAddress", "()Ljava/lang/String;", "getGiftList", "()Ljava/util/List;", "getGiftResult", "getGiftRule", "getMoneyList", "getNotifyDeliveryInfo", "()Z", "getNotifyForever", "getSelectIndex", "getShow100", "getShowChangeAddress", "getShowGift", "component1", "component10", "component11", "component12", "component2", "component3", "component4", "component5", "component6", "component7", "component8", "component9", "copy", "equals", "other", "hashCode", "toString", "data_debug"})
public final class VIPMoney {
    @org.jetbrains.annotations.NotNull()
    private final java.util.List<com.lijianqiang12.silent.data.model.net.pojos.Gift> giftList = null;
    @org.jetbrains.annotations.NotNull()
    private final java.lang.String giftResult = null;
    @org.jetbrains.annotations.NotNull()
    private final java.lang.String giftAddress = null;
    private final boolean show100 = false;
    @org.jetbrains.annotations.NotNull()
    private final java.lang.String giftRule = null;
    @org.jetbrains.annotations.NotNull()
    private final java.util.List<com.lijianqiang12.silent.data.model.net.pojos.Money> moneyList = null;
    private final boolean notifyForever = false;
    private final boolean notifyDeliveryInfo = false;
    private final int selectIndex = 0;
    private final int daojishi = 0;
    private final boolean showGift = false;
    private final boolean showChangeAddress = false;
    
    public VIPMoney(@org.jetbrains.annotations.NotNull()
    java.util.List<com.lijianqiang12.silent.data.model.net.pojos.Gift> giftList, @org.jetbrains.annotations.NotNull()
    java.lang.String giftResult, @org.jetbrains.annotations.NotNull()
    java.lang.String giftAddress, boolean show100, @org.jetbrains.annotations.NotNull()
    java.lang.String giftRule, @org.jetbrains.annotations.NotNull()
    java.util.List<com.lijianqiang12.silent.data.model.net.pojos.Money> moneyList, boolean notifyForever, boolean notifyDeliveryInfo, int selectIndex, int daojishi, boolean showGift, boolean showChangeAddress) {
        super();
    }
    
    @org.jetbrains.annotations.NotNull()
    public final java.util.List<com.lijianqiang12.silent.data.model.net.pojos.Gift> getGiftList() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final java.lang.String getGiftResult() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final java.lang.String getGiftAddress() {
        return null;
    }
    
    public final boolean getShow100() {
        return false;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final java.lang.String getGiftRule() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final java.util.List<com.lijianqiang12.silent.data.model.net.pojos.Money> getMoneyList() {
        return null;
    }
    
    public final boolean getNotifyForever() {
        return false;
    }
    
    public final boolean getNotifyDeliveryInfo() {
        return false;
    }
    
    public final int getSelectIndex() {
        return 0;
    }
    
    public final int getDaojishi() {
        return 0;
    }
    
    public final boolean getShowGift() {
        return false;
    }
    
    public final boolean getShowChangeAddress() {
        return false;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final java.util.List<com.lijianqiang12.silent.data.model.net.pojos.Gift> component1() {
        return null;
    }
    
    public final int component10() {
        return 0;
    }
    
    public final boolean component11() {
        return false;
    }
    
    public final boolean component12() {
        return false;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final java.lang.String component2() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final java.lang.String component3() {
        return null;
    }
    
    public final boolean component4() {
        return false;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final java.lang.String component5() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final java.util.List<com.lijianqiang12.silent.data.model.net.pojos.Money> component6() {
        return null;
    }
    
    public final boolean component7() {
        return false;
    }
    
    public final boolean component8() {
        return false;
    }
    
    public final int component9() {
        return 0;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final com.lijianqiang12.silent.data.model.net.pojos.VIPMoney copy(@org.jetbrains.annotations.NotNull()
    java.util.List<com.lijianqiang12.silent.data.model.net.pojos.Gift> giftList, @org.jetbrains.annotations.NotNull()
    java.lang.String giftResult, @org.jetbrains.annotations.NotNull()
    java.lang.String giftAddress, boolean show100, @org.jetbrains.annotations.NotNull()
    java.lang.String giftRule, @org.jetbrains.annotations.NotNull()
    java.util.List<com.lijianqiang12.silent.data.model.net.pojos.Money> moneyList, boolean notifyForever, boolean notifyDeliveryInfo, int selectIndex, int daojishi, boolean showGift, boolean showChangeAddress) {
        return null;
    }
    
    @java.lang.Override()
    public boolean equals(@org.jetbrains.annotations.Nullable()
    java.lang.Object other) {
        return false;
    }
    
    @java.lang.Override()
    public int hashCode() {
        return 0;
    }
    
    @java.lang.Override()
    @org.jetbrains.annotations.NotNull()
    public java.lang.String toString() {
        return null;
    }
}