package com.lijianqiang12.silent.data.model.repository;

@kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u00008\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0010\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0003\n\u0002\u0010!\n\u0002\b\u0003\n\u0002\u0010\b\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0002\b\u0005\u0018\u0000 \u00172\u00020\u0001:\u0001\u0017B\u000f\b\u0002\u0012\u0006\u0010\u0002\u001a\u00020\u0003\u00a2\u0006\u0002\u0010\u0004J\u0016\u0010\u0005\u001a\u00020\u00062\u0006\u0010\u0007\u001a\u00020\bH\u0086@\u00a2\u0006\u0002\u0010\tJ\u0016\u0010\n\u001a\u00020\u00062\u0006\u0010\u0007\u001a\u00020\bH\u0086@\u00a2\u0006\u0002\u0010\tJ\u0014\u0010\u000b\u001a\b\u0012\u0004\u0012\u00020\b0\fH\u0086@\u00a2\u0006\u0002\u0010\rJ\u001c\u0010\u000e\u001a\b\u0012\u0004\u0012\u00020\b0\f2\u0006\u0010\u000f\u001a\u00020\u0010H\u0086@\u00a2\u0006\u0002\u0010\u0011J\u001a\u0010\u0012\u001a\u000e\u0012\n\u0012\b\u0012\u0004\u0012\u00020\b0\f0\u00132\u0006\u0010\u0014\u001a\u00020\u0010J\u0010\u0010\u0015\u001a\u0004\u0018\u00010\bH\u0086@\u00a2\u0006\u0002\u0010\rJ\u0016\u0010\u0016\u001a\u00020\u00062\u0006\u0010\u0007\u001a\u00020\bH\u0086@\u00a2\u0006\u0002\u0010\tR\u000e\u0010\u0002\u001a\u00020\u0003X\u0082\u0004\u00a2\u0006\u0002\n\u0000\u00a8\u0006\u0018"}, d2 = {"Lcom/lijianqiang12/silent/data/model/repository/AppLimitRepository;", "", "appLimitDao", "Lcom/lijianqiang12/silent/data/model/db/AppLimitDao;", "(Lcom/lijianqiang12/silent/data/model/db/AppLimitDao;)V", "createAppLimit", "", "appLimit", "Lcom/lijianqiang12/silent/data/model/db/AppLimit;", "(Lcom/lijianqiang12/silent/data/model/db/AppLimit;Lkotlin/coroutines/Continuation;)Ljava/lang/Object;", "deleteAppLimit", "getAllAppLimitList", "", "(Lkotlin/coroutines/Continuation;)Ljava/lang/Object;", "getAppLimitWithState", "state", "", "(ILkotlin/coroutines/Continuation;)Ljava/lang/Object;", "getAppLimits", "Landroidx/lifecycle/LiveData;", "userId", "getLastAppLimit", "updateAppLimit", "Companion", "data_debug"})
public final class AppLimitRepository {
    @org.jetbrains.annotations.NotNull()
    private final com.lijianqiang12.silent.data.model.db.AppLimitDao appLimitDao = null;
    @kotlin.jvm.Volatile()
    @org.jetbrains.annotations.Nullable()
    private static volatile com.lijianqiang12.silent.data.model.repository.AppLimitRepository instance;
    @org.jetbrains.annotations.NotNull()
    public static final com.lijianqiang12.silent.data.model.repository.AppLimitRepository.Companion Companion = null;
    
    private AppLimitRepository(com.lijianqiang12.silent.data.model.db.AppLimitDao appLimitDao) {
        super();
    }
    
    @org.jetbrains.annotations.Nullable()
    public final java.lang.Object getAllAppLimitList(@org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super java.util.List<com.lijianqiang12.silent.data.model.db.AppLimit>> $completion) {
        return null;
    }
    
    @org.jetbrains.annotations.Nullable()
    public final java.lang.Object getAppLimitWithState(int state, @org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super java.util.List<com.lijianqiang12.silent.data.model.db.AppLimit>> $completion) {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final androidx.lifecycle.LiveData<java.util.List<com.lijianqiang12.silent.data.model.db.AppLimit>> getAppLimits(int userId) {
        return null;
    }
    
    @org.jetbrains.annotations.Nullable()
    public final java.lang.Object getLastAppLimit(@org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super com.lijianqiang12.silent.data.model.db.AppLimit> $completion) {
        return null;
    }
    
    @org.jetbrains.annotations.Nullable()
    public final java.lang.Object createAppLimit(@org.jetbrains.annotations.NotNull()
    com.lijianqiang12.silent.data.model.db.AppLimit appLimit, @org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super kotlin.Unit> $completion) {
        return null;
    }
    
    @org.jetbrains.annotations.Nullable()
    public final java.lang.Object updateAppLimit(@org.jetbrains.annotations.NotNull()
    com.lijianqiang12.silent.data.model.db.AppLimit appLimit, @org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super kotlin.Unit> $completion) {
        return null;
    }
    
    @org.jetbrains.annotations.Nullable()
    public final java.lang.Object deleteAppLimit(@org.jetbrains.annotations.NotNull()
    com.lijianqiang12.silent.data.model.db.AppLimit appLimit, @org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super kotlin.Unit> $completion) {
        return null;
    }
    
    @kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000\u001a\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0000\b\u0086\u0003\u0018\u00002\u00020\u0001B\u0007\b\u0002\u00a2\u0006\u0002\u0010\u0002J\u000e\u0010\u0005\u001a\u00020\u00042\u0006\u0010\u0006\u001a\u00020\u0007R\u0010\u0010\u0003\u001a\u0004\u0018\u00010\u0004X\u0082\u000e\u00a2\u0006\u0002\n\u0000\u00a8\u0006\b"}, d2 = {"Lcom/lijianqiang12/silent/data/model/repository/AppLimitRepository$Companion;", "", "()V", "instance", "Lcom/lijianqiang12/silent/data/model/repository/AppLimitRepository;", "getInstance", "appLimitDao", "Lcom/lijianqiang12/silent/data/model/db/AppLimitDao;", "data_debug"})
    public static final class Companion {
        
        private Companion() {
            super();
        }
        
        @org.jetbrains.annotations.NotNull()
        public final com.lijianqiang12.silent.data.model.repository.AppLimitRepository getInstance(@org.jetbrains.annotations.NotNull()
        com.lijianqiang12.silent.data.model.db.AppLimitDao appLimitDao) {
            return null;
        }
    }
}