package com.lijianqiang12.silent.data.model.db;

@kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000@\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\t\n\u0000\n\u0002\u0010\u000e\n\u0002\b\u0005\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0010\b\n\u0002\b\u0002\n\u0002\u0010\u000b\n\u0002\bj\n\u0002\u0010\u0000\n\u0002\b\u0003\n\u0002\u0010\u0002\n\u0002\b\u0003\b\u0087\b\u0018\u0000 \u0082\u00012\u00020\u0001:\u0002\u0082\u0001B\u001f\b\u0016\u0012\u0006\u0010\u0002\u001a\u00020\u0003\u0012\u0006\u0010\u0004\u001a\u00020\u0005\u0012\u0006\u0010\u0006\u001a\u00020\u0005\u00a2\u0006\u0002\u0010\u0007B\u000f\b\u0016\u0012\u0006\u0010\u0002\u001a\u00020\u0003\u00a2\u0006\u0002\u0010\bB\u0007\b\u0016\u00a2\u0006\u0002\u0010\tB\u000f\b\u0016\u0012\u0006\u0010\n\u001a\u00020\u000b\u00a2\u0006\u0002\u0010\fB\u00fb\u0001\u0012\b\b\u0002\u0010\u0002\u001a\u00020\u0003\u0012\u0006\u0010\r\u001a\u00020\u000e\u0012\u0006\u0010\u000f\u001a\u00020\u0005\u0012\b\b\u0002\u0010\u0006\u001a\u00020\u0005\u0012\b\b\u0002\u0010\u0010\u001a\u00020\u0011\u0012\b\b\u0002\u0010\u0012\u001a\u00020\u0003\u0012\b\b\u0002\u0010\u0013\u001a\u00020\u0003\u0012\b\b\u0002\u0010\u0014\u001a\u00020\u0003\u0012\b\b\u0002\u0010\u0004\u001a\u00020\u0005\u0012\b\b\u0002\u0010\u0015\u001a\u00020\u0011\u0012\b\b\u0002\u0010\u0016\u001a\u00020\u0011\u0012\b\b\u0002\u0010\u0017\u001a\u00020\u0011\u0012\b\b\u0002\u0010\u0018\u001a\u00020\u0011\u0012\b\b\u0002\u0010\u0019\u001a\u00020\u0011\u0012\b\b\u0002\u0010\u001a\u001a\u00020\u0011\u0012\b\b\u0002\u0010\u001b\u001a\u00020\u0011\u0012\b\b\u0002\u0010\u001c\u001a\u00020\u0003\u0012\b\b\u0002\u0010\u001d\u001a\u00020\u0003\u0012\b\b\u0002\u0010\u001e\u001a\u00020\u0003\u0012\b\b\u0002\u0010\u001f\u001a\u00020\u0011\u0012\b\b\u0002\u0010 \u001a\u00020\u000e\u0012\b\b\u0002\u0010!\u001a\u00020\u000e\u0012\b\b\u0002\u0010\"\u001a\u00020\u0003\u0012\b\b\u0002\u0010#\u001a\u00020\u0003\u0012\b\b\u0002\u0010$\u001a\u00020\u000e\u00a2\u0006\u0002\u0010%J\t\u0010_\u001a\u00020\u0003H\u00c6\u0003J\t\u0010`\u001a\u00020\u0011H\u00c6\u0003J\t\u0010a\u001a\u00020\u0011H\u00c6\u0003J\t\u0010b\u001a\u00020\u0011H\u00c6\u0003J\t\u0010c\u001a\u00020\u0011H\u00c6\u0003J\t\u0010d\u001a\u00020\u0011H\u00c6\u0003J\t\u0010e\u001a\u00020\u0011H\u00c6\u0003J\t\u0010f\u001a\u00020\u0011H\u00c6\u0003J\t\u0010g\u001a\u00020\u0003H\u00c6\u0003J\t\u0010h\u001a\u00020\u0003H\u00c6\u0003J\t\u0010i\u001a\u00020\u0003H\u00c6\u0003J\t\u0010j\u001a\u00020\u000eH\u00c6\u0003J\t\u0010k\u001a\u00020\u0011H\u00c6\u0003J\t\u0010l\u001a\u00020\u000eH\u00c6\u0003J\t\u0010m\u001a\u00020\u000eH\u00c6\u0003J\t\u0010n\u001a\u00020\u0003H\u00c6\u0003J\t\u0010o\u001a\u00020\u0003H\u00c6\u0003J\t\u0010p\u001a\u00020\u000eH\u00c6\u0003J\t\u0010q\u001a\u00020\u0005H\u00c6\u0003J\t\u0010r\u001a\u00020\u0005H\u00c6\u0003J\t\u0010s\u001a\u00020\u0011H\u00c6\u0003J\t\u0010t\u001a\u00020\u0003H\u00c6\u0003J\t\u0010u\u001a\u00020\u0003H\u00c6\u0003J\t\u0010v\u001a\u00020\u0003H\u00c6\u0003J\t\u0010w\u001a\u00020\u0005H\u00c6\u0003J\u0083\u0002\u0010x\u001a\u00020\u00002\b\b\u0002\u0010\u0002\u001a\u00020\u00032\b\b\u0002\u0010\r\u001a\u00020\u000e2\b\b\u0002\u0010\u000f\u001a\u00020\u00052\b\b\u0002\u0010\u0006\u001a\u00020\u00052\b\b\u0002\u0010\u0010\u001a\u00020\u00112\b\b\u0002\u0010\u0012\u001a\u00020\u00032\b\b\u0002\u0010\u0013\u001a\u00020\u00032\b\b\u0002\u0010\u0014\u001a\u00020\u00032\b\b\u0002\u0010\u0004\u001a\u00020\u00052\b\b\u0002\u0010\u0015\u001a\u00020\u00112\b\b\u0002\u0010\u0016\u001a\u00020\u00112\b\b\u0002\u0010\u0017\u001a\u00020\u00112\b\b\u0002\u0010\u0018\u001a\u00020\u00112\b\b\u0002\u0010\u0019\u001a\u00020\u00112\b\b\u0002\u0010\u001a\u001a\u00020\u00112\b\b\u0002\u0010\u001b\u001a\u00020\u00112\b\b\u0002\u0010\u001c\u001a\u00020\u00032\b\b\u0002\u0010\u001d\u001a\u00020\u00032\b\b\u0002\u0010\u001e\u001a\u00020\u00032\b\b\u0002\u0010\u001f\u001a\u00020\u00112\b\b\u0002\u0010 \u001a\u00020\u000e2\b\b\u0002\u0010!\u001a\u00020\u000e2\b\b\u0002\u0010\"\u001a\u00020\u00032\b\b\u0002\u0010#\u001a\u00020\u00032\b\b\u0002\u0010$\u001a\u00020\u000eH\u00c6\u0001J\b\u0010y\u001a\u00020\u000eH\u0016J\u0013\u0010z\u001a\u00020\u00112\b\u0010{\u001a\u0004\u0018\u00010|H\u00d6\u0003J\t\u0010}\u001a\u00020\u000eH\u00d6\u0001J\b\u0010~\u001a\u00020\u0005H\u0016J\u001a\u0010\u007f\u001a\u00030\u0080\u00012\u0006\u0010\n\u001a\u00020\u000b2\u0007\u0010\u0081\u0001\u001a\u00020\u000eH\u0016R\u001a\u0010\u000f\u001a\u00020\u0005X\u0086\u000e\u00a2\u0006\u000e\n\u0000\u001a\u0004\b&\u0010\'\"\u0004\b(\u0010)R\u001a\u0010\u0006\u001a\u00020\u0005X\u0086\u000e\u00a2\u0006\u000e\n\u0000\u001a\u0004\b*\u0010\'\"\u0004\b+\u0010)R\u001a\u0010\u001d\u001a\u00020\u0003X\u0086\u000e\u00a2\u0006\u000e\n\u0000\u001a\u0004\b,\u0010-\"\u0004\b.\u0010\bR\u001a\u0010\u001e\u001a\u00020\u0003X\u0086\u000e\u00a2\u0006\u000e\n\u0000\u001a\u0004\b/\u0010-\"\u0004\b0\u0010\bR\u001a\u0010\u001c\u001a\u00020\u0003X\u0086\u000e\u00a2\u0006\u000e\n\u0000\u001a\u0004\b1\u0010-\"\u0004\b2\u0010\bR\u001a\u0010\u0013\u001a\u00020\u0003X\u0086\u000e\u00a2\u0006\u000e\n\u0000\u001a\u0004\b3\u0010-\"\u0004\b4\u0010\bR\u001a\u0010\u001a\u001a\u00020\u0011X\u0086\u000e\u00a2\u0006\u000e\n\u0000\u001a\u0004\b5\u00106\"\u0004\b7\u00108R\u001e\u0010\u0002\u001a\u00020\u00038\u0006@\u0006X\u0087\u000e\u00a2\u0006\u000e\n\u0000\u001a\u0004\b9\u0010-\"\u0004\b:\u0010\bR\u001a\u0010\u0010\u001a\u00020\u0011X\u0086\u000e\u00a2\u0006\u000e\n\u0000\u001a\u0004\b;\u00106\"\u0004\b<\u00108R\u001a\u0010\u0014\u001a\u00020\u0003X\u0086\u000e\u00a2\u0006\u000e\n\u0000\u001a\u0004\b=\u0010-\"\u0004\b>\u0010\bR\u001a\u0010\u0016\u001a\u00020\u0011X\u0086\u000e\u00a2\u0006\u000e\n\u0000\u001a\u0004\b?\u00106\"\u0004\b@\u00108R\u001a\u0010\u001b\u001a\u00020\u0011X\u0086\u000e\u00a2\u0006\u000e\n\u0000\u001a\u0004\bA\u00106\"\u0004\bB\u00108R\u001a\u0010\u0012\u001a\u00020\u0003X\u0086\u000e\u00a2\u0006\u000e\n\u0000\u001a\u0004\bC\u0010-\"\u0004\bD\u0010\bR\u001a\u0010\u0015\u001a\u00020\u0011X\u0086\u000e\u00a2\u0006\u000e\n\u0000\u001a\u0004\bE\u00106\"\u0004\bF\u00108R\u001a\u0010!\u001a\u00020\u000eX\u0086\u000e\u00a2\u0006\u000e\n\u0000\u001a\u0004\bG\u0010H\"\u0004\bI\u0010JR\u001a\u0010\"\u001a\u00020\u0003X\u0086\u000e\u00a2\u0006\u000e\n\u0000\u001a\u0004\bK\u0010-\"\u0004\bL\u0010\bR\u001a\u0010\u0019\u001a\u00020\u0011X\u0086\u000e\u00a2\u0006\u000e\n\u0000\u001a\u0004\bM\u00106\"\u0004\bN\u00108R\u001a\u0010\u0004\u001a\u00020\u0005X\u0086\u000e\u00a2\u0006\u000e\n\u0000\u001a\u0004\bO\u0010\'\"\u0004\bP\u0010)R\u001a\u0010 \u001a\u00020\u000eX\u0086\u000e\u00a2\u0006\u000e\n\u0000\u001a\u0004\bQ\u0010H\"\u0004\bR\u0010JR\u001a\u0010\u0017\u001a\u00020\u0011X\u0086\u000e\u00a2\u0006\u000e\n\u0000\u001a\u0004\bS\u00106\"\u0004\bT\u00108R\u001a\u0010\r\u001a\u00020\u000eX\u0086\u000e\u00a2\u0006\u000e\n\u0000\u001a\u0004\bU\u0010H\"\u0004\bV\u0010JR\u001a\u0010#\u001a\u00020\u0003X\u0086\u000e\u00a2\u0006\u000e\n\u0000\u001a\u0004\bW\u0010-\"\u0004\bX\u0010\bR\u001a\u0010\u001f\u001a\u00020\u0011X\u0086\u000e\u00a2\u0006\u000e\n\u0000\u001a\u0004\bY\u00106\"\u0004\bZ\u00108R\u001a\u0010$\u001a\u00020\u000eX\u0086\u000e\u00a2\u0006\u000e\n\u0000\u001a\u0004\b[\u0010H\"\u0004\b\\\u0010JR\u001a\u0010\u0018\u001a\u00020\u0011X\u0086\u000e\u00a2\u0006\u000e\n\u0000\u001a\u0004\b]\u00106\"\u0004\b^\u00108\u00a8\u0006\u0083\u0001"}, d2 = {"Lcom/lijianqiang12/silent/data/model/db/AppLimit;", "Landroid/os/Parcelable;", "id", "", "title", "", "appPkg", "(JLjava/lang/String;Ljava/lang/String;)V", "(J)V", "()V", "parcel", "Landroid/os/Parcel;", "(Landroid/os/Parcel;)V", "userId", "", "appLimitIndexId", "ifAllDay", "", "startTime", "endTime", "limitLength", "sunday", "monday", "tuesday", "wednesday", "thursday", "friday", "saturday", "editStartTime", "editEndTime", "editMoney", "valid", "trend", "syncState", "syncTime", "uuid", "version", "(JILjava/lang/String;Ljava/lang/String;ZJJJLjava/lang/String;ZZZZZZZJJJZIIJJI)V", "getAppLimitIndexId", "()Ljava/lang/String;", "setAppLimitIndexId", "(Ljava/lang/String;)V", "getAppPkg", "setAppPkg", "getEditEndTime", "()J", "setEditEndTime", "getEditMoney", "setEditMoney", "getEditStartTime", "setEditStartTime", "getEndTime", "setEndTime", "getFriday", "()Z", "setFriday", "(Z)V", "getId", "setId", "getIfAllDay", "setIfAllDay", "getLimitLength", "setLimitLength", "getMonday", "setMonday", "getSaturday", "setSaturday", "getStartTime", "setStartTime", "getSunday", "setSunday", "getSyncState", "()I", "setSyncState", "(I)V", "getSyncTime", "setSyncTime", "getThursday", "setThursday", "getTitle", "setTitle", "getTrend", "setTrend", "getTuesday", "setTuesday", "getUserId", "setUserId", "getUuid", "setUuid", "getValid", "setValid", "getVersion", "setVersion", "getWednesday", "setWednesday", "component1", "component10", "component11", "component12", "component13", "component14", "component15", "component16", "component17", "component18", "component19", "component2", "component20", "component21", "component22", "component23", "component24", "component25", "component3", "component4", "component5", "component6", "component7", "component8", "component9", "copy", "describeContents", "equals", "other", "", "hashCode", "toString", "writeToParcel", "", "flags", "CREATOR", "data_debug"})
@androidx.room.Entity(indices = {@androidx.room.Index(value = {"appLimitIndexId"}, unique = true)})
public final class AppLimit implements android.os.Parcelable {
    @androidx.room.PrimaryKey(autoGenerate = true)
    private long id;
    private int userId;
    @org.jetbrains.annotations.NotNull()
    private java.lang.String appLimitIndexId;
    @org.jetbrains.annotations.NotNull()
    private java.lang.String appPkg;
    private boolean ifAllDay;
    private long startTime;
    private long endTime;
    private long limitLength;
    @org.jetbrains.annotations.NotNull()
    private java.lang.String title;
    private boolean sunday;
    private boolean monday;
    private boolean tuesday;
    private boolean wednesday;
    private boolean thursday;
    private boolean friday;
    private boolean saturday;
    private long editStartTime;
    private long editEndTime;
    private long editMoney;
    private boolean valid;
    private int trend;
    private int syncState;
    private long syncTime;
    private long uuid;
    private int version;
    @org.jetbrains.annotations.NotNull()
    public static final com.lijianqiang12.silent.data.model.db.AppLimit.CREATOR CREATOR = null;
    
    public AppLimit(long id, int userId, @org.jetbrains.annotations.NotNull()
    java.lang.String appLimitIndexId, @org.jetbrains.annotations.NotNull()
    java.lang.String appPkg, boolean ifAllDay, long startTime, long endTime, long limitLength, @org.jetbrains.annotations.NotNull()
    java.lang.String title, boolean sunday, boolean monday, boolean tuesday, boolean wednesday, boolean thursday, boolean friday, boolean saturday, long editStartTime, long editEndTime, long editMoney, boolean valid, int trend, int syncState, long syncTime, long uuid, int version) {
        super();
    }
    
    public final long getId() {
        return 0L;
    }
    
    public final void setId(long p0) {
    }
    
    public final int getUserId() {
        return 0;
    }
    
    public final void setUserId(int p0) {
    }
    
    @org.jetbrains.annotations.NotNull()
    public final java.lang.String getAppLimitIndexId() {
        return null;
    }
    
    public final void setAppLimitIndexId(@org.jetbrains.annotations.NotNull()
    java.lang.String p0) {
    }
    
    @org.jetbrains.annotations.NotNull()
    public final java.lang.String getAppPkg() {
        return null;
    }
    
    public final void setAppPkg(@org.jetbrains.annotations.NotNull()
    java.lang.String p0) {
    }
    
    public final boolean getIfAllDay() {
        return false;
    }
    
    public final void setIfAllDay(boolean p0) {
    }
    
    public final long getStartTime() {
        return 0L;
    }
    
    public final void setStartTime(long p0) {
    }
    
    public final long getEndTime() {
        return 0L;
    }
    
    public final void setEndTime(long p0) {
    }
    
    public final long getLimitLength() {
        return 0L;
    }
    
    public final void setLimitLength(long p0) {
    }
    
    @org.jetbrains.annotations.NotNull()
    public final java.lang.String getTitle() {
        return null;
    }
    
    public final void setTitle(@org.jetbrains.annotations.NotNull()
    java.lang.String p0) {
    }
    
    public final boolean getSunday() {
        return false;
    }
    
    public final void setSunday(boolean p0) {
    }
    
    public final boolean getMonday() {
        return false;
    }
    
    public final void setMonday(boolean p0) {
    }
    
    public final boolean getTuesday() {
        return false;
    }
    
    public final void setTuesday(boolean p0) {
    }
    
    public final boolean getWednesday() {
        return false;
    }
    
    public final void setWednesday(boolean p0) {
    }
    
    public final boolean getThursday() {
        return false;
    }
    
    public final void setThursday(boolean p0) {
    }
    
    public final boolean getFriday() {
        return false;
    }
    
    public final void setFriday(boolean p0) {
    }
    
    public final boolean getSaturday() {
        return false;
    }
    
    public final void setSaturday(boolean p0) {
    }
    
    public final long getEditStartTime() {
        return 0L;
    }
    
    public final void setEditStartTime(long p0) {
    }
    
    public final long getEditEndTime() {
        return 0L;
    }
    
    public final void setEditEndTime(long p0) {
    }
    
    public final long getEditMoney() {
        return 0L;
    }
    
    public final void setEditMoney(long p0) {
    }
    
    public final boolean getValid() {
        return false;
    }
    
    public final void setValid(boolean p0) {
    }
    
    public final int getTrend() {
        return 0;
    }
    
    public final void setTrend(int p0) {
    }
    
    public final int getSyncState() {
        return 0;
    }
    
    public final void setSyncState(int p0) {
    }
    
    public final long getSyncTime() {
        return 0L;
    }
    
    public final void setSyncTime(long p0) {
    }
    
    public final long getUuid() {
        return 0L;
    }
    
    public final void setUuid(long p0) {
    }
    
    public final int getVersion() {
        return 0;
    }
    
    public final void setVersion(int p0) {
    }
    
    public AppLimit(long id, @org.jetbrains.annotations.NotNull()
    java.lang.String title, @org.jetbrains.annotations.NotNull()
    java.lang.String appPkg) {
        super();
    }
    
    public AppLimit(long id) {
        super();
    }
    
    public AppLimit() {
        super();
    }
    
    public AppLimit(@org.jetbrains.annotations.NotNull()
    android.os.Parcel parcel) {
        super();
    }
    
    @java.lang.Override()
    public void writeToParcel(@org.jetbrains.annotations.NotNull()
    android.os.Parcel parcel, int flags) {
    }
    
    @java.lang.Override()
    public int describeContents() {
        return 0;
    }
    
    @java.lang.Override()
    @org.jetbrains.annotations.NotNull()
    public java.lang.String toString() {
        return null;
    }
    
    public final long component1() {
        return 0L;
    }
    
    public final boolean component10() {
        return false;
    }
    
    public final boolean component11() {
        return false;
    }
    
    public final boolean component12() {
        return false;
    }
    
    public final boolean component13() {
        return false;
    }
    
    public final boolean component14() {
        return false;
    }
    
    public final boolean component15() {
        return false;
    }
    
    public final boolean component16() {
        return false;
    }
    
    public final long component17() {
        return 0L;
    }
    
    public final long component18() {
        return 0L;
    }
    
    public final long component19() {
        return 0L;
    }
    
    public final int component2() {
        return 0;
    }
    
    public final boolean component20() {
        return false;
    }
    
    public final int component21() {
        return 0;
    }
    
    public final int component22() {
        return 0;
    }
    
    public final long component23() {
        return 0L;
    }
    
    public final long component24() {
        return 0L;
    }
    
    public final int component25() {
        return 0;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final java.lang.String component3() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final java.lang.String component4() {
        return null;
    }
    
    public final boolean component5() {
        return false;
    }
    
    public final long component6() {
        return 0L;
    }
    
    public final long component7() {
        return 0L;
    }
    
    public final long component8() {
        return 0L;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final java.lang.String component9() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final com.lijianqiang12.silent.data.model.db.AppLimit copy(long id, int userId, @org.jetbrains.annotations.NotNull()
    java.lang.String appLimitIndexId, @org.jetbrains.annotations.NotNull()
    java.lang.String appPkg, boolean ifAllDay, long startTime, long endTime, long limitLength, @org.jetbrains.annotations.NotNull()
    java.lang.String title, boolean sunday, boolean monday, boolean tuesday, boolean wednesday, boolean thursday, boolean friday, boolean saturday, long editStartTime, long editEndTime, long editMoney, boolean valid, int trend, int syncState, long syncTime, long uuid, int version) {
        return null;
    }
    
    @java.lang.Override()
    public boolean equals(@org.jetbrains.annotations.Nullable()
    java.lang.Object other) {
        return false;
    }
    
    @java.lang.Override()
    public int hashCode() {
        return 0;
    }
    
    @kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000$\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\b\u0003\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\u0011\n\u0000\n\u0002\u0010\b\n\u0002\b\u0002\b\u0086\u0003\u0018\u00002\b\u0012\u0004\u0012\u00020\u00020\u0001B\u0007\b\u0002\u00a2\u0006\u0002\u0010\u0003J\u0010\u0010\u0004\u001a\u00020\u00022\u0006\u0010\u0005\u001a\u00020\u0006H\u0016J\u001d\u0010\u0007\u001a\n\u0012\u0006\u0012\u0004\u0018\u00010\u00020\b2\u0006\u0010\t\u001a\u00020\nH\u0016\u00a2\u0006\u0002\u0010\u000b\u00a8\u0006\f"}, d2 = {"Lcom/lijianqiang12/silent/data/model/db/AppLimit$CREATOR;", "Landroid/os/Parcelable$Creator;", "Lcom/lijianqiang12/silent/data/model/db/AppLimit;", "()V", "createFromParcel", "parcel", "Landroid/os/Parcel;", "newArray", "", "size", "", "(I)[Lcom/lijianqiang12/silent/data/model/db/AppLimit;", "data_debug"})
    public static final class CREATOR implements android.os.Parcelable.Creator<com.lijianqiang12.silent.data.model.db.AppLimit> {
        
        private CREATOR() {
            super();
        }
        
        @java.lang.Override()
        @org.jetbrains.annotations.NotNull()
        public com.lijianqiang12.silent.data.model.db.AppLimit createFromParcel(@org.jetbrains.annotations.NotNull()
        android.os.Parcel parcel) {
            return null;
        }
        
        @java.lang.Override()
        @org.jetbrains.annotations.NotNull()
        public com.lijianqiang12.silent.data.model.db.AppLimit[] newArray(int size) {
            return null;
        }
    }
}