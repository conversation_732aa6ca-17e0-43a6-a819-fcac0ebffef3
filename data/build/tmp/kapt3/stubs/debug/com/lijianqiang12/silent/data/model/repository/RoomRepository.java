package com.lijianqiang12.silent.data.model.repository;

@kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u00006\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0002\u0010 \n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\b\n\u0000\n\u0002\u0010\t\n\u0002\b\u0003\n\u0002\u0018\u0002\n\u0002\b\u0003\u0018\u0000 \u00122\u00020\u0001:\u0001\u0012B\u000f\b\u0002\u0012\u0006\u0010\u0002\u001a\u00020\u0003\u00a2\u0006\u0002\u0010\u0004J2\u0010\u0005\u001a\u000e\u0012\n\u0012\b\u0012\u0004\u0012\u00020\b0\u00070\u00062\u0006\u0010\t\u001a\u00020\n2\u0006\u0010\u000b\u001a\u00020\f2\u0006\u0010\r\u001a\u00020\fH\u0086@\u00a2\u0006\u0002\u0010\u000eJ\u001a\u0010\u000f\u001a\u000e\u0012\n\u0012\b\u0012\u0004\u0012\u00020\u00100\u00070\u0006H\u0086@\u00a2\u0006\u0002\u0010\u0011R\u000e\u0010\u0002\u001a\u00020\u0003X\u0082\u0004\u00a2\u0006\u0002\n\u0000\u00a8\u0006\u0013"}, d2 = {"Lcom/lijianqiang12/silent/data/model/repository/RoomRepository;", "", "appContext", "Landroid/content/Context;", "(Landroid/content/Context;)V", "allRooms", "Lcom/lijianqiang12/silent/data/model/net/pojos/ApiResponse;", "", "Lcom/lijianqiang12/silent/data/model/net/pojos/AllRoom;", "type", "", "lastRoomId", "", "lastLevelCount", "(IJJLkotlin/coroutines/Continuation;)Ljava/lang/Object;", "myJoinedRooms", "Lcom/lijianqiang12/silent/data/model/net/pojos/MyJoinedRoom;", "(Lkotlin/coroutines/Continuation;)Ljava/lang/Object;", "Companion", "data_debug"})
public final class RoomRepository {
    @org.jetbrains.annotations.NotNull()
    private final android.content.Context appContext = null;
    @kotlin.jvm.Volatile()
    @org.jetbrains.annotations.Nullable()
    private static volatile com.lijianqiang12.silent.data.model.repository.RoomRepository instance;
    @org.jetbrains.annotations.NotNull()
    public static final com.lijianqiang12.silent.data.model.repository.RoomRepository.Companion Companion = null;
    
    private RoomRepository(android.content.Context appContext) {
        super();
    }
    
    @org.jetbrains.annotations.Nullable()
    public final java.lang.Object myJoinedRooms(@org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super com.lijianqiang12.silent.data.model.net.pojos.ApiResponse<? extends java.util.List<com.lijianqiang12.silent.data.model.net.pojos.MyJoinedRoom>>> $completion) {
        return null;
    }
    
    @org.jetbrains.annotations.Nullable()
    public final java.lang.Object allRooms(int type, long lastRoomId, long lastLevelCount, @org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super com.lijianqiang12.silent.data.model.net.pojos.ApiResponse<? extends java.util.List<com.lijianqiang12.silent.data.model.net.pojos.AllRoom>>> $completion) {
        return null;
    }
    
    @kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000\u001a\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0000\b\u0086\u0003\u0018\u00002\u00020\u0001B\u0007\b\u0002\u00a2\u0006\u0002\u0010\u0002J\u000e\u0010\u0005\u001a\u00020\u00042\u0006\u0010\u0006\u001a\u00020\u0007R\u0010\u0010\u0003\u001a\u0004\u0018\u00010\u0004X\u0082\u000e\u00a2\u0006\u0002\n\u0000\u00a8\u0006\b"}, d2 = {"Lcom/lijianqiang12/silent/data/model/repository/RoomRepository$Companion;", "", "()V", "instance", "Lcom/lijianqiang12/silent/data/model/repository/RoomRepository;", "getInstance", "appContext", "Landroid/content/Context;", "data_debug"})
    public static final class Companion {
        
        private Companion() {
            super();
        }
        
        @org.jetbrains.annotations.NotNull()
        public final com.lijianqiang12.silent.data.model.repository.RoomRepository getInstance(@org.jetbrains.annotations.NotNull()
        android.content.Context appContext) {
            return null;
        }
    }
}