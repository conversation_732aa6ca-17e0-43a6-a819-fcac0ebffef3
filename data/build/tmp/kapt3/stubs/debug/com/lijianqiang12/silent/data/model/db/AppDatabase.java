package com.lijianqiang12.silent.data.model.db;

@kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000>\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0003\b\'\u0018\u0000 \u00132\u00020\u0001:\u0002\u0013\u0014B\u0005\u00a2\u0006\u0002\u0010\u0002J\b\u0010\u0003\u001a\u00020\u0004H&J\b\u0010\u0005\u001a\u00020\u0006H&J\b\u0010\u0007\u001a\u00020\bH&J\b\u0010\t\u001a\u00020\nH&J\b\u0010\u000b\u001a\u00020\fH&J\b\u0010\r\u001a\u00020\u000eH&J\b\u0010\u000f\u001a\u00020\u0010H&J\b\u0010\u0011\u001a\u00020\u0012H&\u00a8\u0006\u0015"}, d2 = {"Lcom/lijianqiang12/silent/data/model/db/AppDatabase;", "Landroidx/room/RoomDatabase;", "()V", "appLimitDao", "Lcom/lijianqiang12/silent/data/model/db/AppLimitDao;", "appUsageDao", "Lcom/lijianqiang12/silent/data/model/db/AppUsageDao;", "dayLimitDao", "Lcom/lijianqiang12/silent/data/model/db/DayLimitDao;", "fastDao", "Lcom/lijianqiang12/silent/data/model/db/FastDao;", "lockHistoryDao", "Lcom/lijianqiang12/silent/data/model/db/LockHistoryDao;", "scheduleDao", "Lcom/lijianqiang12/silent/data/model/db/ScheduleDao;", "tomatoDao", "Lcom/lijianqiang12/silent/data/model/db/TomatoDao;", "whiteAppDao", "Lcom/lijianqiang12/silent/data/model/db/WhiteAppDao;", "Companion", "MIGRATION_62_63", "data_debug"})
@androidx.room.Database(version = 64, entities = {com.lijianqiang12.silent.data.model.db.Tomato.class, com.lijianqiang12.silent.data.model.db.WhiteApp.class, com.lijianqiang12.silent.data.model.db.LockHistory.class, com.lijianqiang12.silent.data.model.db.Schedule.class, com.lijianqiang12.silent.data.model.db.Fast.class, com.lijianqiang12.silent.data.model.db.DayLimit.class, com.lijianqiang12.silent.data.model.db.AppLimit.class, com.lijianqiang12.silent.data.model.db.AppUsage.class}, autoMigrations = {@androidx.room.AutoMigration(from = 58, to = 59), @androidx.room.AutoMigration(from = 59, to = 60), @androidx.room.AutoMigration(from = 60, to = 61), @androidx.room.AutoMigration(from = 61, to = 62), @androidx.room.AutoMigration(from = 63, to = 64)}, exportSchema = true)
public abstract class AppDatabase extends androidx.room.RoomDatabase {
    @org.jetbrains.annotations.NotNull()
    private static final java.lang.String TAG = "AppDatabase";
    @kotlin.jvm.Volatile()
    @org.jetbrains.annotations.Nullable()
    private static volatile com.lijianqiang12.silent.data.model.db.AppDatabase instance;
    @org.jetbrains.annotations.NotNull()
    public static final com.lijianqiang12.silent.data.model.db.AppDatabase.Companion Companion = null;
    
    public AppDatabase() {
        super();
    }
    
    @org.jetbrains.annotations.NotNull()
    public abstract com.lijianqiang12.silent.data.model.db.FastDao fastDao();
    
    @org.jetbrains.annotations.NotNull()
    public abstract com.lijianqiang12.silent.data.model.db.TomatoDao tomatoDao();
    
    @org.jetbrains.annotations.NotNull()
    public abstract com.lijianqiang12.silent.data.model.db.ScheduleDao scheduleDao();
    
    @org.jetbrains.annotations.NotNull()
    public abstract com.lijianqiang12.silent.data.model.db.LockHistoryDao lockHistoryDao();
    
    @org.jetbrains.annotations.NotNull()
    public abstract com.lijianqiang12.silent.data.model.db.WhiteAppDao whiteAppDao();
    
    @org.jetbrains.annotations.NotNull()
    public abstract com.lijianqiang12.silent.data.model.db.DayLimitDao dayLimitDao();
    
    @org.jetbrains.annotations.NotNull()
    public abstract com.lijianqiang12.silent.data.model.db.AppLimitDao appLimitDao();
    
    @org.jetbrains.annotations.NotNull()
    public abstract com.lijianqiang12.silent.data.model.db.AppUsageDao appUsageDao();
    
    @kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000$\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0002\b\u0002\n\u0002\u0010\u000e\n\u0002\b\u0003\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0002\b\u0002\b\u0086\u0003\u0018\u00002\u00020\u0001B\u0007\b\u0002\u00a2\u0006\u0002\u0010\u0002J\u0010\u0010\t\u001a\u00020\b2\u0006\u0010\n\u001a\u00020\u000bH\u0002J\u000e\u0010\f\u001a\u00020\b2\u0006\u0010\n\u001a\u00020\u000bR\u0014\u0010\u0003\u001a\u00020\u0004X\u0086D\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0005\u0010\u0006R\u0010\u0010\u0007\u001a\u0004\u0018\u00010\bX\u0082\u000e\u00a2\u0006\u0002\n\u0000\u00a8\u0006\r"}, d2 = {"Lcom/lijianqiang12/silent/data/model/db/AppDatabase$Companion;", "", "()V", "TAG", "", "getTAG", "()Ljava/lang/String;", "instance", "Lcom/lijianqiang12/silent/data/model/db/AppDatabase;", "buildDatabase", "context", "Landroid/content/Context;", "getInstance", "data_debug"})
    public static final class Companion {
        
        private Companion() {
            super();
        }
        
        @org.jetbrains.annotations.NotNull()
        public final java.lang.String getTAG() {
            return null;
        }
        
        @org.jetbrains.annotations.NotNull()
        public final com.lijianqiang12.silent.data.model.db.AppDatabase getInstance(@org.jetbrains.annotations.NotNull()
        android.content.Context context) {
            return null;
        }
        
        private final com.lijianqiang12.silent.data.model.db.AppDatabase buildDatabase(android.content.Context context) {
            return null;
        }
    }
    
    @kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000\u0018\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0010\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\b\u00c6\u0002\u0018\u00002\u00020\u0001B\u0007\b\u0002\u00a2\u0006\u0002\u0010\u0002J\u0010\u0010\u0003\u001a\u00020\u00042\u0006\u0010\u0005\u001a\u00020\u0006H\u0016\u00a8\u0006\u0007"}, d2 = {"Lcom/lijianqiang12/silent/data/model/db/AppDatabase$MIGRATION_62_63;", "Landroidx/room/migration/Migration;", "()V", "migrate", "", "db", "Landroidx/sqlite/db/SupportSQLiteDatabase;", "data_debug"})
    public static final class MIGRATION_62_63 extends androidx.room.migration.Migration {
        @org.jetbrains.annotations.NotNull()
        public static final com.lijianqiang12.silent.data.model.db.AppDatabase.MIGRATION_62_63 INSTANCE = null;
        
        private MIGRATION_62_63() {
            super(0, 0);
        }
        
        @java.lang.Override()
        public void migrate(@org.jetbrains.annotations.NotNull()
        androidx.sqlite.db.SupportSQLiteDatabase db) {
        }
    }
}