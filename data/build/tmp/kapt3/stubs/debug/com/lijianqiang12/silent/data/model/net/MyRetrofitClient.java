package com.lijianqiang12.silent.data.model.net;

@kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000 \n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0002\b\u0005\n\u0002\u0010\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\b\u00c6\u0002\u0018\u00002\u00020\u0001B\u0007\b\u0002\u00a2\u0006\u0002\u0010\u0002J\u0010\u0010\t\u001a\u00020\n2\u0006\u0010\u000b\u001a\u00020\fH\u0016R\u001b\u0010\u0003\u001a\u00020\u00048FX\u0086\u0084\u0002\u00a2\u0006\f\n\u0004\b\u0007\u0010\b\u001a\u0004\b\u0005\u0010\u0006\u00a8\u0006\r"}, d2 = {"Lcom/lijianqiang12/silent/data/model/net/MyRetrofitClient;", "Lcom/lijianqiang12/silent/data/model/net/BaseRetrofitClient;", "()V", "service", "Lcom/lijianqiang12/silent/data/model/net/api/Api;", "getService", "()Lcom/lijianqiang12/silent/data/model/net/api/Api;", "service$delegate", "Lkotlin/Lazy;", "handleBuilder", "", "builder", "Lokhttp3/OkHttpClient$Builder;", "data_debug"})
public final class MyRetrofitClient extends com.lijianqiang12.silent.data.model.net.BaseRetrofitClient {
    @org.jetbrains.annotations.NotNull()
    private static final kotlin.Lazy service$delegate = null;
    @org.jetbrains.annotations.NotNull()
    public static final com.lijianqiang12.silent.data.model.net.MyRetrofitClient INSTANCE = null;
    
    private MyRetrofitClient() {
        super();
    }
    
    @org.jetbrains.annotations.NotNull()
    public final com.lijianqiang12.silent.data.model.net.api.Api getService() {
        return null;
    }
    
    @java.lang.Override()
    public void handleBuilder(@org.jetbrains.annotations.NotNull()
    okhttp3.OkHttpClient.Builder builder) {
    }
}