package com.lijianqiang12.silent.data.model.net;

@kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000\u0014\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0000\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0000\b6\u0018\u00002\u00020\u0001B\u0007\b\u0004\u00a2\u0006\u0002\u0010\u0002\u0082\u0001\u0002\u0003\u0004\u00a8\u0006\u0005"}, d2 = {"Lcom/lijianqiang12/silent/data/model/net/HttpResponse;", "", "()V", "Lcom/lijianqiang12/silent/data/model/net/Failure;", "Lcom/lijianqiang12/silent/data/model/net/Success;", "data_debug"})
public abstract class HttpResponse {
    
    private HttpResponse() {
        super();
    }
}