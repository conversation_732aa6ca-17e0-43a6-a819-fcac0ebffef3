package com.lijianqiang12.silent.data.model.db;

@kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u00000\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0000\n\u0002\u0010\u0002\n\u0000\n\u0002\u0010\b\n\u0002\b\u0003\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0010!\n\u0002\b\u0004\n\u0002\u0018\u0002\n\u0002\b\b\bg\u0018\u00002\u00020\u0001J\u0016\u0010\u0002\u001a\u00020\u00032\u0006\u0010\u0004\u001a\u00020\u0005H\u00a7@\u00a2\u0006\u0002\u0010\u0006J\u0016\u0010\u0007\u001a\u00020\u00032\u0006\u0010\b\u001a\u00020\tH\u00a7@\u00a2\u0006\u0002\u0010\nJ\u001c\u0010\u000b\u001a\b\u0012\u0004\u0012\u00020\t0\f2\u0006\u0010\u0004\u001a\u00020\u0005H\u00a7@\u00a2\u0006\u0002\u0010\u0006J$\u0010\r\u001a\b\u0012\u0004\u0012\u00020\t0\f2\u0006\u0010\u0004\u001a\u00020\u00052\u0006\u0010\u000e\u001a\u00020\u0005H\u00a7@\u00a2\u0006\u0002\u0010\u000fJ\u001c\u0010\u0010\u001a\u000e\u0012\n\u0012\b\u0012\u0004\u0012\u00020\t0\f0\u00112\u0006\u0010\u0004\u001a\u00020\u0005H\'J\u0018\u0010\u0012\u001a\u0004\u0018\u00010\t2\u0006\u0010\u0004\u001a\u00020\u0005H\u00a7@\u00a2\u0006\u0002\u0010\u0006J\u0016\u0010\u0013\u001a\u00020\u00032\u0006\u0010\u0014\u001a\u00020\tH\u00a7@\u00a2\u0006\u0002\u0010\nJ\u0016\u0010\u0015\u001a\u00020\u00032\u0006\u0010\u0016\u001a\u00020\tH\u00a7@\u00a2\u0006\u0002\u0010\nJ\u0010\u0010\u0017\u001a\u00020\u00032\u0006\u0010\u0018\u001a\u00020\u0005H\'\u00a8\u0006\u0019"}, d2 = {"Lcom/lijianqiang12/silent/data/model/db/AppLimitDao;", "", "deleteAll", "", "userId", "", "(ILkotlin/coroutines/Continuation;)Ljava/lang/Object;", "deleteAppLimit", "appLimit", "Lcom/lijianqiang12/silent/data/model/db/AppLimit;", "(Lcom/lijianqiang12/silent/data/model/db/AppLimit;Lkotlin/coroutines/Continuation;)Ljava/lang/Object;", "getAllAppLimitList", "", "getAppLimitWithState", "state", "(IILkotlin/coroutines/Continuation;)Ljava/lang/Object;", "getAppLimits", "Landroidx/lifecycle/LiveData;", "getLastAppLimit", "insertAppLimit", "appLimits", "updateAppLimit", "allLimit", "updateUserId", "newUserId", "data_debug"})
@androidx.room.Dao()
public abstract interface AppLimitDao {
    
    @androidx.room.Query(value = "select * from AppLimit where userId = :userId order by trend desc limit 1")
    @org.jetbrains.annotations.Nullable()
    public abstract java.lang.Object getLastAppLimit(int userId, @org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super com.lijianqiang12.silent.data.model.db.AppLimit> $completion);
    
    @androidx.room.Query(value = "select * From AppLimit Where userId = :userId and syncState = :state order by trend")
    @org.jetbrains.annotations.Nullable()
    public abstract java.lang.Object getAppLimitWithState(int userId, int state, @org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super java.util.List<com.lijianqiang12.silent.data.model.db.AppLimit>> $completion);
    
    @androidx.room.Query(value = "select * From AppLimit where userId = :userId order by trend")
    @org.jetbrains.annotations.Nullable()
    public abstract java.lang.Object getAllAppLimitList(int userId, @org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super java.util.List<com.lijianqiang12.silent.data.model.db.AppLimit>> $completion);
    
    @androidx.room.Query(value = "SELECT * FROM AppLimit where userId = :userId ORDER BY trend")
    @org.jetbrains.annotations.NotNull()
    public abstract androidx.lifecycle.LiveData<java.util.List<com.lijianqiang12.silent.data.model.db.AppLimit>> getAppLimits(int userId);
    
    @androidx.room.Insert(onConflict = 5)
    @org.jetbrains.annotations.Nullable()
    public abstract java.lang.Object insertAppLimit(@org.jetbrains.annotations.NotNull()
    com.lijianqiang12.silent.data.model.db.AppLimit appLimits, @org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super kotlin.Unit> $completion);
    
    @androidx.room.Update()
    @org.jetbrains.annotations.Nullable()
    public abstract java.lang.Object updateAppLimit(@org.jetbrains.annotations.NotNull()
    com.lijianqiang12.silent.data.model.db.AppLimit allLimit, @org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super kotlin.Unit> $completion);
    
    @androidx.room.Delete()
    @org.jetbrains.annotations.Nullable()
    public abstract java.lang.Object deleteAppLimit(@org.jetbrains.annotations.NotNull()
    com.lijianqiang12.silent.data.model.db.AppLimit appLimit, @org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super kotlin.Unit> $completion);
    
    @androidx.room.Query(value = "delete from AppLimit where userId = :userId")
    @org.jetbrains.annotations.Nullable()
    public abstract java.lang.Object deleteAll(int userId, @org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super kotlin.Unit> $completion);
    
    @androidx.room.Query(value = "UPDATE AppLimit SET userId = :newUserId WHERE userId = -1")
    public abstract void updateUserId(int newUserId);
}