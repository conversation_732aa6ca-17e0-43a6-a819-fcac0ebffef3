package com.lijianqiang12.silent.data.model.db;

@kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000*\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0002\b\u0002\n\u0002\u0010\t\n\u0000\n\u0002\u0010\b\n\u0002\b\u0002\n\u0002\u0010\u000b\n\u0002\b\u0002\n\u0002\u0010\u000e\n\u0002\bA\b\u0087\b\u0018\u00002\u00020\u0001B\u0007\b\u0016\u00a2\u0006\u0002\u0010\u0002B\u0097\u0001\u0012\u0006\u0010\u0003\u001a\u00020\u0004\u0012\u0006\u0010\u0005\u001a\u00020\u0006\u0012\b\b\u0002\u0010\u0007\u001a\u00020\u0004\u0012\b\b\u0002\u0010\b\u001a\u00020\t\u0012\b\b\u0002\u0010\n\u001a\u00020\t\u0012\b\b\u0002\u0010\u000b\u001a\u00020\f\u0012\b\b\u0002\u0010\r\u001a\u00020\u0006\u0012\b\b\u0002\u0010\u000e\u001a\u00020\t\u0012\b\b\u0002\u0010\u000f\u001a\u00020\t\u0012\b\b\u0002\u0010\u0010\u001a\u00020\t\u0012\b\b\u0002\u0010\u0011\u001a\u00020\t\u0012\b\b\u0002\u0010\u0012\u001a\u00020\t\u0012\b\b\u0002\u0010\u0013\u001a\u00020\t\u0012\b\b\u0002\u0010\u0014\u001a\u00020\t\u0012\b\b\u0002\u0010\u0015\u001a\u00020\t\u00a2\u0006\u0002\u0010\u0016J\t\u00109\u001a\u00020\u0004H\u00c6\u0003J\t\u0010:\u001a\u00020\tH\u00c6\u0003J\t\u0010;\u001a\u00020\tH\u00c6\u0003J\t\u0010<\u001a\u00020\tH\u00c6\u0003J\t\u0010=\u001a\u00020\tH\u00c6\u0003J\t\u0010>\u001a\u00020\tH\u00c6\u0003J\t\u0010?\u001a\u00020\tH\u00c6\u0003J\t\u0010@\u001a\u00020\u0006H\u00c6\u0003J\t\u0010A\u001a\u00020\u0004H\u00c6\u0003J\t\u0010B\u001a\u00020\tH\u00c6\u0003J\t\u0010C\u001a\u00020\tH\u00c6\u0003J\t\u0010D\u001a\u00020\fH\u00c6\u0003J\t\u0010E\u001a\u00020\u0006H\u00c6\u0003J\t\u0010F\u001a\u00020\tH\u00c6\u0003J\t\u0010G\u001a\u00020\tH\u00c6\u0003J\u0006\u0010H\u001a\u00020\u0000J\u009f\u0001\u0010H\u001a\u00020\u00002\b\b\u0002\u0010\u0003\u001a\u00020\u00042\b\b\u0002\u0010\u0005\u001a\u00020\u00062\b\b\u0002\u0010\u0007\u001a\u00020\u00042\b\b\u0002\u0010\b\u001a\u00020\t2\b\b\u0002\u0010\n\u001a\u00020\t2\b\b\u0002\u0010\u000b\u001a\u00020\f2\b\b\u0002\u0010\r\u001a\u00020\u00062\b\b\u0002\u0010\u000e\u001a\u00020\t2\b\b\u0002\u0010\u000f\u001a\u00020\t2\b\b\u0002\u0010\u0010\u001a\u00020\t2\b\b\u0002\u0010\u0011\u001a\u00020\t2\b\b\u0002\u0010\u0012\u001a\u00020\t2\b\b\u0002\u0010\u0013\u001a\u00020\t2\b\b\u0002\u0010\u0014\u001a\u00020\t2\b\b\u0002\u0010\u0015\u001a\u00020\tH\u00c6\u0001J\u0013\u0010I\u001a\u00020\t2\b\u0010J\u001a\u0004\u0018\u00010\u0001H\u00d6\u0003J\t\u0010K\u001a\u00020\u0006H\u00d6\u0001J\t\u0010L\u001a\u00020\fH\u00d6\u0001R\u001a\u0010\u0007\u001a\u00020\u0004X\u0086\u000e\u00a2\u0006\u000e\n\u0000\u001a\u0004\b\u0017\u0010\u0018\"\u0004\b\u0019\u0010\u001aR\u001e\u0010\r\u001a\u00020\u00068\u0006@\u0006X\u0087\u000e\u00a2\u0006\u000e\n\u0000\u001a\u0004\b\u001b\u0010\u001c\"\u0004\b\u001d\u0010\u001eR\u001e\u0010\u0013\u001a\u00020\t8\u0006@\u0006X\u0087\u000e\u00a2\u0006\u000e\n\u0000\u001a\u0004\b\u001f\u0010 \"\u0004\b!\u0010\"R\u0016\u0010\u0003\u001a\u00020\u00048\u0006X\u0087\u0004\u00a2\u0006\b\n\u0000\u001a\u0004\b#\u0010\u0018R\u001a\u0010\n\u001a\u00020\tX\u0086\u000e\u00a2\u0006\u000e\n\u0000\u001a\u0004\b\n\u0010 \"\u0004\b$\u0010\"R\u001a\u0010\b\u001a\u00020\tX\u0086\u000e\u00a2\u0006\u000e\n\u0000\u001a\u0004\b\b\u0010 \"\u0004\b%\u0010\"R\u001e\u0010\u000e\u001a\u00020\t8\u0006@\u0006X\u0087\u000e\u00a2\u0006\u000e\n\u0000\u001a\u0004\b\u000e\u0010 \"\u0004\b&\u0010\"R\u001a\u0010\u000b\u001a\u00020\fX\u0086\u000e\u00a2\u0006\u000e\n\u0000\u001a\u0004\b\'\u0010(\"\u0004\b)\u0010*R\u001e\u0010\u000f\u001a\u00020\t8\u0006@\u0006X\u0087\u000e\u00a2\u0006\u000e\n\u0000\u001a\u0004\b+\u0010 \"\u0004\b,\u0010\"R\u001e\u0010\u0014\u001a\u00020\t8\u0006@\u0006X\u0087\u000e\u00a2\u0006\u000e\n\u0000\u001a\u0004\b-\u0010 \"\u0004\b.\u0010\"R\u001e\u0010\u0015\u001a\u00020\t8\u0006@\u0006X\u0087\u000e\u00a2\u0006\u000e\n\u0000\u001a\u0004\b/\u0010 \"\u0004\b0\u0010\"R\u001e\u0010\u0012\u001a\u00020\t8\u0006@\u0006X\u0087\u000e\u00a2\u0006\u000e\n\u0000\u001a\u0004\b1\u0010 \"\u0004\b2\u0010\"R\u001e\u0010\u0010\u001a\u00020\t8\u0006@\u0006X\u0087\u000e\u00a2\u0006\u000e\n\u0000\u001a\u0004\b3\u0010 \"\u0004\b4\u0010\"R\u001a\u0010\u0005\u001a\u00020\u0006X\u0086\u000e\u00a2\u0006\u000e\n\u0000\u001a\u0004\b5\u0010\u001c\"\u0004\b6\u0010\u001eR\u001e\u0010\u0011\u001a\u00020\t8\u0006@\u0006X\u0087\u000e\u00a2\u0006\u000e\n\u0000\u001a\u0004\b7\u0010 \"\u0004\b8\u0010\"\u00a8\u0006M"}, d2 = {"Lcom/lijianqiang12/silent/data/model/db/DayLimit;", "", "()V", "id", "", "userId", "", "allDayLimit", "isIncludeWhite", "", "isDenyChange", "jumpDate", "", "denyChangeLength", "isWorkDayLimit", "monday", "tuesday", "wednesday", "thursday", "friday", "saturday", "sunday", "(JIJZZLjava/lang/String;IZZZZZZZZ)V", "getAllDayLimit", "()J", "setAllDayLimit", "(J)V", "getDenyChangeLength", "()I", "setDenyChangeLength", "(I)V", "getFriday", "()Z", "setFriday", "(Z)V", "getId", "setDenyChange", "setIncludeWhite", "setWorkDayLimit", "getJumpDate", "()Ljava/lang/String;", "setJumpDate", "(Ljava/lang/String;)V", "getMonday", "setMonday", "getSaturday", "setSaturday", "getSunday", "setSunday", "getThursday", "setThursday", "getTuesday", "setTuesday", "getUserId", "setUserId", "getWednesday", "setWednesday", "component1", "component10", "component11", "component12", "component13", "component14", "component15", "component2", "component3", "component4", "component5", "component6", "component7", "component8", "component9", "copy", "equals", "other", "hashCode", "toString", "data_debug"})
@androidx.room.Entity()
public final class DayLimit {
    @androidx.room.PrimaryKey(autoGenerate = true)
    private final long id = 0L;
    private int userId;
    private long allDayLimit;
    private boolean isIncludeWhite;
    private boolean isDenyChange;
    @org.jetbrains.annotations.NotNull()
    private java.lang.String jumpDate;
    @androidx.room.ColumnInfo(defaultValue = "20")
    private int denyChangeLength;
    @androidx.room.ColumnInfo(defaultValue = "true")
    private boolean isWorkDayLimit;
    @androidx.room.ColumnInfo(defaultValue = "true")
    private boolean monday;
    @androidx.room.ColumnInfo(defaultValue = "true")
    private boolean tuesday;
    @androidx.room.ColumnInfo(defaultValue = "true")
    private boolean wednesday;
    @androidx.room.ColumnInfo(defaultValue = "true")
    private boolean thursday;
    @androidx.room.ColumnInfo(defaultValue = "true")
    private boolean friday;
    @androidx.room.ColumnInfo(defaultValue = "false")
    private boolean saturday;
    @androidx.room.ColumnInfo(defaultValue = "false")
    private boolean sunday;
    
    public DayLimit(long id, int userId, long allDayLimit, boolean isIncludeWhite, boolean isDenyChange, @org.jetbrains.annotations.NotNull()
    java.lang.String jumpDate, int denyChangeLength, boolean isWorkDayLimit, boolean monday, boolean tuesday, boolean wednesday, boolean thursday, boolean friday, boolean saturday, boolean sunday) {
        super();
    }
    
    public final long getId() {
        return 0L;
    }
    
    public final int getUserId() {
        return 0;
    }
    
    public final void setUserId(int p0) {
    }
    
    public final long getAllDayLimit() {
        return 0L;
    }
    
    public final void setAllDayLimit(long p0) {
    }
    
    public final boolean isIncludeWhite() {
        return false;
    }
    
    public final void setIncludeWhite(boolean p0) {
    }
    
    public final boolean isDenyChange() {
        return false;
    }
    
    public final void setDenyChange(boolean p0) {
    }
    
    @org.jetbrains.annotations.NotNull()
    public final java.lang.String getJumpDate() {
        return null;
    }
    
    public final void setJumpDate(@org.jetbrains.annotations.NotNull()
    java.lang.String p0) {
    }
    
    public final int getDenyChangeLength() {
        return 0;
    }
    
    public final void setDenyChangeLength(int p0) {
    }
    
    public final boolean isWorkDayLimit() {
        return false;
    }
    
    public final void setWorkDayLimit(boolean p0) {
    }
    
    public final boolean getMonday() {
        return false;
    }
    
    public final void setMonday(boolean p0) {
    }
    
    public final boolean getTuesday() {
        return false;
    }
    
    public final void setTuesday(boolean p0) {
    }
    
    public final boolean getWednesday() {
        return false;
    }
    
    public final void setWednesday(boolean p0) {
    }
    
    public final boolean getThursday() {
        return false;
    }
    
    public final void setThursday(boolean p0) {
    }
    
    public final boolean getFriday() {
        return false;
    }
    
    public final void setFriday(boolean p0) {
    }
    
    public final boolean getSaturday() {
        return false;
    }
    
    public final void setSaturday(boolean p0) {
    }
    
    public final boolean getSunday() {
        return false;
    }
    
    public final void setSunday(boolean p0) {
    }
    
    public DayLimit() {
        super();
    }
    
    @org.jetbrains.annotations.NotNull()
    public final com.lijianqiang12.silent.data.model.db.DayLimit copy() {
        return null;
    }
    
    public final long component1() {
        return 0L;
    }
    
    public final boolean component10() {
        return false;
    }
    
    public final boolean component11() {
        return false;
    }
    
    public final boolean component12() {
        return false;
    }
    
    public final boolean component13() {
        return false;
    }
    
    public final boolean component14() {
        return false;
    }
    
    public final boolean component15() {
        return false;
    }
    
    public final int component2() {
        return 0;
    }
    
    public final long component3() {
        return 0L;
    }
    
    public final boolean component4() {
        return false;
    }
    
    public final boolean component5() {
        return false;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final java.lang.String component6() {
        return null;
    }
    
    public final int component7() {
        return 0;
    }
    
    public final boolean component8() {
        return false;
    }
    
    public final boolean component9() {
        return false;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final com.lijianqiang12.silent.data.model.db.DayLimit copy(long id, int userId, long allDayLimit, boolean isIncludeWhite, boolean isDenyChange, @org.jetbrains.annotations.NotNull()
    java.lang.String jumpDate, int denyChangeLength, boolean isWorkDayLimit, boolean monday, boolean tuesday, boolean wednesday, boolean thursday, boolean friday, boolean saturday, boolean sunday) {
        return null;
    }
    
    @java.lang.Override()
    public boolean equals(@org.jetbrains.annotations.Nullable()
    java.lang.Object other) {
        return false;
    }
    
    @java.lang.Override()
    public int hashCode() {
        return 0;
    }
    
    @java.lang.Override()
    @org.jetbrains.annotations.NotNull()
    public java.lang.String toString() {
        return null;
    }
}