package com.lijianqiang12.silent.data.model.repository;

@kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000L\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0010\u000e\n\u0000\n\u0002\u0010!\n\u0002\b\u0006\n\u0002\u0010 \n\u0002\b\u0003\n\u0002\u0010\t\n\u0002\b\u000f\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0010\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0003\u0018\u0000 ,2\u00020\u0001:\u0001,B\u0017\b\u0002\u0012\u0006\u0010\u0002\u001a\u00020\u0003\u0012\u0006\u0010\u0004\u001a\u00020\u0005\u00a2\u0006\u0002\u0010\u0006J\u0014\u0010\t\u001a\b\u0012\u0004\u0012\u00020\u000b0\nH\u0086@\u00a2\u0006\u0002\u0010\fJ\u0014\u0010\r\u001a\b\u0012\u0004\u0012\u00020\u000b0\nH\u0086@\u00a2\u0006\u0002\u0010\fJ#\u0010\u000e\u001a\u00020\u000b2\u0006\u0010\u000f\u001a\u00020\u000b2\f\u0010\u0010\u001a\b\u0012\u0004\u0012\u00020\u000b0\u0011H\u0002\u00a2\u0006\u0002\u0010\u0012J$\u0010\u0013\u001a\b\u0012\u0004\u0012\u00020\u000b0\n2\u0006\u0010\u0014\u001a\u00020\u00152\u0006\u0010\u0016\u001a\u00020\u0015H\u0082@\u00a2\u0006\u0002\u0010\u0017J$\u0010\u0018\u001a\b\u0012\u0004\u0012\u00020\u000b0\u00112\u0006\u0010\u0014\u001a\u00020\u00152\u0006\u0010\u0016\u001a\u00020\u0015H\u0082@\u00a2\u0006\u0002\u0010\u0017J,\u0010\u0019\u001a\u00020\u00152\f\u0010\u001a\u001a\b\u0012\u0004\u0012\u00020\b0\n2\u0006\u0010\u001b\u001a\u00020\u00152\u0006\u0010\u001c\u001a\u00020\u0015H\u0086@\u00a2\u0006\u0002\u0010\u001dJ\u0014\u0010\u001e\u001a\b\u0012\u0004\u0012\u00020\u000b0\nH\u0086@\u00a2\u0006\u0002\u0010\fJ\u000e\u0010\u001f\u001a\u00020\u0015H\u0086@\u00a2\u0006\u0002\u0010\fJ&\u0010 \u001a\u00020\u00152\u0006\u0010!\u001a\u00020\b2\u0006\u0010\u0014\u001a\u00020\u00152\u0006\u0010\u0016\u001a\u00020\u0015H\u0086@\u00a2\u0006\u0002\u0010\"J\u001c\u0010#\u001a\u00020\u00152\f\u0010$\u001a\b\u0012\u0004\u0012\u00020%0\nH\u0086@\u00a2\u0006\u0002\u0010&J\u0016\u0010\'\u001a\u00020(2\u0006\u0010)\u001a\u00020*H\u0086@\u00a2\u0006\u0002\u0010+R\u000e\u0010\u0007\u001a\u00020\bX\u0082D\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0002\u001a\u00020\u0003X\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0004\u001a\u00020\u0005X\u0082\u0004\u00a2\u0006\u0002\n\u0000\u00a8\u0006-"}, d2 = {"Lcom/lijianqiang12/silent/data/model/repository/UsageRepository;", "", "appContext", "Landroid/content/Context;", "appUsageDao", "Lcom/lijianqiang12/silent/data/model/db/AppUsageDao;", "(Landroid/content/Context;Lcom/lijianqiang12/silent/data/model/db/AppUsageDao;)V", "TAG", "", "get7DaysAppUsageTimeRank", "", "error/NonExistentClass", "(Lkotlin/coroutines/Continuation;)Ljava/lang/Object;", "getAppAnalyze", "getInAppTimeList", "appTime", "appTimeList", "", "(Lerror/NonExistentClass;Ljava/util/List;)Lerror/NonExistentClass;", "getPeriodAppUsageTimeRank", "startTime", "", "endTime", "(JJLkotlin/coroutines/Continuation;)Ljava/lang/Object;", "getPeriodUsageList", "getPkgListUsageLength", "pkgList", "startTimeInSecond", "endTimeInSecond", "(Ljava/util/List;JJLkotlin/coroutines/Continuation;)Ljava/lang/Object;", "getTodayAppUsageTimeRank", "getTodayUsageLength", "getTodayUsageLengthFromTrueTime", "pkg", "(Ljava/lang/String;JJLkotlin/coroutines/Continuation;)Ljava/lang/Object;", "getTodayUsageLengthWithoutWhite", "whiteList", "Lcom/lijianqiang12/silent/data/model/db/WhiteApp;", "(Ljava/util/List;Lkotlin/coroutines/Continuation;)Ljava/lang/Object;", "insertAppUsage", "", "appUsage", "Lcom/lijianqiang12/silent/data/model/db/AppUsage;", "(Lcom/lijianqiang12/silent/data/model/db/AppUsage;Lkotlin/coroutines/Continuation;)Ljava/lang/Object;", "Companion", "data_debug"})
public final class UsageRepository {
    @org.jetbrains.annotations.NotNull()
    private final android.content.Context appContext = null;
    @org.jetbrains.annotations.NotNull()
    private final com.lijianqiang12.silent.data.model.db.AppUsageDao appUsageDao = null;
    @org.jetbrains.annotations.NotNull()
    private final java.lang.String TAG = "UsageRepository";
    @kotlin.jvm.Volatile()
    @org.jetbrains.annotations.Nullable()
    private static volatile com.lijianqiang12.silent.data.model.repository.UsageRepository instance;
    @org.jetbrains.annotations.NotNull()
    public static final com.lijianqiang12.silent.data.model.repository.UsageRepository.Companion Companion = null;
    
    private UsageRepository(android.content.Context appContext, com.lijianqiang12.silent.data.model.db.AppUsageDao appUsageDao) {
        super();
    }
    
    @org.jetbrains.annotations.Nullable()
    public final java.lang.Object insertAppUsage(@org.jetbrains.annotations.NotNull()
    com.lijianqiang12.silent.data.model.db.AppUsage appUsage, @org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super kotlin.Unit> $completion) {
        return null;
    }
    
    /**
     * 今天的全部使用时长 s
     */
    @org.jetbrains.annotations.Nullable()
    public final java.lang.Object getTodayUsageLength(@org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super java.lang.Long> $completion) {
        return null;
    }
    
    /**
     * 今天的全部使用时长，除去白名单app s
     */
    @org.jetbrains.annotations.Nullable()
    public final java.lang.Object getTodayUsageLengthWithoutWhite(@org.jetbrains.annotations.NotNull()
    java.util.List<com.lijianqiang12.silent.data.model.db.WhiteApp> whiteList, @org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super java.lang.Long> $completion) {
        return null;
    }
    
    /**
     * 应用们在一段时间内的使用时长 s
     */
    @org.jetbrains.annotations.Nullable()
    public final java.lang.Object getPkgListUsageLength(@org.jetbrains.annotations.NotNull()
    java.util.List<java.lang.String> pkgList, long startTimeInSecond, long endTimeInSecond, @org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super java.lang.Long> $completion) {
        return null;
    }
    
    /**
     * 应用在今天一段时间内的使用时长 s
     */
    @org.jetbrains.annotations.Nullable()
    public final java.lang.Object getTodayUsageLengthFromTrueTime(@org.jetbrains.annotations.NotNull()
    java.lang.String pkg, long startTime, long endTime, @org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super java.lang.Long> $completion) {
        return null;
    }
    
    /**
     * 一段时间内的app使用情况列表，最长能获取一周
     */
    private final java.lang.Object getPeriodUsageList(long startTime, long endTime, kotlin.coroutines.Continuation<? super java.util.List<error.NonExistentClass>> $completion) {
        return null;
    }
    
    /**
     * 一段时间内app使用时长排名(ms)
     */
    private final java.lang.Object getPeriodAppUsageTimeRank(long startTime, long endTime, kotlin.coroutines.Continuation<? super java.util.List<error.NonExistentClass>> $completion) {
        return null;
    }
    
    /**
     * 今天app使用时长排名
     */
    @org.jetbrains.annotations.Nullable()
    public final java.lang.Object getTodayAppUsageTimeRank(@org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super java.util.List<error.NonExistentClass>> $completion) {
        return null;
    }
    
    /**
     * 最近7天app使用时长排名
     */
    @org.jetbrains.annotations.Nullable()
    public final java.lang.Object get7DaysAppUsageTimeRank(@org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super java.util.List<error.NonExistentClass>> $completion) {
        return null;
    }
    
    /**
     * app使用分析3-7天
     */
    @org.jetbrains.annotations.Nullable()
    public final java.lang.Object getAppAnalyze(@org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super java.util.List<error.NonExistentClass>> $completion) {
        return null;
    }
    
    private final error.NonExistentClass getInAppTimeList(error.NonExistentClass appTime, java.util.List<error.NonExistentClass> appTimeList) {
        return null;
    }
    
    @kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000 \n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\b\u0086\u0003\u0018\u00002\u00020\u0001B\u0007\b\u0002\u00a2\u0006\u0002\u0010\u0002J\u0016\u0010\u0005\u001a\u00020\u00042\u0006\u0010\u0006\u001a\u00020\u00072\u0006\u0010\b\u001a\u00020\tR\u0010\u0010\u0003\u001a\u0004\u0018\u00010\u0004X\u0082\u000e\u00a2\u0006\u0002\n\u0000\u00a8\u0006\n"}, d2 = {"Lcom/lijianqiang12/silent/data/model/repository/UsageRepository$Companion;", "", "()V", "instance", "Lcom/lijianqiang12/silent/data/model/repository/UsageRepository;", "getInstance", "appContext", "Landroid/content/Context;", "appUsageDao", "Lcom/lijianqiang12/silent/data/model/db/AppUsageDao;", "data_debug"})
    public static final class Companion {
        
        private Companion() {
            super();
        }
        
        @org.jetbrains.annotations.NotNull()
        public final com.lijianqiang12.silent.data.model.repository.UsageRepository getInstance(@org.jetbrains.annotations.NotNull()
        android.content.Context appContext, @org.jetbrains.annotations.NotNull()
        com.lijianqiang12.silent.data.model.db.AppUsageDao appUsageDao) {
            return null;
        }
    }
}