package com.lijianqiang12.silent.data.model.db;

@kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000*\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0000\n\u0002\u0010\u0002\n\u0000\n\u0002\u0010\b\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\u000b\n\u0002\b\u0015\bg\u0018\u00002\u00020\u0001J\u0016\u0010\u0002\u001a\u00020\u00032\u0006\u0010\u0004\u001a\u00020\u0005H\u00a7@\u00a2\u0006\u0002\u0010\u0006J \u0010\u0007\u001a\n\u0012\u0006\u0012\u0004\u0018\u00010\t0\b2\u0006\u0010\u0004\u001a\u00020\u00052\u0006\u0010\n\u001a\u00020\u000bH\'J\u0018\u0010\f\u001a\n\u0012\u0006\u0012\u0004\u0018\u00010\t0\b2\u0006\u0010\u0004\u001a\u00020\u0005H\'J\u0012\u0010\r\u001a\u0004\u0018\u00010\t2\u0006\u0010\u0004\u001a\u00020\u0005H\'J\u0012\u0010\u000e\u001a\u0004\u0018\u00010\t2\u0006\u0010\u0004\u001a\u00020\u0005H\'J\u0012\u0010\u000f\u001a\u0004\u0018\u00010\t2\u0006\u0010\u0004\u001a\u00020\u0005H\'J\u0012\u0010\u0010\u001a\u0004\u0018\u00010\t2\u0006\u0010\u0004\u001a\u00020\u0005H\'J\u0012\u0010\u0011\u001a\u0004\u0018\u00010\t2\u0006\u0010\u0004\u001a\u00020\u0005H\'J\u0012\u0010\u0012\u001a\u0004\u0018\u00010\t2\u0006\u0010\u0004\u001a\u00020\u0005H\'J\u0012\u0010\u0013\u001a\u0004\u0018\u00010\t2\u0006\u0010\u0004\u001a\u00020\u0005H\'J\u0018\u0010\u0014\u001a\n\u0012\u0006\u0012\u0004\u0018\u00010\t0\b2\u0006\u0010\u0004\u001a\u00020\u0005H\'J\u0018\u0010\u0015\u001a\n\u0012\u0006\u0012\u0004\u0018\u00010\t0\b2\u0006\u0010\u0004\u001a\u00020\u0005H\'J\u0018\u0010\u0016\u001a\n\u0012\u0006\u0012\u0004\u0018\u00010\t0\b2\u0006\u0010\u0004\u001a\u00020\u0005H\'J\u0018\u0010\u0017\u001a\n\u0012\u0006\u0012\u0004\u0018\u00010\t0\b2\u0006\u0010\u0004\u001a\u00020\u0005H\'J\u0018\u0010\u0018\u001a\n\u0012\u0006\u0012\u0004\u0018\u00010\t0\b2\u0006\u0010\u0004\u001a\u00020\u0005H\'J\u0018\u0010\u0019\u001a\n\u0012\u0006\u0012\u0004\u0018\u00010\t0\b2\u0006\u0010\u0004\u001a\u00020\u0005H\'J\u0016\u0010\u001a\u001a\u00020\u00032\u0006\u0010\u001b\u001a\u00020\tH\u00a7@\u00a2\u0006\u0002\u0010\u001cJ\u0016\u0010\u001d\u001a\u00020\u00032\u0006\u0010\u001b\u001a\u00020\tH\u00a7@\u00a2\u0006\u0002\u0010\u001cJ\u0010\u0010\u001e\u001a\u00020\u00032\u0006\u0010\u001f\u001a\u00020\u0005H\'\u00a8\u0006 "}, d2 = {"Lcom/lijianqiang12/silent/data/model/db/DayLimitDao;", "", "deleteAll", "", "userId", "", "(ILkotlin/coroutines/Continuation;)Ljava/lang/Object;", "getDayLimits", "Landroidx/lifecycle/LiveData;", "Lcom/lijianqiang12/silent/data/model/db/DayLimit;", "isWorkDay", "", "getTodayLimitsFriday", "getTodayLimitsImmediatelyFriday", "getTodayLimitsImmediatelyMonday", "getTodayLimitsImmediatelySaturday", "getTodayLimitsImmediatelySunday", "getTodayLimitsImmediatelyThursday", "getTodayLimitsImmediatelyTuesday", "getTodayLimitsImmediatelyWednesday", "getTodayLimitsMonday", "getTodayLimitsSaturday", "getTodayLimitsSunday", "getTodayLimitsThursday", "getTodayLimitsTuesday", "getTodayLimitsWednesday", "insertDayLimit", "dayLimit", "(Lcom/lijianqiang12/silent/data/model/db/DayLimit;Lkotlin/coroutines/Continuation;)Ljava/lang/Object;", "updateDayLimit", "updateUserId", "newUserId", "data_debug"})
@androidx.room.Dao()
public abstract interface DayLimitDao {
    
    @androidx.room.Query(value = "SELECT * FROM DayLimit where userId = :userId and monday=1 limit 1")
    @org.jetbrains.annotations.NotNull()
    public abstract androidx.lifecycle.LiveData<com.lijianqiang12.silent.data.model.db.DayLimit> getTodayLimitsMonday(int userId);
    
    @androidx.room.Query(value = "SELECT * FROM DayLimit where userId = :userId and tuesday=1 limit 1")
    @org.jetbrains.annotations.NotNull()
    public abstract androidx.lifecycle.LiveData<com.lijianqiang12.silent.data.model.db.DayLimit> getTodayLimitsTuesday(int userId);
    
    @androidx.room.Query(value = "SELECT * FROM DayLimit where userId = :userId and wednesday=1 limit 1")
    @org.jetbrains.annotations.NotNull()
    public abstract androidx.lifecycle.LiveData<com.lijianqiang12.silent.data.model.db.DayLimit> getTodayLimitsWednesday(int userId);
    
    @androidx.room.Query(value = "SELECT * FROM DayLimit where userId = :userId and thursday=1 limit 1")
    @org.jetbrains.annotations.NotNull()
    public abstract androidx.lifecycle.LiveData<com.lijianqiang12.silent.data.model.db.DayLimit> getTodayLimitsThursday(int userId);
    
    @androidx.room.Query(value = "SELECT * FROM DayLimit where userId = :userId and friday=1 limit 1")
    @org.jetbrains.annotations.NotNull()
    public abstract androidx.lifecycle.LiveData<com.lijianqiang12.silent.data.model.db.DayLimit> getTodayLimitsFriday(int userId);
    
    @androidx.room.Query(value = "SELECT * FROM DayLimit where userId = :userId and saturday=1 limit 1")
    @org.jetbrains.annotations.NotNull()
    public abstract androidx.lifecycle.LiveData<com.lijianqiang12.silent.data.model.db.DayLimit> getTodayLimitsSaturday(int userId);
    
    @androidx.room.Query(value = "SELECT * FROM DayLimit where userId = :userId and sunday=1 limit 1")
    @org.jetbrains.annotations.NotNull()
    public abstract androidx.lifecycle.LiveData<com.lijianqiang12.silent.data.model.db.DayLimit> getTodayLimitsSunday(int userId);
    
    @androidx.room.Query(value = "SELECT * FROM DayLimit where userId = :userId and monday=1 limit 1")
    @org.jetbrains.annotations.Nullable()
    public abstract com.lijianqiang12.silent.data.model.db.DayLimit getTodayLimitsImmediatelyMonday(int userId);
    
    @androidx.room.Query(value = "SELECT * FROM DayLimit where userId = :userId and tuesday=1 limit 1")
    @org.jetbrains.annotations.Nullable()
    public abstract com.lijianqiang12.silent.data.model.db.DayLimit getTodayLimitsImmediatelyTuesday(int userId);
    
    @androidx.room.Query(value = "SELECT * FROM DayLimit where userId = :userId and wednesday=1 limit 1")
    @org.jetbrains.annotations.Nullable()
    public abstract com.lijianqiang12.silent.data.model.db.DayLimit getTodayLimitsImmediatelyWednesday(int userId);
    
    @androidx.room.Query(value = "SELECT * FROM DayLimit where userId = :userId and thursday=1 limit 1")
    @org.jetbrains.annotations.Nullable()
    public abstract com.lijianqiang12.silent.data.model.db.DayLimit getTodayLimitsImmediatelyThursday(int userId);
    
    @androidx.room.Query(value = "SELECT * FROM DayLimit where userId = :userId and friday=1 limit 1")
    @org.jetbrains.annotations.Nullable()
    public abstract com.lijianqiang12.silent.data.model.db.DayLimit getTodayLimitsImmediatelyFriday(int userId);
    
    @androidx.room.Query(value = "SELECT * FROM DayLimit where userId = :userId and saturday=1 limit 1")
    @org.jetbrains.annotations.Nullable()
    public abstract com.lijianqiang12.silent.data.model.db.DayLimit getTodayLimitsImmediatelySaturday(int userId);
    
    @androidx.room.Query(value = "SELECT * FROM DayLimit where userId = :userId and sunday=1 limit 1")
    @org.jetbrains.annotations.Nullable()
    public abstract com.lijianqiang12.silent.data.model.db.DayLimit getTodayLimitsImmediatelySunday(int userId);
    
    @androidx.room.Query(value = "SELECT * FROM DayLimit where userId = :userId and isWorkDayLimit=:isWorkDay limit 1")
    @org.jetbrains.annotations.NotNull()
    public abstract androidx.lifecycle.LiveData<com.lijianqiang12.silent.data.model.db.DayLimit> getDayLimits(int userId, boolean isWorkDay);
    
    @androidx.room.Insert(onConflict = 5)
    @org.jetbrains.annotations.Nullable()
    public abstract java.lang.Object insertDayLimit(@org.jetbrains.annotations.NotNull()
    com.lijianqiang12.silent.data.model.db.DayLimit dayLimit, @org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super kotlin.Unit> $completion);
    
    @androidx.room.Update()
    @org.jetbrains.annotations.Nullable()
    public abstract java.lang.Object updateDayLimit(@org.jetbrains.annotations.NotNull()
    com.lijianqiang12.silent.data.model.db.DayLimit dayLimit, @org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super kotlin.Unit> $completion);
    
    @androidx.room.Query(value = "delete from DayLimit where userId = :userId")
    @org.jetbrains.annotations.Nullable()
    public abstract java.lang.Object deleteAll(int userId, @org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super kotlin.Unit> $completion);
    
    @androidx.room.Query(value = "UPDATE DayLimit SET userId = :newUserId WHERE userId = -1")
    public abstract void updateUserId(int newUserId);
}