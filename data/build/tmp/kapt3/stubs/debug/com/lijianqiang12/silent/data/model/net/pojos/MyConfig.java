package com.lijianqiang12.silent.data.model.net.pojos;

@kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000\"\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0000\n\u0002\u0010\u000e\n\u0002\b\u0002\n\u0002\u0010\u000b\n\u0002\b\u001d\n\u0002\u0010\b\n\u0002\bJ\b\u0086\b\u0018\u00002\u00020\u0001B\u0085\u0002\u0012\u0006\u0010\u0002\u001a\u00020\u0003\u0012\u0006\u0010\u0004\u001a\u00020\u0003\u0012\u0006\u0010\u0005\u001a\u00020\u0006\u0012\u0006\u0010\u0007\u001a\u00020\u0006\u0012\u0006\u0010\b\u001a\u00020\u0006\u0012\u0006\u0010\t\u001a\u00020\u0006\u0012\u0006\u0010\n\u001a\u00020\u0006\u0012\u0006\u0010\u000b\u001a\u00020\u0003\u0012\u0006\u0010\f\u001a\u00020\u0003\u0012\u0006\u0010\r\u001a\u00020\u0003\u0012\u0006\u0010\u000e\u001a\u00020\u0003\u0012\u0006\u0010\u000f\u001a\u00020\u0003\u0012\u0006\u0010\u0010\u001a\u00020\u0006\u0012\u0006\u0010\u0011\u001a\u00020\u0006\u0012\u0006\u0010\u0012\u001a\u00020\u0006\u0012\u0006\u0010\u0013\u001a\u00020\u0006\u0012\u0006\u0010\u0014\u001a\u00020\u0006\u0012\u0006\u0010\u0015\u001a\u00020\u0006\u0012\u0006\u0010\u0016\u001a\u00020\u0006\u0012\u0006\u0010\u0017\u001a\u00020\u0006\u0012\u0006\u0010\u0018\u001a\u00020\u0006\u0012\u0006\u0010\u0019\u001a\u00020\u0006\u0012\u0006\u0010\u001a\u001a\u00020\u0003\u0012\u0006\u0010\u001b\u001a\u00020\u0006\u0012\u0006\u0010\u001c\u001a\u00020\u0006\u0012\u0006\u0010\u001d\u001a\u00020\u0003\u0012\u0006\u0010\u001e\u001a\u00020\u0006\u0012\u0006\u0010\u001f\u001a\u00020\u0003\u0012\u0006\u0010 \u001a\u00020\u0006\u0012\u0006\u0010!\u001a\u00020\u0003\u0012\u0006\u0010\"\u001a\u00020\u0006\u0012\u0006\u0010#\u001a\u00020$\u00a2\u0006\u0002\u0010%J\t\u0010I\u001a\u00020\u0003H\u00c6\u0003J\t\u0010J\u001a\u00020\u0003H\u00c6\u0003J\t\u0010K\u001a\u00020\u0003H\u00c6\u0003J\t\u0010L\u001a\u00020\u0003H\u00c6\u0003J\t\u0010M\u001a\u00020\u0006H\u00c6\u0003J\t\u0010N\u001a\u00020\u0006H\u00c6\u0003J\t\u0010O\u001a\u00020\u0006H\u00c6\u0003J\t\u0010P\u001a\u00020\u0006H\u00c6\u0003J\t\u0010Q\u001a\u00020\u0006H\u00c6\u0003J\t\u0010R\u001a\u00020\u0006H\u00c6\u0003J\t\u0010S\u001a\u00020\u0006H\u00c6\u0003J\t\u0010T\u001a\u00020\u0003H\u00c6\u0003J\t\u0010U\u001a\u00020\u0006H\u00c6\u0003J\t\u0010V\u001a\u00020\u0006H\u00c6\u0003J\t\u0010W\u001a\u00020\u0006H\u00c6\u0003J\t\u0010X\u001a\u00020\u0003H\u00c6\u0003J\t\u0010Y\u001a\u00020\u0006H\u00c6\u0003J\t\u0010Z\u001a\u00020\u0006H\u00c6\u0003J\t\u0010[\u001a\u00020\u0003H\u00c6\u0003J\t\u0010\\\u001a\u00020\u0006H\u00c6\u0003J\t\u0010]\u001a\u00020\u0003H\u00c6\u0003J\t\u0010^\u001a\u00020\u0006H\u00c6\u0003J\t\u0010_\u001a\u00020\u0006H\u00c6\u0003J\t\u0010`\u001a\u00020\u0003H\u00c6\u0003J\t\u0010a\u001a\u00020\u0006H\u00c6\u0003J\t\u0010b\u001a\u00020$H\u00c6\u0003J\t\u0010c\u001a\u00020\u0006H\u00c6\u0003J\t\u0010d\u001a\u00020\u0006H\u00c6\u0003J\t\u0010e\u001a\u00020\u0006H\u00c6\u0003J\t\u0010f\u001a\u00020\u0006H\u00c6\u0003J\t\u0010g\u001a\u00020\u0003H\u00c6\u0003J\t\u0010h\u001a\u00020\u0003H\u00c6\u0003J\u00c9\u0002\u0010i\u001a\u00020\u00002\b\b\u0002\u0010\u0002\u001a\u00020\u00032\b\b\u0002\u0010\u0004\u001a\u00020\u00032\b\b\u0002\u0010\u0005\u001a\u00020\u00062\b\b\u0002\u0010\u0007\u001a\u00020\u00062\b\b\u0002\u0010\b\u001a\u00020\u00062\b\b\u0002\u0010\t\u001a\u00020\u00062\b\b\u0002\u0010\n\u001a\u00020\u00062\b\b\u0002\u0010\u000b\u001a\u00020\u00032\b\b\u0002\u0010\f\u001a\u00020\u00032\b\b\u0002\u0010\r\u001a\u00020\u00032\b\b\u0002\u0010\u000e\u001a\u00020\u00032\b\b\u0002\u0010\u000f\u001a\u00020\u00032\b\b\u0002\u0010\u0010\u001a\u00020\u00062\b\b\u0002\u0010\u0011\u001a\u00020\u00062\b\b\u0002\u0010\u0012\u001a\u00020\u00062\b\b\u0002\u0010\u0013\u001a\u00020\u00062\b\b\u0002\u0010\u0014\u001a\u00020\u00062\b\b\u0002\u0010\u0015\u001a\u00020\u00062\b\b\u0002\u0010\u0016\u001a\u00020\u00062\b\b\u0002\u0010\u0017\u001a\u00020\u00062\b\b\u0002\u0010\u0018\u001a\u00020\u00062\b\b\u0002\u0010\u0019\u001a\u00020\u00062\b\b\u0002\u0010\u001a\u001a\u00020\u00032\b\b\u0002\u0010\u001b\u001a\u00020\u00062\b\b\u0002\u0010\u001c\u001a\u00020\u00062\b\b\u0002\u0010\u001d\u001a\u00020\u00032\b\b\u0002\u0010\u001e\u001a\u00020\u00062\b\b\u0002\u0010\u001f\u001a\u00020\u00032\b\b\u0002\u0010 \u001a\u00020\u00062\b\b\u0002\u0010!\u001a\u00020\u00032\b\b\u0002\u0010\"\u001a\u00020\u00062\b\b\u0002\u0010#\u001a\u00020$H\u00c6\u0001J\u0013\u0010j\u001a\u00020\u00062\b\u0010k\u001a\u0004\u0018\u00010\u0001H\u00d6\u0003J\t\u0010l\u001a\u00020$H\u00d6\u0001J\t\u0010m\u001a\u00020\u0003H\u00d6\u0001R\u0011\u0010\u001a\u001a\u00020\u0003\u00a2\u0006\b\n\u0000\u001a\u0004\b&\u0010\'R\u0011\u0010\u000e\u001a\u00020\u0003\u00a2\u0006\b\n\u0000\u001a\u0004\b(\u0010\'R\u0011\u0010\u000f\u001a\u00020\u0003\u00a2\u0006\b\n\u0000\u001a\u0004\b)\u0010\'R\u0011\u0010!\u001a\u00020\u0003\u00a2\u0006\b\n\u0000\u001a\u0004\b*\u0010\'R\u0011\u0010\u0011\u001a\u00020\u0006\u00a2\u0006\b\n\u0000\u001a\u0004\b+\u0010,R\u0011\u0010#\u001a\u00020$\u00a2\u0006\b\n\u0000\u001a\u0004\b-\u0010.R\u0011\u0010\f\u001a\u00020\u0003\u00a2\u0006\b\n\u0000\u001a\u0004\b/\u0010\'R\u0011\u0010\r\u001a\u00020\u0003\u00a2\u0006\b\n\u0000\u001a\u0004\b0\u0010\'R\u0011\u0010\u0004\u001a\u00020\u0003\u00a2\u0006\b\n\u0000\u001a\u0004\b1\u0010\'R\u0011\u0010\u0002\u001a\u00020\u0003\u00a2\u0006\b\n\u0000\u001a\u0004\b2\u0010\'R\u0011\u0010\u0013\u001a\u00020\u0006\u00a2\u0006\b\n\u0000\u001a\u0004\b3\u0010,R\u0011\u0010\u0014\u001a\u00020\u0006\u00a2\u0006\b\n\u0000\u001a\u0004\b4\u0010,R\u0011\u0010\u0015\u001a\u00020\u0006\u00a2\u0006\b\n\u0000\u001a\u0004\b5\u0010,R\u0011\u0010\u0019\u001a\u00020\u0006\u00a2\u0006\b\n\u0000\u001a\u0004\b6\u0010,R\u0011\u0010 \u001a\u00020\u0006\u00a2\u0006\b\n\u0000\u001a\u0004\b7\u0010,R\u0011\u0010\u0017\u001a\u00020\u0006\u00a2\u0006\b\n\u0000\u001a\u0004\b8\u0010,R\u0011\u0010\u001b\u001a\u00020\u0006\u00a2\u0006\b\n\u0000\u001a\u0004\b9\u0010,R\u0011\u0010\n\u001a\u00020\u0006\u00a2\u0006\b\n\u0000\u001a\u0004\b:\u0010,R\u0011\u0010\u0005\u001a\u00020\u0006\u00a2\u0006\b\n\u0000\u001a\u0004\b;\u0010,R\u0011\u0010\"\u001a\u00020\u0006\u00a2\u0006\b\n\u0000\u001a\u0004\b<\u0010,R\u0011\u0010\u0018\u001a\u00020\u0006\u00a2\u0006\b\n\u0000\u001a\u0004\b=\u0010,R\u0011\u0010\u0012\u001a\u00020\u0006\u00a2\u0006\b\n\u0000\u001a\u0004\b>\u0010,R\u0011\u0010\u0007\u001a\u00020\u0006\u00a2\u0006\b\n\u0000\u001a\u0004\b?\u0010,R\u0011\u0010\t\u001a\u00020\u0006\u00a2\u0006\b\n\u0000\u001a\u0004\b@\u0010,R\u0011\u0010\u0010\u001a\u00020\u0006\u00a2\u0006\b\n\u0000\u001a\u0004\bA\u0010,R\u0011\u0010\b\u001a\u00020\u0006\u00a2\u0006\b\n\u0000\u001a\u0004\bB\u0010,R\u0011\u0010\u0016\u001a\u00020\u0006\u00a2\u0006\b\n\u0000\u001a\u0004\bC\u0010,R\u0011\u0010\u001c\u001a\u00020\u0006\u00a2\u0006\b\n\u0000\u001a\u0004\bD\u0010,R\u0011\u0010\u001e\u001a\u00020\u0006\u00a2\u0006\b\n\u0000\u001a\u0004\bE\u0010,R\u0011\u0010\u000b\u001a\u00020\u0003\u00a2\u0006\b\n\u0000\u001a\u0004\bF\u0010\'R\u0011\u0010\u001d\u001a\u00020\u0003\u00a2\u0006\b\n\u0000\u001a\u0004\bG\u0010\'R\u0011\u0010\u001f\u001a\u00020\u0003\u00a2\u0006\b\n\u0000\u001a\u0004\bH\u0010\'\u00a8\u0006n"}, d2 = {"Lcom/lijianqiang12/silent/data/model/net/pojos/MyConfig;", "", "qqNumber", "", "qqLink", "showIAMFINE", "", "showQQ", "showWX", "showRoomRequest", "showDeveloperUnlock", "tomatoUrl", "giftPicUrl", "giftText", "baozangIconUrl", "baozangText", "showVipDialog", "canCloseShowProduct", "showProduct", "show1", "show2", "show3", "showWallpaper", "showBoard", "showNoBoardNotice", "showAlipay", "alipayUrl", "showComplain", "showXiaoHongShu", "xiaoHongShuUrl", "showZhiHu", "zhiHuUrl", "showBilibili", "bilibiliUrl", "showInviteGift", "defaultCheckAppMode", "", "(Ljava/lang/String;Ljava/lang/String;ZZZZZLjava/lang/String;Ljava/lang/String;Ljava/lang/String;Ljava/lang/String;Ljava/lang/String;ZZZZZZZZZZLjava/lang/String;ZZLjava/lang/String;ZLjava/lang/String;ZLjava/lang/String;ZI)V", "getAlipayUrl", "()Ljava/lang/String;", "getBaozangIconUrl", "getBaozangText", "getBilibiliUrl", "getCanCloseShowProduct", "()Z", "getDefaultCheckAppMode", "()I", "getGiftPicUrl", "getGiftText", "getQqLink", "getQqNumber", "getShow1", "getShow2", "getShow3", "getShowAlipay", "getShowBilibili", "getShowBoard", "getShowComplain", "getShowDeveloperUnlock", "getShowIAMFINE", "getShowInviteGift", "getShowNoBoardNotice", "getShowProduct", "getShowQQ", "getShowRoomRequest", "getShowVipDialog", "getShowWX", "getShowWallpaper", "getShowXiaoHongShu", "getShowZhiHu", "getTomatoUrl", "getXiaoHongShuUrl", "getZhiHuUrl", "component1", "component10", "component11", "component12", "component13", "component14", "component15", "component16", "component17", "component18", "component19", "component2", "component20", "component21", "component22", "component23", "component24", "component25", "component26", "component27", "component28", "component29", "component3", "component30", "component31", "component32", "component4", "component5", "component6", "component7", "component8", "component9", "copy", "equals", "other", "hashCode", "toString", "data_debug"})
public final class MyConfig {
    @org.jetbrains.annotations.NotNull()
    private final java.lang.String qqNumber = null;
    @org.jetbrains.annotations.NotNull()
    private final java.lang.String qqLink = null;
    private final boolean showIAMFINE = false;
    private final boolean showQQ = false;
    private final boolean showWX = false;
    private final boolean showRoomRequest = false;
    private final boolean showDeveloperUnlock = false;
    @org.jetbrains.annotations.NotNull()
    private final java.lang.String tomatoUrl = null;
    @org.jetbrains.annotations.NotNull()
    private final java.lang.String giftPicUrl = null;
    @org.jetbrains.annotations.NotNull()
    private final java.lang.String giftText = null;
    @org.jetbrains.annotations.NotNull()
    private final java.lang.String baozangIconUrl = null;
    @org.jetbrains.annotations.NotNull()
    private final java.lang.String baozangText = null;
    private final boolean showVipDialog = false;
    private final boolean canCloseShowProduct = false;
    private final boolean showProduct = false;
    private final boolean show1 = false;
    private final boolean show2 = false;
    private final boolean show3 = false;
    private final boolean showWallpaper = false;
    private final boolean showBoard = false;
    private final boolean showNoBoardNotice = false;
    private final boolean showAlipay = false;
    @org.jetbrains.annotations.NotNull()
    private final java.lang.String alipayUrl = null;
    private final boolean showComplain = false;
    private final boolean showXiaoHongShu = false;
    @org.jetbrains.annotations.NotNull()
    private final java.lang.String xiaoHongShuUrl = null;
    private final boolean showZhiHu = false;
    @org.jetbrains.annotations.NotNull()
    private final java.lang.String zhiHuUrl = null;
    private final boolean showBilibili = false;
    @org.jetbrains.annotations.NotNull()
    private final java.lang.String bilibiliUrl = null;
    private final boolean showInviteGift = false;
    private final int defaultCheckAppMode = 0;
    
    public MyConfig(@org.jetbrains.annotations.NotNull()
    java.lang.String qqNumber, @org.jetbrains.annotations.NotNull()
    java.lang.String qqLink, boolean showIAMFINE, boolean showQQ, boolean showWX, boolean showRoomRequest, boolean showDeveloperUnlock, @org.jetbrains.annotations.NotNull()
    java.lang.String tomatoUrl, @org.jetbrains.annotations.NotNull()
    java.lang.String giftPicUrl, @org.jetbrains.annotations.NotNull()
    java.lang.String giftText, @org.jetbrains.annotations.NotNull()
    java.lang.String baozangIconUrl, @org.jetbrains.annotations.NotNull()
    java.lang.String baozangText, boolean showVipDialog, boolean canCloseShowProduct, boolean showProduct, boolean show1, boolean show2, boolean show3, boolean showWallpaper, boolean showBoard, boolean showNoBoardNotice, boolean showAlipay, @org.jetbrains.annotations.NotNull()
    java.lang.String alipayUrl, boolean showComplain, boolean showXiaoHongShu, @org.jetbrains.annotations.NotNull()
    java.lang.String xiaoHongShuUrl, boolean showZhiHu, @org.jetbrains.annotations.NotNull()
    java.lang.String zhiHuUrl, boolean showBilibili, @org.jetbrains.annotations.NotNull()
    java.lang.String bilibiliUrl, boolean showInviteGift, int defaultCheckAppMode) {
        super();
    }
    
    @org.jetbrains.annotations.NotNull()
    public final java.lang.String getQqNumber() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final java.lang.String getQqLink() {
        return null;
    }
    
    public final boolean getShowIAMFINE() {
        return false;
    }
    
    public final boolean getShowQQ() {
        return false;
    }
    
    public final boolean getShowWX() {
        return false;
    }
    
    public final boolean getShowRoomRequest() {
        return false;
    }
    
    public final boolean getShowDeveloperUnlock() {
        return false;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final java.lang.String getTomatoUrl() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final java.lang.String getGiftPicUrl() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final java.lang.String getGiftText() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final java.lang.String getBaozangIconUrl() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final java.lang.String getBaozangText() {
        return null;
    }
    
    public final boolean getShowVipDialog() {
        return false;
    }
    
    public final boolean getCanCloseShowProduct() {
        return false;
    }
    
    public final boolean getShowProduct() {
        return false;
    }
    
    public final boolean getShow1() {
        return false;
    }
    
    public final boolean getShow2() {
        return false;
    }
    
    public final boolean getShow3() {
        return false;
    }
    
    public final boolean getShowWallpaper() {
        return false;
    }
    
    public final boolean getShowBoard() {
        return false;
    }
    
    public final boolean getShowNoBoardNotice() {
        return false;
    }
    
    public final boolean getShowAlipay() {
        return false;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final java.lang.String getAlipayUrl() {
        return null;
    }
    
    public final boolean getShowComplain() {
        return false;
    }
    
    public final boolean getShowXiaoHongShu() {
        return false;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final java.lang.String getXiaoHongShuUrl() {
        return null;
    }
    
    public final boolean getShowZhiHu() {
        return false;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final java.lang.String getZhiHuUrl() {
        return null;
    }
    
    public final boolean getShowBilibili() {
        return false;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final java.lang.String getBilibiliUrl() {
        return null;
    }
    
    public final boolean getShowInviteGift() {
        return false;
    }
    
    public final int getDefaultCheckAppMode() {
        return 0;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final java.lang.String component1() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final java.lang.String component10() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final java.lang.String component11() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final java.lang.String component12() {
        return null;
    }
    
    public final boolean component13() {
        return false;
    }
    
    public final boolean component14() {
        return false;
    }
    
    public final boolean component15() {
        return false;
    }
    
    public final boolean component16() {
        return false;
    }
    
    public final boolean component17() {
        return false;
    }
    
    public final boolean component18() {
        return false;
    }
    
    public final boolean component19() {
        return false;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final java.lang.String component2() {
        return null;
    }
    
    public final boolean component20() {
        return false;
    }
    
    public final boolean component21() {
        return false;
    }
    
    public final boolean component22() {
        return false;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final java.lang.String component23() {
        return null;
    }
    
    public final boolean component24() {
        return false;
    }
    
    public final boolean component25() {
        return false;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final java.lang.String component26() {
        return null;
    }
    
    public final boolean component27() {
        return false;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final java.lang.String component28() {
        return null;
    }
    
    public final boolean component29() {
        return false;
    }
    
    public final boolean component3() {
        return false;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final java.lang.String component30() {
        return null;
    }
    
    public final boolean component31() {
        return false;
    }
    
    public final int component32() {
        return 0;
    }
    
    public final boolean component4() {
        return false;
    }
    
    public final boolean component5() {
        return false;
    }
    
    public final boolean component6() {
        return false;
    }
    
    public final boolean component7() {
        return false;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final java.lang.String component8() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final java.lang.String component9() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final com.lijianqiang12.silent.data.model.net.pojos.MyConfig copy(@org.jetbrains.annotations.NotNull()
    java.lang.String qqNumber, @org.jetbrains.annotations.NotNull()
    java.lang.String qqLink, boolean showIAMFINE, boolean showQQ, boolean showWX, boolean showRoomRequest, boolean showDeveloperUnlock, @org.jetbrains.annotations.NotNull()
    java.lang.String tomatoUrl, @org.jetbrains.annotations.NotNull()
    java.lang.String giftPicUrl, @org.jetbrains.annotations.NotNull()
    java.lang.String giftText, @org.jetbrains.annotations.NotNull()
    java.lang.String baozangIconUrl, @org.jetbrains.annotations.NotNull()
    java.lang.String baozangText, boolean showVipDialog, boolean canCloseShowProduct, boolean showProduct, boolean show1, boolean show2, boolean show3, boolean showWallpaper, boolean showBoard, boolean showNoBoardNotice, boolean showAlipay, @org.jetbrains.annotations.NotNull()
    java.lang.String alipayUrl, boolean showComplain, boolean showXiaoHongShu, @org.jetbrains.annotations.NotNull()
    java.lang.String xiaoHongShuUrl, boolean showZhiHu, @org.jetbrains.annotations.NotNull()
    java.lang.String zhiHuUrl, boolean showBilibili, @org.jetbrains.annotations.NotNull()
    java.lang.String bilibiliUrl, boolean showInviteGift, int defaultCheckAppMode) {
        return null;
    }
    
    @java.lang.Override()
    public boolean equals(@org.jetbrains.annotations.Nullable()
    java.lang.Object other) {
        return false;
    }
    
    @java.lang.Override()
    public int hashCode() {
        return 0;
    }
    
    @java.lang.Override()
    @org.jetbrains.annotations.NotNull()
    public java.lang.String toString() {
        return null;
    }
}