package com.lijianqiang12.silent.data.model.net;

@kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000\"\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0010\u000e\n\u0002\b\u0005\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0003\u0018\u0000 \r2\u00020\u0001:\u0002\r\u000eB\u0005\u00a2\u0006\u0002\u0010\u0002J\u0010\u0010\t\u001a\u00020\n2\u0006\u0010\u000b\u001a\u00020\fH\u0016R\u0014\u0010\u0003\u001a\u00020\u00048BX\u0082\u0004\u00a2\u0006\u0006\u001a\u0004\b\u0005\u0010\u0006R\u0014\u0010\u0007\u001a\u00020\u00048BX\u0082\u0004\u00a2\u0006\u0006\u001a\u0004\b\b\u0010\u0006\u00a8\u0006\u000f"}, d2 = {"Lcom/lijianqiang12/silent/data/model/net/CommonInterceptor;", "Lokhttp3/Interceptor;", "()V", "deviceId", "", "getDeviceId", "()Ljava/lang/String;", "oaid", "getOaid", "intercept", "Lokhttp3/Response;", "chain", "Lokhttp3/Interceptor$Chain;", "Companion", "MyForm", "data_debug"})
public final class CommonInterceptor implements okhttp3.Interceptor {
    @org.jetbrains.annotations.NotNull()
    public static final com.lijianqiang12.silent.data.model.net.CommonInterceptor.Companion Companion = null;
    
    public CommonInterceptor() {
        super();
    }
    
    @java.lang.Override()
    @org.jetbrains.annotations.NotNull()
    public okhttp3.Response intercept(@org.jetbrains.annotations.NotNull()
    okhttp3.Interceptor.Chain chain) {
        return null;
    }
    
    private final java.lang.String getDeviceId() {
        return null;
    }
    
    private final java.lang.String getOaid() {
        return null;
    }
    
    @kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000\u001e\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0002\b\u0002\n\u0002\u0010$\n\u0002\u0010\u000e\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0003\b\u0086\u0003\u0018\u00002\u00020\u0001B\u0007\b\u0002\u00a2\u0006\u0002\u0010\u0002J\"\u0010\u0003\u001a\u0014\u0012\u0006\u0012\u0004\u0018\u00010\u0005\u0012\u0006\u0012\u0004\u0018\u00010\u0005\u0018\u00010\u00042\u0006\u0010\u0006\u001a\u00020\u0007H\u0002J \u0010\b\u001a\u0012\u0012\u0006\u0012\u0004\u0018\u00010\u0005\u0012\u0006\u0012\u0004\u0018\u00010\u00050\u00042\u0006\u0010\u0006\u001a\u00020\u0007H\u0002J \u0010\t\u001a\u0014\u0012\u0006\u0012\u0004\u0018\u00010\u0005\u0012\u0006\u0012\u0004\u0018\u00010\u0005\u0018\u00010\u00042\u0006\u0010\u0006\u001a\u00020\u0007\u00a8\u0006\n"}, d2 = {"Lcom/lijianqiang12/silent/data/model/net/CommonInterceptor$Companion;", "", "()V", "doForm", "", "", "request", "Lokhttp3/Request;", "doGet", "parseParams", "data_debug"})
    public static final class Companion {
        
        private Companion() {
            super();
        }
        
        /**
         * 解析请求参数
         *
         * @param request
         * @return
         */
        @org.jetbrains.annotations.Nullable()
        public final java.util.Map<java.lang.String, java.lang.String> parseParams(@org.jetbrains.annotations.NotNull()
        okhttp3.Request request) {
            return null;
        }
        
        /**
         * 获取get方式的请求参数
         */
        private final java.util.Map<java.lang.String, java.lang.String> doGet(okhttp3.Request request) {
            return null;
        }
        
        /**
         * 获取表单的请求参数
         */
        private final java.util.Map<java.lang.String, java.lang.String> doForm(okhttp3.Request request) {
            return null;
        }
    }
    
    @kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000\u0012\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0000\n\u0002\u0010\u000e\n\u0002\b\t\b\u0080\u0004\u0018\u00002\u00020\u0001B\u0015\u0012\u0006\u0010\u0002\u001a\u00020\u0003\u0012\u0006\u0010\u0004\u001a\u00020\u0003\u00a2\u0006\u0002\u0010\u0005R\u001a\u0010\u0002\u001a\u00020\u0003X\u0086\u000e\u00a2\u0006\u000e\n\u0000\u001a\u0004\b\u0006\u0010\u0007\"\u0004\b\b\u0010\tR\u001a\u0010\u0004\u001a\u00020\u0003X\u0086\u000e\u00a2\u0006\u000e\n\u0000\u001a\u0004\b\n\u0010\u0007\"\u0004\b\u000b\u0010\t\u00a8\u0006\f"}, d2 = {"Lcom/lijianqiang12/silent/data/model/net/CommonInterceptor$MyForm;", "", "key", "", "value", "(Lcom/lijianqiang12/silent/data/model/net/CommonInterceptor;Ljava/lang/String;Ljava/lang/String;)V", "getKey", "()Ljava/lang/String;", "setKey", "(Ljava/lang/String;)V", "getValue", "setValue", "data_debug"})
    public final class MyForm {
        @org.jetbrains.annotations.NotNull()
        private java.lang.String key;
        @org.jetbrains.annotations.NotNull()
        private java.lang.String value;
        
        public MyForm(@org.jetbrains.annotations.NotNull()
        java.lang.String key, @org.jetbrains.annotations.NotNull()
        java.lang.String value) {
            super();
        }
        
        @org.jetbrains.annotations.NotNull()
        public final java.lang.String getKey() {
            return null;
        }
        
        public final void setKey(@org.jetbrains.annotations.NotNull()
        java.lang.String p0) {
        }
        
        @org.jetbrains.annotations.NotNull()
        public final java.lang.String getValue() {
            return null;
        }
        
        public final void setValue(@org.jetbrains.annotations.NotNull()
        java.lang.String p0) {
        }
    }
}