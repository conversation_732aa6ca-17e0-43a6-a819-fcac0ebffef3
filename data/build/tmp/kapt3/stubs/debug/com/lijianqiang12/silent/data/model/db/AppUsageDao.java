package com.lijianqiang12.silent.data.model.db;

@kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u00004\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0000\n\u0002\u0010!\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\t\n\u0002\b\u0004\n\u0002\u0010\u000e\n\u0002\b\u0003\n\u0002\u0010 \n\u0002\b\u0002\n\u0002\u0010\u0002\n\u0002\b\u0003\bg\u0018\u00002\u00020\u0001J$\u0010\u0002\u001a\b\u0012\u0004\u0012\u00020\u00040\u00032\u0006\u0010\u0005\u001a\u00020\u00062\u0006\u0010\u0007\u001a\u00020\u0006H\u00a7@\u00a2\u0006\u0002\u0010\bJ,\u0010\t\u001a\b\u0012\u0004\u0012\u00020\u00040\u00032\u0006\u0010\u0005\u001a\u00020\u00062\u0006\u0010\u0007\u001a\u00020\u00062\u0006\u0010\n\u001a\u00020\u000bH\u00a7@\u00a2\u0006\u0002\u0010\fJ2\u0010\r\u001a\b\u0012\u0004\u0012\u00020\u00040\u00032\u0006\u0010\u0005\u001a\u00020\u00062\u0006\u0010\u0007\u001a\u00020\u00062\f\u0010\u000e\u001a\b\u0012\u0004\u0012\u00020\u000b0\u000fH\u00a7@\u00a2\u0006\u0002\u0010\u0010J\u0016\u0010\u0011\u001a\u00020\u00122\u0006\u0010\u0013\u001a\u00020\u0004H\u00a7@\u00a2\u0006\u0002\u0010\u0014\u00a8\u0006\u0015"}, d2 = {"Lcom/lijianqiang12/silent/data/model/db/AppUsageDao;", "", "getAllAppUsageList", "", "Lcom/lijianqiang12/silent/data/model/db/AppUsage;", "startTime", "", "endTime", "(JJLkotlin/coroutines/Continuation;)Ljava/lang/Object;", "getOneAppUsageList", "appPkg", "", "(JJLjava/lang/String;Lkotlin/coroutines/Continuation;)Ljava/lang/Object;", "getSomeAppUsageList", "appPkgList", "", "(JJLjava/util/List;Lkotlin/coroutines/Continuation;)Ljava/lang/Object;", "insertAppUsage", "", "appUsage", "(Lcom/lijianqiang12/silent/data/model/db/AppUsage;Lkotlin/coroutines/Continuation;)Ljava/lang/Object;", "data_debug"})
@androidx.room.Dao()
public abstract interface AppUsageDao {
    
    @androidx.room.Query(value = "select * From AppUsage where timestamp>=:startTime and timestamp<=:endTime")
    @org.jetbrains.annotations.Nullable()
    public abstract java.lang.Object getAllAppUsageList(long startTime, long endTime, @org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super java.util.List<com.lijianqiang12.silent.data.model.db.AppUsage>> $completion);
    
    @androidx.room.Query(value = "select * From AppUsage where timestamp>=:startTime and timestamp<=:endTime and appPkg=:appPkg")
    @org.jetbrains.annotations.Nullable()
    public abstract java.lang.Object getOneAppUsageList(long startTime, long endTime, @org.jetbrains.annotations.NotNull()
    java.lang.String appPkg, @org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super java.util.List<com.lijianqiang12.silent.data.model.db.AppUsage>> $completion);
    
    @androidx.room.Query(value = "select * From AppUsage where timestamp>=:startTime and timestamp<=:endTime and appPkg in (:appPkgList)")
    @org.jetbrains.annotations.Nullable()
    public abstract java.lang.Object getSomeAppUsageList(long startTime, long endTime, @org.jetbrains.annotations.NotNull()
    java.util.List<java.lang.String> appPkgList, @org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super java.util.List<com.lijianqiang12.silent.data.model.db.AppUsage>> $completion);
    
    @androidx.room.Insert(onConflict = 5)
    @org.jetbrains.annotations.Nullable()
    public abstract java.lang.Object insertAppUsage(@org.jetbrains.annotations.NotNull()
    com.lijianqiang12.silent.data.model.db.AppUsage appUsage, @org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super kotlin.Unit> $completion);
}