package com.lijianqiang12.silent.data.model.db;

@kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u00004\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0000\n\u0002\u0010\t\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\u0002\n\u0000\n\u0002\u0010\b\n\u0002\b\u0004\n\u0002\u0010!\n\u0002\b\u0004\n\u0002\u0018\u0002\n\u0002\b\u0006\bg\u0018\u00002\u00020\u0001J\u0010\u0010\u0002\u001a\u00020\u00032\u0006\u0010\u0004\u001a\u00020\u0005H\'J\u0016\u0010\u0006\u001a\u00020\u00072\u0006\u0010\b\u001a\u00020\tH\u00a7@\u00a2\u0006\u0002\u0010\nJ\u0016\u0010\u000b\u001a\u00020\u00072\u0006\u0010\u0004\u001a\u00020\u0005H\u00a7@\u00a2\u0006\u0002\u0010\fJ\u001c\u0010\r\u001a\b\u0012\u0004\u0012\u00020\u00050\u000e2\u0006\u0010\b\u001a\u00020\tH\u00a7@\u00a2\u0006\u0002\u0010\nJ$\u0010\u000f\u001a\b\u0012\u0004\u0012\u00020\u00050\u000e2\u0006\u0010\b\u001a\u00020\t2\u0006\u0010\u0010\u001a\u00020\tH\u00a7@\u00a2\u0006\u0002\u0010\u0011J\u001c\u0010\u0012\u001a\u000e\u0012\n\u0012\b\u0012\u0004\u0012\u00020\u00050\u000e0\u00132\u0006\u0010\b\u001a\u00020\tH\'J\u0018\u0010\u0014\u001a\u0004\u0018\u00010\u00052\u0006\u0010\b\u001a\u00020\tH\u00a7@\u00a2\u0006\u0002\u0010\nJ\u0016\u0010\u0015\u001a\u00020\u00032\u0006\u0010\u0004\u001a\u00020\u0005H\u00a7@\u00a2\u0006\u0002\u0010\fJ\u0016\u0010\u0016\u001a\u00020\u00072\u0006\u0010\u0004\u001a\u00020\u0005H\u00a7@\u00a2\u0006\u0002\u0010\fJ\u0010\u0010\u0017\u001a\u00020\u00072\u0006\u0010\u0018\u001a\u00020\tH\'\u00a8\u0006\u0019"}, d2 = {"Lcom/lijianqiang12/silent/data/model/db/FastDao;", "", "addFast", "", "fast", "Lcom/lijianqiang12/silent/data/model/db/Fast;", "deleteAll", "", "userId", "", "(ILkotlin/coroutines/Continuation;)Ljava/lang/Object;", "deleteFast", "(Lcom/lijianqiang12/silent/data/model/db/Fast;Lkotlin/coroutines/Continuation;)Ljava/lang/Object;", "getAllFastList", "", "getFastWithState", "state", "(IILkotlin/coroutines/Continuation;)Ljava/lang/Object;", "getFasts", "Landroidx/lifecycle/LiveData;", "getLastFast", "insertFast", "updateFast", "updateUserId", "newUserId", "data_debug"})
@androidx.room.Dao()
public abstract interface FastDao {
    
    @androidx.room.Query(value = "select * From Fast Where userId = :userId and syncState = :state order by trend")
    @org.jetbrains.annotations.Nullable()
    public abstract java.lang.Object getFastWithState(int userId, int state, @org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super java.util.List<com.lijianqiang12.silent.data.model.db.Fast>> $completion);
    
    @androidx.room.Query(value = "select * From Fast Where userId = :userId order by trend")
    @org.jetbrains.annotations.Nullable()
    public abstract java.lang.Object getAllFastList(int userId, @org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super java.util.List<com.lijianqiang12.silent.data.model.db.Fast>> $completion);
    
    @androidx.room.Query(value = "select * From Fast where userId = :userId and syncState>=0 order by trend")
    @org.jetbrains.annotations.NotNull()
    public abstract androidx.lifecycle.LiveData<java.util.List<com.lijianqiang12.silent.data.model.db.Fast>> getFasts(int userId);
    
    @androidx.room.Insert(onConflict = 5)
    @org.jetbrains.annotations.Nullable()
    public abstract java.lang.Object insertFast(@org.jetbrains.annotations.NotNull()
    com.lijianqiang12.silent.data.model.db.Fast fast, @org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super java.lang.Long> $completion);
    
    @androidx.room.Update()
    @org.jetbrains.annotations.Nullable()
    public abstract java.lang.Object updateFast(@org.jetbrains.annotations.NotNull()
    com.lijianqiang12.silent.data.model.db.Fast fast, @org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super kotlin.Unit> $completion);
    
    @androidx.room.Delete()
    @org.jetbrains.annotations.Nullable()
    public abstract java.lang.Object deleteFast(@org.jetbrains.annotations.NotNull()
    com.lijianqiang12.silent.data.model.db.Fast fast, @org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super kotlin.Unit> $completion);
    
    @androidx.room.Query(value = "select * from Fast where userId = :userId order by trend desc limit 1")
    @org.jetbrains.annotations.Nullable()
    public abstract java.lang.Object getLastFast(int userId, @org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super com.lijianqiang12.silent.data.model.db.Fast> $completion);
    
    @androidx.room.Insert(onConflict = 5)
    public abstract long addFast(@org.jetbrains.annotations.NotNull()
    com.lijianqiang12.silent.data.model.db.Fast fast);
    
    @androidx.room.Query(value = "delete from Fast where userId = :userId")
    @org.jetbrains.annotations.Nullable()
    public abstract java.lang.Object deleteAll(int userId, @org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super kotlin.Unit> $completion);
    
    @androidx.room.Query(value = "UPDATE Fast SET userId = :newUserId WHERE userId = -1")
    public abstract void updateUserId(int newUserId);
}