package com.lijianqiang12.silent.data.model.net.pojos;

@kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000 \n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0000\n\u0002\u0010\u000e\n\u0000\n\u0002\u0010\b\n\u0002\b\u001d\n\u0002\u0010\u000b\n\u0002\b\u0004\b\u0086\b\u0018\u00002\u00020\u0001BA\u0012\b\b\u0002\u0010\u0002\u001a\u00020\u0003\u0012\b\b\u0002\u0010\u0004\u001a\u00020\u0005\u0012\b\b\u0002\u0010\u0006\u001a\u00020\u0003\u0012\b\b\u0002\u0010\u0007\u001a\u00020\u0003\u0012\b\b\u0002\u0010\b\u001a\u00020\u0003\u0012\b\b\u0002\u0010\t\u001a\u00020\u0003\u00a2\u0006\u0002\u0010\nJ\t\u0010\u001b\u001a\u00020\u0003H\u00c6\u0003J\t\u0010\u001c\u001a\u00020\u0005H\u00c6\u0003J\t\u0010\u001d\u001a\u00020\u0003H\u00c6\u0003J\t\u0010\u001e\u001a\u00020\u0003H\u00c6\u0003J\t\u0010\u001f\u001a\u00020\u0003H\u00c6\u0003J\t\u0010 \u001a\u00020\u0003H\u00c6\u0003JE\u0010!\u001a\u00020\u00002\b\b\u0002\u0010\u0002\u001a\u00020\u00032\b\b\u0002\u0010\u0004\u001a\u00020\u00052\b\b\u0002\u0010\u0006\u001a\u00020\u00032\b\b\u0002\u0010\u0007\u001a\u00020\u00032\b\b\u0002\u0010\b\u001a\u00020\u00032\b\b\u0002\u0010\t\u001a\u00020\u0003H\u00c6\u0001J\u0013\u0010\"\u001a\u00020#2\b\u0010$\u001a\u0004\u0018\u00010\u0001H\u00d6\u0003J\t\u0010%\u001a\u00020\u0005H\u00d6\u0001J\t\u0010&\u001a\u00020\u0003H\u00d6\u0001R\u001a\u0010\u0002\u001a\u00020\u0003X\u0086\u000e\u00a2\u0006\u000e\n\u0000\u001a\u0004\b\u000b\u0010\f\"\u0004\b\r\u0010\u000eR\u001a\u0010\u0004\u001a\u00020\u0005X\u0086\u000e\u00a2\u0006\u000e\n\u0000\u001a\u0004\b\u000f\u0010\u0010\"\u0004\b\u0011\u0010\u0012R\u001a\u0010\u0006\u001a\u00020\u0003X\u0086\u000e\u00a2\u0006\u000e\n\u0000\u001a\u0004\b\u0013\u0010\f\"\u0004\b\u0014\u0010\u000eR\u001a\u0010\u0007\u001a\u00020\u0003X\u0086\u000e\u00a2\u0006\u000e\n\u0000\u001a\u0004\b\u0015\u0010\f\"\u0004\b\u0016\u0010\u000eR\u001a\u0010\b\u001a\u00020\u0003X\u0086\u000e\u00a2\u0006\u000e\n\u0000\u001a\u0004\b\u0017\u0010\f\"\u0004\b\u0018\u0010\u000eR\u001a\u0010\t\u001a\u00020\u0003X\u0086\u000e\u00a2\u0006\u000e\n\u0000\u001a\u0004\b\u0019\u0010\f\"\u0004\b\u001a\u0010\u000e\u00a8\u0006\'"}, d2 = {"Lcom/lijianqiang12/silent/data/model/net/pojos/WXTokenModel;", "", "access_token", "", "expires_in", "", "openid", "refresh_token", "scope", "unionid", "(Ljava/lang/String;ILjava/lang/String;Ljava/lang/String;Ljava/lang/String;Ljava/lang/String;)V", "getAccess_token", "()Ljava/lang/String;", "setAccess_token", "(Ljava/lang/String;)V", "getExpires_in", "()I", "setExpires_in", "(I)V", "getOpenid", "setOpenid", "getRefresh_token", "setRefresh_token", "getScope", "setScope", "getUnionid", "setUnionid", "component1", "component2", "component3", "component4", "component5", "component6", "copy", "equals", "", "other", "hashCode", "toString", "data_debug"})
public final class WXTokenModel {
    @org.jetbrains.annotations.NotNull()
    private java.lang.String access_token;
    private int expires_in;
    @org.jetbrains.annotations.NotNull()
    private java.lang.String openid;
    @org.jetbrains.annotations.NotNull()
    private java.lang.String refresh_token;
    @org.jetbrains.annotations.NotNull()
    private java.lang.String scope;
    @org.jetbrains.annotations.NotNull()
    private java.lang.String unionid;
    
    public WXTokenModel(@org.jetbrains.annotations.NotNull()
    java.lang.String access_token, int expires_in, @org.jetbrains.annotations.NotNull()
    java.lang.String openid, @org.jetbrains.annotations.NotNull()
    java.lang.String refresh_token, @org.jetbrains.annotations.NotNull()
    java.lang.String scope, @org.jetbrains.annotations.NotNull()
    java.lang.String unionid) {
        super();
    }
    
    @org.jetbrains.annotations.NotNull()
    public final java.lang.String getAccess_token() {
        return null;
    }
    
    public final void setAccess_token(@org.jetbrains.annotations.NotNull()
    java.lang.String p0) {
    }
    
    public final int getExpires_in() {
        return 0;
    }
    
    public final void setExpires_in(int p0) {
    }
    
    @org.jetbrains.annotations.NotNull()
    public final java.lang.String getOpenid() {
        return null;
    }
    
    public final void setOpenid(@org.jetbrains.annotations.NotNull()
    java.lang.String p0) {
    }
    
    @org.jetbrains.annotations.NotNull()
    public final java.lang.String getRefresh_token() {
        return null;
    }
    
    public final void setRefresh_token(@org.jetbrains.annotations.NotNull()
    java.lang.String p0) {
    }
    
    @org.jetbrains.annotations.NotNull()
    public final java.lang.String getScope() {
        return null;
    }
    
    public final void setScope(@org.jetbrains.annotations.NotNull()
    java.lang.String p0) {
    }
    
    @org.jetbrains.annotations.NotNull()
    public final java.lang.String getUnionid() {
        return null;
    }
    
    public final void setUnionid(@org.jetbrains.annotations.NotNull()
    java.lang.String p0) {
    }
    
    public WXTokenModel() {
        super();
    }
    
    @org.jetbrains.annotations.NotNull()
    public final java.lang.String component1() {
        return null;
    }
    
    public final int component2() {
        return 0;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final java.lang.String component3() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final java.lang.String component4() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final java.lang.String component5() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final java.lang.String component6() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final com.lijianqiang12.silent.data.model.net.pojos.WXTokenModel copy(@org.jetbrains.annotations.NotNull()
    java.lang.String access_token, int expires_in, @org.jetbrains.annotations.NotNull()
    java.lang.String openid, @org.jetbrains.annotations.NotNull()
    java.lang.String refresh_token, @org.jetbrains.annotations.NotNull()
    java.lang.String scope, @org.jetbrains.annotations.NotNull()
    java.lang.String unionid) {
        return null;
    }
    
    @java.lang.Override()
    public boolean equals(@org.jetbrains.annotations.Nullable()
    java.lang.Object other) {
        return false;
    }
    
    @java.lang.Override()
    public int hashCode() {
        return 0;
    }
    
    @java.lang.Override()
    @org.jetbrains.annotations.NotNull()
    public java.lang.String toString() {
        return null;
    }
}