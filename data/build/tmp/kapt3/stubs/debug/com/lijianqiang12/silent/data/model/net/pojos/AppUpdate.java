package com.lijianqiang12.silent.data.model.net.pojos;

@kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000 \n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0000\n\u0002\u0010\u000e\n\u0000\n\u0002\u0010\u000b\n\u0002\b\u0004\n\u0002\u0010\b\n\u0002\b)\b\u0086\b\u0018\u00002\u00020\u0001Bm\u0012\u0006\u0010\u0002\u001a\u00020\u0003\u0012\u0006\u0010\u0004\u001a\u00020\u0005\u0012\u0006\u0010\u0006\u001a\u00020\u0005\u0012\u0006\u0010\u0007\u001a\u00020\u0005\u0012\u0006\u0010\b\u001a\u00020\u0005\u0012\u0006\u0010\t\u001a\u00020\n\u0012\u0006\u0010\u000b\u001a\u00020\u0003\u0012\u0006\u0010\f\u001a\u00020\u0005\u0012\u0006\u0010\r\u001a\u00020\u0005\u0012\u0006\u0010\u000e\u001a\u00020\u0003\u0012\u0006\u0010\u000f\u001a\u00020\u0005\u0012\u0006\u0010\u0010\u001a\u00020\u0005\u0012\u0006\u0010\u0011\u001a\u00020\u0005\u00a2\u0006\u0002\u0010\u0012J\t\u0010!\u001a\u00020\u0003H\u00c6\u0003J\t\u0010\"\u001a\u00020\u0003H\u00c6\u0003J\t\u0010#\u001a\u00020\u0005H\u00c6\u0003J\t\u0010$\u001a\u00020\u0005H\u00c6\u0003J\t\u0010%\u001a\u00020\u0005H\u00c6\u0003J\t\u0010&\u001a\u00020\u0005H\u00c6\u0003J\t\u0010\'\u001a\u00020\u0005H\u00c6\u0003J\t\u0010(\u001a\u00020\u0005H\u00c6\u0003J\t\u0010)\u001a\u00020\u0005H\u00c6\u0003J\t\u0010*\u001a\u00020\nH\u00c6\u0003J\t\u0010+\u001a\u00020\u0003H\u00c6\u0003J\t\u0010,\u001a\u00020\u0005H\u00c6\u0003J\t\u0010-\u001a\u00020\u0005H\u00c6\u0003J\u008b\u0001\u0010.\u001a\u00020\u00002\b\b\u0002\u0010\u0002\u001a\u00020\u00032\b\b\u0002\u0010\u0004\u001a\u00020\u00052\b\b\u0002\u0010\u0006\u001a\u00020\u00052\b\b\u0002\u0010\u0007\u001a\u00020\u00052\b\b\u0002\u0010\b\u001a\u00020\u00052\b\b\u0002\u0010\t\u001a\u00020\n2\b\b\u0002\u0010\u000b\u001a\u00020\u00032\b\b\u0002\u0010\f\u001a\u00020\u00052\b\b\u0002\u0010\r\u001a\u00020\u00052\b\b\u0002\u0010\u000e\u001a\u00020\u00032\b\b\u0002\u0010\u000f\u001a\u00020\u00052\b\b\u0002\u0010\u0010\u001a\u00020\u00052\b\b\u0002\u0010\u0011\u001a\u00020\u0005H\u00c6\u0001J\u0013\u0010/\u001a\u00020\u00052\b\u00100\u001a\u0004\u0018\u00010\u0001H\u00d6\u0003J\t\u00101\u001a\u00020\nH\u00d6\u0001J\t\u00102\u001a\u00020\u0003H\u00d6\u0001R\u0011\u0010\u0002\u001a\u00020\u0003\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0013\u0010\u0014R\u0011\u0010\u0006\u001a\u00020\u0005\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0015\u0010\u0016R\u0011\u0010\u0004\u001a\u00020\u0005\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0017\u0010\u0016R\u0011\u0010\u0007\u001a\u00020\u0005\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0007\u0010\u0016R\u0011\u0010\b\u001a\u00020\u0005\u00a2\u0006\b\n\u0000\u001a\u0004\b\b\u0010\u0016R\u0011\u0010\t\u001a\u00020\n\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0018\u0010\u0019R\u0011\u0010\u000b\u001a\u00020\u0003\u00a2\u0006\b\n\u0000\u001a\u0004\b\u001a\u0010\u0014R\u0011\u0010\u0010\u001a\u00020\u0005\u00a2\u0006\b\n\u0000\u001a\u0004\b\u001b\u0010\u0016R\u0011\u0010\f\u001a\u00020\u0005\u00a2\u0006\b\n\u0000\u001a\u0004\b\u001c\u0010\u0016R\u0011\u0010\r\u001a\u00020\u0005\u00a2\u0006\b\n\u0000\u001a\u0004\b\u001d\u0010\u0016R\u0011\u0010\u0011\u001a\u00020\u0005\u00a2\u0006\b\n\u0000\u001a\u0004\b\u001e\u0010\u0016R\u0011\u0010\u000e\u001a\u00020\u0003\u00a2\u0006\b\n\u0000\u001a\u0004\b\u001f\u0010\u0014R\u0011\u0010\u000f\u001a\u00020\u0005\u00a2\u0006\b\n\u0000\u001a\u0004\b \u0010\u0016\u00a8\u00063"}, d2 = {"Lcom/lijianqiang12/silent/data/model/net/pojos/AppUpdate;", "", "downloadUrl", "", "huaweiValid", "", "honorValid", "isBeta", "isForce", "lastestVersionCode", "", "lastestVersionName", "miuiValid", "oppoValid", "updateContent", "vivoValid", "meizuValid", "samsungValid", "(Ljava/lang/String;ZZZZILjava/lang/String;ZZLjava/lang/String;ZZZ)V", "getDownloadUrl", "()Ljava/lang/String;", "getHonorValid", "()Z", "getHuaweiValid", "getLastestVersionCode", "()I", "getLastestVersionName", "getMeizuValid", "getMiuiValid", "getOppoValid", "getSamsungValid", "getUpdateContent", "getVivoValid", "component1", "component10", "component11", "component12", "component13", "component2", "component3", "component4", "component5", "component6", "component7", "component8", "component9", "copy", "equals", "other", "hashCode", "toString", "data_debug"})
public final class AppUpdate {
    @org.jetbrains.annotations.NotNull()
    private final java.lang.String downloadUrl = null;
    private final boolean huaweiValid = false;
    private final boolean honorValid = false;
    private final boolean isBeta = false;
    private final boolean isForce = false;
    private final int lastestVersionCode = 0;
    @org.jetbrains.annotations.NotNull()
    private final java.lang.String lastestVersionName = null;
    private final boolean miuiValid = false;
    private final boolean oppoValid = false;
    @org.jetbrains.annotations.NotNull()
    private final java.lang.String updateContent = null;
    private final boolean vivoValid = false;
    private final boolean meizuValid = false;
    private final boolean samsungValid = false;
    
    public AppUpdate(@org.jetbrains.annotations.NotNull()
    java.lang.String downloadUrl, boolean huaweiValid, boolean honorValid, boolean isBeta, boolean isForce, int lastestVersionCode, @org.jetbrains.annotations.NotNull()
    java.lang.String lastestVersionName, boolean miuiValid, boolean oppoValid, @org.jetbrains.annotations.NotNull()
    java.lang.String updateContent, boolean vivoValid, boolean meizuValid, boolean samsungValid) {
        super();
    }
    
    @org.jetbrains.annotations.NotNull()
    public final java.lang.String getDownloadUrl() {
        return null;
    }
    
    public final boolean getHuaweiValid() {
        return false;
    }
    
    public final boolean getHonorValid() {
        return false;
    }
    
    public final boolean isBeta() {
        return false;
    }
    
    public final boolean isForce() {
        return false;
    }
    
    public final int getLastestVersionCode() {
        return 0;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final java.lang.String getLastestVersionName() {
        return null;
    }
    
    public final boolean getMiuiValid() {
        return false;
    }
    
    public final boolean getOppoValid() {
        return false;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final java.lang.String getUpdateContent() {
        return null;
    }
    
    public final boolean getVivoValid() {
        return false;
    }
    
    public final boolean getMeizuValid() {
        return false;
    }
    
    public final boolean getSamsungValid() {
        return false;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final java.lang.String component1() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final java.lang.String component10() {
        return null;
    }
    
    public final boolean component11() {
        return false;
    }
    
    public final boolean component12() {
        return false;
    }
    
    public final boolean component13() {
        return false;
    }
    
    public final boolean component2() {
        return false;
    }
    
    public final boolean component3() {
        return false;
    }
    
    public final boolean component4() {
        return false;
    }
    
    public final boolean component5() {
        return false;
    }
    
    public final int component6() {
        return 0;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final java.lang.String component7() {
        return null;
    }
    
    public final boolean component8() {
        return false;
    }
    
    public final boolean component9() {
        return false;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final com.lijianqiang12.silent.data.model.net.pojos.AppUpdate copy(@org.jetbrains.annotations.NotNull()
    java.lang.String downloadUrl, boolean huaweiValid, boolean honorValid, boolean isBeta, boolean isForce, int lastestVersionCode, @org.jetbrains.annotations.NotNull()
    java.lang.String lastestVersionName, boolean miuiValid, boolean oppoValid, @org.jetbrains.annotations.NotNull()
    java.lang.String updateContent, boolean vivoValid, boolean meizuValid, boolean samsungValid) {
        return null;
    }
    
    @java.lang.Override()
    public boolean equals(@org.jetbrains.annotations.Nullable()
    java.lang.Object other) {
        return false;
    }
    
    @java.lang.Override()
    public int hashCode() {
        return 0;
    }
    
    @java.lang.Override()
    @org.jetbrains.annotations.NotNull()
    public java.lang.String toString() {
        return null;
    }
}