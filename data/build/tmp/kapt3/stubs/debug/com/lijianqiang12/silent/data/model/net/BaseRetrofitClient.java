package com.lijianqiang12.silent.data.model.net;

@kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u00000\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0002\b\u0005\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\u000e\n\u0002\b\u0002\n\u0002\u0010\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0002\b&\u0018\u0000 \u00122\u00020\u0001:\u0001\u0012B\u0005\u00a2\u0006\u0002\u0010\u0002J)\u0010\u0007\u001a\u0002H\b\"\u0004\b\u0000\u0010\b2\f\u0010\t\u001a\b\u0012\u0004\u0012\u0002H\b0\n2\u0006\u0010\u000b\u001a\u00020\fH\u0016\u00a2\u0006\u0002\u0010\rJ\u0010\u0010\u000e\u001a\u00020\u000f2\u0006\u0010\u0010\u001a\u00020\u0011H&R\u0014\u0010\u0003\u001a\u00020\u00048BX\u0082\u0004\u00a2\u0006\u0006\u001a\u0004\b\u0005\u0010\u0006\u00a8\u0006\u0013"}, d2 = {"Lcom/lijianqiang12/silent/data/model/net/BaseRetrofitClient;", "", "()V", "client", "Lokhttp3/OkHttpClient;", "getClient", "()Lokhttp3/OkHttpClient;", "getService", "Service", "serviceClass", "Ljava/lang/Class;", "baseUrl", "", "(Ljava/lang/Class;Ljava/lang/String;)Ljava/lang/Object;", "handleBuilder", "", "builder", "Lokhttp3/OkHttpClient$Builder;", "CLIENT", "data_debug"})
public abstract class BaseRetrofitClient {
    public static final long TIME_OUT = 10L;
    @org.jetbrains.annotations.NotNull()
    public static final com.lijianqiang12.silent.data.model.net.BaseRetrofitClient.CLIENT CLIENT = null;
    
    public BaseRetrofitClient() {
        super();
    }
    
    private final okhttp3.OkHttpClient getClient() {
        return null;
    }
    
    /**
     * 以便对builder可以再扩展
     */
    public abstract void handleBuilder(@org.jetbrains.annotations.NotNull()
    okhttp3.OkHttpClient.Builder builder);
    
    public <Service extends java.lang.Object>Service getService(@org.jetbrains.annotations.NotNull()
    java.lang.Class<Service> serviceClass, @org.jetbrains.annotations.NotNull()
    java.lang.String baseUrl) {
        return null;
    }
    
    @kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000\u0012\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0002\b\u0002\n\u0002\u0010\t\n\u0000\b\u0086\u0003\u0018\u00002\u00020\u0001B\u0007\b\u0002\u00a2\u0006\u0002\u0010\u0002R\u000e\u0010\u0003\u001a\u00020\u0004X\u0086T\u00a2\u0006\u0002\n\u0000\u00a8\u0006\u0005"}, d2 = {"Lcom/lijianqiang12/silent/data/model/net/BaseRetrofitClient$CLIENT;", "", "()V", "TIME_OUT", "", "data_debug"})
    public static final class CLIENT {
        
        private CLIENT() {
            super();
        }
    }
}