package com.lijianqiang12.silent.data.model.net.pojos;

@kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000(\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0000\n\u0002\u0010\t\n\u0000\n\u0002\u0010\u000e\n\u0002\b\u0002\n\u0002\u0010\b\n\u0002\b\u0006\n\u0002\u0010\u000b\n\u0002\bN\b\u0086\b\u0018\u00002\u00020\u0001B\u00dd\u0001\u0012\u0006\u0010\u0002\u001a\u00020\u0003\u0012\u0006\u0010\u0004\u001a\u00020\u0005\u0012\u0006\u0010\u0006\u001a\u00020\u0005\u0012\u0006\u0010\u0007\u001a\u00020\b\u0012\u0006\u0010\t\u001a\u00020\b\u0012\u0006\u0010\n\u001a\u00020\b\u0012\u0006\u0010\u000b\u001a\u00020\b\u0012\u0006\u0010\f\u001a\u00020\b\u0012\u0006\u0010\r\u001a\u00020\u0005\u0012\u0006\u0010\u000e\u001a\u00020\u000f\u0012\u0006\u0010\u0010\u001a\u00020\u000f\u0012\u0006\u0010\u0011\u001a\u00020\b\u0012\u0006\u0010\u0012\u001a\u00020\b\u0012\u0006\u0010\u0013\u001a\u00020\u0003\u0012\u0006\u0010\u0014\u001a\u00020\u0003\u0012\u0006\u0010\u0015\u001a\u00020\u000f\u0012\u0006\u0010\u0016\u001a\u00020\u000f\u0012\u0006\u0010\u0017\u001a\u00020\u000f\u0012\u0006\u0010\u0018\u001a\u00020\u000f\u0012\u0006\u0010\u0019\u001a\u00020\u000f\u0012\u0006\u0010\u001a\u001a\u00020\u000f\u0012\u0006\u0010\u001b\u001a\u00020\u000f\u0012\u0006\u0010\u001c\u001a\u00020\u000f\u0012\u0006\u0010\u001d\u001a\u00020\b\u0012\u0006\u0010\u001e\u001a\u00020\b\u0012\u0006\u0010\u001f\u001a\u00020\u0003\u0012\u0006\u0010 \u001a\u00020\b\u00a2\u0006\u0002\u0010!J\t\u0010=\u001a\u00020\u0003H\u00c6\u0003J\t\u0010>\u001a\u00020\u000fH\u00c6\u0003J\t\u0010?\u001a\u00020\u000fH\u00c6\u0003J\t\u0010@\u001a\u00020\bH\u00c6\u0003J\t\u0010A\u001a\u00020\bH\u00c6\u0003J\t\u0010B\u001a\u00020\u0003H\u00c6\u0003J\t\u0010C\u001a\u00020\u0003H\u00c6\u0003J\t\u0010D\u001a\u00020\u000fH\u00c6\u0003J\t\u0010E\u001a\u00020\u000fH\u00c6\u0003J\t\u0010F\u001a\u00020\u000fH\u00c6\u0003J\t\u0010G\u001a\u00020\u000fH\u00c6\u0003J\t\u0010H\u001a\u00020\u0005H\u00c6\u0003J\t\u0010I\u001a\u00020\u000fH\u00c6\u0003J\t\u0010J\u001a\u00020\u000fH\u00c6\u0003J\t\u0010K\u001a\u00020\u000fH\u00c6\u0003J\t\u0010L\u001a\u00020\u000fH\u00c6\u0003J\t\u0010M\u001a\u00020\bH\u00c6\u0003J\t\u0010N\u001a\u00020\bH\u00c6\u0003J\t\u0010O\u001a\u00020\u0003H\u00c6\u0003J\t\u0010P\u001a\u00020\bH\u00c6\u0003J\t\u0010Q\u001a\u00020\u0005H\u00c6\u0003J\t\u0010R\u001a\u00020\bH\u00c6\u0003J\t\u0010S\u001a\u00020\bH\u00c6\u0003J\t\u0010T\u001a\u00020\bH\u00c6\u0003J\t\u0010U\u001a\u00020\bH\u00c6\u0003J\t\u0010V\u001a\u00020\bH\u00c6\u0003J\t\u0010W\u001a\u00020\u0005H\u00c6\u0003J\u0097\u0002\u0010X\u001a\u00020\u00002\b\b\u0002\u0010\u0002\u001a\u00020\u00032\b\b\u0002\u0010\u0004\u001a\u00020\u00052\b\b\u0002\u0010\u0006\u001a\u00020\u00052\b\b\u0002\u0010\u0007\u001a\u00020\b2\b\b\u0002\u0010\t\u001a\u00020\b2\b\b\u0002\u0010\n\u001a\u00020\b2\b\b\u0002\u0010\u000b\u001a\u00020\b2\b\b\u0002\u0010\f\u001a\u00020\b2\b\b\u0002\u0010\r\u001a\u00020\u00052\b\b\u0002\u0010\u000e\u001a\u00020\u000f2\b\b\u0002\u0010\u0010\u001a\u00020\u000f2\b\b\u0002\u0010\u0011\u001a\u00020\b2\b\b\u0002\u0010\u0012\u001a\u00020\b2\b\b\u0002\u0010\u0013\u001a\u00020\u00032\b\b\u0002\u0010\u0014\u001a\u00020\u00032\b\b\u0002\u0010\u0015\u001a\u00020\u000f2\b\b\u0002\u0010\u0016\u001a\u00020\u000f2\b\b\u0002\u0010\u0017\u001a\u00020\u000f2\b\b\u0002\u0010\u0018\u001a\u00020\u000f2\b\b\u0002\u0010\u0019\u001a\u00020\u000f2\b\b\u0002\u0010\u001a\u001a\u00020\u000f2\b\b\u0002\u0010\u001b\u001a\u00020\u000f2\b\b\u0002\u0010\u001c\u001a\u00020\u000f2\b\b\u0002\u0010\u001d\u001a\u00020\b2\b\b\u0002\u0010\u001e\u001a\u00020\b2\b\b\u0002\u0010\u001f\u001a\u00020\u00032\b\b\u0002\u0010 \u001a\u00020\bH\u00c6\u0001J\u0013\u0010Y\u001a\u00020\u000f2\b\u0010Z\u001a\u0004\u0018\u00010\u0001H\u00d6\u0003J\t\u0010[\u001a\u00020\bH\u00d6\u0001J\t\u0010\\\u001a\u00020\u0005H\u00d6\u0001R\u0011\u0010\r\u001a\u00020\u0005\u00a2\u0006\b\n\u0000\u001a\u0004\b\"\u0010#R\u0011\u0010\u0016\u001a\u00020\u000f\u00a2\u0006\b\n\u0000\u001a\u0004\b$\u0010%R\u0011\u0010\u0014\u001a\u00020\u0003\u00a2\u0006\b\n\u0000\u001a\u0004\b&\u0010\'R\u0011\u0010\u001c\u001a\u00020\u000f\u00a2\u0006\b\n\u0000\u001a\u0004\b(\u0010%R\u0011\u0010\u0012\u001a\u00020\b\u00a2\u0006\b\n\u0000\u001a\u0004\b)\u0010*R\u0011\u0010\u001a\u001a\u00020\u000f\u00a2\u0006\b\n\u0000\u001a\u0004\b+\u0010%R\u0011\u0010\u000e\u001a\u00020\u000f\u00a2\u0006\b\n\u0000\u001a\u0004\b\u000e\u0010%R\u0011\u0010\u0017\u001a\u00020\u000f\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0017\u0010%R\u0011\u0010\u0010\u001a\u00020\u000f\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0010\u0010%R\u0011\u0010\u0018\u001a\u00020\u000f\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0018\u0010%R\u0011\u0010\u0013\u001a\u00020\u0003\u00a2\u0006\b\n\u0000\u001a\u0004\b,\u0010\'R\u0011\u0010\u001b\u001a\u00020\u000f\u00a2\u0006\b\n\u0000\u001a\u0004\b-\u0010%R\u0011\u0010\u0011\u001a\u00020\b\u00a2\u0006\b\n\u0000\u001a\u0004\b.\u0010*R\u0011\u0010\u0019\u001a\u00020\u000f\u00a2\u0006\b\n\u0000\u001a\u0004\b/\u0010%R\u0011\u0010\u001e\u001a\u00020\b\u00a2\u0006\b\n\u0000\u001a\u0004\b0\u0010*R\u0011\u0010\u001f\u001a\u00020\u0003\u00a2\u0006\b\n\u0000\u001a\u0004\b1\u0010\'R\u0011\u0010\u0006\u001a\u00020\u0005\u00a2\u0006\b\n\u0000\u001a\u0004\b2\u0010#R\u0011\u0010\n\u001a\u00020\b\u00a2\u0006\b\n\u0000\u001a\u0004\b3\u0010*R\u0011\u0010\u0004\u001a\u00020\u0005\u00a2\u0006\b\n\u0000\u001a\u0004\b4\u0010#R\u0011\u0010\f\u001a\u00020\b\u00a2\u0006\b\n\u0000\u001a\u0004\b5\u0010*R\u0011\u0010\u000b\u001a\u00020\b\u00a2\u0006\b\n\u0000\u001a\u0004\b6\u0010*R\u0011\u0010\t\u001a\u00020\b\u00a2\u0006\b\n\u0000\u001a\u0004\b7\u0010*R\u0011\u0010\u0007\u001a\u00020\b\u00a2\u0006\b\n\u0000\u001a\u0004\b8\u0010*R\u0011\u0010\u001d\u001a\u00020\b\u00a2\u0006\b\n\u0000\u001a\u0004\b9\u0010*R\u0011\u0010\u0002\u001a\u00020\u0003\u00a2\u0006\b\n\u0000\u001a\u0004\b:\u0010\'R\u0011\u0010 \u001a\u00020\b\u00a2\u0006\b\n\u0000\u001a\u0004\b;\u0010*R\u0011\u0010\u0015\u001a\u00020\u000f\u00a2\u0006\b\n\u0000\u001a\u0004\b<\u0010%\u00a8\u0006]"}, d2 = {"Lcom/lijianqiang12/silent/data/model/net/pojos/PullTomatoResult;", "", "uuid", "", "tomatoIndexId", "", "title", "tomatoWorkLength", "", "tomatoRestLength", "tomatoCount", "tomatoLongRestPerCount", "tomatoLongRestLength", "bgUrl", "isRemoveNotification", "", "isSilent", "startVoiceNotify", "endVoiceNotify", "startShakeNotify", "endShakeNotify", "whiteFollowGlobal", "bgUrlFollowGlobal", "isRemoveNotificationFollowGlobal", "isSilentFollowGlobal", "startVoiceNotifyFollowGlobal", "endVoiceNotifyFollowGlobal", "startShakeNotifyFollowGlobal", "endShakeNotifyFollowGlobal", "trend", "syncState", "syncTime", "version", "(JLjava/lang/String;Ljava/lang/String;IIIIILjava/lang/String;ZZIIJJZZZZZZZZIIJI)V", "getBgUrl", "()Ljava/lang/String;", "getBgUrlFollowGlobal", "()Z", "getEndShakeNotify", "()J", "getEndShakeNotifyFollowGlobal", "getEndVoiceNotify", "()I", "getEndVoiceNotifyFollowGlobal", "getStartShakeNotify", "getStartShakeNotifyFollowGlobal", "getStartVoiceNotify", "getStartVoiceNotifyFollowGlobal", "getSyncState", "getSyncTime", "getTitle", "getTomatoCount", "getTomatoIndexId", "getTomatoLongRestLength", "getTomatoLongRestPerCount", "getTomatoRestLength", "getTomatoWorkLength", "getTrend", "getUuid", "getVersion", "getWhiteFollowGlobal", "component1", "component10", "component11", "component12", "component13", "component14", "component15", "component16", "component17", "component18", "component19", "component2", "component20", "component21", "component22", "component23", "component24", "component25", "component26", "component27", "component3", "component4", "component5", "component6", "component7", "component8", "component9", "copy", "equals", "other", "hashCode", "toString", "data_debug"})
public final class PullTomatoResult {
    private final long uuid = 0L;
    @org.jetbrains.annotations.NotNull()
    private final java.lang.String tomatoIndexId = null;
    @org.jetbrains.annotations.NotNull()
    private final java.lang.String title = null;
    private final int tomatoWorkLength = 0;
    private final int tomatoRestLength = 0;
    private final int tomatoCount = 0;
    private final int tomatoLongRestPerCount = 0;
    private final int tomatoLongRestLength = 0;
    @org.jetbrains.annotations.NotNull()
    private final java.lang.String bgUrl = null;
    private final boolean isRemoveNotification = false;
    private final boolean isSilent = false;
    private final int startVoiceNotify = 0;
    private final int endVoiceNotify = 0;
    private final long startShakeNotify = 0L;
    private final long endShakeNotify = 0L;
    private final boolean whiteFollowGlobal = false;
    private final boolean bgUrlFollowGlobal = false;
    private final boolean isRemoveNotificationFollowGlobal = false;
    private final boolean isSilentFollowGlobal = false;
    private final boolean startVoiceNotifyFollowGlobal = false;
    private final boolean endVoiceNotifyFollowGlobal = false;
    private final boolean startShakeNotifyFollowGlobal = false;
    private final boolean endShakeNotifyFollowGlobal = false;
    private final int trend = 0;
    private final int syncState = 0;
    private final long syncTime = 0L;
    private final int version = 0;
    
    public PullTomatoResult(long uuid, @org.jetbrains.annotations.NotNull()
    java.lang.String tomatoIndexId, @org.jetbrains.annotations.NotNull()
    java.lang.String title, int tomatoWorkLength, int tomatoRestLength, int tomatoCount, int tomatoLongRestPerCount, int tomatoLongRestLength, @org.jetbrains.annotations.NotNull()
    java.lang.String bgUrl, boolean isRemoveNotification, boolean isSilent, int startVoiceNotify, int endVoiceNotify, long startShakeNotify, long endShakeNotify, boolean whiteFollowGlobal, boolean bgUrlFollowGlobal, boolean isRemoveNotificationFollowGlobal, boolean isSilentFollowGlobal, boolean startVoiceNotifyFollowGlobal, boolean endVoiceNotifyFollowGlobal, boolean startShakeNotifyFollowGlobal, boolean endShakeNotifyFollowGlobal, int trend, int syncState, long syncTime, int version) {
        super();
    }
    
    public final long getUuid() {
        return 0L;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final java.lang.String getTomatoIndexId() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final java.lang.String getTitle() {
        return null;
    }
    
    public final int getTomatoWorkLength() {
        return 0;
    }
    
    public final int getTomatoRestLength() {
        return 0;
    }
    
    public final int getTomatoCount() {
        return 0;
    }
    
    public final int getTomatoLongRestPerCount() {
        return 0;
    }
    
    public final int getTomatoLongRestLength() {
        return 0;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final java.lang.String getBgUrl() {
        return null;
    }
    
    public final boolean isRemoveNotification() {
        return false;
    }
    
    public final boolean isSilent() {
        return false;
    }
    
    public final int getStartVoiceNotify() {
        return 0;
    }
    
    public final int getEndVoiceNotify() {
        return 0;
    }
    
    public final long getStartShakeNotify() {
        return 0L;
    }
    
    public final long getEndShakeNotify() {
        return 0L;
    }
    
    public final boolean getWhiteFollowGlobal() {
        return false;
    }
    
    public final boolean getBgUrlFollowGlobal() {
        return false;
    }
    
    public final boolean isRemoveNotificationFollowGlobal() {
        return false;
    }
    
    public final boolean isSilentFollowGlobal() {
        return false;
    }
    
    public final boolean getStartVoiceNotifyFollowGlobal() {
        return false;
    }
    
    public final boolean getEndVoiceNotifyFollowGlobal() {
        return false;
    }
    
    public final boolean getStartShakeNotifyFollowGlobal() {
        return false;
    }
    
    public final boolean getEndShakeNotifyFollowGlobal() {
        return false;
    }
    
    public final int getTrend() {
        return 0;
    }
    
    public final int getSyncState() {
        return 0;
    }
    
    public final long getSyncTime() {
        return 0L;
    }
    
    public final int getVersion() {
        return 0;
    }
    
    public final long component1() {
        return 0L;
    }
    
    public final boolean component10() {
        return false;
    }
    
    public final boolean component11() {
        return false;
    }
    
    public final int component12() {
        return 0;
    }
    
    public final int component13() {
        return 0;
    }
    
    public final long component14() {
        return 0L;
    }
    
    public final long component15() {
        return 0L;
    }
    
    public final boolean component16() {
        return false;
    }
    
    public final boolean component17() {
        return false;
    }
    
    public final boolean component18() {
        return false;
    }
    
    public final boolean component19() {
        return false;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final java.lang.String component2() {
        return null;
    }
    
    public final boolean component20() {
        return false;
    }
    
    public final boolean component21() {
        return false;
    }
    
    public final boolean component22() {
        return false;
    }
    
    public final boolean component23() {
        return false;
    }
    
    public final int component24() {
        return 0;
    }
    
    public final int component25() {
        return 0;
    }
    
    public final long component26() {
        return 0L;
    }
    
    public final int component27() {
        return 0;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final java.lang.String component3() {
        return null;
    }
    
    public final int component4() {
        return 0;
    }
    
    public final int component5() {
        return 0;
    }
    
    public final int component6() {
        return 0;
    }
    
    public final int component7() {
        return 0;
    }
    
    public final int component8() {
        return 0;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final java.lang.String component9() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final com.lijianqiang12.silent.data.model.net.pojos.PullTomatoResult copy(long uuid, @org.jetbrains.annotations.NotNull()
    java.lang.String tomatoIndexId, @org.jetbrains.annotations.NotNull()
    java.lang.String title, int tomatoWorkLength, int tomatoRestLength, int tomatoCount, int tomatoLongRestPerCount, int tomatoLongRestLength, @org.jetbrains.annotations.NotNull()
    java.lang.String bgUrl, boolean isRemoveNotification, boolean isSilent, int startVoiceNotify, int endVoiceNotify, long startShakeNotify, long endShakeNotify, boolean whiteFollowGlobal, boolean bgUrlFollowGlobal, boolean isRemoveNotificationFollowGlobal, boolean isSilentFollowGlobal, boolean startVoiceNotifyFollowGlobal, boolean endVoiceNotifyFollowGlobal, boolean startShakeNotifyFollowGlobal, boolean endShakeNotifyFollowGlobal, int trend, int syncState, long syncTime, int version) {
        return null;
    }
    
    @java.lang.Override()
    public boolean equals(@org.jetbrains.annotations.Nullable()
    java.lang.Object other) {
        return false;
    }
    
    @java.lang.Override()
    public int hashCode() {
        return 0;
    }
    
    @java.lang.Override()
    @org.jetbrains.annotations.NotNull()
    public java.lang.String toString() {
        return null;
    }
}