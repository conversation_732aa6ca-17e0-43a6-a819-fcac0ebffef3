package com.lijianqiang12.silent.data.model.db;

@kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000 \n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0000\n\u0002\u0010\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0010\t\n\u0002\b\u0002\bg\u0018\u00002\u00020\u0001J\u0016\u0010\u0002\u001a\u00020\u00032\u0006\u0010\u0004\u001a\u00020\u0005H\u00a7@\u00a2\u0006\u0002\u0010\u0006J\u0016\u0010\u0007\u001a\u00020\b2\u0006\u0010\u0004\u001a\u00020\u0005H\u00a7@\u00a2\u0006\u0002\u0010\u0006J\u0016\u0010\t\u001a\u00020\u00032\u0006\u0010\u0004\u001a\u00020\u0005H\u00a7@\u00a2\u0006\u0002\u0010\u0006\u00a8\u0006\n"}, d2 = {"Lcom/lijianqiang12/silent/data/model/db/DenyPageDao;", "", "deleteDenyPage", "", "DenyPage", "Lcom/lijianqiang12/silent/data/model/db/DenyPage;", "(Lcom/lijianqiang12/silent/data/model/db/DenyPage;Lkotlin/coroutines/Continuation;)Ljava/lang/Object;", "insertDenyPage", "", "updateDenyPage", "data_debug"})
@androidx.room.Dao()
public abstract interface DenyPageDao {
    
    @androidx.room.Insert(onConflict = 5)
    @org.jetbrains.annotations.Nullable()
    public abstract java.lang.Object insertDenyPage(@org.jetbrains.annotations.NotNull()
    com.lijianqiang12.silent.data.model.db.DenyPage DenyPage, @org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super java.lang.Long> $completion);
    
    @androidx.room.Update()
    @org.jetbrains.annotations.Nullable()
    public abstract java.lang.Object updateDenyPage(@org.jetbrains.annotations.NotNull()
    com.lijianqiang12.silent.data.model.db.DenyPage DenyPage, @org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super kotlin.Unit> $completion);
    
    @androidx.room.Delete()
    @org.jetbrains.annotations.Nullable()
    public abstract java.lang.Object deleteDenyPage(@org.jetbrains.annotations.NotNull()
    com.lijianqiang12.silent.data.model.db.DenyPage DenyPage, @org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super kotlin.Unit> $completion);
}