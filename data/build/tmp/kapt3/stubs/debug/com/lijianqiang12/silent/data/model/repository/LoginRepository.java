package com.lijianqiang12.silent.data.model.repository;

@kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u00004\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0002\u0010\u000e\n\u0000\n\u0002\u0010\b\n\u0002\b\u0003\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0002\b\f\u0018\u0000 \u001b2\u00020\u0001:\u0001\u001bB\u000f\b\u0002\u0012\u0006\u0010\u0002\u001a\u00020\u0003\u00a2\u0006\u0002\u0010\u0004J$\u0010\u0005\u001a\b\u0012\u0004\u0012\u00020\u00070\u00062\u0006\u0010\b\u001a\u00020\t2\u0006\u0010\n\u001a\u00020\u0007H\u0086@\u00a2\u0006\u0002\u0010\u000bJ\u0014\u0010\f\u001a\b\u0012\u0004\u0012\u00020\r0\u0006H\u0086@\u00a2\u0006\u0002\u0010\u000eJD\u0010\u000f\u001a\b\u0012\u0004\u0012\u00020\u00100\u00062\u0006\u0010\u0011\u001a\u00020\t2\u0006\u0010\u0012\u001a\u00020\u00072\u0006\u0010\u0013\u001a\u00020\u00072\u0006\u0010\u0014\u001a\u00020\u00072\u0006\u0010\u0015\u001a\u00020\u00072\u0006\u0010\u0016\u001a\u00020\tH\u0086@\u00a2\u0006\u0002\u0010\u0017J,\u0010\u0018\u001a\b\u0012\u0004\u0012\u00020\u00100\u00062\u0006\u0010\n\u001a\u00020\u00072\u0006\u0010\u0019\u001a\u00020\u00072\u0006\u0010\u0016\u001a\u00020\tH\u0086@\u00a2\u0006\u0002\u0010\u001aR\u000e\u0010\u0002\u001a\u00020\u0003X\u0082\u0004\u00a2\u0006\u0002\n\u0000\u00a8\u0006\u001c"}, d2 = {"Lcom/lijianqiang12/silent/data/model/repository/LoginRepository;", "", "appContext", "Landroid/content/Context;", "(Landroid/content/Context;)V", "getVerifyCode", "Lcom/lijianqiang12/silent/data/model/net/pojos/ApiResponse;", "", "codeType", "", "phone", "(ILjava/lang/String;Lkotlin/coroutines/Continuation;)Ljava/lang/Object;", "refreshState", "Lcom/lijianqiang12/silent/data/model/net/pojos/RefreshStateResponse;", "(Lkotlin/coroutines/Continuation;)Ljava/lang/Object;", "socialAccountLogin", "Lcom/lijianqiang12/silent/data/model/net/pojos/LoginResponse;", "socialType", "uid", "username", "avatar", "gender", "inviteCode", "(ILjava/lang/String;Ljava/lang/String;Ljava/lang/String;Ljava/lang/String;ILkotlin/coroutines/Continuation;)Ljava/lang/Object;", "verifyCode", "code", "(Ljava/lang/String;Ljava/lang/String;ILkotlin/coroutines/Continuation;)Ljava/lang/Object;", "Companion", "data_debug"})
public final class LoginRepository {
    @org.jetbrains.annotations.NotNull()
    private final android.content.Context appContext = null;
    @kotlin.jvm.Volatile()
    @org.jetbrains.annotations.Nullable()
    private static volatile com.lijianqiang12.silent.data.model.repository.LoginRepository instance;
    @org.jetbrains.annotations.NotNull()
    public static final com.lijianqiang12.silent.data.model.repository.LoginRepository.Companion Companion = null;
    
    private LoginRepository(android.content.Context appContext) {
        super();
    }
    
    @org.jetbrains.annotations.Nullable()
    public final java.lang.Object getVerifyCode(int codeType, @org.jetbrains.annotations.NotNull()
    java.lang.String phone, @org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super com.lijianqiang12.silent.data.model.net.pojos.ApiResponse<java.lang.String>> $completion) {
        return null;
    }
    
    @org.jetbrains.annotations.Nullable()
    public final java.lang.Object verifyCode(@org.jetbrains.annotations.NotNull()
    java.lang.String phone, @org.jetbrains.annotations.NotNull()
    java.lang.String code, int inviteCode, @org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super com.lijianqiang12.silent.data.model.net.pojos.ApiResponse<com.lijianqiang12.silent.data.model.net.pojos.LoginResponse>> $completion) {
        return null;
    }
    
    @org.jetbrains.annotations.Nullable()
    public final java.lang.Object socialAccountLogin(int socialType, @org.jetbrains.annotations.NotNull()
    java.lang.String uid, @org.jetbrains.annotations.NotNull()
    java.lang.String username, @org.jetbrains.annotations.NotNull()
    java.lang.String avatar, @org.jetbrains.annotations.NotNull()
    java.lang.String gender, int inviteCode, @org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super com.lijianqiang12.silent.data.model.net.pojos.ApiResponse<com.lijianqiang12.silent.data.model.net.pojos.LoginResponse>> $completion) {
        return null;
    }
    
    @org.jetbrains.annotations.Nullable()
    public final java.lang.Object refreshState(@org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super com.lijianqiang12.silent.data.model.net.pojos.ApiResponse<com.lijianqiang12.silent.data.model.net.pojos.RefreshStateResponse>> $completion) {
        return null;
    }
    
    @kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000\u001a\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0000\b\u0086\u0003\u0018\u00002\u00020\u0001B\u0007\b\u0002\u00a2\u0006\u0002\u0010\u0002J\u000e\u0010\u0005\u001a\u00020\u00042\u0006\u0010\u0006\u001a\u00020\u0007R\u0010\u0010\u0003\u001a\u0004\u0018\u00010\u0004X\u0082\u000e\u00a2\u0006\u0002\n\u0000\u00a8\u0006\b"}, d2 = {"Lcom/lijianqiang12/silent/data/model/repository/LoginRepository$Companion;", "", "()V", "instance", "Lcom/lijianqiang12/silent/data/model/repository/LoginRepository;", "getInstance", "appContext", "Landroid/content/Context;", "data_debug"})
    public static final class Companion {
        
        private Companion() {
            super();
        }
        
        @org.jetbrains.annotations.NotNull()
        public final com.lijianqiang12.silent.data.model.repository.LoginRepository getInstance(@org.jetbrains.annotations.NotNull()
        android.content.Context appContext) {
            return null;
        }
    }
}