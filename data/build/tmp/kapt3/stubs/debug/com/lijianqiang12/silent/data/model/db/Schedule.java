package com.lijianqiang12.silent.data.model.db;

@kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000H\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0010\t\n\u0000\n\u0002\u0010\b\n\u0000\n\u0002\u0010\u000e\n\u0002\b\u0005\n\u0002\u0010\u000b\n\u0002\b\u0014\n\u0002\u0018\u0002\n\u0002\ba\n\u0002\u0010\u0000\n\u0002\b\u0003\n\u0002\u0010\u0002\n\u0002\b\u0003\b\u0087\b\u0018\u0000 \u008e\u00012\u00020\u0001:\u0002\u008e\u0001B\u0007\b\u0016\u00a2\u0006\u0002\u0010\u0002B\u000f\b\u0016\u0012\u0006\u0010\u0003\u001a\u00020\u0004\u00a2\u0006\u0002\u0010\u0005B\u00e5\u0001\u0012\u0006\u0010\u0006\u001a\u00020\u0007\u0012\u0006\u0010\b\u001a\u00020\t\u0012\u0006\u0010\n\u001a\u00020\u000b\u0012\u0006\u0010\f\u001a\u00020\u000b\u0012\u0006\u0010\r\u001a\u00020\u000b\u0012\u0006\u0010\u000e\u001a\u00020\t\u0012\u0006\u0010\u000f\u001a\u00020\t\u0012\u0006\u0010\u0010\u001a\u00020\u0011\u0012\u0006\u0010\u0012\u001a\u00020\u0011\u0012\u0006\u0010\u0013\u001a\u00020\u0011\u0012\u0006\u0010\u0014\u001a\u00020\u0011\u0012\u0006\u0010\u0015\u001a\u00020\u0011\u0012\u0006\u0010\u0016\u001a\u00020\u0011\u0012\u0006\u0010\u0017\u001a\u00020\u0011\u0012\u0006\u0010\u0018\u001a\u00020\u0011\u0012\u0006\u0010\u0019\u001a\u00020\u0011\u0012\u0006\u0010\u001a\u001a\u00020\t\u0012\u0006\u0010\u001b\u001a\u00020\t\u0012\u0006\u0010\u001c\u001a\u00020\u0011\u0012\u0006\u0010\u001d\u001a\u00020\u0011\u0012\u0006\u0010\u001e\u001a\u00020\t\u0012\u0006\u0010\u001f\u001a\u00020\u000b\u0012\u0006\u0010 \u001a\u00020\t\u0012\u0006\u0010!\u001a\u00020\t\u0012\u0006\u0010\"\u001a\u00020\u0007\u0012\u0006\u0010#\u001a\u00020\u0007\u0012\u0006\u0010$\u001a\u00020\t\u0012\u0006\u0010%\u001a\u00020&\u00a2\u0006\u0002\u0010\'J\t\u0010h\u001a\u00020\u0007H\u00c6\u0003J\t\u0010i\u001a\u00020\u0011H\u00c6\u0003J\t\u0010j\u001a\u00020\u0011H\u00c6\u0003J\t\u0010k\u001a\u00020\u0011H\u00c6\u0003J\t\u0010l\u001a\u00020\u0011H\u00c6\u0003J\t\u0010m\u001a\u00020\u0011H\u00c6\u0003J\t\u0010n\u001a\u00020\u0011H\u00c6\u0003J\t\u0010o\u001a\u00020\u0011H\u00c6\u0003J\t\u0010p\u001a\u00020\tH\u00c6\u0003J\t\u0010q\u001a\u00020\tH\u00c6\u0003J\t\u0010r\u001a\u00020\u0011H\u00c6\u0003J\t\u0010s\u001a\u00020\tH\u00c6\u0003J\t\u0010t\u001a\u00020\u0011H\u00c6\u0003J\t\u0010u\u001a\u00020\tH\u00c6\u0003J\t\u0010v\u001a\u00020\u000bH\u00c6\u0003J\t\u0010w\u001a\u00020\tH\u00c6\u0003J\t\u0010x\u001a\u00020\tH\u00c6\u0003J\t\u0010y\u001a\u00020\u0007H\u00c6\u0003J\t\u0010z\u001a\u00020\u0007H\u00c6\u0003J\t\u0010{\u001a\u00020\tH\u00c6\u0003J\t\u0010|\u001a\u00020&H\u00c6\u0003J\t\u0010}\u001a\u00020\u000bH\u00c6\u0003J\t\u0010~\u001a\u00020\u000bH\u00c6\u0003J\t\u0010\u007f\u001a\u00020\u000bH\u00c6\u0003J\n\u0010\u0080\u0001\u001a\u00020\tH\u00c6\u0003J\n\u0010\u0081\u0001\u001a\u00020\tH\u00c6\u0003J\n\u0010\u0082\u0001\u001a\u00020\u0011H\u00c6\u0003J\n\u0010\u0083\u0001\u001a\u00020\u0011H\u00c6\u0003J\u0007\u0010\u0084\u0001\u001a\u00020\u0000J\u00a2\u0002\u0010\u0084\u0001\u001a\u00020\u00002\b\b\u0002\u0010\u0006\u001a\u00020\u00072\b\b\u0002\u0010\b\u001a\u00020\t2\b\b\u0002\u0010\n\u001a\u00020\u000b2\b\b\u0002\u0010\f\u001a\u00020\u000b2\b\b\u0002\u0010\r\u001a\u00020\u000b2\b\b\u0002\u0010\u000e\u001a\u00020\t2\b\b\u0002\u0010\u000f\u001a\u00020\t2\b\b\u0002\u0010\u0010\u001a\u00020\u00112\b\b\u0002\u0010\u0012\u001a\u00020\u00112\b\b\u0002\u0010\u0013\u001a\u00020\u00112\b\b\u0002\u0010\u0014\u001a\u00020\u00112\b\b\u0002\u0010\u0015\u001a\u00020\u00112\b\b\u0002\u0010\u0016\u001a\u00020\u00112\b\b\u0002\u0010\u0017\u001a\u00020\u00112\b\b\u0002\u0010\u0018\u001a\u00020\u00112\b\b\u0002\u0010\u0019\u001a\u00020\u00112\b\b\u0002\u0010\u001a\u001a\u00020\t2\b\b\u0002\u0010\u001b\u001a\u00020\t2\b\b\u0002\u0010\u001c\u001a\u00020\u00112\b\b\u0002\u0010\u001d\u001a\u00020\u00112\b\b\u0002\u0010\u001e\u001a\u00020\t2\b\b\u0002\u0010\u001f\u001a\u00020\u000b2\b\b\u0002\u0010 \u001a\u00020\t2\b\b\u0002\u0010!\u001a\u00020\t2\b\b\u0002\u0010\"\u001a\u00020\u00072\b\b\u0002\u0010#\u001a\u00020\u00072\b\b\u0002\u0010$\u001a\u00020\t2\b\b\u0002\u0010%\u001a\u00020&H\u00c6\u0001J\t\u0010\u0085\u0001\u001a\u00020\tH\u0016J\u0016\u0010\u0086\u0001\u001a\u00020\u00112\n\u0010\u0087\u0001\u001a\u0005\u0018\u00010\u0088\u0001H\u00d6\u0003J\n\u0010\u0089\u0001\u001a\u00020\tH\u00d6\u0001J\t\u0010\u008a\u0001\u001a\u00020\u000bH\u0016J\u001b\u0010\u008b\u0001\u001a\u00030\u008c\u00012\u0006\u0010\u0003\u001a\u00020\u00042\u0007\u0010\u008d\u0001\u001a\u00020\tH\u0016R\u001e\u0010\u001e\u001a\u00020\t8\u0006@\u0006X\u0087\u000e\u00a2\u0006\u000e\n\u0000\u001a\u0004\b(\u0010)\"\u0004\b*\u0010+R\u001a\u0010\u001a\u001a\u00020\tX\u0086\u000e\u00a2\u0006\u000e\n\u0000\u001a\u0004\b,\u0010)\"\u0004\b-\u0010+R\u001a\u0010\u001b\u001a\u00020\tX\u0086\u000e\u00a2\u0006\u000e\n\u0000\u001a\u0004\b.\u0010)\"\u0004\b/\u0010+R\u001a\u0010\u0017\u001a\u00020\u0011X\u0086\u000e\u00a2\u0006\u000e\n\u0000\u001a\u0004\b0\u00101\"\u0004\b2\u00103R\u001e\u0010\u0006\u001a\u00020\u00078\u0006@\u0006X\u0087\u000e\u00a2\u0006\u000e\n\u0000\u001a\u0004\b4\u00105\"\u0004\b6\u00107R\u001a\u0010\u001d\u001a\u00020\u0011X\u0086\u000e\u00a2\u0006\u000e\n\u0000\u001a\u0004\b\u001d\u00101\"\u0004\b8\u00103R\u001a\u0010\u001c\u001a\u00020\u0011X\u0086\u000e\u00a2\u0006\u000e\n\u0000\u001a\u0004\b\u001c\u00101\"\u0004\b9\u00103R\u001a\u0010\u001f\u001a\u00020\u000bX\u0086\u000e\u00a2\u0006\u000e\n\u0000\u001a\u0004\b:\u0010;\"\u0004\b<\u0010=R\u001e\u0010%\u001a\u00020&8\u0006@\u0006X\u0087\u000e\u00a2\u0006\u000e\n\u0000\u001a\u0004\b>\u0010?\"\u0004\b@\u0010AR\u001a\u0010\u0013\u001a\u00020\u0011X\u0086\u000e\u00a2\u0006\u000e\n\u0000\u001a\u0004\bB\u00101\"\u0004\bC\u00103R\u001a\u0010\u0018\u001a\u00020\u0011X\u0086\u000e\u00a2\u0006\u000e\n\u0000\u001a\u0004\bD\u00101\"\u0004\bE\u00103R\u001a\u0010\r\u001a\u00020\u000bX\u0086\u000e\u00a2\u0006\u000e\n\u0000\u001a\u0004\bF\u0010;\"\u0004\bG\u0010=R\u001a\u0010\u000e\u001a\u00020\tX\u0086\u000e\u00a2\u0006\u000e\n\u0000\u001a\u0004\bH\u0010)\"\u0004\bI\u0010+R\u001a\u0010\u000f\u001a\u00020\tX\u0086\u000e\u00a2\u0006\u000e\n\u0000\u001a\u0004\bJ\u0010)\"\u0004\bK\u0010+R\u001a\u0010\u0012\u001a\u00020\u0011X\u0086\u000e\u00a2\u0006\u000e\n\u0000\u001a\u0004\bL\u00101\"\u0004\bM\u00103R\u001a\u0010!\u001a\u00020\tX\u0086\u000e\u00a2\u0006\u000e\n\u0000\u001a\u0004\bN\u0010)\"\u0004\bO\u0010+R\u001a\u0010\"\u001a\u00020\u0007X\u0086\u000e\u00a2\u0006\u000e\n\u0000\u001a\u0004\bP\u00105\"\u0004\bQ\u00107R\u001a\u0010\u0016\u001a\u00020\u0011X\u0086\u000e\u00a2\u0006\u000e\n\u0000\u001a\u0004\bR\u00101\"\u0004\bS\u00103R\u001a\u0010\n\u001a\u00020\u000bX\u0086\u000e\u00a2\u0006\u000e\n\u0000\u001a\u0004\bT\u0010;\"\u0004\bU\u0010=R\u001a\u0010\f\u001a\u00020\u000bX\u0086\u000e\u00a2\u0006\u000e\n\u0000\u001a\u0004\bV\u0010;\"\u0004\bW\u0010=R\u001a\u0010 \u001a\u00020\tX\u0086\u000e\u00a2\u0006\u000e\n\u0000\u001a\u0004\bX\u0010)\"\u0004\bY\u0010+R\u001a\u0010\u0014\u001a\u00020\u0011X\u0086\u000e\u00a2\u0006\u000e\n\u0000\u001a\u0004\bZ\u00101\"\u0004\b[\u00103R\u001a\u0010\u0019\u001a\u00020\u0011X\u0086\u000e\u00a2\u0006\u000e\n\u0000\u001a\u0004\b\\\u00101\"\u0004\b]\u00103R\u001a\u0010\b\u001a\u00020\tX\u0086\u000e\u00a2\u0006\u000e\n\u0000\u001a\u0004\b^\u0010)\"\u0004\b_\u0010+R\u001a\u0010#\u001a\u00020\u0007X\u0086\u000e\u00a2\u0006\u000e\n\u0000\u001a\u0004\b`\u00105\"\u0004\ba\u00107R\u001a\u0010\u0010\u001a\u00020\u0011X\u0086\u000e\u00a2\u0006\u000e\n\u0000\u001a\u0004\bb\u00101\"\u0004\bc\u00103R\u001a\u0010$\u001a\u00020\tX\u0086\u000e\u00a2\u0006\u000e\n\u0000\u001a\u0004\bd\u0010)\"\u0004\be\u0010+R\u001a\u0010\u0015\u001a\u00020\u0011X\u0086\u000e\u00a2\u0006\u000e\n\u0000\u001a\u0004\bf\u00101\"\u0004\bg\u00103\u00a8\u0006\u008f\u0001"}, d2 = {"Lcom/lijianqiang12/silent/data/model/db/Schedule;", "Landroid/os/Parcelable;", "()V", "parcel", "Landroid/os/Parcel;", "(Landroid/os/Parcel;)V", "id", "", "userId", "", "title", "", "tomatoIndexId", "scheduleIndexId", "startHour", "startMinute", "validate", "", "sunday", "monday", "tuesday", "wednesday", "thursday", "friday", "saturday", "useTomato", "endHour", "endMinute", "isRecycle", "isDenyChange", "denyChangeLength", "jumpDate", "trend", "syncState", "syncTime", "uuid", "version", "lockConfig", "Lcom/lijianqiang12/silent/data/model/db/LockConfig;", "(JILjava/lang/String;Ljava/lang/String;Ljava/lang/String;IIZZZZZZZZZIIZZILjava/lang/String;IIJJILcom/lijianqiang12/silent/data/model/db/LockConfig;)V", "getDenyChangeLength", "()I", "setDenyChangeLength", "(I)V", "getEndHour", "setEndHour", "getEndMinute", "setEndMinute", "getFriday", "()Z", "setFriday", "(Z)V", "getId", "()J", "setId", "(J)V", "setDenyChange", "setRecycle", "getJumpDate", "()Ljava/lang/String;", "setJumpDate", "(Ljava/lang/String;)V", "getLockConfig", "()Lcom/lijianqiang12/silent/data/model/db/LockConfig;", "setLockConfig", "(Lcom/lijianqiang12/silent/data/model/db/LockConfig;)V", "getMonday", "setMonday", "getSaturday", "setSaturday", "getScheduleIndexId", "setScheduleIndexId", "getStartHour", "setStartHour", "getStartMinute", "setStartMinute", "getSunday", "setSunday", "getSyncState", "setSyncState", "getSyncTime", "setSyncTime", "getThursday", "setThursday", "getTitle", "setTitle", "getTomatoIndexId", "setTomatoIndexId", "getTrend", "setTrend", "getTuesday", "setTuesday", "getUseTomato", "setUseTomato", "getUserId", "setUserId", "getUuid", "setUuid", "getValidate", "setValidate", "getVersion", "setVersion", "getWednesday", "setWednesday", "component1", "component10", "component11", "component12", "component13", "component14", "component15", "component16", "component17", "component18", "component19", "component2", "component20", "component21", "component22", "component23", "component24", "component25", "component26", "component27", "component28", "component3", "component4", "component5", "component6", "component7", "component8", "component9", "copy", "describeContents", "equals", "other", "", "hashCode", "toString", "writeToParcel", "", "flags", "CREATOR", "data_debug"})
@androidx.room.Entity(indices = {@androidx.room.Index(value = {"scheduleIndexId"}, unique = true)})
public final class Schedule implements android.os.Parcelable {
    @androidx.room.PrimaryKey(autoGenerate = true)
    private long id;
    private int userId;
    @org.jetbrains.annotations.NotNull()
    private java.lang.String title;
    @org.jetbrains.annotations.NotNull()
    private java.lang.String tomatoIndexId;
    @org.jetbrains.annotations.NotNull()
    private java.lang.String scheduleIndexId;
    private int startHour;
    private int startMinute;
    private boolean validate;
    private boolean sunday;
    private boolean monday;
    private boolean tuesday;
    private boolean wednesday;
    private boolean thursday;
    private boolean friday;
    private boolean saturday;
    private boolean useTomato;
    private int endHour;
    private int endMinute;
    private boolean isRecycle;
    private boolean isDenyChange;
    @androidx.room.ColumnInfo(defaultValue = "60")
    private int denyChangeLength;
    @org.jetbrains.annotations.NotNull()
    private java.lang.String jumpDate;
    private int trend;
    private int syncState;
    private long syncTime;
    private long uuid;
    private int version;
    @androidx.room.Embedded()
    @org.jetbrains.annotations.NotNull()
    private com.lijianqiang12.silent.data.model.db.LockConfig lockConfig;
    @org.jetbrains.annotations.NotNull()
    public static final com.lijianqiang12.silent.data.model.db.Schedule.CREATOR CREATOR = null;
    
    public Schedule(long id, int userId, @org.jetbrains.annotations.NotNull()
    java.lang.String title, @org.jetbrains.annotations.NotNull()
    java.lang.String tomatoIndexId, @org.jetbrains.annotations.NotNull()
    java.lang.String scheduleIndexId, int startHour, int startMinute, boolean validate, boolean sunday, boolean monday, boolean tuesday, boolean wednesday, boolean thursday, boolean friday, boolean saturday, boolean useTomato, int endHour, int endMinute, boolean isRecycle, boolean isDenyChange, int denyChangeLength, @org.jetbrains.annotations.NotNull()
    java.lang.String jumpDate, int trend, int syncState, long syncTime, long uuid, int version, @org.jetbrains.annotations.NotNull()
    com.lijianqiang12.silent.data.model.db.LockConfig lockConfig) {
        super();
    }
    
    public final long getId() {
        return 0L;
    }
    
    public final void setId(long p0) {
    }
    
    public final int getUserId() {
        return 0;
    }
    
    public final void setUserId(int p0) {
    }
    
    @org.jetbrains.annotations.NotNull()
    public final java.lang.String getTitle() {
        return null;
    }
    
    public final void setTitle(@org.jetbrains.annotations.NotNull()
    java.lang.String p0) {
    }
    
    @org.jetbrains.annotations.NotNull()
    public final java.lang.String getTomatoIndexId() {
        return null;
    }
    
    public final void setTomatoIndexId(@org.jetbrains.annotations.NotNull()
    java.lang.String p0) {
    }
    
    @org.jetbrains.annotations.NotNull()
    public final java.lang.String getScheduleIndexId() {
        return null;
    }
    
    public final void setScheduleIndexId(@org.jetbrains.annotations.NotNull()
    java.lang.String p0) {
    }
    
    public final int getStartHour() {
        return 0;
    }
    
    public final void setStartHour(int p0) {
    }
    
    public final int getStartMinute() {
        return 0;
    }
    
    public final void setStartMinute(int p0) {
    }
    
    public final boolean getValidate() {
        return false;
    }
    
    public final void setValidate(boolean p0) {
    }
    
    public final boolean getSunday() {
        return false;
    }
    
    public final void setSunday(boolean p0) {
    }
    
    public final boolean getMonday() {
        return false;
    }
    
    public final void setMonday(boolean p0) {
    }
    
    public final boolean getTuesday() {
        return false;
    }
    
    public final void setTuesday(boolean p0) {
    }
    
    public final boolean getWednesday() {
        return false;
    }
    
    public final void setWednesday(boolean p0) {
    }
    
    public final boolean getThursday() {
        return false;
    }
    
    public final void setThursday(boolean p0) {
    }
    
    public final boolean getFriday() {
        return false;
    }
    
    public final void setFriday(boolean p0) {
    }
    
    public final boolean getSaturday() {
        return false;
    }
    
    public final void setSaturday(boolean p0) {
    }
    
    public final boolean getUseTomato() {
        return false;
    }
    
    public final void setUseTomato(boolean p0) {
    }
    
    public final int getEndHour() {
        return 0;
    }
    
    public final void setEndHour(int p0) {
    }
    
    public final int getEndMinute() {
        return 0;
    }
    
    public final void setEndMinute(int p0) {
    }
    
    public final boolean isRecycle() {
        return false;
    }
    
    public final void setRecycle(boolean p0) {
    }
    
    public final boolean isDenyChange() {
        return false;
    }
    
    public final void setDenyChange(boolean p0) {
    }
    
    public final int getDenyChangeLength() {
        return 0;
    }
    
    public final void setDenyChangeLength(int p0) {
    }
    
    @org.jetbrains.annotations.NotNull()
    public final java.lang.String getJumpDate() {
        return null;
    }
    
    public final void setJumpDate(@org.jetbrains.annotations.NotNull()
    java.lang.String p0) {
    }
    
    public final int getTrend() {
        return 0;
    }
    
    public final void setTrend(int p0) {
    }
    
    public final int getSyncState() {
        return 0;
    }
    
    public final void setSyncState(int p0) {
    }
    
    public final long getSyncTime() {
        return 0L;
    }
    
    public final void setSyncTime(long p0) {
    }
    
    public final long getUuid() {
        return 0L;
    }
    
    public final void setUuid(long p0) {
    }
    
    public final int getVersion() {
        return 0;
    }
    
    public final void setVersion(int p0) {
    }
    
    @org.jetbrains.annotations.NotNull()
    public final com.lijianqiang12.silent.data.model.db.LockConfig getLockConfig() {
        return null;
    }
    
    public final void setLockConfig(@org.jetbrains.annotations.NotNull()
    com.lijianqiang12.silent.data.model.db.LockConfig p0) {
    }
    
    public Schedule() {
        super();
    }
    
    public Schedule(@org.jetbrains.annotations.NotNull()
    android.os.Parcel parcel) {
        super();
    }
    
    @java.lang.Override()
    public void writeToParcel(@org.jetbrains.annotations.NotNull()
    android.os.Parcel parcel, int flags) {
    }
    
    @java.lang.Override()
    public int describeContents() {
        return 0;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final com.lijianqiang12.silent.data.model.db.Schedule copy() {
        return null;
    }
    
    @java.lang.Override()
    @org.jetbrains.annotations.NotNull()
    public java.lang.String toString() {
        return null;
    }
    
    public final long component1() {
        return 0L;
    }
    
    public final boolean component10() {
        return false;
    }
    
    public final boolean component11() {
        return false;
    }
    
    public final boolean component12() {
        return false;
    }
    
    public final boolean component13() {
        return false;
    }
    
    public final boolean component14() {
        return false;
    }
    
    public final boolean component15() {
        return false;
    }
    
    public final boolean component16() {
        return false;
    }
    
    public final int component17() {
        return 0;
    }
    
    public final int component18() {
        return 0;
    }
    
    public final boolean component19() {
        return false;
    }
    
    public final int component2() {
        return 0;
    }
    
    public final boolean component20() {
        return false;
    }
    
    public final int component21() {
        return 0;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final java.lang.String component22() {
        return null;
    }
    
    public final int component23() {
        return 0;
    }
    
    public final int component24() {
        return 0;
    }
    
    public final long component25() {
        return 0L;
    }
    
    public final long component26() {
        return 0L;
    }
    
    public final int component27() {
        return 0;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final com.lijianqiang12.silent.data.model.db.LockConfig component28() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final java.lang.String component3() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final java.lang.String component4() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final java.lang.String component5() {
        return null;
    }
    
    public final int component6() {
        return 0;
    }
    
    public final int component7() {
        return 0;
    }
    
    public final boolean component8() {
        return false;
    }
    
    public final boolean component9() {
        return false;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final com.lijianqiang12.silent.data.model.db.Schedule copy(long id, int userId, @org.jetbrains.annotations.NotNull()
    java.lang.String title, @org.jetbrains.annotations.NotNull()
    java.lang.String tomatoIndexId, @org.jetbrains.annotations.NotNull()
    java.lang.String scheduleIndexId, int startHour, int startMinute, boolean validate, boolean sunday, boolean monday, boolean tuesday, boolean wednesday, boolean thursday, boolean friday, boolean saturday, boolean useTomato, int endHour, int endMinute, boolean isRecycle, boolean isDenyChange, int denyChangeLength, @org.jetbrains.annotations.NotNull()
    java.lang.String jumpDate, int trend, int syncState, long syncTime, long uuid, int version, @org.jetbrains.annotations.NotNull()
    com.lijianqiang12.silent.data.model.db.LockConfig lockConfig) {
        return null;
    }
    
    @java.lang.Override()
    public boolean equals(@org.jetbrains.annotations.Nullable()
    java.lang.Object other) {
        return false;
    }
    
    @java.lang.Override()
    public int hashCode() {
        return 0;
    }
    
    @kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000$\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\b\u0003\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\u0011\n\u0000\n\u0002\u0010\b\n\u0002\b\u0002\b\u0086\u0003\u0018\u00002\b\u0012\u0004\u0012\u00020\u00020\u0001B\u0007\b\u0002\u00a2\u0006\u0002\u0010\u0003J\u0010\u0010\u0004\u001a\u00020\u00022\u0006\u0010\u0005\u001a\u00020\u0006H\u0016J\u001d\u0010\u0007\u001a\n\u0012\u0006\u0012\u0004\u0018\u00010\u00020\b2\u0006\u0010\t\u001a\u00020\nH\u0016\u00a2\u0006\u0002\u0010\u000b\u00a8\u0006\f"}, d2 = {"Lcom/lijianqiang12/silent/data/model/db/Schedule$CREATOR;", "Landroid/os/Parcelable$Creator;", "Lcom/lijianqiang12/silent/data/model/db/Schedule;", "()V", "createFromParcel", "parcel", "Landroid/os/Parcel;", "newArray", "", "size", "", "(I)[Lcom/lijianqiang12/silent/data/model/db/Schedule;", "data_debug"})
    public static final class CREATOR implements android.os.Parcelable.Creator<com.lijianqiang12.silent.data.model.db.Schedule> {
        
        private CREATOR() {
            super();
        }
        
        @java.lang.Override()
        @org.jetbrains.annotations.NotNull()
        public com.lijianqiang12.silent.data.model.db.Schedule createFromParcel(@org.jetbrains.annotations.NotNull()
        android.os.Parcel parcel) {
            return null;
        }
        
        @java.lang.Override()
        @org.jetbrains.annotations.NotNull()
        public com.lijianqiang12.silent.data.model.db.Schedule[] newArray(int size) {
            return null;
        }
    }
}