package com.lijianqiang12.silent.data.model.repository;

@kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000:\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\u000b\n\u0002\b\u0002\n\u0002\u0010\u000e\n\u0002\b\u0002\n\u0002\u0010\u0002\n\u0002\b\u0005\u0018\u0000 \u00152\u00020\u0001:\u0001\u0015B\u0017\b\u0002\u0012\u0006\u0010\u0002\u001a\u00020\u0003\u0012\u0006\u0010\u0004\u001a\u00020\u0005\u00a2\u0006\u0002\u0010\u0006J\u0016\u0010\u0007\u001a\n\u0012\u0006\u0012\u0004\u0018\u00010\t0\b2\u0006\u0010\n\u001a\u00020\u000bJ\u0016\u0010\f\u001a\n\u0012\u0006\u0012\u0004\u0018\u00010\t0\b2\u0006\u0010\r\u001a\u00020\u000eJ\u0010\u0010\u000f\u001a\u0004\u0018\u00010\t2\u0006\u0010\r\u001a\u00020\u000eJ\u0016\u0010\u0010\u001a\u00020\u00112\u0006\u0010\u0012\u001a\u00020\tH\u0086@\u00a2\u0006\u0002\u0010\u0013J\u0016\u0010\u0014\u001a\u00020\u00112\u0006\u0010\u0012\u001a\u00020\tH\u0086@\u00a2\u0006\u0002\u0010\u0013R\u000e\u0010\u0004\u001a\u00020\u0005X\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0002\u001a\u00020\u0003X\u0082\u0004\u00a2\u0006\u0002\n\u0000\u00a8\u0006\u0016"}, d2 = {"Lcom/lijianqiang12/silent/data/model/repository/DayLimitRepository;", "", "dayLimitDao", "Lcom/lijianqiang12/silent/data/model/db/DayLimitDao;", "appContext", "Landroid/content/Context;", "(Lcom/lijianqiang12/silent/data/model/db/DayLimitDao;Landroid/content/Context;)V", "getDayLimits", "Landroidx/lifecycle/LiveData;", "Lcom/lijianqiang12/silent/data/model/db/DayLimit;", "isWorkDay", "", "getTodayLimits", "dayName", "", "getTodayLimitsImmediately", "insertDayLimit", "", "dayLimit", "(Lcom/lijianqiang12/silent/data/model/db/DayLimit;Lkotlin/coroutines/Continuation;)Ljava/lang/Object;", "updateDayLimit", "Companion", "data_debug"})
public final class DayLimitRepository {
    @org.jetbrains.annotations.NotNull()
    private final com.lijianqiang12.silent.data.model.db.DayLimitDao dayLimitDao = null;
    @org.jetbrains.annotations.NotNull()
    private final android.content.Context appContext = null;
    @kotlin.jvm.Volatile()
    @org.jetbrains.annotations.Nullable()
    private static volatile com.lijianqiang12.silent.data.model.repository.DayLimitRepository sInstance;
    @org.jetbrains.annotations.NotNull()
    public static final com.lijianqiang12.silent.data.model.repository.DayLimitRepository.Companion Companion = null;
    
    private DayLimitRepository(com.lijianqiang12.silent.data.model.db.DayLimitDao dayLimitDao, android.content.Context appContext) {
        super();
    }
    
    @org.jetbrains.annotations.NotNull()
    public final androidx.lifecycle.LiveData<com.lijianqiang12.silent.data.model.db.DayLimit> getTodayLimits(@org.jetbrains.annotations.NotNull()
    java.lang.String dayName) {
        return null;
    }
    
    @org.jetbrains.annotations.Nullable()
    public final com.lijianqiang12.silent.data.model.db.DayLimit getTodayLimitsImmediately(@org.jetbrains.annotations.NotNull()
    java.lang.String dayName) {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final androidx.lifecycle.LiveData<com.lijianqiang12.silent.data.model.db.DayLimit> getDayLimits(boolean isWorkDay) {
        return null;
    }
    
    @org.jetbrains.annotations.Nullable()
    public final java.lang.Object insertDayLimit(@org.jetbrains.annotations.NotNull()
    com.lijianqiang12.silent.data.model.db.DayLimit dayLimit, @org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super kotlin.Unit> $completion) {
        return null;
    }
    
    @org.jetbrains.annotations.Nullable()
    public final java.lang.Object updateDayLimit(@org.jetbrains.annotations.NotNull()
    com.lijianqiang12.silent.data.model.db.DayLimit dayLimit, @org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super kotlin.Unit> $completion) {
        return null;
    }
    
    @kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000 \n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\b\u0086\u0003\u0018\u00002\u00020\u0001B\u0007\b\u0002\u00a2\u0006\u0002\u0010\u0002J\u0016\u0010\u0005\u001a\u00020\u00042\u0006\u0010\u0006\u001a\u00020\u00072\u0006\u0010\b\u001a\u00020\tR\u0010\u0010\u0003\u001a\u0004\u0018\u00010\u0004X\u0082\u000e\u00a2\u0006\u0002\n\u0000\u00a8\u0006\n"}, d2 = {"Lcom/lijianqiang12/silent/data/model/repository/DayLimitRepository$Companion;", "", "()V", "sInstance", "Lcom/lijianqiang12/silent/data/model/repository/DayLimitRepository;", "getInstance", "dayLimitDao", "Lcom/lijianqiang12/silent/data/model/db/DayLimitDao;", "appContext", "Landroid/content/Context;", "data_debug"})
    public static final class Companion {
        
        private Companion() {
            super();
        }
        
        @org.jetbrains.annotations.NotNull()
        public final com.lijianqiang12.silent.data.model.repository.DayLimitRepository getInstance(@org.jetbrains.annotations.NotNull()
        com.lijianqiang12.silent.data.model.db.DayLimitDao dayLimitDao, @org.jetbrains.annotations.NotNull()
        android.content.Context appContext) {
            return null;
        }
    }
}