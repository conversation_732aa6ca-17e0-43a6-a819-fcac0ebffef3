package com.lijianqiang12.silent.data.model.db;

import androidx.annotation.NonNull;
import androidx.room.migration.Migration;
import androidx.sqlite.db.SupportSQLiteDatabase;
import java.lang.Override;
import java.lang.SuppressWarnings;
import javax.annotation.processing.Generated;

@Generated("androidx.room.RoomProcessor")
@SuppressWarnings({"unchecked", "deprecation"})
final class AppDatabase_AutoMigration_58_59_Impl extends Migration {
  public AppDatabase_AutoMigration_58_59_Impl() {
    super(58, 59);
  }

  @Override
  public void migrate(@NonNull final SupportSQLiteDatabase db) {
    db.execSQL("ALTER TABLE `LockHistory` ADD COLUMN `deleteWhiteAppTemp` TEXT NOT NULL DEFAULT '[]'");
  }
}
