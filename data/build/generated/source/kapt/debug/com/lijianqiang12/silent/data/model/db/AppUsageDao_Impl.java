package com.lijianqiang12.silent.data.model.db;

import android.database.Cursor;
import android.os.CancellationSignal;
import androidx.annotation.NonNull;
import androidx.room.CoroutinesRoom;
import androidx.room.EntityInsertionAdapter;
import androidx.room.RoomDatabase;
import androidx.room.RoomSQLiteQuery;
import androidx.room.util.CursorUtil;
import androidx.room.util.DBUtil;
import androidx.room.util.StringUtil;
import androidx.sqlite.db.SupportSQLiteStatement;
import java.lang.Class;
import java.lang.Exception;
import java.lang.Object;
import java.lang.Override;
import java.lang.String;
import java.lang.StringBuilder;
import java.lang.SuppressWarnings;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.concurrent.Callable;
import javax.annotation.processing.Generated;
import kotlin.Unit;
import kotlin.coroutines.Continuation;

@Generated("androidx.room.RoomProcessor")
@SuppressWarnings({"unchecked", "deprecation"})
public final class AppUsageDao_Impl implements AppUsageDao {
  private final RoomDatabase __db;

  private final EntityInsertionAdapter<AppUsage> __insertionAdapterOfAppUsage;

  public AppUsageDao_Impl(@NonNull final RoomDatabase __db) {
    this.__db = __db;
    this.__insertionAdapterOfAppUsage = new EntityInsertionAdapter<AppUsage>(__db) {
      @Override
      @NonNull
      protected String createQuery() {
        return "INSERT OR IGNORE INTO `AppUsage` (`id`,`appPkg`,`timestamp`,`event`) VALUES (nullif(?, 0),?,?,?)";
      }

      @Override
      protected void bind(@NonNull final SupportSQLiteStatement statement,
          @NonNull final AppUsage entity) {
        statement.bindLong(1, entity.getId());
        if (entity.getAppPkg() == null) {
          statement.bindNull(2);
        } else {
          statement.bindString(2, entity.getAppPkg());
        }
        statement.bindLong(3, entity.getTimestamp());
        statement.bindLong(4, entity.getEvent());
      }
    };
  }

  @Override
  public Object insertAppUsage(final AppUsage appUsage,
      final Continuation<? super Unit> $completion) {
    return CoroutinesRoom.execute(__db, true, new Callable<Unit>() {
      @Override
      @NonNull
      public Unit call() throws Exception {
        __db.beginTransaction();
        try {
          __insertionAdapterOfAppUsage.insert(appUsage);
          __db.setTransactionSuccessful();
          return Unit.INSTANCE;
        } finally {
          __db.endTransaction();
        }
      }
    }, $completion);
  }

  @Override
  public Object getAllAppUsageList(final long startTime, final long endTime,
      final Continuation<? super List<AppUsage>> $completion) {
    final String _sql = "select * From AppUsage where timestamp>=? and timestamp<=?";
    final RoomSQLiteQuery _statement = RoomSQLiteQuery.acquire(_sql, 2);
    int _argIndex = 1;
    _statement.bindLong(_argIndex, startTime);
    _argIndex = 2;
    _statement.bindLong(_argIndex, endTime);
    final CancellationSignal _cancellationSignal = DBUtil.createCancellationSignal();
    return CoroutinesRoom.execute(__db, false, _cancellationSignal, new Callable<List<AppUsage>>() {
      @Override
      @NonNull
      public List<AppUsage> call() throws Exception {
        final Cursor _cursor = DBUtil.query(__db, _statement, false, null);
        try {
          final int _cursorIndexOfId = CursorUtil.getColumnIndexOrThrow(_cursor, "id");
          final int _cursorIndexOfAppPkg = CursorUtil.getColumnIndexOrThrow(_cursor, "appPkg");
          final int _cursorIndexOfTimestamp = CursorUtil.getColumnIndexOrThrow(_cursor, "timestamp");
          final int _cursorIndexOfEvent = CursorUtil.getColumnIndexOrThrow(_cursor, "event");
          final List<AppUsage> _result = new ArrayList<AppUsage>(_cursor.getCount());
          while (_cursor.moveToNext()) {
            final AppUsage _item;
            final long _tmpId;
            _tmpId = _cursor.getLong(_cursorIndexOfId);
            final String _tmpAppPkg;
            if (_cursor.isNull(_cursorIndexOfAppPkg)) {
              _tmpAppPkg = null;
            } else {
              _tmpAppPkg = _cursor.getString(_cursorIndexOfAppPkg);
            }
            final long _tmpTimestamp;
            _tmpTimestamp = _cursor.getLong(_cursorIndexOfTimestamp);
            final int _tmpEvent;
            _tmpEvent = _cursor.getInt(_cursorIndexOfEvent);
            _item = new AppUsage(_tmpId,_tmpAppPkg,_tmpTimestamp,_tmpEvent);
            _result.add(_item);
          }
          return _result;
        } finally {
          _cursor.close();
          _statement.release();
        }
      }
    }, $completion);
  }

  @Override
  public Object getOneAppUsageList(final long startTime, final long endTime, final String appPkg,
      final Continuation<? super List<AppUsage>> $completion) {
    final String _sql = "select * From AppUsage where timestamp>=? and timestamp<=? and appPkg=?";
    final RoomSQLiteQuery _statement = RoomSQLiteQuery.acquire(_sql, 3);
    int _argIndex = 1;
    _statement.bindLong(_argIndex, startTime);
    _argIndex = 2;
    _statement.bindLong(_argIndex, endTime);
    _argIndex = 3;
    if (appPkg == null) {
      _statement.bindNull(_argIndex);
    } else {
      _statement.bindString(_argIndex, appPkg);
    }
    final CancellationSignal _cancellationSignal = DBUtil.createCancellationSignal();
    return CoroutinesRoom.execute(__db, false, _cancellationSignal, new Callable<List<AppUsage>>() {
      @Override
      @NonNull
      public List<AppUsage> call() throws Exception {
        final Cursor _cursor = DBUtil.query(__db, _statement, false, null);
        try {
          final int _cursorIndexOfId = CursorUtil.getColumnIndexOrThrow(_cursor, "id");
          final int _cursorIndexOfAppPkg = CursorUtil.getColumnIndexOrThrow(_cursor, "appPkg");
          final int _cursorIndexOfTimestamp = CursorUtil.getColumnIndexOrThrow(_cursor, "timestamp");
          final int _cursorIndexOfEvent = CursorUtil.getColumnIndexOrThrow(_cursor, "event");
          final List<AppUsage> _result = new ArrayList<AppUsage>(_cursor.getCount());
          while (_cursor.moveToNext()) {
            final AppUsage _item;
            final long _tmpId;
            _tmpId = _cursor.getLong(_cursorIndexOfId);
            final String _tmpAppPkg;
            if (_cursor.isNull(_cursorIndexOfAppPkg)) {
              _tmpAppPkg = null;
            } else {
              _tmpAppPkg = _cursor.getString(_cursorIndexOfAppPkg);
            }
            final long _tmpTimestamp;
            _tmpTimestamp = _cursor.getLong(_cursorIndexOfTimestamp);
            final int _tmpEvent;
            _tmpEvent = _cursor.getInt(_cursorIndexOfEvent);
            _item = new AppUsage(_tmpId,_tmpAppPkg,_tmpTimestamp,_tmpEvent);
            _result.add(_item);
          }
          return _result;
        } finally {
          _cursor.close();
          _statement.release();
        }
      }
    }, $completion);
  }

  @Override
  public Object getSomeAppUsageList(final long startTime, final long endTime,
      final List<String> appPkgList, final Continuation<? super List<AppUsage>> $completion) {
    final StringBuilder _stringBuilder = StringUtil.newStringBuilder();
    _stringBuilder.append("select * From AppUsage where timestamp>=");
    _stringBuilder.append("?");
    _stringBuilder.append(" and timestamp<=");
    _stringBuilder.append("?");
    _stringBuilder.append(" and appPkg in (");
    final int _inputSize = appPkgList.size();
    StringUtil.appendPlaceholders(_stringBuilder, _inputSize);
    _stringBuilder.append(")");
    final String _sql = _stringBuilder.toString();
    final int _argCount = 2 + _inputSize;
    final RoomSQLiteQuery _statement = RoomSQLiteQuery.acquire(_sql, _argCount);
    int _argIndex = 1;
    _statement.bindLong(_argIndex, startTime);
    _argIndex = 2;
    _statement.bindLong(_argIndex, endTime);
    _argIndex = 3;
    for (String _item : appPkgList) {
      if (_item == null) {
        _statement.bindNull(_argIndex);
      } else {
        _statement.bindString(_argIndex, _item);
      }
      _argIndex++;
    }
    final CancellationSignal _cancellationSignal = DBUtil.createCancellationSignal();
    return CoroutinesRoom.execute(__db, false, _cancellationSignal, new Callable<List<AppUsage>>() {
      @Override
      @NonNull
      public List<AppUsage> call() throws Exception {
        final Cursor _cursor = DBUtil.query(__db, _statement, false, null);
        try {
          final int _cursorIndexOfId = CursorUtil.getColumnIndexOrThrow(_cursor, "id");
          final int _cursorIndexOfAppPkg = CursorUtil.getColumnIndexOrThrow(_cursor, "appPkg");
          final int _cursorIndexOfTimestamp = CursorUtil.getColumnIndexOrThrow(_cursor, "timestamp");
          final int _cursorIndexOfEvent = CursorUtil.getColumnIndexOrThrow(_cursor, "event");
          final List<AppUsage> _result = new ArrayList<AppUsage>(_cursor.getCount());
          while (_cursor.moveToNext()) {
            final AppUsage _item_1;
            final long _tmpId;
            _tmpId = _cursor.getLong(_cursorIndexOfId);
            final String _tmpAppPkg;
            if (_cursor.isNull(_cursorIndexOfAppPkg)) {
              _tmpAppPkg = null;
            } else {
              _tmpAppPkg = _cursor.getString(_cursorIndexOfAppPkg);
            }
            final long _tmpTimestamp;
            _tmpTimestamp = _cursor.getLong(_cursorIndexOfTimestamp);
            final int _tmpEvent;
            _tmpEvent = _cursor.getInt(_cursorIndexOfEvent);
            _item_1 = new AppUsage(_tmpId,_tmpAppPkg,_tmpTimestamp,_tmpEvent);
            _result.add(_item_1);
          }
          return _result;
        } finally {
          _cursor.close();
          _statement.release();
        }
      }
    }, $completion);
  }

  @NonNull
  public static List<Class<?>> getRequiredConverters() {
    return Collections.emptyList();
  }
}
