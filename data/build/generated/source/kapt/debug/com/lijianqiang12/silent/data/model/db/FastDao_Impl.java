package com.lijianqiang12.silent.data.model.db;

import android.database.Cursor;
import android.os.CancellationSignal;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.lifecycle.LiveData;
import androidx.room.CoroutinesRoom;
import androidx.room.EntityDeletionOrUpdateAdapter;
import androidx.room.EntityInsertionAdapter;
import androidx.room.RoomDatabase;
import androidx.room.RoomSQLiteQuery;
import androidx.room.SharedSQLiteStatement;
import androidx.room.util.CursorUtil;
import androidx.room.util.DBUtil;
import androidx.sqlite.db.SupportSQLiteStatement;
import java.lang.Class;
import java.lang.Exception;
import java.lang.Long;
import java.lang.Object;
import java.lang.Override;
import java.lang.String;
import java.lang.SuppressWarnings;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.concurrent.Callable;
import javax.annotation.processing.Generated;
import kotlin.Unit;
import kotlin.coroutines.Continuation;

@Generated("androidx.room.RoomProcessor")
@SuppressWarnings({"unchecked", "deprecation"})
public final class FastDao_Impl implements FastDao {
  private final RoomDatabase __db;

  private final EntityInsertionAdapter<Fast> __insertionAdapterOfFast;

  private final EntityDeletionOrUpdateAdapter<Fast> __deletionAdapterOfFast;

  private final EntityDeletionOrUpdateAdapter<Fast> __updateAdapterOfFast;

  private final SharedSQLiteStatement __preparedStmtOfDeleteAll;

  private final SharedSQLiteStatement __preparedStmtOfUpdateUserId;

  public FastDao_Impl(@NonNull final RoomDatabase __db) {
    this.__db = __db;
    this.__insertionAdapterOfFast = new EntityInsertionAdapter<Fast>(__db) {
      @Override
      @NonNull
      protected String createQuery() {
        return "INSERT OR IGNORE INTO `Fast` (`id`,`userId`,`fastIndexId`,`length`,`trend`,`syncState`,`syncTime`,`uuid`,`version`) VALUES (nullif(?, 0),?,?,?,?,?,?,?,?)";
      }

      @Override
      protected void bind(@NonNull final SupportSQLiteStatement statement,
          @NonNull final Fast entity) {
        statement.bindLong(1, entity.getId());
        statement.bindLong(2, entity.getUserId());
        if (entity.getFastIndexId() == null) {
          statement.bindNull(3);
        } else {
          statement.bindString(3, entity.getFastIndexId());
        }
        statement.bindLong(4, entity.getLength());
        statement.bindLong(5, entity.getTrend());
        statement.bindLong(6, entity.getSyncState());
        statement.bindLong(7, entity.getSyncTime());
        statement.bindLong(8, entity.getUuid());
        statement.bindLong(9, entity.getVersion());
      }
    };
    this.__deletionAdapterOfFast = new EntityDeletionOrUpdateAdapter<Fast>(__db) {
      @Override
      @NonNull
      protected String createQuery() {
        return "DELETE FROM `Fast` WHERE `id` = ?";
      }

      @Override
      protected void bind(@NonNull final SupportSQLiteStatement statement,
          @NonNull final Fast entity) {
        statement.bindLong(1, entity.getId());
      }
    };
    this.__updateAdapterOfFast = new EntityDeletionOrUpdateAdapter<Fast>(__db) {
      @Override
      @NonNull
      protected String createQuery() {
        return "UPDATE OR ABORT `Fast` SET `id` = ?,`userId` = ?,`fastIndexId` = ?,`length` = ?,`trend` = ?,`syncState` = ?,`syncTime` = ?,`uuid` = ?,`version` = ? WHERE `id` = ?";
      }

      @Override
      protected void bind(@NonNull final SupportSQLiteStatement statement,
          @NonNull final Fast entity) {
        statement.bindLong(1, entity.getId());
        statement.bindLong(2, entity.getUserId());
        if (entity.getFastIndexId() == null) {
          statement.bindNull(3);
        } else {
          statement.bindString(3, entity.getFastIndexId());
        }
        statement.bindLong(4, entity.getLength());
        statement.bindLong(5, entity.getTrend());
        statement.bindLong(6, entity.getSyncState());
        statement.bindLong(7, entity.getSyncTime());
        statement.bindLong(8, entity.getUuid());
        statement.bindLong(9, entity.getVersion());
        statement.bindLong(10, entity.getId());
      }
    };
    this.__preparedStmtOfDeleteAll = new SharedSQLiteStatement(__db) {
      @Override
      @NonNull
      public String createQuery() {
        final String _query = "delete from Fast where userId = ?";
        return _query;
      }
    };
    this.__preparedStmtOfUpdateUserId = new SharedSQLiteStatement(__db) {
      @Override
      @NonNull
      public String createQuery() {
        final String _query = "UPDATE Fast SET userId = ? WHERE userId = -1";
        return _query;
      }
    };
  }

  @Override
  public Object insertFast(final Fast fast, final Continuation<? super Long> $completion) {
    return CoroutinesRoom.execute(__db, true, new Callable<Long>() {
      @Override
      @NonNull
      public Long call() throws Exception {
        __db.beginTransaction();
        try {
          final Long _result = __insertionAdapterOfFast.insertAndReturnId(fast);
          __db.setTransactionSuccessful();
          return _result;
        } finally {
          __db.endTransaction();
        }
      }
    }, $completion);
  }

  @Override
  public long addFast(final Fast fast) {
    __db.assertNotSuspendingTransaction();
    __db.beginTransaction();
    try {
      final long _result = __insertionAdapterOfFast.insertAndReturnId(fast);
      __db.setTransactionSuccessful();
      return _result;
    } finally {
      __db.endTransaction();
    }
  }

  @Override
  public Object deleteFast(final Fast fast, final Continuation<? super Unit> $completion) {
    return CoroutinesRoom.execute(__db, true, new Callable<Unit>() {
      @Override
      @NonNull
      public Unit call() throws Exception {
        __db.beginTransaction();
        try {
          __deletionAdapterOfFast.handle(fast);
          __db.setTransactionSuccessful();
          return Unit.INSTANCE;
        } finally {
          __db.endTransaction();
        }
      }
    }, $completion);
  }

  @Override
  public Object updateFast(final Fast fast, final Continuation<? super Unit> $completion) {
    return CoroutinesRoom.execute(__db, true, new Callable<Unit>() {
      @Override
      @NonNull
      public Unit call() throws Exception {
        __db.beginTransaction();
        try {
          __updateAdapterOfFast.handle(fast);
          __db.setTransactionSuccessful();
          return Unit.INSTANCE;
        } finally {
          __db.endTransaction();
        }
      }
    }, $completion);
  }

  @Override
  public Object deleteAll(final int userId, final Continuation<? super Unit> $completion) {
    return CoroutinesRoom.execute(__db, true, new Callable<Unit>() {
      @Override
      @NonNull
      public Unit call() throws Exception {
        final SupportSQLiteStatement _stmt = __preparedStmtOfDeleteAll.acquire();
        int _argIndex = 1;
        _stmt.bindLong(_argIndex, userId);
        try {
          __db.beginTransaction();
          try {
            _stmt.executeUpdateDelete();
            __db.setTransactionSuccessful();
            return Unit.INSTANCE;
          } finally {
            __db.endTransaction();
          }
        } finally {
          __preparedStmtOfDeleteAll.release(_stmt);
        }
      }
    }, $completion);
  }

  @Override
  public void updateUserId(final int newUserId) {
    __db.assertNotSuspendingTransaction();
    final SupportSQLiteStatement _stmt = __preparedStmtOfUpdateUserId.acquire();
    int _argIndex = 1;
    _stmt.bindLong(_argIndex, newUserId);
    try {
      __db.beginTransaction();
      try {
        _stmt.executeUpdateDelete();
        __db.setTransactionSuccessful();
      } finally {
        __db.endTransaction();
      }
    } finally {
      __preparedStmtOfUpdateUserId.release(_stmt);
    }
  }

  @Override
  public Object getFastWithState(final int userId, final int state,
      final Continuation<? super List<Fast>> $completion) {
    final String _sql = "select * From Fast Where userId = ? and syncState = ? order by trend";
    final RoomSQLiteQuery _statement = RoomSQLiteQuery.acquire(_sql, 2);
    int _argIndex = 1;
    _statement.bindLong(_argIndex, userId);
    _argIndex = 2;
    _statement.bindLong(_argIndex, state);
    final CancellationSignal _cancellationSignal = DBUtil.createCancellationSignal();
    return CoroutinesRoom.execute(__db, false, _cancellationSignal, new Callable<List<Fast>>() {
      @Override
      @NonNull
      public List<Fast> call() throws Exception {
        final Cursor _cursor = DBUtil.query(__db, _statement, false, null);
        try {
          final int _cursorIndexOfId = CursorUtil.getColumnIndexOrThrow(_cursor, "id");
          final int _cursorIndexOfUserId = CursorUtil.getColumnIndexOrThrow(_cursor, "userId");
          final int _cursorIndexOfFastIndexId = CursorUtil.getColumnIndexOrThrow(_cursor, "fastIndexId");
          final int _cursorIndexOfLength = CursorUtil.getColumnIndexOrThrow(_cursor, "length");
          final int _cursorIndexOfTrend = CursorUtil.getColumnIndexOrThrow(_cursor, "trend");
          final int _cursorIndexOfSyncState = CursorUtil.getColumnIndexOrThrow(_cursor, "syncState");
          final int _cursorIndexOfSyncTime = CursorUtil.getColumnIndexOrThrow(_cursor, "syncTime");
          final int _cursorIndexOfUuid = CursorUtil.getColumnIndexOrThrow(_cursor, "uuid");
          final int _cursorIndexOfVersion = CursorUtil.getColumnIndexOrThrow(_cursor, "version");
          final List<Fast> _result = new ArrayList<Fast>(_cursor.getCount());
          while (_cursor.moveToNext()) {
            final Fast _item;
            final long _tmpId;
            _tmpId = _cursor.getLong(_cursorIndexOfId);
            final int _tmpUserId;
            _tmpUserId = _cursor.getInt(_cursorIndexOfUserId);
            final String _tmpFastIndexId;
            if (_cursor.isNull(_cursorIndexOfFastIndexId)) {
              _tmpFastIndexId = null;
            } else {
              _tmpFastIndexId = _cursor.getString(_cursorIndexOfFastIndexId);
            }
            final int _tmpLength;
            _tmpLength = _cursor.getInt(_cursorIndexOfLength);
            final int _tmpTrend;
            _tmpTrend = _cursor.getInt(_cursorIndexOfTrend);
            final int _tmpSyncState;
            _tmpSyncState = _cursor.getInt(_cursorIndexOfSyncState);
            final long _tmpSyncTime;
            _tmpSyncTime = _cursor.getLong(_cursorIndexOfSyncTime);
            final long _tmpUuid;
            _tmpUuid = _cursor.getLong(_cursorIndexOfUuid);
            final int _tmpVersion;
            _tmpVersion = _cursor.getInt(_cursorIndexOfVersion);
            _item = new Fast(_tmpId,_tmpUserId,_tmpFastIndexId,_tmpLength,_tmpTrend,_tmpSyncState,_tmpSyncTime,_tmpUuid,_tmpVersion);
            _result.add(_item);
          }
          return _result;
        } finally {
          _cursor.close();
          _statement.release();
        }
      }
    }, $completion);
  }

  @Override
  public Object getAllFastList(final int userId,
      final Continuation<? super List<Fast>> $completion) {
    final String _sql = "select * From Fast Where userId = ? order by trend";
    final RoomSQLiteQuery _statement = RoomSQLiteQuery.acquire(_sql, 1);
    int _argIndex = 1;
    _statement.bindLong(_argIndex, userId);
    final CancellationSignal _cancellationSignal = DBUtil.createCancellationSignal();
    return CoroutinesRoom.execute(__db, false, _cancellationSignal, new Callable<List<Fast>>() {
      @Override
      @NonNull
      public List<Fast> call() throws Exception {
        final Cursor _cursor = DBUtil.query(__db, _statement, false, null);
        try {
          final int _cursorIndexOfId = CursorUtil.getColumnIndexOrThrow(_cursor, "id");
          final int _cursorIndexOfUserId = CursorUtil.getColumnIndexOrThrow(_cursor, "userId");
          final int _cursorIndexOfFastIndexId = CursorUtil.getColumnIndexOrThrow(_cursor, "fastIndexId");
          final int _cursorIndexOfLength = CursorUtil.getColumnIndexOrThrow(_cursor, "length");
          final int _cursorIndexOfTrend = CursorUtil.getColumnIndexOrThrow(_cursor, "trend");
          final int _cursorIndexOfSyncState = CursorUtil.getColumnIndexOrThrow(_cursor, "syncState");
          final int _cursorIndexOfSyncTime = CursorUtil.getColumnIndexOrThrow(_cursor, "syncTime");
          final int _cursorIndexOfUuid = CursorUtil.getColumnIndexOrThrow(_cursor, "uuid");
          final int _cursorIndexOfVersion = CursorUtil.getColumnIndexOrThrow(_cursor, "version");
          final List<Fast> _result = new ArrayList<Fast>(_cursor.getCount());
          while (_cursor.moveToNext()) {
            final Fast _item;
            final long _tmpId;
            _tmpId = _cursor.getLong(_cursorIndexOfId);
            final int _tmpUserId;
            _tmpUserId = _cursor.getInt(_cursorIndexOfUserId);
            final String _tmpFastIndexId;
            if (_cursor.isNull(_cursorIndexOfFastIndexId)) {
              _tmpFastIndexId = null;
            } else {
              _tmpFastIndexId = _cursor.getString(_cursorIndexOfFastIndexId);
            }
            final int _tmpLength;
            _tmpLength = _cursor.getInt(_cursorIndexOfLength);
            final int _tmpTrend;
            _tmpTrend = _cursor.getInt(_cursorIndexOfTrend);
            final int _tmpSyncState;
            _tmpSyncState = _cursor.getInt(_cursorIndexOfSyncState);
            final long _tmpSyncTime;
            _tmpSyncTime = _cursor.getLong(_cursorIndexOfSyncTime);
            final long _tmpUuid;
            _tmpUuid = _cursor.getLong(_cursorIndexOfUuid);
            final int _tmpVersion;
            _tmpVersion = _cursor.getInt(_cursorIndexOfVersion);
            _item = new Fast(_tmpId,_tmpUserId,_tmpFastIndexId,_tmpLength,_tmpTrend,_tmpSyncState,_tmpSyncTime,_tmpUuid,_tmpVersion);
            _result.add(_item);
          }
          return _result;
        } finally {
          _cursor.close();
          _statement.release();
        }
      }
    }, $completion);
  }

  @Override
  public LiveData<List<Fast>> getFasts(final int userId) {
    final String _sql = "select * From Fast where userId = ? and syncState>=0 order by trend";
    final RoomSQLiteQuery _statement = RoomSQLiteQuery.acquire(_sql, 1);
    int _argIndex = 1;
    _statement.bindLong(_argIndex, userId);
    return __db.getInvalidationTracker().createLiveData(new String[] {"Fast"}, false, new Callable<List<Fast>>() {
      @Override
      @Nullable
      public List<Fast> call() throws Exception {
        final Cursor _cursor = DBUtil.query(__db, _statement, false, null);
        try {
          final int _cursorIndexOfId = CursorUtil.getColumnIndexOrThrow(_cursor, "id");
          final int _cursorIndexOfUserId = CursorUtil.getColumnIndexOrThrow(_cursor, "userId");
          final int _cursorIndexOfFastIndexId = CursorUtil.getColumnIndexOrThrow(_cursor, "fastIndexId");
          final int _cursorIndexOfLength = CursorUtil.getColumnIndexOrThrow(_cursor, "length");
          final int _cursorIndexOfTrend = CursorUtil.getColumnIndexOrThrow(_cursor, "trend");
          final int _cursorIndexOfSyncState = CursorUtil.getColumnIndexOrThrow(_cursor, "syncState");
          final int _cursorIndexOfSyncTime = CursorUtil.getColumnIndexOrThrow(_cursor, "syncTime");
          final int _cursorIndexOfUuid = CursorUtil.getColumnIndexOrThrow(_cursor, "uuid");
          final int _cursorIndexOfVersion = CursorUtil.getColumnIndexOrThrow(_cursor, "version");
          final List<Fast> _result = new ArrayList<Fast>(_cursor.getCount());
          while (_cursor.moveToNext()) {
            final Fast _item;
            final long _tmpId;
            _tmpId = _cursor.getLong(_cursorIndexOfId);
            final int _tmpUserId;
            _tmpUserId = _cursor.getInt(_cursorIndexOfUserId);
            final String _tmpFastIndexId;
            if (_cursor.isNull(_cursorIndexOfFastIndexId)) {
              _tmpFastIndexId = null;
            } else {
              _tmpFastIndexId = _cursor.getString(_cursorIndexOfFastIndexId);
            }
            final int _tmpLength;
            _tmpLength = _cursor.getInt(_cursorIndexOfLength);
            final int _tmpTrend;
            _tmpTrend = _cursor.getInt(_cursorIndexOfTrend);
            final int _tmpSyncState;
            _tmpSyncState = _cursor.getInt(_cursorIndexOfSyncState);
            final long _tmpSyncTime;
            _tmpSyncTime = _cursor.getLong(_cursorIndexOfSyncTime);
            final long _tmpUuid;
            _tmpUuid = _cursor.getLong(_cursorIndexOfUuid);
            final int _tmpVersion;
            _tmpVersion = _cursor.getInt(_cursorIndexOfVersion);
            _item = new Fast(_tmpId,_tmpUserId,_tmpFastIndexId,_tmpLength,_tmpTrend,_tmpSyncState,_tmpSyncTime,_tmpUuid,_tmpVersion);
            _result.add(_item);
          }
          return _result;
        } finally {
          _cursor.close();
        }
      }

      @Override
      protected void finalize() {
        _statement.release();
      }
    });
  }

  @Override
  public Object getLastFast(final int userId, final Continuation<? super Fast> $completion) {
    final String _sql = "select * from Fast where userId = ? order by trend desc limit 1";
    final RoomSQLiteQuery _statement = RoomSQLiteQuery.acquire(_sql, 1);
    int _argIndex = 1;
    _statement.bindLong(_argIndex, userId);
    final CancellationSignal _cancellationSignal = DBUtil.createCancellationSignal();
    return CoroutinesRoom.execute(__db, false, _cancellationSignal, new Callable<Fast>() {
      @Override
      @Nullable
      public Fast call() throws Exception {
        final Cursor _cursor = DBUtil.query(__db, _statement, false, null);
        try {
          final int _cursorIndexOfId = CursorUtil.getColumnIndexOrThrow(_cursor, "id");
          final int _cursorIndexOfUserId = CursorUtil.getColumnIndexOrThrow(_cursor, "userId");
          final int _cursorIndexOfFastIndexId = CursorUtil.getColumnIndexOrThrow(_cursor, "fastIndexId");
          final int _cursorIndexOfLength = CursorUtil.getColumnIndexOrThrow(_cursor, "length");
          final int _cursorIndexOfTrend = CursorUtil.getColumnIndexOrThrow(_cursor, "trend");
          final int _cursorIndexOfSyncState = CursorUtil.getColumnIndexOrThrow(_cursor, "syncState");
          final int _cursorIndexOfSyncTime = CursorUtil.getColumnIndexOrThrow(_cursor, "syncTime");
          final int _cursorIndexOfUuid = CursorUtil.getColumnIndexOrThrow(_cursor, "uuid");
          final int _cursorIndexOfVersion = CursorUtil.getColumnIndexOrThrow(_cursor, "version");
          final Fast _result;
          if (_cursor.moveToFirst()) {
            final long _tmpId;
            _tmpId = _cursor.getLong(_cursorIndexOfId);
            final int _tmpUserId;
            _tmpUserId = _cursor.getInt(_cursorIndexOfUserId);
            final String _tmpFastIndexId;
            if (_cursor.isNull(_cursorIndexOfFastIndexId)) {
              _tmpFastIndexId = null;
            } else {
              _tmpFastIndexId = _cursor.getString(_cursorIndexOfFastIndexId);
            }
            final int _tmpLength;
            _tmpLength = _cursor.getInt(_cursorIndexOfLength);
            final int _tmpTrend;
            _tmpTrend = _cursor.getInt(_cursorIndexOfTrend);
            final int _tmpSyncState;
            _tmpSyncState = _cursor.getInt(_cursorIndexOfSyncState);
            final long _tmpSyncTime;
            _tmpSyncTime = _cursor.getLong(_cursorIndexOfSyncTime);
            final long _tmpUuid;
            _tmpUuid = _cursor.getLong(_cursorIndexOfUuid);
            final int _tmpVersion;
            _tmpVersion = _cursor.getInt(_cursorIndexOfVersion);
            _result = new Fast(_tmpId,_tmpUserId,_tmpFastIndexId,_tmpLength,_tmpTrend,_tmpSyncState,_tmpSyncTime,_tmpUuid,_tmpVersion);
          } else {
            _result = null;
          }
          return _result;
        } finally {
          _cursor.close();
          _statement.release();
        }
      }
    }, $completion);
  }

  @NonNull
  public static List<Class<?>> getRequiredConverters() {
    return Collections.emptyList();
  }
}
