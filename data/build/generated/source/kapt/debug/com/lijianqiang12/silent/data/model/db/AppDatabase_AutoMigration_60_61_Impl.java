package com.lijianqiang12.silent.data.model.db;

import androidx.annotation.NonNull;
import androidx.room.migration.Migration;
import androidx.sqlite.db.SupportSQLiteDatabase;
import java.lang.Override;
import java.lang.SuppressWarnings;
import javax.annotation.processing.Generated;

@Generated("androidx.room.RoomProcessor")
@SuppressWarnings({"unchecked", "deprecation"})
final class AppDatabase_AutoMigration_60_61_Impl extends Migration {
  public AppDatabase_AutoMigration_60_61_Impl() {
    super(60, 61);
  }

  @Override
  public void migrate(@NonNull final SupportSQLiteDatabase db) {
    db.execSQL("ALTER TABLE `Schedule` ADD COLUMN `denyChangeLength` INTEGER NOT NULL DEFAULT 60");
    db.execSQL("ALTER TABLE `DayLimit` ADD COLUMN `denyChangeLength` INTEGER NOT NULL DEFAULT 20");
  }
}
