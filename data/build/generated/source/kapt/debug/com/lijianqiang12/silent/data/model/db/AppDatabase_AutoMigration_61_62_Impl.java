package com.lijianqiang12.silent.data.model.db;

import androidx.annotation.NonNull;
import androidx.room.migration.Migration;
import androidx.sqlite.db.SupportSQLiteDatabase;
import java.lang.Override;
import java.lang.SuppressWarnings;
import javax.annotation.processing.Generated;

@Generated("androidx.room.RoomProcessor")
@SuppressWarnings({"unchecked", "deprecation"})
final class AppDatabase_AutoMigration_61_62_Impl extends Migration {
  public AppDatabase_AutoMigration_61_62_Impl() {
    super(61, 62);
  }

  @Override
  public void migrate(@NonNull final SupportSQLiteDatabase db) {
    db.execSQL("CREATE TABLE IF NOT EXISTS `AppUsage` (`id` INTEGER PRIMARY KEY AUTOINCREMENT NOT NULL, `appPkg` TEXT NOT NULL, `timestamp` INTEGER NOT NULL, `event` INTEGER NOT NULL)");
    db.execSQL("CREATE UNIQUE INDEX IF NOT EXISTS `index_AppUsage_timestamp` ON `AppUsage` (`timestamp`)");
    db.execSQL("CREATE INDEX IF NOT EXISTS `index_AppUsage_appPkg` ON `AppUsage` (`appPkg`)");
  }
}
