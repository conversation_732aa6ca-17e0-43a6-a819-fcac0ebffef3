package com.lijianqiang12.silent.data.model.db;

import androidx.annotation.NonNull;
import androidx.room.DatabaseConfiguration;
import androidx.room.InvalidationTracker;
import androidx.room.RoomDatabase;
import androidx.room.RoomOpenHelper;
import androidx.room.migration.AutoMigrationSpec;
import androidx.room.migration.Migration;
import androidx.room.util.DBUtil;
import androidx.room.util.TableInfo;
import androidx.sqlite.db.SupportSQLiteDatabase;
import androidx.sqlite.db.SupportSQLiteOpenHelper;
import java.lang.Class;
import java.lang.Override;
import java.lang.String;
import java.lang.SuppressWarnings;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Set;
import javax.annotation.processing.Generated;

@Generated("androidx.room.RoomProcessor")
@SuppressWarnings({"unchecked", "deprecation"})
public final class AppDatabase_Impl extends AppDatabase {
  private volatile FastDao _fastDao;

  private volatile TomatoDao _tomatoDao;

  private volatile ScheduleDao _scheduleDao;

  private volatile LockHistoryDao _lockHistoryDao;

  private volatile WhiteAppDao _whiteAppDao;

  private volatile DayLimitDao _dayLimitDao;

  private volatile AppLimitDao _appLimitDao;

  private volatile AppUsageDao _appUsageDao;

  @Override
  @NonNull
  protected SupportSQLiteOpenHelper createOpenHelper(@NonNull final DatabaseConfiguration config) {
    final SupportSQLiteOpenHelper.Callback _openCallback = new RoomOpenHelper(config, new RoomOpenHelper.Delegate(64) {
      @Override
      public void createAllTables(@NonNull final SupportSQLiteDatabase db) {
        db.execSQL("CREATE TABLE IF NOT EXISTS `Tomato` (`id` INTEGER PRIMARY KEY AUTOINCREMENT NOT NULL, `userId` INTEGER NOT NULL, `tomatoIndexId` TEXT NOT NULL, `title` TEXT NOT NULL, `tomatoWorkLength` INTEGER NOT NULL, `tomatoRestLength` INTEGER NOT NULL, `tomatoCount` INTEGER NOT NULL, `tomatoLongRestPerCount` INTEGER NOT NULL, `tomatoLongRestLength` INTEGER NOT NULL, `trend` INTEGER NOT NULL, `syncState` INTEGER NOT NULL, `syncTime` INTEGER NOT NULL, `uuid` INTEGER NOT NULL, `version` INTEGER NOT NULL, `bgUrl` TEXT NOT NULL, `isRemoveNotification` INTEGER NOT NULL, `isSilent` INTEGER NOT NULL, `startVoiceNotify` INTEGER NOT NULL, `endVoiceNotify` INTEGER NOT NULL, `startShakeNotify` INTEGER NOT NULL, `endShakeNotify` INTEGER NOT NULL, `whiteFollowGlobal` INTEGER NOT NULL, `bgUrlFollowGlobal` INTEGER NOT NULL, `isRemoveNotificationFollowGlobal` INTEGER NOT NULL, `isSilentFollowGlobal` INTEGER NOT NULL, `startVoiceNotifyFollowGlobal` INTEGER NOT NULL, `endVoiceNotifyFollowGlobal` INTEGER NOT NULL, `startShakeNotifyFollowGlobal` INTEGER NOT NULL, `endShakeNotifyFollowGlobal` INTEGER NOT NULL)");
        db.execSQL("CREATE UNIQUE INDEX IF NOT EXISTS `index_Tomato_tomatoIndexId` ON `Tomato` (`tomatoIndexId`)");
        db.execSQL("CREATE TABLE IF NOT EXISTS `WhiteApp` (`id` INTEGER PRIMARY KEY AUTOINCREMENT NOT NULL, `userId` INTEGER NOT NULL, `whiteAppIndexId` TEXT NOT NULL, `tomatoIndexId` TEXT NOT NULL, `scheduleIndexId` TEXT NOT NULL, `pkg` TEXT NOT NULL, `mainActivity` TEXT NOT NULL, `maxLen` INTEGER NOT NULL, `trend` INTEGER NOT NULL, `syncState` INTEGER NOT NULL, `syncTime` INTEGER NOT NULL, `uuid` INTEGER NOT NULL, `version` INTEGER NOT NULL)");
        db.execSQL("CREATE INDEX IF NOT EXISTS `index_WhiteApp_tomatoIndexId` ON `WhiteApp` (`tomatoIndexId`)");
        db.execSQL("CREATE INDEX IF NOT EXISTS `index_WhiteApp_scheduleIndexId` ON `WhiteApp` (`scheduleIndexId`)");
        db.execSQL("CREATE UNIQUE INDEX IF NOT EXISTS `index_WhiteApp_whiteAppIndexId` ON `WhiteApp` (`whiteAppIndexId`)");
        db.execSQL("CREATE TABLE IF NOT EXISTS `LockHistory` (`id` INTEGER PRIMARY KEY AUTOINCREMENT NOT NULL, `title` TEXT NOT NULL, `content` TEXT NOT NULL DEFAULT '', `startTime` INTEGER NOT NULL, `trueStartTime` INTEGER NOT NULL, `timeLength` INTEGER NOT NULL, `trueTimeLength` INTEGER NOT NULL, `lockType` INTEGER NOT NULL, `simpleLockLength` INTEGER NOT NULL, `tomatoIndexId` TEXT NOT NULL, `scheduleIndexId` TEXT NOT NULL, `isFinish` INTEGER NOT NULL, `isForceQuit` INTEGER NOT NULL, `isSynced` INTEGER NOT NULL, `isGeneratedCard` INTEGER NOT NULL, `deleteWhiteAppTemp` TEXT NOT NULL DEFAULT '[]')");
        db.execSQL("CREATE TABLE IF NOT EXISTS `Schedule` (`id` INTEGER PRIMARY KEY AUTOINCREMENT NOT NULL, `userId` INTEGER NOT NULL, `title` TEXT NOT NULL, `tomatoIndexId` TEXT NOT NULL, `scheduleIndexId` TEXT NOT NULL, `startHour` INTEGER NOT NULL, `startMinute` INTEGER NOT NULL, `validate` INTEGER NOT NULL, `sunday` INTEGER NOT NULL, `monday` INTEGER NOT NULL, `tuesday` INTEGER NOT NULL, `wednesday` INTEGER NOT NULL, `thursday` INTEGER NOT NULL, `friday` INTEGER NOT NULL, `saturday` INTEGER NOT NULL, `useTomato` INTEGER NOT NULL, `endHour` INTEGER NOT NULL, `endMinute` INTEGER NOT NULL, `isRecycle` INTEGER NOT NULL, `isDenyChange` INTEGER NOT NULL, `denyChangeLength` INTEGER NOT NULL DEFAULT 60, `jumpDate` TEXT NOT NULL, `trend` INTEGER NOT NULL, `syncState` INTEGER NOT NULL, `syncTime` INTEGER NOT NULL, `uuid` INTEGER NOT NULL, `version` INTEGER NOT NULL, `bgUrl` TEXT NOT NULL, `isRemoveNotification` INTEGER NOT NULL, `isSilent` INTEGER NOT NULL, `startVoiceNotify` INTEGER NOT NULL, `endVoiceNotify` INTEGER NOT NULL, `startShakeNotify` INTEGER NOT NULL, `endShakeNotify` INTEGER NOT NULL, `whiteFollowGlobal` INTEGER NOT NULL, `bgUrlFollowGlobal` INTEGER NOT NULL, `isRemoveNotificationFollowGlobal` INTEGER NOT NULL, `isSilentFollowGlobal` INTEGER NOT NULL, `startVoiceNotifyFollowGlobal` INTEGER NOT NULL, `endVoiceNotifyFollowGlobal` INTEGER NOT NULL, `startShakeNotifyFollowGlobal` INTEGER NOT NULL, `endShakeNotifyFollowGlobal` INTEGER NOT NULL)");
        db.execSQL("CREATE UNIQUE INDEX IF NOT EXISTS `index_Schedule_scheduleIndexId` ON `Schedule` (`scheduleIndexId`)");
        db.execSQL("CREATE TABLE IF NOT EXISTS `Fast` (`id` INTEGER PRIMARY KEY AUTOINCREMENT NOT NULL, `userId` INTEGER NOT NULL, `fastIndexId` TEXT NOT NULL, `length` INTEGER NOT NULL, `trend` INTEGER NOT NULL, `syncState` INTEGER NOT NULL, `syncTime` INTEGER NOT NULL, `uuid` INTEGER NOT NULL, `version` INTEGER NOT NULL)");
        db.execSQL("CREATE UNIQUE INDEX IF NOT EXISTS `index_Fast_fastIndexId` ON `Fast` (`fastIndexId`)");
        db.execSQL("CREATE TABLE IF NOT EXISTS `DayLimit` (`id` INTEGER PRIMARY KEY AUTOINCREMENT NOT NULL, `userId` INTEGER NOT NULL, `allDayLimit` INTEGER NOT NULL, `isIncludeWhite` INTEGER NOT NULL, `isDenyChange` INTEGER NOT NULL, `jumpDate` TEXT NOT NULL, `denyChangeLength` INTEGER NOT NULL DEFAULT 20, `isWorkDayLimit` INTEGER NOT NULL DEFAULT true, `monday` INTEGER NOT NULL DEFAULT true, `tuesday` INTEGER NOT NULL DEFAULT true, `wednesday` INTEGER NOT NULL DEFAULT true, `thursday` INTEGER NOT NULL DEFAULT true, `friday` INTEGER NOT NULL DEFAULT true, `saturday` INTEGER NOT NULL DEFAULT false, `sunday` INTEGER NOT NULL DEFAULT false)");
        db.execSQL("CREATE TABLE IF NOT EXISTS `AppLimit` (`id` INTEGER PRIMARY KEY AUTOINCREMENT NOT NULL, `userId` INTEGER NOT NULL, `appLimitIndexId` TEXT NOT NULL, `appPkg` TEXT NOT NULL, `ifAllDay` INTEGER NOT NULL, `startTime` INTEGER NOT NULL, `endTime` INTEGER NOT NULL, `limitLength` INTEGER NOT NULL, `title` TEXT NOT NULL, `sunday` INTEGER NOT NULL, `monday` INTEGER NOT NULL, `tuesday` INTEGER NOT NULL, `wednesday` INTEGER NOT NULL, `thursday` INTEGER NOT NULL, `friday` INTEGER NOT NULL, `saturday` INTEGER NOT NULL, `editStartTime` INTEGER NOT NULL, `editEndTime` INTEGER NOT NULL, `editMoney` INTEGER NOT NULL, `valid` INTEGER NOT NULL, `trend` INTEGER NOT NULL, `syncState` INTEGER NOT NULL, `syncTime` INTEGER NOT NULL, `uuid` INTEGER NOT NULL, `version` INTEGER NOT NULL)");
        db.execSQL("CREATE UNIQUE INDEX IF NOT EXISTS `index_AppLimit_appLimitIndexId` ON `AppLimit` (`appLimitIndexId`)");
        db.execSQL("CREATE TABLE IF NOT EXISTS `AppUsage` (`id` INTEGER PRIMARY KEY AUTOINCREMENT NOT NULL, `appPkg` TEXT NOT NULL, `timestamp` INTEGER NOT NULL, `event` INTEGER NOT NULL)");
        db.execSQL("CREATE UNIQUE INDEX IF NOT EXISTS `index_AppUsage_timestamp` ON `AppUsage` (`timestamp`)");
        db.execSQL("CREATE INDEX IF NOT EXISTS `index_AppUsage_appPkg` ON `AppUsage` (`appPkg`)");
        db.execSQL("CREATE TABLE IF NOT EXISTS room_master_table (id INTEGER PRIMARY KEY,identity_hash TEXT)");
        db.execSQL("INSERT OR REPLACE INTO room_master_table (id,identity_hash) VALUES(42, '0808babf03197daf239e6e5f99d67149')");
      }

      @Override
      public void dropAllTables(@NonNull final SupportSQLiteDatabase db) {
        db.execSQL("DROP TABLE IF EXISTS `Tomato`");
        db.execSQL("DROP TABLE IF EXISTS `WhiteApp`");
        db.execSQL("DROP TABLE IF EXISTS `LockHistory`");
        db.execSQL("DROP TABLE IF EXISTS `Schedule`");
        db.execSQL("DROP TABLE IF EXISTS `Fast`");
        db.execSQL("DROP TABLE IF EXISTS `DayLimit`");
        db.execSQL("DROP TABLE IF EXISTS `AppLimit`");
        db.execSQL("DROP TABLE IF EXISTS `AppUsage`");
        final List<? extends RoomDatabase.Callback> _callbacks = mCallbacks;
        if (_callbacks != null) {
          for (RoomDatabase.Callback _callback : _callbacks) {
            _callback.onDestructiveMigration(db);
          }
        }
      }

      @Override
      public void onCreate(@NonNull final SupportSQLiteDatabase db) {
        final List<? extends RoomDatabase.Callback> _callbacks = mCallbacks;
        if (_callbacks != null) {
          for (RoomDatabase.Callback _callback : _callbacks) {
            _callback.onCreate(db);
          }
        }
      }

      @Override
      public void onOpen(@NonNull final SupportSQLiteDatabase db) {
        mDatabase = db;
        internalInitInvalidationTracker(db);
        final List<? extends RoomDatabase.Callback> _callbacks = mCallbacks;
        if (_callbacks != null) {
          for (RoomDatabase.Callback _callback : _callbacks) {
            _callback.onOpen(db);
          }
        }
      }

      @Override
      public void onPreMigrate(@NonNull final SupportSQLiteDatabase db) {
        DBUtil.dropFtsSyncTriggers(db);
      }

      @Override
      public void onPostMigrate(@NonNull final SupportSQLiteDatabase db) {
      }

      @Override
      @NonNull
      public RoomOpenHelper.ValidationResult onValidateSchema(
          @NonNull final SupportSQLiteDatabase db) {
        final HashMap<String, TableInfo.Column> _columnsTomato = new HashMap<String, TableInfo.Column>(29);
        _columnsTomato.put("id", new TableInfo.Column("id", "INTEGER", true, 1, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsTomato.put("userId", new TableInfo.Column("userId", "INTEGER", true, 0, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsTomato.put("tomatoIndexId", new TableInfo.Column("tomatoIndexId", "TEXT", true, 0, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsTomato.put("title", new TableInfo.Column("title", "TEXT", true, 0, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsTomato.put("tomatoWorkLength", new TableInfo.Column("tomatoWorkLength", "INTEGER", true, 0, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsTomato.put("tomatoRestLength", new TableInfo.Column("tomatoRestLength", "INTEGER", true, 0, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsTomato.put("tomatoCount", new TableInfo.Column("tomatoCount", "INTEGER", true, 0, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsTomato.put("tomatoLongRestPerCount", new TableInfo.Column("tomatoLongRestPerCount", "INTEGER", true, 0, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsTomato.put("tomatoLongRestLength", new TableInfo.Column("tomatoLongRestLength", "INTEGER", true, 0, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsTomato.put("trend", new TableInfo.Column("trend", "INTEGER", true, 0, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsTomato.put("syncState", new TableInfo.Column("syncState", "INTEGER", true, 0, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsTomato.put("syncTime", new TableInfo.Column("syncTime", "INTEGER", true, 0, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsTomato.put("uuid", new TableInfo.Column("uuid", "INTEGER", true, 0, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsTomato.put("version", new TableInfo.Column("version", "INTEGER", true, 0, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsTomato.put("bgUrl", new TableInfo.Column("bgUrl", "TEXT", true, 0, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsTomato.put("isRemoveNotification", new TableInfo.Column("isRemoveNotification", "INTEGER", true, 0, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsTomato.put("isSilent", new TableInfo.Column("isSilent", "INTEGER", true, 0, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsTomato.put("startVoiceNotify", new TableInfo.Column("startVoiceNotify", "INTEGER", true, 0, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsTomato.put("endVoiceNotify", new TableInfo.Column("endVoiceNotify", "INTEGER", true, 0, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsTomato.put("startShakeNotify", new TableInfo.Column("startShakeNotify", "INTEGER", true, 0, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsTomato.put("endShakeNotify", new TableInfo.Column("endShakeNotify", "INTEGER", true, 0, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsTomato.put("whiteFollowGlobal", new TableInfo.Column("whiteFollowGlobal", "INTEGER", true, 0, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsTomato.put("bgUrlFollowGlobal", new TableInfo.Column("bgUrlFollowGlobal", "INTEGER", true, 0, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsTomato.put("isRemoveNotificationFollowGlobal", new TableInfo.Column("isRemoveNotificationFollowGlobal", "INTEGER", true, 0, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsTomato.put("isSilentFollowGlobal", new TableInfo.Column("isSilentFollowGlobal", "INTEGER", true, 0, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsTomato.put("startVoiceNotifyFollowGlobal", new TableInfo.Column("startVoiceNotifyFollowGlobal", "INTEGER", true, 0, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsTomato.put("endVoiceNotifyFollowGlobal", new TableInfo.Column("endVoiceNotifyFollowGlobal", "INTEGER", true, 0, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsTomato.put("startShakeNotifyFollowGlobal", new TableInfo.Column("startShakeNotifyFollowGlobal", "INTEGER", true, 0, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsTomato.put("endShakeNotifyFollowGlobal", new TableInfo.Column("endShakeNotifyFollowGlobal", "INTEGER", true, 0, null, TableInfo.CREATED_FROM_ENTITY));
        final HashSet<TableInfo.ForeignKey> _foreignKeysTomato = new HashSet<TableInfo.ForeignKey>(0);
        final HashSet<TableInfo.Index> _indicesTomato = new HashSet<TableInfo.Index>(1);
        _indicesTomato.add(new TableInfo.Index("index_Tomato_tomatoIndexId", true, Arrays.asList("tomatoIndexId"), Arrays.asList("ASC")));
        final TableInfo _infoTomato = new TableInfo("Tomato", _columnsTomato, _foreignKeysTomato, _indicesTomato);
        final TableInfo _existingTomato = TableInfo.read(db, "Tomato");
        if (!_infoTomato.equals(_existingTomato)) {
          return new RoomOpenHelper.ValidationResult(false, "Tomato(com.lijianqiang12.silent.data.model.db.Tomato).\n"
                  + " Expected:\n" + _infoTomato + "\n"
                  + " Found:\n" + _existingTomato);
        }
        final HashMap<String, TableInfo.Column> _columnsWhiteApp = new HashMap<String, TableInfo.Column>(13);
        _columnsWhiteApp.put("id", new TableInfo.Column("id", "INTEGER", true, 1, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsWhiteApp.put("userId", new TableInfo.Column("userId", "INTEGER", true, 0, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsWhiteApp.put("whiteAppIndexId", new TableInfo.Column("whiteAppIndexId", "TEXT", true, 0, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsWhiteApp.put("tomatoIndexId", new TableInfo.Column("tomatoIndexId", "TEXT", true, 0, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsWhiteApp.put("scheduleIndexId", new TableInfo.Column("scheduleIndexId", "TEXT", true, 0, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsWhiteApp.put("pkg", new TableInfo.Column("pkg", "TEXT", true, 0, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsWhiteApp.put("mainActivity", new TableInfo.Column("mainActivity", "TEXT", true, 0, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsWhiteApp.put("maxLen", new TableInfo.Column("maxLen", "INTEGER", true, 0, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsWhiteApp.put("trend", new TableInfo.Column("trend", "INTEGER", true, 0, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsWhiteApp.put("syncState", new TableInfo.Column("syncState", "INTEGER", true, 0, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsWhiteApp.put("syncTime", new TableInfo.Column("syncTime", "INTEGER", true, 0, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsWhiteApp.put("uuid", new TableInfo.Column("uuid", "INTEGER", true, 0, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsWhiteApp.put("version", new TableInfo.Column("version", "INTEGER", true, 0, null, TableInfo.CREATED_FROM_ENTITY));
        final HashSet<TableInfo.ForeignKey> _foreignKeysWhiteApp = new HashSet<TableInfo.ForeignKey>(0);
        final HashSet<TableInfo.Index> _indicesWhiteApp = new HashSet<TableInfo.Index>(3);
        _indicesWhiteApp.add(new TableInfo.Index("index_WhiteApp_tomatoIndexId", false, Arrays.asList("tomatoIndexId"), Arrays.asList("ASC")));
        _indicesWhiteApp.add(new TableInfo.Index("index_WhiteApp_scheduleIndexId", false, Arrays.asList("scheduleIndexId"), Arrays.asList("ASC")));
        _indicesWhiteApp.add(new TableInfo.Index("index_WhiteApp_whiteAppIndexId", true, Arrays.asList("whiteAppIndexId"), Arrays.asList("ASC")));
        final TableInfo _infoWhiteApp = new TableInfo("WhiteApp", _columnsWhiteApp, _foreignKeysWhiteApp, _indicesWhiteApp);
        final TableInfo _existingWhiteApp = TableInfo.read(db, "WhiteApp");
        if (!_infoWhiteApp.equals(_existingWhiteApp)) {
          return new RoomOpenHelper.ValidationResult(false, "WhiteApp(com.lijianqiang12.silent.data.model.db.WhiteApp).\n"
                  + " Expected:\n" + _infoWhiteApp + "\n"
                  + " Found:\n" + _existingWhiteApp);
        }
        final HashMap<String, TableInfo.Column> _columnsLockHistory = new HashMap<String, TableInfo.Column>(16);
        _columnsLockHistory.put("id", new TableInfo.Column("id", "INTEGER", true, 1, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsLockHistory.put("title", new TableInfo.Column("title", "TEXT", true, 0, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsLockHistory.put("content", new TableInfo.Column("content", "TEXT", true, 0, "''", TableInfo.CREATED_FROM_ENTITY));
        _columnsLockHistory.put("startTime", new TableInfo.Column("startTime", "INTEGER", true, 0, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsLockHistory.put("trueStartTime", new TableInfo.Column("trueStartTime", "INTEGER", true, 0, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsLockHistory.put("timeLength", new TableInfo.Column("timeLength", "INTEGER", true, 0, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsLockHistory.put("trueTimeLength", new TableInfo.Column("trueTimeLength", "INTEGER", true, 0, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsLockHistory.put("lockType", new TableInfo.Column("lockType", "INTEGER", true, 0, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsLockHistory.put("simpleLockLength", new TableInfo.Column("simpleLockLength", "INTEGER", true, 0, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsLockHistory.put("tomatoIndexId", new TableInfo.Column("tomatoIndexId", "TEXT", true, 0, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsLockHistory.put("scheduleIndexId", new TableInfo.Column("scheduleIndexId", "TEXT", true, 0, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsLockHistory.put("isFinish", new TableInfo.Column("isFinish", "INTEGER", true, 0, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsLockHistory.put("isForceQuit", new TableInfo.Column("isForceQuit", "INTEGER", true, 0, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsLockHistory.put("isSynced", new TableInfo.Column("isSynced", "INTEGER", true, 0, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsLockHistory.put("isGeneratedCard", new TableInfo.Column("isGeneratedCard", "INTEGER", true, 0, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsLockHistory.put("deleteWhiteAppTemp", new TableInfo.Column("deleteWhiteAppTemp", "TEXT", true, 0, "'[]'", TableInfo.CREATED_FROM_ENTITY));
        final HashSet<TableInfo.ForeignKey> _foreignKeysLockHistory = new HashSet<TableInfo.ForeignKey>(0);
        final HashSet<TableInfo.Index> _indicesLockHistory = new HashSet<TableInfo.Index>(0);
        final TableInfo _infoLockHistory = new TableInfo("LockHistory", _columnsLockHistory, _foreignKeysLockHistory, _indicesLockHistory);
        final TableInfo _existingLockHistory = TableInfo.read(db, "LockHistory");
        if (!_infoLockHistory.equals(_existingLockHistory)) {
          return new RoomOpenHelper.ValidationResult(false, "LockHistory(com.lijianqiang12.silent.data.model.db.LockHistory).\n"
                  + " Expected:\n" + _infoLockHistory + "\n"
                  + " Found:\n" + _existingLockHistory);
        }
        final HashMap<String, TableInfo.Column> _columnsSchedule = new HashMap<String, TableInfo.Column>(42);
        _columnsSchedule.put("id", new TableInfo.Column("id", "INTEGER", true, 1, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsSchedule.put("userId", new TableInfo.Column("userId", "INTEGER", true, 0, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsSchedule.put("title", new TableInfo.Column("title", "TEXT", true, 0, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsSchedule.put("tomatoIndexId", new TableInfo.Column("tomatoIndexId", "TEXT", true, 0, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsSchedule.put("scheduleIndexId", new TableInfo.Column("scheduleIndexId", "TEXT", true, 0, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsSchedule.put("startHour", new TableInfo.Column("startHour", "INTEGER", true, 0, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsSchedule.put("startMinute", new TableInfo.Column("startMinute", "INTEGER", true, 0, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsSchedule.put("validate", new TableInfo.Column("validate", "INTEGER", true, 0, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsSchedule.put("sunday", new TableInfo.Column("sunday", "INTEGER", true, 0, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsSchedule.put("monday", new TableInfo.Column("monday", "INTEGER", true, 0, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsSchedule.put("tuesday", new TableInfo.Column("tuesday", "INTEGER", true, 0, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsSchedule.put("wednesday", new TableInfo.Column("wednesday", "INTEGER", true, 0, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsSchedule.put("thursday", new TableInfo.Column("thursday", "INTEGER", true, 0, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsSchedule.put("friday", new TableInfo.Column("friday", "INTEGER", true, 0, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsSchedule.put("saturday", new TableInfo.Column("saturday", "INTEGER", true, 0, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsSchedule.put("useTomato", new TableInfo.Column("useTomato", "INTEGER", true, 0, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsSchedule.put("endHour", new TableInfo.Column("endHour", "INTEGER", true, 0, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsSchedule.put("endMinute", new TableInfo.Column("endMinute", "INTEGER", true, 0, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsSchedule.put("isRecycle", new TableInfo.Column("isRecycle", "INTEGER", true, 0, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsSchedule.put("isDenyChange", new TableInfo.Column("isDenyChange", "INTEGER", true, 0, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsSchedule.put("denyChangeLength", new TableInfo.Column("denyChangeLength", "INTEGER", true, 0, "60", TableInfo.CREATED_FROM_ENTITY));
        _columnsSchedule.put("jumpDate", new TableInfo.Column("jumpDate", "TEXT", true, 0, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsSchedule.put("trend", new TableInfo.Column("trend", "INTEGER", true, 0, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsSchedule.put("syncState", new TableInfo.Column("syncState", "INTEGER", true, 0, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsSchedule.put("syncTime", new TableInfo.Column("syncTime", "INTEGER", true, 0, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsSchedule.put("uuid", new TableInfo.Column("uuid", "INTEGER", true, 0, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsSchedule.put("version", new TableInfo.Column("version", "INTEGER", true, 0, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsSchedule.put("bgUrl", new TableInfo.Column("bgUrl", "TEXT", true, 0, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsSchedule.put("isRemoveNotification", new TableInfo.Column("isRemoveNotification", "INTEGER", true, 0, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsSchedule.put("isSilent", new TableInfo.Column("isSilent", "INTEGER", true, 0, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsSchedule.put("startVoiceNotify", new TableInfo.Column("startVoiceNotify", "INTEGER", true, 0, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsSchedule.put("endVoiceNotify", new TableInfo.Column("endVoiceNotify", "INTEGER", true, 0, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsSchedule.put("startShakeNotify", new TableInfo.Column("startShakeNotify", "INTEGER", true, 0, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsSchedule.put("endShakeNotify", new TableInfo.Column("endShakeNotify", "INTEGER", true, 0, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsSchedule.put("whiteFollowGlobal", new TableInfo.Column("whiteFollowGlobal", "INTEGER", true, 0, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsSchedule.put("bgUrlFollowGlobal", new TableInfo.Column("bgUrlFollowGlobal", "INTEGER", true, 0, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsSchedule.put("isRemoveNotificationFollowGlobal", new TableInfo.Column("isRemoveNotificationFollowGlobal", "INTEGER", true, 0, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsSchedule.put("isSilentFollowGlobal", new TableInfo.Column("isSilentFollowGlobal", "INTEGER", true, 0, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsSchedule.put("startVoiceNotifyFollowGlobal", new TableInfo.Column("startVoiceNotifyFollowGlobal", "INTEGER", true, 0, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsSchedule.put("endVoiceNotifyFollowGlobal", new TableInfo.Column("endVoiceNotifyFollowGlobal", "INTEGER", true, 0, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsSchedule.put("startShakeNotifyFollowGlobal", new TableInfo.Column("startShakeNotifyFollowGlobal", "INTEGER", true, 0, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsSchedule.put("endShakeNotifyFollowGlobal", new TableInfo.Column("endShakeNotifyFollowGlobal", "INTEGER", true, 0, null, TableInfo.CREATED_FROM_ENTITY));
        final HashSet<TableInfo.ForeignKey> _foreignKeysSchedule = new HashSet<TableInfo.ForeignKey>(0);
        final HashSet<TableInfo.Index> _indicesSchedule = new HashSet<TableInfo.Index>(1);
        _indicesSchedule.add(new TableInfo.Index("index_Schedule_scheduleIndexId", true, Arrays.asList("scheduleIndexId"), Arrays.asList("ASC")));
        final TableInfo _infoSchedule = new TableInfo("Schedule", _columnsSchedule, _foreignKeysSchedule, _indicesSchedule);
        final TableInfo _existingSchedule = TableInfo.read(db, "Schedule");
        if (!_infoSchedule.equals(_existingSchedule)) {
          return new RoomOpenHelper.ValidationResult(false, "Schedule(com.lijianqiang12.silent.data.model.db.Schedule).\n"
                  + " Expected:\n" + _infoSchedule + "\n"
                  + " Found:\n" + _existingSchedule);
        }
        final HashMap<String, TableInfo.Column> _columnsFast = new HashMap<String, TableInfo.Column>(9);
        _columnsFast.put("id", new TableInfo.Column("id", "INTEGER", true, 1, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsFast.put("userId", new TableInfo.Column("userId", "INTEGER", true, 0, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsFast.put("fastIndexId", new TableInfo.Column("fastIndexId", "TEXT", true, 0, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsFast.put("length", new TableInfo.Column("length", "INTEGER", true, 0, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsFast.put("trend", new TableInfo.Column("trend", "INTEGER", true, 0, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsFast.put("syncState", new TableInfo.Column("syncState", "INTEGER", true, 0, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsFast.put("syncTime", new TableInfo.Column("syncTime", "INTEGER", true, 0, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsFast.put("uuid", new TableInfo.Column("uuid", "INTEGER", true, 0, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsFast.put("version", new TableInfo.Column("version", "INTEGER", true, 0, null, TableInfo.CREATED_FROM_ENTITY));
        final HashSet<TableInfo.ForeignKey> _foreignKeysFast = new HashSet<TableInfo.ForeignKey>(0);
        final HashSet<TableInfo.Index> _indicesFast = new HashSet<TableInfo.Index>(1);
        _indicesFast.add(new TableInfo.Index("index_Fast_fastIndexId", true, Arrays.asList("fastIndexId"), Arrays.asList("ASC")));
        final TableInfo _infoFast = new TableInfo("Fast", _columnsFast, _foreignKeysFast, _indicesFast);
        final TableInfo _existingFast = TableInfo.read(db, "Fast");
        if (!_infoFast.equals(_existingFast)) {
          return new RoomOpenHelper.ValidationResult(false, "Fast(com.lijianqiang12.silent.data.model.db.Fast).\n"
                  + " Expected:\n" + _infoFast + "\n"
                  + " Found:\n" + _existingFast);
        }
        final HashMap<String, TableInfo.Column> _columnsDayLimit = new HashMap<String, TableInfo.Column>(15);
        _columnsDayLimit.put("id", new TableInfo.Column("id", "INTEGER", true, 1, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsDayLimit.put("userId", new TableInfo.Column("userId", "INTEGER", true, 0, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsDayLimit.put("allDayLimit", new TableInfo.Column("allDayLimit", "INTEGER", true, 0, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsDayLimit.put("isIncludeWhite", new TableInfo.Column("isIncludeWhite", "INTEGER", true, 0, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsDayLimit.put("isDenyChange", new TableInfo.Column("isDenyChange", "INTEGER", true, 0, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsDayLimit.put("jumpDate", new TableInfo.Column("jumpDate", "TEXT", true, 0, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsDayLimit.put("denyChangeLength", new TableInfo.Column("denyChangeLength", "INTEGER", true, 0, "20", TableInfo.CREATED_FROM_ENTITY));
        _columnsDayLimit.put("isWorkDayLimit", new TableInfo.Column("isWorkDayLimit", "INTEGER", true, 0, "true", TableInfo.CREATED_FROM_ENTITY));
        _columnsDayLimit.put("monday", new TableInfo.Column("monday", "INTEGER", true, 0, "true", TableInfo.CREATED_FROM_ENTITY));
        _columnsDayLimit.put("tuesday", new TableInfo.Column("tuesday", "INTEGER", true, 0, "true", TableInfo.CREATED_FROM_ENTITY));
        _columnsDayLimit.put("wednesday", new TableInfo.Column("wednesday", "INTEGER", true, 0, "true", TableInfo.CREATED_FROM_ENTITY));
        _columnsDayLimit.put("thursday", new TableInfo.Column("thursday", "INTEGER", true, 0, "true", TableInfo.CREATED_FROM_ENTITY));
        _columnsDayLimit.put("friday", new TableInfo.Column("friday", "INTEGER", true, 0, "true", TableInfo.CREATED_FROM_ENTITY));
        _columnsDayLimit.put("saturday", new TableInfo.Column("saturday", "INTEGER", true, 0, "false", TableInfo.CREATED_FROM_ENTITY));
        _columnsDayLimit.put("sunday", new TableInfo.Column("sunday", "INTEGER", true, 0, "false", TableInfo.CREATED_FROM_ENTITY));
        final HashSet<TableInfo.ForeignKey> _foreignKeysDayLimit = new HashSet<TableInfo.ForeignKey>(0);
        final HashSet<TableInfo.Index> _indicesDayLimit = new HashSet<TableInfo.Index>(0);
        final TableInfo _infoDayLimit = new TableInfo("DayLimit", _columnsDayLimit, _foreignKeysDayLimit, _indicesDayLimit);
        final TableInfo _existingDayLimit = TableInfo.read(db, "DayLimit");
        if (!_infoDayLimit.equals(_existingDayLimit)) {
          return new RoomOpenHelper.ValidationResult(false, "DayLimit(com.lijianqiang12.silent.data.model.db.DayLimit).\n"
                  + " Expected:\n" + _infoDayLimit + "\n"
                  + " Found:\n" + _existingDayLimit);
        }
        final HashMap<String, TableInfo.Column> _columnsAppLimit = new HashMap<String, TableInfo.Column>(25);
        _columnsAppLimit.put("id", new TableInfo.Column("id", "INTEGER", true, 1, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsAppLimit.put("userId", new TableInfo.Column("userId", "INTEGER", true, 0, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsAppLimit.put("appLimitIndexId", new TableInfo.Column("appLimitIndexId", "TEXT", true, 0, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsAppLimit.put("appPkg", new TableInfo.Column("appPkg", "TEXT", true, 0, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsAppLimit.put("ifAllDay", new TableInfo.Column("ifAllDay", "INTEGER", true, 0, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsAppLimit.put("startTime", new TableInfo.Column("startTime", "INTEGER", true, 0, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsAppLimit.put("endTime", new TableInfo.Column("endTime", "INTEGER", true, 0, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsAppLimit.put("limitLength", new TableInfo.Column("limitLength", "INTEGER", true, 0, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsAppLimit.put("title", new TableInfo.Column("title", "TEXT", true, 0, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsAppLimit.put("sunday", new TableInfo.Column("sunday", "INTEGER", true, 0, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsAppLimit.put("monday", new TableInfo.Column("monday", "INTEGER", true, 0, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsAppLimit.put("tuesday", new TableInfo.Column("tuesday", "INTEGER", true, 0, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsAppLimit.put("wednesday", new TableInfo.Column("wednesday", "INTEGER", true, 0, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsAppLimit.put("thursday", new TableInfo.Column("thursday", "INTEGER", true, 0, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsAppLimit.put("friday", new TableInfo.Column("friday", "INTEGER", true, 0, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsAppLimit.put("saturday", new TableInfo.Column("saturday", "INTEGER", true, 0, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsAppLimit.put("editStartTime", new TableInfo.Column("editStartTime", "INTEGER", true, 0, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsAppLimit.put("editEndTime", new TableInfo.Column("editEndTime", "INTEGER", true, 0, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsAppLimit.put("editMoney", new TableInfo.Column("editMoney", "INTEGER", true, 0, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsAppLimit.put("valid", new TableInfo.Column("valid", "INTEGER", true, 0, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsAppLimit.put("trend", new TableInfo.Column("trend", "INTEGER", true, 0, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsAppLimit.put("syncState", new TableInfo.Column("syncState", "INTEGER", true, 0, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsAppLimit.put("syncTime", new TableInfo.Column("syncTime", "INTEGER", true, 0, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsAppLimit.put("uuid", new TableInfo.Column("uuid", "INTEGER", true, 0, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsAppLimit.put("version", new TableInfo.Column("version", "INTEGER", true, 0, null, TableInfo.CREATED_FROM_ENTITY));
        final HashSet<TableInfo.ForeignKey> _foreignKeysAppLimit = new HashSet<TableInfo.ForeignKey>(0);
        final HashSet<TableInfo.Index> _indicesAppLimit = new HashSet<TableInfo.Index>(1);
        _indicesAppLimit.add(new TableInfo.Index("index_AppLimit_appLimitIndexId", true, Arrays.asList("appLimitIndexId"), Arrays.asList("ASC")));
        final TableInfo _infoAppLimit = new TableInfo("AppLimit", _columnsAppLimit, _foreignKeysAppLimit, _indicesAppLimit);
        final TableInfo _existingAppLimit = TableInfo.read(db, "AppLimit");
        if (!_infoAppLimit.equals(_existingAppLimit)) {
          return new RoomOpenHelper.ValidationResult(false, "AppLimit(com.lijianqiang12.silent.data.model.db.AppLimit).\n"
                  + " Expected:\n" + _infoAppLimit + "\n"
                  + " Found:\n" + _existingAppLimit);
        }
        final HashMap<String, TableInfo.Column> _columnsAppUsage = new HashMap<String, TableInfo.Column>(4);
        _columnsAppUsage.put("id", new TableInfo.Column("id", "INTEGER", true, 1, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsAppUsage.put("appPkg", new TableInfo.Column("appPkg", "TEXT", true, 0, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsAppUsage.put("timestamp", new TableInfo.Column("timestamp", "INTEGER", true, 0, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsAppUsage.put("event", new TableInfo.Column("event", "INTEGER", true, 0, null, TableInfo.CREATED_FROM_ENTITY));
        final HashSet<TableInfo.ForeignKey> _foreignKeysAppUsage = new HashSet<TableInfo.ForeignKey>(0);
        final HashSet<TableInfo.Index> _indicesAppUsage = new HashSet<TableInfo.Index>(2);
        _indicesAppUsage.add(new TableInfo.Index("index_AppUsage_timestamp", true, Arrays.asList("timestamp"), Arrays.asList("ASC")));
        _indicesAppUsage.add(new TableInfo.Index("index_AppUsage_appPkg", false, Arrays.asList("appPkg"), Arrays.asList("ASC")));
        final TableInfo _infoAppUsage = new TableInfo("AppUsage", _columnsAppUsage, _foreignKeysAppUsage, _indicesAppUsage);
        final TableInfo _existingAppUsage = TableInfo.read(db, "AppUsage");
        if (!_infoAppUsage.equals(_existingAppUsage)) {
          return new RoomOpenHelper.ValidationResult(false, "AppUsage(com.lijianqiang12.silent.data.model.db.AppUsage).\n"
                  + " Expected:\n" + _infoAppUsage + "\n"
                  + " Found:\n" + _existingAppUsage);
        }
        return new RoomOpenHelper.ValidationResult(true, null);
      }
    }, "0808babf03197daf239e6e5f99d67149", "43bd8d0f9097dfe9f230786e2ee23cbe");
    final SupportSQLiteOpenHelper.Configuration _sqliteConfig = SupportSQLiteOpenHelper.Configuration.builder(config.context).name(config.name).callback(_openCallback).build();
    final SupportSQLiteOpenHelper _helper = config.sqliteOpenHelperFactory.create(_sqliteConfig);
    return _helper;
  }

  @Override
  @NonNull
  protected InvalidationTracker createInvalidationTracker() {
    final HashMap<String, String> _shadowTablesMap = new HashMap<String, String>(0);
    final HashMap<String, Set<String>> _viewTables = new HashMap<String, Set<String>>(0);
    return new InvalidationTracker(this, _shadowTablesMap, _viewTables, "Tomato","WhiteApp","LockHistory","Schedule","Fast","DayLimit","AppLimit","AppUsage");
  }

  @Override
  public void clearAllTables() {
    super.assertNotMainThread();
    final SupportSQLiteDatabase _db = super.getOpenHelper().getWritableDatabase();
    try {
      super.beginTransaction();
      _db.execSQL("DELETE FROM `Tomato`");
      _db.execSQL("DELETE FROM `WhiteApp`");
      _db.execSQL("DELETE FROM `LockHistory`");
      _db.execSQL("DELETE FROM `Schedule`");
      _db.execSQL("DELETE FROM `Fast`");
      _db.execSQL("DELETE FROM `DayLimit`");
      _db.execSQL("DELETE FROM `AppLimit`");
      _db.execSQL("DELETE FROM `AppUsage`");
      super.setTransactionSuccessful();
    } finally {
      super.endTransaction();
      _db.query("PRAGMA wal_checkpoint(FULL)").close();
      if (!_db.inTransaction()) {
        _db.execSQL("VACUUM");
      }
    }
  }

  @Override
  @NonNull
  protected Map<Class<?>, List<Class<?>>> getRequiredTypeConverters() {
    final HashMap<Class<?>, List<Class<?>>> _typeConvertersMap = new HashMap<Class<?>, List<Class<?>>>();
    _typeConvertersMap.put(FastDao.class, FastDao_Impl.getRequiredConverters());
    _typeConvertersMap.put(TomatoDao.class, TomatoDao_Impl.getRequiredConverters());
    _typeConvertersMap.put(ScheduleDao.class, ScheduleDao_Impl.getRequiredConverters());
    _typeConvertersMap.put(LockHistoryDao.class, LockHistoryDao_Impl.getRequiredConverters());
    _typeConvertersMap.put(WhiteAppDao.class, WhiteAppDao_Impl.getRequiredConverters());
    _typeConvertersMap.put(DayLimitDao.class, DayLimitDao_Impl.getRequiredConverters());
    _typeConvertersMap.put(AppLimitDao.class, AppLimitDao_Impl.getRequiredConverters());
    _typeConvertersMap.put(AppUsageDao.class, AppUsageDao_Impl.getRequiredConverters());
    return _typeConvertersMap;
  }

  @Override
  @NonNull
  public Set<Class<? extends AutoMigrationSpec>> getRequiredAutoMigrationSpecs() {
    final HashSet<Class<? extends AutoMigrationSpec>> _autoMigrationSpecsSet = new HashSet<Class<? extends AutoMigrationSpec>>();
    return _autoMigrationSpecsSet;
  }

  @Override
  @NonNull
  public List<Migration> getAutoMigrations(
      @NonNull final Map<Class<? extends AutoMigrationSpec>, AutoMigrationSpec> autoMigrationSpecs) {
    final List<Migration> _autoMigrations = new ArrayList<Migration>();
    _autoMigrations.add(new AppDatabase_AutoMigration_58_59_Impl());
    _autoMigrations.add(new AppDatabase_AutoMigration_59_60_Impl());
    _autoMigrations.add(new AppDatabase_AutoMigration_60_61_Impl());
    _autoMigrations.add(new AppDatabase_AutoMigration_61_62_Impl());
    _autoMigrations.add(new AppDatabase_AutoMigration_63_64_Impl());
    return _autoMigrations;
  }

  @Override
  public FastDao fastDao() {
    if (_fastDao != null) {
      return _fastDao;
    } else {
      synchronized(this) {
        if(_fastDao == null) {
          _fastDao = new FastDao_Impl(this);
        }
        return _fastDao;
      }
    }
  }

  @Override
  public TomatoDao tomatoDao() {
    if (_tomatoDao != null) {
      return _tomatoDao;
    } else {
      synchronized(this) {
        if(_tomatoDao == null) {
          _tomatoDao = new TomatoDao_Impl(this);
        }
        return _tomatoDao;
      }
    }
  }

  @Override
  public ScheduleDao scheduleDao() {
    if (_scheduleDao != null) {
      return _scheduleDao;
    } else {
      synchronized(this) {
        if(_scheduleDao == null) {
          _scheduleDao = new ScheduleDao_Impl(this);
        }
        return _scheduleDao;
      }
    }
  }

  @Override
  public LockHistoryDao lockHistoryDao() {
    if (_lockHistoryDao != null) {
      return _lockHistoryDao;
    } else {
      synchronized(this) {
        if(_lockHistoryDao == null) {
          _lockHistoryDao = new LockHistoryDao_Impl(this);
        }
        return _lockHistoryDao;
      }
    }
  }

  @Override
  public WhiteAppDao whiteAppDao() {
    if (_whiteAppDao != null) {
      return _whiteAppDao;
    } else {
      synchronized(this) {
        if(_whiteAppDao == null) {
          _whiteAppDao = new WhiteAppDao_Impl(this);
        }
        return _whiteAppDao;
      }
    }
  }

  @Override
  public DayLimitDao dayLimitDao() {
    if (_dayLimitDao != null) {
      return _dayLimitDao;
    } else {
      synchronized(this) {
        if(_dayLimitDao == null) {
          _dayLimitDao = new DayLimitDao_Impl(this);
        }
        return _dayLimitDao;
      }
    }
  }

  @Override
  public AppLimitDao appLimitDao() {
    if (_appLimitDao != null) {
      return _appLimitDao;
    } else {
      synchronized(this) {
        if(_appLimitDao == null) {
          _appLimitDao = new AppLimitDao_Impl(this);
        }
        return _appLimitDao;
      }
    }
  }

  @Override
  public AppUsageDao appUsageDao() {
    if (_appUsageDao != null) {
      return _appUsageDao;
    } else {
      synchronized(this) {
        if(_appUsageDao == null) {
          _appUsageDao = new AppUsageDao_Impl(this);
        }
        return _appUsageDao;
      }
    }
  }
}
