package com.lijianqiang12.silent.data.model.db;

import androidx.annotation.NonNull;
import androidx.room.migration.Migration;
import androidx.sqlite.db.SupportSQLiteDatabase;
import java.lang.Override;
import java.lang.SuppressWarnings;
import javax.annotation.processing.Generated;

@Generated("androidx.room.RoomProcessor")
@SuppressWarnings({"unchecked", "deprecation"})
final class AppDatabase_AutoMigration_59_60_Impl extends Migration {
  public AppDatabase_AutoMigration_59_60_Impl() {
    super(59, 60);
  }

  @Override
  public void migrate(@NonNull final SupportSQLiteDatabase db) {
    db.execSQL("ALTER TABLE `DayLimit` ADD COLUMN `isWorkDayLimit` INTEGER NOT NULL DEFAULT true");
    db.execSQL("ALTER TABLE `DayLimit` ADD COLUMN `monday` INTEGER NOT NULL DEFAULT true");
    db.execSQL("ALTER TABLE `DayLimit` ADD COLUMN `tuesday` INTEGER NOT NULL DEFAULT true");
    db.execSQL("ALTER TABLE `DayLimit` ADD COLUMN `wednesday` INTEGER NOT NULL DEFAULT true");
    db.execSQL("ALTER TABLE `DayLimit` ADD COLUMN `thursday` INTEGER NOT NULL DEFAULT true");
    db.execSQL("ALTER TABLE `DayLimit` ADD COLUMN `friday` INTEGER NOT NULL DEFAULT true");
    db.execSQL("ALTER TABLE `DayLimit` ADD COLUMN `saturday` INTEGER NOT NULL DEFAULT false");
    db.execSQL("ALTER TABLE `DayLimit` ADD COLUMN `sunday` INTEGER NOT NULL DEFAULT false");
  }
}
