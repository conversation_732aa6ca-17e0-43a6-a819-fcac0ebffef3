package com.lijianqiang12.silent.data.model.db;

import android.database.Cursor;
import android.os.CancellationSignal;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.lifecycle.LiveData;
import androidx.room.CoroutinesRoom;
import androidx.room.EntityDeletionOrUpdateAdapter;
import androidx.room.EntityInsertionAdapter;
import androidx.room.RoomDatabase;
import androidx.room.RoomSQLiteQuery;
import androidx.room.SharedSQLiteStatement;
import androidx.room.util.CursorUtil;
import androidx.room.util.DBUtil;
import androidx.sqlite.db.SupportSQLiteStatement;
import java.lang.Class;
import java.lang.Exception;
import java.lang.Object;
import java.lang.Override;
import java.lang.String;
import java.lang.SuppressWarnings;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.concurrent.Callable;
import javax.annotation.processing.Generated;
import kotlin.Unit;
import kotlin.coroutines.Continuation;

@Generated("androidx.room.RoomProcessor")
@SuppressWarnings({"unchecked", "deprecation"})
public final class AppLimitDao_Impl implements AppLimitDao {
  private final RoomDatabase __db;

  private final EntityInsertionAdapter<AppLimit> __insertionAdapterOfAppLimit;

  private final EntityDeletionOrUpdateAdapter<AppLimit> __deletionAdapterOfAppLimit;

  private final EntityDeletionOrUpdateAdapter<AppLimit> __updateAdapterOfAppLimit;

  private final SharedSQLiteStatement __preparedStmtOfDeleteAll;

  private final SharedSQLiteStatement __preparedStmtOfUpdateUserId;

  public AppLimitDao_Impl(@NonNull final RoomDatabase __db) {
    this.__db = __db;
    this.__insertionAdapterOfAppLimit = new EntityInsertionAdapter<AppLimit>(__db) {
      @Override
      @NonNull
      protected String createQuery() {
        return "INSERT OR IGNORE INTO `AppLimit` (`id`,`userId`,`appLimitIndexId`,`appPkg`,`ifAllDay`,`startTime`,`endTime`,`limitLength`,`title`,`sunday`,`monday`,`tuesday`,`wednesday`,`thursday`,`friday`,`saturday`,`editStartTime`,`editEndTime`,`editMoney`,`valid`,`trend`,`syncState`,`syncTime`,`uuid`,`version`) VALUES (nullif(?, 0),?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?)";
      }

      @Override
      protected void bind(@NonNull final SupportSQLiteStatement statement,
          @NonNull final AppLimit entity) {
        statement.bindLong(1, entity.getId());
        statement.bindLong(2, entity.getUserId());
        if (entity.getAppLimitIndexId() == null) {
          statement.bindNull(3);
        } else {
          statement.bindString(3, entity.getAppLimitIndexId());
        }
        if (entity.getAppPkg() == null) {
          statement.bindNull(4);
        } else {
          statement.bindString(4, entity.getAppPkg());
        }
        final int _tmp = entity.getIfAllDay() ? 1 : 0;
        statement.bindLong(5, _tmp);
        statement.bindLong(6, entity.getStartTime());
        statement.bindLong(7, entity.getEndTime());
        statement.bindLong(8, entity.getLimitLength());
        if (entity.getTitle() == null) {
          statement.bindNull(9);
        } else {
          statement.bindString(9, entity.getTitle());
        }
        final int _tmp_1 = entity.getSunday() ? 1 : 0;
        statement.bindLong(10, _tmp_1);
        final int _tmp_2 = entity.getMonday() ? 1 : 0;
        statement.bindLong(11, _tmp_2);
        final int _tmp_3 = entity.getTuesday() ? 1 : 0;
        statement.bindLong(12, _tmp_3);
        final int _tmp_4 = entity.getWednesday() ? 1 : 0;
        statement.bindLong(13, _tmp_4);
        final int _tmp_5 = entity.getThursday() ? 1 : 0;
        statement.bindLong(14, _tmp_5);
        final int _tmp_6 = entity.getFriday() ? 1 : 0;
        statement.bindLong(15, _tmp_6);
        final int _tmp_7 = entity.getSaturday() ? 1 : 0;
        statement.bindLong(16, _tmp_7);
        statement.bindLong(17, entity.getEditStartTime());
        statement.bindLong(18, entity.getEditEndTime());
        statement.bindLong(19, entity.getEditMoney());
        final int _tmp_8 = entity.getValid() ? 1 : 0;
        statement.bindLong(20, _tmp_8);
        statement.bindLong(21, entity.getTrend());
        statement.bindLong(22, entity.getSyncState());
        statement.bindLong(23, entity.getSyncTime());
        statement.bindLong(24, entity.getUuid());
        statement.bindLong(25, entity.getVersion());
      }
    };
    this.__deletionAdapterOfAppLimit = new EntityDeletionOrUpdateAdapter<AppLimit>(__db) {
      @Override
      @NonNull
      protected String createQuery() {
        return "DELETE FROM `AppLimit` WHERE `id` = ?";
      }

      @Override
      protected void bind(@NonNull final SupportSQLiteStatement statement,
          @NonNull final AppLimit entity) {
        statement.bindLong(1, entity.getId());
      }
    };
    this.__updateAdapterOfAppLimit = new EntityDeletionOrUpdateAdapter<AppLimit>(__db) {
      @Override
      @NonNull
      protected String createQuery() {
        return "UPDATE OR ABORT `AppLimit` SET `id` = ?,`userId` = ?,`appLimitIndexId` = ?,`appPkg` = ?,`ifAllDay` = ?,`startTime` = ?,`endTime` = ?,`limitLength` = ?,`title` = ?,`sunday` = ?,`monday` = ?,`tuesday` = ?,`wednesday` = ?,`thursday` = ?,`friday` = ?,`saturday` = ?,`editStartTime` = ?,`editEndTime` = ?,`editMoney` = ?,`valid` = ?,`trend` = ?,`syncState` = ?,`syncTime` = ?,`uuid` = ?,`version` = ? WHERE `id` = ?";
      }

      @Override
      protected void bind(@NonNull final SupportSQLiteStatement statement,
          @NonNull final AppLimit entity) {
        statement.bindLong(1, entity.getId());
        statement.bindLong(2, entity.getUserId());
        if (entity.getAppLimitIndexId() == null) {
          statement.bindNull(3);
        } else {
          statement.bindString(3, entity.getAppLimitIndexId());
        }
        if (entity.getAppPkg() == null) {
          statement.bindNull(4);
        } else {
          statement.bindString(4, entity.getAppPkg());
        }
        final int _tmp = entity.getIfAllDay() ? 1 : 0;
        statement.bindLong(5, _tmp);
        statement.bindLong(6, entity.getStartTime());
        statement.bindLong(7, entity.getEndTime());
        statement.bindLong(8, entity.getLimitLength());
        if (entity.getTitle() == null) {
          statement.bindNull(9);
        } else {
          statement.bindString(9, entity.getTitle());
        }
        final int _tmp_1 = entity.getSunday() ? 1 : 0;
        statement.bindLong(10, _tmp_1);
        final int _tmp_2 = entity.getMonday() ? 1 : 0;
        statement.bindLong(11, _tmp_2);
        final int _tmp_3 = entity.getTuesday() ? 1 : 0;
        statement.bindLong(12, _tmp_3);
        final int _tmp_4 = entity.getWednesday() ? 1 : 0;
        statement.bindLong(13, _tmp_4);
        final int _tmp_5 = entity.getThursday() ? 1 : 0;
        statement.bindLong(14, _tmp_5);
        final int _tmp_6 = entity.getFriday() ? 1 : 0;
        statement.bindLong(15, _tmp_6);
        final int _tmp_7 = entity.getSaturday() ? 1 : 0;
        statement.bindLong(16, _tmp_7);
        statement.bindLong(17, entity.getEditStartTime());
        statement.bindLong(18, entity.getEditEndTime());
        statement.bindLong(19, entity.getEditMoney());
        final int _tmp_8 = entity.getValid() ? 1 : 0;
        statement.bindLong(20, _tmp_8);
        statement.bindLong(21, entity.getTrend());
        statement.bindLong(22, entity.getSyncState());
        statement.bindLong(23, entity.getSyncTime());
        statement.bindLong(24, entity.getUuid());
        statement.bindLong(25, entity.getVersion());
        statement.bindLong(26, entity.getId());
      }
    };
    this.__preparedStmtOfDeleteAll = new SharedSQLiteStatement(__db) {
      @Override
      @NonNull
      public String createQuery() {
        final String _query = "delete from AppLimit where userId = ?";
        return _query;
      }
    };
    this.__preparedStmtOfUpdateUserId = new SharedSQLiteStatement(__db) {
      @Override
      @NonNull
      public String createQuery() {
        final String _query = "UPDATE AppLimit SET userId = ? WHERE userId = -1";
        return _query;
      }
    };
  }

  @Override
  public Object insertAppLimit(final AppLimit appLimits,
      final Continuation<? super Unit> $completion) {
    return CoroutinesRoom.execute(__db, true, new Callable<Unit>() {
      @Override
      @NonNull
      public Unit call() throws Exception {
        __db.beginTransaction();
        try {
          __insertionAdapterOfAppLimit.insert(appLimits);
          __db.setTransactionSuccessful();
          return Unit.INSTANCE;
        } finally {
          __db.endTransaction();
        }
      }
    }, $completion);
  }

  @Override
  public Object deleteAppLimit(final AppLimit appLimit,
      final Continuation<? super Unit> $completion) {
    return CoroutinesRoom.execute(__db, true, new Callable<Unit>() {
      @Override
      @NonNull
      public Unit call() throws Exception {
        __db.beginTransaction();
        try {
          __deletionAdapterOfAppLimit.handle(appLimit);
          __db.setTransactionSuccessful();
          return Unit.INSTANCE;
        } finally {
          __db.endTransaction();
        }
      }
    }, $completion);
  }

  @Override
  public Object updateAppLimit(final AppLimit allLimit,
      final Continuation<? super Unit> $completion) {
    return CoroutinesRoom.execute(__db, true, new Callable<Unit>() {
      @Override
      @NonNull
      public Unit call() throws Exception {
        __db.beginTransaction();
        try {
          __updateAdapterOfAppLimit.handle(allLimit);
          __db.setTransactionSuccessful();
          return Unit.INSTANCE;
        } finally {
          __db.endTransaction();
        }
      }
    }, $completion);
  }

  @Override
  public Object deleteAll(final int userId, final Continuation<? super Unit> $completion) {
    return CoroutinesRoom.execute(__db, true, new Callable<Unit>() {
      @Override
      @NonNull
      public Unit call() throws Exception {
        final SupportSQLiteStatement _stmt = __preparedStmtOfDeleteAll.acquire();
        int _argIndex = 1;
        _stmt.bindLong(_argIndex, userId);
        try {
          __db.beginTransaction();
          try {
            _stmt.executeUpdateDelete();
            __db.setTransactionSuccessful();
            return Unit.INSTANCE;
          } finally {
            __db.endTransaction();
          }
        } finally {
          __preparedStmtOfDeleteAll.release(_stmt);
        }
      }
    }, $completion);
  }

  @Override
  public void updateUserId(final int newUserId) {
    __db.assertNotSuspendingTransaction();
    final SupportSQLiteStatement _stmt = __preparedStmtOfUpdateUserId.acquire();
    int _argIndex = 1;
    _stmt.bindLong(_argIndex, newUserId);
    try {
      __db.beginTransaction();
      try {
        _stmt.executeUpdateDelete();
        __db.setTransactionSuccessful();
      } finally {
        __db.endTransaction();
      }
    } finally {
      __preparedStmtOfUpdateUserId.release(_stmt);
    }
  }

  @Override
  public Object getLastAppLimit(final int userId,
      final Continuation<? super AppLimit> $completion) {
    final String _sql = "select * from AppLimit where userId = ? order by trend desc limit 1";
    final RoomSQLiteQuery _statement = RoomSQLiteQuery.acquire(_sql, 1);
    int _argIndex = 1;
    _statement.bindLong(_argIndex, userId);
    final CancellationSignal _cancellationSignal = DBUtil.createCancellationSignal();
    return CoroutinesRoom.execute(__db, false, _cancellationSignal, new Callable<AppLimit>() {
      @Override
      @Nullable
      public AppLimit call() throws Exception {
        final Cursor _cursor = DBUtil.query(__db, _statement, false, null);
        try {
          final int _cursorIndexOfId = CursorUtil.getColumnIndexOrThrow(_cursor, "id");
          final int _cursorIndexOfUserId = CursorUtil.getColumnIndexOrThrow(_cursor, "userId");
          final int _cursorIndexOfAppLimitIndexId = CursorUtil.getColumnIndexOrThrow(_cursor, "appLimitIndexId");
          final int _cursorIndexOfAppPkg = CursorUtil.getColumnIndexOrThrow(_cursor, "appPkg");
          final int _cursorIndexOfIfAllDay = CursorUtil.getColumnIndexOrThrow(_cursor, "ifAllDay");
          final int _cursorIndexOfStartTime = CursorUtil.getColumnIndexOrThrow(_cursor, "startTime");
          final int _cursorIndexOfEndTime = CursorUtil.getColumnIndexOrThrow(_cursor, "endTime");
          final int _cursorIndexOfLimitLength = CursorUtil.getColumnIndexOrThrow(_cursor, "limitLength");
          final int _cursorIndexOfTitle = CursorUtil.getColumnIndexOrThrow(_cursor, "title");
          final int _cursorIndexOfSunday = CursorUtil.getColumnIndexOrThrow(_cursor, "sunday");
          final int _cursorIndexOfMonday = CursorUtil.getColumnIndexOrThrow(_cursor, "monday");
          final int _cursorIndexOfTuesday = CursorUtil.getColumnIndexOrThrow(_cursor, "tuesday");
          final int _cursorIndexOfWednesday = CursorUtil.getColumnIndexOrThrow(_cursor, "wednesday");
          final int _cursorIndexOfThursday = CursorUtil.getColumnIndexOrThrow(_cursor, "thursday");
          final int _cursorIndexOfFriday = CursorUtil.getColumnIndexOrThrow(_cursor, "friday");
          final int _cursorIndexOfSaturday = CursorUtil.getColumnIndexOrThrow(_cursor, "saturday");
          final int _cursorIndexOfEditStartTime = CursorUtil.getColumnIndexOrThrow(_cursor, "editStartTime");
          final int _cursorIndexOfEditEndTime = CursorUtil.getColumnIndexOrThrow(_cursor, "editEndTime");
          final int _cursorIndexOfEditMoney = CursorUtil.getColumnIndexOrThrow(_cursor, "editMoney");
          final int _cursorIndexOfValid = CursorUtil.getColumnIndexOrThrow(_cursor, "valid");
          final int _cursorIndexOfTrend = CursorUtil.getColumnIndexOrThrow(_cursor, "trend");
          final int _cursorIndexOfSyncState = CursorUtil.getColumnIndexOrThrow(_cursor, "syncState");
          final int _cursorIndexOfSyncTime = CursorUtil.getColumnIndexOrThrow(_cursor, "syncTime");
          final int _cursorIndexOfUuid = CursorUtil.getColumnIndexOrThrow(_cursor, "uuid");
          final int _cursorIndexOfVersion = CursorUtil.getColumnIndexOrThrow(_cursor, "version");
          final AppLimit _result;
          if (_cursor.moveToFirst()) {
            final long _tmpId;
            _tmpId = _cursor.getLong(_cursorIndexOfId);
            final int _tmpUserId;
            _tmpUserId = _cursor.getInt(_cursorIndexOfUserId);
            final String _tmpAppLimitIndexId;
            if (_cursor.isNull(_cursorIndexOfAppLimitIndexId)) {
              _tmpAppLimitIndexId = null;
            } else {
              _tmpAppLimitIndexId = _cursor.getString(_cursorIndexOfAppLimitIndexId);
            }
            final String _tmpAppPkg;
            if (_cursor.isNull(_cursorIndexOfAppPkg)) {
              _tmpAppPkg = null;
            } else {
              _tmpAppPkg = _cursor.getString(_cursorIndexOfAppPkg);
            }
            final boolean _tmpIfAllDay;
            final int _tmp;
            _tmp = _cursor.getInt(_cursorIndexOfIfAllDay);
            _tmpIfAllDay = _tmp != 0;
            final long _tmpStartTime;
            _tmpStartTime = _cursor.getLong(_cursorIndexOfStartTime);
            final long _tmpEndTime;
            _tmpEndTime = _cursor.getLong(_cursorIndexOfEndTime);
            final long _tmpLimitLength;
            _tmpLimitLength = _cursor.getLong(_cursorIndexOfLimitLength);
            final String _tmpTitle;
            if (_cursor.isNull(_cursorIndexOfTitle)) {
              _tmpTitle = null;
            } else {
              _tmpTitle = _cursor.getString(_cursorIndexOfTitle);
            }
            final boolean _tmpSunday;
            final int _tmp_1;
            _tmp_1 = _cursor.getInt(_cursorIndexOfSunday);
            _tmpSunday = _tmp_1 != 0;
            final boolean _tmpMonday;
            final int _tmp_2;
            _tmp_2 = _cursor.getInt(_cursorIndexOfMonday);
            _tmpMonday = _tmp_2 != 0;
            final boolean _tmpTuesday;
            final int _tmp_3;
            _tmp_3 = _cursor.getInt(_cursorIndexOfTuesday);
            _tmpTuesday = _tmp_3 != 0;
            final boolean _tmpWednesday;
            final int _tmp_4;
            _tmp_4 = _cursor.getInt(_cursorIndexOfWednesday);
            _tmpWednesday = _tmp_4 != 0;
            final boolean _tmpThursday;
            final int _tmp_5;
            _tmp_5 = _cursor.getInt(_cursorIndexOfThursday);
            _tmpThursday = _tmp_5 != 0;
            final boolean _tmpFriday;
            final int _tmp_6;
            _tmp_6 = _cursor.getInt(_cursorIndexOfFriday);
            _tmpFriday = _tmp_6 != 0;
            final boolean _tmpSaturday;
            final int _tmp_7;
            _tmp_7 = _cursor.getInt(_cursorIndexOfSaturday);
            _tmpSaturday = _tmp_7 != 0;
            final long _tmpEditStartTime;
            _tmpEditStartTime = _cursor.getLong(_cursorIndexOfEditStartTime);
            final long _tmpEditEndTime;
            _tmpEditEndTime = _cursor.getLong(_cursorIndexOfEditEndTime);
            final long _tmpEditMoney;
            _tmpEditMoney = _cursor.getLong(_cursorIndexOfEditMoney);
            final boolean _tmpValid;
            final int _tmp_8;
            _tmp_8 = _cursor.getInt(_cursorIndexOfValid);
            _tmpValid = _tmp_8 != 0;
            final int _tmpTrend;
            _tmpTrend = _cursor.getInt(_cursorIndexOfTrend);
            final int _tmpSyncState;
            _tmpSyncState = _cursor.getInt(_cursorIndexOfSyncState);
            final long _tmpSyncTime;
            _tmpSyncTime = _cursor.getLong(_cursorIndexOfSyncTime);
            final long _tmpUuid;
            _tmpUuid = _cursor.getLong(_cursorIndexOfUuid);
            final int _tmpVersion;
            _tmpVersion = _cursor.getInt(_cursorIndexOfVersion);
            _result = new AppLimit(_tmpId,_tmpUserId,_tmpAppLimitIndexId,_tmpAppPkg,_tmpIfAllDay,_tmpStartTime,_tmpEndTime,_tmpLimitLength,_tmpTitle,_tmpSunday,_tmpMonday,_tmpTuesday,_tmpWednesday,_tmpThursday,_tmpFriday,_tmpSaturday,_tmpEditStartTime,_tmpEditEndTime,_tmpEditMoney,_tmpValid,_tmpTrend,_tmpSyncState,_tmpSyncTime,_tmpUuid,_tmpVersion);
          } else {
            _result = null;
          }
          return _result;
        } finally {
          _cursor.close();
          _statement.release();
        }
      }
    }, $completion);
  }

  @Override
  public Object getAppLimitWithState(final int userId, final int state,
      final Continuation<? super List<AppLimit>> $completion) {
    final String _sql = "select * From AppLimit Where userId = ? and syncState = ? order by trend";
    final RoomSQLiteQuery _statement = RoomSQLiteQuery.acquire(_sql, 2);
    int _argIndex = 1;
    _statement.bindLong(_argIndex, userId);
    _argIndex = 2;
    _statement.bindLong(_argIndex, state);
    final CancellationSignal _cancellationSignal = DBUtil.createCancellationSignal();
    return CoroutinesRoom.execute(__db, false, _cancellationSignal, new Callable<List<AppLimit>>() {
      @Override
      @NonNull
      public List<AppLimit> call() throws Exception {
        final Cursor _cursor = DBUtil.query(__db, _statement, false, null);
        try {
          final int _cursorIndexOfId = CursorUtil.getColumnIndexOrThrow(_cursor, "id");
          final int _cursorIndexOfUserId = CursorUtil.getColumnIndexOrThrow(_cursor, "userId");
          final int _cursorIndexOfAppLimitIndexId = CursorUtil.getColumnIndexOrThrow(_cursor, "appLimitIndexId");
          final int _cursorIndexOfAppPkg = CursorUtil.getColumnIndexOrThrow(_cursor, "appPkg");
          final int _cursorIndexOfIfAllDay = CursorUtil.getColumnIndexOrThrow(_cursor, "ifAllDay");
          final int _cursorIndexOfStartTime = CursorUtil.getColumnIndexOrThrow(_cursor, "startTime");
          final int _cursorIndexOfEndTime = CursorUtil.getColumnIndexOrThrow(_cursor, "endTime");
          final int _cursorIndexOfLimitLength = CursorUtil.getColumnIndexOrThrow(_cursor, "limitLength");
          final int _cursorIndexOfTitle = CursorUtil.getColumnIndexOrThrow(_cursor, "title");
          final int _cursorIndexOfSunday = CursorUtil.getColumnIndexOrThrow(_cursor, "sunday");
          final int _cursorIndexOfMonday = CursorUtil.getColumnIndexOrThrow(_cursor, "monday");
          final int _cursorIndexOfTuesday = CursorUtil.getColumnIndexOrThrow(_cursor, "tuesday");
          final int _cursorIndexOfWednesday = CursorUtil.getColumnIndexOrThrow(_cursor, "wednesday");
          final int _cursorIndexOfThursday = CursorUtil.getColumnIndexOrThrow(_cursor, "thursday");
          final int _cursorIndexOfFriday = CursorUtil.getColumnIndexOrThrow(_cursor, "friday");
          final int _cursorIndexOfSaturday = CursorUtil.getColumnIndexOrThrow(_cursor, "saturday");
          final int _cursorIndexOfEditStartTime = CursorUtil.getColumnIndexOrThrow(_cursor, "editStartTime");
          final int _cursorIndexOfEditEndTime = CursorUtil.getColumnIndexOrThrow(_cursor, "editEndTime");
          final int _cursorIndexOfEditMoney = CursorUtil.getColumnIndexOrThrow(_cursor, "editMoney");
          final int _cursorIndexOfValid = CursorUtil.getColumnIndexOrThrow(_cursor, "valid");
          final int _cursorIndexOfTrend = CursorUtil.getColumnIndexOrThrow(_cursor, "trend");
          final int _cursorIndexOfSyncState = CursorUtil.getColumnIndexOrThrow(_cursor, "syncState");
          final int _cursorIndexOfSyncTime = CursorUtil.getColumnIndexOrThrow(_cursor, "syncTime");
          final int _cursorIndexOfUuid = CursorUtil.getColumnIndexOrThrow(_cursor, "uuid");
          final int _cursorIndexOfVersion = CursorUtil.getColumnIndexOrThrow(_cursor, "version");
          final List<AppLimit> _result = new ArrayList<AppLimit>(_cursor.getCount());
          while (_cursor.moveToNext()) {
            final AppLimit _item;
            final long _tmpId;
            _tmpId = _cursor.getLong(_cursorIndexOfId);
            final int _tmpUserId;
            _tmpUserId = _cursor.getInt(_cursorIndexOfUserId);
            final String _tmpAppLimitIndexId;
            if (_cursor.isNull(_cursorIndexOfAppLimitIndexId)) {
              _tmpAppLimitIndexId = null;
            } else {
              _tmpAppLimitIndexId = _cursor.getString(_cursorIndexOfAppLimitIndexId);
            }
            final String _tmpAppPkg;
            if (_cursor.isNull(_cursorIndexOfAppPkg)) {
              _tmpAppPkg = null;
            } else {
              _tmpAppPkg = _cursor.getString(_cursorIndexOfAppPkg);
            }
            final boolean _tmpIfAllDay;
            final int _tmp;
            _tmp = _cursor.getInt(_cursorIndexOfIfAllDay);
            _tmpIfAllDay = _tmp != 0;
            final long _tmpStartTime;
            _tmpStartTime = _cursor.getLong(_cursorIndexOfStartTime);
            final long _tmpEndTime;
            _tmpEndTime = _cursor.getLong(_cursorIndexOfEndTime);
            final long _tmpLimitLength;
            _tmpLimitLength = _cursor.getLong(_cursorIndexOfLimitLength);
            final String _tmpTitle;
            if (_cursor.isNull(_cursorIndexOfTitle)) {
              _tmpTitle = null;
            } else {
              _tmpTitle = _cursor.getString(_cursorIndexOfTitle);
            }
            final boolean _tmpSunday;
            final int _tmp_1;
            _tmp_1 = _cursor.getInt(_cursorIndexOfSunday);
            _tmpSunday = _tmp_1 != 0;
            final boolean _tmpMonday;
            final int _tmp_2;
            _tmp_2 = _cursor.getInt(_cursorIndexOfMonday);
            _tmpMonday = _tmp_2 != 0;
            final boolean _tmpTuesday;
            final int _tmp_3;
            _tmp_3 = _cursor.getInt(_cursorIndexOfTuesday);
            _tmpTuesday = _tmp_3 != 0;
            final boolean _tmpWednesday;
            final int _tmp_4;
            _tmp_4 = _cursor.getInt(_cursorIndexOfWednesday);
            _tmpWednesday = _tmp_4 != 0;
            final boolean _tmpThursday;
            final int _tmp_5;
            _tmp_5 = _cursor.getInt(_cursorIndexOfThursday);
            _tmpThursday = _tmp_5 != 0;
            final boolean _tmpFriday;
            final int _tmp_6;
            _tmp_6 = _cursor.getInt(_cursorIndexOfFriday);
            _tmpFriday = _tmp_6 != 0;
            final boolean _tmpSaturday;
            final int _tmp_7;
            _tmp_7 = _cursor.getInt(_cursorIndexOfSaturday);
            _tmpSaturday = _tmp_7 != 0;
            final long _tmpEditStartTime;
            _tmpEditStartTime = _cursor.getLong(_cursorIndexOfEditStartTime);
            final long _tmpEditEndTime;
            _tmpEditEndTime = _cursor.getLong(_cursorIndexOfEditEndTime);
            final long _tmpEditMoney;
            _tmpEditMoney = _cursor.getLong(_cursorIndexOfEditMoney);
            final boolean _tmpValid;
            final int _tmp_8;
            _tmp_8 = _cursor.getInt(_cursorIndexOfValid);
            _tmpValid = _tmp_8 != 0;
            final int _tmpTrend;
            _tmpTrend = _cursor.getInt(_cursorIndexOfTrend);
            final int _tmpSyncState;
            _tmpSyncState = _cursor.getInt(_cursorIndexOfSyncState);
            final long _tmpSyncTime;
            _tmpSyncTime = _cursor.getLong(_cursorIndexOfSyncTime);
            final long _tmpUuid;
            _tmpUuid = _cursor.getLong(_cursorIndexOfUuid);
            final int _tmpVersion;
            _tmpVersion = _cursor.getInt(_cursorIndexOfVersion);
            _item = new AppLimit(_tmpId,_tmpUserId,_tmpAppLimitIndexId,_tmpAppPkg,_tmpIfAllDay,_tmpStartTime,_tmpEndTime,_tmpLimitLength,_tmpTitle,_tmpSunday,_tmpMonday,_tmpTuesday,_tmpWednesday,_tmpThursday,_tmpFriday,_tmpSaturday,_tmpEditStartTime,_tmpEditEndTime,_tmpEditMoney,_tmpValid,_tmpTrend,_tmpSyncState,_tmpSyncTime,_tmpUuid,_tmpVersion);
            _result.add(_item);
          }
          return _result;
        } finally {
          _cursor.close();
          _statement.release();
        }
      }
    }, $completion);
  }

  @Override
  public Object getAllAppLimitList(final int userId,
      final Continuation<? super List<AppLimit>> $completion) {
    final String _sql = "select * From AppLimit where userId = ? order by trend";
    final RoomSQLiteQuery _statement = RoomSQLiteQuery.acquire(_sql, 1);
    int _argIndex = 1;
    _statement.bindLong(_argIndex, userId);
    final CancellationSignal _cancellationSignal = DBUtil.createCancellationSignal();
    return CoroutinesRoom.execute(__db, false, _cancellationSignal, new Callable<List<AppLimit>>() {
      @Override
      @NonNull
      public List<AppLimit> call() throws Exception {
        final Cursor _cursor = DBUtil.query(__db, _statement, false, null);
        try {
          final int _cursorIndexOfId = CursorUtil.getColumnIndexOrThrow(_cursor, "id");
          final int _cursorIndexOfUserId = CursorUtil.getColumnIndexOrThrow(_cursor, "userId");
          final int _cursorIndexOfAppLimitIndexId = CursorUtil.getColumnIndexOrThrow(_cursor, "appLimitIndexId");
          final int _cursorIndexOfAppPkg = CursorUtil.getColumnIndexOrThrow(_cursor, "appPkg");
          final int _cursorIndexOfIfAllDay = CursorUtil.getColumnIndexOrThrow(_cursor, "ifAllDay");
          final int _cursorIndexOfStartTime = CursorUtil.getColumnIndexOrThrow(_cursor, "startTime");
          final int _cursorIndexOfEndTime = CursorUtil.getColumnIndexOrThrow(_cursor, "endTime");
          final int _cursorIndexOfLimitLength = CursorUtil.getColumnIndexOrThrow(_cursor, "limitLength");
          final int _cursorIndexOfTitle = CursorUtil.getColumnIndexOrThrow(_cursor, "title");
          final int _cursorIndexOfSunday = CursorUtil.getColumnIndexOrThrow(_cursor, "sunday");
          final int _cursorIndexOfMonday = CursorUtil.getColumnIndexOrThrow(_cursor, "monday");
          final int _cursorIndexOfTuesday = CursorUtil.getColumnIndexOrThrow(_cursor, "tuesday");
          final int _cursorIndexOfWednesday = CursorUtil.getColumnIndexOrThrow(_cursor, "wednesday");
          final int _cursorIndexOfThursday = CursorUtil.getColumnIndexOrThrow(_cursor, "thursday");
          final int _cursorIndexOfFriday = CursorUtil.getColumnIndexOrThrow(_cursor, "friday");
          final int _cursorIndexOfSaturday = CursorUtil.getColumnIndexOrThrow(_cursor, "saturday");
          final int _cursorIndexOfEditStartTime = CursorUtil.getColumnIndexOrThrow(_cursor, "editStartTime");
          final int _cursorIndexOfEditEndTime = CursorUtil.getColumnIndexOrThrow(_cursor, "editEndTime");
          final int _cursorIndexOfEditMoney = CursorUtil.getColumnIndexOrThrow(_cursor, "editMoney");
          final int _cursorIndexOfValid = CursorUtil.getColumnIndexOrThrow(_cursor, "valid");
          final int _cursorIndexOfTrend = CursorUtil.getColumnIndexOrThrow(_cursor, "trend");
          final int _cursorIndexOfSyncState = CursorUtil.getColumnIndexOrThrow(_cursor, "syncState");
          final int _cursorIndexOfSyncTime = CursorUtil.getColumnIndexOrThrow(_cursor, "syncTime");
          final int _cursorIndexOfUuid = CursorUtil.getColumnIndexOrThrow(_cursor, "uuid");
          final int _cursorIndexOfVersion = CursorUtil.getColumnIndexOrThrow(_cursor, "version");
          final List<AppLimit> _result = new ArrayList<AppLimit>(_cursor.getCount());
          while (_cursor.moveToNext()) {
            final AppLimit _item;
            final long _tmpId;
            _tmpId = _cursor.getLong(_cursorIndexOfId);
            final int _tmpUserId;
            _tmpUserId = _cursor.getInt(_cursorIndexOfUserId);
            final String _tmpAppLimitIndexId;
            if (_cursor.isNull(_cursorIndexOfAppLimitIndexId)) {
              _tmpAppLimitIndexId = null;
            } else {
              _tmpAppLimitIndexId = _cursor.getString(_cursorIndexOfAppLimitIndexId);
            }
            final String _tmpAppPkg;
            if (_cursor.isNull(_cursorIndexOfAppPkg)) {
              _tmpAppPkg = null;
            } else {
              _tmpAppPkg = _cursor.getString(_cursorIndexOfAppPkg);
            }
            final boolean _tmpIfAllDay;
            final int _tmp;
            _tmp = _cursor.getInt(_cursorIndexOfIfAllDay);
            _tmpIfAllDay = _tmp != 0;
            final long _tmpStartTime;
            _tmpStartTime = _cursor.getLong(_cursorIndexOfStartTime);
            final long _tmpEndTime;
            _tmpEndTime = _cursor.getLong(_cursorIndexOfEndTime);
            final long _tmpLimitLength;
            _tmpLimitLength = _cursor.getLong(_cursorIndexOfLimitLength);
            final String _tmpTitle;
            if (_cursor.isNull(_cursorIndexOfTitle)) {
              _tmpTitle = null;
            } else {
              _tmpTitle = _cursor.getString(_cursorIndexOfTitle);
            }
            final boolean _tmpSunday;
            final int _tmp_1;
            _tmp_1 = _cursor.getInt(_cursorIndexOfSunday);
            _tmpSunday = _tmp_1 != 0;
            final boolean _tmpMonday;
            final int _tmp_2;
            _tmp_2 = _cursor.getInt(_cursorIndexOfMonday);
            _tmpMonday = _tmp_2 != 0;
            final boolean _tmpTuesday;
            final int _tmp_3;
            _tmp_3 = _cursor.getInt(_cursorIndexOfTuesday);
            _tmpTuesday = _tmp_3 != 0;
            final boolean _tmpWednesday;
            final int _tmp_4;
            _tmp_4 = _cursor.getInt(_cursorIndexOfWednesday);
            _tmpWednesday = _tmp_4 != 0;
            final boolean _tmpThursday;
            final int _tmp_5;
            _tmp_5 = _cursor.getInt(_cursorIndexOfThursday);
            _tmpThursday = _tmp_5 != 0;
            final boolean _tmpFriday;
            final int _tmp_6;
            _tmp_6 = _cursor.getInt(_cursorIndexOfFriday);
            _tmpFriday = _tmp_6 != 0;
            final boolean _tmpSaturday;
            final int _tmp_7;
            _tmp_7 = _cursor.getInt(_cursorIndexOfSaturday);
            _tmpSaturday = _tmp_7 != 0;
            final long _tmpEditStartTime;
            _tmpEditStartTime = _cursor.getLong(_cursorIndexOfEditStartTime);
            final long _tmpEditEndTime;
            _tmpEditEndTime = _cursor.getLong(_cursorIndexOfEditEndTime);
            final long _tmpEditMoney;
            _tmpEditMoney = _cursor.getLong(_cursorIndexOfEditMoney);
            final boolean _tmpValid;
            final int _tmp_8;
            _tmp_8 = _cursor.getInt(_cursorIndexOfValid);
            _tmpValid = _tmp_8 != 0;
            final int _tmpTrend;
            _tmpTrend = _cursor.getInt(_cursorIndexOfTrend);
            final int _tmpSyncState;
            _tmpSyncState = _cursor.getInt(_cursorIndexOfSyncState);
            final long _tmpSyncTime;
            _tmpSyncTime = _cursor.getLong(_cursorIndexOfSyncTime);
            final long _tmpUuid;
            _tmpUuid = _cursor.getLong(_cursorIndexOfUuid);
            final int _tmpVersion;
            _tmpVersion = _cursor.getInt(_cursorIndexOfVersion);
            _item = new AppLimit(_tmpId,_tmpUserId,_tmpAppLimitIndexId,_tmpAppPkg,_tmpIfAllDay,_tmpStartTime,_tmpEndTime,_tmpLimitLength,_tmpTitle,_tmpSunday,_tmpMonday,_tmpTuesday,_tmpWednesday,_tmpThursday,_tmpFriday,_tmpSaturday,_tmpEditStartTime,_tmpEditEndTime,_tmpEditMoney,_tmpValid,_tmpTrend,_tmpSyncState,_tmpSyncTime,_tmpUuid,_tmpVersion);
            _result.add(_item);
          }
          return _result;
        } finally {
          _cursor.close();
          _statement.release();
        }
      }
    }, $completion);
  }

  @Override
  public LiveData<List<AppLimit>> getAppLimits(final int userId) {
    final String _sql = "SELECT * FROM AppLimit where userId = ? ORDER BY trend";
    final RoomSQLiteQuery _statement = RoomSQLiteQuery.acquire(_sql, 1);
    int _argIndex = 1;
    _statement.bindLong(_argIndex, userId);
    return __db.getInvalidationTracker().createLiveData(new String[] {"AppLimit"}, false, new Callable<List<AppLimit>>() {
      @Override
      @Nullable
      public List<AppLimit> call() throws Exception {
        final Cursor _cursor = DBUtil.query(__db, _statement, false, null);
        try {
          final int _cursorIndexOfId = CursorUtil.getColumnIndexOrThrow(_cursor, "id");
          final int _cursorIndexOfUserId = CursorUtil.getColumnIndexOrThrow(_cursor, "userId");
          final int _cursorIndexOfAppLimitIndexId = CursorUtil.getColumnIndexOrThrow(_cursor, "appLimitIndexId");
          final int _cursorIndexOfAppPkg = CursorUtil.getColumnIndexOrThrow(_cursor, "appPkg");
          final int _cursorIndexOfIfAllDay = CursorUtil.getColumnIndexOrThrow(_cursor, "ifAllDay");
          final int _cursorIndexOfStartTime = CursorUtil.getColumnIndexOrThrow(_cursor, "startTime");
          final int _cursorIndexOfEndTime = CursorUtil.getColumnIndexOrThrow(_cursor, "endTime");
          final int _cursorIndexOfLimitLength = CursorUtil.getColumnIndexOrThrow(_cursor, "limitLength");
          final int _cursorIndexOfTitle = CursorUtil.getColumnIndexOrThrow(_cursor, "title");
          final int _cursorIndexOfSunday = CursorUtil.getColumnIndexOrThrow(_cursor, "sunday");
          final int _cursorIndexOfMonday = CursorUtil.getColumnIndexOrThrow(_cursor, "monday");
          final int _cursorIndexOfTuesday = CursorUtil.getColumnIndexOrThrow(_cursor, "tuesday");
          final int _cursorIndexOfWednesday = CursorUtil.getColumnIndexOrThrow(_cursor, "wednesday");
          final int _cursorIndexOfThursday = CursorUtil.getColumnIndexOrThrow(_cursor, "thursday");
          final int _cursorIndexOfFriday = CursorUtil.getColumnIndexOrThrow(_cursor, "friday");
          final int _cursorIndexOfSaturday = CursorUtil.getColumnIndexOrThrow(_cursor, "saturday");
          final int _cursorIndexOfEditStartTime = CursorUtil.getColumnIndexOrThrow(_cursor, "editStartTime");
          final int _cursorIndexOfEditEndTime = CursorUtil.getColumnIndexOrThrow(_cursor, "editEndTime");
          final int _cursorIndexOfEditMoney = CursorUtil.getColumnIndexOrThrow(_cursor, "editMoney");
          final int _cursorIndexOfValid = CursorUtil.getColumnIndexOrThrow(_cursor, "valid");
          final int _cursorIndexOfTrend = CursorUtil.getColumnIndexOrThrow(_cursor, "trend");
          final int _cursorIndexOfSyncState = CursorUtil.getColumnIndexOrThrow(_cursor, "syncState");
          final int _cursorIndexOfSyncTime = CursorUtil.getColumnIndexOrThrow(_cursor, "syncTime");
          final int _cursorIndexOfUuid = CursorUtil.getColumnIndexOrThrow(_cursor, "uuid");
          final int _cursorIndexOfVersion = CursorUtil.getColumnIndexOrThrow(_cursor, "version");
          final List<AppLimit> _result = new ArrayList<AppLimit>(_cursor.getCount());
          while (_cursor.moveToNext()) {
            final AppLimit _item;
            final long _tmpId;
            _tmpId = _cursor.getLong(_cursorIndexOfId);
            final int _tmpUserId;
            _tmpUserId = _cursor.getInt(_cursorIndexOfUserId);
            final String _tmpAppLimitIndexId;
            if (_cursor.isNull(_cursorIndexOfAppLimitIndexId)) {
              _tmpAppLimitIndexId = null;
            } else {
              _tmpAppLimitIndexId = _cursor.getString(_cursorIndexOfAppLimitIndexId);
            }
            final String _tmpAppPkg;
            if (_cursor.isNull(_cursorIndexOfAppPkg)) {
              _tmpAppPkg = null;
            } else {
              _tmpAppPkg = _cursor.getString(_cursorIndexOfAppPkg);
            }
            final boolean _tmpIfAllDay;
            final int _tmp;
            _tmp = _cursor.getInt(_cursorIndexOfIfAllDay);
            _tmpIfAllDay = _tmp != 0;
            final long _tmpStartTime;
            _tmpStartTime = _cursor.getLong(_cursorIndexOfStartTime);
            final long _tmpEndTime;
            _tmpEndTime = _cursor.getLong(_cursorIndexOfEndTime);
            final long _tmpLimitLength;
            _tmpLimitLength = _cursor.getLong(_cursorIndexOfLimitLength);
            final String _tmpTitle;
            if (_cursor.isNull(_cursorIndexOfTitle)) {
              _tmpTitle = null;
            } else {
              _tmpTitle = _cursor.getString(_cursorIndexOfTitle);
            }
            final boolean _tmpSunday;
            final int _tmp_1;
            _tmp_1 = _cursor.getInt(_cursorIndexOfSunday);
            _tmpSunday = _tmp_1 != 0;
            final boolean _tmpMonday;
            final int _tmp_2;
            _tmp_2 = _cursor.getInt(_cursorIndexOfMonday);
            _tmpMonday = _tmp_2 != 0;
            final boolean _tmpTuesday;
            final int _tmp_3;
            _tmp_3 = _cursor.getInt(_cursorIndexOfTuesday);
            _tmpTuesday = _tmp_3 != 0;
            final boolean _tmpWednesday;
            final int _tmp_4;
            _tmp_4 = _cursor.getInt(_cursorIndexOfWednesday);
            _tmpWednesday = _tmp_4 != 0;
            final boolean _tmpThursday;
            final int _tmp_5;
            _tmp_5 = _cursor.getInt(_cursorIndexOfThursday);
            _tmpThursday = _tmp_5 != 0;
            final boolean _tmpFriday;
            final int _tmp_6;
            _tmp_6 = _cursor.getInt(_cursorIndexOfFriday);
            _tmpFriday = _tmp_6 != 0;
            final boolean _tmpSaturday;
            final int _tmp_7;
            _tmp_7 = _cursor.getInt(_cursorIndexOfSaturday);
            _tmpSaturday = _tmp_7 != 0;
            final long _tmpEditStartTime;
            _tmpEditStartTime = _cursor.getLong(_cursorIndexOfEditStartTime);
            final long _tmpEditEndTime;
            _tmpEditEndTime = _cursor.getLong(_cursorIndexOfEditEndTime);
            final long _tmpEditMoney;
            _tmpEditMoney = _cursor.getLong(_cursorIndexOfEditMoney);
            final boolean _tmpValid;
            final int _tmp_8;
            _tmp_8 = _cursor.getInt(_cursorIndexOfValid);
            _tmpValid = _tmp_8 != 0;
            final int _tmpTrend;
            _tmpTrend = _cursor.getInt(_cursorIndexOfTrend);
            final int _tmpSyncState;
            _tmpSyncState = _cursor.getInt(_cursorIndexOfSyncState);
            final long _tmpSyncTime;
            _tmpSyncTime = _cursor.getLong(_cursorIndexOfSyncTime);
            final long _tmpUuid;
            _tmpUuid = _cursor.getLong(_cursorIndexOfUuid);
            final int _tmpVersion;
            _tmpVersion = _cursor.getInt(_cursorIndexOfVersion);
            _item = new AppLimit(_tmpId,_tmpUserId,_tmpAppLimitIndexId,_tmpAppPkg,_tmpIfAllDay,_tmpStartTime,_tmpEndTime,_tmpLimitLength,_tmpTitle,_tmpSunday,_tmpMonday,_tmpTuesday,_tmpWednesday,_tmpThursday,_tmpFriday,_tmpSaturday,_tmpEditStartTime,_tmpEditEndTime,_tmpEditMoney,_tmpValid,_tmpTrend,_tmpSyncState,_tmpSyncTime,_tmpUuid,_tmpVersion);
            _result.add(_item);
          }
          return _result;
        } finally {
          _cursor.close();
        }
      }

      @Override
      protected void finalize() {
        _statement.release();
      }
    });
  }

  @NonNull
  public static List<Class<?>> getRequiredConverters() {
    return Collections.emptyList();
  }
}
