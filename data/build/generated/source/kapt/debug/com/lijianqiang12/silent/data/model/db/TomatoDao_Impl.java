package com.lijianqiang12.silent.data.model.db;

import android.database.Cursor;
import android.os.CancellationSignal;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.lifecycle.LiveData;
import androidx.room.CoroutinesRoom;
import androidx.room.EntityDeletionOrUpdateAdapter;
import androidx.room.EntityInsertionAdapter;
import androidx.room.RoomDatabase;
import androidx.room.RoomSQLiteQuery;
import androidx.room.SharedSQLiteStatement;
import androidx.room.util.CursorUtil;
import androidx.room.util.DBUtil;
import androidx.room.util.RelationUtil;
import androidx.room.util.StringUtil;
import androidx.sqlite.db.SupportSQLiteStatement;
import java.lang.Class;
import java.lang.Exception;
import java.lang.Long;
import java.lang.Object;
import java.lang.Override;
import java.lang.String;
import java.lang.StringBuilder;
import java.lang.SuppressWarnings;
import java.util.ArrayList;
import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Set;
import java.util.concurrent.Callable;
import javax.annotation.processing.Generated;
import kotlin.Unit;
import kotlin.coroutines.Continuation;

@Generated("androidx.room.RoomProcessor")
@SuppressWarnings({"unchecked", "deprecation"})
public final class TomatoDao_Impl implements TomatoDao {
  private final RoomDatabase __db;

  private final EntityInsertionAdapter<Tomato> __insertionAdapterOfTomato;

  private final EntityDeletionOrUpdateAdapter<Tomato> __deletionAdapterOfTomato;

  private final EntityDeletionOrUpdateAdapter<Tomato> __updateAdapterOfTomato;

  private final SharedSQLiteStatement __preparedStmtOfDeleteAll;

  private final SharedSQLiteStatement __preparedStmtOfUpdateUserId;

  public TomatoDao_Impl(@NonNull final RoomDatabase __db) {
    this.__db = __db;
    this.__insertionAdapterOfTomato = new EntityInsertionAdapter<Tomato>(__db) {
      @Override
      @NonNull
      protected String createQuery() {
        return "INSERT OR IGNORE INTO `Tomato` (`id`,`userId`,`tomatoIndexId`,`title`,`tomatoWorkLength`,`tomatoRestLength`,`tomatoCount`,`tomatoLongRestPerCount`,`tomatoLongRestLength`,`trend`,`syncState`,`syncTime`,`uuid`,`version`,`bgUrl`,`isRemoveNotification`,`isSilent`,`startVoiceNotify`,`endVoiceNotify`,`startShakeNotify`,`endShakeNotify`,`whiteFollowGlobal`,`bgUrlFollowGlobal`,`isRemoveNotificationFollowGlobal`,`isSilentFollowGlobal`,`startVoiceNotifyFollowGlobal`,`endVoiceNotifyFollowGlobal`,`startShakeNotifyFollowGlobal`,`endShakeNotifyFollowGlobal`) VALUES (nullif(?, 0),?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?)";
      }

      @Override
      protected void bind(@NonNull final SupportSQLiteStatement statement,
          @NonNull final Tomato entity) {
        statement.bindLong(1, entity.getId());
        statement.bindLong(2, entity.getUserId());
        if (entity.getTomatoIndexId() == null) {
          statement.bindNull(3);
        } else {
          statement.bindString(3, entity.getTomatoIndexId());
        }
        if (entity.getTitle() == null) {
          statement.bindNull(4);
        } else {
          statement.bindString(4, entity.getTitle());
        }
        statement.bindLong(5, entity.getTomatoWorkLength());
        statement.bindLong(6, entity.getTomatoRestLength());
        statement.bindLong(7, entity.getTomatoCount());
        statement.bindLong(8, entity.getTomatoLongRestPerCount());
        statement.bindLong(9, entity.getTomatoLongRestLength());
        statement.bindLong(10, entity.getTrend());
        statement.bindLong(11, entity.getSyncState());
        statement.bindLong(12, entity.getSyncTime());
        statement.bindLong(13, entity.getUuid());
        statement.bindLong(14, entity.getVersion());
        final LockConfig _tmpLockConfig = entity.getLockConfig();
        if (_tmpLockConfig.getBgUrl() == null) {
          statement.bindNull(15);
        } else {
          statement.bindString(15, _tmpLockConfig.getBgUrl());
        }
        final int _tmp = _tmpLockConfig.isRemoveNotification() ? 1 : 0;
        statement.bindLong(16, _tmp);
        final int _tmp_1 = _tmpLockConfig.isSilent() ? 1 : 0;
        statement.bindLong(17, _tmp_1);
        statement.bindLong(18, _tmpLockConfig.getStartVoiceNotify());
        statement.bindLong(19, _tmpLockConfig.getEndVoiceNotify());
        statement.bindLong(20, _tmpLockConfig.getStartShakeNotify());
        statement.bindLong(21, _tmpLockConfig.getEndShakeNotify());
        final int _tmp_2 = _tmpLockConfig.getWhiteFollowGlobal() ? 1 : 0;
        statement.bindLong(22, _tmp_2);
        final int _tmp_3 = _tmpLockConfig.getBgUrlFollowGlobal() ? 1 : 0;
        statement.bindLong(23, _tmp_3);
        final int _tmp_4 = _tmpLockConfig.isRemoveNotificationFollowGlobal() ? 1 : 0;
        statement.bindLong(24, _tmp_4);
        final int _tmp_5 = _tmpLockConfig.isSilentFollowGlobal() ? 1 : 0;
        statement.bindLong(25, _tmp_5);
        final int _tmp_6 = _tmpLockConfig.getStartVoiceNotifyFollowGlobal() ? 1 : 0;
        statement.bindLong(26, _tmp_6);
        final int _tmp_7 = _tmpLockConfig.getEndVoiceNotifyFollowGlobal() ? 1 : 0;
        statement.bindLong(27, _tmp_7);
        final int _tmp_8 = _tmpLockConfig.getStartShakeNotifyFollowGlobal() ? 1 : 0;
        statement.bindLong(28, _tmp_8);
        final int _tmp_9 = _tmpLockConfig.getEndShakeNotifyFollowGlobal() ? 1 : 0;
        statement.bindLong(29, _tmp_9);
      }
    };
    this.__deletionAdapterOfTomato = new EntityDeletionOrUpdateAdapter<Tomato>(__db) {
      @Override
      @NonNull
      protected String createQuery() {
        return "DELETE FROM `Tomato` WHERE `id` = ?";
      }

      @Override
      protected void bind(@NonNull final SupportSQLiteStatement statement,
          @NonNull final Tomato entity) {
        statement.bindLong(1, entity.getId());
      }
    };
    this.__updateAdapterOfTomato = new EntityDeletionOrUpdateAdapter<Tomato>(__db) {
      @Override
      @NonNull
      protected String createQuery() {
        return "UPDATE OR ABORT `Tomato` SET `id` = ?,`userId` = ?,`tomatoIndexId` = ?,`title` = ?,`tomatoWorkLength` = ?,`tomatoRestLength` = ?,`tomatoCount` = ?,`tomatoLongRestPerCount` = ?,`tomatoLongRestLength` = ?,`trend` = ?,`syncState` = ?,`syncTime` = ?,`uuid` = ?,`version` = ?,`bgUrl` = ?,`isRemoveNotification` = ?,`isSilent` = ?,`startVoiceNotify` = ?,`endVoiceNotify` = ?,`startShakeNotify` = ?,`endShakeNotify` = ?,`whiteFollowGlobal` = ?,`bgUrlFollowGlobal` = ?,`isRemoveNotificationFollowGlobal` = ?,`isSilentFollowGlobal` = ?,`startVoiceNotifyFollowGlobal` = ?,`endVoiceNotifyFollowGlobal` = ?,`startShakeNotifyFollowGlobal` = ?,`endShakeNotifyFollowGlobal` = ? WHERE `id` = ?";
      }

      @Override
      protected void bind(@NonNull final SupportSQLiteStatement statement,
          @NonNull final Tomato entity) {
        statement.bindLong(1, entity.getId());
        statement.bindLong(2, entity.getUserId());
        if (entity.getTomatoIndexId() == null) {
          statement.bindNull(3);
        } else {
          statement.bindString(3, entity.getTomatoIndexId());
        }
        if (entity.getTitle() == null) {
          statement.bindNull(4);
        } else {
          statement.bindString(4, entity.getTitle());
        }
        statement.bindLong(5, entity.getTomatoWorkLength());
        statement.bindLong(6, entity.getTomatoRestLength());
        statement.bindLong(7, entity.getTomatoCount());
        statement.bindLong(8, entity.getTomatoLongRestPerCount());
        statement.bindLong(9, entity.getTomatoLongRestLength());
        statement.bindLong(10, entity.getTrend());
        statement.bindLong(11, entity.getSyncState());
        statement.bindLong(12, entity.getSyncTime());
        statement.bindLong(13, entity.getUuid());
        statement.bindLong(14, entity.getVersion());
        final LockConfig _tmpLockConfig = entity.getLockConfig();
        if (_tmpLockConfig.getBgUrl() == null) {
          statement.bindNull(15);
        } else {
          statement.bindString(15, _tmpLockConfig.getBgUrl());
        }
        final int _tmp = _tmpLockConfig.isRemoveNotification() ? 1 : 0;
        statement.bindLong(16, _tmp);
        final int _tmp_1 = _tmpLockConfig.isSilent() ? 1 : 0;
        statement.bindLong(17, _tmp_1);
        statement.bindLong(18, _tmpLockConfig.getStartVoiceNotify());
        statement.bindLong(19, _tmpLockConfig.getEndVoiceNotify());
        statement.bindLong(20, _tmpLockConfig.getStartShakeNotify());
        statement.bindLong(21, _tmpLockConfig.getEndShakeNotify());
        final int _tmp_2 = _tmpLockConfig.getWhiteFollowGlobal() ? 1 : 0;
        statement.bindLong(22, _tmp_2);
        final int _tmp_3 = _tmpLockConfig.getBgUrlFollowGlobal() ? 1 : 0;
        statement.bindLong(23, _tmp_3);
        final int _tmp_4 = _tmpLockConfig.isRemoveNotificationFollowGlobal() ? 1 : 0;
        statement.bindLong(24, _tmp_4);
        final int _tmp_5 = _tmpLockConfig.isSilentFollowGlobal() ? 1 : 0;
        statement.bindLong(25, _tmp_5);
        final int _tmp_6 = _tmpLockConfig.getStartVoiceNotifyFollowGlobal() ? 1 : 0;
        statement.bindLong(26, _tmp_6);
        final int _tmp_7 = _tmpLockConfig.getEndVoiceNotifyFollowGlobal() ? 1 : 0;
        statement.bindLong(27, _tmp_7);
        final int _tmp_8 = _tmpLockConfig.getStartShakeNotifyFollowGlobal() ? 1 : 0;
        statement.bindLong(28, _tmp_8);
        final int _tmp_9 = _tmpLockConfig.getEndShakeNotifyFollowGlobal() ? 1 : 0;
        statement.bindLong(29, _tmp_9);
        statement.bindLong(30, entity.getId());
      }
    };
    this.__preparedStmtOfDeleteAll = new SharedSQLiteStatement(__db) {
      @Override
      @NonNull
      public String createQuery() {
        final String _query = "delete from Tomato where userId = ?";
        return _query;
      }
    };
    this.__preparedStmtOfUpdateUserId = new SharedSQLiteStatement(__db) {
      @Override
      @NonNull
      public String createQuery() {
        final String _query = "UPDATE Tomato SET userId = ? WHERE userId = -1";
        return _query;
      }
    };
  }

  @Override
  public Object insertTomato(final Tomato tomato, final Continuation<? super Long> $completion) {
    return CoroutinesRoom.execute(__db, true, new Callable<Long>() {
      @Override
      @NonNull
      public Long call() throws Exception {
        __db.beginTransaction();
        try {
          final Long _result = __insertionAdapterOfTomato.insertAndReturnId(tomato);
          __db.setTransactionSuccessful();
          return _result;
        } finally {
          __db.endTransaction();
        }
      }
    }, $completion);
  }

  @Override
  public Object deleteTomato(final Tomato tomato, final Continuation<? super Unit> $completion) {
    return CoroutinesRoom.execute(__db, true, new Callable<Unit>() {
      @Override
      @NonNull
      public Unit call() throws Exception {
        __db.beginTransaction();
        try {
          __deletionAdapterOfTomato.handle(tomato);
          __db.setTransactionSuccessful();
          return Unit.INSTANCE;
        } finally {
          __db.endTransaction();
        }
      }
    }, $completion);
  }

  @Override
  public Object updateTomato(final Tomato tomato, final Continuation<? super Unit> $completion) {
    return CoroutinesRoom.execute(__db, true, new Callable<Unit>() {
      @Override
      @NonNull
      public Unit call() throws Exception {
        __db.beginTransaction();
        try {
          __updateAdapterOfTomato.handle(tomato);
          __db.setTransactionSuccessful();
          return Unit.INSTANCE;
        } finally {
          __db.endTransaction();
        }
      }
    }, $completion);
  }

  @Override
  public Object deleteAll(final int userId, final Continuation<? super Unit> $completion) {
    return CoroutinesRoom.execute(__db, true, new Callable<Unit>() {
      @Override
      @NonNull
      public Unit call() throws Exception {
        final SupportSQLiteStatement _stmt = __preparedStmtOfDeleteAll.acquire();
        int _argIndex = 1;
        _stmt.bindLong(_argIndex, userId);
        try {
          __db.beginTransaction();
          try {
            _stmt.executeUpdateDelete();
            __db.setTransactionSuccessful();
            return Unit.INSTANCE;
          } finally {
            __db.endTransaction();
          }
        } finally {
          __preparedStmtOfDeleteAll.release(_stmt);
        }
      }
    }, $completion);
  }

  @Override
  public void updateUserId(final int newUserId) {
    __db.assertNotSuspendingTransaction();
    final SupportSQLiteStatement _stmt = __preparedStmtOfUpdateUserId.acquire();
    int _argIndex = 1;
    _stmt.bindLong(_argIndex, newUserId);
    try {
      __db.beginTransaction();
      try {
        _stmt.executeUpdateDelete();
        __db.setTransactionSuccessful();
      } finally {
        __db.endTransaction();
      }
    } finally {
      __preparedStmtOfUpdateUserId.release(_stmt);
    }
  }

  @Override
  public Object getTomatoWithState(final int userId, final int state,
      final Continuation<? super List<Tomato>> $completion) {
    final String _sql = "select * From Tomato Where userId = ? and syncState = ? order by trend";
    final RoomSQLiteQuery _statement = RoomSQLiteQuery.acquire(_sql, 2);
    int _argIndex = 1;
    _statement.bindLong(_argIndex, userId);
    _argIndex = 2;
    _statement.bindLong(_argIndex, state);
    final CancellationSignal _cancellationSignal = DBUtil.createCancellationSignal();
    return CoroutinesRoom.execute(__db, false, _cancellationSignal, new Callable<List<Tomato>>() {
      @Override
      @NonNull
      public List<Tomato> call() throws Exception {
        final Cursor _cursor = DBUtil.query(__db, _statement, false, null);
        try {
          final int _cursorIndexOfId = CursorUtil.getColumnIndexOrThrow(_cursor, "id");
          final int _cursorIndexOfUserId = CursorUtil.getColumnIndexOrThrow(_cursor, "userId");
          final int _cursorIndexOfTomatoIndexId = CursorUtil.getColumnIndexOrThrow(_cursor, "tomatoIndexId");
          final int _cursorIndexOfTitle = CursorUtil.getColumnIndexOrThrow(_cursor, "title");
          final int _cursorIndexOfTomatoWorkLength = CursorUtil.getColumnIndexOrThrow(_cursor, "tomatoWorkLength");
          final int _cursorIndexOfTomatoRestLength = CursorUtil.getColumnIndexOrThrow(_cursor, "tomatoRestLength");
          final int _cursorIndexOfTomatoCount = CursorUtil.getColumnIndexOrThrow(_cursor, "tomatoCount");
          final int _cursorIndexOfTomatoLongRestPerCount = CursorUtil.getColumnIndexOrThrow(_cursor, "tomatoLongRestPerCount");
          final int _cursorIndexOfTomatoLongRestLength = CursorUtil.getColumnIndexOrThrow(_cursor, "tomatoLongRestLength");
          final int _cursorIndexOfTrend = CursorUtil.getColumnIndexOrThrow(_cursor, "trend");
          final int _cursorIndexOfSyncState = CursorUtil.getColumnIndexOrThrow(_cursor, "syncState");
          final int _cursorIndexOfSyncTime = CursorUtil.getColumnIndexOrThrow(_cursor, "syncTime");
          final int _cursorIndexOfUuid = CursorUtil.getColumnIndexOrThrow(_cursor, "uuid");
          final int _cursorIndexOfVersion = CursorUtil.getColumnIndexOrThrow(_cursor, "version");
          final int _cursorIndexOfBgUrl = CursorUtil.getColumnIndexOrThrow(_cursor, "bgUrl");
          final int _cursorIndexOfIsRemoveNotification = CursorUtil.getColumnIndexOrThrow(_cursor, "isRemoveNotification");
          final int _cursorIndexOfIsSilent = CursorUtil.getColumnIndexOrThrow(_cursor, "isSilent");
          final int _cursorIndexOfStartVoiceNotify = CursorUtil.getColumnIndexOrThrow(_cursor, "startVoiceNotify");
          final int _cursorIndexOfEndVoiceNotify = CursorUtil.getColumnIndexOrThrow(_cursor, "endVoiceNotify");
          final int _cursorIndexOfStartShakeNotify = CursorUtil.getColumnIndexOrThrow(_cursor, "startShakeNotify");
          final int _cursorIndexOfEndShakeNotify = CursorUtil.getColumnIndexOrThrow(_cursor, "endShakeNotify");
          final int _cursorIndexOfWhiteFollowGlobal = CursorUtil.getColumnIndexOrThrow(_cursor, "whiteFollowGlobal");
          final int _cursorIndexOfBgUrlFollowGlobal = CursorUtil.getColumnIndexOrThrow(_cursor, "bgUrlFollowGlobal");
          final int _cursorIndexOfIsRemoveNotificationFollowGlobal = CursorUtil.getColumnIndexOrThrow(_cursor, "isRemoveNotificationFollowGlobal");
          final int _cursorIndexOfIsSilentFollowGlobal = CursorUtil.getColumnIndexOrThrow(_cursor, "isSilentFollowGlobal");
          final int _cursorIndexOfStartVoiceNotifyFollowGlobal = CursorUtil.getColumnIndexOrThrow(_cursor, "startVoiceNotifyFollowGlobal");
          final int _cursorIndexOfEndVoiceNotifyFollowGlobal = CursorUtil.getColumnIndexOrThrow(_cursor, "endVoiceNotifyFollowGlobal");
          final int _cursorIndexOfStartShakeNotifyFollowGlobal = CursorUtil.getColumnIndexOrThrow(_cursor, "startShakeNotifyFollowGlobal");
          final int _cursorIndexOfEndShakeNotifyFollowGlobal = CursorUtil.getColumnIndexOrThrow(_cursor, "endShakeNotifyFollowGlobal");
          final List<Tomato> _result = new ArrayList<Tomato>(_cursor.getCount());
          while (_cursor.moveToNext()) {
            final Tomato _item;
            final long _tmpId;
            _tmpId = _cursor.getLong(_cursorIndexOfId);
            final int _tmpUserId;
            _tmpUserId = _cursor.getInt(_cursorIndexOfUserId);
            final String _tmpTomatoIndexId;
            if (_cursor.isNull(_cursorIndexOfTomatoIndexId)) {
              _tmpTomatoIndexId = null;
            } else {
              _tmpTomatoIndexId = _cursor.getString(_cursorIndexOfTomatoIndexId);
            }
            final String _tmpTitle;
            if (_cursor.isNull(_cursorIndexOfTitle)) {
              _tmpTitle = null;
            } else {
              _tmpTitle = _cursor.getString(_cursorIndexOfTitle);
            }
            final int _tmpTomatoWorkLength;
            _tmpTomatoWorkLength = _cursor.getInt(_cursorIndexOfTomatoWorkLength);
            final int _tmpTomatoRestLength;
            _tmpTomatoRestLength = _cursor.getInt(_cursorIndexOfTomatoRestLength);
            final int _tmpTomatoCount;
            _tmpTomatoCount = _cursor.getInt(_cursorIndexOfTomatoCount);
            final int _tmpTomatoLongRestPerCount;
            _tmpTomatoLongRestPerCount = _cursor.getInt(_cursorIndexOfTomatoLongRestPerCount);
            final int _tmpTomatoLongRestLength;
            _tmpTomatoLongRestLength = _cursor.getInt(_cursorIndexOfTomatoLongRestLength);
            final int _tmpTrend;
            _tmpTrend = _cursor.getInt(_cursorIndexOfTrend);
            final int _tmpSyncState;
            _tmpSyncState = _cursor.getInt(_cursorIndexOfSyncState);
            final long _tmpSyncTime;
            _tmpSyncTime = _cursor.getLong(_cursorIndexOfSyncTime);
            final long _tmpUuid;
            _tmpUuid = _cursor.getLong(_cursorIndexOfUuid);
            final int _tmpVersion;
            _tmpVersion = _cursor.getInt(_cursorIndexOfVersion);
            final LockConfig _tmpLockConfig;
            final String _tmpBgUrl;
            if (_cursor.isNull(_cursorIndexOfBgUrl)) {
              _tmpBgUrl = null;
            } else {
              _tmpBgUrl = _cursor.getString(_cursorIndexOfBgUrl);
            }
            final boolean _tmpIsRemoveNotification;
            final int _tmp;
            _tmp = _cursor.getInt(_cursorIndexOfIsRemoveNotification);
            _tmpIsRemoveNotification = _tmp != 0;
            final boolean _tmpIsSilent;
            final int _tmp_1;
            _tmp_1 = _cursor.getInt(_cursorIndexOfIsSilent);
            _tmpIsSilent = _tmp_1 != 0;
            final int _tmpStartVoiceNotify;
            _tmpStartVoiceNotify = _cursor.getInt(_cursorIndexOfStartVoiceNotify);
            final int _tmpEndVoiceNotify;
            _tmpEndVoiceNotify = _cursor.getInt(_cursorIndexOfEndVoiceNotify);
            final long _tmpStartShakeNotify;
            _tmpStartShakeNotify = _cursor.getLong(_cursorIndexOfStartShakeNotify);
            final long _tmpEndShakeNotify;
            _tmpEndShakeNotify = _cursor.getLong(_cursorIndexOfEndShakeNotify);
            final boolean _tmpWhiteFollowGlobal;
            final int _tmp_2;
            _tmp_2 = _cursor.getInt(_cursorIndexOfWhiteFollowGlobal);
            _tmpWhiteFollowGlobal = _tmp_2 != 0;
            final boolean _tmpBgUrlFollowGlobal;
            final int _tmp_3;
            _tmp_3 = _cursor.getInt(_cursorIndexOfBgUrlFollowGlobal);
            _tmpBgUrlFollowGlobal = _tmp_3 != 0;
            final boolean _tmpIsRemoveNotificationFollowGlobal;
            final int _tmp_4;
            _tmp_4 = _cursor.getInt(_cursorIndexOfIsRemoveNotificationFollowGlobal);
            _tmpIsRemoveNotificationFollowGlobal = _tmp_4 != 0;
            final boolean _tmpIsSilentFollowGlobal;
            final int _tmp_5;
            _tmp_5 = _cursor.getInt(_cursorIndexOfIsSilentFollowGlobal);
            _tmpIsSilentFollowGlobal = _tmp_5 != 0;
            final boolean _tmpStartVoiceNotifyFollowGlobal;
            final int _tmp_6;
            _tmp_6 = _cursor.getInt(_cursorIndexOfStartVoiceNotifyFollowGlobal);
            _tmpStartVoiceNotifyFollowGlobal = _tmp_6 != 0;
            final boolean _tmpEndVoiceNotifyFollowGlobal;
            final int _tmp_7;
            _tmp_7 = _cursor.getInt(_cursorIndexOfEndVoiceNotifyFollowGlobal);
            _tmpEndVoiceNotifyFollowGlobal = _tmp_7 != 0;
            final boolean _tmpStartShakeNotifyFollowGlobal;
            final int _tmp_8;
            _tmp_8 = _cursor.getInt(_cursorIndexOfStartShakeNotifyFollowGlobal);
            _tmpStartShakeNotifyFollowGlobal = _tmp_8 != 0;
            final boolean _tmpEndShakeNotifyFollowGlobal;
            final int _tmp_9;
            _tmp_9 = _cursor.getInt(_cursorIndexOfEndShakeNotifyFollowGlobal);
            _tmpEndShakeNotifyFollowGlobal = _tmp_9 != 0;
            _tmpLockConfig = new LockConfig(_tmpBgUrl,_tmpIsRemoveNotification,_tmpIsSilent,_tmpStartVoiceNotify,_tmpEndVoiceNotify,_tmpStartShakeNotify,_tmpEndShakeNotify,_tmpWhiteFollowGlobal,_tmpBgUrlFollowGlobal,_tmpIsRemoveNotificationFollowGlobal,_tmpIsSilentFollowGlobal,_tmpStartVoiceNotifyFollowGlobal,_tmpEndVoiceNotifyFollowGlobal,_tmpStartShakeNotifyFollowGlobal,_tmpEndShakeNotifyFollowGlobal);
            _item = new Tomato(_tmpId,_tmpUserId,_tmpTomatoIndexId,_tmpTitle,_tmpTomatoWorkLength,_tmpTomatoRestLength,_tmpTomatoCount,_tmpTomatoLongRestPerCount,_tmpTomatoLongRestLength,_tmpTrend,_tmpSyncState,_tmpSyncTime,_tmpUuid,_tmpVersion,_tmpLockConfig);
            _result.add(_item);
          }
          return _result;
        } finally {
          _cursor.close();
          _statement.release();
        }
      }
    }, $completion);
  }

  @Override
  public Object getAllTomatoList(final int userId,
      final Continuation<? super List<Tomato>> $completion) {
    final String _sql = "select * From Tomato Where userId = ? order by trend";
    final RoomSQLiteQuery _statement = RoomSQLiteQuery.acquire(_sql, 1);
    int _argIndex = 1;
    _statement.bindLong(_argIndex, userId);
    final CancellationSignal _cancellationSignal = DBUtil.createCancellationSignal();
    return CoroutinesRoom.execute(__db, false, _cancellationSignal, new Callable<List<Tomato>>() {
      @Override
      @NonNull
      public List<Tomato> call() throws Exception {
        final Cursor _cursor = DBUtil.query(__db, _statement, false, null);
        try {
          final int _cursorIndexOfId = CursorUtil.getColumnIndexOrThrow(_cursor, "id");
          final int _cursorIndexOfUserId = CursorUtil.getColumnIndexOrThrow(_cursor, "userId");
          final int _cursorIndexOfTomatoIndexId = CursorUtil.getColumnIndexOrThrow(_cursor, "tomatoIndexId");
          final int _cursorIndexOfTitle = CursorUtil.getColumnIndexOrThrow(_cursor, "title");
          final int _cursorIndexOfTomatoWorkLength = CursorUtil.getColumnIndexOrThrow(_cursor, "tomatoWorkLength");
          final int _cursorIndexOfTomatoRestLength = CursorUtil.getColumnIndexOrThrow(_cursor, "tomatoRestLength");
          final int _cursorIndexOfTomatoCount = CursorUtil.getColumnIndexOrThrow(_cursor, "tomatoCount");
          final int _cursorIndexOfTomatoLongRestPerCount = CursorUtil.getColumnIndexOrThrow(_cursor, "tomatoLongRestPerCount");
          final int _cursorIndexOfTomatoLongRestLength = CursorUtil.getColumnIndexOrThrow(_cursor, "tomatoLongRestLength");
          final int _cursorIndexOfTrend = CursorUtil.getColumnIndexOrThrow(_cursor, "trend");
          final int _cursorIndexOfSyncState = CursorUtil.getColumnIndexOrThrow(_cursor, "syncState");
          final int _cursorIndexOfSyncTime = CursorUtil.getColumnIndexOrThrow(_cursor, "syncTime");
          final int _cursorIndexOfUuid = CursorUtil.getColumnIndexOrThrow(_cursor, "uuid");
          final int _cursorIndexOfVersion = CursorUtil.getColumnIndexOrThrow(_cursor, "version");
          final int _cursorIndexOfBgUrl = CursorUtil.getColumnIndexOrThrow(_cursor, "bgUrl");
          final int _cursorIndexOfIsRemoveNotification = CursorUtil.getColumnIndexOrThrow(_cursor, "isRemoveNotification");
          final int _cursorIndexOfIsSilent = CursorUtil.getColumnIndexOrThrow(_cursor, "isSilent");
          final int _cursorIndexOfStartVoiceNotify = CursorUtil.getColumnIndexOrThrow(_cursor, "startVoiceNotify");
          final int _cursorIndexOfEndVoiceNotify = CursorUtil.getColumnIndexOrThrow(_cursor, "endVoiceNotify");
          final int _cursorIndexOfStartShakeNotify = CursorUtil.getColumnIndexOrThrow(_cursor, "startShakeNotify");
          final int _cursorIndexOfEndShakeNotify = CursorUtil.getColumnIndexOrThrow(_cursor, "endShakeNotify");
          final int _cursorIndexOfWhiteFollowGlobal = CursorUtil.getColumnIndexOrThrow(_cursor, "whiteFollowGlobal");
          final int _cursorIndexOfBgUrlFollowGlobal = CursorUtil.getColumnIndexOrThrow(_cursor, "bgUrlFollowGlobal");
          final int _cursorIndexOfIsRemoveNotificationFollowGlobal = CursorUtil.getColumnIndexOrThrow(_cursor, "isRemoveNotificationFollowGlobal");
          final int _cursorIndexOfIsSilentFollowGlobal = CursorUtil.getColumnIndexOrThrow(_cursor, "isSilentFollowGlobal");
          final int _cursorIndexOfStartVoiceNotifyFollowGlobal = CursorUtil.getColumnIndexOrThrow(_cursor, "startVoiceNotifyFollowGlobal");
          final int _cursorIndexOfEndVoiceNotifyFollowGlobal = CursorUtil.getColumnIndexOrThrow(_cursor, "endVoiceNotifyFollowGlobal");
          final int _cursorIndexOfStartShakeNotifyFollowGlobal = CursorUtil.getColumnIndexOrThrow(_cursor, "startShakeNotifyFollowGlobal");
          final int _cursorIndexOfEndShakeNotifyFollowGlobal = CursorUtil.getColumnIndexOrThrow(_cursor, "endShakeNotifyFollowGlobal");
          final List<Tomato> _result = new ArrayList<Tomato>(_cursor.getCount());
          while (_cursor.moveToNext()) {
            final Tomato _item;
            final long _tmpId;
            _tmpId = _cursor.getLong(_cursorIndexOfId);
            final int _tmpUserId;
            _tmpUserId = _cursor.getInt(_cursorIndexOfUserId);
            final String _tmpTomatoIndexId;
            if (_cursor.isNull(_cursorIndexOfTomatoIndexId)) {
              _tmpTomatoIndexId = null;
            } else {
              _tmpTomatoIndexId = _cursor.getString(_cursorIndexOfTomatoIndexId);
            }
            final String _tmpTitle;
            if (_cursor.isNull(_cursorIndexOfTitle)) {
              _tmpTitle = null;
            } else {
              _tmpTitle = _cursor.getString(_cursorIndexOfTitle);
            }
            final int _tmpTomatoWorkLength;
            _tmpTomatoWorkLength = _cursor.getInt(_cursorIndexOfTomatoWorkLength);
            final int _tmpTomatoRestLength;
            _tmpTomatoRestLength = _cursor.getInt(_cursorIndexOfTomatoRestLength);
            final int _tmpTomatoCount;
            _tmpTomatoCount = _cursor.getInt(_cursorIndexOfTomatoCount);
            final int _tmpTomatoLongRestPerCount;
            _tmpTomatoLongRestPerCount = _cursor.getInt(_cursorIndexOfTomatoLongRestPerCount);
            final int _tmpTomatoLongRestLength;
            _tmpTomatoLongRestLength = _cursor.getInt(_cursorIndexOfTomatoLongRestLength);
            final int _tmpTrend;
            _tmpTrend = _cursor.getInt(_cursorIndexOfTrend);
            final int _tmpSyncState;
            _tmpSyncState = _cursor.getInt(_cursorIndexOfSyncState);
            final long _tmpSyncTime;
            _tmpSyncTime = _cursor.getLong(_cursorIndexOfSyncTime);
            final long _tmpUuid;
            _tmpUuid = _cursor.getLong(_cursorIndexOfUuid);
            final int _tmpVersion;
            _tmpVersion = _cursor.getInt(_cursorIndexOfVersion);
            final LockConfig _tmpLockConfig;
            final String _tmpBgUrl;
            if (_cursor.isNull(_cursorIndexOfBgUrl)) {
              _tmpBgUrl = null;
            } else {
              _tmpBgUrl = _cursor.getString(_cursorIndexOfBgUrl);
            }
            final boolean _tmpIsRemoveNotification;
            final int _tmp;
            _tmp = _cursor.getInt(_cursorIndexOfIsRemoveNotification);
            _tmpIsRemoveNotification = _tmp != 0;
            final boolean _tmpIsSilent;
            final int _tmp_1;
            _tmp_1 = _cursor.getInt(_cursorIndexOfIsSilent);
            _tmpIsSilent = _tmp_1 != 0;
            final int _tmpStartVoiceNotify;
            _tmpStartVoiceNotify = _cursor.getInt(_cursorIndexOfStartVoiceNotify);
            final int _tmpEndVoiceNotify;
            _tmpEndVoiceNotify = _cursor.getInt(_cursorIndexOfEndVoiceNotify);
            final long _tmpStartShakeNotify;
            _tmpStartShakeNotify = _cursor.getLong(_cursorIndexOfStartShakeNotify);
            final long _tmpEndShakeNotify;
            _tmpEndShakeNotify = _cursor.getLong(_cursorIndexOfEndShakeNotify);
            final boolean _tmpWhiteFollowGlobal;
            final int _tmp_2;
            _tmp_2 = _cursor.getInt(_cursorIndexOfWhiteFollowGlobal);
            _tmpWhiteFollowGlobal = _tmp_2 != 0;
            final boolean _tmpBgUrlFollowGlobal;
            final int _tmp_3;
            _tmp_3 = _cursor.getInt(_cursorIndexOfBgUrlFollowGlobal);
            _tmpBgUrlFollowGlobal = _tmp_3 != 0;
            final boolean _tmpIsRemoveNotificationFollowGlobal;
            final int _tmp_4;
            _tmp_4 = _cursor.getInt(_cursorIndexOfIsRemoveNotificationFollowGlobal);
            _tmpIsRemoveNotificationFollowGlobal = _tmp_4 != 0;
            final boolean _tmpIsSilentFollowGlobal;
            final int _tmp_5;
            _tmp_5 = _cursor.getInt(_cursorIndexOfIsSilentFollowGlobal);
            _tmpIsSilentFollowGlobal = _tmp_5 != 0;
            final boolean _tmpStartVoiceNotifyFollowGlobal;
            final int _tmp_6;
            _tmp_6 = _cursor.getInt(_cursorIndexOfStartVoiceNotifyFollowGlobal);
            _tmpStartVoiceNotifyFollowGlobal = _tmp_6 != 0;
            final boolean _tmpEndVoiceNotifyFollowGlobal;
            final int _tmp_7;
            _tmp_7 = _cursor.getInt(_cursorIndexOfEndVoiceNotifyFollowGlobal);
            _tmpEndVoiceNotifyFollowGlobal = _tmp_7 != 0;
            final boolean _tmpStartShakeNotifyFollowGlobal;
            final int _tmp_8;
            _tmp_8 = _cursor.getInt(_cursorIndexOfStartShakeNotifyFollowGlobal);
            _tmpStartShakeNotifyFollowGlobal = _tmp_8 != 0;
            final boolean _tmpEndShakeNotifyFollowGlobal;
            final int _tmp_9;
            _tmp_9 = _cursor.getInt(_cursorIndexOfEndShakeNotifyFollowGlobal);
            _tmpEndShakeNotifyFollowGlobal = _tmp_9 != 0;
            _tmpLockConfig = new LockConfig(_tmpBgUrl,_tmpIsRemoveNotification,_tmpIsSilent,_tmpStartVoiceNotify,_tmpEndVoiceNotify,_tmpStartShakeNotify,_tmpEndShakeNotify,_tmpWhiteFollowGlobal,_tmpBgUrlFollowGlobal,_tmpIsRemoveNotificationFollowGlobal,_tmpIsSilentFollowGlobal,_tmpStartVoiceNotifyFollowGlobal,_tmpEndVoiceNotifyFollowGlobal,_tmpStartShakeNotifyFollowGlobal,_tmpEndShakeNotifyFollowGlobal);
            _item = new Tomato(_tmpId,_tmpUserId,_tmpTomatoIndexId,_tmpTitle,_tmpTomatoWorkLength,_tmpTomatoRestLength,_tmpTomatoCount,_tmpTomatoLongRestPerCount,_tmpTomatoLongRestLength,_tmpTrend,_tmpSyncState,_tmpSyncTime,_tmpUuid,_tmpVersion,_tmpLockConfig);
            _result.add(_item);
          }
          return _result;
        } finally {
          _cursor.close();
          _statement.release();
        }
      }
    }, $completion);
  }

  @Override
  public LiveData<List<TomatoWithSub>> getTomatoesWithSub(final int userId) {
    final String _sql = "select * From Tomato where userId = ? and syncState>=0 order by trend";
    final RoomSQLiteQuery _statement = RoomSQLiteQuery.acquire(_sql, 1);
    int _argIndex = 1;
    _statement.bindLong(_argIndex, userId);
    return __db.getInvalidationTracker().createLiveData(new String[] {"WhiteApp",
        "Tomato"}, true, new Callable<List<TomatoWithSub>>() {
      @Override
      @Nullable
      public List<TomatoWithSub> call() throws Exception {
        __db.beginTransaction();
        try {
          final Cursor _cursor = DBUtil.query(__db, _statement, true, null);
          try {
            final int _cursorIndexOfId = CursorUtil.getColumnIndexOrThrow(_cursor, "id");
            final int _cursorIndexOfUserId = CursorUtil.getColumnIndexOrThrow(_cursor, "userId");
            final int _cursorIndexOfTomatoIndexId = CursorUtil.getColumnIndexOrThrow(_cursor, "tomatoIndexId");
            final int _cursorIndexOfTitle = CursorUtil.getColumnIndexOrThrow(_cursor, "title");
            final int _cursorIndexOfTomatoWorkLength = CursorUtil.getColumnIndexOrThrow(_cursor, "tomatoWorkLength");
            final int _cursorIndexOfTomatoRestLength = CursorUtil.getColumnIndexOrThrow(_cursor, "tomatoRestLength");
            final int _cursorIndexOfTomatoCount = CursorUtil.getColumnIndexOrThrow(_cursor, "tomatoCount");
            final int _cursorIndexOfTomatoLongRestPerCount = CursorUtil.getColumnIndexOrThrow(_cursor, "tomatoLongRestPerCount");
            final int _cursorIndexOfTomatoLongRestLength = CursorUtil.getColumnIndexOrThrow(_cursor, "tomatoLongRestLength");
            final int _cursorIndexOfTrend = CursorUtil.getColumnIndexOrThrow(_cursor, "trend");
            final int _cursorIndexOfSyncState = CursorUtil.getColumnIndexOrThrow(_cursor, "syncState");
            final int _cursorIndexOfSyncTime = CursorUtil.getColumnIndexOrThrow(_cursor, "syncTime");
            final int _cursorIndexOfUuid = CursorUtil.getColumnIndexOrThrow(_cursor, "uuid");
            final int _cursorIndexOfVersion = CursorUtil.getColumnIndexOrThrow(_cursor, "version");
            final int _cursorIndexOfBgUrl = CursorUtil.getColumnIndexOrThrow(_cursor, "bgUrl");
            final int _cursorIndexOfIsRemoveNotification = CursorUtil.getColumnIndexOrThrow(_cursor, "isRemoveNotification");
            final int _cursorIndexOfIsSilent = CursorUtil.getColumnIndexOrThrow(_cursor, "isSilent");
            final int _cursorIndexOfStartVoiceNotify = CursorUtil.getColumnIndexOrThrow(_cursor, "startVoiceNotify");
            final int _cursorIndexOfEndVoiceNotify = CursorUtil.getColumnIndexOrThrow(_cursor, "endVoiceNotify");
            final int _cursorIndexOfStartShakeNotify = CursorUtil.getColumnIndexOrThrow(_cursor, "startShakeNotify");
            final int _cursorIndexOfEndShakeNotify = CursorUtil.getColumnIndexOrThrow(_cursor, "endShakeNotify");
            final int _cursorIndexOfWhiteFollowGlobal = CursorUtil.getColumnIndexOrThrow(_cursor, "whiteFollowGlobal");
            final int _cursorIndexOfBgUrlFollowGlobal = CursorUtil.getColumnIndexOrThrow(_cursor, "bgUrlFollowGlobal");
            final int _cursorIndexOfIsRemoveNotificationFollowGlobal = CursorUtil.getColumnIndexOrThrow(_cursor, "isRemoveNotificationFollowGlobal");
            final int _cursorIndexOfIsSilentFollowGlobal = CursorUtil.getColumnIndexOrThrow(_cursor, "isSilentFollowGlobal");
            final int _cursorIndexOfStartVoiceNotifyFollowGlobal = CursorUtil.getColumnIndexOrThrow(_cursor, "startVoiceNotifyFollowGlobal");
            final int _cursorIndexOfEndVoiceNotifyFollowGlobal = CursorUtil.getColumnIndexOrThrow(_cursor, "endVoiceNotifyFollowGlobal");
            final int _cursorIndexOfStartShakeNotifyFollowGlobal = CursorUtil.getColumnIndexOrThrow(_cursor, "startShakeNotifyFollowGlobal");
            final int _cursorIndexOfEndShakeNotifyFollowGlobal = CursorUtil.getColumnIndexOrThrow(_cursor, "endShakeNotifyFollowGlobal");
            final HashMap<String, ArrayList<WhiteApp>> _collectionWhiteList = new HashMap<String, ArrayList<WhiteApp>>();
            while (_cursor.moveToNext()) {
              final String _tmpKey;
              if (_cursor.isNull(_cursorIndexOfTomatoIndexId)) {
                _tmpKey = null;
              } else {
                _tmpKey = _cursor.getString(_cursorIndexOfTomatoIndexId);
              }
              if (_tmpKey != null) {
                if (!_collectionWhiteList.containsKey(_tmpKey)) {
                  _collectionWhiteList.put(_tmpKey, new ArrayList<WhiteApp>());
                }
              }
            }
            _cursor.moveToPosition(-1);
            __fetchRelationshipWhiteAppAscomLijianqiang12SilentDataModelDbWhiteApp(_collectionWhiteList);
            final List<TomatoWithSub> _result = new ArrayList<TomatoWithSub>(_cursor.getCount());
            while (_cursor.moveToNext()) {
              final TomatoWithSub _item;
              final Tomato _tmpTomato;
              final long _tmpId;
              _tmpId = _cursor.getLong(_cursorIndexOfId);
              final int _tmpUserId;
              _tmpUserId = _cursor.getInt(_cursorIndexOfUserId);
              final String _tmpTomatoIndexId;
              if (_cursor.isNull(_cursorIndexOfTomatoIndexId)) {
                _tmpTomatoIndexId = null;
              } else {
                _tmpTomatoIndexId = _cursor.getString(_cursorIndexOfTomatoIndexId);
              }
              final String _tmpTitle;
              if (_cursor.isNull(_cursorIndexOfTitle)) {
                _tmpTitle = null;
              } else {
                _tmpTitle = _cursor.getString(_cursorIndexOfTitle);
              }
              final int _tmpTomatoWorkLength;
              _tmpTomatoWorkLength = _cursor.getInt(_cursorIndexOfTomatoWorkLength);
              final int _tmpTomatoRestLength;
              _tmpTomatoRestLength = _cursor.getInt(_cursorIndexOfTomatoRestLength);
              final int _tmpTomatoCount;
              _tmpTomatoCount = _cursor.getInt(_cursorIndexOfTomatoCount);
              final int _tmpTomatoLongRestPerCount;
              _tmpTomatoLongRestPerCount = _cursor.getInt(_cursorIndexOfTomatoLongRestPerCount);
              final int _tmpTomatoLongRestLength;
              _tmpTomatoLongRestLength = _cursor.getInt(_cursorIndexOfTomatoLongRestLength);
              final int _tmpTrend;
              _tmpTrend = _cursor.getInt(_cursorIndexOfTrend);
              final int _tmpSyncState;
              _tmpSyncState = _cursor.getInt(_cursorIndexOfSyncState);
              final long _tmpSyncTime;
              _tmpSyncTime = _cursor.getLong(_cursorIndexOfSyncTime);
              final long _tmpUuid;
              _tmpUuid = _cursor.getLong(_cursorIndexOfUuid);
              final int _tmpVersion;
              _tmpVersion = _cursor.getInt(_cursorIndexOfVersion);
              final LockConfig _tmpLockConfig;
              final String _tmpBgUrl;
              if (_cursor.isNull(_cursorIndexOfBgUrl)) {
                _tmpBgUrl = null;
              } else {
                _tmpBgUrl = _cursor.getString(_cursorIndexOfBgUrl);
              }
              final boolean _tmpIsRemoveNotification;
              final int _tmp;
              _tmp = _cursor.getInt(_cursorIndexOfIsRemoveNotification);
              _tmpIsRemoveNotification = _tmp != 0;
              final boolean _tmpIsSilent;
              final int _tmp_1;
              _tmp_1 = _cursor.getInt(_cursorIndexOfIsSilent);
              _tmpIsSilent = _tmp_1 != 0;
              final int _tmpStartVoiceNotify;
              _tmpStartVoiceNotify = _cursor.getInt(_cursorIndexOfStartVoiceNotify);
              final int _tmpEndVoiceNotify;
              _tmpEndVoiceNotify = _cursor.getInt(_cursorIndexOfEndVoiceNotify);
              final long _tmpStartShakeNotify;
              _tmpStartShakeNotify = _cursor.getLong(_cursorIndexOfStartShakeNotify);
              final long _tmpEndShakeNotify;
              _tmpEndShakeNotify = _cursor.getLong(_cursorIndexOfEndShakeNotify);
              final boolean _tmpWhiteFollowGlobal;
              final int _tmp_2;
              _tmp_2 = _cursor.getInt(_cursorIndexOfWhiteFollowGlobal);
              _tmpWhiteFollowGlobal = _tmp_2 != 0;
              final boolean _tmpBgUrlFollowGlobal;
              final int _tmp_3;
              _tmp_3 = _cursor.getInt(_cursorIndexOfBgUrlFollowGlobal);
              _tmpBgUrlFollowGlobal = _tmp_3 != 0;
              final boolean _tmpIsRemoveNotificationFollowGlobal;
              final int _tmp_4;
              _tmp_4 = _cursor.getInt(_cursorIndexOfIsRemoveNotificationFollowGlobal);
              _tmpIsRemoveNotificationFollowGlobal = _tmp_4 != 0;
              final boolean _tmpIsSilentFollowGlobal;
              final int _tmp_5;
              _tmp_5 = _cursor.getInt(_cursorIndexOfIsSilentFollowGlobal);
              _tmpIsSilentFollowGlobal = _tmp_5 != 0;
              final boolean _tmpStartVoiceNotifyFollowGlobal;
              final int _tmp_6;
              _tmp_6 = _cursor.getInt(_cursorIndexOfStartVoiceNotifyFollowGlobal);
              _tmpStartVoiceNotifyFollowGlobal = _tmp_6 != 0;
              final boolean _tmpEndVoiceNotifyFollowGlobal;
              final int _tmp_7;
              _tmp_7 = _cursor.getInt(_cursorIndexOfEndVoiceNotifyFollowGlobal);
              _tmpEndVoiceNotifyFollowGlobal = _tmp_7 != 0;
              final boolean _tmpStartShakeNotifyFollowGlobal;
              final int _tmp_8;
              _tmp_8 = _cursor.getInt(_cursorIndexOfStartShakeNotifyFollowGlobal);
              _tmpStartShakeNotifyFollowGlobal = _tmp_8 != 0;
              final boolean _tmpEndShakeNotifyFollowGlobal;
              final int _tmp_9;
              _tmp_9 = _cursor.getInt(_cursorIndexOfEndShakeNotifyFollowGlobal);
              _tmpEndShakeNotifyFollowGlobal = _tmp_9 != 0;
              _tmpLockConfig = new LockConfig(_tmpBgUrl,_tmpIsRemoveNotification,_tmpIsSilent,_tmpStartVoiceNotify,_tmpEndVoiceNotify,_tmpStartShakeNotify,_tmpEndShakeNotify,_tmpWhiteFollowGlobal,_tmpBgUrlFollowGlobal,_tmpIsRemoveNotificationFollowGlobal,_tmpIsSilentFollowGlobal,_tmpStartVoiceNotifyFollowGlobal,_tmpEndVoiceNotifyFollowGlobal,_tmpStartShakeNotifyFollowGlobal,_tmpEndShakeNotifyFollowGlobal);
              _tmpTomato = new Tomato(_tmpId,_tmpUserId,_tmpTomatoIndexId,_tmpTitle,_tmpTomatoWorkLength,_tmpTomatoRestLength,_tmpTomatoCount,_tmpTomatoLongRestPerCount,_tmpTomatoLongRestLength,_tmpTrend,_tmpSyncState,_tmpSyncTime,_tmpUuid,_tmpVersion,_tmpLockConfig);
              final ArrayList<WhiteApp> _tmpWhiteListCollection;
              final String _tmpKey_1;
              if (_cursor.isNull(_cursorIndexOfTomatoIndexId)) {
                _tmpKey_1 = null;
              } else {
                _tmpKey_1 = _cursor.getString(_cursorIndexOfTomatoIndexId);
              }
              if (_tmpKey_1 != null) {
                _tmpWhiteListCollection = _collectionWhiteList.get(_tmpKey_1);
              } else {
                _tmpWhiteListCollection = new ArrayList<WhiteApp>();
              }
              _item = new TomatoWithSub(_tmpTomato,_tmpWhiteListCollection);
              _result.add(_item);
            }
            __db.setTransactionSuccessful();
            return _result;
          } finally {
            _cursor.close();
          }
        } finally {
          __db.endTransaction();
        }
      }

      @Override
      protected void finalize() {
        _statement.release();
      }
    });
  }

  @Override
  public Object getTomatoWithSub(final String tomatoIndexId,
      final Continuation<? super TomatoWithSub> $completion) {
    final String _sql = "select * from Tomato where tomatoIndexId = ? and  syncState>=0 limit 1";
    final RoomSQLiteQuery _statement = RoomSQLiteQuery.acquire(_sql, 1);
    int _argIndex = 1;
    if (tomatoIndexId == null) {
      _statement.bindNull(_argIndex);
    } else {
      _statement.bindString(_argIndex, tomatoIndexId);
    }
    final CancellationSignal _cancellationSignal = DBUtil.createCancellationSignal();
    return CoroutinesRoom.execute(__db, true, _cancellationSignal, new Callable<TomatoWithSub>() {
      @Override
      @Nullable
      public TomatoWithSub call() throws Exception {
        __db.beginTransaction();
        try {
          final Cursor _cursor = DBUtil.query(__db, _statement, true, null);
          try {
            final int _cursorIndexOfId = CursorUtil.getColumnIndexOrThrow(_cursor, "id");
            final int _cursorIndexOfUserId = CursorUtil.getColumnIndexOrThrow(_cursor, "userId");
            final int _cursorIndexOfTomatoIndexId = CursorUtil.getColumnIndexOrThrow(_cursor, "tomatoIndexId");
            final int _cursorIndexOfTitle = CursorUtil.getColumnIndexOrThrow(_cursor, "title");
            final int _cursorIndexOfTomatoWorkLength = CursorUtil.getColumnIndexOrThrow(_cursor, "tomatoWorkLength");
            final int _cursorIndexOfTomatoRestLength = CursorUtil.getColumnIndexOrThrow(_cursor, "tomatoRestLength");
            final int _cursorIndexOfTomatoCount = CursorUtil.getColumnIndexOrThrow(_cursor, "tomatoCount");
            final int _cursorIndexOfTomatoLongRestPerCount = CursorUtil.getColumnIndexOrThrow(_cursor, "tomatoLongRestPerCount");
            final int _cursorIndexOfTomatoLongRestLength = CursorUtil.getColumnIndexOrThrow(_cursor, "tomatoLongRestLength");
            final int _cursorIndexOfTrend = CursorUtil.getColumnIndexOrThrow(_cursor, "trend");
            final int _cursorIndexOfSyncState = CursorUtil.getColumnIndexOrThrow(_cursor, "syncState");
            final int _cursorIndexOfSyncTime = CursorUtil.getColumnIndexOrThrow(_cursor, "syncTime");
            final int _cursorIndexOfUuid = CursorUtil.getColumnIndexOrThrow(_cursor, "uuid");
            final int _cursorIndexOfVersion = CursorUtil.getColumnIndexOrThrow(_cursor, "version");
            final int _cursorIndexOfBgUrl = CursorUtil.getColumnIndexOrThrow(_cursor, "bgUrl");
            final int _cursorIndexOfIsRemoveNotification = CursorUtil.getColumnIndexOrThrow(_cursor, "isRemoveNotification");
            final int _cursorIndexOfIsSilent = CursorUtil.getColumnIndexOrThrow(_cursor, "isSilent");
            final int _cursorIndexOfStartVoiceNotify = CursorUtil.getColumnIndexOrThrow(_cursor, "startVoiceNotify");
            final int _cursorIndexOfEndVoiceNotify = CursorUtil.getColumnIndexOrThrow(_cursor, "endVoiceNotify");
            final int _cursorIndexOfStartShakeNotify = CursorUtil.getColumnIndexOrThrow(_cursor, "startShakeNotify");
            final int _cursorIndexOfEndShakeNotify = CursorUtil.getColumnIndexOrThrow(_cursor, "endShakeNotify");
            final int _cursorIndexOfWhiteFollowGlobal = CursorUtil.getColumnIndexOrThrow(_cursor, "whiteFollowGlobal");
            final int _cursorIndexOfBgUrlFollowGlobal = CursorUtil.getColumnIndexOrThrow(_cursor, "bgUrlFollowGlobal");
            final int _cursorIndexOfIsRemoveNotificationFollowGlobal = CursorUtil.getColumnIndexOrThrow(_cursor, "isRemoveNotificationFollowGlobal");
            final int _cursorIndexOfIsSilentFollowGlobal = CursorUtil.getColumnIndexOrThrow(_cursor, "isSilentFollowGlobal");
            final int _cursorIndexOfStartVoiceNotifyFollowGlobal = CursorUtil.getColumnIndexOrThrow(_cursor, "startVoiceNotifyFollowGlobal");
            final int _cursorIndexOfEndVoiceNotifyFollowGlobal = CursorUtil.getColumnIndexOrThrow(_cursor, "endVoiceNotifyFollowGlobal");
            final int _cursorIndexOfStartShakeNotifyFollowGlobal = CursorUtil.getColumnIndexOrThrow(_cursor, "startShakeNotifyFollowGlobal");
            final int _cursorIndexOfEndShakeNotifyFollowGlobal = CursorUtil.getColumnIndexOrThrow(_cursor, "endShakeNotifyFollowGlobal");
            final HashMap<String, ArrayList<WhiteApp>> _collectionWhiteList = new HashMap<String, ArrayList<WhiteApp>>();
            while (_cursor.moveToNext()) {
              final String _tmpKey;
              if (_cursor.isNull(_cursorIndexOfTomatoIndexId)) {
                _tmpKey = null;
              } else {
                _tmpKey = _cursor.getString(_cursorIndexOfTomatoIndexId);
              }
              if (_tmpKey != null) {
                if (!_collectionWhiteList.containsKey(_tmpKey)) {
                  _collectionWhiteList.put(_tmpKey, new ArrayList<WhiteApp>());
                }
              }
            }
            _cursor.moveToPosition(-1);
            __fetchRelationshipWhiteAppAscomLijianqiang12SilentDataModelDbWhiteApp(_collectionWhiteList);
            final TomatoWithSub _result;
            if (_cursor.moveToFirst()) {
              final Tomato _tmpTomato;
              final long _tmpId;
              _tmpId = _cursor.getLong(_cursorIndexOfId);
              final int _tmpUserId;
              _tmpUserId = _cursor.getInt(_cursorIndexOfUserId);
              final String _tmpTomatoIndexId;
              if (_cursor.isNull(_cursorIndexOfTomatoIndexId)) {
                _tmpTomatoIndexId = null;
              } else {
                _tmpTomatoIndexId = _cursor.getString(_cursorIndexOfTomatoIndexId);
              }
              final String _tmpTitle;
              if (_cursor.isNull(_cursorIndexOfTitle)) {
                _tmpTitle = null;
              } else {
                _tmpTitle = _cursor.getString(_cursorIndexOfTitle);
              }
              final int _tmpTomatoWorkLength;
              _tmpTomatoWorkLength = _cursor.getInt(_cursorIndexOfTomatoWorkLength);
              final int _tmpTomatoRestLength;
              _tmpTomatoRestLength = _cursor.getInt(_cursorIndexOfTomatoRestLength);
              final int _tmpTomatoCount;
              _tmpTomatoCount = _cursor.getInt(_cursorIndexOfTomatoCount);
              final int _tmpTomatoLongRestPerCount;
              _tmpTomatoLongRestPerCount = _cursor.getInt(_cursorIndexOfTomatoLongRestPerCount);
              final int _tmpTomatoLongRestLength;
              _tmpTomatoLongRestLength = _cursor.getInt(_cursorIndexOfTomatoLongRestLength);
              final int _tmpTrend;
              _tmpTrend = _cursor.getInt(_cursorIndexOfTrend);
              final int _tmpSyncState;
              _tmpSyncState = _cursor.getInt(_cursorIndexOfSyncState);
              final long _tmpSyncTime;
              _tmpSyncTime = _cursor.getLong(_cursorIndexOfSyncTime);
              final long _tmpUuid;
              _tmpUuid = _cursor.getLong(_cursorIndexOfUuid);
              final int _tmpVersion;
              _tmpVersion = _cursor.getInt(_cursorIndexOfVersion);
              final LockConfig _tmpLockConfig;
              final String _tmpBgUrl;
              if (_cursor.isNull(_cursorIndexOfBgUrl)) {
                _tmpBgUrl = null;
              } else {
                _tmpBgUrl = _cursor.getString(_cursorIndexOfBgUrl);
              }
              final boolean _tmpIsRemoveNotification;
              final int _tmp;
              _tmp = _cursor.getInt(_cursorIndexOfIsRemoveNotification);
              _tmpIsRemoveNotification = _tmp != 0;
              final boolean _tmpIsSilent;
              final int _tmp_1;
              _tmp_1 = _cursor.getInt(_cursorIndexOfIsSilent);
              _tmpIsSilent = _tmp_1 != 0;
              final int _tmpStartVoiceNotify;
              _tmpStartVoiceNotify = _cursor.getInt(_cursorIndexOfStartVoiceNotify);
              final int _tmpEndVoiceNotify;
              _tmpEndVoiceNotify = _cursor.getInt(_cursorIndexOfEndVoiceNotify);
              final long _tmpStartShakeNotify;
              _tmpStartShakeNotify = _cursor.getLong(_cursorIndexOfStartShakeNotify);
              final long _tmpEndShakeNotify;
              _tmpEndShakeNotify = _cursor.getLong(_cursorIndexOfEndShakeNotify);
              final boolean _tmpWhiteFollowGlobal;
              final int _tmp_2;
              _tmp_2 = _cursor.getInt(_cursorIndexOfWhiteFollowGlobal);
              _tmpWhiteFollowGlobal = _tmp_2 != 0;
              final boolean _tmpBgUrlFollowGlobal;
              final int _tmp_3;
              _tmp_3 = _cursor.getInt(_cursorIndexOfBgUrlFollowGlobal);
              _tmpBgUrlFollowGlobal = _tmp_3 != 0;
              final boolean _tmpIsRemoveNotificationFollowGlobal;
              final int _tmp_4;
              _tmp_4 = _cursor.getInt(_cursorIndexOfIsRemoveNotificationFollowGlobal);
              _tmpIsRemoveNotificationFollowGlobal = _tmp_4 != 0;
              final boolean _tmpIsSilentFollowGlobal;
              final int _tmp_5;
              _tmp_5 = _cursor.getInt(_cursorIndexOfIsSilentFollowGlobal);
              _tmpIsSilentFollowGlobal = _tmp_5 != 0;
              final boolean _tmpStartVoiceNotifyFollowGlobal;
              final int _tmp_6;
              _tmp_6 = _cursor.getInt(_cursorIndexOfStartVoiceNotifyFollowGlobal);
              _tmpStartVoiceNotifyFollowGlobal = _tmp_6 != 0;
              final boolean _tmpEndVoiceNotifyFollowGlobal;
              final int _tmp_7;
              _tmp_7 = _cursor.getInt(_cursorIndexOfEndVoiceNotifyFollowGlobal);
              _tmpEndVoiceNotifyFollowGlobal = _tmp_7 != 0;
              final boolean _tmpStartShakeNotifyFollowGlobal;
              final int _tmp_8;
              _tmp_8 = _cursor.getInt(_cursorIndexOfStartShakeNotifyFollowGlobal);
              _tmpStartShakeNotifyFollowGlobal = _tmp_8 != 0;
              final boolean _tmpEndShakeNotifyFollowGlobal;
              final int _tmp_9;
              _tmp_9 = _cursor.getInt(_cursorIndexOfEndShakeNotifyFollowGlobal);
              _tmpEndShakeNotifyFollowGlobal = _tmp_9 != 0;
              _tmpLockConfig = new LockConfig(_tmpBgUrl,_tmpIsRemoveNotification,_tmpIsSilent,_tmpStartVoiceNotify,_tmpEndVoiceNotify,_tmpStartShakeNotify,_tmpEndShakeNotify,_tmpWhiteFollowGlobal,_tmpBgUrlFollowGlobal,_tmpIsRemoveNotificationFollowGlobal,_tmpIsSilentFollowGlobal,_tmpStartVoiceNotifyFollowGlobal,_tmpEndVoiceNotifyFollowGlobal,_tmpStartShakeNotifyFollowGlobal,_tmpEndShakeNotifyFollowGlobal);
              _tmpTomato = new Tomato(_tmpId,_tmpUserId,_tmpTomatoIndexId,_tmpTitle,_tmpTomatoWorkLength,_tmpTomatoRestLength,_tmpTomatoCount,_tmpTomatoLongRestPerCount,_tmpTomatoLongRestLength,_tmpTrend,_tmpSyncState,_tmpSyncTime,_tmpUuid,_tmpVersion,_tmpLockConfig);
              final ArrayList<WhiteApp> _tmpWhiteListCollection;
              final String _tmpKey_1;
              if (_cursor.isNull(_cursorIndexOfTomatoIndexId)) {
                _tmpKey_1 = null;
              } else {
                _tmpKey_1 = _cursor.getString(_cursorIndexOfTomatoIndexId);
              }
              if (_tmpKey_1 != null) {
                _tmpWhiteListCollection = _collectionWhiteList.get(_tmpKey_1);
              } else {
                _tmpWhiteListCollection = new ArrayList<WhiteApp>();
              }
              _result = new TomatoWithSub(_tmpTomato,_tmpWhiteListCollection);
            } else {
              _result = null;
            }
            __db.setTransactionSuccessful();
            return _result;
          } finally {
            _cursor.close();
            _statement.release();
          }
        } finally {
          __db.endTransaction();
        }
      }
    }, $completion);
  }

  @Override
  public Object getLastTomato(final int userId, final Continuation<? super Tomato> $completion) {
    final String _sql = "select * from Tomato Where userId = ? order by trend desc limit 1";
    final RoomSQLiteQuery _statement = RoomSQLiteQuery.acquire(_sql, 1);
    int _argIndex = 1;
    _statement.bindLong(_argIndex, userId);
    final CancellationSignal _cancellationSignal = DBUtil.createCancellationSignal();
    return CoroutinesRoom.execute(__db, false, _cancellationSignal, new Callable<Tomato>() {
      @Override
      @Nullable
      public Tomato call() throws Exception {
        final Cursor _cursor = DBUtil.query(__db, _statement, false, null);
        try {
          final int _cursorIndexOfId = CursorUtil.getColumnIndexOrThrow(_cursor, "id");
          final int _cursorIndexOfUserId = CursorUtil.getColumnIndexOrThrow(_cursor, "userId");
          final int _cursorIndexOfTomatoIndexId = CursorUtil.getColumnIndexOrThrow(_cursor, "tomatoIndexId");
          final int _cursorIndexOfTitle = CursorUtil.getColumnIndexOrThrow(_cursor, "title");
          final int _cursorIndexOfTomatoWorkLength = CursorUtil.getColumnIndexOrThrow(_cursor, "tomatoWorkLength");
          final int _cursorIndexOfTomatoRestLength = CursorUtil.getColumnIndexOrThrow(_cursor, "tomatoRestLength");
          final int _cursorIndexOfTomatoCount = CursorUtil.getColumnIndexOrThrow(_cursor, "tomatoCount");
          final int _cursorIndexOfTomatoLongRestPerCount = CursorUtil.getColumnIndexOrThrow(_cursor, "tomatoLongRestPerCount");
          final int _cursorIndexOfTomatoLongRestLength = CursorUtil.getColumnIndexOrThrow(_cursor, "tomatoLongRestLength");
          final int _cursorIndexOfTrend = CursorUtil.getColumnIndexOrThrow(_cursor, "trend");
          final int _cursorIndexOfSyncState = CursorUtil.getColumnIndexOrThrow(_cursor, "syncState");
          final int _cursorIndexOfSyncTime = CursorUtil.getColumnIndexOrThrow(_cursor, "syncTime");
          final int _cursorIndexOfUuid = CursorUtil.getColumnIndexOrThrow(_cursor, "uuid");
          final int _cursorIndexOfVersion = CursorUtil.getColumnIndexOrThrow(_cursor, "version");
          final int _cursorIndexOfBgUrl = CursorUtil.getColumnIndexOrThrow(_cursor, "bgUrl");
          final int _cursorIndexOfIsRemoveNotification = CursorUtil.getColumnIndexOrThrow(_cursor, "isRemoveNotification");
          final int _cursorIndexOfIsSilent = CursorUtil.getColumnIndexOrThrow(_cursor, "isSilent");
          final int _cursorIndexOfStartVoiceNotify = CursorUtil.getColumnIndexOrThrow(_cursor, "startVoiceNotify");
          final int _cursorIndexOfEndVoiceNotify = CursorUtil.getColumnIndexOrThrow(_cursor, "endVoiceNotify");
          final int _cursorIndexOfStartShakeNotify = CursorUtil.getColumnIndexOrThrow(_cursor, "startShakeNotify");
          final int _cursorIndexOfEndShakeNotify = CursorUtil.getColumnIndexOrThrow(_cursor, "endShakeNotify");
          final int _cursorIndexOfWhiteFollowGlobal = CursorUtil.getColumnIndexOrThrow(_cursor, "whiteFollowGlobal");
          final int _cursorIndexOfBgUrlFollowGlobal = CursorUtil.getColumnIndexOrThrow(_cursor, "bgUrlFollowGlobal");
          final int _cursorIndexOfIsRemoveNotificationFollowGlobal = CursorUtil.getColumnIndexOrThrow(_cursor, "isRemoveNotificationFollowGlobal");
          final int _cursorIndexOfIsSilentFollowGlobal = CursorUtil.getColumnIndexOrThrow(_cursor, "isSilentFollowGlobal");
          final int _cursorIndexOfStartVoiceNotifyFollowGlobal = CursorUtil.getColumnIndexOrThrow(_cursor, "startVoiceNotifyFollowGlobal");
          final int _cursorIndexOfEndVoiceNotifyFollowGlobal = CursorUtil.getColumnIndexOrThrow(_cursor, "endVoiceNotifyFollowGlobal");
          final int _cursorIndexOfStartShakeNotifyFollowGlobal = CursorUtil.getColumnIndexOrThrow(_cursor, "startShakeNotifyFollowGlobal");
          final int _cursorIndexOfEndShakeNotifyFollowGlobal = CursorUtil.getColumnIndexOrThrow(_cursor, "endShakeNotifyFollowGlobal");
          final Tomato _result;
          if (_cursor.moveToFirst()) {
            final long _tmpId;
            _tmpId = _cursor.getLong(_cursorIndexOfId);
            final int _tmpUserId;
            _tmpUserId = _cursor.getInt(_cursorIndexOfUserId);
            final String _tmpTomatoIndexId;
            if (_cursor.isNull(_cursorIndexOfTomatoIndexId)) {
              _tmpTomatoIndexId = null;
            } else {
              _tmpTomatoIndexId = _cursor.getString(_cursorIndexOfTomatoIndexId);
            }
            final String _tmpTitle;
            if (_cursor.isNull(_cursorIndexOfTitle)) {
              _tmpTitle = null;
            } else {
              _tmpTitle = _cursor.getString(_cursorIndexOfTitle);
            }
            final int _tmpTomatoWorkLength;
            _tmpTomatoWorkLength = _cursor.getInt(_cursorIndexOfTomatoWorkLength);
            final int _tmpTomatoRestLength;
            _tmpTomatoRestLength = _cursor.getInt(_cursorIndexOfTomatoRestLength);
            final int _tmpTomatoCount;
            _tmpTomatoCount = _cursor.getInt(_cursorIndexOfTomatoCount);
            final int _tmpTomatoLongRestPerCount;
            _tmpTomatoLongRestPerCount = _cursor.getInt(_cursorIndexOfTomatoLongRestPerCount);
            final int _tmpTomatoLongRestLength;
            _tmpTomatoLongRestLength = _cursor.getInt(_cursorIndexOfTomatoLongRestLength);
            final int _tmpTrend;
            _tmpTrend = _cursor.getInt(_cursorIndexOfTrend);
            final int _tmpSyncState;
            _tmpSyncState = _cursor.getInt(_cursorIndexOfSyncState);
            final long _tmpSyncTime;
            _tmpSyncTime = _cursor.getLong(_cursorIndexOfSyncTime);
            final long _tmpUuid;
            _tmpUuid = _cursor.getLong(_cursorIndexOfUuid);
            final int _tmpVersion;
            _tmpVersion = _cursor.getInt(_cursorIndexOfVersion);
            final LockConfig _tmpLockConfig;
            final String _tmpBgUrl;
            if (_cursor.isNull(_cursorIndexOfBgUrl)) {
              _tmpBgUrl = null;
            } else {
              _tmpBgUrl = _cursor.getString(_cursorIndexOfBgUrl);
            }
            final boolean _tmpIsRemoveNotification;
            final int _tmp;
            _tmp = _cursor.getInt(_cursorIndexOfIsRemoveNotification);
            _tmpIsRemoveNotification = _tmp != 0;
            final boolean _tmpIsSilent;
            final int _tmp_1;
            _tmp_1 = _cursor.getInt(_cursorIndexOfIsSilent);
            _tmpIsSilent = _tmp_1 != 0;
            final int _tmpStartVoiceNotify;
            _tmpStartVoiceNotify = _cursor.getInt(_cursorIndexOfStartVoiceNotify);
            final int _tmpEndVoiceNotify;
            _tmpEndVoiceNotify = _cursor.getInt(_cursorIndexOfEndVoiceNotify);
            final long _tmpStartShakeNotify;
            _tmpStartShakeNotify = _cursor.getLong(_cursorIndexOfStartShakeNotify);
            final long _tmpEndShakeNotify;
            _tmpEndShakeNotify = _cursor.getLong(_cursorIndexOfEndShakeNotify);
            final boolean _tmpWhiteFollowGlobal;
            final int _tmp_2;
            _tmp_2 = _cursor.getInt(_cursorIndexOfWhiteFollowGlobal);
            _tmpWhiteFollowGlobal = _tmp_2 != 0;
            final boolean _tmpBgUrlFollowGlobal;
            final int _tmp_3;
            _tmp_3 = _cursor.getInt(_cursorIndexOfBgUrlFollowGlobal);
            _tmpBgUrlFollowGlobal = _tmp_3 != 0;
            final boolean _tmpIsRemoveNotificationFollowGlobal;
            final int _tmp_4;
            _tmp_4 = _cursor.getInt(_cursorIndexOfIsRemoveNotificationFollowGlobal);
            _tmpIsRemoveNotificationFollowGlobal = _tmp_4 != 0;
            final boolean _tmpIsSilentFollowGlobal;
            final int _tmp_5;
            _tmp_5 = _cursor.getInt(_cursorIndexOfIsSilentFollowGlobal);
            _tmpIsSilentFollowGlobal = _tmp_5 != 0;
            final boolean _tmpStartVoiceNotifyFollowGlobal;
            final int _tmp_6;
            _tmp_6 = _cursor.getInt(_cursorIndexOfStartVoiceNotifyFollowGlobal);
            _tmpStartVoiceNotifyFollowGlobal = _tmp_6 != 0;
            final boolean _tmpEndVoiceNotifyFollowGlobal;
            final int _tmp_7;
            _tmp_7 = _cursor.getInt(_cursorIndexOfEndVoiceNotifyFollowGlobal);
            _tmpEndVoiceNotifyFollowGlobal = _tmp_7 != 0;
            final boolean _tmpStartShakeNotifyFollowGlobal;
            final int _tmp_8;
            _tmp_8 = _cursor.getInt(_cursorIndexOfStartShakeNotifyFollowGlobal);
            _tmpStartShakeNotifyFollowGlobal = _tmp_8 != 0;
            final boolean _tmpEndShakeNotifyFollowGlobal;
            final int _tmp_9;
            _tmp_9 = _cursor.getInt(_cursorIndexOfEndShakeNotifyFollowGlobal);
            _tmpEndShakeNotifyFollowGlobal = _tmp_9 != 0;
            _tmpLockConfig = new LockConfig(_tmpBgUrl,_tmpIsRemoveNotification,_tmpIsSilent,_tmpStartVoiceNotify,_tmpEndVoiceNotify,_tmpStartShakeNotify,_tmpEndShakeNotify,_tmpWhiteFollowGlobal,_tmpBgUrlFollowGlobal,_tmpIsRemoveNotificationFollowGlobal,_tmpIsSilentFollowGlobal,_tmpStartVoiceNotifyFollowGlobal,_tmpEndVoiceNotifyFollowGlobal,_tmpStartShakeNotifyFollowGlobal,_tmpEndShakeNotifyFollowGlobal);
            _result = new Tomato(_tmpId,_tmpUserId,_tmpTomatoIndexId,_tmpTitle,_tmpTomatoWorkLength,_tmpTomatoRestLength,_tmpTomatoCount,_tmpTomatoLongRestPerCount,_tmpTomatoLongRestLength,_tmpTrend,_tmpSyncState,_tmpSyncTime,_tmpUuid,_tmpVersion,_tmpLockConfig);
          } else {
            _result = null;
          }
          return _result;
        } finally {
          _cursor.close();
          _statement.release();
        }
      }
    }, $completion);
  }

  @NonNull
  public static List<Class<?>> getRequiredConverters() {
    return Collections.emptyList();
  }

  private void __fetchRelationshipWhiteAppAscomLijianqiang12SilentDataModelDbWhiteApp(
      @NonNull final HashMap<String, ArrayList<WhiteApp>> _map) {
    final Set<String> __mapKeySet = _map.keySet();
    if (__mapKeySet.isEmpty()) {
      return;
    }
    if (_map.size() > RoomDatabase.MAX_BIND_PARAMETER_CNT) {
      RelationUtil.recursiveFetchHashMap(_map, true, (map) -> {
        __fetchRelationshipWhiteAppAscomLijianqiang12SilentDataModelDbWhiteApp(map);
        return Unit.INSTANCE;
      });
      return;
    }
    final StringBuilder _stringBuilder = StringUtil.newStringBuilder();
    _stringBuilder.append("SELECT `id`,`userId`,`whiteAppIndexId`,`tomatoIndexId`,`scheduleIndexId`,`pkg`,`mainActivity`,`maxLen`,`trend`,`syncState`,`syncTime`,`uuid`,`version` FROM `WhiteApp` WHERE `tomatoIndexId` IN (");
    final int _inputSize = __mapKeySet == null ? 1 : __mapKeySet.size();
    StringUtil.appendPlaceholders(_stringBuilder, _inputSize);
    _stringBuilder.append(")");
    final String _sql = _stringBuilder.toString();
    final int _argCount = 0 + _inputSize;
    final RoomSQLiteQuery _stmt = RoomSQLiteQuery.acquire(_sql, _argCount);
    int _argIndex = 1;
    if (__mapKeySet == null) {
      _stmt.bindNull(_argIndex);
    } else {
      for (String _item : __mapKeySet) {
        if (_item == null) {
          _stmt.bindNull(_argIndex);
        } else {
          _stmt.bindString(_argIndex, _item);
        }
        _argIndex++;
      }
    }
    final Cursor _cursor = DBUtil.query(__db, _stmt, false, null);
    try {
      final int _itemKeyIndex = CursorUtil.getColumnIndex(_cursor, "tomatoIndexId");
      if (_itemKeyIndex == -1) {
        return;
      }
      final int _cursorIndexOfId = 0;
      final int _cursorIndexOfUserId = 1;
      final int _cursorIndexOfWhiteAppIndexId = 2;
      final int _cursorIndexOfTomatoIndexId = 3;
      final int _cursorIndexOfScheduleIndexId = 4;
      final int _cursorIndexOfPkg = 5;
      final int _cursorIndexOfMainActivity = 6;
      final int _cursorIndexOfMaxLen = 7;
      final int _cursorIndexOfTrend = 8;
      final int _cursorIndexOfSyncState = 9;
      final int _cursorIndexOfSyncTime = 10;
      final int _cursorIndexOfUuid = 11;
      final int _cursorIndexOfVersion = 12;
      while (_cursor.moveToNext()) {
        final String _tmpKey;
        if (_cursor.isNull(_itemKeyIndex)) {
          _tmpKey = null;
        } else {
          _tmpKey = _cursor.getString(_itemKeyIndex);
        }
        if (_tmpKey != null) {
          final ArrayList<WhiteApp> _tmpRelation = _map.get(_tmpKey);
          if (_tmpRelation != null) {
            final WhiteApp _item_1;
            final long _tmpId;
            _tmpId = _cursor.getLong(_cursorIndexOfId);
            final int _tmpUserId;
            _tmpUserId = _cursor.getInt(_cursorIndexOfUserId);
            final String _tmpWhiteAppIndexId;
            if (_cursor.isNull(_cursorIndexOfWhiteAppIndexId)) {
              _tmpWhiteAppIndexId = null;
            } else {
              _tmpWhiteAppIndexId = _cursor.getString(_cursorIndexOfWhiteAppIndexId);
            }
            final String _tmpTomatoIndexId;
            if (_cursor.isNull(_cursorIndexOfTomatoIndexId)) {
              _tmpTomatoIndexId = null;
            } else {
              _tmpTomatoIndexId = _cursor.getString(_cursorIndexOfTomatoIndexId);
            }
            final String _tmpScheduleIndexId;
            if (_cursor.isNull(_cursorIndexOfScheduleIndexId)) {
              _tmpScheduleIndexId = null;
            } else {
              _tmpScheduleIndexId = _cursor.getString(_cursorIndexOfScheduleIndexId);
            }
            final String _tmpPkg;
            if (_cursor.isNull(_cursorIndexOfPkg)) {
              _tmpPkg = null;
            } else {
              _tmpPkg = _cursor.getString(_cursorIndexOfPkg);
            }
            final String _tmpMainActivity;
            if (_cursor.isNull(_cursorIndexOfMainActivity)) {
              _tmpMainActivity = null;
            } else {
              _tmpMainActivity = _cursor.getString(_cursorIndexOfMainActivity);
            }
            final int _tmpMaxLen;
            _tmpMaxLen = _cursor.getInt(_cursorIndexOfMaxLen);
            final int _tmpTrend;
            _tmpTrend = _cursor.getInt(_cursorIndexOfTrend);
            final int _tmpSyncState;
            _tmpSyncState = _cursor.getInt(_cursorIndexOfSyncState);
            final long _tmpSyncTime;
            _tmpSyncTime = _cursor.getLong(_cursorIndexOfSyncTime);
            final long _tmpUuid;
            _tmpUuid = _cursor.getLong(_cursorIndexOfUuid);
            final int _tmpVersion;
            _tmpVersion = _cursor.getInt(_cursorIndexOfVersion);
            _item_1 = new WhiteApp(_tmpId,_tmpUserId,_tmpWhiteAppIndexId,_tmpTomatoIndexId,_tmpScheduleIndexId,_tmpPkg,_tmpMainActivity,_tmpMaxLen,_tmpTrend,_tmpSyncState,_tmpSyncTime,_tmpUuid,_tmpVersion);
            _tmpRelation.add(_item_1);
          }
        }
      }
    } finally {
      _cursor.close();
    }
  }
}
