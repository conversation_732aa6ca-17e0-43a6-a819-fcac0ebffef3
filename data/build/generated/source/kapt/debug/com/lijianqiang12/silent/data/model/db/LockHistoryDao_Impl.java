package com.lijianqiang12.silent.data.model.db;

import android.database.Cursor;
import android.os.CancellationSignal;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.lifecycle.LiveData;
import androidx.room.CoroutinesRoom;
import androidx.room.EntityDeletionOrUpdateAdapter;
import androidx.room.EntityInsertionAdapter;
import androidx.room.RoomDatabase;
import androidx.room.RoomSQLiteQuery;
import androidx.room.util.DBUtil;
import androidx.sqlite.db.SupportSQLiteStatement;
import java.lang.Class;
import java.lang.Exception;
import java.lang.Long;
import java.lang.Object;
import java.lang.Override;
import java.lang.String;
import java.lang.SuppressWarnings;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.concurrent.Callable;
import javax.annotation.processing.Generated;
import kotlin.Unit;
import kotlin.coroutines.Continuation;

@Generated("androidx.room.RoomProcessor")
@SuppressWarnings({"unchecked", "deprecation"})
public final class LockHistoryDao_Impl implements LockHistoryDao {
  private final RoomDatabase __db;

  private final EntityInsertionAdapter<LockHistory> __insertionAdapterOfLockHistory;

  private final EntityDeletionOrUpdateAdapter<LockHistory> __deletionAdapterOfLockHistory;

  private final EntityDeletionOrUpdateAdapter<LockHistory> __updateAdapterOfLockHistory;

  public LockHistoryDao_Impl(@NonNull final RoomDatabase __db) {
    this.__db = __db;
    this.__insertionAdapterOfLockHistory = new EntityInsertionAdapter<LockHistory>(__db) {
      @Override
      @NonNull
      protected String createQuery() {
        return "INSERT OR IGNORE INTO `LockHistory` (`id`,`title`,`content`,`startTime`,`trueStartTime`,`timeLength`,`trueTimeLength`,`lockType`,`simpleLockLength`,`tomatoIndexId`,`scheduleIndexId`,`isFinish`,`isForceQuit`,`isSynced`,`isGeneratedCard`,`deleteWhiteAppTemp`) VALUES (nullif(?, 0),?,?,?,?,?,?,?,?,?,?,?,?,?,?,?)";
      }

      @Override
      protected void bind(@NonNull final SupportSQLiteStatement statement,
          @NonNull final LockHistory entity) {
        statement.bindLong(1, entity.getId());
        if (entity.getTitle() == null) {
          statement.bindNull(2);
        } else {
          statement.bindString(2, entity.getTitle());
        }
        if (entity.getContent() == null) {
          statement.bindNull(3);
        } else {
          statement.bindString(3, entity.getContent());
        }
        statement.bindLong(4, entity.getStartTime());
        statement.bindLong(5, entity.getTrueStartTime());
        statement.bindLong(6, entity.getTimeLength());
        statement.bindLong(7, entity.getTrueTimeLength());
        statement.bindLong(8, entity.getLockType());
        statement.bindLong(9, entity.getSimpleLockLength());
        if (entity.getTomatoIndexId() == null) {
          statement.bindNull(10);
        } else {
          statement.bindString(10, entity.getTomatoIndexId());
        }
        if (entity.getScheduleIndexId() == null) {
          statement.bindNull(11);
        } else {
          statement.bindString(11, entity.getScheduleIndexId());
        }
        final int _tmp = entity.isFinish() ? 1 : 0;
        statement.bindLong(12, _tmp);
        final int _tmp_1 = entity.isForceQuit() ? 1 : 0;
        statement.bindLong(13, _tmp_1);
        final int _tmp_2 = entity.isSynced() ? 1 : 0;
        statement.bindLong(14, _tmp_2);
        final int _tmp_3 = entity.isGeneratedCard() ? 1 : 0;
        statement.bindLong(15, _tmp_3);
        if (entity.getDeleteWhiteAppTemp() == null) {
          statement.bindNull(16);
        } else {
          statement.bindString(16, entity.getDeleteWhiteAppTemp());
        }
      }
    };
    this.__deletionAdapterOfLockHistory = new EntityDeletionOrUpdateAdapter<LockHistory>(__db) {
      @Override
      @NonNull
      protected String createQuery() {
        return "DELETE FROM `LockHistory` WHERE `id` = ?";
      }

      @Override
      protected void bind(@NonNull final SupportSQLiteStatement statement,
          @NonNull final LockHistory entity) {
        statement.bindLong(1, entity.getId());
      }
    };
    this.__updateAdapterOfLockHistory = new EntityDeletionOrUpdateAdapter<LockHistory>(__db) {
      @Override
      @NonNull
      protected String createQuery() {
        return "UPDATE OR ABORT `LockHistory` SET `id` = ?,`title` = ?,`content` = ?,`startTime` = ?,`trueStartTime` = ?,`timeLength` = ?,`trueTimeLength` = ?,`lockType` = ?,`simpleLockLength` = ?,`tomatoIndexId` = ?,`scheduleIndexId` = ?,`isFinish` = ?,`isForceQuit` = ?,`isSynced` = ?,`isGeneratedCard` = ?,`deleteWhiteAppTemp` = ? WHERE `id` = ?";
      }

      @Override
      protected void bind(@NonNull final SupportSQLiteStatement statement,
          @NonNull final LockHistory entity) {
        statement.bindLong(1, entity.getId());
        if (entity.getTitle() == null) {
          statement.bindNull(2);
        } else {
          statement.bindString(2, entity.getTitle());
        }
        if (entity.getContent() == null) {
          statement.bindNull(3);
        } else {
          statement.bindString(3, entity.getContent());
        }
        statement.bindLong(4, entity.getStartTime());
        statement.bindLong(5, entity.getTrueStartTime());
        statement.bindLong(6, entity.getTimeLength());
        statement.bindLong(7, entity.getTrueTimeLength());
        statement.bindLong(8, entity.getLockType());
        statement.bindLong(9, entity.getSimpleLockLength());
        if (entity.getTomatoIndexId() == null) {
          statement.bindNull(10);
        } else {
          statement.bindString(10, entity.getTomatoIndexId());
        }
        if (entity.getScheduleIndexId() == null) {
          statement.bindNull(11);
        } else {
          statement.bindString(11, entity.getScheduleIndexId());
        }
        final int _tmp = entity.isFinish() ? 1 : 0;
        statement.bindLong(12, _tmp);
        final int _tmp_1 = entity.isForceQuit() ? 1 : 0;
        statement.bindLong(13, _tmp_1);
        final int _tmp_2 = entity.isSynced() ? 1 : 0;
        statement.bindLong(14, _tmp_2);
        final int _tmp_3 = entity.isGeneratedCard() ? 1 : 0;
        statement.bindLong(15, _tmp_3);
        if (entity.getDeleteWhiteAppTemp() == null) {
          statement.bindNull(16);
        } else {
          statement.bindString(16, entity.getDeleteWhiteAppTemp());
        }
        statement.bindLong(17, entity.getId());
      }
    };
  }

  @Override
  public Object insertLockHistory(final LockHistory lockHistory,
      final Continuation<? super Long> $completion) {
    return CoroutinesRoom.execute(__db, true, new Callable<Long>() {
      @Override
      @NonNull
      public Long call() throws Exception {
        __db.beginTransaction();
        try {
          final Long _result = __insertionAdapterOfLockHistory.insertAndReturnId(lockHistory);
          __db.setTransactionSuccessful();
          return _result;
        } finally {
          __db.endTransaction();
        }
      }
    }, $completion);
  }

  @Override
  public Object deleteLockHistory(final LockHistory lockHistory,
      final Continuation<? super Unit> $completion) {
    return CoroutinesRoom.execute(__db, true, new Callable<Unit>() {
      @Override
      @NonNull
      public Unit call() throws Exception {
        __db.beginTransaction();
        try {
          __deletionAdapterOfLockHistory.handle(lockHistory);
          __db.setTransactionSuccessful();
          return Unit.INSTANCE;
        } finally {
          __db.endTransaction();
        }
      }
    }, $completion);
  }

  @Override
  public Object updateLockHistory(final LockHistory lockHistory,
      final Continuation<? super Unit> $completion) {
    return CoroutinesRoom.execute(__db, true, new Callable<Unit>() {
      @Override
      @NonNull
      public Unit call() throws Exception {
        __db.beginTransaction();
        try {
          __updateAdapterOfLockHistory.handle(lockHistory);
          __db.setTransactionSuccessful();
          return Unit.INSTANCE;
        } finally {
          __db.endTransaction();
        }
      }
    }, $completion);
  }

  @Override
  public Object getUnUploadHistory(final Continuation<? super List<LockHistory>> $completion) {
    final String _sql = "select `LockHistory`.`id` AS `id`, `LockHistory`.`title` AS `title`, `LockHistory`.`content` AS `content`, `LockHistory`.`startTime` AS `startTime`, `LockHistory`.`trueStartTime` AS `trueStartTime`, `LockHistory`.`timeLength` AS `timeLength`, `LockHistory`.`trueTimeLength` AS `trueTimeLength`, `LockHistory`.`lockType` AS `lockType`, `LockHistory`.`simpleLockLength` AS `simpleLockLength`, `LockHistory`.`tomatoIndexId` AS `tomatoIndexId`, `LockHistory`.`scheduleIndexId` AS `scheduleIndexId`, `LockHistory`.`isFinish` AS `isFinish`, `LockHistory`.`isForceQuit` AS `isForceQuit`, `LockHistory`.`isSynced` AS `isSynced`, `LockHistory`.`isGeneratedCard` AS `isGeneratedCard`, `LockHistory`.`deleteWhiteAppTemp` AS `deleteWhiteAppTemp` From LockHistory where isFinish = 1 and isSynced = 0 order by id";
    final RoomSQLiteQuery _statement = RoomSQLiteQuery.acquire(_sql, 0);
    final CancellationSignal _cancellationSignal = DBUtil.createCancellationSignal();
    return CoroutinesRoom.execute(__db, false, _cancellationSignal, new Callable<List<LockHistory>>() {
      @Override
      @NonNull
      public List<LockHistory> call() throws Exception {
        final Cursor _cursor = DBUtil.query(__db, _statement, false, null);
        try {
          final int _cursorIndexOfId = 0;
          final int _cursorIndexOfTitle = 1;
          final int _cursorIndexOfContent = 2;
          final int _cursorIndexOfStartTime = 3;
          final int _cursorIndexOfTrueStartTime = 4;
          final int _cursorIndexOfTimeLength = 5;
          final int _cursorIndexOfTrueTimeLength = 6;
          final int _cursorIndexOfLockType = 7;
          final int _cursorIndexOfSimpleLockLength = 8;
          final int _cursorIndexOfTomatoIndexId = 9;
          final int _cursorIndexOfScheduleIndexId = 10;
          final int _cursorIndexOfIsFinish = 11;
          final int _cursorIndexOfIsForceQuit = 12;
          final int _cursorIndexOfIsSynced = 13;
          final int _cursorIndexOfIsGeneratedCard = 14;
          final int _cursorIndexOfDeleteWhiteAppTemp = 15;
          final List<LockHistory> _result = new ArrayList<LockHistory>(_cursor.getCount());
          while (_cursor.moveToNext()) {
            final LockHistory _item;
            final long _tmpId;
            _tmpId = _cursor.getLong(_cursorIndexOfId);
            final String _tmpTitle;
            if (_cursor.isNull(_cursorIndexOfTitle)) {
              _tmpTitle = null;
            } else {
              _tmpTitle = _cursor.getString(_cursorIndexOfTitle);
            }
            final String _tmpContent;
            if (_cursor.isNull(_cursorIndexOfContent)) {
              _tmpContent = null;
            } else {
              _tmpContent = _cursor.getString(_cursorIndexOfContent);
            }
            final long _tmpStartTime;
            _tmpStartTime = _cursor.getLong(_cursorIndexOfStartTime);
            final long _tmpTrueStartTime;
            _tmpTrueStartTime = _cursor.getLong(_cursorIndexOfTrueStartTime);
            final long _tmpTimeLength;
            _tmpTimeLength = _cursor.getLong(_cursorIndexOfTimeLength);
            final long _tmpTrueTimeLength;
            _tmpTrueTimeLength = _cursor.getLong(_cursorIndexOfTrueTimeLength);
            final int _tmpLockType;
            _tmpLockType = _cursor.getInt(_cursorIndexOfLockType);
            final int _tmpSimpleLockLength;
            _tmpSimpleLockLength = _cursor.getInt(_cursorIndexOfSimpleLockLength);
            final String _tmpTomatoIndexId;
            if (_cursor.isNull(_cursorIndexOfTomatoIndexId)) {
              _tmpTomatoIndexId = null;
            } else {
              _tmpTomatoIndexId = _cursor.getString(_cursorIndexOfTomatoIndexId);
            }
            final String _tmpScheduleIndexId;
            if (_cursor.isNull(_cursorIndexOfScheduleIndexId)) {
              _tmpScheduleIndexId = null;
            } else {
              _tmpScheduleIndexId = _cursor.getString(_cursorIndexOfScheduleIndexId);
            }
            final boolean _tmpIsFinish;
            final int _tmp;
            _tmp = _cursor.getInt(_cursorIndexOfIsFinish);
            _tmpIsFinish = _tmp != 0;
            final boolean _tmpIsForceQuit;
            final int _tmp_1;
            _tmp_1 = _cursor.getInt(_cursorIndexOfIsForceQuit);
            _tmpIsForceQuit = _tmp_1 != 0;
            final boolean _tmpIsSynced;
            final int _tmp_2;
            _tmp_2 = _cursor.getInt(_cursorIndexOfIsSynced);
            _tmpIsSynced = _tmp_2 != 0;
            final boolean _tmpIsGeneratedCard;
            final int _tmp_3;
            _tmp_3 = _cursor.getInt(_cursorIndexOfIsGeneratedCard);
            _tmpIsGeneratedCard = _tmp_3 != 0;
            final String _tmpDeleteWhiteAppTemp;
            if (_cursor.isNull(_cursorIndexOfDeleteWhiteAppTemp)) {
              _tmpDeleteWhiteAppTemp = null;
            } else {
              _tmpDeleteWhiteAppTemp = _cursor.getString(_cursorIndexOfDeleteWhiteAppTemp);
            }
            _item = new LockHistory(_tmpId,_tmpTitle,_tmpContent,_tmpStartTime,_tmpTrueStartTime,_tmpTimeLength,_tmpTrueTimeLength,_tmpLockType,_tmpSimpleLockLength,_tmpTomatoIndexId,_tmpScheduleIndexId,_tmpIsFinish,_tmpIsForceQuit,_tmpIsSynced,_tmpIsGeneratedCard,_tmpDeleteWhiteAppTemp);
            _result.add(_item);
          }
          return _result;
        } finally {
          _cursor.close();
          _statement.release();
        }
      }
    }, $completion);
  }

  @Override
  public LiveData<LockHistory> getUnfinishedLockHistory() {
    final String _sql = "select `LockHistory`.`id` AS `id`, `LockHistory`.`title` AS `title`, `LockHistory`.`content` AS `content`, `LockHistory`.`startTime` AS `startTime`, `LockHistory`.`trueStartTime` AS `trueStartTime`, `LockHistory`.`timeLength` AS `timeLength`, `LockHistory`.`trueTimeLength` AS `trueTimeLength`, `LockHistory`.`lockType` AS `lockType`, `LockHistory`.`simpleLockLength` AS `simpleLockLength`, `LockHistory`.`tomatoIndexId` AS `tomatoIndexId`, `LockHistory`.`scheduleIndexId` AS `scheduleIndexId`, `LockHistory`.`isFinish` AS `isFinish`, `LockHistory`.`isForceQuit` AS `isForceQuit`, `LockHistory`.`isSynced` AS `isSynced`, `LockHistory`.`isGeneratedCard` AS `isGeneratedCard`, `LockHistory`.`deleteWhiteAppTemp` AS `deleteWhiteAppTemp` From LockHistory where isFinish = 0 order by id desc";
    final RoomSQLiteQuery _statement = RoomSQLiteQuery.acquire(_sql, 0);
    return __db.getInvalidationTracker().createLiveData(new String[] {"LockHistory"}, false, new Callable<LockHistory>() {
      @Override
      @Nullable
      public LockHistory call() throws Exception {
        final Cursor _cursor = DBUtil.query(__db, _statement, false, null);
        try {
          final int _cursorIndexOfId = 0;
          final int _cursorIndexOfTitle = 1;
          final int _cursorIndexOfContent = 2;
          final int _cursorIndexOfStartTime = 3;
          final int _cursorIndexOfTrueStartTime = 4;
          final int _cursorIndexOfTimeLength = 5;
          final int _cursorIndexOfTrueTimeLength = 6;
          final int _cursorIndexOfLockType = 7;
          final int _cursorIndexOfSimpleLockLength = 8;
          final int _cursorIndexOfTomatoIndexId = 9;
          final int _cursorIndexOfScheduleIndexId = 10;
          final int _cursorIndexOfIsFinish = 11;
          final int _cursorIndexOfIsForceQuit = 12;
          final int _cursorIndexOfIsSynced = 13;
          final int _cursorIndexOfIsGeneratedCard = 14;
          final int _cursorIndexOfDeleteWhiteAppTemp = 15;
          final LockHistory _result;
          if (_cursor.moveToFirst()) {
            final long _tmpId;
            _tmpId = _cursor.getLong(_cursorIndexOfId);
            final String _tmpTitle;
            if (_cursor.isNull(_cursorIndexOfTitle)) {
              _tmpTitle = null;
            } else {
              _tmpTitle = _cursor.getString(_cursorIndexOfTitle);
            }
            final String _tmpContent;
            if (_cursor.isNull(_cursorIndexOfContent)) {
              _tmpContent = null;
            } else {
              _tmpContent = _cursor.getString(_cursorIndexOfContent);
            }
            final long _tmpStartTime;
            _tmpStartTime = _cursor.getLong(_cursorIndexOfStartTime);
            final long _tmpTrueStartTime;
            _tmpTrueStartTime = _cursor.getLong(_cursorIndexOfTrueStartTime);
            final long _tmpTimeLength;
            _tmpTimeLength = _cursor.getLong(_cursorIndexOfTimeLength);
            final long _tmpTrueTimeLength;
            _tmpTrueTimeLength = _cursor.getLong(_cursorIndexOfTrueTimeLength);
            final int _tmpLockType;
            _tmpLockType = _cursor.getInt(_cursorIndexOfLockType);
            final int _tmpSimpleLockLength;
            _tmpSimpleLockLength = _cursor.getInt(_cursorIndexOfSimpleLockLength);
            final String _tmpTomatoIndexId;
            if (_cursor.isNull(_cursorIndexOfTomatoIndexId)) {
              _tmpTomatoIndexId = null;
            } else {
              _tmpTomatoIndexId = _cursor.getString(_cursorIndexOfTomatoIndexId);
            }
            final String _tmpScheduleIndexId;
            if (_cursor.isNull(_cursorIndexOfScheduleIndexId)) {
              _tmpScheduleIndexId = null;
            } else {
              _tmpScheduleIndexId = _cursor.getString(_cursorIndexOfScheduleIndexId);
            }
            final boolean _tmpIsFinish;
            final int _tmp;
            _tmp = _cursor.getInt(_cursorIndexOfIsFinish);
            _tmpIsFinish = _tmp != 0;
            final boolean _tmpIsForceQuit;
            final int _tmp_1;
            _tmp_1 = _cursor.getInt(_cursorIndexOfIsForceQuit);
            _tmpIsForceQuit = _tmp_1 != 0;
            final boolean _tmpIsSynced;
            final int _tmp_2;
            _tmp_2 = _cursor.getInt(_cursorIndexOfIsSynced);
            _tmpIsSynced = _tmp_2 != 0;
            final boolean _tmpIsGeneratedCard;
            final int _tmp_3;
            _tmp_3 = _cursor.getInt(_cursorIndexOfIsGeneratedCard);
            _tmpIsGeneratedCard = _tmp_3 != 0;
            final String _tmpDeleteWhiteAppTemp;
            if (_cursor.isNull(_cursorIndexOfDeleteWhiteAppTemp)) {
              _tmpDeleteWhiteAppTemp = null;
            } else {
              _tmpDeleteWhiteAppTemp = _cursor.getString(_cursorIndexOfDeleteWhiteAppTemp);
            }
            _result = new LockHistory(_tmpId,_tmpTitle,_tmpContent,_tmpStartTime,_tmpTrueStartTime,_tmpTimeLength,_tmpTrueTimeLength,_tmpLockType,_tmpSimpleLockLength,_tmpTomatoIndexId,_tmpScheduleIndexId,_tmpIsFinish,_tmpIsForceQuit,_tmpIsSynced,_tmpIsGeneratedCard,_tmpDeleteWhiteAppTemp);
          } else {
            _result = null;
          }
          return _result;
        } finally {
          _cursor.close();
        }
      }

      @Override
      protected void finalize() {
        _statement.release();
      }
    });
  }

  @Override
  public Object getUnfinishedLastLockHistory(final Continuation<? super LockHistory> $completion) {
    final String _sql = "select `LockHistory`.`id` AS `id`, `LockHistory`.`title` AS `title`, `LockHistory`.`content` AS `content`, `LockHistory`.`startTime` AS `startTime`, `LockHistory`.`trueStartTime` AS `trueStartTime`, `LockHistory`.`timeLength` AS `timeLength`, `LockHistory`.`trueTimeLength` AS `trueTimeLength`, `LockHistory`.`lockType` AS `lockType`, `LockHistory`.`simpleLockLength` AS `simpleLockLength`, `LockHistory`.`tomatoIndexId` AS `tomatoIndexId`, `LockHistory`.`scheduleIndexId` AS `scheduleIndexId`, `LockHistory`.`isFinish` AS `isFinish`, `LockHistory`.`isForceQuit` AS `isForceQuit`, `LockHistory`.`isSynced` AS `isSynced`, `LockHistory`.`isGeneratedCard` AS `isGeneratedCard`, `LockHistory`.`deleteWhiteAppTemp` AS `deleteWhiteAppTemp` from LockHistory where isFinish = 0 order by id desc limit 1";
    final RoomSQLiteQuery _statement = RoomSQLiteQuery.acquire(_sql, 0);
    final CancellationSignal _cancellationSignal = DBUtil.createCancellationSignal();
    return CoroutinesRoom.execute(__db, false, _cancellationSignal, new Callable<LockHistory>() {
      @Override
      @Nullable
      public LockHistory call() throws Exception {
        final Cursor _cursor = DBUtil.query(__db, _statement, false, null);
        try {
          final int _cursorIndexOfId = 0;
          final int _cursorIndexOfTitle = 1;
          final int _cursorIndexOfContent = 2;
          final int _cursorIndexOfStartTime = 3;
          final int _cursorIndexOfTrueStartTime = 4;
          final int _cursorIndexOfTimeLength = 5;
          final int _cursorIndexOfTrueTimeLength = 6;
          final int _cursorIndexOfLockType = 7;
          final int _cursorIndexOfSimpleLockLength = 8;
          final int _cursorIndexOfTomatoIndexId = 9;
          final int _cursorIndexOfScheduleIndexId = 10;
          final int _cursorIndexOfIsFinish = 11;
          final int _cursorIndexOfIsForceQuit = 12;
          final int _cursorIndexOfIsSynced = 13;
          final int _cursorIndexOfIsGeneratedCard = 14;
          final int _cursorIndexOfDeleteWhiteAppTemp = 15;
          final LockHistory _result;
          if (_cursor.moveToFirst()) {
            final long _tmpId;
            _tmpId = _cursor.getLong(_cursorIndexOfId);
            final String _tmpTitle;
            if (_cursor.isNull(_cursorIndexOfTitle)) {
              _tmpTitle = null;
            } else {
              _tmpTitle = _cursor.getString(_cursorIndexOfTitle);
            }
            final String _tmpContent;
            if (_cursor.isNull(_cursorIndexOfContent)) {
              _tmpContent = null;
            } else {
              _tmpContent = _cursor.getString(_cursorIndexOfContent);
            }
            final long _tmpStartTime;
            _tmpStartTime = _cursor.getLong(_cursorIndexOfStartTime);
            final long _tmpTrueStartTime;
            _tmpTrueStartTime = _cursor.getLong(_cursorIndexOfTrueStartTime);
            final long _tmpTimeLength;
            _tmpTimeLength = _cursor.getLong(_cursorIndexOfTimeLength);
            final long _tmpTrueTimeLength;
            _tmpTrueTimeLength = _cursor.getLong(_cursorIndexOfTrueTimeLength);
            final int _tmpLockType;
            _tmpLockType = _cursor.getInt(_cursorIndexOfLockType);
            final int _tmpSimpleLockLength;
            _tmpSimpleLockLength = _cursor.getInt(_cursorIndexOfSimpleLockLength);
            final String _tmpTomatoIndexId;
            if (_cursor.isNull(_cursorIndexOfTomatoIndexId)) {
              _tmpTomatoIndexId = null;
            } else {
              _tmpTomatoIndexId = _cursor.getString(_cursorIndexOfTomatoIndexId);
            }
            final String _tmpScheduleIndexId;
            if (_cursor.isNull(_cursorIndexOfScheduleIndexId)) {
              _tmpScheduleIndexId = null;
            } else {
              _tmpScheduleIndexId = _cursor.getString(_cursorIndexOfScheduleIndexId);
            }
            final boolean _tmpIsFinish;
            final int _tmp;
            _tmp = _cursor.getInt(_cursorIndexOfIsFinish);
            _tmpIsFinish = _tmp != 0;
            final boolean _tmpIsForceQuit;
            final int _tmp_1;
            _tmp_1 = _cursor.getInt(_cursorIndexOfIsForceQuit);
            _tmpIsForceQuit = _tmp_1 != 0;
            final boolean _tmpIsSynced;
            final int _tmp_2;
            _tmp_2 = _cursor.getInt(_cursorIndexOfIsSynced);
            _tmpIsSynced = _tmp_2 != 0;
            final boolean _tmpIsGeneratedCard;
            final int _tmp_3;
            _tmp_3 = _cursor.getInt(_cursorIndexOfIsGeneratedCard);
            _tmpIsGeneratedCard = _tmp_3 != 0;
            final String _tmpDeleteWhiteAppTemp;
            if (_cursor.isNull(_cursorIndexOfDeleteWhiteAppTemp)) {
              _tmpDeleteWhiteAppTemp = null;
            } else {
              _tmpDeleteWhiteAppTemp = _cursor.getString(_cursorIndexOfDeleteWhiteAppTemp);
            }
            _result = new LockHistory(_tmpId,_tmpTitle,_tmpContent,_tmpStartTime,_tmpTrueStartTime,_tmpTimeLength,_tmpTrueTimeLength,_tmpLockType,_tmpSimpleLockLength,_tmpTomatoIndexId,_tmpScheduleIndexId,_tmpIsFinish,_tmpIsForceQuit,_tmpIsSynced,_tmpIsGeneratedCard,_tmpDeleteWhiteAppTemp);
          } else {
            _result = null;
          }
          return _result;
        } finally {
          _cursor.close();
          _statement.release();
        }
      }
    }, $completion);
  }

  @NonNull
  public static List<Class<?>> getRequiredConverters() {
    return Collections.emptyList();
  }
}
