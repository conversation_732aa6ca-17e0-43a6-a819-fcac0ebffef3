package com.lijianqiang12.silent.data.model.db;

import android.database.Cursor;
import android.os.CancellationSignal;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.lifecycle.LiveData;
import androidx.room.CoroutinesRoom;
import androidx.room.EntityDeletionOrUpdateAdapter;
import androidx.room.EntityInsertionAdapter;
import androidx.room.RoomDatabase;
import androidx.room.RoomSQLiteQuery;
import androidx.room.SharedSQLiteStatement;
import androidx.room.util.CursorUtil;
import androidx.room.util.DBUtil;
import androidx.sqlite.db.SupportSQLiteStatement;
import java.lang.Boolean;
import java.lang.Class;
import java.lang.Exception;
import java.lang.Integer;
import java.lang.Long;
import java.lang.Object;
import java.lang.Override;
import java.lang.String;
import java.lang.SuppressWarnings;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.concurrent.Callable;
import javax.annotation.processing.Generated;
import kotlin.Unit;
import kotlin.coroutines.Continuation;

@Generated("androidx.room.RoomProcessor")
@SuppressWarnings({"unchecked", "deprecation"})
public final class WhiteAppDao_Impl implements WhiteAppDao {
  private final RoomDatabase __db;

  private final EntityInsertionAdapter<WhiteApp> __insertionAdapterOfWhiteApp;

  private final EntityDeletionOrUpdateAdapter<WhiteApp> __deletionAdapterOfWhiteApp;

  private final EntityDeletionOrUpdateAdapter<WhiteApp> __updateAdapterOfWhiteApp;

  private final SharedSQLiteStatement __preparedStmtOfDeleteAll;

  private final SharedSQLiteStatement __preparedStmtOfUpdateUserId;

  public WhiteAppDao_Impl(@NonNull final RoomDatabase __db) {
    this.__db = __db;
    this.__insertionAdapterOfWhiteApp = new EntityInsertionAdapter<WhiteApp>(__db) {
      @Override
      @NonNull
      protected String createQuery() {
        return "INSERT OR IGNORE INTO `WhiteApp` (`id`,`userId`,`whiteAppIndexId`,`tomatoIndexId`,`scheduleIndexId`,`pkg`,`mainActivity`,`maxLen`,`trend`,`syncState`,`syncTime`,`uuid`,`version`) VALUES (nullif(?, 0),?,?,?,?,?,?,?,?,?,?,?,?)";
      }

      @Override
      protected void bind(@NonNull final SupportSQLiteStatement statement,
          @NonNull final WhiteApp entity) {
        statement.bindLong(1, entity.getId());
        statement.bindLong(2, entity.getUserId());
        if (entity.getWhiteAppIndexId() == null) {
          statement.bindNull(3);
        } else {
          statement.bindString(3, entity.getWhiteAppIndexId());
        }
        if (entity.getTomatoIndexId() == null) {
          statement.bindNull(4);
        } else {
          statement.bindString(4, entity.getTomatoIndexId());
        }
        if (entity.getScheduleIndexId() == null) {
          statement.bindNull(5);
        } else {
          statement.bindString(5, entity.getScheduleIndexId());
        }
        if (entity.getPkg() == null) {
          statement.bindNull(6);
        } else {
          statement.bindString(6, entity.getPkg());
        }
        if (entity.getMainActivity() == null) {
          statement.bindNull(7);
        } else {
          statement.bindString(7, entity.getMainActivity());
        }
        statement.bindLong(8, entity.getMaxLen());
        statement.bindLong(9, entity.getTrend());
        statement.bindLong(10, entity.getSyncState());
        statement.bindLong(11, entity.getSyncTime());
        statement.bindLong(12, entity.getUuid());
        statement.bindLong(13, entity.getVersion());
      }
    };
    this.__deletionAdapterOfWhiteApp = new EntityDeletionOrUpdateAdapter<WhiteApp>(__db) {
      @Override
      @NonNull
      protected String createQuery() {
        return "DELETE FROM `WhiteApp` WHERE `id` = ?";
      }

      @Override
      protected void bind(@NonNull final SupportSQLiteStatement statement,
          @NonNull final WhiteApp entity) {
        statement.bindLong(1, entity.getId());
      }
    };
    this.__updateAdapterOfWhiteApp = new EntityDeletionOrUpdateAdapter<WhiteApp>(__db) {
      @Override
      @NonNull
      protected String createQuery() {
        return "UPDATE OR ABORT `WhiteApp` SET `id` = ?,`userId` = ?,`whiteAppIndexId` = ?,`tomatoIndexId` = ?,`scheduleIndexId` = ?,`pkg` = ?,`mainActivity` = ?,`maxLen` = ?,`trend` = ?,`syncState` = ?,`syncTime` = ?,`uuid` = ?,`version` = ? WHERE `id` = ?";
      }

      @Override
      protected void bind(@NonNull final SupportSQLiteStatement statement,
          @NonNull final WhiteApp entity) {
        statement.bindLong(1, entity.getId());
        statement.bindLong(2, entity.getUserId());
        if (entity.getWhiteAppIndexId() == null) {
          statement.bindNull(3);
        } else {
          statement.bindString(3, entity.getWhiteAppIndexId());
        }
        if (entity.getTomatoIndexId() == null) {
          statement.bindNull(4);
        } else {
          statement.bindString(4, entity.getTomatoIndexId());
        }
        if (entity.getScheduleIndexId() == null) {
          statement.bindNull(5);
        } else {
          statement.bindString(5, entity.getScheduleIndexId());
        }
        if (entity.getPkg() == null) {
          statement.bindNull(6);
        } else {
          statement.bindString(6, entity.getPkg());
        }
        if (entity.getMainActivity() == null) {
          statement.bindNull(7);
        } else {
          statement.bindString(7, entity.getMainActivity());
        }
        statement.bindLong(8, entity.getMaxLen());
        statement.bindLong(9, entity.getTrend());
        statement.bindLong(10, entity.getSyncState());
        statement.bindLong(11, entity.getSyncTime());
        statement.bindLong(12, entity.getUuid());
        statement.bindLong(13, entity.getVersion());
        statement.bindLong(14, entity.getId());
      }
    };
    this.__preparedStmtOfDeleteAll = new SharedSQLiteStatement(__db) {
      @Override
      @NonNull
      public String createQuery() {
        final String _query = "delete from WhiteApp where userId = ?";
        return _query;
      }
    };
    this.__preparedStmtOfUpdateUserId = new SharedSQLiteStatement(__db) {
      @Override
      @NonNull
      public String createQuery() {
        final String _query = "UPDATE WhiteApp SET userId = ? WHERE userId = -1";
        return _query;
      }
    };
  }

  @Override
  public long insertWhiteApp(final WhiteApp whiteApp) {
    __db.assertNotSuspendingTransaction();
    __db.beginTransaction();
    try {
      final long _result = __insertionAdapterOfWhiteApp.insertAndReturnId(whiteApp);
      __db.setTransactionSuccessful();
      return _result;
    } finally {
      __db.endTransaction();
    }
  }

  @Override
  public long insertWhiteAppImmediate(final WhiteApp whiteApp) {
    __db.assertNotSuspendingTransaction();
    __db.beginTransaction();
    try {
      final long _result = __insertionAdapterOfWhiteApp.insertAndReturnId(whiteApp);
      __db.setTransactionSuccessful();
      return _result;
    } finally {
      __db.endTransaction();
    }
  }

  @Override
  public List<Long> insertWhiteApps(final List<WhiteApp> whiteApps) {
    __db.assertNotSuspendingTransaction();
    __db.beginTransaction();
    try {
      final List<Long> _result = __insertionAdapterOfWhiteApp.insertAndReturnIdsList(whiteApps);
      __db.setTransactionSuccessful();
      return _result;
    } finally {
      __db.endTransaction();
    }
  }

  @Override
  public Object deleteWhiteApp(final WhiteApp whiteApp,
      final Continuation<? super Unit> $completion) {
    return CoroutinesRoom.execute(__db, true, new Callable<Unit>() {
      @Override
      @NonNull
      public Unit call() throws Exception {
        __db.beginTransaction();
        try {
          __deletionAdapterOfWhiteApp.handle(whiteApp);
          __db.setTransactionSuccessful();
          return Unit.INSTANCE;
        } finally {
          __db.endTransaction();
        }
      }
    }, $completion);
  }

  @Override
  public Object deleteWhiteApps(final List<WhiteApp> whiteApps,
      final Continuation<? super Unit> $completion) {
    return CoroutinesRoom.execute(__db, true, new Callable<Unit>() {
      @Override
      @NonNull
      public Unit call() throws Exception {
        __db.beginTransaction();
        try {
          __deletionAdapterOfWhiteApp.handleMultiple(whiteApps);
          __db.setTransactionSuccessful();
          return Unit.INSTANCE;
        } finally {
          __db.endTransaction();
        }
      }
    }, $completion);
  }

  @Override
  public Object updateWhiteApp(final WhiteApp whiteApp,
      final Continuation<? super Unit> $completion) {
    return CoroutinesRoom.execute(__db, true, new Callable<Unit>() {
      @Override
      @NonNull
      public Unit call() throws Exception {
        __db.beginTransaction();
        try {
          __updateAdapterOfWhiteApp.handle(whiteApp);
          __db.setTransactionSuccessful();
          return Unit.INSTANCE;
        } finally {
          __db.endTransaction();
        }
      }
    }, $completion);
  }

  @Override
  public Object deleteAll(final int userId, final Continuation<? super Unit> $completion) {
    return CoroutinesRoom.execute(__db, true, new Callable<Unit>() {
      @Override
      @NonNull
      public Unit call() throws Exception {
        final SupportSQLiteStatement _stmt = __preparedStmtOfDeleteAll.acquire();
        int _argIndex = 1;
        _stmt.bindLong(_argIndex, userId);
        try {
          __db.beginTransaction();
          try {
            _stmt.executeUpdateDelete();
            __db.setTransactionSuccessful();
            return Unit.INSTANCE;
          } finally {
            __db.endTransaction();
          }
        } finally {
          __preparedStmtOfDeleteAll.release(_stmt);
        }
      }
    }, $completion);
  }

  @Override
  public void updateUserId(final int newUserId) {
    __db.assertNotSuspendingTransaction();
    final SupportSQLiteStatement _stmt = __preparedStmtOfUpdateUserId.acquire();
    int _argIndex = 1;
    _stmt.bindLong(_argIndex, newUserId);
    try {
      __db.beginTransaction();
      try {
        _stmt.executeUpdateDelete();
        __db.setTransactionSuccessful();
      } finally {
        __db.endTransaction();
      }
    } finally {
      __preparedStmtOfUpdateUserId.release(_stmt);
    }
  }

  @Override
  public Object getGlobalWhiteAppList(final int userId,
      final Continuation<? super List<WhiteApp>> $completion) {
    final String _sql = "select * From WhiteApp Where userId = ? and tomatoIndexId = '' and scheduleIndexId = '' and syncState>=0 order by trend";
    final RoomSQLiteQuery _statement = RoomSQLiteQuery.acquire(_sql, 1);
    int _argIndex = 1;
    _statement.bindLong(_argIndex, userId);
    final CancellationSignal _cancellationSignal = DBUtil.createCancellationSignal();
    return CoroutinesRoom.execute(__db, false, _cancellationSignal, new Callable<List<WhiteApp>>() {
      @Override
      @NonNull
      public List<WhiteApp> call() throws Exception {
        final Cursor _cursor = DBUtil.query(__db, _statement, false, null);
        try {
          final int _cursorIndexOfId = CursorUtil.getColumnIndexOrThrow(_cursor, "id");
          final int _cursorIndexOfUserId = CursorUtil.getColumnIndexOrThrow(_cursor, "userId");
          final int _cursorIndexOfWhiteAppIndexId = CursorUtil.getColumnIndexOrThrow(_cursor, "whiteAppIndexId");
          final int _cursorIndexOfTomatoIndexId = CursorUtil.getColumnIndexOrThrow(_cursor, "tomatoIndexId");
          final int _cursorIndexOfScheduleIndexId = CursorUtil.getColumnIndexOrThrow(_cursor, "scheduleIndexId");
          final int _cursorIndexOfPkg = CursorUtil.getColumnIndexOrThrow(_cursor, "pkg");
          final int _cursorIndexOfMainActivity = CursorUtil.getColumnIndexOrThrow(_cursor, "mainActivity");
          final int _cursorIndexOfMaxLen = CursorUtil.getColumnIndexOrThrow(_cursor, "maxLen");
          final int _cursorIndexOfTrend = CursorUtil.getColumnIndexOrThrow(_cursor, "trend");
          final int _cursorIndexOfSyncState = CursorUtil.getColumnIndexOrThrow(_cursor, "syncState");
          final int _cursorIndexOfSyncTime = CursorUtil.getColumnIndexOrThrow(_cursor, "syncTime");
          final int _cursorIndexOfUuid = CursorUtil.getColumnIndexOrThrow(_cursor, "uuid");
          final int _cursorIndexOfVersion = CursorUtil.getColumnIndexOrThrow(_cursor, "version");
          final List<WhiteApp> _result = new ArrayList<WhiteApp>(_cursor.getCount());
          while (_cursor.moveToNext()) {
            final WhiteApp _item;
            final long _tmpId;
            _tmpId = _cursor.getLong(_cursorIndexOfId);
            final int _tmpUserId;
            _tmpUserId = _cursor.getInt(_cursorIndexOfUserId);
            final String _tmpWhiteAppIndexId;
            if (_cursor.isNull(_cursorIndexOfWhiteAppIndexId)) {
              _tmpWhiteAppIndexId = null;
            } else {
              _tmpWhiteAppIndexId = _cursor.getString(_cursorIndexOfWhiteAppIndexId);
            }
            final String _tmpTomatoIndexId;
            if (_cursor.isNull(_cursorIndexOfTomatoIndexId)) {
              _tmpTomatoIndexId = null;
            } else {
              _tmpTomatoIndexId = _cursor.getString(_cursorIndexOfTomatoIndexId);
            }
            final String _tmpScheduleIndexId;
            if (_cursor.isNull(_cursorIndexOfScheduleIndexId)) {
              _tmpScheduleIndexId = null;
            } else {
              _tmpScheduleIndexId = _cursor.getString(_cursorIndexOfScheduleIndexId);
            }
            final String _tmpPkg;
            if (_cursor.isNull(_cursorIndexOfPkg)) {
              _tmpPkg = null;
            } else {
              _tmpPkg = _cursor.getString(_cursorIndexOfPkg);
            }
            final String _tmpMainActivity;
            if (_cursor.isNull(_cursorIndexOfMainActivity)) {
              _tmpMainActivity = null;
            } else {
              _tmpMainActivity = _cursor.getString(_cursorIndexOfMainActivity);
            }
            final int _tmpMaxLen;
            _tmpMaxLen = _cursor.getInt(_cursorIndexOfMaxLen);
            final int _tmpTrend;
            _tmpTrend = _cursor.getInt(_cursorIndexOfTrend);
            final int _tmpSyncState;
            _tmpSyncState = _cursor.getInt(_cursorIndexOfSyncState);
            final long _tmpSyncTime;
            _tmpSyncTime = _cursor.getLong(_cursorIndexOfSyncTime);
            final long _tmpUuid;
            _tmpUuid = _cursor.getLong(_cursorIndexOfUuid);
            final int _tmpVersion;
            _tmpVersion = _cursor.getInt(_cursorIndexOfVersion);
            _item = new WhiteApp(_tmpId,_tmpUserId,_tmpWhiteAppIndexId,_tmpTomatoIndexId,_tmpScheduleIndexId,_tmpPkg,_tmpMainActivity,_tmpMaxLen,_tmpTrend,_tmpSyncState,_tmpSyncTime,_tmpUuid,_tmpVersion);
            _result.add(_item);
          }
          return _result;
        } finally {
          _cursor.close();
          _statement.release();
        }
      }
    }, $completion);
  }

  @Override
  public Object getAllWhiteAppList(final int userId,
      final Continuation<? super List<WhiteApp>> $completion) {
    final String _sql = "select * From WhiteApp Where userId = ? order by trend";
    final RoomSQLiteQuery _statement = RoomSQLiteQuery.acquire(_sql, 1);
    int _argIndex = 1;
    _statement.bindLong(_argIndex, userId);
    final CancellationSignal _cancellationSignal = DBUtil.createCancellationSignal();
    return CoroutinesRoom.execute(__db, false, _cancellationSignal, new Callable<List<WhiteApp>>() {
      @Override
      @NonNull
      public List<WhiteApp> call() throws Exception {
        final Cursor _cursor = DBUtil.query(__db, _statement, false, null);
        try {
          final int _cursorIndexOfId = CursorUtil.getColumnIndexOrThrow(_cursor, "id");
          final int _cursorIndexOfUserId = CursorUtil.getColumnIndexOrThrow(_cursor, "userId");
          final int _cursorIndexOfWhiteAppIndexId = CursorUtil.getColumnIndexOrThrow(_cursor, "whiteAppIndexId");
          final int _cursorIndexOfTomatoIndexId = CursorUtil.getColumnIndexOrThrow(_cursor, "tomatoIndexId");
          final int _cursorIndexOfScheduleIndexId = CursorUtil.getColumnIndexOrThrow(_cursor, "scheduleIndexId");
          final int _cursorIndexOfPkg = CursorUtil.getColumnIndexOrThrow(_cursor, "pkg");
          final int _cursorIndexOfMainActivity = CursorUtil.getColumnIndexOrThrow(_cursor, "mainActivity");
          final int _cursorIndexOfMaxLen = CursorUtil.getColumnIndexOrThrow(_cursor, "maxLen");
          final int _cursorIndexOfTrend = CursorUtil.getColumnIndexOrThrow(_cursor, "trend");
          final int _cursorIndexOfSyncState = CursorUtil.getColumnIndexOrThrow(_cursor, "syncState");
          final int _cursorIndexOfSyncTime = CursorUtil.getColumnIndexOrThrow(_cursor, "syncTime");
          final int _cursorIndexOfUuid = CursorUtil.getColumnIndexOrThrow(_cursor, "uuid");
          final int _cursorIndexOfVersion = CursorUtil.getColumnIndexOrThrow(_cursor, "version");
          final List<WhiteApp> _result = new ArrayList<WhiteApp>(_cursor.getCount());
          while (_cursor.moveToNext()) {
            final WhiteApp _item;
            final long _tmpId;
            _tmpId = _cursor.getLong(_cursorIndexOfId);
            final int _tmpUserId;
            _tmpUserId = _cursor.getInt(_cursorIndexOfUserId);
            final String _tmpWhiteAppIndexId;
            if (_cursor.isNull(_cursorIndexOfWhiteAppIndexId)) {
              _tmpWhiteAppIndexId = null;
            } else {
              _tmpWhiteAppIndexId = _cursor.getString(_cursorIndexOfWhiteAppIndexId);
            }
            final String _tmpTomatoIndexId;
            if (_cursor.isNull(_cursorIndexOfTomatoIndexId)) {
              _tmpTomatoIndexId = null;
            } else {
              _tmpTomatoIndexId = _cursor.getString(_cursorIndexOfTomatoIndexId);
            }
            final String _tmpScheduleIndexId;
            if (_cursor.isNull(_cursorIndexOfScheduleIndexId)) {
              _tmpScheduleIndexId = null;
            } else {
              _tmpScheduleIndexId = _cursor.getString(_cursorIndexOfScheduleIndexId);
            }
            final String _tmpPkg;
            if (_cursor.isNull(_cursorIndexOfPkg)) {
              _tmpPkg = null;
            } else {
              _tmpPkg = _cursor.getString(_cursorIndexOfPkg);
            }
            final String _tmpMainActivity;
            if (_cursor.isNull(_cursorIndexOfMainActivity)) {
              _tmpMainActivity = null;
            } else {
              _tmpMainActivity = _cursor.getString(_cursorIndexOfMainActivity);
            }
            final int _tmpMaxLen;
            _tmpMaxLen = _cursor.getInt(_cursorIndexOfMaxLen);
            final int _tmpTrend;
            _tmpTrend = _cursor.getInt(_cursorIndexOfTrend);
            final int _tmpSyncState;
            _tmpSyncState = _cursor.getInt(_cursorIndexOfSyncState);
            final long _tmpSyncTime;
            _tmpSyncTime = _cursor.getLong(_cursorIndexOfSyncTime);
            final long _tmpUuid;
            _tmpUuid = _cursor.getLong(_cursorIndexOfUuid);
            final int _tmpVersion;
            _tmpVersion = _cursor.getInt(_cursorIndexOfVersion);
            _item = new WhiteApp(_tmpId,_tmpUserId,_tmpWhiteAppIndexId,_tmpTomatoIndexId,_tmpScheduleIndexId,_tmpPkg,_tmpMainActivity,_tmpMaxLen,_tmpTrend,_tmpSyncState,_tmpSyncTime,_tmpUuid,_tmpVersion);
            _result.add(_item);
          }
          return _result;
        } finally {
          _cursor.close();
          _statement.release();
        }
      }
    }, $completion);
  }

  @Override
  public LiveData<List<WhiteApp>> getGlobalWhiteApps(final int userId) {
    final String _sql = "select * From WhiteApp Where userId = ? and tomatoIndexId = '' and scheduleIndexId = '' and syncState>=0 order by trend";
    final RoomSQLiteQuery _statement = RoomSQLiteQuery.acquire(_sql, 1);
    int _argIndex = 1;
    _statement.bindLong(_argIndex, userId);
    return __db.getInvalidationTracker().createLiveData(new String[] {"WhiteApp"}, false, new Callable<List<WhiteApp>>() {
      @Override
      @Nullable
      public List<WhiteApp> call() throws Exception {
        final Cursor _cursor = DBUtil.query(__db, _statement, false, null);
        try {
          final int _cursorIndexOfId = CursorUtil.getColumnIndexOrThrow(_cursor, "id");
          final int _cursorIndexOfUserId = CursorUtil.getColumnIndexOrThrow(_cursor, "userId");
          final int _cursorIndexOfWhiteAppIndexId = CursorUtil.getColumnIndexOrThrow(_cursor, "whiteAppIndexId");
          final int _cursorIndexOfTomatoIndexId = CursorUtil.getColumnIndexOrThrow(_cursor, "tomatoIndexId");
          final int _cursorIndexOfScheduleIndexId = CursorUtil.getColumnIndexOrThrow(_cursor, "scheduleIndexId");
          final int _cursorIndexOfPkg = CursorUtil.getColumnIndexOrThrow(_cursor, "pkg");
          final int _cursorIndexOfMainActivity = CursorUtil.getColumnIndexOrThrow(_cursor, "mainActivity");
          final int _cursorIndexOfMaxLen = CursorUtil.getColumnIndexOrThrow(_cursor, "maxLen");
          final int _cursorIndexOfTrend = CursorUtil.getColumnIndexOrThrow(_cursor, "trend");
          final int _cursorIndexOfSyncState = CursorUtil.getColumnIndexOrThrow(_cursor, "syncState");
          final int _cursorIndexOfSyncTime = CursorUtil.getColumnIndexOrThrow(_cursor, "syncTime");
          final int _cursorIndexOfUuid = CursorUtil.getColumnIndexOrThrow(_cursor, "uuid");
          final int _cursorIndexOfVersion = CursorUtil.getColumnIndexOrThrow(_cursor, "version");
          final List<WhiteApp> _result = new ArrayList<WhiteApp>(_cursor.getCount());
          while (_cursor.moveToNext()) {
            final WhiteApp _item;
            final long _tmpId;
            _tmpId = _cursor.getLong(_cursorIndexOfId);
            final int _tmpUserId;
            _tmpUserId = _cursor.getInt(_cursorIndexOfUserId);
            final String _tmpWhiteAppIndexId;
            if (_cursor.isNull(_cursorIndexOfWhiteAppIndexId)) {
              _tmpWhiteAppIndexId = null;
            } else {
              _tmpWhiteAppIndexId = _cursor.getString(_cursorIndexOfWhiteAppIndexId);
            }
            final String _tmpTomatoIndexId;
            if (_cursor.isNull(_cursorIndexOfTomatoIndexId)) {
              _tmpTomatoIndexId = null;
            } else {
              _tmpTomatoIndexId = _cursor.getString(_cursorIndexOfTomatoIndexId);
            }
            final String _tmpScheduleIndexId;
            if (_cursor.isNull(_cursorIndexOfScheduleIndexId)) {
              _tmpScheduleIndexId = null;
            } else {
              _tmpScheduleIndexId = _cursor.getString(_cursorIndexOfScheduleIndexId);
            }
            final String _tmpPkg;
            if (_cursor.isNull(_cursorIndexOfPkg)) {
              _tmpPkg = null;
            } else {
              _tmpPkg = _cursor.getString(_cursorIndexOfPkg);
            }
            final String _tmpMainActivity;
            if (_cursor.isNull(_cursorIndexOfMainActivity)) {
              _tmpMainActivity = null;
            } else {
              _tmpMainActivity = _cursor.getString(_cursorIndexOfMainActivity);
            }
            final int _tmpMaxLen;
            _tmpMaxLen = _cursor.getInt(_cursorIndexOfMaxLen);
            final int _tmpTrend;
            _tmpTrend = _cursor.getInt(_cursorIndexOfTrend);
            final int _tmpSyncState;
            _tmpSyncState = _cursor.getInt(_cursorIndexOfSyncState);
            final long _tmpSyncTime;
            _tmpSyncTime = _cursor.getLong(_cursorIndexOfSyncTime);
            final long _tmpUuid;
            _tmpUuid = _cursor.getLong(_cursorIndexOfUuid);
            final int _tmpVersion;
            _tmpVersion = _cursor.getInt(_cursorIndexOfVersion);
            _item = new WhiteApp(_tmpId,_tmpUserId,_tmpWhiteAppIndexId,_tmpTomatoIndexId,_tmpScheduleIndexId,_tmpPkg,_tmpMainActivity,_tmpMaxLen,_tmpTrend,_tmpSyncState,_tmpSyncTime,_tmpUuid,_tmpVersion);
            _result.add(_item);
          }
          return _result;
        } finally {
          _cursor.close();
        }
      }

      @Override
      protected void finalize() {
        _statement.release();
      }
    });
  }

  @Override
  public List<WhiteApp> getGlobalWhiteAppsImmediate(final int userId) {
    final String _sql = "select * From WhiteApp Where userId = ? and tomatoIndexId = '' and scheduleIndexId = '' and syncState>=0 order by trend";
    final RoomSQLiteQuery _statement = RoomSQLiteQuery.acquire(_sql, 1);
    int _argIndex = 1;
    _statement.bindLong(_argIndex, userId);
    __db.assertNotSuspendingTransaction();
    final Cursor _cursor = DBUtil.query(__db, _statement, false, null);
    try {
      final int _cursorIndexOfId = CursorUtil.getColumnIndexOrThrow(_cursor, "id");
      final int _cursorIndexOfUserId = CursorUtil.getColumnIndexOrThrow(_cursor, "userId");
      final int _cursorIndexOfWhiteAppIndexId = CursorUtil.getColumnIndexOrThrow(_cursor, "whiteAppIndexId");
      final int _cursorIndexOfTomatoIndexId = CursorUtil.getColumnIndexOrThrow(_cursor, "tomatoIndexId");
      final int _cursorIndexOfScheduleIndexId = CursorUtil.getColumnIndexOrThrow(_cursor, "scheduleIndexId");
      final int _cursorIndexOfPkg = CursorUtil.getColumnIndexOrThrow(_cursor, "pkg");
      final int _cursorIndexOfMainActivity = CursorUtil.getColumnIndexOrThrow(_cursor, "mainActivity");
      final int _cursorIndexOfMaxLen = CursorUtil.getColumnIndexOrThrow(_cursor, "maxLen");
      final int _cursorIndexOfTrend = CursorUtil.getColumnIndexOrThrow(_cursor, "trend");
      final int _cursorIndexOfSyncState = CursorUtil.getColumnIndexOrThrow(_cursor, "syncState");
      final int _cursorIndexOfSyncTime = CursorUtil.getColumnIndexOrThrow(_cursor, "syncTime");
      final int _cursorIndexOfUuid = CursorUtil.getColumnIndexOrThrow(_cursor, "uuid");
      final int _cursorIndexOfVersion = CursorUtil.getColumnIndexOrThrow(_cursor, "version");
      final List<WhiteApp> _result = new ArrayList<WhiteApp>(_cursor.getCount());
      while (_cursor.moveToNext()) {
        final WhiteApp _item;
        final long _tmpId;
        _tmpId = _cursor.getLong(_cursorIndexOfId);
        final int _tmpUserId;
        _tmpUserId = _cursor.getInt(_cursorIndexOfUserId);
        final String _tmpWhiteAppIndexId;
        if (_cursor.isNull(_cursorIndexOfWhiteAppIndexId)) {
          _tmpWhiteAppIndexId = null;
        } else {
          _tmpWhiteAppIndexId = _cursor.getString(_cursorIndexOfWhiteAppIndexId);
        }
        final String _tmpTomatoIndexId;
        if (_cursor.isNull(_cursorIndexOfTomatoIndexId)) {
          _tmpTomatoIndexId = null;
        } else {
          _tmpTomatoIndexId = _cursor.getString(_cursorIndexOfTomatoIndexId);
        }
        final String _tmpScheduleIndexId;
        if (_cursor.isNull(_cursorIndexOfScheduleIndexId)) {
          _tmpScheduleIndexId = null;
        } else {
          _tmpScheduleIndexId = _cursor.getString(_cursorIndexOfScheduleIndexId);
        }
        final String _tmpPkg;
        if (_cursor.isNull(_cursorIndexOfPkg)) {
          _tmpPkg = null;
        } else {
          _tmpPkg = _cursor.getString(_cursorIndexOfPkg);
        }
        final String _tmpMainActivity;
        if (_cursor.isNull(_cursorIndexOfMainActivity)) {
          _tmpMainActivity = null;
        } else {
          _tmpMainActivity = _cursor.getString(_cursorIndexOfMainActivity);
        }
        final int _tmpMaxLen;
        _tmpMaxLen = _cursor.getInt(_cursorIndexOfMaxLen);
        final int _tmpTrend;
        _tmpTrend = _cursor.getInt(_cursorIndexOfTrend);
        final int _tmpSyncState;
        _tmpSyncState = _cursor.getInt(_cursorIndexOfSyncState);
        final long _tmpSyncTime;
        _tmpSyncTime = _cursor.getLong(_cursorIndexOfSyncTime);
        final long _tmpUuid;
        _tmpUuid = _cursor.getLong(_cursorIndexOfUuid);
        final int _tmpVersion;
        _tmpVersion = _cursor.getInt(_cursorIndexOfVersion);
        _item = new WhiteApp(_tmpId,_tmpUserId,_tmpWhiteAppIndexId,_tmpTomatoIndexId,_tmpScheduleIndexId,_tmpPkg,_tmpMainActivity,_tmpMaxLen,_tmpTrend,_tmpSyncState,_tmpSyncTime,_tmpUuid,_tmpVersion);
        _result.add(_item);
      }
      return _result;
    } finally {
      _cursor.close();
      _statement.release();
    }
  }

  @Override
  public Object getWhiteAppsWithState(final int userId, final int state,
      final Continuation<? super List<WhiteApp>> $completion) {
    final String _sql = "select * From WhiteApp Where userId = ? and syncState = ? order by trend";
    final RoomSQLiteQuery _statement = RoomSQLiteQuery.acquire(_sql, 2);
    int _argIndex = 1;
    _statement.bindLong(_argIndex, userId);
    _argIndex = 2;
    _statement.bindLong(_argIndex, state);
    final CancellationSignal _cancellationSignal = DBUtil.createCancellationSignal();
    return CoroutinesRoom.execute(__db, false, _cancellationSignal, new Callable<List<WhiteApp>>() {
      @Override
      @NonNull
      public List<WhiteApp> call() throws Exception {
        final Cursor _cursor = DBUtil.query(__db, _statement, false, null);
        try {
          final int _cursorIndexOfId = CursorUtil.getColumnIndexOrThrow(_cursor, "id");
          final int _cursorIndexOfUserId = CursorUtil.getColumnIndexOrThrow(_cursor, "userId");
          final int _cursorIndexOfWhiteAppIndexId = CursorUtil.getColumnIndexOrThrow(_cursor, "whiteAppIndexId");
          final int _cursorIndexOfTomatoIndexId = CursorUtil.getColumnIndexOrThrow(_cursor, "tomatoIndexId");
          final int _cursorIndexOfScheduleIndexId = CursorUtil.getColumnIndexOrThrow(_cursor, "scheduleIndexId");
          final int _cursorIndexOfPkg = CursorUtil.getColumnIndexOrThrow(_cursor, "pkg");
          final int _cursorIndexOfMainActivity = CursorUtil.getColumnIndexOrThrow(_cursor, "mainActivity");
          final int _cursorIndexOfMaxLen = CursorUtil.getColumnIndexOrThrow(_cursor, "maxLen");
          final int _cursorIndexOfTrend = CursorUtil.getColumnIndexOrThrow(_cursor, "trend");
          final int _cursorIndexOfSyncState = CursorUtil.getColumnIndexOrThrow(_cursor, "syncState");
          final int _cursorIndexOfSyncTime = CursorUtil.getColumnIndexOrThrow(_cursor, "syncTime");
          final int _cursorIndexOfUuid = CursorUtil.getColumnIndexOrThrow(_cursor, "uuid");
          final int _cursorIndexOfVersion = CursorUtil.getColumnIndexOrThrow(_cursor, "version");
          final List<WhiteApp> _result = new ArrayList<WhiteApp>(_cursor.getCount());
          while (_cursor.moveToNext()) {
            final WhiteApp _item;
            final long _tmpId;
            _tmpId = _cursor.getLong(_cursorIndexOfId);
            final int _tmpUserId;
            _tmpUserId = _cursor.getInt(_cursorIndexOfUserId);
            final String _tmpWhiteAppIndexId;
            if (_cursor.isNull(_cursorIndexOfWhiteAppIndexId)) {
              _tmpWhiteAppIndexId = null;
            } else {
              _tmpWhiteAppIndexId = _cursor.getString(_cursorIndexOfWhiteAppIndexId);
            }
            final String _tmpTomatoIndexId;
            if (_cursor.isNull(_cursorIndexOfTomatoIndexId)) {
              _tmpTomatoIndexId = null;
            } else {
              _tmpTomatoIndexId = _cursor.getString(_cursorIndexOfTomatoIndexId);
            }
            final String _tmpScheduleIndexId;
            if (_cursor.isNull(_cursorIndexOfScheduleIndexId)) {
              _tmpScheduleIndexId = null;
            } else {
              _tmpScheduleIndexId = _cursor.getString(_cursorIndexOfScheduleIndexId);
            }
            final String _tmpPkg;
            if (_cursor.isNull(_cursorIndexOfPkg)) {
              _tmpPkg = null;
            } else {
              _tmpPkg = _cursor.getString(_cursorIndexOfPkg);
            }
            final String _tmpMainActivity;
            if (_cursor.isNull(_cursorIndexOfMainActivity)) {
              _tmpMainActivity = null;
            } else {
              _tmpMainActivity = _cursor.getString(_cursorIndexOfMainActivity);
            }
            final int _tmpMaxLen;
            _tmpMaxLen = _cursor.getInt(_cursorIndexOfMaxLen);
            final int _tmpTrend;
            _tmpTrend = _cursor.getInt(_cursorIndexOfTrend);
            final int _tmpSyncState;
            _tmpSyncState = _cursor.getInt(_cursorIndexOfSyncState);
            final long _tmpSyncTime;
            _tmpSyncTime = _cursor.getLong(_cursorIndexOfSyncTime);
            final long _tmpUuid;
            _tmpUuid = _cursor.getLong(_cursorIndexOfUuid);
            final int _tmpVersion;
            _tmpVersion = _cursor.getInt(_cursorIndexOfVersion);
            _item = new WhiteApp(_tmpId,_tmpUserId,_tmpWhiteAppIndexId,_tmpTomatoIndexId,_tmpScheduleIndexId,_tmpPkg,_tmpMainActivity,_tmpMaxLen,_tmpTrend,_tmpSyncState,_tmpSyncTime,_tmpUuid,_tmpVersion);
            _result.add(_item);
          }
          return _result;
        } finally {
          _cursor.close();
          _statement.release();
        }
      }
    }, $completion);
  }

  @Override
  public LiveData<List<WhiteApp>> getWhiteAppsWithTomatoId(final String tomatoIndexId) {
    final String _sql = "select * From WhiteApp Where tomatoIndexId = ? and syncState>=0 order by trend";
    final RoomSQLiteQuery _statement = RoomSQLiteQuery.acquire(_sql, 1);
    int _argIndex = 1;
    if (tomatoIndexId == null) {
      _statement.bindNull(_argIndex);
    } else {
      _statement.bindString(_argIndex, tomatoIndexId);
    }
    return __db.getInvalidationTracker().createLiveData(new String[] {"WhiteApp"}, false, new Callable<List<WhiteApp>>() {
      @Override
      @Nullable
      public List<WhiteApp> call() throws Exception {
        final Cursor _cursor = DBUtil.query(__db, _statement, false, null);
        try {
          final int _cursorIndexOfId = CursorUtil.getColumnIndexOrThrow(_cursor, "id");
          final int _cursorIndexOfUserId = CursorUtil.getColumnIndexOrThrow(_cursor, "userId");
          final int _cursorIndexOfWhiteAppIndexId = CursorUtil.getColumnIndexOrThrow(_cursor, "whiteAppIndexId");
          final int _cursorIndexOfTomatoIndexId = CursorUtil.getColumnIndexOrThrow(_cursor, "tomatoIndexId");
          final int _cursorIndexOfScheduleIndexId = CursorUtil.getColumnIndexOrThrow(_cursor, "scheduleIndexId");
          final int _cursorIndexOfPkg = CursorUtil.getColumnIndexOrThrow(_cursor, "pkg");
          final int _cursorIndexOfMainActivity = CursorUtil.getColumnIndexOrThrow(_cursor, "mainActivity");
          final int _cursorIndexOfMaxLen = CursorUtil.getColumnIndexOrThrow(_cursor, "maxLen");
          final int _cursorIndexOfTrend = CursorUtil.getColumnIndexOrThrow(_cursor, "trend");
          final int _cursorIndexOfSyncState = CursorUtil.getColumnIndexOrThrow(_cursor, "syncState");
          final int _cursorIndexOfSyncTime = CursorUtil.getColumnIndexOrThrow(_cursor, "syncTime");
          final int _cursorIndexOfUuid = CursorUtil.getColumnIndexOrThrow(_cursor, "uuid");
          final int _cursorIndexOfVersion = CursorUtil.getColumnIndexOrThrow(_cursor, "version");
          final List<WhiteApp> _result = new ArrayList<WhiteApp>(_cursor.getCount());
          while (_cursor.moveToNext()) {
            final WhiteApp _item;
            final long _tmpId;
            _tmpId = _cursor.getLong(_cursorIndexOfId);
            final int _tmpUserId;
            _tmpUserId = _cursor.getInt(_cursorIndexOfUserId);
            final String _tmpWhiteAppIndexId;
            if (_cursor.isNull(_cursorIndexOfWhiteAppIndexId)) {
              _tmpWhiteAppIndexId = null;
            } else {
              _tmpWhiteAppIndexId = _cursor.getString(_cursorIndexOfWhiteAppIndexId);
            }
            final String _tmpTomatoIndexId;
            if (_cursor.isNull(_cursorIndexOfTomatoIndexId)) {
              _tmpTomatoIndexId = null;
            } else {
              _tmpTomatoIndexId = _cursor.getString(_cursorIndexOfTomatoIndexId);
            }
            final String _tmpScheduleIndexId;
            if (_cursor.isNull(_cursorIndexOfScheduleIndexId)) {
              _tmpScheduleIndexId = null;
            } else {
              _tmpScheduleIndexId = _cursor.getString(_cursorIndexOfScheduleIndexId);
            }
            final String _tmpPkg;
            if (_cursor.isNull(_cursorIndexOfPkg)) {
              _tmpPkg = null;
            } else {
              _tmpPkg = _cursor.getString(_cursorIndexOfPkg);
            }
            final String _tmpMainActivity;
            if (_cursor.isNull(_cursorIndexOfMainActivity)) {
              _tmpMainActivity = null;
            } else {
              _tmpMainActivity = _cursor.getString(_cursorIndexOfMainActivity);
            }
            final int _tmpMaxLen;
            _tmpMaxLen = _cursor.getInt(_cursorIndexOfMaxLen);
            final int _tmpTrend;
            _tmpTrend = _cursor.getInt(_cursorIndexOfTrend);
            final int _tmpSyncState;
            _tmpSyncState = _cursor.getInt(_cursorIndexOfSyncState);
            final long _tmpSyncTime;
            _tmpSyncTime = _cursor.getLong(_cursorIndexOfSyncTime);
            final long _tmpUuid;
            _tmpUuid = _cursor.getLong(_cursorIndexOfUuid);
            final int _tmpVersion;
            _tmpVersion = _cursor.getInt(_cursorIndexOfVersion);
            _item = new WhiteApp(_tmpId,_tmpUserId,_tmpWhiteAppIndexId,_tmpTomatoIndexId,_tmpScheduleIndexId,_tmpPkg,_tmpMainActivity,_tmpMaxLen,_tmpTrend,_tmpSyncState,_tmpSyncTime,_tmpUuid,_tmpVersion);
            _result.add(_item);
          }
          return _result;
        } finally {
          _cursor.close();
        }
      }

      @Override
      protected void finalize() {
        _statement.release();
      }
    });
  }

  @Override
  public LiveData<List<WhiteApp>> getWhiteAppsWithScheduleId(final String scheduleIndexId) {
    final String _sql = "select * From WhiteApp Where scheduleIndexId = ? and syncState>=0 order by trend";
    final RoomSQLiteQuery _statement = RoomSQLiteQuery.acquire(_sql, 1);
    int _argIndex = 1;
    if (scheduleIndexId == null) {
      _statement.bindNull(_argIndex);
    } else {
      _statement.bindString(_argIndex, scheduleIndexId);
    }
    return __db.getInvalidationTracker().createLiveData(new String[] {"WhiteApp"}, false, new Callable<List<WhiteApp>>() {
      @Override
      @Nullable
      public List<WhiteApp> call() throws Exception {
        final Cursor _cursor = DBUtil.query(__db, _statement, false, null);
        try {
          final int _cursorIndexOfId = CursorUtil.getColumnIndexOrThrow(_cursor, "id");
          final int _cursorIndexOfUserId = CursorUtil.getColumnIndexOrThrow(_cursor, "userId");
          final int _cursorIndexOfWhiteAppIndexId = CursorUtil.getColumnIndexOrThrow(_cursor, "whiteAppIndexId");
          final int _cursorIndexOfTomatoIndexId = CursorUtil.getColumnIndexOrThrow(_cursor, "tomatoIndexId");
          final int _cursorIndexOfScheduleIndexId = CursorUtil.getColumnIndexOrThrow(_cursor, "scheduleIndexId");
          final int _cursorIndexOfPkg = CursorUtil.getColumnIndexOrThrow(_cursor, "pkg");
          final int _cursorIndexOfMainActivity = CursorUtil.getColumnIndexOrThrow(_cursor, "mainActivity");
          final int _cursorIndexOfMaxLen = CursorUtil.getColumnIndexOrThrow(_cursor, "maxLen");
          final int _cursorIndexOfTrend = CursorUtil.getColumnIndexOrThrow(_cursor, "trend");
          final int _cursorIndexOfSyncState = CursorUtil.getColumnIndexOrThrow(_cursor, "syncState");
          final int _cursorIndexOfSyncTime = CursorUtil.getColumnIndexOrThrow(_cursor, "syncTime");
          final int _cursorIndexOfUuid = CursorUtil.getColumnIndexOrThrow(_cursor, "uuid");
          final int _cursorIndexOfVersion = CursorUtil.getColumnIndexOrThrow(_cursor, "version");
          final List<WhiteApp> _result = new ArrayList<WhiteApp>(_cursor.getCount());
          while (_cursor.moveToNext()) {
            final WhiteApp _item;
            final long _tmpId;
            _tmpId = _cursor.getLong(_cursorIndexOfId);
            final int _tmpUserId;
            _tmpUserId = _cursor.getInt(_cursorIndexOfUserId);
            final String _tmpWhiteAppIndexId;
            if (_cursor.isNull(_cursorIndexOfWhiteAppIndexId)) {
              _tmpWhiteAppIndexId = null;
            } else {
              _tmpWhiteAppIndexId = _cursor.getString(_cursorIndexOfWhiteAppIndexId);
            }
            final String _tmpTomatoIndexId;
            if (_cursor.isNull(_cursorIndexOfTomatoIndexId)) {
              _tmpTomatoIndexId = null;
            } else {
              _tmpTomatoIndexId = _cursor.getString(_cursorIndexOfTomatoIndexId);
            }
            final String _tmpScheduleIndexId;
            if (_cursor.isNull(_cursorIndexOfScheduleIndexId)) {
              _tmpScheduleIndexId = null;
            } else {
              _tmpScheduleIndexId = _cursor.getString(_cursorIndexOfScheduleIndexId);
            }
            final String _tmpPkg;
            if (_cursor.isNull(_cursorIndexOfPkg)) {
              _tmpPkg = null;
            } else {
              _tmpPkg = _cursor.getString(_cursorIndexOfPkg);
            }
            final String _tmpMainActivity;
            if (_cursor.isNull(_cursorIndexOfMainActivity)) {
              _tmpMainActivity = null;
            } else {
              _tmpMainActivity = _cursor.getString(_cursorIndexOfMainActivity);
            }
            final int _tmpMaxLen;
            _tmpMaxLen = _cursor.getInt(_cursorIndexOfMaxLen);
            final int _tmpTrend;
            _tmpTrend = _cursor.getInt(_cursorIndexOfTrend);
            final int _tmpSyncState;
            _tmpSyncState = _cursor.getInt(_cursorIndexOfSyncState);
            final long _tmpSyncTime;
            _tmpSyncTime = _cursor.getLong(_cursorIndexOfSyncTime);
            final long _tmpUuid;
            _tmpUuid = _cursor.getLong(_cursorIndexOfUuid);
            final int _tmpVersion;
            _tmpVersion = _cursor.getInt(_cursorIndexOfVersion);
            _item = new WhiteApp(_tmpId,_tmpUserId,_tmpWhiteAppIndexId,_tmpTomatoIndexId,_tmpScheduleIndexId,_tmpPkg,_tmpMainActivity,_tmpMaxLen,_tmpTrend,_tmpSyncState,_tmpSyncTime,_tmpUuid,_tmpVersion);
            _result.add(_item);
          }
          return _result;
        } finally {
          _cursor.close();
        }
      }

      @Override
      protected void finalize() {
        _statement.release();
      }
    });
  }

  @Override
  public List<WhiteApp> getWhiteAppsWithTomatoIdAtOnce(final String tomatoIndexId) {
    final String _sql = "select * From WhiteApp Where tomatoIndexId = ? and syncState>=0 order by trend";
    final RoomSQLiteQuery _statement = RoomSQLiteQuery.acquire(_sql, 1);
    int _argIndex = 1;
    if (tomatoIndexId == null) {
      _statement.bindNull(_argIndex);
    } else {
      _statement.bindString(_argIndex, tomatoIndexId);
    }
    __db.assertNotSuspendingTransaction();
    final Cursor _cursor = DBUtil.query(__db, _statement, false, null);
    try {
      final int _cursorIndexOfId = CursorUtil.getColumnIndexOrThrow(_cursor, "id");
      final int _cursorIndexOfUserId = CursorUtil.getColumnIndexOrThrow(_cursor, "userId");
      final int _cursorIndexOfWhiteAppIndexId = CursorUtil.getColumnIndexOrThrow(_cursor, "whiteAppIndexId");
      final int _cursorIndexOfTomatoIndexId = CursorUtil.getColumnIndexOrThrow(_cursor, "tomatoIndexId");
      final int _cursorIndexOfScheduleIndexId = CursorUtil.getColumnIndexOrThrow(_cursor, "scheduleIndexId");
      final int _cursorIndexOfPkg = CursorUtil.getColumnIndexOrThrow(_cursor, "pkg");
      final int _cursorIndexOfMainActivity = CursorUtil.getColumnIndexOrThrow(_cursor, "mainActivity");
      final int _cursorIndexOfMaxLen = CursorUtil.getColumnIndexOrThrow(_cursor, "maxLen");
      final int _cursorIndexOfTrend = CursorUtil.getColumnIndexOrThrow(_cursor, "trend");
      final int _cursorIndexOfSyncState = CursorUtil.getColumnIndexOrThrow(_cursor, "syncState");
      final int _cursorIndexOfSyncTime = CursorUtil.getColumnIndexOrThrow(_cursor, "syncTime");
      final int _cursorIndexOfUuid = CursorUtil.getColumnIndexOrThrow(_cursor, "uuid");
      final int _cursorIndexOfVersion = CursorUtil.getColumnIndexOrThrow(_cursor, "version");
      final List<WhiteApp> _result = new ArrayList<WhiteApp>(_cursor.getCount());
      while (_cursor.moveToNext()) {
        final WhiteApp _item;
        final long _tmpId;
        _tmpId = _cursor.getLong(_cursorIndexOfId);
        final int _tmpUserId;
        _tmpUserId = _cursor.getInt(_cursorIndexOfUserId);
        final String _tmpWhiteAppIndexId;
        if (_cursor.isNull(_cursorIndexOfWhiteAppIndexId)) {
          _tmpWhiteAppIndexId = null;
        } else {
          _tmpWhiteAppIndexId = _cursor.getString(_cursorIndexOfWhiteAppIndexId);
        }
        final String _tmpTomatoIndexId;
        if (_cursor.isNull(_cursorIndexOfTomatoIndexId)) {
          _tmpTomatoIndexId = null;
        } else {
          _tmpTomatoIndexId = _cursor.getString(_cursorIndexOfTomatoIndexId);
        }
        final String _tmpScheduleIndexId;
        if (_cursor.isNull(_cursorIndexOfScheduleIndexId)) {
          _tmpScheduleIndexId = null;
        } else {
          _tmpScheduleIndexId = _cursor.getString(_cursorIndexOfScheduleIndexId);
        }
        final String _tmpPkg;
        if (_cursor.isNull(_cursorIndexOfPkg)) {
          _tmpPkg = null;
        } else {
          _tmpPkg = _cursor.getString(_cursorIndexOfPkg);
        }
        final String _tmpMainActivity;
        if (_cursor.isNull(_cursorIndexOfMainActivity)) {
          _tmpMainActivity = null;
        } else {
          _tmpMainActivity = _cursor.getString(_cursorIndexOfMainActivity);
        }
        final int _tmpMaxLen;
        _tmpMaxLen = _cursor.getInt(_cursorIndexOfMaxLen);
        final int _tmpTrend;
        _tmpTrend = _cursor.getInt(_cursorIndexOfTrend);
        final int _tmpSyncState;
        _tmpSyncState = _cursor.getInt(_cursorIndexOfSyncState);
        final long _tmpSyncTime;
        _tmpSyncTime = _cursor.getLong(_cursorIndexOfSyncTime);
        final long _tmpUuid;
        _tmpUuid = _cursor.getLong(_cursorIndexOfUuid);
        final int _tmpVersion;
        _tmpVersion = _cursor.getInt(_cursorIndexOfVersion);
        _item = new WhiteApp(_tmpId,_tmpUserId,_tmpWhiteAppIndexId,_tmpTomatoIndexId,_tmpScheduleIndexId,_tmpPkg,_tmpMainActivity,_tmpMaxLen,_tmpTrend,_tmpSyncState,_tmpSyncTime,_tmpUuid,_tmpVersion);
        _result.add(_item);
      }
      return _result;
    } finally {
      _cursor.close();
      _statement.release();
    }
  }

  @Override
  public List<WhiteApp> getWhiteAppsWithScheduleIdAtOnce(final String scheduleIndexId) {
    final String _sql = "select * From WhiteApp Where scheduleIndexId = ? and syncState>=0 order by trend";
    final RoomSQLiteQuery _statement = RoomSQLiteQuery.acquire(_sql, 1);
    int _argIndex = 1;
    if (scheduleIndexId == null) {
      _statement.bindNull(_argIndex);
    } else {
      _statement.bindString(_argIndex, scheduleIndexId);
    }
    __db.assertNotSuspendingTransaction();
    final Cursor _cursor = DBUtil.query(__db, _statement, false, null);
    try {
      final int _cursorIndexOfId = CursorUtil.getColumnIndexOrThrow(_cursor, "id");
      final int _cursorIndexOfUserId = CursorUtil.getColumnIndexOrThrow(_cursor, "userId");
      final int _cursorIndexOfWhiteAppIndexId = CursorUtil.getColumnIndexOrThrow(_cursor, "whiteAppIndexId");
      final int _cursorIndexOfTomatoIndexId = CursorUtil.getColumnIndexOrThrow(_cursor, "tomatoIndexId");
      final int _cursorIndexOfScheduleIndexId = CursorUtil.getColumnIndexOrThrow(_cursor, "scheduleIndexId");
      final int _cursorIndexOfPkg = CursorUtil.getColumnIndexOrThrow(_cursor, "pkg");
      final int _cursorIndexOfMainActivity = CursorUtil.getColumnIndexOrThrow(_cursor, "mainActivity");
      final int _cursorIndexOfMaxLen = CursorUtil.getColumnIndexOrThrow(_cursor, "maxLen");
      final int _cursorIndexOfTrend = CursorUtil.getColumnIndexOrThrow(_cursor, "trend");
      final int _cursorIndexOfSyncState = CursorUtil.getColumnIndexOrThrow(_cursor, "syncState");
      final int _cursorIndexOfSyncTime = CursorUtil.getColumnIndexOrThrow(_cursor, "syncTime");
      final int _cursorIndexOfUuid = CursorUtil.getColumnIndexOrThrow(_cursor, "uuid");
      final int _cursorIndexOfVersion = CursorUtil.getColumnIndexOrThrow(_cursor, "version");
      final List<WhiteApp> _result = new ArrayList<WhiteApp>(_cursor.getCount());
      while (_cursor.moveToNext()) {
        final WhiteApp _item;
        final long _tmpId;
        _tmpId = _cursor.getLong(_cursorIndexOfId);
        final int _tmpUserId;
        _tmpUserId = _cursor.getInt(_cursorIndexOfUserId);
        final String _tmpWhiteAppIndexId;
        if (_cursor.isNull(_cursorIndexOfWhiteAppIndexId)) {
          _tmpWhiteAppIndexId = null;
        } else {
          _tmpWhiteAppIndexId = _cursor.getString(_cursorIndexOfWhiteAppIndexId);
        }
        final String _tmpTomatoIndexId;
        if (_cursor.isNull(_cursorIndexOfTomatoIndexId)) {
          _tmpTomatoIndexId = null;
        } else {
          _tmpTomatoIndexId = _cursor.getString(_cursorIndexOfTomatoIndexId);
        }
        final String _tmpScheduleIndexId;
        if (_cursor.isNull(_cursorIndexOfScheduleIndexId)) {
          _tmpScheduleIndexId = null;
        } else {
          _tmpScheduleIndexId = _cursor.getString(_cursorIndexOfScheduleIndexId);
        }
        final String _tmpPkg;
        if (_cursor.isNull(_cursorIndexOfPkg)) {
          _tmpPkg = null;
        } else {
          _tmpPkg = _cursor.getString(_cursorIndexOfPkg);
        }
        final String _tmpMainActivity;
        if (_cursor.isNull(_cursorIndexOfMainActivity)) {
          _tmpMainActivity = null;
        } else {
          _tmpMainActivity = _cursor.getString(_cursorIndexOfMainActivity);
        }
        final int _tmpMaxLen;
        _tmpMaxLen = _cursor.getInt(_cursorIndexOfMaxLen);
        final int _tmpTrend;
        _tmpTrend = _cursor.getInt(_cursorIndexOfTrend);
        final int _tmpSyncState;
        _tmpSyncState = _cursor.getInt(_cursorIndexOfSyncState);
        final long _tmpSyncTime;
        _tmpSyncTime = _cursor.getLong(_cursorIndexOfSyncTime);
        final long _tmpUuid;
        _tmpUuid = _cursor.getLong(_cursorIndexOfUuid);
        final int _tmpVersion;
        _tmpVersion = _cursor.getInt(_cursorIndexOfVersion);
        _item = new WhiteApp(_tmpId,_tmpUserId,_tmpWhiteAppIndexId,_tmpTomatoIndexId,_tmpScheduleIndexId,_tmpPkg,_tmpMainActivity,_tmpMaxLen,_tmpTrend,_tmpSyncState,_tmpSyncTime,_tmpUuid,_tmpVersion);
        _result.add(_item);
      }
      return _result;
    } finally {
      _cursor.close();
      _statement.release();
    }
  }

  @Override
  public Object getLastWhiteApp(final String tomatoIndexId, final String scheduleIndexId,
      final Continuation<? super WhiteApp> $completion) {
    final String _sql = "select * from WhiteApp Where tomatoIndexId = ? and scheduleIndexId = ?  order by trend desc limit 1";
    final RoomSQLiteQuery _statement = RoomSQLiteQuery.acquire(_sql, 2);
    int _argIndex = 1;
    if (tomatoIndexId == null) {
      _statement.bindNull(_argIndex);
    } else {
      _statement.bindString(_argIndex, tomatoIndexId);
    }
    _argIndex = 2;
    if (scheduleIndexId == null) {
      _statement.bindNull(_argIndex);
    } else {
      _statement.bindString(_argIndex, scheduleIndexId);
    }
    final CancellationSignal _cancellationSignal = DBUtil.createCancellationSignal();
    return CoroutinesRoom.execute(__db, false, _cancellationSignal, new Callable<WhiteApp>() {
      @Override
      @Nullable
      public WhiteApp call() throws Exception {
        final Cursor _cursor = DBUtil.query(__db, _statement, false, null);
        try {
          final int _cursorIndexOfId = CursorUtil.getColumnIndexOrThrow(_cursor, "id");
          final int _cursorIndexOfUserId = CursorUtil.getColumnIndexOrThrow(_cursor, "userId");
          final int _cursorIndexOfWhiteAppIndexId = CursorUtil.getColumnIndexOrThrow(_cursor, "whiteAppIndexId");
          final int _cursorIndexOfTomatoIndexId = CursorUtil.getColumnIndexOrThrow(_cursor, "tomatoIndexId");
          final int _cursorIndexOfScheduleIndexId = CursorUtil.getColumnIndexOrThrow(_cursor, "scheduleIndexId");
          final int _cursorIndexOfPkg = CursorUtil.getColumnIndexOrThrow(_cursor, "pkg");
          final int _cursorIndexOfMainActivity = CursorUtil.getColumnIndexOrThrow(_cursor, "mainActivity");
          final int _cursorIndexOfMaxLen = CursorUtil.getColumnIndexOrThrow(_cursor, "maxLen");
          final int _cursorIndexOfTrend = CursorUtil.getColumnIndexOrThrow(_cursor, "trend");
          final int _cursorIndexOfSyncState = CursorUtil.getColumnIndexOrThrow(_cursor, "syncState");
          final int _cursorIndexOfSyncTime = CursorUtil.getColumnIndexOrThrow(_cursor, "syncTime");
          final int _cursorIndexOfUuid = CursorUtil.getColumnIndexOrThrow(_cursor, "uuid");
          final int _cursorIndexOfVersion = CursorUtil.getColumnIndexOrThrow(_cursor, "version");
          final WhiteApp _result;
          if (_cursor.moveToFirst()) {
            final long _tmpId;
            _tmpId = _cursor.getLong(_cursorIndexOfId);
            final int _tmpUserId;
            _tmpUserId = _cursor.getInt(_cursorIndexOfUserId);
            final String _tmpWhiteAppIndexId;
            if (_cursor.isNull(_cursorIndexOfWhiteAppIndexId)) {
              _tmpWhiteAppIndexId = null;
            } else {
              _tmpWhiteAppIndexId = _cursor.getString(_cursorIndexOfWhiteAppIndexId);
            }
            final String _tmpTomatoIndexId;
            if (_cursor.isNull(_cursorIndexOfTomatoIndexId)) {
              _tmpTomatoIndexId = null;
            } else {
              _tmpTomatoIndexId = _cursor.getString(_cursorIndexOfTomatoIndexId);
            }
            final String _tmpScheduleIndexId;
            if (_cursor.isNull(_cursorIndexOfScheduleIndexId)) {
              _tmpScheduleIndexId = null;
            } else {
              _tmpScheduleIndexId = _cursor.getString(_cursorIndexOfScheduleIndexId);
            }
            final String _tmpPkg;
            if (_cursor.isNull(_cursorIndexOfPkg)) {
              _tmpPkg = null;
            } else {
              _tmpPkg = _cursor.getString(_cursorIndexOfPkg);
            }
            final String _tmpMainActivity;
            if (_cursor.isNull(_cursorIndexOfMainActivity)) {
              _tmpMainActivity = null;
            } else {
              _tmpMainActivity = _cursor.getString(_cursorIndexOfMainActivity);
            }
            final int _tmpMaxLen;
            _tmpMaxLen = _cursor.getInt(_cursorIndexOfMaxLen);
            final int _tmpTrend;
            _tmpTrend = _cursor.getInt(_cursorIndexOfTrend);
            final int _tmpSyncState;
            _tmpSyncState = _cursor.getInt(_cursorIndexOfSyncState);
            final long _tmpSyncTime;
            _tmpSyncTime = _cursor.getLong(_cursorIndexOfSyncTime);
            final long _tmpUuid;
            _tmpUuid = _cursor.getLong(_cursorIndexOfUuid);
            final int _tmpVersion;
            _tmpVersion = _cursor.getInt(_cursorIndexOfVersion);
            _result = new WhiteApp(_tmpId,_tmpUserId,_tmpWhiteAppIndexId,_tmpTomatoIndexId,_tmpScheduleIndexId,_tmpPkg,_tmpMainActivity,_tmpMaxLen,_tmpTrend,_tmpSyncState,_tmpSyncTime,_tmpUuid,_tmpVersion);
          } else {
            _result = null;
          }
          return _result;
        } finally {
          _cursor.close();
          _statement.release();
        }
      }
    }, $completion);
  }

  @Override
  public Object isWhiteAppExist(final int userId, final String tomatoIndexId,
      final String scheduleIndexId, final String pkg, final String mainActivity,
      final Continuation<? super Boolean> $completion) {
    final String _sql = "SELECT EXISTS(select 1 from WhiteApp Where userId = ? and tomatoIndexId = ? and scheduleIndexId = ? and pkg=? and mainActivity=? and syncState>=0 limit 1)";
    final RoomSQLiteQuery _statement = RoomSQLiteQuery.acquire(_sql, 5);
    int _argIndex = 1;
    _statement.bindLong(_argIndex, userId);
    _argIndex = 2;
    if (tomatoIndexId == null) {
      _statement.bindNull(_argIndex);
    } else {
      _statement.bindString(_argIndex, tomatoIndexId);
    }
    _argIndex = 3;
    if (scheduleIndexId == null) {
      _statement.bindNull(_argIndex);
    } else {
      _statement.bindString(_argIndex, scheduleIndexId);
    }
    _argIndex = 4;
    if (pkg == null) {
      _statement.bindNull(_argIndex);
    } else {
      _statement.bindString(_argIndex, pkg);
    }
    _argIndex = 5;
    if (mainActivity == null) {
      _statement.bindNull(_argIndex);
    } else {
      _statement.bindString(_argIndex, mainActivity);
    }
    final CancellationSignal _cancellationSignal = DBUtil.createCancellationSignal();
    return CoroutinesRoom.execute(__db, false, _cancellationSignal, new Callable<Boolean>() {
      @Override
      @NonNull
      public Boolean call() throws Exception {
        final Cursor _cursor = DBUtil.query(__db, _statement, false, null);
        try {
          final Boolean _result;
          if (_cursor.moveToFirst()) {
            final Integer _tmp;
            if (_cursor.isNull(0)) {
              _tmp = null;
            } else {
              _tmp = _cursor.getInt(0);
            }
            _result = _tmp == null ? null : _tmp != 0;
          } else {
            _result = null;
          }
          return _result;
        } finally {
          _cursor.close();
          _statement.release();
        }
      }
    }, $completion);
  }

  @NonNull
  public static List<Class<?>> getRequiredConverters() {
    return Collections.emptyList();
  }
}
