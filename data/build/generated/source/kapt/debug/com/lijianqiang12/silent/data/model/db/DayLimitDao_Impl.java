package com.lijianqiang12.silent.data.model.db;

import android.database.Cursor;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.lifecycle.LiveData;
import androidx.room.CoroutinesRoom;
import androidx.room.EntityDeletionOrUpdateAdapter;
import androidx.room.EntityInsertionAdapter;
import androidx.room.RoomDatabase;
import androidx.room.RoomSQLiteQuery;
import androidx.room.SharedSQLiteStatement;
import androidx.room.util.CursorUtil;
import androidx.room.util.DBUtil;
import androidx.sqlite.db.SupportSQLiteStatement;
import java.lang.Class;
import java.lang.Exception;
import java.lang.Object;
import java.lang.Override;
import java.lang.String;
import java.lang.SuppressWarnings;
import java.util.Collections;
import java.util.List;
import java.util.concurrent.Callable;
import javax.annotation.processing.Generated;
import kotlin.Unit;
import kotlin.coroutines.Continuation;

@Generated("androidx.room.RoomProcessor")
@SuppressWarnings({"unchecked", "deprecation"})
public final class DayLimitDao_Impl implements DayLimitDao {
  private final RoomDatabase __db;

  private final EntityInsertionAdapter<DayLimit> __insertionAdapterOfDayLimit;

  private final EntityDeletionOrUpdateAdapter<DayLimit> __updateAdapterOfDayLimit;

  private final SharedSQLiteStatement __preparedStmtOfDeleteAll;

  private final SharedSQLiteStatement __preparedStmtOfUpdateUserId;

  public DayLimitDao_Impl(@NonNull final RoomDatabase __db) {
    this.__db = __db;
    this.__insertionAdapterOfDayLimit = new EntityInsertionAdapter<DayLimit>(__db) {
      @Override
      @NonNull
      protected String createQuery() {
        return "INSERT OR IGNORE INTO `DayLimit` (`id`,`userId`,`allDayLimit`,`isIncludeWhite`,`isDenyChange`,`jumpDate`,`denyChangeLength`,`isWorkDayLimit`,`monday`,`tuesday`,`wednesday`,`thursday`,`friday`,`saturday`,`sunday`) VALUES (nullif(?, 0),?,?,?,?,?,?,?,?,?,?,?,?,?,?)";
      }

      @Override
      protected void bind(@NonNull final SupportSQLiteStatement statement,
          @NonNull final DayLimit entity) {
        statement.bindLong(1, entity.getId());
        statement.bindLong(2, entity.getUserId());
        statement.bindLong(3, entity.getAllDayLimit());
        final int _tmp = entity.isIncludeWhite() ? 1 : 0;
        statement.bindLong(4, _tmp);
        final int _tmp_1 = entity.isDenyChange() ? 1 : 0;
        statement.bindLong(5, _tmp_1);
        if (entity.getJumpDate() == null) {
          statement.bindNull(6);
        } else {
          statement.bindString(6, entity.getJumpDate());
        }
        statement.bindLong(7, entity.getDenyChangeLength());
        final int _tmp_2 = entity.isWorkDayLimit() ? 1 : 0;
        statement.bindLong(8, _tmp_2);
        final int _tmp_3 = entity.getMonday() ? 1 : 0;
        statement.bindLong(9, _tmp_3);
        final int _tmp_4 = entity.getTuesday() ? 1 : 0;
        statement.bindLong(10, _tmp_4);
        final int _tmp_5 = entity.getWednesday() ? 1 : 0;
        statement.bindLong(11, _tmp_5);
        final int _tmp_6 = entity.getThursday() ? 1 : 0;
        statement.bindLong(12, _tmp_6);
        final int _tmp_7 = entity.getFriday() ? 1 : 0;
        statement.bindLong(13, _tmp_7);
        final int _tmp_8 = entity.getSaturday() ? 1 : 0;
        statement.bindLong(14, _tmp_8);
        final int _tmp_9 = entity.getSunday() ? 1 : 0;
        statement.bindLong(15, _tmp_9);
      }
    };
    this.__updateAdapterOfDayLimit = new EntityDeletionOrUpdateAdapter<DayLimit>(__db) {
      @Override
      @NonNull
      protected String createQuery() {
        return "UPDATE OR ABORT `DayLimit` SET `id` = ?,`userId` = ?,`allDayLimit` = ?,`isIncludeWhite` = ?,`isDenyChange` = ?,`jumpDate` = ?,`denyChangeLength` = ?,`isWorkDayLimit` = ?,`monday` = ?,`tuesday` = ?,`wednesday` = ?,`thursday` = ?,`friday` = ?,`saturday` = ?,`sunday` = ? WHERE `id` = ?";
      }

      @Override
      protected void bind(@NonNull final SupportSQLiteStatement statement,
          @NonNull final DayLimit entity) {
        statement.bindLong(1, entity.getId());
        statement.bindLong(2, entity.getUserId());
        statement.bindLong(3, entity.getAllDayLimit());
        final int _tmp = entity.isIncludeWhite() ? 1 : 0;
        statement.bindLong(4, _tmp);
        final int _tmp_1 = entity.isDenyChange() ? 1 : 0;
        statement.bindLong(5, _tmp_1);
        if (entity.getJumpDate() == null) {
          statement.bindNull(6);
        } else {
          statement.bindString(6, entity.getJumpDate());
        }
        statement.bindLong(7, entity.getDenyChangeLength());
        final int _tmp_2 = entity.isWorkDayLimit() ? 1 : 0;
        statement.bindLong(8, _tmp_2);
        final int _tmp_3 = entity.getMonday() ? 1 : 0;
        statement.bindLong(9, _tmp_3);
        final int _tmp_4 = entity.getTuesday() ? 1 : 0;
        statement.bindLong(10, _tmp_4);
        final int _tmp_5 = entity.getWednesday() ? 1 : 0;
        statement.bindLong(11, _tmp_5);
        final int _tmp_6 = entity.getThursday() ? 1 : 0;
        statement.bindLong(12, _tmp_6);
        final int _tmp_7 = entity.getFriday() ? 1 : 0;
        statement.bindLong(13, _tmp_7);
        final int _tmp_8 = entity.getSaturday() ? 1 : 0;
        statement.bindLong(14, _tmp_8);
        final int _tmp_9 = entity.getSunday() ? 1 : 0;
        statement.bindLong(15, _tmp_9);
        statement.bindLong(16, entity.getId());
      }
    };
    this.__preparedStmtOfDeleteAll = new SharedSQLiteStatement(__db) {
      @Override
      @NonNull
      public String createQuery() {
        final String _query = "delete from DayLimit where userId = ?";
        return _query;
      }
    };
    this.__preparedStmtOfUpdateUserId = new SharedSQLiteStatement(__db) {
      @Override
      @NonNull
      public String createQuery() {
        final String _query = "UPDATE DayLimit SET userId = ? WHERE userId = -1";
        return _query;
      }
    };
  }

  @Override
  public Object insertDayLimit(final DayLimit dayLimit,
      final Continuation<? super Unit> $completion) {
    return CoroutinesRoom.execute(__db, true, new Callable<Unit>() {
      @Override
      @NonNull
      public Unit call() throws Exception {
        __db.beginTransaction();
        try {
          __insertionAdapterOfDayLimit.insert(dayLimit);
          __db.setTransactionSuccessful();
          return Unit.INSTANCE;
        } finally {
          __db.endTransaction();
        }
      }
    }, $completion);
  }

  @Override
  public Object updateDayLimit(final DayLimit dayLimit,
      final Continuation<? super Unit> $completion) {
    return CoroutinesRoom.execute(__db, true, new Callable<Unit>() {
      @Override
      @NonNull
      public Unit call() throws Exception {
        __db.beginTransaction();
        try {
          __updateAdapterOfDayLimit.handle(dayLimit);
          __db.setTransactionSuccessful();
          return Unit.INSTANCE;
        } finally {
          __db.endTransaction();
        }
      }
    }, $completion);
  }

  @Override
  public Object deleteAll(final int userId, final Continuation<? super Unit> $completion) {
    return CoroutinesRoom.execute(__db, true, new Callable<Unit>() {
      @Override
      @NonNull
      public Unit call() throws Exception {
        final SupportSQLiteStatement _stmt = __preparedStmtOfDeleteAll.acquire();
        int _argIndex = 1;
        _stmt.bindLong(_argIndex, userId);
        try {
          __db.beginTransaction();
          try {
            _stmt.executeUpdateDelete();
            __db.setTransactionSuccessful();
            return Unit.INSTANCE;
          } finally {
            __db.endTransaction();
          }
        } finally {
          __preparedStmtOfDeleteAll.release(_stmt);
        }
      }
    }, $completion);
  }

  @Override
  public void updateUserId(final int newUserId) {
    __db.assertNotSuspendingTransaction();
    final SupportSQLiteStatement _stmt = __preparedStmtOfUpdateUserId.acquire();
    int _argIndex = 1;
    _stmt.bindLong(_argIndex, newUserId);
    try {
      __db.beginTransaction();
      try {
        _stmt.executeUpdateDelete();
        __db.setTransactionSuccessful();
      } finally {
        __db.endTransaction();
      }
    } finally {
      __preparedStmtOfUpdateUserId.release(_stmt);
    }
  }

  @Override
  public LiveData<DayLimit> getTodayLimitsMonday(final int userId) {
    final String _sql = "SELECT * FROM DayLimit where userId = ? and monday=1 limit 1";
    final RoomSQLiteQuery _statement = RoomSQLiteQuery.acquire(_sql, 1);
    int _argIndex = 1;
    _statement.bindLong(_argIndex, userId);
    return __db.getInvalidationTracker().createLiveData(new String[] {"DayLimit"}, false, new Callable<DayLimit>() {
      @Override
      @Nullable
      public DayLimit call() throws Exception {
        final Cursor _cursor = DBUtil.query(__db, _statement, false, null);
        try {
          final int _cursorIndexOfId = CursorUtil.getColumnIndexOrThrow(_cursor, "id");
          final int _cursorIndexOfUserId = CursorUtil.getColumnIndexOrThrow(_cursor, "userId");
          final int _cursorIndexOfAllDayLimit = CursorUtil.getColumnIndexOrThrow(_cursor, "allDayLimit");
          final int _cursorIndexOfIsIncludeWhite = CursorUtil.getColumnIndexOrThrow(_cursor, "isIncludeWhite");
          final int _cursorIndexOfIsDenyChange = CursorUtil.getColumnIndexOrThrow(_cursor, "isDenyChange");
          final int _cursorIndexOfJumpDate = CursorUtil.getColumnIndexOrThrow(_cursor, "jumpDate");
          final int _cursorIndexOfDenyChangeLength = CursorUtil.getColumnIndexOrThrow(_cursor, "denyChangeLength");
          final int _cursorIndexOfIsWorkDayLimit = CursorUtil.getColumnIndexOrThrow(_cursor, "isWorkDayLimit");
          final int _cursorIndexOfMonday = CursorUtil.getColumnIndexOrThrow(_cursor, "monday");
          final int _cursorIndexOfTuesday = CursorUtil.getColumnIndexOrThrow(_cursor, "tuesday");
          final int _cursorIndexOfWednesday = CursorUtil.getColumnIndexOrThrow(_cursor, "wednesday");
          final int _cursorIndexOfThursday = CursorUtil.getColumnIndexOrThrow(_cursor, "thursday");
          final int _cursorIndexOfFriday = CursorUtil.getColumnIndexOrThrow(_cursor, "friday");
          final int _cursorIndexOfSaturday = CursorUtil.getColumnIndexOrThrow(_cursor, "saturday");
          final int _cursorIndexOfSunday = CursorUtil.getColumnIndexOrThrow(_cursor, "sunday");
          final DayLimit _result;
          if (_cursor.moveToFirst()) {
            final long _tmpId;
            _tmpId = _cursor.getLong(_cursorIndexOfId);
            final int _tmpUserId;
            _tmpUserId = _cursor.getInt(_cursorIndexOfUserId);
            final long _tmpAllDayLimit;
            _tmpAllDayLimit = _cursor.getLong(_cursorIndexOfAllDayLimit);
            final boolean _tmpIsIncludeWhite;
            final int _tmp;
            _tmp = _cursor.getInt(_cursorIndexOfIsIncludeWhite);
            _tmpIsIncludeWhite = _tmp != 0;
            final boolean _tmpIsDenyChange;
            final int _tmp_1;
            _tmp_1 = _cursor.getInt(_cursorIndexOfIsDenyChange);
            _tmpIsDenyChange = _tmp_1 != 0;
            final String _tmpJumpDate;
            if (_cursor.isNull(_cursorIndexOfJumpDate)) {
              _tmpJumpDate = null;
            } else {
              _tmpJumpDate = _cursor.getString(_cursorIndexOfJumpDate);
            }
            final int _tmpDenyChangeLength;
            _tmpDenyChangeLength = _cursor.getInt(_cursorIndexOfDenyChangeLength);
            final boolean _tmpIsWorkDayLimit;
            final int _tmp_2;
            _tmp_2 = _cursor.getInt(_cursorIndexOfIsWorkDayLimit);
            _tmpIsWorkDayLimit = _tmp_2 != 0;
            final boolean _tmpMonday;
            final int _tmp_3;
            _tmp_3 = _cursor.getInt(_cursorIndexOfMonday);
            _tmpMonday = _tmp_3 != 0;
            final boolean _tmpTuesday;
            final int _tmp_4;
            _tmp_4 = _cursor.getInt(_cursorIndexOfTuesday);
            _tmpTuesday = _tmp_4 != 0;
            final boolean _tmpWednesday;
            final int _tmp_5;
            _tmp_5 = _cursor.getInt(_cursorIndexOfWednesday);
            _tmpWednesday = _tmp_5 != 0;
            final boolean _tmpThursday;
            final int _tmp_6;
            _tmp_6 = _cursor.getInt(_cursorIndexOfThursday);
            _tmpThursday = _tmp_6 != 0;
            final boolean _tmpFriday;
            final int _tmp_7;
            _tmp_7 = _cursor.getInt(_cursorIndexOfFriday);
            _tmpFriday = _tmp_7 != 0;
            final boolean _tmpSaturday;
            final int _tmp_8;
            _tmp_8 = _cursor.getInt(_cursorIndexOfSaturday);
            _tmpSaturday = _tmp_8 != 0;
            final boolean _tmpSunday;
            final int _tmp_9;
            _tmp_9 = _cursor.getInt(_cursorIndexOfSunday);
            _tmpSunday = _tmp_9 != 0;
            _result = new DayLimit(_tmpId,_tmpUserId,_tmpAllDayLimit,_tmpIsIncludeWhite,_tmpIsDenyChange,_tmpJumpDate,_tmpDenyChangeLength,_tmpIsWorkDayLimit,_tmpMonday,_tmpTuesday,_tmpWednesday,_tmpThursday,_tmpFriday,_tmpSaturday,_tmpSunday);
          } else {
            _result = null;
          }
          return _result;
        } finally {
          _cursor.close();
        }
      }

      @Override
      protected void finalize() {
        _statement.release();
      }
    });
  }

  @Override
  public LiveData<DayLimit> getTodayLimitsTuesday(final int userId) {
    final String _sql = "SELECT * FROM DayLimit where userId = ? and tuesday=1 limit 1";
    final RoomSQLiteQuery _statement = RoomSQLiteQuery.acquire(_sql, 1);
    int _argIndex = 1;
    _statement.bindLong(_argIndex, userId);
    return __db.getInvalidationTracker().createLiveData(new String[] {"DayLimit"}, false, new Callable<DayLimit>() {
      @Override
      @Nullable
      public DayLimit call() throws Exception {
        final Cursor _cursor = DBUtil.query(__db, _statement, false, null);
        try {
          final int _cursorIndexOfId = CursorUtil.getColumnIndexOrThrow(_cursor, "id");
          final int _cursorIndexOfUserId = CursorUtil.getColumnIndexOrThrow(_cursor, "userId");
          final int _cursorIndexOfAllDayLimit = CursorUtil.getColumnIndexOrThrow(_cursor, "allDayLimit");
          final int _cursorIndexOfIsIncludeWhite = CursorUtil.getColumnIndexOrThrow(_cursor, "isIncludeWhite");
          final int _cursorIndexOfIsDenyChange = CursorUtil.getColumnIndexOrThrow(_cursor, "isDenyChange");
          final int _cursorIndexOfJumpDate = CursorUtil.getColumnIndexOrThrow(_cursor, "jumpDate");
          final int _cursorIndexOfDenyChangeLength = CursorUtil.getColumnIndexOrThrow(_cursor, "denyChangeLength");
          final int _cursorIndexOfIsWorkDayLimit = CursorUtil.getColumnIndexOrThrow(_cursor, "isWorkDayLimit");
          final int _cursorIndexOfMonday = CursorUtil.getColumnIndexOrThrow(_cursor, "monday");
          final int _cursorIndexOfTuesday = CursorUtil.getColumnIndexOrThrow(_cursor, "tuesday");
          final int _cursorIndexOfWednesday = CursorUtil.getColumnIndexOrThrow(_cursor, "wednesday");
          final int _cursorIndexOfThursday = CursorUtil.getColumnIndexOrThrow(_cursor, "thursday");
          final int _cursorIndexOfFriday = CursorUtil.getColumnIndexOrThrow(_cursor, "friday");
          final int _cursorIndexOfSaturday = CursorUtil.getColumnIndexOrThrow(_cursor, "saturday");
          final int _cursorIndexOfSunday = CursorUtil.getColumnIndexOrThrow(_cursor, "sunday");
          final DayLimit _result;
          if (_cursor.moveToFirst()) {
            final long _tmpId;
            _tmpId = _cursor.getLong(_cursorIndexOfId);
            final int _tmpUserId;
            _tmpUserId = _cursor.getInt(_cursorIndexOfUserId);
            final long _tmpAllDayLimit;
            _tmpAllDayLimit = _cursor.getLong(_cursorIndexOfAllDayLimit);
            final boolean _tmpIsIncludeWhite;
            final int _tmp;
            _tmp = _cursor.getInt(_cursorIndexOfIsIncludeWhite);
            _tmpIsIncludeWhite = _tmp != 0;
            final boolean _tmpIsDenyChange;
            final int _tmp_1;
            _tmp_1 = _cursor.getInt(_cursorIndexOfIsDenyChange);
            _tmpIsDenyChange = _tmp_1 != 0;
            final String _tmpJumpDate;
            if (_cursor.isNull(_cursorIndexOfJumpDate)) {
              _tmpJumpDate = null;
            } else {
              _tmpJumpDate = _cursor.getString(_cursorIndexOfJumpDate);
            }
            final int _tmpDenyChangeLength;
            _tmpDenyChangeLength = _cursor.getInt(_cursorIndexOfDenyChangeLength);
            final boolean _tmpIsWorkDayLimit;
            final int _tmp_2;
            _tmp_2 = _cursor.getInt(_cursorIndexOfIsWorkDayLimit);
            _tmpIsWorkDayLimit = _tmp_2 != 0;
            final boolean _tmpMonday;
            final int _tmp_3;
            _tmp_3 = _cursor.getInt(_cursorIndexOfMonday);
            _tmpMonday = _tmp_3 != 0;
            final boolean _tmpTuesday;
            final int _tmp_4;
            _tmp_4 = _cursor.getInt(_cursorIndexOfTuesday);
            _tmpTuesday = _tmp_4 != 0;
            final boolean _tmpWednesday;
            final int _tmp_5;
            _tmp_5 = _cursor.getInt(_cursorIndexOfWednesday);
            _tmpWednesday = _tmp_5 != 0;
            final boolean _tmpThursday;
            final int _tmp_6;
            _tmp_6 = _cursor.getInt(_cursorIndexOfThursday);
            _tmpThursday = _tmp_6 != 0;
            final boolean _tmpFriday;
            final int _tmp_7;
            _tmp_7 = _cursor.getInt(_cursorIndexOfFriday);
            _tmpFriday = _tmp_7 != 0;
            final boolean _tmpSaturday;
            final int _tmp_8;
            _tmp_8 = _cursor.getInt(_cursorIndexOfSaturday);
            _tmpSaturday = _tmp_8 != 0;
            final boolean _tmpSunday;
            final int _tmp_9;
            _tmp_9 = _cursor.getInt(_cursorIndexOfSunday);
            _tmpSunday = _tmp_9 != 0;
            _result = new DayLimit(_tmpId,_tmpUserId,_tmpAllDayLimit,_tmpIsIncludeWhite,_tmpIsDenyChange,_tmpJumpDate,_tmpDenyChangeLength,_tmpIsWorkDayLimit,_tmpMonday,_tmpTuesday,_tmpWednesday,_tmpThursday,_tmpFriday,_tmpSaturday,_tmpSunday);
          } else {
            _result = null;
          }
          return _result;
        } finally {
          _cursor.close();
        }
      }

      @Override
      protected void finalize() {
        _statement.release();
      }
    });
  }

  @Override
  public LiveData<DayLimit> getTodayLimitsWednesday(final int userId) {
    final String _sql = "SELECT * FROM DayLimit where userId = ? and wednesday=1 limit 1";
    final RoomSQLiteQuery _statement = RoomSQLiteQuery.acquire(_sql, 1);
    int _argIndex = 1;
    _statement.bindLong(_argIndex, userId);
    return __db.getInvalidationTracker().createLiveData(new String[] {"DayLimit"}, false, new Callable<DayLimit>() {
      @Override
      @Nullable
      public DayLimit call() throws Exception {
        final Cursor _cursor = DBUtil.query(__db, _statement, false, null);
        try {
          final int _cursorIndexOfId = CursorUtil.getColumnIndexOrThrow(_cursor, "id");
          final int _cursorIndexOfUserId = CursorUtil.getColumnIndexOrThrow(_cursor, "userId");
          final int _cursorIndexOfAllDayLimit = CursorUtil.getColumnIndexOrThrow(_cursor, "allDayLimit");
          final int _cursorIndexOfIsIncludeWhite = CursorUtil.getColumnIndexOrThrow(_cursor, "isIncludeWhite");
          final int _cursorIndexOfIsDenyChange = CursorUtil.getColumnIndexOrThrow(_cursor, "isDenyChange");
          final int _cursorIndexOfJumpDate = CursorUtil.getColumnIndexOrThrow(_cursor, "jumpDate");
          final int _cursorIndexOfDenyChangeLength = CursorUtil.getColumnIndexOrThrow(_cursor, "denyChangeLength");
          final int _cursorIndexOfIsWorkDayLimit = CursorUtil.getColumnIndexOrThrow(_cursor, "isWorkDayLimit");
          final int _cursorIndexOfMonday = CursorUtil.getColumnIndexOrThrow(_cursor, "monday");
          final int _cursorIndexOfTuesday = CursorUtil.getColumnIndexOrThrow(_cursor, "tuesday");
          final int _cursorIndexOfWednesday = CursorUtil.getColumnIndexOrThrow(_cursor, "wednesday");
          final int _cursorIndexOfThursday = CursorUtil.getColumnIndexOrThrow(_cursor, "thursday");
          final int _cursorIndexOfFriday = CursorUtil.getColumnIndexOrThrow(_cursor, "friday");
          final int _cursorIndexOfSaturday = CursorUtil.getColumnIndexOrThrow(_cursor, "saturday");
          final int _cursorIndexOfSunday = CursorUtil.getColumnIndexOrThrow(_cursor, "sunday");
          final DayLimit _result;
          if (_cursor.moveToFirst()) {
            final long _tmpId;
            _tmpId = _cursor.getLong(_cursorIndexOfId);
            final int _tmpUserId;
            _tmpUserId = _cursor.getInt(_cursorIndexOfUserId);
            final long _tmpAllDayLimit;
            _tmpAllDayLimit = _cursor.getLong(_cursorIndexOfAllDayLimit);
            final boolean _tmpIsIncludeWhite;
            final int _tmp;
            _tmp = _cursor.getInt(_cursorIndexOfIsIncludeWhite);
            _tmpIsIncludeWhite = _tmp != 0;
            final boolean _tmpIsDenyChange;
            final int _tmp_1;
            _tmp_1 = _cursor.getInt(_cursorIndexOfIsDenyChange);
            _tmpIsDenyChange = _tmp_1 != 0;
            final String _tmpJumpDate;
            if (_cursor.isNull(_cursorIndexOfJumpDate)) {
              _tmpJumpDate = null;
            } else {
              _tmpJumpDate = _cursor.getString(_cursorIndexOfJumpDate);
            }
            final int _tmpDenyChangeLength;
            _tmpDenyChangeLength = _cursor.getInt(_cursorIndexOfDenyChangeLength);
            final boolean _tmpIsWorkDayLimit;
            final int _tmp_2;
            _tmp_2 = _cursor.getInt(_cursorIndexOfIsWorkDayLimit);
            _tmpIsWorkDayLimit = _tmp_2 != 0;
            final boolean _tmpMonday;
            final int _tmp_3;
            _tmp_3 = _cursor.getInt(_cursorIndexOfMonday);
            _tmpMonday = _tmp_3 != 0;
            final boolean _tmpTuesday;
            final int _tmp_4;
            _tmp_4 = _cursor.getInt(_cursorIndexOfTuesday);
            _tmpTuesday = _tmp_4 != 0;
            final boolean _tmpWednesday;
            final int _tmp_5;
            _tmp_5 = _cursor.getInt(_cursorIndexOfWednesday);
            _tmpWednesday = _tmp_5 != 0;
            final boolean _tmpThursday;
            final int _tmp_6;
            _tmp_6 = _cursor.getInt(_cursorIndexOfThursday);
            _tmpThursday = _tmp_6 != 0;
            final boolean _tmpFriday;
            final int _tmp_7;
            _tmp_7 = _cursor.getInt(_cursorIndexOfFriday);
            _tmpFriday = _tmp_7 != 0;
            final boolean _tmpSaturday;
            final int _tmp_8;
            _tmp_8 = _cursor.getInt(_cursorIndexOfSaturday);
            _tmpSaturday = _tmp_8 != 0;
            final boolean _tmpSunday;
            final int _tmp_9;
            _tmp_9 = _cursor.getInt(_cursorIndexOfSunday);
            _tmpSunday = _tmp_9 != 0;
            _result = new DayLimit(_tmpId,_tmpUserId,_tmpAllDayLimit,_tmpIsIncludeWhite,_tmpIsDenyChange,_tmpJumpDate,_tmpDenyChangeLength,_tmpIsWorkDayLimit,_tmpMonday,_tmpTuesday,_tmpWednesday,_tmpThursday,_tmpFriday,_tmpSaturday,_tmpSunday);
          } else {
            _result = null;
          }
          return _result;
        } finally {
          _cursor.close();
        }
      }

      @Override
      protected void finalize() {
        _statement.release();
      }
    });
  }

  @Override
  public LiveData<DayLimit> getTodayLimitsThursday(final int userId) {
    final String _sql = "SELECT * FROM DayLimit where userId = ? and thursday=1 limit 1";
    final RoomSQLiteQuery _statement = RoomSQLiteQuery.acquire(_sql, 1);
    int _argIndex = 1;
    _statement.bindLong(_argIndex, userId);
    return __db.getInvalidationTracker().createLiveData(new String[] {"DayLimit"}, false, new Callable<DayLimit>() {
      @Override
      @Nullable
      public DayLimit call() throws Exception {
        final Cursor _cursor = DBUtil.query(__db, _statement, false, null);
        try {
          final int _cursorIndexOfId = CursorUtil.getColumnIndexOrThrow(_cursor, "id");
          final int _cursorIndexOfUserId = CursorUtil.getColumnIndexOrThrow(_cursor, "userId");
          final int _cursorIndexOfAllDayLimit = CursorUtil.getColumnIndexOrThrow(_cursor, "allDayLimit");
          final int _cursorIndexOfIsIncludeWhite = CursorUtil.getColumnIndexOrThrow(_cursor, "isIncludeWhite");
          final int _cursorIndexOfIsDenyChange = CursorUtil.getColumnIndexOrThrow(_cursor, "isDenyChange");
          final int _cursorIndexOfJumpDate = CursorUtil.getColumnIndexOrThrow(_cursor, "jumpDate");
          final int _cursorIndexOfDenyChangeLength = CursorUtil.getColumnIndexOrThrow(_cursor, "denyChangeLength");
          final int _cursorIndexOfIsWorkDayLimit = CursorUtil.getColumnIndexOrThrow(_cursor, "isWorkDayLimit");
          final int _cursorIndexOfMonday = CursorUtil.getColumnIndexOrThrow(_cursor, "monday");
          final int _cursorIndexOfTuesday = CursorUtil.getColumnIndexOrThrow(_cursor, "tuesday");
          final int _cursorIndexOfWednesday = CursorUtil.getColumnIndexOrThrow(_cursor, "wednesday");
          final int _cursorIndexOfThursday = CursorUtil.getColumnIndexOrThrow(_cursor, "thursday");
          final int _cursorIndexOfFriday = CursorUtil.getColumnIndexOrThrow(_cursor, "friday");
          final int _cursorIndexOfSaturday = CursorUtil.getColumnIndexOrThrow(_cursor, "saturday");
          final int _cursorIndexOfSunday = CursorUtil.getColumnIndexOrThrow(_cursor, "sunday");
          final DayLimit _result;
          if (_cursor.moveToFirst()) {
            final long _tmpId;
            _tmpId = _cursor.getLong(_cursorIndexOfId);
            final int _tmpUserId;
            _tmpUserId = _cursor.getInt(_cursorIndexOfUserId);
            final long _tmpAllDayLimit;
            _tmpAllDayLimit = _cursor.getLong(_cursorIndexOfAllDayLimit);
            final boolean _tmpIsIncludeWhite;
            final int _tmp;
            _tmp = _cursor.getInt(_cursorIndexOfIsIncludeWhite);
            _tmpIsIncludeWhite = _tmp != 0;
            final boolean _tmpIsDenyChange;
            final int _tmp_1;
            _tmp_1 = _cursor.getInt(_cursorIndexOfIsDenyChange);
            _tmpIsDenyChange = _tmp_1 != 0;
            final String _tmpJumpDate;
            if (_cursor.isNull(_cursorIndexOfJumpDate)) {
              _tmpJumpDate = null;
            } else {
              _tmpJumpDate = _cursor.getString(_cursorIndexOfJumpDate);
            }
            final int _tmpDenyChangeLength;
            _tmpDenyChangeLength = _cursor.getInt(_cursorIndexOfDenyChangeLength);
            final boolean _tmpIsWorkDayLimit;
            final int _tmp_2;
            _tmp_2 = _cursor.getInt(_cursorIndexOfIsWorkDayLimit);
            _tmpIsWorkDayLimit = _tmp_2 != 0;
            final boolean _tmpMonday;
            final int _tmp_3;
            _tmp_3 = _cursor.getInt(_cursorIndexOfMonday);
            _tmpMonday = _tmp_3 != 0;
            final boolean _tmpTuesday;
            final int _tmp_4;
            _tmp_4 = _cursor.getInt(_cursorIndexOfTuesday);
            _tmpTuesday = _tmp_4 != 0;
            final boolean _tmpWednesday;
            final int _tmp_5;
            _tmp_5 = _cursor.getInt(_cursorIndexOfWednesday);
            _tmpWednesday = _tmp_5 != 0;
            final boolean _tmpThursday;
            final int _tmp_6;
            _tmp_6 = _cursor.getInt(_cursorIndexOfThursday);
            _tmpThursday = _tmp_6 != 0;
            final boolean _tmpFriday;
            final int _tmp_7;
            _tmp_7 = _cursor.getInt(_cursorIndexOfFriday);
            _tmpFriday = _tmp_7 != 0;
            final boolean _tmpSaturday;
            final int _tmp_8;
            _tmp_8 = _cursor.getInt(_cursorIndexOfSaturday);
            _tmpSaturday = _tmp_8 != 0;
            final boolean _tmpSunday;
            final int _tmp_9;
            _tmp_9 = _cursor.getInt(_cursorIndexOfSunday);
            _tmpSunday = _tmp_9 != 0;
            _result = new DayLimit(_tmpId,_tmpUserId,_tmpAllDayLimit,_tmpIsIncludeWhite,_tmpIsDenyChange,_tmpJumpDate,_tmpDenyChangeLength,_tmpIsWorkDayLimit,_tmpMonday,_tmpTuesday,_tmpWednesday,_tmpThursday,_tmpFriday,_tmpSaturday,_tmpSunday);
          } else {
            _result = null;
          }
          return _result;
        } finally {
          _cursor.close();
        }
      }

      @Override
      protected void finalize() {
        _statement.release();
      }
    });
  }

  @Override
  public LiveData<DayLimit> getTodayLimitsFriday(final int userId) {
    final String _sql = "SELECT * FROM DayLimit where userId = ? and friday=1 limit 1";
    final RoomSQLiteQuery _statement = RoomSQLiteQuery.acquire(_sql, 1);
    int _argIndex = 1;
    _statement.bindLong(_argIndex, userId);
    return __db.getInvalidationTracker().createLiveData(new String[] {"DayLimit"}, false, new Callable<DayLimit>() {
      @Override
      @Nullable
      public DayLimit call() throws Exception {
        final Cursor _cursor = DBUtil.query(__db, _statement, false, null);
        try {
          final int _cursorIndexOfId = CursorUtil.getColumnIndexOrThrow(_cursor, "id");
          final int _cursorIndexOfUserId = CursorUtil.getColumnIndexOrThrow(_cursor, "userId");
          final int _cursorIndexOfAllDayLimit = CursorUtil.getColumnIndexOrThrow(_cursor, "allDayLimit");
          final int _cursorIndexOfIsIncludeWhite = CursorUtil.getColumnIndexOrThrow(_cursor, "isIncludeWhite");
          final int _cursorIndexOfIsDenyChange = CursorUtil.getColumnIndexOrThrow(_cursor, "isDenyChange");
          final int _cursorIndexOfJumpDate = CursorUtil.getColumnIndexOrThrow(_cursor, "jumpDate");
          final int _cursorIndexOfDenyChangeLength = CursorUtil.getColumnIndexOrThrow(_cursor, "denyChangeLength");
          final int _cursorIndexOfIsWorkDayLimit = CursorUtil.getColumnIndexOrThrow(_cursor, "isWorkDayLimit");
          final int _cursorIndexOfMonday = CursorUtil.getColumnIndexOrThrow(_cursor, "monday");
          final int _cursorIndexOfTuesday = CursorUtil.getColumnIndexOrThrow(_cursor, "tuesday");
          final int _cursorIndexOfWednesday = CursorUtil.getColumnIndexOrThrow(_cursor, "wednesday");
          final int _cursorIndexOfThursday = CursorUtil.getColumnIndexOrThrow(_cursor, "thursday");
          final int _cursorIndexOfFriday = CursorUtil.getColumnIndexOrThrow(_cursor, "friday");
          final int _cursorIndexOfSaturday = CursorUtil.getColumnIndexOrThrow(_cursor, "saturday");
          final int _cursorIndexOfSunday = CursorUtil.getColumnIndexOrThrow(_cursor, "sunday");
          final DayLimit _result;
          if (_cursor.moveToFirst()) {
            final long _tmpId;
            _tmpId = _cursor.getLong(_cursorIndexOfId);
            final int _tmpUserId;
            _tmpUserId = _cursor.getInt(_cursorIndexOfUserId);
            final long _tmpAllDayLimit;
            _tmpAllDayLimit = _cursor.getLong(_cursorIndexOfAllDayLimit);
            final boolean _tmpIsIncludeWhite;
            final int _tmp;
            _tmp = _cursor.getInt(_cursorIndexOfIsIncludeWhite);
            _tmpIsIncludeWhite = _tmp != 0;
            final boolean _tmpIsDenyChange;
            final int _tmp_1;
            _tmp_1 = _cursor.getInt(_cursorIndexOfIsDenyChange);
            _tmpIsDenyChange = _tmp_1 != 0;
            final String _tmpJumpDate;
            if (_cursor.isNull(_cursorIndexOfJumpDate)) {
              _tmpJumpDate = null;
            } else {
              _tmpJumpDate = _cursor.getString(_cursorIndexOfJumpDate);
            }
            final int _tmpDenyChangeLength;
            _tmpDenyChangeLength = _cursor.getInt(_cursorIndexOfDenyChangeLength);
            final boolean _tmpIsWorkDayLimit;
            final int _tmp_2;
            _tmp_2 = _cursor.getInt(_cursorIndexOfIsWorkDayLimit);
            _tmpIsWorkDayLimit = _tmp_2 != 0;
            final boolean _tmpMonday;
            final int _tmp_3;
            _tmp_3 = _cursor.getInt(_cursorIndexOfMonday);
            _tmpMonday = _tmp_3 != 0;
            final boolean _tmpTuesday;
            final int _tmp_4;
            _tmp_4 = _cursor.getInt(_cursorIndexOfTuesday);
            _tmpTuesday = _tmp_4 != 0;
            final boolean _tmpWednesday;
            final int _tmp_5;
            _tmp_5 = _cursor.getInt(_cursorIndexOfWednesday);
            _tmpWednesday = _tmp_5 != 0;
            final boolean _tmpThursday;
            final int _tmp_6;
            _tmp_6 = _cursor.getInt(_cursorIndexOfThursday);
            _tmpThursday = _tmp_6 != 0;
            final boolean _tmpFriday;
            final int _tmp_7;
            _tmp_7 = _cursor.getInt(_cursorIndexOfFriday);
            _tmpFriday = _tmp_7 != 0;
            final boolean _tmpSaturday;
            final int _tmp_8;
            _tmp_8 = _cursor.getInt(_cursorIndexOfSaturday);
            _tmpSaturday = _tmp_8 != 0;
            final boolean _tmpSunday;
            final int _tmp_9;
            _tmp_9 = _cursor.getInt(_cursorIndexOfSunday);
            _tmpSunday = _tmp_9 != 0;
            _result = new DayLimit(_tmpId,_tmpUserId,_tmpAllDayLimit,_tmpIsIncludeWhite,_tmpIsDenyChange,_tmpJumpDate,_tmpDenyChangeLength,_tmpIsWorkDayLimit,_tmpMonday,_tmpTuesday,_tmpWednesday,_tmpThursday,_tmpFriday,_tmpSaturday,_tmpSunday);
          } else {
            _result = null;
          }
          return _result;
        } finally {
          _cursor.close();
        }
      }

      @Override
      protected void finalize() {
        _statement.release();
      }
    });
  }

  @Override
  public LiveData<DayLimit> getTodayLimitsSaturday(final int userId) {
    final String _sql = "SELECT * FROM DayLimit where userId = ? and saturday=1 limit 1";
    final RoomSQLiteQuery _statement = RoomSQLiteQuery.acquire(_sql, 1);
    int _argIndex = 1;
    _statement.bindLong(_argIndex, userId);
    return __db.getInvalidationTracker().createLiveData(new String[] {"DayLimit"}, false, new Callable<DayLimit>() {
      @Override
      @Nullable
      public DayLimit call() throws Exception {
        final Cursor _cursor = DBUtil.query(__db, _statement, false, null);
        try {
          final int _cursorIndexOfId = CursorUtil.getColumnIndexOrThrow(_cursor, "id");
          final int _cursorIndexOfUserId = CursorUtil.getColumnIndexOrThrow(_cursor, "userId");
          final int _cursorIndexOfAllDayLimit = CursorUtil.getColumnIndexOrThrow(_cursor, "allDayLimit");
          final int _cursorIndexOfIsIncludeWhite = CursorUtil.getColumnIndexOrThrow(_cursor, "isIncludeWhite");
          final int _cursorIndexOfIsDenyChange = CursorUtil.getColumnIndexOrThrow(_cursor, "isDenyChange");
          final int _cursorIndexOfJumpDate = CursorUtil.getColumnIndexOrThrow(_cursor, "jumpDate");
          final int _cursorIndexOfDenyChangeLength = CursorUtil.getColumnIndexOrThrow(_cursor, "denyChangeLength");
          final int _cursorIndexOfIsWorkDayLimit = CursorUtil.getColumnIndexOrThrow(_cursor, "isWorkDayLimit");
          final int _cursorIndexOfMonday = CursorUtil.getColumnIndexOrThrow(_cursor, "monday");
          final int _cursorIndexOfTuesday = CursorUtil.getColumnIndexOrThrow(_cursor, "tuesday");
          final int _cursorIndexOfWednesday = CursorUtil.getColumnIndexOrThrow(_cursor, "wednesday");
          final int _cursorIndexOfThursday = CursorUtil.getColumnIndexOrThrow(_cursor, "thursday");
          final int _cursorIndexOfFriday = CursorUtil.getColumnIndexOrThrow(_cursor, "friday");
          final int _cursorIndexOfSaturday = CursorUtil.getColumnIndexOrThrow(_cursor, "saturday");
          final int _cursorIndexOfSunday = CursorUtil.getColumnIndexOrThrow(_cursor, "sunday");
          final DayLimit _result;
          if (_cursor.moveToFirst()) {
            final long _tmpId;
            _tmpId = _cursor.getLong(_cursorIndexOfId);
            final int _tmpUserId;
            _tmpUserId = _cursor.getInt(_cursorIndexOfUserId);
            final long _tmpAllDayLimit;
            _tmpAllDayLimit = _cursor.getLong(_cursorIndexOfAllDayLimit);
            final boolean _tmpIsIncludeWhite;
            final int _tmp;
            _tmp = _cursor.getInt(_cursorIndexOfIsIncludeWhite);
            _tmpIsIncludeWhite = _tmp != 0;
            final boolean _tmpIsDenyChange;
            final int _tmp_1;
            _tmp_1 = _cursor.getInt(_cursorIndexOfIsDenyChange);
            _tmpIsDenyChange = _tmp_1 != 0;
            final String _tmpJumpDate;
            if (_cursor.isNull(_cursorIndexOfJumpDate)) {
              _tmpJumpDate = null;
            } else {
              _tmpJumpDate = _cursor.getString(_cursorIndexOfJumpDate);
            }
            final int _tmpDenyChangeLength;
            _tmpDenyChangeLength = _cursor.getInt(_cursorIndexOfDenyChangeLength);
            final boolean _tmpIsWorkDayLimit;
            final int _tmp_2;
            _tmp_2 = _cursor.getInt(_cursorIndexOfIsWorkDayLimit);
            _tmpIsWorkDayLimit = _tmp_2 != 0;
            final boolean _tmpMonday;
            final int _tmp_3;
            _tmp_3 = _cursor.getInt(_cursorIndexOfMonday);
            _tmpMonday = _tmp_3 != 0;
            final boolean _tmpTuesday;
            final int _tmp_4;
            _tmp_4 = _cursor.getInt(_cursorIndexOfTuesday);
            _tmpTuesday = _tmp_4 != 0;
            final boolean _tmpWednesday;
            final int _tmp_5;
            _tmp_5 = _cursor.getInt(_cursorIndexOfWednesday);
            _tmpWednesday = _tmp_5 != 0;
            final boolean _tmpThursday;
            final int _tmp_6;
            _tmp_6 = _cursor.getInt(_cursorIndexOfThursday);
            _tmpThursday = _tmp_6 != 0;
            final boolean _tmpFriday;
            final int _tmp_7;
            _tmp_7 = _cursor.getInt(_cursorIndexOfFriday);
            _tmpFriday = _tmp_7 != 0;
            final boolean _tmpSaturday;
            final int _tmp_8;
            _tmp_8 = _cursor.getInt(_cursorIndexOfSaturday);
            _tmpSaturday = _tmp_8 != 0;
            final boolean _tmpSunday;
            final int _tmp_9;
            _tmp_9 = _cursor.getInt(_cursorIndexOfSunday);
            _tmpSunday = _tmp_9 != 0;
            _result = new DayLimit(_tmpId,_tmpUserId,_tmpAllDayLimit,_tmpIsIncludeWhite,_tmpIsDenyChange,_tmpJumpDate,_tmpDenyChangeLength,_tmpIsWorkDayLimit,_tmpMonday,_tmpTuesday,_tmpWednesday,_tmpThursday,_tmpFriday,_tmpSaturday,_tmpSunday);
          } else {
            _result = null;
          }
          return _result;
        } finally {
          _cursor.close();
        }
      }

      @Override
      protected void finalize() {
        _statement.release();
      }
    });
  }

  @Override
  public LiveData<DayLimit> getTodayLimitsSunday(final int userId) {
    final String _sql = "SELECT * FROM DayLimit where userId = ? and sunday=1 limit 1";
    final RoomSQLiteQuery _statement = RoomSQLiteQuery.acquire(_sql, 1);
    int _argIndex = 1;
    _statement.bindLong(_argIndex, userId);
    return __db.getInvalidationTracker().createLiveData(new String[] {"DayLimit"}, false, new Callable<DayLimit>() {
      @Override
      @Nullable
      public DayLimit call() throws Exception {
        final Cursor _cursor = DBUtil.query(__db, _statement, false, null);
        try {
          final int _cursorIndexOfId = CursorUtil.getColumnIndexOrThrow(_cursor, "id");
          final int _cursorIndexOfUserId = CursorUtil.getColumnIndexOrThrow(_cursor, "userId");
          final int _cursorIndexOfAllDayLimit = CursorUtil.getColumnIndexOrThrow(_cursor, "allDayLimit");
          final int _cursorIndexOfIsIncludeWhite = CursorUtil.getColumnIndexOrThrow(_cursor, "isIncludeWhite");
          final int _cursorIndexOfIsDenyChange = CursorUtil.getColumnIndexOrThrow(_cursor, "isDenyChange");
          final int _cursorIndexOfJumpDate = CursorUtil.getColumnIndexOrThrow(_cursor, "jumpDate");
          final int _cursorIndexOfDenyChangeLength = CursorUtil.getColumnIndexOrThrow(_cursor, "denyChangeLength");
          final int _cursorIndexOfIsWorkDayLimit = CursorUtil.getColumnIndexOrThrow(_cursor, "isWorkDayLimit");
          final int _cursorIndexOfMonday = CursorUtil.getColumnIndexOrThrow(_cursor, "monday");
          final int _cursorIndexOfTuesday = CursorUtil.getColumnIndexOrThrow(_cursor, "tuesday");
          final int _cursorIndexOfWednesday = CursorUtil.getColumnIndexOrThrow(_cursor, "wednesday");
          final int _cursorIndexOfThursday = CursorUtil.getColumnIndexOrThrow(_cursor, "thursday");
          final int _cursorIndexOfFriday = CursorUtil.getColumnIndexOrThrow(_cursor, "friday");
          final int _cursorIndexOfSaturday = CursorUtil.getColumnIndexOrThrow(_cursor, "saturday");
          final int _cursorIndexOfSunday = CursorUtil.getColumnIndexOrThrow(_cursor, "sunday");
          final DayLimit _result;
          if (_cursor.moveToFirst()) {
            final long _tmpId;
            _tmpId = _cursor.getLong(_cursorIndexOfId);
            final int _tmpUserId;
            _tmpUserId = _cursor.getInt(_cursorIndexOfUserId);
            final long _tmpAllDayLimit;
            _tmpAllDayLimit = _cursor.getLong(_cursorIndexOfAllDayLimit);
            final boolean _tmpIsIncludeWhite;
            final int _tmp;
            _tmp = _cursor.getInt(_cursorIndexOfIsIncludeWhite);
            _tmpIsIncludeWhite = _tmp != 0;
            final boolean _tmpIsDenyChange;
            final int _tmp_1;
            _tmp_1 = _cursor.getInt(_cursorIndexOfIsDenyChange);
            _tmpIsDenyChange = _tmp_1 != 0;
            final String _tmpJumpDate;
            if (_cursor.isNull(_cursorIndexOfJumpDate)) {
              _tmpJumpDate = null;
            } else {
              _tmpJumpDate = _cursor.getString(_cursorIndexOfJumpDate);
            }
            final int _tmpDenyChangeLength;
            _tmpDenyChangeLength = _cursor.getInt(_cursorIndexOfDenyChangeLength);
            final boolean _tmpIsWorkDayLimit;
            final int _tmp_2;
            _tmp_2 = _cursor.getInt(_cursorIndexOfIsWorkDayLimit);
            _tmpIsWorkDayLimit = _tmp_2 != 0;
            final boolean _tmpMonday;
            final int _tmp_3;
            _tmp_3 = _cursor.getInt(_cursorIndexOfMonday);
            _tmpMonday = _tmp_3 != 0;
            final boolean _tmpTuesday;
            final int _tmp_4;
            _tmp_4 = _cursor.getInt(_cursorIndexOfTuesday);
            _tmpTuesday = _tmp_4 != 0;
            final boolean _tmpWednesday;
            final int _tmp_5;
            _tmp_5 = _cursor.getInt(_cursorIndexOfWednesday);
            _tmpWednesday = _tmp_5 != 0;
            final boolean _tmpThursday;
            final int _tmp_6;
            _tmp_6 = _cursor.getInt(_cursorIndexOfThursday);
            _tmpThursday = _tmp_6 != 0;
            final boolean _tmpFriday;
            final int _tmp_7;
            _tmp_7 = _cursor.getInt(_cursorIndexOfFriday);
            _tmpFriday = _tmp_7 != 0;
            final boolean _tmpSaturday;
            final int _tmp_8;
            _tmp_8 = _cursor.getInt(_cursorIndexOfSaturday);
            _tmpSaturday = _tmp_8 != 0;
            final boolean _tmpSunday;
            final int _tmp_9;
            _tmp_9 = _cursor.getInt(_cursorIndexOfSunday);
            _tmpSunday = _tmp_9 != 0;
            _result = new DayLimit(_tmpId,_tmpUserId,_tmpAllDayLimit,_tmpIsIncludeWhite,_tmpIsDenyChange,_tmpJumpDate,_tmpDenyChangeLength,_tmpIsWorkDayLimit,_tmpMonday,_tmpTuesday,_tmpWednesday,_tmpThursday,_tmpFriday,_tmpSaturday,_tmpSunday);
          } else {
            _result = null;
          }
          return _result;
        } finally {
          _cursor.close();
        }
      }

      @Override
      protected void finalize() {
        _statement.release();
      }
    });
  }

  @Override
  public DayLimit getTodayLimitsImmediatelyMonday(final int userId) {
    final String _sql = "SELECT * FROM DayLimit where userId = ? and monday=1 limit 1";
    final RoomSQLiteQuery _statement = RoomSQLiteQuery.acquire(_sql, 1);
    int _argIndex = 1;
    _statement.bindLong(_argIndex, userId);
    __db.assertNotSuspendingTransaction();
    final Cursor _cursor = DBUtil.query(__db, _statement, false, null);
    try {
      final int _cursorIndexOfId = CursorUtil.getColumnIndexOrThrow(_cursor, "id");
      final int _cursorIndexOfUserId = CursorUtil.getColumnIndexOrThrow(_cursor, "userId");
      final int _cursorIndexOfAllDayLimit = CursorUtil.getColumnIndexOrThrow(_cursor, "allDayLimit");
      final int _cursorIndexOfIsIncludeWhite = CursorUtil.getColumnIndexOrThrow(_cursor, "isIncludeWhite");
      final int _cursorIndexOfIsDenyChange = CursorUtil.getColumnIndexOrThrow(_cursor, "isDenyChange");
      final int _cursorIndexOfJumpDate = CursorUtil.getColumnIndexOrThrow(_cursor, "jumpDate");
      final int _cursorIndexOfDenyChangeLength = CursorUtil.getColumnIndexOrThrow(_cursor, "denyChangeLength");
      final int _cursorIndexOfIsWorkDayLimit = CursorUtil.getColumnIndexOrThrow(_cursor, "isWorkDayLimit");
      final int _cursorIndexOfMonday = CursorUtil.getColumnIndexOrThrow(_cursor, "monday");
      final int _cursorIndexOfTuesday = CursorUtil.getColumnIndexOrThrow(_cursor, "tuesday");
      final int _cursorIndexOfWednesday = CursorUtil.getColumnIndexOrThrow(_cursor, "wednesday");
      final int _cursorIndexOfThursday = CursorUtil.getColumnIndexOrThrow(_cursor, "thursday");
      final int _cursorIndexOfFriday = CursorUtil.getColumnIndexOrThrow(_cursor, "friday");
      final int _cursorIndexOfSaturday = CursorUtil.getColumnIndexOrThrow(_cursor, "saturday");
      final int _cursorIndexOfSunday = CursorUtil.getColumnIndexOrThrow(_cursor, "sunday");
      final DayLimit _result;
      if (_cursor.moveToFirst()) {
        final long _tmpId;
        _tmpId = _cursor.getLong(_cursorIndexOfId);
        final int _tmpUserId;
        _tmpUserId = _cursor.getInt(_cursorIndexOfUserId);
        final long _tmpAllDayLimit;
        _tmpAllDayLimit = _cursor.getLong(_cursorIndexOfAllDayLimit);
        final boolean _tmpIsIncludeWhite;
        final int _tmp;
        _tmp = _cursor.getInt(_cursorIndexOfIsIncludeWhite);
        _tmpIsIncludeWhite = _tmp != 0;
        final boolean _tmpIsDenyChange;
        final int _tmp_1;
        _tmp_1 = _cursor.getInt(_cursorIndexOfIsDenyChange);
        _tmpIsDenyChange = _tmp_1 != 0;
        final String _tmpJumpDate;
        if (_cursor.isNull(_cursorIndexOfJumpDate)) {
          _tmpJumpDate = null;
        } else {
          _tmpJumpDate = _cursor.getString(_cursorIndexOfJumpDate);
        }
        final int _tmpDenyChangeLength;
        _tmpDenyChangeLength = _cursor.getInt(_cursorIndexOfDenyChangeLength);
        final boolean _tmpIsWorkDayLimit;
        final int _tmp_2;
        _tmp_2 = _cursor.getInt(_cursorIndexOfIsWorkDayLimit);
        _tmpIsWorkDayLimit = _tmp_2 != 0;
        final boolean _tmpMonday;
        final int _tmp_3;
        _tmp_3 = _cursor.getInt(_cursorIndexOfMonday);
        _tmpMonday = _tmp_3 != 0;
        final boolean _tmpTuesday;
        final int _tmp_4;
        _tmp_4 = _cursor.getInt(_cursorIndexOfTuesday);
        _tmpTuesday = _tmp_4 != 0;
        final boolean _tmpWednesday;
        final int _tmp_5;
        _tmp_5 = _cursor.getInt(_cursorIndexOfWednesday);
        _tmpWednesday = _tmp_5 != 0;
        final boolean _tmpThursday;
        final int _tmp_6;
        _tmp_6 = _cursor.getInt(_cursorIndexOfThursday);
        _tmpThursday = _tmp_6 != 0;
        final boolean _tmpFriday;
        final int _tmp_7;
        _tmp_7 = _cursor.getInt(_cursorIndexOfFriday);
        _tmpFriday = _tmp_7 != 0;
        final boolean _tmpSaturday;
        final int _tmp_8;
        _tmp_8 = _cursor.getInt(_cursorIndexOfSaturday);
        _tmpSaturday = _tmp_8 != 0;
        final boolean _tmpSunday;
        final int _tmp_9;
        _tmp_9 = _cursor.getInt(_cursorIndexOfSunday);
        _tmpSunday = _tmp_9 != 0;
        _result = new DayLimit(_tmpId,_tmpUserId,_tmpAllDayLimit,_tmpIsIncludeWhite,_tmpIsDenyChange,_tmpJumpDate,_tmpDenyChangeLength,_tmpIsWorkDayLimit,_tmpMonday,_tmpTuesday,_tmpWednesday,_tmpThursday,_tmpFriday,_tmpSaturday,_tmpSunday);
      } else {
        _result = null;
      }
      return _result;
    } finally {
      _cursor.close();
      _statement.release();
    }
  }

  @Override
  public DayLimit getTodayLimitsImmediatelyTuesday(final int userId) {
    final String _sql = "SELECT * FROM DayLimit where userId = ? and tuesday=1 limit 1";
    final RoomSQLiteQuery _statement = RoomSQLiteQuery.acquire(_sql, 1);
    int _argIndex = 1;
    _statement.bindLong(_argIndex, userId);
    __db.assertNotSuspendingTransaction();
    final Cursor _cursor = DBUtil.query(__db, _statement, false, null);
    try {
      final int _cursorIndexOfId = CursorUtil.getColumnIndexOrThrow(_cursor, "id");
      final int _cursorIndexOfUserId = CursorUtil.getColumnIndexOrThrow(_cursor, "userId");
      final int _cursorIndexOfAllDayLimit = CursorUtil.getColumnIndexOrThrow(_cursor, "allDayLimit");
      final int _cursorIndexOfIsIncludeWhite = CursorUtil.getColumnIndexOrThrow(_cursor, "isIncludeWhite");
      final int _cursorIndexOfIsDenyChange = CursorUtil.getColumnIndexOrThrow(_cursor, "isDenyChange");
      final int _cursorIndexOfJumpDate = CursorUtil.getColumnIndexOrThrow(_cursor, "jumpDate");
      final int _cursorIndexOfDenyChangeLength = CursorUtil.getColumnIndexOrThrow(_cursor, "denyChangeLength");
      final int _cursorIndexOfIsWorkDayLimit = CursorUtil.getColumnIndexOrThrow(_cursor, "isWorkDayLimit");
      final int _cursorIndexOfMonday = CursorUtil.getColumnIndexOrThrow(_cursor, "monday");
      final int _cursorIndexOfTuesday = CursorUtil.getColumnIndexOrThrow(_cursor, "tuesday");
      final int _cursorIndexOfWednesday = CursorUtil.getColumnIndexOrThrow(_cursor, "wednesday");
      final int _cursorIndexOfThursday = CursorUtil.getColumnIndexOrThrow(_cursor, "thursday");
      final int _cursorIndexOfFriday = CursorUtil.getColumnIndexOrThrow(_cursor, "friday");
      final int _cursorIndexOfSaturday = CursorUtil.getColumnIndexOrThrow(_cursor, "saturday");
      final int _cursorIndexOfSunday = CursorUtil.getColumnIndexOrThrow(_cursor, "sunday");
      final DayLimit _result;
      if (_cursor.moveToFirst()) {
        final long _tmpId;
        _tmpId = _cursor.getLong(_cursorIndexOfId);
        final int _tmpUserId;
        _tmpUserId = _cursor.getInt(_cursorIndexOfUserId);
        final long _tmpAllDayLimit;
        _tmpAllDayLimit = _cursor.getLong(_cursorIndexOfAllDayLimit);
        final boolean _tmpIsIncludeWhite;
        final int _tmp;
        _tmp = _cursor.getInt(_cursorIndexOfIsIncludeWhite);
        _tmpIsIncludeWhite = _tmp != 0;
        final boolean _tmpIsDenyChange;
        final int _tmp_1;
        _tmp_1 = _cursor.getInt(_cursorIndexOfIsDenyChange);
        _tmpIsDenyChange = _tmp_1 != 0;
        final String _tmpJumpDate;
        if (_cursor.isNull(_cursorIndexOfJumpDate)) {
          _tmpJumpDate = null;
        } else {
          _tmpJumpDate = _cursor.getString(_cursorIndexOfJumpDate);
        }
        final int _tmpDenyChangeLength;
        _tmpDenyChangeLength = _cursor.getInt(_cursorIndexOfDenyChangeLength);
        final boolean _tmpIsWorkDayLimit;
        final int _tmp_2;
        _tmp_2 = _cursor.getInt(_cursorIndexOfIsWorkDayLimit);
        _tmpIsWorkDayLimit = _tmp_2 != 0;
        final boolean _tmpMonday;
        final int _tmp_3;
        _tmp_3 = _cursor.getInt(_cursorIndexOfMonday);
        _tmpMonday = _tmp_3 != 0;
        final boolean _tmpTuesday;
        final int _tmp_4;
        _tmp_4 = _cursor.getInt(_cursorIndexOfTuesday);
        _tmpTuesday = _tmp_4 != 0;
        final boolean _tmpWednesday;
        final int _tmp_5;
        _tmp_5 = _cursor.getInt(_cursorIndexOfWednesday);
        _tmpWednesday = _tmp_5 != 0;
        final boolean _tmpThursday;
        final int _tmp_6;
        _tmp_6 = _cursor.getInt(_cursorIndexOfThursday);
        _tmpThursday = _tmp_6 != 0;
        final boolean _tmpFriday;
        final int _tmp_7;
        _tmp_7 = _cursor.getInt(_cursorIndexOfFriday);
        _tmpFriday = _tmp_7 != 0;
        final boolean _tmpSaturday;
        final int _tmp_8;
        _tmp_8 = _cursor.getInt(_cursorIndexOfSaturday);
        _tmpSaturday = _tmp_8 != 0;
        final boolean _tmpSunday;
        final int _tmp_9;
        _tmp_9 = _cursor.getInt(_cursorIndexOfSunday);
        _tmpSunday = _tmp_9 != 0;
        _result = new DayLimit(_tmpId,_tmpUserId,_tmpAllDayLimit,_tmpIsIncludeWhite,_tmpIsDenyChange,_tmpJumpDate,_tmpDenyChangeLength,_tmpIsWorkDayLimit,_tmpMonday,_tmpTuesday,_tmpWednesday,_tmpThursday,_tmpFriday,_tmpSaturday,_tmpSunday);
      } else {
        _result = null;
      }
      return _result;
    } finally {
      _cursor.close();
      _statement.release();
    }
  }

  @Override
  public DayLimit getTodayLimitsImmediatelyWednesday(final int userId) {
    final String _sql = "SELECT * FROM DayLimit where userId = ? and wednesday=1 limit 1";
    final RoomSQLiteQuery _statement = RoomSQLiteQuery.acquire(_sql, 1);
    int _argIndex = 1;
    _statement.bindLong(_argIndex, userId);
    __db.assertNotSuspendingTransaction();
    final Cursor _cursor = DBUtil.query(__db, _statement, false, null);
    try {
      final int _cursorIndexOfId = CursorUtil.getColumnIndexOrThrow(_cursor, "id");
      final int _cursorIndexOfUserId = CursorUtil.getColumnIndexOrThrow(_cursor, "userId");
      final int _cursorIndexOfAllDayLimit = CursorUtil.getColumnIndexOrThrow(_cursor, "allDayLimit");
      final int _cursorIndexOfIsIncludeWhite = CursorUtil.getColumnIndexOrThrow(_cursor, "isIncludeWhite");
      final int _cursorIndexOfIsDenyChange = CursorUtil.getColumnIndexOrThrow(_cursor, "isDenyChange");
      final int _cursorIndexOfJumpDate = CursorUtil.getColumnIndexOrThrow(_cursor, "jumpDate");
      final int _cursorIndexOfDenyChangeLength = CursorUtil.getColumnIndexOrThrow(_cursor, "denyChangeLength");
      final int _cursorIndexOfIsWorkDayLimit = CursorUtil.getColumnIndexOrThrow(_cursor, "isWorkDayLimit");
      final int _cursorIndexOfMonday = CursorUtil.getColumnIndexOrThrow(_cursor, "monday");
      final int _cursorIndexOfTuesday = CursorUtil.getColumnIndexOrThrow(_cursor, "tuesday");
      final int _cursorIndexOfWednesday = CursorUtil.getColumnIndexOrThrow(_cursor, "wednesday");
      final int _cursorIndexOfThursday = CursorUtil.getColumnIndexOrThrow(_cursor, "thursday");
      final int _cursorIndexOfFriday = CursorUtil.getColumnIndexOrThrow(_cursor, "friday");
      final int _cursorIndexOfSaturday = CursorUtil.getColumnIndexOrThrow(_cursor, "saturday");
      final int _cursorIndexOfSunday = CursorUtil.getColumnIndexOrThrow(_cursor, "sunday");
      final DayLimit _result;
      if (_cursor.moveToFirst()) {
        final long _tmpId;
        _tmpId = _cursor.getLong(_cursorIndexOfId);
        final int _tmpUserId;
        _tmpUserId = _cursor.getInt(_cursorIndexOfUserId);
        final long _tmpAllDayLimit;
        _tmpAllDayLimit = _cursor.getLong(_cursorIndexOfAllDayLimit);
        final boolean _tmpIsIncludeWhite;
        final int _tmp;
        _tmp = _cursor.getInt(_cursorIndexOfIsIncludeWhite);
        _tmpIsIncludeWhite = _tmp != 0;
        final boolean _tmpIsDenyChange;
        final int _tmp_1;
        _tmp_1 = _cursor.getInt(_cursorIndexOfIsDenyChange);
        _tmpIsDenyChange = _tmp_1 != 0;
        final String _tmpJumpDate;
        if (_cursor.isNull(_cursorIndexOfJumpDate)) {
          _tmpJumpDate = null;
        } else {
          _tmpJumpDate = _cursor.getString(_cursorIndexOfJumpDate);
        }
        final int _tmpDenyChangeLength;
        _tmpDenyChangeLength = _cursor.getInt(_cursorIndexOfDenyChangeLength);
        final boolean _tmpIsWorkDayLimit;
        final int _tmp_2;
        _tmp_2 = _cursor.getInt(_cursorIndexOfIsWorkDayLimit);
        _tmpIsWorkDayLimit = _tmp_2 != 0;
        final boolean _tmpMonday;
        final int _tmp_3;
        _tmp_3 = _cursor.getInt(_cursorIndexOfMonday);
        _tmpMonday = _tmp_3 != 0;
        final boolean _tmpTuesday;
        final int _tmp_4;
        _tmp_4 = _cursor.getInt(_cursorIndexOfTuesday);
        _tmpTuesday = _tmp_4 != 0;
        final boolean _tmpWednesday;
        final int _tmp_5;
        _tmp_5 = _cursor.getInt(_cursorIndexOfWednesday);
        _tmpWednesday = _tmp_5 != 0;
        final boolean _tmpThursday;
        final int _tmp_6;
        _tmp_6 = _cursor.getInt(_cursorIndexOfThursday);
        _tmpThursday = _tmp_6 != 0;
        final boolean _tmpFriday;
        final int _tmp_7;
        _tmp_7 = _cursor.getInt(_cursorIndexOfFriday);
        _tmpFriday = _tmp_7 != 0;
        final boolean _tmpSaturday;
        final int _tmp_8;
        _tmp_8 = _cursor.getInt(_cursorIndexOfSaturday);
        _tmpSaturday = _tmp_8 != 0;
        final boolean _tmpSunday;
        final int _tmp_9;
        _tmp_9 = _cursor.getInt(_cursorIndexOfSunday);
        _tmpSunday = _tmp_9 != 0;
        _result = new DayLimit(_tmpId,_tmpUserId,_tmpAllDayLimit,_tmpIsIncludeWhite,_tmpIsDenyChange,_tmpJumpDate,_tmpDenyChangeLength,_tmpIsWorkDayLimit,_tmpMonday,_tmpTuesday,_tmpWednesday,_tmpThursday,_tmpFriday,_tmpSaturday,_tmpSunday);
      } else {
        _result = null;
      }
      return _result;
    } finally {
      _cursor.close();
      _statement.release();
    }
  }

  @Override
  public DayLimit getTodayLimitsImmediatelyThursday(final int userId) {
    final String _sql = "SELECT * FROM DayLimit where userId = ? and thursday=1 limit 1";
    final RoomSQLiteQuery _statement = RoomSQLiteQuery.acquire(_sql, 1);
    int _argIndex = 1;
    _statement.bindLong(_argIndex, userId);
    __db.assertNotSuspendingTransaction();
    final Cursor _cursor = DBUtil.query(__db, _statement, false, null);
    try {
      final int _cursorIndexOfId = CursorUtil.getColumnIndexOrThrow(_cursor, "id");
      final int _cursorIndexOfUserId = CursorUtil.getColumnIndexOrThrow(_cursor, "userId");
      final int _cursorIndexOfAllDayLimit = CursorUtil.getColumnIndexOrThrow(_cursor, "allDayLimit");
      final int _cursorIndexOfIsIncludeWhite = CursorUtil.getColumnIndexOrThrow(_cursor, "isIncludeWhite");
      final int _cursorIndexOfIsDenyChange = CursorUtil.getColumnIndexOrThrow(_cursor, "isDenyChange");
      final int _cursorIndexOfJumpDate = CursorUtil.getColumnIndexOrThrow(_cursor, "jumpDate");
      final int _cursorIndexOfDenyChangeLength = CursorUtil.getColumnIndexOrThrow(_cursor, "denyChangeLength");
      final int _cursorIndexOfIsWorkDayLimit = CursorUtil.getColumnIndexOrThrow(_cursor, "isWorkDayLimit");
      final int _cursorIndexOfMonday = CursorUtil.getColumnIndexOrThrow(_cursor, "monday");
      final int _cursorIndexOfTuesday = CursorUtil.getColumnIndexOrThrow(_cursor, "tuesday");
      final int _cursorIndexOfWednesday = CursorUtil.getColumnIndexOrThrow(_cursor, "wednesday");
      final int _cursorIndexOfThursday = CursorUtil.getColumnIndexOrThrow(_cursor, "thursday");
      final int _cursorIndexOfFriday = CursorUtil.getColumnIndexOrThrow(_cursor, "friday");
      final int _cursorIndexOfSaturday = CursorUtil.getColumnIndexOrThrow(_cursor, "saturday");
      final int _cursorIndexOfSunday = CursorUtil.getColumnIndexOrThrow(_cursor, "sunday");
      final DayLimit _result;
      if (_cursor.moveToFirst()) {
        final long _tmpId;
        _tmpId = _cursor.getLong(_cursorIndexOfId);
        final int _tmpUserId;
        _tmpUserId = _cursor.getInt(_cursorIndexOfUserId);
        final long _tmpAllDayLimit;
        _tmpAllDayLimit = _cursor.getLong(_cursorIndexOfAllDayLimit);
        final boolean _tmpIsIncludeWhite;
        final int _tmp;
        _tmp = _cursor.getInt(_cursorIndexOfIsIncludeWhite);
        _tmpIsIncludeWhite = _tmp != 0;
        final boolean _tmpIsDenyChange;
        final int _tmp_1;
        _tmp_1 = _cursor.getInt(_cursorIndexOfIsDenyChange);
        _tmpIsDenyChange = _tmp_1 != 0;
        final String _tmpJumpDate;
        if (_cursor.isNull(_cursorIndexOfJumpDate)) {
          _tmpJumpDate = null;
        } else {
          _tmpJumpDate = _cursor.getString(_cursorIndexOfJumpDate);
        }
        final int _tmpDenyChangeLength;
        _tmpDenyChangeLength = _cursor.getInt(_cursorIndexOfDenyChangeLength);
        final boolean _tmpIsWorkDayLimit;
        final int _tmp_2;
        _tmp_2 = _cursor.getInt(_cursorIndexOfIsWorkDayLimit);
        _tmpIsWorkDayLimit = _tmp_2 != 0;
        final boolean _tmpMonday;
        final int _tmp_3;
        _tmp_3 = _cursor.getInt(_cursorIndexOfMonday);
        _tmpMonday = _tmp_3 != 0;
        final boolean _tmpTuesday;
        final int _tmp_4;
        _tmp_4 = _cursor.getInt(_cursorIndexOfTuesday);
        _tmpTuesday = _tmp_4 != 0;
        final boolean _tmpWednesday;
        final int _tmp_5;
        _tmp_5 = _cursor.getInt(_cursorIndexOfWednesday);
        _tmpWednesday = _tmp_5 != 0;
        final boolean _tmpThursday;
        final int _tmp_6;
        _tmp_6 = _cursor.getInt(_cursorIndexOfThursday);
        _tmpThursday = _tmp_6 != 0;
        final boolean _tmpFriday;
        final int _tmp_7;
        _tmp_7 = _cursor.getInt(_cursorIndexOfFriday);
        _tmpFriday = _tmp_7 != 0;
        final boolean _tmpSaturday;
        final int _tmp_8;
        _tmp_8 = _cursor.getInt(_cursorIndexOfSaturday);
        _tmpSaturday = _tmp_8 != 0;
        final boolean _tmpSunday;
        final int _tmp_9;
        _tmp_9 = _cursor.getInt(_cursorIndexOfSunday);
        _tmpSunday = _tmp_9 != 0;
        _result = new DayLimit(_tmpId,_tmpUserId,_tmpAllDayLimit,_tmpIsIncludeWhite,_tmpIsDenyChange,_tmpJumpDate,_tmpDenyChangeLength,_tmpIsWorkDayLimit,_tmpMonday,_tmpTuesday,_tmpWednesday,_tmpThursday,_tmpFriday,_tmpSaturday,_tmpSunday);
      } else {
        _result = null;
      }
      return _result;
    } finally {
      _cursor.close();
      _statement.release();
    }
  }

  @Override
  public DayLimit getTodayLimitsImmediatelyFriday(final int userId) {
    final String _sql = "SELECT * FROM DayLimit where userId = ? and friday=1 limit 1";
    final RoomSQLiteQuery _statement = RoomSQLiteQuery.acquire(_sql, 1);
    int _argIndex = 1;
    _statement.bindLong(_argIndex, userId);
    __db.assertNotSuspendingTransaction();
    final Cursor _cursor = DBUtil.query(__db, _statement, false, null);
    try {
      final int _cursorIndexOfId = CursorUtil.getColumnIndexOrThrow(_cursor, "id");
      final int _cursorIndexOfUserId = CursorUtil.getColumnIndexOrThrow(_cursor, "userId");
      final int _cursorIndexOfAllDayLimit = CursorUtil.getColumnIndexOrThrow(_cursor, "allDayLimit");
      final int _cursorIndexOfIsIncludeWhite = CursorUtil.getColumnIndexOrThrow(_cursor, "isIncludeWhite");
      final int _cursorIndexOfIsDenyChange = CursorUtil.getColumnIndexOrThrow(_cursor, "isDenyChange");
      final int _cursorIndexOfJumpDate = CursorUtil.getColumnIndexOrThrow(_cursor, "jumpDate");
      final int _cursorIndexOfDenyChangeLength = CursorUtil.getColumnIndexOrThrow(_cursor, "denyChangeLength");
      final int _cursorIndexOfIsWorkDayLimit = CursorUtil.getColumnIndexOrThrow(_cursor, "isWorkDayLimit");
      final int _cursorIndexOfMonday = CursorUtil.getColumnIndexOrThrow(_cursor, "monday");
      final int _cursorIndexOfTuesday = CursorUtil.getColumnIndexOrThrow(_cursor, "tuesday");
      final int _cursorIndexOfWednesday = CursorUtil.getColumnIndexOrThrow(_cursor, "wednesday");
      final int _cursorIndexOfThursday = CursorUtil.getColumnIndexOrThrow(_cursor, "thursday");
      final int _cursorIndexOfFriday = CursorUtil.getColumnIndexOrThrow(_cursor, "friday");
      final int _cursorIndexOfSaturday = CursorUtil.getColumnIndexOrThrow(_cursor, "saturday");
      final int _cursorIndexOfSunday = CursorUtil.getColumnIndexOrThrow(_cursor, "sunday");
      final DayLimit _result;
      if (_cursor.moveToFirst()) {
        final long _tmpId;
        _tmpId = _cursor.getLong(_cursorIndexOfId);
        final int _tmpUserId;
        _tmpUserId = _cursor.getInt(_cursorIndexOfUserId);
        final long _tmpAllDayLimit;
        _tmpAllDayLimit = _cursor.getLong(_cursorIndexOfAllDayLimit);
        final boolean _tmpIsIncludeWhite;
        final int _tmp;
        _tmp = _cursor.getInt(_cursorIndexOfIsIncludeWhite);
        _tmpIsIncludeWhite = _tmp != 0;
        final boolean _tmpIsDenyChange;
        final int _tmp_1;
        _tmp_1 = _cursor.getInt(_cursorIndexOfIsDenyChange);
        _tmpIsDenyChange = _tmp_1 != 0;
        final String _tmpJumpDate;
        if (_cursor.isNull(_cursorIndexOfJumpDate)) {
          _tmpJumpDate = null;
        } else {
          _tmpJumpDate = _cursor.getString(_cursorIndexOfJumpDate);
        }
        final int _tmpDenyChangeLength;
        _tmpDenyChangeLength = _cursor.getInt(_cursorIndexOfDenyChangeLength);
        final boolean _tmpIsWorkDayLimit;
        final int _tmp_2;
        _tmp_2 = _cursor.getInt(_cursorIndexOfIsWorkDayLimit);
        _tmpIsWorkDayLimit = _tmp_2 != 0;
        final boolean _tmpMonday;
        final int _tmp_3;
        _tmp_3 = _cursor.getInt(_cursorIndexOfMonday);
        _tmpMonday = _tmp_3 != 0;
        final boolean _tmpTuesday;
        final int _tmp_4;
        _tmp_4 = _cursor.getInt(_cursorIndexOfTuesday);
        _tmpTuesday = _tmp_4 != 0;
        final boolean _tmpWednesday;
        final int _tmp_5;
        _tmp_5 = _cursor.getInt(_cursorIndexOfWednesday);
        _tmpWednesday = _tmp_5 != 0;
        final boolean _tmpThursday;
        final int _tmp_6;
        _tmp_6 = _cursor.getInt(_cursorIndexOfThursday);
        _tmpThursday = _tmp_6 != 0;
        final boolean _tmpFriday;
        final int _tmp_7;
        _tmp_7 = _cursor.getInt(_cursorIndexOfFriday);
        _tmpFriday = _tmp_7 != 0;
        final boolean _tmpSaturday;
        final int _tmp_8;
        _tmp_8 = _cursor.getInt(_cursorIndexOfSaturday);
        _tmpSaturday = _tmp_8 != 0;
        final boolean _tmpSunday;
        final int _tmp_9;
        _tmp_9 = _cursor.getInt(_cursorIndexOfSunday);
        _tmpSunday = _tmp_9 != 0;
        _result = new DayLimit(_tmpId,_tmpUserId,_tmpAllDayLimit,_tmpIsIncludeWhite,_tmpIsDenyChange,_tmpJumpDate,_tmpDenyChangeLength,_tmpIsWorkDayLimit,_tmpMonday,_tmpTuesday,_tmpWednesday,_tmpThursday,_tmpFriday,_tmpSaturday,_tmpSunday);
      } else {
        _result = null;
      }
      return _result;
    } finally {
      _cursor.close();
      _statement.release();
    }
  }

  @Override
  public DayLimit getTodayLimitsImmediatelySaturday(final int userId) {
    final String _sql = "SELECT * FROM DayLimit where userId = ? and saturday=1 limit 1";
    final RoomSQLiteQuery _statement = RoomSQLiteQuery.acquire(_sql, 1);
    int _argIndex = 1;
    _statement.bindLong(_argIndex, userId);
    __db.assertNotSuspendingTransaction();
    final Cursor _cursor = DBUtil.query(__db, _statement, false, null);
    try {
      final int _cursorIndexOfId = CursorUtil.getColumnIndexOrThrow(_cursor, "id");
      final int _cursorIndexOfUserId = CursorUtil.getColumnIndexOrThrow(_cursor, "userId");
      final int _cursorIndexOfAllDayLimit = CursorUtil.getColumnIndexOrThrow(_cursor, "allDayLimit");
      final int _cursorIndexOfIsIncludeWhite = CursorUtil.getColumnIndexOrThrow(_cursor, "isIncludeWhite");
      final int _cursorIndexOfIsDenyChange = CursorUtil.getColumnIndexOrThrow(_cursor, "isDenyChange");
      final int _cursorIndexOfJumpDate = CursorUtil.getColumnIndexOrThrow(_cursor, "jumpDate");
      final int _cursorIndexOfDenyChangeLength = CursorUtil.getColumnIndexOrThrow(_cursor, "denyChangeLength");
      final int _cursorIndexOfIsWorkDayLimit = CursorUtil.getColumnIndexOrThrow(_cursor, "isWorkDayLimit");
      final int _cursorIndexOfMonday = CursorUtil.getColumnIndexOrThrow(_cursor, "monday");
      final int _cursorIndexOfTuesday = CursorUtil.getColumnIndexOrThrow(_cursor, "tuesday");
      final int _cursorIndexOfWednesday = CursorUtil.getColumnIndexOrThrow(_cursor, "wednesday");
      final int _cursorIndexOfThursday = CursorUtil.getColumnIndexOrThrow(_cursor, "thursday");
      final int _cursorIndexOfFriday = CursorUtil.getColumnIndexOrThrow(_cursor, "friday");
      final int _cursorIndexOfSaturday = CursorUtil.getColumnIndexOrThrow(_cursor, "saturday");
      final int _cursorIndexOfSunday = CursorUtil.getColumnIndexOrThrow(_cursor, "sunday");
      final DayLimit _result;
      if (_cursor.moveToFirst()) {
        final long _tmpId;
        _tmpId = _cursor.getLong(_cursorIndexOfId);
        final int _tmpUserId;
        _tmpUserId = _cursor.getInt(_cursorIndexOfUserId);
        final long _tmpAllDayLimit;
        _tmpAllDayLimit = _cursor.getLong(_cursorIndexOfAllDayLimit);
        final boolean _tmpIsIncludeWhite;
        final int _tmp;
        _tmp = _cursor.getInt(_cursorIndexOfIsIncludeWhite);
        _tmpIsIncludeWhite = _tmp != 0;
        final boolean _tmpIsDenyChange;
        final int _tmp_1;
        _tmp_1 = _cursor.getInt(_cursorIndexOfIsDenyChange);
        _tmpIsDenyChange = _tmp_1 != 0;
        final String _tmpJumpDate;
        if (_cursor.isNull(_cursorIndexOfJumpDate)) {
          _tmpJumpDate = null;
        } else {
          _tmpJumpDate = _cursor.getString(_cursorIndexOfJumpDate);
        }
        final int _tmpDenyChangeLength;
        _tmpDenyChangeLength = _cursor.getInt(_cursorIndexOfDenyChangeLength);
        final boolean _tmpIsWorkDayLimit;
        final int _tmp_2;
        _tmp_2 = _cursor.getInt(_cursorIndexOfIsWorkDayLimit);
        _tmpIsWorkDayLimit = _tmp_2 != 0;
        final boolean _tmpMonday;
        final int _tmp_3;
        _tmp_3 = _cursor.getInt(_cursorIndexOfMonday);
        _tmpMonday = _tmp_3 != 0;
        final boolean _tmpTuesday;
        final int _tmp_4;
        _tmp_4 = _cursor.getInt(_cursorIndexOfTuesday);
        _tmpTuesday = _tmp_4 != 0;
        final boolean _tmpWednesday;
        final int _tmp_5;
        _tmp_5 = _cursor.getInt(_cursorIndexOfWednesday);
        _tmpWednesday = _tmp_5 != 0;
        final boolean _tmpThursday;
        final int _tmp_6;
        _tmp_6 = _cursor.getInt(_cursorIndexOfThursday);
        _tmpThursday = _tmp_6 != 0;
        final boolean _tmpFriday;
        final int _tmp_7;
        _tmp_7 = _cursor.getInt(_cursorIndexOfFriday);
        _tmpFriday = _tmp_7 != 0;
        final boolean _tmpSaturday;
        final int _tmp_8;
        _tmp_8 = _cursor.getInt(_cursorIndexOfSaturday);
        _tmpSaturday = _tmp_8 != 0;
        final boolean _tmpSunday;
        final int _tmp_9;
        _tmp_9 = _cursor.getInt(_cursorIndexOfSunday);
        _tmpSunday = _tmp_9 != 0;
        _result = new DayLimit(_tmpId,_tmpUserId,_tmpAllDayLimit,_tmpIsIncludeWhite,_tmpIsDenyChange,_tmpJumpDate,_tmpDenyChangeLength,_tmpIsWorkDayLimit,_tmpMonday,_tmpTuesday,_tmpWednesday,_tmpThursday,_tmpFriday,_tmpSaturday,_tmpSunday);
      } else {
        _result = null;
      }
      return _result;
    } finally {
      _cursor.close();
      _statement.release();
    }
  }

  @Override
  public DayLimit getTodayLimitsImmediatelySunday(final int userId) {
    final String _sql = "SELECT * FROM DayLimit where userId = ? and sunday=1 limit 1";
    final RoomSQLiteQuery _statement = RoomSQLiteQuery.acquire(_sql, 1);
    int _argIndex = 1;
    _statement.bindLong(_argIndex, userId);
    __db.assertNotSuspendingTransaction();
    final Cursor _cursor = DBUtil.query(__db, _statement, false, null);
    try {
      final int _cursorIndexOfId = CursorUtil.getColumnIndexOrThrow(_cursor, "id");
      final int _cursorIndexOfUserId = CursorUtil.getColumnIndexOrThrow(_cursor, "userId");
      final int _cursorIndexOfAllDayLimit = CursorUtil.getColumnIndexOrThrow(_cursor, "allDayLimit");
      final int _cursorIndexOfIsIncludeWhite = CursorUtil.getColumnIndexOrThrow(_cursor, "isIncludeWhite");
      final int _cursorIndexOfIsDenyChange = CursorUtil.getColumnIndexOrThrow(_cursor, "isDenyChange");
      final int _cursorIndexOfJumpDate = CursorUtil.getColumnIndexOrThrow(_cursor, "jumpDate");
      final int _cursorIndexOfDenyChangeLength = CursorUtil.getColumnIndexOrThrow(_cursor, "denyChangeLength");
      final int _cursorIndexOfIsWorkDayLimit = CursorUtil.getColumnIndexOrThrow(_cursor, "isWorkDayLimit");
      final int _cursorIndexOfMonday = CursorUtil.getColumnIndexOrThrow(_cursor, "monday");
      final int _cursorIndexOfTuesday = CursorUtil.getColumnIndexOrThrow(_cursor, "tuesday");
      final int _cursorIndexOfWednesday = CursorUtil.getColumnIndexOrThrow(_cursor, "wednesday");
      final int _cursorIndexOfThursday = CursorUtil.getColumnIndexOrThrow(_cursor, "thursday");
      final int _cursorIndexOfFriday = CursorUtil.getColumnIndexOrThrow(_cursor, "friday");
      final int _cursorIndexOfSaturday = CursorUtil.getColumnIndexOrThrow(_cursor, "saturday");
      final int _cursorIndexOfSunday = CursorUtil.getColumnIndexOrThrow(_cursor, "sunday");
      final DayLimit _result;
      if (_cursor.moveToFirst()) {
        final long _tmpId;
        _tmpId = _cursor.getLong(_cursorIndexOfId);
        final int _tmpUserId;
        _tmpUserId = _cursor.getInt(_cursorIndexOfUserId);
        final long _tmpAllDayLimit;
        _tmpAllDayLimit = _cursor.getLong(_cursorIndexOfAllDayLimit);
        final boolean _tmpIsIncludeWhite;
        final int _tmp;
        _tmp = _cursor.getInt(_cursorIndexOfIsIncludeWhite);
        _tmpIsIncludeWhite = _tmp != 0;
        final boolean _tmpIsDenyChange;
        final int _tmp_1;
        _tmp_1 = _cursor.getInt(_cursorIndexOfIsDenyChange);
        _tmpIsDenyChange = _tmp_1 != 0;
        final String _tmpJumpDate;
        if (_cursor.isNull(_cursorIndexOfJumpDate)) {
          _tmpJumpDate = null;
        } else {
          _tmpJumpDate = _cursor.getString(_cursorIndexOfJumpDate);
        }
        final int _tmpDenyChangeLength;
        _tmpDenyChangeLength = _cursor.getInt(_cursorIndexOfDenyChangeLength);
        final boolean _tmpIsWorkDayLimit;
        final int _tmp_2;
        _tmp_2 = _cursor.getInt(_cursorIndexOfIsWorkDayLimit);
        _tmpIsWorkDayLimit = _tmp_2 != 0;
        final boolean _tmpMonday;
        final int _tmp_3;
        _tmp_3 = _cursor.getInt(_cursorIndexOfMonday);
        _tmpMonday = _tmp_3 != 0;
        final boolean _tmpTuesday;
        final int _tmp_4;
        _tmp_4 = _cursor.getInt(_cursorIndexOfTuesday);
        _tmpTuesday = _tmp_4 != 0;
        final boolean _tmpWednesday;
        final int _tmp_5;
        _tmp_5 = _cursor.getInt(_cursorIndexOfWednesday);
        _tmpWednesday = _tmp_5 != 0;
        final boolean _tmpThursday;
        final int _tmp_6;
        _tmp_6 = _cursor.getInt(_cursorIndexOfThursday);
        _tmpThursday = _tmp_6 != 0;
        final boolean _tmpFriday;
        final int _tmp_7;
        _tmp_7 = _cursor.getInt(_cursorIndexOfFriday);
        _tmpFriday = _tmp_7 != 0;
        final boolean _tmpSaturday;
        final int _tmp_8;
        _tmp_8 = _cursor.getInt(_cursorIndexOfSaturday);
        _tmpSaturday = _tmp_8 != 0;
        final boolean _tmpSunday;
        final int _tmp_9;
        _tmp_9 = _cursor.getInt(_cursorIndexOfSunday);
        _tmpSunday = _tmp_9 != 0;
        _result = new DayLimit(_tmpId,_tmpUserId,_tmpAllDayLimit,_tmpIsIncludeWhite,_tmpIsDenyChange,_tmpJumpDate,_tmpDenyChangeLength,_tmpIsWorkDayLimit,_tmpMonday,_tmpTuesday,_tmpWednesday,_tmpThursday,_tmpFriday,_tmpSaturday,_tmpSunday);
      } else {
        _result = null;
      }
      return _result;
    } finally {
      _cursor.close();
      _statement.release();
    }
  }

  @Override
  public LiveData<DayLimit> getDayLimits(final int userId, final boolean isWorkDay) {
    final String _sql = "SELECT * FROM DayLimit where userId = ? and isWorkDayLimit=? limit 1";
    final RoomSQLiteQuery _statement = RoomSQLiteQuery.acquire(_sql, 2);
    int _argIndex = 1;
    _statement.bindLong(_argIndex, userId);
    _argIndex = 2;
    final int _tmp = isWorkDay ? 1 : 0;
    _statement.bindLong(_argIndex, _tmp);
    return __db.getInvalidationTracker().createLiveData(new String[] {"DayLimit"}, false, new Callable<DayLimit>() {
      @Override
      @Nullable
      public DayLimit call() throws Exception {
        final Cursor _cursor = DBUtil.query(__db, _statement, false, null);
        try {
          final int _cursorIndexOfId = CursorUtil.getColumnIndexOrThrow(_cursor, "id");
          final int _cursorIndexOfUserId = CursorUtil.getColumnIndexOrThrow(_cursor, "userId");
          final int _cursorIndexOfAllDayLimit = CursorUtil.getColumnIndexOrThrow(_cursor, "allDayLimit");
          final int _cursorIndexOfIsIncludeWhite = CursorUtil.getColumnIndexOrThrow(_cursor, "isIncludeWhite");
          final int _cursorIndexOfIsDenyChange = CursorUtil.getColumnIndexOrThrow(_cursor, "isDenyChange");
          final int _cursorIndexOfJumpDate = CursorUtil.getColumnIndexOrThrow(_cursor, "jumpDate");
          final int _cursorIndexOfDenyChangeLength = CursorUtil.getColumnIndexOrThrow(_cursor, "denyChangeLength");
          final int _cursorIndexOfIsWorkDayLimit = CursorUtil.getColumnIndexOrThrow(_cursor, "isWorkDayLimit");
          final int _cursorIndexOfMonday = CursorUtil.getColumnIndexOrThrow(_cursor, "monday");
          final int _cursorIndexOfTuesday = CursorUtil.getColumnIndexOrThrow(_cursor, "tuesday");
          final int _cursorIndexOfWednesday = CursorUtil.getColumnIndexOrThrow(_cursor, "wednesday");
          final int _cursorIndexOfThursday = CursorUtil.getColumnIndexOrThrow(_cursor, "thursday");
          final int _cursorIndexOfFriday = CursorUtil.getColumnIndexOrThrow(_cursor, "friday");
          final int _cursorIndexOfSaturday = CursorUtil.getColumnIndexOrThrow(_cursor, "saturday");
          final int _cursorIndexOfSunday = CursorUtil.getColumnIndexOrThrow(_cursor, "sunday");
          final DayLimit _result;
          if (_cursor.moveToFirst()) {
            final long _tmpId;
            _tmpId = _cursor.getLong(_cursorIndexOfId);
            final int _tmpUserId;
            _tmpUserId = _cursor.getInt(_cursorIndexOfUserId);
            final long _tmpAllDayLimit;
            _tmpAllDayLimit = _cursor.getLong(_cursorIndexOfAllDayLimit);
            final boolean _tmpIsIncludeWhite;
            final int _tmp_1;
            _tmp_1 = _cursor.getInt(_cursorIndexOfIsIncludeWhite);
            _tmpIsIncludeWhite = _tmp_1 != 0;
            final boolean _tmpIsDenyChange;
            final int _tmp_2;
            _tmp_2 = _cursor.getInt(_cursorIndexOfIsDenyChange);
            _tmpIsDenyChange = _tmp_2 != 0;
            final String _tmpJumpDate;
            if (_cursor.isNull(_cursorIndexOfJumpDate)) {
              _tmpJumpDate = null;
            } else {
              _tmpJumpDate = _cursor.getString(_cursorIndexOfJumpDate);
            }
            final int _tmpDenyChangeLength;
            _tmpDenyChangeLength = _cursor.getInt(_cursorIndexOfDenyChangeLength);
            final boolean _tmpIsWorkDayLimit;
            final int _tmp_3;
            _tmp_3 = _cursor.getInt(_cursorIndexOfIsWorkDayLimit);
            _tmpIsWorkDayLimit = _tmp_3 != 0;
            final boolean _tmpMonday;
            final int _tmp_4;
            _tmp_4 = _cursor.getInt(_cursorIndexOfMonday);
            _tmpMonday = _tmp_4 != 0;
            final boolean _tmpTuesday;
            final int _tmp_5;
            _tmp_5 = _cursor.getInt(_cursorIndexOfTuesday);
            _tmpTuesday = _tmp_5 != 0;
            final boolean _tmpWednesday;
            final int _tmp_6;
            _tmp_6 = _cursor.getInt(_cursorIndexOfWednesday);
            _tmpWednesday = _tmp_6 != 0;
            final boolean _tmpThursday;
            final int _tmp_7;
            _tmp_7 = _cursor.getInt(_cursorIndexOfThursday);
            _tmpThursday = _tmp_7 != 0;
            final boolean _tmpFriday;
            final int _tmp_8;
            _tmp_8 = _cursor.getInt(_cursorIndexOfFriday);
            _tmpFriday = _tmp_8 != 0;
            final boolean _tmpSaturday;
            final int _tmp_9;
            _tmp_9 = _cursor.getInt(_cursorIndexOfSaturday);
            _tmpSaturday = _tmp_9 != 0;
            final boolean _tmpSunday;
            final int _tmp_10;
            _tmp_10 = _cursor.getInt(_cursorIndexOfSunday);
            _tmpSunday = _tmp_10 != 0;
            _result = new DayLimit(_tmpId,_tmpUserId,_tmpAllDayLimit,_tmpIsIncludeWhite,_tmpIsDenyChange,_tmpJumpDate,_tmpDenyChangeLength,_tmpIsWorkDayLimit,_tmpMonday,_tmpTuesday,_tmpWednesday,_tmpThursday,_tmpFriday,_tmpSaturday,_tmpSunday);
          } else {
            _result = null;
          }
          return _result;
        } finally {
          _cursor.close();
        }
      }

      @Override
      protected void finalize() {
        _statement.release();
      }
    });
  }

  @NonNull
  public static List<Class<?>> getRequiredConverters() {
    return Collections.emptyList();
  }
}
