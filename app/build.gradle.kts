import java.util.Properties
import java.io.FileInputStream

plugins {
//    id("io.sentry.android.gradle")
    id("com.android.application")
    id("org.jetbrains.kotlin.android")
    id("com.google.devtools.ksp")
    id("com.kanyun.kace")
    id("androidx.room")
    id("kotlin-parcelize") // 可选，当使用了`@Parcelize`注解时需要添加
    id("dagger.hilt.android.plugin") // Hilt 依赖注入
    id("kotlin-kapt") // Hilt 需要 kapt
}


android {
    namespace = "com.lijianqiang12.silent"
    compileSdk = 35

    defaultConfig {
        applicationId = "com.lijianqiang12.silent"
        minSdk = 23
        targetSdk = 34
        versionCode = 49999
        versionName = "4.9.9.9.9"

        testInstrumentationRunner = "androidx.test.runner.AndroidJUnitRunner"
        manifestPlaceholders += mapOf("qqappid" to "1106233322") //友盟qqShare的aar文件中需要用到此占位符

        vectorDrawables.useSupportLibrary = true
    }


    val keystorePropertiesFile = rootProject.file("keystore.properties")
    val keystoreProperties = Properties()
    keystoreProperties.load(FileInputStream(keystorePropertiesFile))

    signingConfigs {
        create("lxsp") {
            keyAlias = keystoreProperties["keyAlias"] as String
            keyPassword = keystoreProperties["keyPassword"] as String
            storeFile = file(keystoreProperties["storeFile"] as String)
            storePassword = keystoreProperties["storePassword"] as String
        }

    }

    buildTypes {
        debug {
            signingConfig = signingConfigs["lxsp"]
        }
        release {
            isDebuggable = false
            isShrinkResources = true
            isMinifyEnabled = true
            signingConfig = signingConfigs["lxsp"]
            proguardFile("proguard-common.pro")
            proguardFile("proguard-rules.pro")

        }
    }
    externalNativeBuild {
        cmake {
            path = file("src/main/cpp/CMakeLists.txt")
        }
    }


    compileOptions {
        sourceCompatibility = JavaVersion.VERSION_17
        targetCompatibility = JavaVersion.VERSION_17
    }

    kotlinOptions {
        jvmTarget = "17"
    }
    buildFeatures {
        viewBinding = true
        buildConfig = true //加上以后才能调用BuildConfig
    }


    //变体维度
    flavorDimensions += listOf("channel", "abi")
    val channelList = listOf("huawei", "honor")
//    val channelList = listOf("huawei", "honor", "xiaomi", "oppo", "vivo", "tencent", "cooapk")
    productFlavors {
//        create("32") {
//            dimension = "abi"
//            ndk {
//                abiFilters += "armeabi-v7a"
//            }
//        }
        create("64") {
            dimension = "abi"
            ndk {
                abiFilters += "arm64-v8a"
            }
        }

        channelList.forEach {
            create(it) {
                dimension = "channel"
                manifestPlaceholders += mapOf("channelValue" to name)
                //华为不让在多任务页面隐藏
                manifestPlaceholders += if (name == "huawei") {
                    mapOf("excludeFromRecents" to false)
                } else {
                    mapOf("excludeFromRecents" to true)
                }
            }
        }
    }



    //打包全部渠道并将apk复制到release目录下
    tasks.register("packageAllProductFlavors") {
        dependsOn("assemble")
        doLast {
            applicationVariants.all { variant ->

                if (variant.buildType.name == "release") {
                    if (variant.flavorName != "huawei32" && variant.flavorName != "honor32"
                        && variant.flavorName != "xiaomi32" && variant.flavorName != "oppo32" && variant.flavorName != "vivo32"
                        && variant.flavorName != "cooapk32"
                    ) {

                        copy {
                            from("build/outputs/apk/${variant.dirName}/")
                            include("**/*.apk")
                            into("release/${variant.versionName}/")
                        }
                    }
                }
                true
            }

        }
    }


}




dependencies {
    implementation(fileTree("libs").include("*.jar", "*.aar"))
    implementation(project(":ReadableBottomBar"))
//    implementation("com.sharetrace:sharetrace-android-sdk:2.1.9")

    // Hilt 依赖注入
    val hiltVersion = "2.48"
    implementation("com.google.dagger:hilt-android:$hiltVersion")
    kapt("com.google.dagger:hilt-compiler:$hiltVersion")

    //ktx相关
    implementation("androidx.core:core-ktx:1.15.0")
    implementation("androidx.palette:palette-ktx:1.0.0")
    implementation("androidx.lifecycle:lifecycle-common:2.8.7")
    implementation("androidx.lifecycle:lifecycle-viewmodel-ktx:2.8.7")
    implementation("androidx.lifecycle:lifecycle-runtime-ktx:2.8.7")
    implementation("androidx.lifecycle:lifecycle-livedata-ktx:2.8.7")
    implementation("androidx.lifecycle:lifecycle-reactivestreams-ktx:2.8.7")
    implementation("androidx.fragment:fragment-ktx:1.8.5")
    implementation("androidx.navigation:navigation-fragment-ktx:2.8.5")
    implementation("androidx.navigation:navigation-ui-ktx:2.8.5")

    implementation("org.jetbrains.kotlinx:kotlinx-coroutines-core:1.7.3")
    implementation("org.jetbrains.kotlinx:kotlinx-coroutines-android:1.7.3")
    implementation("androidx.work:work-runtime-ktx:2.10.0")

    //noinspection GradleCompatible
    implementation("androidx.appcompat:appcompat:1.7.0")
    implementation("com.google.android.material:material:1.12.0")
    implementation("androidx.viewpager2:viewpager2:1.1.0")
    implementation("androidx.gridlayout:gridlayout:1.0.0")
    implementation("androidx.vectordrawable:vectordrawable:1.2.0")
    implementation("androidx.cardview:cardview:1.0.0")
    implementation("androidx.constraintlayout:constraintlayout:2.2.0")
    implementation("com.github.CymChad:BaseRecyclerViewAdapterHelper:3.0.4")
    implementation("com.github.bumptech.glide:glide:4.13.2")
    implementation("androidx.legacy:legacy-support-v4:1.0.0")
    implementation("androidx.activity:activity-ktx:1.10.0")
    annotationProcessor("com.github.bumptech.glide:compiler:4.12.0")
    implementation("androidx.recyclerview:recyclerview:1.4.0")
    implementation("moe.feng:AlipayZeroSdk:1.1")
    implementation("com.blankj:utilcodex:1.31.1")
    implementation("com.google.code.gson:gson:2.11.0")
    implementation("de.hdodenhof:circleimageview:3.0.1")
    implementation("com.github.PhilJay:MPAndroidChart:v3.1.0")

    implementation("com.afollestad.material-dialogs:core:3.1.1")
    implementation("com.afollestad.material-dialogs:input:3.1.1")
    implementation("com.afollestad.material-dialogs:color:3.1.1")
    implementation("com.afollestad.material-dialogs:lifecycle:3.1.0")
    implementation("com.afollestad.material-dialogs:datetime:3.1.0")

    //room
    val roomVersion = "2.6.1"
    implementation("androidx.room:room-ktx:$roomVersion")
    implementation("androidx.room:room-runtime:$roomVersion")
    ksp("androidx.room:room-compiler:$roomVersion")
    androidTestImplementation("androidx.arch.core:core-testing:2.2.0")
    implementation("com.github.czy1121:settingscompat:1.1.4")
    implementation("com.squareup.retrofit2:retrofit:2.6.2")
    implementation("com.squareup.retrofit2:converter-gson:2.6.2")
    implementation("com.squareup.okhttp3:okhttp:4.12.0")//此版本千万别升级！！！否则tencentCos会报错找不到方法)
//    implementation("com.squareup.okhttp3:okhttp:3.12.13")//此版本千万别升级！！！否则tencentCos会报错找不到方法)
    implementation("com.squareup.okhttp3:logging-interceptor:4.12.0")
    implementation("com.airbnb.android:lottie:3.4.0")
//    implementation("com.oasisfeng.condom:library:2.5.0")
    implementation("com.qcloud.cos:cos-android-nobeacon:5.9.30")
//    implementation("com.qcloud.cos:cos-ktx:5.6.5") {
//        exclude(group = "com.tencent.qcloud", module = "beacon-android-release")
//    }
    implementation("com.github.yalantis:ucrop:2.2.8")
    implementation("com.wang.avi:library:2.1.3") //loading组件
    implementation("jp.wasabeef:glide-transformations:4.0.0")
    implementation("com.xw.repo:bubbleseekbar:3.20-lite")
    implementation("com.jeremyliao:live-event-bus-x:1.5.7")
    implementation("com.github.iammert:MaterialIntroView:1.6.0")
    implementation("com.danikula:videocache:2.7.1")
    implementation("cn.jzvd:jiaozivideoplayer:7.7.0")//视频教程播放
    //友盟统计
//    implementation("com.umeng.umsdk:common:9.6.8")//必选
//    implementation("com.umeng.umsdk:asms:1.8.2")//必选
//    implementation("com.umeng.umsdk:apm:1.9.5")//推荐，错误分析升级为U-APM产品，关注crash数据需集成
    //分享，登录
//    implementation("com.umeng.umsdk:share-core:7.3.3")//分享核心库，必选
//    implementation("com.umeng.umsdk:share-board:7.3.3")//分享面板功能，可选
//    implementation("com.umeng.umsdk:share-qq:7.3.3")
//    implementation("com.umeng.umsdk:share-wx:7.3.3")
//    implementation("com.umeng.umsdk:share-sina:7.3.3")
    implementation("com.tencent.tauth:qqopensdk:3.53.0") {//QQ官方SDK依赖库
        exclude(group = "com.tencent.stat")
    }
    implementation("com.tencent.mm.opensdk:wechat-sdk-android-without-mta:6.8.0") {//微信官方SDK依赖库
        exclude(group = "com.tencent.stat")
    }
//    implementation("io.github.sinaweibosdk:core:13.10.2@aar")
//    implementation("io.github.sinaweibosdk:core:12.5.0@aar")

    implementation("com.tencent:mmkv:2.0.2")
    implementation("com.tencent.bugly:crashreport:4.1.9.3")
    implementation("com.superluo:textbannerview:1.0.5")  //文字轮播，邀请页面使用
    implementation("com.github.yuzhiqiang1993:zxing:2.2.5")
    implementation("com.github.getActivity:XXPermissions:18.5")
    implementation("com.github.maning0303:MNPasswordEditText:V1.0.4")
    implementation("androidx.core:core-splashscreen:1.0.1")

//    implementation("io.sentry:sentry-android:7.2.0") {
//        exclude(group = "androidx.core", module = "core")
//    }

    // aar依赖
    val privacyVersion = "1.3.4.2"
    implementation("com.github.allenymt.PrivacySentry:hook-sentry:$privacyVersion")
    implementation("com.github.allenymt.PrivacySentry:privacy-annotation:$privacyVersion")
    implementation("com.github.allenymt.PrivacySentry:privacy-proxy:$privacyVersion")
    implementation("com.github.allenymt.PrivacySentry:privacy-replace:$privacyVersion")

    testImplementation("junit:junit:4.13.2")
    androidTestImplementation("androidx.test:runner:1.6.2")
    androidTestImplementation("androidx.test.espresso:espresso-core:3.6.1")

}

// 黑名单配置，可以设置这部分包名不会被修改字节码
// 项目里如果有引入高德地图，先加黑 blackList = ["com.loc","com.amap.api"], asm的版本有冲突
//privacy {
//    blackList = setOf()
//}

//kace {
//    whiteList = listOf() // 当 whiteList 不为空时，只有 whiteList 中的 layout 才会被解析
//    blackList = listOf("activity_main.xml") // 当 blackList 不为空时，blackList 中的 layout 不会被解析
//}

room {
    schemaDirectory("$projectDir/schemas")
}

//sentry {
//    org = "shuge888"
//    projectName = "offphone"
//    authToken =
//        "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"
//    includeSourceContext.set(true)
//
//    // List the build types that should be ignored (e.g. "release").
////    ignoredBuildTypes.set(listOf("release"))
//
//    // List the build flavors that should be ignored (e.g. "production").
//    ignoredFlavors.set(listOf("xiaomi"))
//
//    // List the build variant that should be ignored (e.g. "productionRelease").
////    ignoredVariants.set(listOf("productionRelease"))
//}