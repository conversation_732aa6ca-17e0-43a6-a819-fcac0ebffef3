package com.lijianqiang12.silent.sync

import android.content.Context
import com.blankj.utilcode.util.LogUtils
import com.lijianqiang12.silent.MyConstants
import com.lijianqiang12.silent.data.model.db.*
import com.lijianqiang12.silent.data.model.net.MyRetrofitClient
import com.lijianqiang12.silent.data.viewmodel.InjectorUtils
import com.lijianqiang12.silent.data.viewmodel.LockViewModel
import com.lijianqiang12.silent.data.viewmodel.MonitorViewModel
import com.lijianqiang12.silent.utils.MMKVUtils
import com.lijianqiang12.silent.utils.MyToastUtil
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.GlobalScope
import kotlinx.coroutines.launch
import kotlinx.coroutines.withContext

class SyncHelper(context: Context) {

    @Volatile
    private var isSyncingWhiteApp = false//是否正在向云端同步

    @Volatile
    private var isSyncingFast = false//是否正在向云端同步

    @Volatile
    private var isSyncingTomato = false//是否正在向云端同步

    @Volatile
    private var isSyncingSchedule = false//是否正在向云端同步

    @Volatile
    private var isSyncingAppLimit = false//是否正在向云端同步

    @Volatile
    private var isSyncingHistory = false//是否正在向云端同步
    private var lockViewModel: LockViewModel = InjectorUtils.provideLockViewModelFactory(context.applicationContext).create(LockViewModel::class.java)
    private var monitorViewModel: MonitorViewModel =
        InjectorUtils.provideMonitorViewModelFactory(context.applicationContext).create(MonitorViewModel::class.java)

    fun startSyncAll(s: String = "") {

        startSyncWhiteApp()
        startSyncAppLimit()
        startSyncFast()
        startSyncTomato()
        startSyncSchedule(s = s)
        startSyncHistory()
    }

    fun startSyncWhiteApp() {
        LogUtils.d("startSyncAll start")
        val userId = MMKVUtils.getInt(MyConstants.SP_KEY_USER_ID, -1)
        if (userId == -1) return

        if (!isSyncingWhiteApp) {
//            LogUtils.d("ljq=========2")
            isSyncingWhiteApp = true
            GlobalScope.launch(Dispatchers.IO) {
//                LogUtils.d("ljq=========3")
                try {

                    val newWhiteList = lockViewModel.getWhiteAppsWithState(0)//新添加的
                    newWhiteList.forEach {
                        val uploadResult = MyRetrofitClient.service.addWhiteApp(
                            it.whiteAppIndexId, it.tomatoIndexId, it.scheduleIndexId,
                            it.pkg, it.mainActivity, it.maxLen, it.version
                        )
                        if (uploadResult.code == 200) {
                            it.trend = uploadResult.data!!.trend
                            it.uuid = uploadResult.data.uuid
                            it.syncTime = uploadResult.data.syncTime
                            it.syncState = 100
                            lockViewModel.lockRepository.updateWhiteApp(it)
                        } else {
                            MyToastUtil.showInfo(uploadResult.msg)
                        }
                    }

                    val updateWhiteList = lockViewModel.getWhiteAppsWithState(1)//新添加的
                    updateWhiteList.forEach {
                        val uploadResult = MyRetrofitClient.service.updateWhiteApp(
                            it.tomatoIndexId, it.scheduleIndexId,
                            it.pkg, it.mainActivity, it.maxLen, it.trend, it.syncTime, it.uuid, it.version
                        )
                        if (uploadResult.code == 200) {
                            it.tomatoIndexId = uploadResult.data!!.tomatoIndexId
                            it.scheduleIndexId = uploadResult.data.scheduleIndexId
                            it.pkg = uploadResult.data.pkg
                            it.mainActivity = uploadResult.data.mainActivity
                            it.maxLen = uploadResult.data.maxLen
                            it.trend = uploadResult.data.trend
                            it.version = uploadResult.data.version
                            it.syncTime = uploadResult.data.syncTime
                            it.syncState = 100
                            lockViewModel.lockRepository.updateWhiteApp(it)
                        } else {
                            MyToastUtil.showInfo(uploadResult.msg)
                        }
                    }

                    val deleteWhiteList = lockViewModel.getWhiteAppsWithState(-1)//新添加的
                    deleteWhiteList.forEach {
                        val uploadResult = MyRetrofitClient.service.deleteWhiteApp(it.uuid, it.version, it.syncTime)
                        if (uploadResult.code == 200) {
                            lockViewModel.lockRepository.deleteWhiteApp(it)
                        } else {
                            MyToastUtil.showInfo(uploadResult.msg)
                        }
                    }


//                val latestWhiteApp = lockViewModel.lockRepository.getLastWhiteApp()
//                var latestTime = 0L
//                if (latestWhiteApp != null) {
//                    latestTime = latestWhiteApp.syncTime
//                }

//                    LogUtils.d("ljq=========isSyncingWhiteApp")

                    val latestTime = MMKVUtils.getLong(
                        MyConstants.SP_KEY_LATEST_PULL_TIME_WHITE_APP + "$userId", 0L
                    )
//                    LogUtils.d("latestTime=" + latestTime)
                    val localWhiteAppList = lockViewModel.getAllWhiteAppList()
                    val uploadResult = MyRetrofitClient.service.pullWhiteApp(latestTime)


//                    LogUtils.d("ljq=========uploadResult=${uploadResult.toString()}")

                    if (uploadResult.code == 200) {
                        var newLatestPullTime = 0L
                        uploadResult.data!!.forEachIndexed loop1@{ index, pullWhiteResult ->
                            if (pullWhiteResult.syncTime > newLatestPullTime) newLatestPullTime = pullWhiteResult.syncTime
                            when (pullWhiteResult.syncState) {
                                100 -> {
                                    localWhiteAppList.forEach {
                                        if (pullWhiteResult.uuid == it.uuid) {
                                            if (pullWhiteResult.version > it.version || (pullWhiteResult.version == it.version && pullWhiteResult.syncTime > it.syncTime)) {
                                                if (it.syncState == 100) {
                                                    it.tomatoIndexId = pullWhiteResult.tomatoIndexId
                                                    it.scheduleIndexId = pullWhiteResult.scheduleIndexId
                                                    it.pkg = pullWhiteResult.pkg
                                                    it.mainActivity = pullWhiteResult.mainActivity
                                                    it.maxLen = pullWhiteResult.maxLen
                                                    it.trend = pullWhiteResult.trend
                                                    it.version = pullWhiteResult.version
                                                    it.syncTime = pullWhiteResult.syncTime
                                                    lockViewModel.lockRepository.updateWhiteApp(it)
                                                }
                                            }
                                            return@loop1
                                        }
                                    }
                                }

                                -1 -> {
                                    localWhiteAppList.forEach {
                                        if (pullWhiteResult.uuid == it.uuid) {
                                            if (pullWhiteResult.version > it.version || (pullWhiteResult.version == it.version && pullWhiteResult.syncTime > it.syncTime)) {
                                                if (it.syncState == 100) {
                                                    lockViewModel.lockRepository.deleteWhiteApp(it)
                                                }
                                            }
                                            return@loop1
                                        }
                                    }
                                }
                            }

                            if (pullWhiteResult.syncState == 100) {
                                val whiteApp = WhiteApp(
                                    0L,
                                    userId,
                                    pullWhiteResult.whiteAppIndexId,
                                    pullWhiteResult.tomatoIndexId,
                                    pullWhiteResult.scheduleIndexId,
                                    pullWhiteResult.pkg,
                                    pullWhiteResult.mainActivity,
                                    pullWhiteResult.maxLen,
                                    pullWhiteResult.trend,
                                    pullWhiteResult.syncState,
                                    pullWhiteResult.syncTime,
                                    pullWhiteResult.uuid,
                                    pullWhiteResult.version
                                )
                                lockViewModel.lockRepository.createWhiteApp(whiteApp)
                            }
                        }
                        if (newLatestPullTime > 0L) MMKVUtils.put(MyConstants.SP_KEY_LATEST_PULL_TIME_WHITE_APP + "$userId", newLatestPullTime)
                    } else {
                        MyToastUtil.showInfo(uploadResult.msg)
                    }
                } catch (e: Exception) {
                    MyToastUtil.showInfo(e.message)
                } finally {
                    isSyncingWhiteApp = false
                }
            }
        }

        LogUtils.d("startSyncAll end")
    }

    fun startSyncAppLimit() {

        LogUtils.d("startSyncAppLimit start")
        val userId = MMKVUtils.getInt(MyConstants.SP_KEY_USER_ID, -1)
        if (userId == -1) return

        if (!isSyncingAppLimit) {
            isSyncingAppLimit = true

            GlobalScope.launch(Dispatchers.IO) {
                try {
                    val newAppLimitList = monitorViewModel.getAppLimitWithState(0)//新添加的

//                    LogUtils.d("newAppLimitList=${newAppLimitList.toString()}")

                    newAppLimitList.forEach {
                        val uploadResult = MyRetrofitClient.service.addAppLimit(
                            it.appLimitIndexId,
                            it.appPkg, it.ifAllDay, it.startTime, it.endTime, it.limitLength,
                            it.title, it.sunday, it.monday, it.tuesday, it.wednesday, it.thursday,
                            it.friday, it.saturday, it.editStartTime, it.editEndTime, it.editMoney,
                            it.valid,
                            it.version
                        )
                        if (uploadResult.code == 200) {
                            it.trend = uploadResult.data!!.trend
                            it.uuid = uploadResult.data.uuid
                            it.syncTime = uploadResult.data.syncTime
                            it.syncState = 100
                            monitorViewModel.appLimitRepository.updateAppLimit(it)
                        } else {
                            MyToastUtil.showInfo(uploadResult.msg)
                        }
                    }

                    val updateAppLimitList = monitorViewModel.getAppLimitWithState(1)//新添加的
                    updateAppLimitList.forEach {
                        val uploadResult = MyRetrofitClient.service.updateAppLimit(
                            it.appPkg, it.ifAllDay, it.startTime, it.endTime, it.limitLength,

                            it.title, it.sunday, it.monday, it.tuesday, it.wednesday, it.thursday,
                            it.friday, it.saturday, it.editStartTime, it.editEndTime, it.editMoney,
                            it.valid,
                            it.trend, it.syncTime, it.uuid, it.version
                        )
                        if (uploadResult.code == 200) {
                            it.appPkg = uploadResult.data!!.appPkg
                            it.ifAllDay = uploadResult.data!!.ifAllDay
                            it.startTime = uploadResult.data!!.startTime
                            it.endTime = uploadResult.data!!.endTime
                            it.limitLength = uploadResult.data!!.limitLength

                            it.title = uploadResult.data!!.title
                            it.sunday = uploadResult.data!!.sunday
                            it.monday = uploadResult.data!!.monday
                            it.tuesday = uploadResult.data!!.tuesday
                            it.wednesday = uploadResult.data!!.wednesday
                            it.thursday = uploadResult.data!!.thursday
                            it.friday = uploadResult.data!!.friday
                            it.saturday = uploadResult.data!!.saturday
                            it.editStartTime = uploadResult.data!!.editStartTime
                            it.editEndTime = uploadResult.data!!.editEndTime
                            it.editMoney = uploadResult.data!!.editMoney
                            it.valid = uploadResult.data!!.valid

                            it.trend = uploadResult.data.trend
                            it.version = uploadResult.data.version
                            it.syncTime = uploadResult.data.syncTime
                            it.syncState = 100
                            monitorViewModel.appLimitRepository.updateAppLimit(it)
                        } else {
                            MyToastUtil.showInfo(uploadResult.msg)
                        }
                    }

                    val deleteAppLimitList = monitorViewModel.getAppLimitWithState(-1)//新添加的
                    deleteAppLimitList.forEach {
                        val uploadResult = MyRetrofitClient.service.deleteAppLimit(it.uuid, it.version, it.syncTime)
                        if (uploadResult.code == 200) {
                            monitorViewModel.appLimitRepository.deleteAppLimit(it)
                        } else {
                            MyToastUtil.showInfo(uploadResult.msg)
                        }
                    }

                    val latestTime = MMKVUtils.getLong(
                        MyConstants.SP_KEY_LATEST_PULL_TIME_APP_LIMIT + "$userId", 0L
                    )
                    val localAppLimitList = monitorViewModel.getAllAppLimitList()
                    val uploadResult = MyRetrofitClient.service.pullAppLimit(latestTime)
                    if (uploadResult.code == 200) {
                        var newLatestPullTime = 0L
                        uploadResult.data!!.forEachIndexed loop1@{ index, pullResult ->
                            if (pullResult.syncTime > newLatestPullTime) newLatestPullTime = pullResult.syncTime
                            when (pullResult.syncState) {
                                100 -> {
                                    localAppLimitList.forEach {
                                        if (pullResult.uuid == it.uuid) {
                                            if (pullResult.version > it.version || (pullResult.version == it.version && pullResult.syncTime > it.syncTime)) {
                                                if (it.syncState == 100) {
                                                    it.appPkg = pullResult.appPkg
                                                    it.ifAllDay = pullResult.ifAllDay
                                                    it.startTime = pullResult.startTime
                                                    it.endTime = pullResult.endTime
                                                    it.limitLength = pullResult.limitLength

                                                    it.title = pullResult.title
                                                    it.sunday = pullResult.sunday
                                                    it.monday = pullResult.monday
                                                    it.tuesday = pullResult.tuesday
                                                    it.wednesday = pullResult.wednesday
                                                    it.thursday = pullResult.thursday
                                                    it.friday = pullResult.friday
                                                    it.saturday = pullResult.saturday
                                                    it.editStartTime = pullResult.editStartTime
                                                    it.editEndTime = pullResult.editEndTime
                                                    it.editMoney = pullResult.editMoney
                                                    it.valid = pullResult.valid

                                                    it.version = pullResult.version
                                                    it.syncTime = pullResult.syncTime
                                                    it.trend = pullResult.trend
                                                    monitorViewModel.appLimitRepository.updateAppLimit(it)
                                                }
                                            }
                                            return@loop1
                                        }
                                    }
                                }

                                -1 -> {
                                    localAppLimitList.forEach {
                                        if (pullResult.uuid == it.uuid) {
                                            if (pullResult.version > it.version || (pullResult.version == it.version && pullResult.syncTime > it.syncTime)) {
                                                if (it.syncState == 100) {
                                                    monitorViewModel.appLimitRepository.deleteAppLimit(it)
                                                }
                                            }
                                            return@loop1
                                        }
                                    }
                                }
                            }

                            if (pullResult.syncState == 100) {
                                val appLimit = AppLimit(
                                    0L,
                                    userId,
                                    pullResult.appLimitIndexId,
                                    pullResult.appPkg,
                                    pullResult.ifAllDay,
                                    pullResult.startTime,
                                    pullResult.endTime,
                                    pullResult.limitLength,

                                    pullResult.title,
                                    pullResult.sunday,
                                    pullResult.monday,
                                    pullResult.tuesday,
                                    pullResult.wednesday,
                                    pullResult.thursday,
                                    pullResult.friday,
                                    pullResult.saturday,
                                    pullResult.editStartTime,
                                    pullResult.editEndTime,
                                    pullResult.editMoney,
                                    pullResult.valid,


                                    pullResult.trend, pullResult.syncState,
                                    pullResult.syncTime, pullResult.uuid, pullResult.version
                                )
                                monitorViewModel.appLimitRepository.createAppLimit(appLimit)
                            }
                        }
                        if (newLatestPullTime > 0L) MMKVUtils.put(
                            MyConstants.SP_KEY_LATEST_PULL_TIME_APP_LIMIT + "$userId", newLatestPullTime
                        )
                    } else {
                        MyToastUtil.showInfo(uploadResult.msg)
                    }
                } catch (e: Exception) {
                    MyToastUtil.showInfo(e.message)
                } finally {
                    isSyncingAppLimit = false
                }
            }
        }

        LogUtils.d("startSyncAppLimit end")
    }

    fun startSyncFast() {
        LogUtils.d("startSyncFast start")
        val userId = MMKVUtils.getInt(MyConstants.SP_KEY_USER_ID, -1)
        if (userId == -1) return

        if (!isSyncingFast) {
            isSyncingFast = true

            GlobalScope.launch(Dispatchers.IO) {
                try {
                    val newFastList = lockViewModel.getFastWithState(0)//新添加的
                    newFastList.forEach {
                        val uploadResult = MyRetrofitClient.service.addFast(it.fastIndexId, it.length, it.version)
                        if (uploadResult.code == 200) {
                            it.trend = uploadResult.data!!.trend
                            it.uuid = uploadResult.data.uuid
                            it.syncTime = uploadResult.data.syncTime
                            it.syncState = 100
                            lockViewModel.lockRepository.updateFast(it)
                        } else {
                            MyToastUtil.showInfo(uploadResult.msg)
                        }
                    }

                    val updateFastList = lockViewModel.getFastWithState(1)//新添加的
                    updateFastList.forEach {
                        val uploadResult = MyRetrofitClient.service.updateFast(
                            it.length, it.trend, it.syncTime,
                            it.uuid, it.version
                        )
                        if (uploadResult.code == 200) {
                            it.length = uploadResult.data!!.length
                            it.trend = uploadResult.data.trend
                            it.version = uploadResult.data.version
                            it.syncTime = uploadResult.data.syncTime
                            it.syncState = 100
                            lockViewModel.lockRepository.updateFast(it)
                        } else {
                            MyToastUtil.showInfo(uploadResult.msg)
                        }
                    }

                    val deleteFastList = lockViewModel.getFastWithState(-1)//新添加的
                    deleteFastList.forEach {
                        val uploadResult = MyRetrofitClient.service.deleteFast(it.uuid, it.version, it.syncTime)
                        if (uploadResult.code == 200) {
                            lockViewModel.lockRepository.deleteFast(it)
                        } else {
                            MyToastUtil.showInfo(uploadResult.msg)
                        }
                    }

                    val latestTime = MMKVUtils.getLong(
                        MyConstants.SP_KEY_LATEST_PULL_TIME_FAST + "$userId", 0L
                    )
                    val localFastList = lockViewModel.getAllFastList()
                    val uploadResult = MyRetrofitClient.service.pullFast(latestTime)
                    if (uploadResult.code == 200) {
                        var newLatestPullTime = 0L
                        uploadResult.data!!.forEachIndexed loop1@{ index, pullWhiteResult ->
                            if (pullWhiteResult.syncTime > newLatestPullTime) newLatestPullTime = pullWhiteResult.syncTime
                            when (pullWhiteResult.syncState) {
                                100 -> {
                                    localFastList.forEach {
                                        if (pullWhiteResult.uuid == it.uuid) {
                                            if (pullWhiteResult.version > it.version || (pullWhiteResult.version == it.version && pullWhiteResult.syncTime > it.syncTime)) {
                                                if (it.syncState == 100) {
                                                    it.length = pullWhiteResult.length
                                                    it.version = pullWhiteResult.version
                                                    it.syncTime = pullWhiteResult.syncTime
                                                    it.trend = pullWhiteResult.trend
                                                    lockViewModel.lockRepository.updateFast(it)
                                                }
                                            }
                                            return@loop1
                                        }
                                    }
                                }

                                -1 -> {
                                    localFastList.forEach {
                                        if (pullWhiteResult.uuid == it.uuid) {
                                            if (pullWhiteResult.version > it.version || (pullWhiteResult.version == it.version && pullWhiteResult.syncTime > it.syncTime)) {
                                                if (it.syncState == 100) {
                                                    lockViewModel.lockRepository.deleteFast(it)
                                                }
                                            }
                                            return@loop1
                                        }
                                    }
                                }
                            }

                            if (pullWhiteResult.syncState == 100) {
                                val fast = Fast(
                                    0L, userId, pullWhiteResult.fastIndexId, pullWhiteResult.length, pullWhiteResult.trend, pullWhiteResult.syncState,
                                    pullWhiteResult.syncTime, pullWhiteResult.uuid, pullWhiteResult.version
                                )
                                lockViewModel.lockRepository.createFast(fast)
                            }
                        }
                        if (newLatestPullTime > 0L) MMKVUtils.put(
                            MyConstants.SP_KEY_LATEST_PULL_TIME_FAST + "$userId", newLatestPullTime
                        )
                    } else {
                        MyToastUtil.showInfo(uploadResult.msg)
                    }
                } catch (e: Exception) {
                    MyToastUtil.showInfo(e.message)
                } finally {
                    isSyncingFast = false
                }
            }
        }

        LogUtils.d("startSyncFast end")
    }

    fun startSyncTomato() {
        val userId = MMKVUtils.getInt(MyConstants.SP_KEY_USER_ID, -1)
        if (userId == -1) return

        if (!isSyncingTomato) {
            isSyncingTomato = true

            GlobalScope.launch(Dispatchers.IO) {
                try {
                    val newTomatoList = lockViewModel.getTomatoWithState(0)//新添加的
                    newTomatoList.forEach {
                        val uploadResult = MyRetrofitClient.service.addTomato(
                            it.tomatoIndexId, it.title, it.tomatoWorkLength, it.tomatoRestLength, it.tomatoCount,
                            it.tomatoLongRestPerCount, it.tomatoLongRestLength,

                            it.lockConfig.bgUrl, it.lockConfig.isRemoveNotification,
                            it.lockConfig.isSilent, it.lockConfig.startVoiceNotify, it.lockConfig.endVoiceNotify,
                            it.lockConfig.startShakeNotify, it.lockConfig.endShakeNotify, it.lockConfig.whiteFollowGlobal,
                            it.lockConfig.bgUrlFollowGlobal, it.lockConfig.isRemoveNotificationFollowGlobal,
                            it.lockConfig.isSilentFollowGlobal, it.lockConfig.startVoiceNotifyFollowGlobal,
                            it.lockConfig.endVoiceNotifyFollowGlobal, it.lockConfig.startShakeNotifyFollowGlobal,
                            it.lockConfig.endShakeNotifyFollowGlobal,

                            it.version
                        )
                        if (uploadResult.code == 200) {
                            it.trend = uploadResult.data!!.trend
                            it.uuid = uploadResult.data.uuid
                            it.syncTime = uploadResult.data.syncTime
                            it.syncState = 100
                            lockViewModel.lockRepository.updateTomato(it)
                        } else {
                            MyToastUtil.showInfo(uploadResult.msg)
                        }
                    }

                    val updateTomatoList = lockViewModel.getTomatoWithState(1)//新添加的
                    updateTomatoList.forEach {
                        val uploadResult = MyRetrofitClient.service.updateTomato(
                            it.tomatoIndexId, it.title, it.tomatoWorkLength, it.tomatoRestLength, it.tomatoCount,
                            it.tomatoLongRestPerCount, it.tomatoLongRestLength,

                            it.lockConfig.bgUrl, it.lockConfig.isRemoveNotification,
                            it.lockConfig.isSilent, it.lockConfig.startVoiceNotify, it.lockConfig.endVoiceNotify,
                            it.lockConfig.startShakeNotify, it.lockConfig.endShakeNotify, it.lockConfig.whiteFollowGlobal,
                            it.lockConfig.bgUrlFollowGlobal, it.lockConfig.isRemoveNotificationFollowGlobal,
                            it.lockConfig.isSilentFollowGlobal, it.lockConfig.startVoiceNotifyFollowGlobal,
                            it.lockConfig.endVoiceNotifyFollowGlobal, it.lockConfig.startShakeNotifyFollowGlobal,
                            it.lockConfig.endShakeNotifyFollowGlobal,

                            it.trend, it.syncTime,
                            it.uuid, it.version
                        )
                        if (uploadResult.code == 200) {
                            it.tomatoIndexId = uploadResult.data!!.tomatoIndexId
                            it.title = uploadResult.data!!.title
                            it.tomatoWorkLength = uploadResult.data!!.tomatoWorkLength
                            it.tomatoRestLength = uploadResult.data!!.tomatoRestLength
                            it.tomatoCount = uploadResult.data!!.tomatoCount
                            it.tomatoLongRestPerCount = uploadResult.data!!.tomatoLongRestPerCount

                            it.lockConfig.bgUrl = uploadResult.data!!.bgUrl
                            it.lockConfig.isRemoveNotification = uploadResult.data!!.isRemoveNotification
                            it.lockConfig.isSilent = uploadResult.data!!.isSilent
                            it.lockConfig.startVoiceNotify = uploadResult.data!!.startVoiceNotify
                            it.lockConfig.endVoiceNotify = uploadResult.data!!.endVoiceNotify
                            it.lockConfig.startShakeNotify = uploadResult.data!!.startShakeNotify
                            it.lockConfig.endShakeNotify = uploadResult.data!!.endShakeNotify
                            it.lockConfig.whiteFollowGlobal = uploadResult.data!!.whiteFollowGlobal
                            it.lockConfig.bgUrlFollowGlobal = uploadResult.data!!.bgUrlFollowGlobal
                            it.lockConfig.isRemoveNotificationFollowGlobal = uploadResult.data!!.isRemoveNotificationFollowGlobal
                            it.lockConfig.isSilentFollowGlobal = uploadResult.data!!.isSilentFollowGlobal
                            it.lockConfig.startVoiceNotifyFollowGlobal = uploadResult.data!!.startVoiceNotifyFollowGlobal
                            it.lockConfig.endVoiceNotifyFollowGlobal = uploadResult.data!!.endVoiceNotifyFollowGlobal
                            it.lockConfig.startShakeNotifyFollowGlobal = uploadResult.data!!.startShakeNotifyFollowGlobal
                            it.lockConfig.endShakeNotifyFollowGlobal = uploadResult.data!!.endShakeNotifyFollowGlobal

                            it.trend = uploadResult.data.trend
                            it.version = uploadResult.data.version
                            it.syncTime = uploadResult.data.syncTime
                            it.syncState = 100
                            lockViewModel.lockRepository.updateTomato(it)
                        } else {
                            MyToastUtil.showInfo(uploadResult.msg)
                        }
                    }

                    val deleteTomatoList = lockViewModel.getTomatoWithState(-1)//新添加的
                    deleteTomatoList.forEach {
                        val uploadResult = MyRetrofitClient.service.deleteTomato(it.uuid, it.version, it.syncTime)
                        if (uploadResult.code == 200) {
                            lockViewModel.lockRepository.deleteTomato(it)
                        } else {
                            MyToastUtil.showInfo(uploadResult.msg)
                        }
                    }

                    val latestTime = MMKVUtils.getLong(
                        MyConstants.SP_KEY_LATEST_PULL_TIME_TOMATO + "$userId", 0L
                    )
                    val localTomatoList = lockViewModel.getAllTomatoList()
                    val uploadResult = MyRetrofitClient.service.pullTomato(latestTime)
                    if (uploadResult.code == 200) {
                        var newLatestPullTime = 0L
                        uploadResult.data!!.forEachIndexed loop1@{ index, pullTomatoResult ->
                            if (pullTomatoResult.syncTime > newLatestPullTime) newLatestPullTime = pullTomatoResult.syncTime
                            when (pullTomatoResult.syncState) {
                                100 -> {
                                    localTomatoList.forEach {
                                        if (pullTomatoResult.uuid == it.uuid) {
                                            if (pullTomatoResult.version > it.version || (pullTomatoResult.version == it.version && pullTomatoResult.syncTime > it.syncTime)) {
                                                if (it.syncState == 100) {
                                                    it.tomatoIndexId = pullTomatoResult.tomatoIndexId
                                                    it.title = pullTomatoResult.title
                                                    it.tomatoWorkLength = pullTomatoResult.tomatoWorkLength
                                                    it.tomatoRestLength = pullTomatoResult.tomatoRestLength
                                                    it.tomatoCount = pullTomatoResult.tomatoCount
                                                    it.tomatoLongRestPerCount = pullTomatoResult.tomatoLongRestPerCount

                                                    it.lockConfig.bgUrl = pullTomatoResult.bgUrl
                                                    it.lockConfig.isRemoveNotification = pullTomatoResult.isRemoveNotification
                                                    it.lockConfig.isSilent = pullTomatoResult.isSilent
                                                    it.lockConfig.startVoiceNotify = pullTomatoResult.startVoiceNotify
                                                    it.lockConfig.endVoiceNotify = pullTomatoResult.endVoiceNotify
                                                    it.lockConfig.startShakeNotify = pullTomatoResult.startShakeNotify
                                                    it.lockConfig.endShakeNotify = pullTomatoResult.endShakeNotify
                                                    it.lockConfig.whiteFollowGlobal = pullTomatoResult.whiteFollowGlobal
                                                    it.lockConfig.bgUrlFollowGlobal = pullTomatoResult.bgUrlFollowGlobal
                                                    it.lockConfig.isRemoveNotificationFollowGlobal = pullTomatoResult.isRemoveNotificationFollowGlobal
                                                    it.lockConfig.isSilentFollowGlobal = pullTomatoResult.isSilentFollowGlobal
                                                    it.lockConfig.startVoiceNotifyFollowGlobal = pullTomatoResult.startVoiceNotifyFollowGlobal
                                                    it.lockConfig.endVoiceNotifyFollowGlobal = pullTomatoResult.endVoiceNotifyFollowGlobal
                                                    it.lockConfig.startShakeNotifyFollowGlobal = pullTomatoResult.startShakeNotifyFollowGlobal
                                                    it.lockConfig.endShakeNotifyFollowGlobal = pullTomatoResult.endShakeNotifyFollowGlobal

                                                    it.trend = pullTomatoResult.trend
                                                    it.version = pullTomatoResult.version
                                                    it.syncTime = pullTomatoResult.syncTime
                                                    lockViewModel.lockRepository.updateTomato(it)
                                                }
                                            }
                                            return@loop1
                                        }
                                    }
                                }

                                -1 -> {
                                    localTomatoList.forEach {
                                        if (pullTomatoResult.uuid == it.uuid) {
                                            if (pullTomatoResult.version > it.version || (pullTomatoResult.version == it.version && pullTomatoResult.syncTime > it.syncTime)) {
                                                if (it.syncState == 100) {
                                                    lockViewModel.lockRepository.deleteTomato(it)
                                                }
                                            }
                                            return@loop1
                                        }
                                    }
                                }
                            }

                            if (pullTomatoResult.syncState == 100) {
                                val lockConfig = LockConfig(
                                    pullTomatoResult.bgUrl, pullTomatoResult.isRemoveNotification,
                                    pullTomatoResult.isSilent, pullTomatoResult.startVoiceNotify, pullTomatoResult.endVoiceNotify,
                                    pullTomatoResult.startShakeNotify, pullTomatoResult.endShakeNotify, pullTomatoResult.whiteFollowGlobal,
                                    pullTomatoResult.bgUrlFollowGlobal, pullTomatoResult.isRemoveNotificationFollowGlobal,
                                    pullTomatoResult.isSilentFollowGlobal, pullTomatoResult.startVoiceNotifyFollowGlobal,
                                    pullTomatoResult.endVoiceNotifyFollowGlobal, pullTomatoResult.startShakeNotifyFollowGlobal,
                                    pullTomatoResult.endShakeNotifyFollowGlobal
                                )

                                val tomato = Tomato(
                                    0L,
                                    userId,
                                    pullTomatoResult.tomatoIndexId, pullTomatoResult.title, pullTomatoResult.tomatoWorkLength,
                                    pullTomatoResult.tomatoRestLength, pullTomatoResult.tomatoCount,
                                    pullTomatoResult.tomatoLongRestPerCount, pullTomatoResult.tomatoLongRestLength,

                                    pullTomatoResult.trend, pullTomatoResult.syncState,
                                    pullTomatoResult.syncTime, pullTomatoResult.uuid, pullTomatoResult.version,
                                    lockConfig
                                )
                                lockViewModel.lockRepository.createTomato(tomato)
                            }
                        }
                        if (newLatestPullTime > 0L) MMKVUtils.put(
                            MyConstants.SP_KEY_LATEST_PULL_TIME_TOMATO + "$userId", newLatestPullTime
                        )
                    } else {
                        MyToastUtil.showInfo(uploadResult.msg)
                    }
                } catch (e: Exception) {
                    MyToastUtil.showInfo(e.message)
                } finally {
                    isSyncingTomato = false
                }
            }
        }
    }

    fun startSyncSchedule(s: String = "") {
        val userId = MMKVUtils.getInt(MyConstants.SP_KEY_USER_ID, -1)
        if (userId == -1) return

        if (!isSyncingSchedule) {
            isSyncingSchedule = true

            GlobalScope.launch(Dispatchers.IO) {
                try {
                    val newScheduleList = lockViewModel.getScheduleWithState(0)//新添加的
                    newScheduleList.forEach {
                        LogUtils.d(it.toString())
                        val uploadResult = MyRetrofitClient.service.addSchedule(
                            it.title, it.tomatoIndexId, it.scheduleIndexId, it.startHour, it.startMinute, it.validate, it.sunday, it.monday,
                            it.tuesday, it.wednesday, it.thursday, it.friday, it.saturday, it.useTomato, it.endHour, it.endMinute,
                            it.isRecycle, it.isDenyChange, it.denyChangeLength,

                            it.lockConfig.bgUrl, it.lockConfig.isRemoveNotification,
                            it.lockConfig.isSilent, it.lockConfig.startVoiceNotify, it.lockConfig.endVoiceNotify,
                            it.lockConfig.startShakeNotify, it.lockConfig.endShakeNotify, it.lockConfig.whiteFollowGlobal,
                            it.lockConfig.bgUrlFollowGlobal, it.lockConfig.isRemoveNotificationFollowGlobal,
                            it.lockConfig.isSilentFollowGlobal, it.lockConfig.startVoiceNotifyFollowGlobal,
                            it.lockConfig.endVoiceNotifyFollowGlobal, it.lockConfig.startShakeNotifyFollowGlobal,
                            it.lockConfig.endShakeNotifyFollowGlobal,
                            it.jumpDate,
                            it.version
                        )
                        if (uploadResult.code == 200) {
                            it.trend = uploadResult.data!!.trend
                            it.uuid = uploadResult.data.uuid
                            it.syncTime = uploadResult.data.syncTime
                            it.syncState = 100
                            lockViewModel.lockRepository.updateSchedule(it)
                        } else {
                            MyToastUtil.showInfo(uploadResult.msg)
                        }
                    }

                    val updateScheduleList = lockViewModel.getScheduleWithState(1)//新添加的
                    updateScheduleList.forEach {
//                        LogUtils.d("schedule update it.useTomato=" + it.useTomato)
                        val uploadResult = MyRetrofitClient.service.updateSchedule(
                            it.title, it.tomatoIndexId, it.scheduleIndexId, it.startHour, it.startMinute, it.validate, it.sunday, it.monday,
                            it.tuesday, it.wednesday, it.thursday, it.friday, it.saturday, it.useTomato, it.endHour, it.endMinute,
                            it.isRecycle, it.isDenyChange, it.denyChangeLength,

                            it.lockConfig.bgUrl, it.lockConfig.isRemoveNotification,
                            it.lockConfig.isSilent, it.lockConfig.startVoiceNotify, it.lockConfig.endVoiceNotify,
                            it.lockConfig.startShakeNotify, it.lockConfig.endShakeNotify, it.lockConfig.whiteFollowGlobal,
                            it.lockConfig.bgUrlFollowGlobal, it.lockConfig.isRemoveNotificationFollowGlobal,
                            it.lockConfig.isSilentFollowGlobal, it.lockConfig.startVoiceNotifyFollowGlobal,
                            it.lockConfig.endVoiceNotifyFollowGlobal, it.lockConfig.startShakeNotifyFollowGlobal,
                            it.lockConfig.endShakeNotifyFollowGlobal,
                            it.jumpDate,
                            it.trend, it.syncTime,
                            it.uuid, it.version
                        )

//                        LogUtils.d("schedule update1")
                        if (uploadResult.code == 200) {
//                            LogUtils.d("schedule update2 it.useTomato=" + it.useTomato)
                            it.title = uploadResult.data!!.title
                            it.tomatoIndexId = uploadResult.data!!.tomatoIndexId
                            it.scheduleIndexId = uploadResult.data!!.scheduleIndexId
                            it.startHour = uploadResult.data!!.startHour
                            it.startMinute = uploadResult.data!!.startMinute
                            it.validate = uploadResult.data!!.validate
                            it.sunday = uploadResult.data!!.sunday
                            it.monday = uploadResult.data!!.monday
                            it.tuesday = uploadResult.data!!.tuesday
                            it.wednesday = uploadResult.data!!.wednesday
                            it.thursday = uploadResult.data!!.thursday
                            it.friday = uploadResult.data!!.friday
                            it.saturday = uploadResult.data!!.saturday
                            it.useTomato = uploadResult.data!!.useTomato
                            it.endHour = uploadResult.data!!.endHour
                            it.endMinute = uploadResult.data!!.endMinute
                            it.isRecycle = uploadResult.data!!.isRecycle
                            it.isDenyChange = uploadResult.data!!.isDenyChange
                            it.denyChangeLength = uploadResult.data!!.denyChangeLength

                            it.lockConfig.bgUrl = uploadResult.data!!.bgUrl
                            it.lockConfig.isRemoveNotification = uploadResult.data!!.isRemoveNotification
                            it.lockConfig.isSilent = uploadResult.data!!.isSilent
                            it.lockConfig.startVoiceNotify = uploadResult.data!!.startVoiceNotify
                            it.lockConfig.endVoiceNotify = uploadResult.data!!.endVoiceNotify
                            it.lockConfig.startShakeNotify = uploadResult.data!!.startShakeNotify
                            it.lockConfig.endShakeNotify = uploadResult.data!!.endShakeNotify
                            it.lockConfig.whiteFollowGlobal = uploadResult.data!!.whiteFollowGlobal
                            it.lockConfig.bgUrlFollowGlobal = uploadResult.data!!.bgUrlFollowGlobal
                            it.lockConfig.isRemoveNotificationFollowGlobal = uploadResult.data!!.isRemoveNotificationFollowGlobal
                            it.lockConfig.isSilentFollowGlobal = uploadResult.data!!.isSilentFollowGlobal
                            it.lockConfig.startVoiceNotifyFollowGlobal = uploadResult.data!!.startVoiceNotifyFollowGlobal
                            it.lockConfig.endVoiceNotifyFollowGlobal = uploadResult.data!!.endVoiceNotifyFollowGlobal
                            it.lockConfig.startShakeNotifyFollowGlobal = uploadResult.data!!.startShakeNotifyFollowGlobal
                            it.lockConfig.endShakeNotifyFollowGlobal = uploadResult.data!!.endShakeNotifyFollowGlobal

                            it.jumpDate = uploadResult.data.jumpDate

                            it.trend = uploadResult.data.trend
                            it.version = uploadResult.data.version
                            it.syncTime = uploadResult.data.syncTime
                            it.syncState = 100
                            lockViewModel.lockRepository.updateSchedule(it)
                        } else {

                            LogUtils.d("schedule update3")
                            MyToastUtil.showInfo(uploadResult.msg)
                        }
                    }

                    val deleteScheduleList = lockViewModel.getScheduleWithState(-1)//新添加的
                    deleteScheduleList.forEach {
                        val uploadResult = MyRetrofitClient.service.deleteSchedule(it.uuid, it.version, it.syncTime)
                        if (uploadResult.code == 200) {
                            lockViewModel.lockRepository.deleteSchedule(it)
                        } else {
                            MyToastUtil.showInfo(uploadResult.msg)
                        }
                    }

                    val latestTime = MMKVUtils.getLong(
                        MyConstants.SP_KEY_LATEST_PULL_TIME_SCHEDULE + "$userId", 0L
                    )
                    val localScheduleList = lockViewModel.getAllScheduleList()
                    val uploadResult = MyRetrofitClient.service.pullSchedule(latestTime, s == "update")
                    if (uploadResult.code == 200) {
                        var newLatestPullTime = 0L
                        uploadResult.data!!.forEachIndexed loop1@{ index, pullResult ->
                            if (pullResult.syncTime > newLatestPullTime) newLatestPullTime = pullResult.syncTime
                            when (pullResult.syncState) {
                                100 -> {
                                    localScheduleList.forEach {
                                        if (pullResult.uuid == it.uuid) {
                                            if (pullResult.version > it.version || (pullResult.version == it.version && pullResult.syncTime > it.syncTime)) {
                                                if (it.syncState == 100) {
                                                    it.title = pullResult.title
                                                    it.tomatoIndexId = pullResult.tomatoIndexId
                                                    it.scheduleIndexId = pullResult.scheduleIndexId
                                                    it.startHour = pullResult.startHour
                                                    it.startMinute = pullResult.startMinute
                                                    it.validate = pullResult.validate
                                                    it.sunday = pullResult.sunday
                                                    it.monday = pullResult.monday
                                                    it.tuesday = pullResult.tuesday
                                                    it.wednesday = pullResult.wednesday
                                                    it.thursday = pullResult.thursday
                                                    it.friday = pullResult.friday
                                                    it.saturday = pullResult.saturday
                                                    it.useTomato = pullResult.useTomato
                                                    it.endHour = pullResult.endHour
                                                    it.endMinute = pullResult.endMinute
                                                    it.isRecycle = pullResult.isRecycle
                                                    it.isDenyChange = pullResult.isDenyChange
                                                    it.denyChangeLength = pullResult.denyChangeLength

                                                    it.lockConfig.bgUrl = pullResult.bgUrl
                                                    it.lockConfig.isRemoveNotification = pullResult.isRemoveNotification
                                                    it.lockConfig.isSilent = pullResult.isSilent
                                                    it.lockConfig.startVoiceNotify = pullResult.startVoiceNotify
                                                    it.lockConfig.endVoiceNotify = pullResult.endVoiceNotify
                                                    it.lockConfig.startShakeNotify = pullResult.startShakeNotify
                                                    it.lockConfig.endShakeNotify = pullResult.endShakeNotify
                                                    it.lockConfig.whiteFollowGlobal = pullResult.whiteFollowGlobal
                                                    it.lockConfig.bgUrlFollowGlobal = pullResult.bgUrlFollowGlobal
                                                    it.lockConfig.isRemoveNotificationFollowGlobal = pullResult.isRemoveNotificationFollowGlobal
                                                    it.lockConfig.isSilentFollowGlobal = pullResult.isSilentFollowGlobal
                                                    it.lockConfig.startVoiceNotifyFollowGlobal = pullResult.startVoiceNotifyFollowGlobal
                                                    it.lockConfig.endVoiceNotifyFollowGlobal = pullResult.endVoiceNotifyFollowGlobal
                                                    it.lockConfig.startShakeNotifyFollowGlobal = pullResult.startShakeNotifyFollowGlobal
                                                    it.lockConfig.endShakeNotifyFollowGlobal = pullResult.endShakeNotifyFollowGlobal

                                                    it.jumpDate = pullResult.jumpDate
                                                    it.trend = pullResult.trend
                                                    it.version = pullResult.version
                                                    it.syncTime = pullResult.syncTime
                                                    lockViewModel.lockRepository.updateSchedule(it)
                                                }
                                            }
                                            return@loop1
                                        }
                                    }
                                }

                                -1 -> {
                                    localScheduleList.forEach {
                                        if (pullResult.uuid == it.uuid) {
                                            if (pullResult.version > it.version || (pullResult.version == it.version && pullResult.syncTime > it.syncTime)) {
                                                if (it.syncState == 100) {
                                                    lockViewModel.lockRepository.deleteSchedule(it)
                                                }
                                            }
                                            return@loop1
                                        }
                                    }
                                }
                            }

                            if (pullResult.syncState == 100) {
                                val lockConfig = LockConfig(
                                    pullResult.bgUrl, pullResult.isRemoveNotification,
                                    pullResult.isSilent, pullResult.startVoiceNotify, pullResult.endVoiceNotify,
                                    pullResult.startShakeNotify, pullResult.endShakeNotify, pullResult.whiteFollowGlobal,
                                    pullResult.bgUrlFollowGlobal, pullResult.isRemoveNotificationFollowGlobal,
                                    pullResult.isSilentFollowGlobal, pullResult.startVoiceNotifyFollowGlobal,
                                    pullResult.endVoiceNotifyFollowGlobal, pullResult.startShakeNotifyFollowGlobal,
                                    pullResult.endShakeNotifyFollowGlobal
                                )

                                val schedule = Schedule(
                                    0L,
                                    userId,
                                    pullResult.title,
                                    pullResult.tomatoIndexId,
                                    pullResult.scheduleIndexId,
                                    pullResult.startHour,
                                    pullResult.startMinute,
                                    pullResult.validate,
                                    pullResult.sunday,
                                    pullResult.monday,
                                    pullResult.tuesday,
                                    pullResult.wednesday,
                                    pullResult.thursday,
                                    pullResult.friday,
                                    pullResult.saturday,
                                    pullResult.useTomato,
                                    pullResult.endHour,
                                    pullResult.endMinute,
                                    pullResult.isRecycle,
                                    pullResult.isDenyChange,
                                    pullResult.denyChangeLength,
                                    pullResult.jumpDate,
                                    pullResult.trend,
                                    pullResult.syncState,
                                    pullResult.syncTime,
                                    pullResult.uuid,
                                    pullResult.version,
                                    lockConfig
                                )
                                lockViewModel.lockRepository.createSchedule(schedule)
                            }
                        }
                        if (newLatestPullTime > 0L) MMKVUtils.put(
                            MyConstants.SP_KEY_LATEST_PULL_TIME_SCHEDULE + "$userId", newLatestPullTime
                        )
                    } else {
                        withContext(Dispatchers.Main) {
                            MyToastUtil.showInfo(uploadResult.msg)
                        }
                    }
                } catch (e: Exception) {
                    withContext(Dispatchers.Main) {
                        MyToastUtil.showInfo(e.message)
                    }
                } finally {
                    isSyncingSchedule = false
                }
            }
        }
    }


    fun startSyncHistory() {
        val userId = MMKVUtils.getInt(MyConstants.SP_KEY_USER_ID, -1)
        if (userId == -1) return

        if (!isSyncingHistory) {
            isSyncingHistory = true

            GlobalScope.launch(Dispatchers.IO) {
                try {
                    val newHistoryList = lockViewModel.getUnUploadHistory()
                    newHistoryList.forEach {
                        val uploadResult = MyRetrofitClient.service.syncHistory(
                            it.startTime, it.trueStartTime, it.timeLength,
                            it.trueTimeLength, it.lockType, it.tomatoIndexId, it.scheduleIndexId
                        )
                        if (uploadResult.code == 200) {
                            it.isSynced = true
                            lockViewModel.lockRepository.updateLockHistory(it)
                        } else {
                            MyToastUtil.showInfo(uploadResult.msg)
                        }

                    }

                } catch (e: Exception) {
                    MyToastUtil.showInfo(e.message)
                } finally {
                    isSyncingHistory = false
                }
            }
        }
    }


}