package com.lijianqiang12.silent.data.model.repository

import com.lijianqiang12.silent.MyConstants
import com.lijianqiang12.silent.data.model.db.AppLimit
import com.lijianqiang12.silent.data.model.db.AppLimitDao
import com.lijianqiang12.silent.utils.MMKVUtils

class AppLimitRepository private constructor(private val appLimitDao: AppLimitDao) {

    /*同步相关*/
    suspend fun getAllAppLimitList() = appLimitDao.getAllAppLimitList(MMKVUtils.getInt(MyConstants.SP_KEY_USER_ID, -1))
    suspend fun getAppLimitWithState(state: Int) = appLimitDao.getAppLimitWithState(MMKVUtils.getInt(MyConstants.SP_KEY_USER_ID, -1), state)

    //    fun getAppLimitList(planId:Long) = appLimitDao.getAppLimitList(planId)
    fun getAppLimits(userId: Int) = appLimitDao.getAppLimits(userId)
    suspend fun getLastAppLimit() = appLimitDao.getLastAppLimit(MMKVUtils.getInt(MyConstants.SP_KEY_USER_ID, -1))

    suspend fun createAppLimit(appLimit: AppLimit) = appLimitDao.insertAppLimit(appLimit)
    suspend fun updateAppLimit(appLimit: AppLimit) = appLimitDao.updateAppLimit(appLimit)
    suspend fun deleteAppLimit(appLimit: AppLimit) = appLimitDao.deleteAppLimit(appLimit)


//    suspend fun removeAppLimit(gardenPlanting: GardenPlanting) {
//        gardenPlantingDao.deleteGardenPlanting(gardenPlanting)
//    }

//    fun isPlanted(plantId: String) =
//        gardenPlantingDao.isPlanted(plantId)

//    fun getPlantedGardens() = gardenPlantingDao.getPlantedGardens()


    companion object {

        // For Singleton instantiation
        @Volatile
        private var instance: AppLimitRepository? = null

        fun getInstance(appLimitDao: AppLimitDao) = instance ?: synchronized(this) {
            instance ?: AppLimitRepository(appLimitDao).also { instance = it }
        }
    }

}