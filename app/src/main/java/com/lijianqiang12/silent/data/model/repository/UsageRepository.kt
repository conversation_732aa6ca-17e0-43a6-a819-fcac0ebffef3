package com.lijianqiang12.silent.data.model.repository

import android.content.Context
import com.lijianqiang12.silent.component.activity.analyze.AppTime
import com.lijianqiang12.silent.data.model.db.AppUsage
import com.lijianqiang12.silent.data.model.db.AppUsageDao
import com.lijianqiang12.silent.data.model.db.WhiteApp
import com.lijianqiang12.silent.data.viewmodel.AppTimeAnalyze
import com.lijianqiang12.silent.utils.UsageUtil
import java.util.*


class UsageRepository private constructor(private val appContext: Context, private val appUsageDao: AppUsageDao) {

    private val TAG = "UsageRepository"

    suspend fun insertAppUsage(appUsage: AppUsage) = appUsageDao.insertAppUsage(appUsage)

    /**
     * 今天的全部使用时长 s
     */
    suspend fun getTodayUsageLength(): Long {

        val currentTime = System.currentTimeMillis()
        val beginCal = Calendar.getInstance()
        beginCal.set(Calendar.HOUR_OF_DAY, 0)
        beginCal.set(Calendar.MINUTE, 0)
        beginCal.set(Calendar.SECOND, 0)
        beginCal.set(Calendar.MILLISECOND, 0)
        val startTime = beginCal.timeInMillis
        val appTimeList = UsageUtil.getPeriodTimeList(appContext, startTime, currentTime)

        var timeLength = 0L


        appTimeList.forEach {
            timeLength += it.timeLength
        }

        return timeLength / 1000
    }


    /**
     * 今天的全部使用时长，除去白名单app s
     */
    suspend fun getTodayUsageLengthWithoutWhite(whiteList: MutableList<WhiteApp>): Long {

        val currentTime = System.currentTimeMillis()
        val beginCal = Calendar.getInstance()
        beginCal.set(Calendar.HOUR_OF_DAY, 0)
        beginCal.set(Calendar.MINUTE, 0)
        beginCal.set(Calendar.SECOND, 0)
        beginCal.set(Calendar.MILLISECOND, 0)
        val startTime = beginCal.timeInMillis
        val appTimeList = UsageUtil.getPeriodTimeList(appContext, startTime, currentTime)

        var timeLength = 0L

        appTimeList.forEach {
            whiteList.forEachIndexed { _, whiteApp ->
                if (whiteApp.pkg == it.pkg) return@forEach
            }

            timeLength += it.timeLength
        }


        return timeLength / 1000
    }


    /**
     * 应用们在一段时间内的使用时长 s
     */
    suspend fun getPkgListUsageLength(pkgList: MutableList<String>, startTimeInSecond: Long, endTimeInSecond: Long): Long {


        val trueTime = UsageUtil.timeInSecond2Timestamp(startTimeInSecond, endTimeInSecond)
        val trueStartTime = trueTime.first
        val trueEndTime = trueTime.second

        val fixTimeList = appUsageDao.getSomeAppUsageList(trueStartTime, trueEndTime, pkgList)


        val usage = UsageUtil.getPkgListUsageLength(appContext, pkgList, startTimeInSecond, endTimeInSecond)

        return usage + fixTimeList.size
    }

    /**
     * 应用在今天一段时间内的使用时长 s
     */
    suspend fun getTodayUsageLengthFromTrueTime(pkg: String, startTime: Long, endTime: Long): Long {

        val fixTimeList = appUsageDao.getOneAppUsageList(startTime, endTime, pkg)

        val appTimeList = UsageUtil.getPeriodTimeList(appContext, startTime, endTime)

        var timeLength = 0L
        for (appTime in appTimeList) {
            if (appTime.pkg == pkg) {
                timeLength = appTime.timeLength
                break
            }
        }
//        return timeLength / 1000
        return timeLength / 1000 + fixTimeList.size
    }


    /**
     * 一段时间内的app使用情况列表，最长能获取一周
     */
    private suspend fun getPeriodUsageList(startTime: Long, endTime: Long): List<AppTime> {
        val fixTimeList = appUsageDao.getAllAppUsageList(startTime, endTime)
        val usage = UsageUtil.getPeriodTimeList(appContext, startTime, endTime)
        usage.forEach {
            fixTimeList.forEachIndexed { _, appUsage ->
                if (it.pkg == appUsage.appPkg) {
                    it.timeLength += 1000
                }
            }
        }
        return usage
    }


    /**
     * 一段时间内app使用时长排名(ms)
     */
    private suspend fun getPeriodAppUsageTimeRank(startTime: Long, endTime: Long): MutableList<AppTime> {

        val tempList = getPeriodUsageList(startTime, endTime)

        var resultList = mutableListOf<AppTime>()

        tempList.forEach {
            resultList.add(it)
        }

        resultList = resultList.sortedByDescending {
            it.timeLength
        }.toMutableList()

        return resultList
    }


    /**
     * 今天app使用时长排名
     */
    suspend fun getTodayAppUsageTimeRank(): MutableList<AppTime> {
        val beginCal = Calendar.getInstance()
        beginCal.set(Calendar.HOUR_OF_DAY, 0)
        beginCal.set(Calendar.MINUTE, 0)
        beginCal.set(Calendar.SECOND, 0)
        beginCal.set(Calendar.MILLISECOND, 0)
        val startTime = beginCal.timeInMillis
        val endTime = System.currentTimeMillis()

        return getPeriodAppUsageTimeRank(startTime, endTime)
    }

    /**
     * 最近7天app使用时长排名
     */
    suspend fun get7DaysAppUsageTimeRank(): MutableList<AppTime> {
        val beginCal = Calendar.getInstance()
        beginCal.add(Calendar.DAY_OF_MONTH, -7)
        val startTime = beginCal.timeInMillis
        val endTime = System.currentTimeMillis()

        return getPeriodAppUsageTimeRank(startTime, endTime)
    }

    /**
     * app使用分析3-7天
     */
    suspend fun getAppAnalyze(): MutableList<AppTimeAnalyze> {
        val minDay = 1
        val beginCal = Calendar.getInstance()
//        beginCal.set(Calendar.HOUR_OF_DAY, 0)
//        beginCal.set(Calendar.MINUTE, 0)
//        beginCal.set(Calendar.SECOND, 0)
//        beginCal.set(Calendar.MILLISECOND, 0)
        beginCal.add(Calendar.DAY_OF_MONTH, -1 * minDay)
        val startTime = beginCal.timeInMillis
        val endTime = System.currentTimeMillis()

        val list3 = getPeriodAppUsageTimeRank(startTime, endTime)

        beginCal.add(Calendar.DAY_OF_MONTH, -1 * (7 - minDay))
        val startTime2 = beginCal.timeInMillis
        val list7 = getPeriodAppUsageTimeRank(startTime2, endTime)

        var resultList = mutableListOf<AppTimeAnalyze>()
        list3.forEachIndexed { index, appTime ->
            val appTime7 = getInAppTimeList(appTime, list7)
            appTime7?.apply {
                var isUp = false
                var changeTime = 0L
                if (this.timeLength / 7 < appTime.timeLength / minDay) {
                    isUp = true
                    changeTime = appTime.timeLength / minDay - this.timeLength / 7
                } else {
                    isUp = false
                    changeTime = this.timeLength / 7 - appTime.timeLength / minDay
                }
                resultList.add(AppTimeAnalyze(this.timeLength / 7, appTime.timeLength / minDay, appTime.name, appTime.icon, appTime.pkg, isUp, changeTime))
            }
        }

        resultList = resultList.sortedByDescending {
//            abs(it.timeLengthPer3Days - it.timeLengthPer7Days)
            it.timeLengthPer3Days - it.timeLengthPer7Days
        }.toMutableList()

        return resultList
    }

    private fun getInAppTimeList(appTime: AppTime, appTimeList: List<AppTime>): AppTime? {
        appTimeList.forEach {
            if (it.pkg == appTime.pkg) return it
        }
        return null
    }


    companion object {

        @Volatile
        private var instance: UsageRepository? = null

        fun getInstance(appContext: Context, appUsageDao: AppUsageDao) = instance ?: synchronized(this) {
            instance ?: UsageRepository(appContext, appUsageDao).also { instance = it }
        }
    }

}