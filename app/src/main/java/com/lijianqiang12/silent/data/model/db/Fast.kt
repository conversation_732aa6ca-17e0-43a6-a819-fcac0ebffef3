package com.lijianqiang12.silent.data.model.db

import android.os.Parcel
import android.os.Parcelable
import androidx.room.ColumnInfo
import androidx.room.Entity
import androidx.room.Index
import androidx.room.PrimaryKey
import com.lijianqiang12.silent.MyConstants
import com.lijianqiang12.silent.utils.MMKVUtils
import com.lijianqiang12.silent.utils.getRandomIndexId

@Entity(indices = [Index("fastIndexId", unique = true)])
data class Fast(
    @PrimaryKey(autoGenerate = true) var id: Long,
    var userId: Int,
    var fastIndexId: String,
    var length: Int,
    var trend: Int,
    var syncState: Int,
    var syncTime: Long,
    var uuid: Long,
    var version: Int
) : Parcelable {//length单位分钟

    constructor() : this(
        0,
        MMKVUtils.getInt(MyConstants.SP_KEY_USER_ID, -1),
        getRandomIndexId(), 0, 0, 0, 0, 0, 0
    )

    constructor(length: Int) : this(
        0,
        MMKVUtils.getInt(MyConstants.SP_KEY_USER_ID, -1),
        getRandomIndexId(), length, 0, 0, 0, 0, 0
    )


    constructor(parcel: Parcel) : this(
        parcel.readLong(),
        parcel.readInt(),
        parcel.readString()!!,
        parcel.readInt(),
        parcel.readInt(),
        parcel.readInt(),
        parcel.readLong(),
        parcel.readLong(),
        parcel.readInt()
    ) {
    }

    fun copy(): Fast {
        val result = Fast()
        result.length = this.length
        return result
    }


    override fun writeToParcel(parcel: Parcel, flags: Int) {
        parcel.writeLong(id)
        parcel.writeInt(userId)
        parcel.writeString(fastIndexId)
        parcel.writeInt(length)
        parcel.writeInt(trend)
        parcel.writeInt(syncState)
        parcel.writeLong(syncTime)
        parcel.writeLong(uuid)
        parcel.writeInt(version)
    }

    override fun describeContents(): Int {
        return 0
    }

    override fun toString(): String {
        return "Fast(id=$id, userId=$userId, length=$length, trend=$trend, syncState=$syncState, syncTime=$syncTime, uuid=$uuid, version=$version)"
    }

    companion object CREATOR : Parcelable.Creator<Fast> {
        override fun createFromParcel(parcel: Parcel): Fast {
            return Fast(parcel)
        }

        override fun newArray(size: Int): Array<Fast?> {
            return arrayOfNulls(size)
        }
    }


}
