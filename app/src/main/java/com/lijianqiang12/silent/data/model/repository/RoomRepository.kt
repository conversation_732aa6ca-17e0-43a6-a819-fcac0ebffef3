package com.lijianqiang12.silent.data.model.repository

import android.content.Context
import com.lijianqiang12.silent.data.model.net.MyRetrofitClient

class RoomRepository private constructor(private val appContext: Context) {
    suspend fun myJoinedRooms() = MyRetrofitClient.service.myJoinedRooms()
    suspend fun allRooms(type:Int,lastRoomId:Long,lastLevelCount:Long) = MyRetrofitClient.service.allRooms(type,lastRoomId,lastLevelCount)


    companion object {

        @Volatile
        private var instance: RoomRepository? = null

        fun getInstance(appContext: Context) = instance ?: synchronized(this) {
            instance ?: RoomRepository(appContext).also { instance = it }
        }
    }
}

