package com.lijianqiang12.silent.data.viewmodel

import androidx.lifecycle.LiveData
import androidx.lifecycle.MutableLiveData
import androidx.lifecycle.switchMap
import androidx.lifecycle.viewModelScope
import com.jeremyliao.liveeventbus.LiveEventBus
import com.lijianqiang12.silent.LiveBus
import com.lijianqiang12.silent.MyConstants
import com.lijianqiang12.silent.TheApplication
import com.lijianqiang12.silent.data.model.db.AppLimit
import com.lijianqiang12.silent.data.model.db.AppUsage
import com.lijianqiang12.silent.data.model.db.DayLimit
import com.lijianqiang12.silent.data.model.db.WhiteApp
import com.lijianqiang12.silent.data.model.repository.AppLimitRepository
import com.lijianqiang12.silent.data.model.repository.DayLimitRepository
import com.lijianqiang12.silent.data.model.repository.UsageRepository
import com.lijianqiang12.silent.utils.MMKVUtils
import dagger.hilt.android.lifecycle.HiltViewModel
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.launch
import javax.inject.Inject

@HiltViewModel
class MonitorViewModel @Inject constructor(
    val dayLimitRepository: DayLimitRepository,
    val appLimitRepository: AppLimitRepository,
    val usageRepository: UsageRepository
) : BaseViewModel() {

    private val TAG = "MonitorViewModel"

    /*同步相关*/
    suspend fun getAllAppLimitList() = appLimitRepository.getAllAppLimitList()
    suspend fun getAppLimitWithState(state: Int) = appLimitRepository.getAppLimitWithState(state)

    /*全局限时*/
    fun getTodayLimits(dayName: String) = dayLimitRepository.getTodayLimits(dayName)
    fun getTodayLimitsImmediately(dayName: String) = dayLimitRepository.getTodayLimitsImmediately(dayName)
    fun getDayLimits(isWorkDay: Boolean) = dayLimitRepository.getDayLimits(isWorkDay)

    suspend fun insertAppUsage(appUsage: AppUsage) = usageRepository.insertAppUsage(appUsage)

    fun insertDayLimit(dayLimit: DayLimit) {
        viewModelScope.launch(Dispatchers.IO) {
            dayLimitRepository.insertDayLimit(dayLimit)
        }
    }

    fun updateDayLimit(dayLimit: DayLimit) {
        viewModelScope.launch(Dispatchers.IO) {
            dayLimitRepository.updateDayLimit(dayLimit)
            TheApplication.getInstance().globalParams.lastMinute = -1
        }
    }

    private val userIdLiveData = MutableLiveData(MMKVUtils.getInt(MyConstants.SP_KEY_USER_ID, -1))
    fun setUserId(userId: Int) {
        if (userIdLiveData.value != userId) {
            userIdLiveData.value = userId
        }
    }

    // 根据 userIdLiveData 的变化使用 switchMap 动态查询
    val appLimits: LiveData<MutableList<AppLimit>> = userIdLiveData.switchMap { userId ->
        appLimitRepository.getAppLimits(userId)
    }

    /*应用限时*/
//    fun getAppLimits() = appLimitRepository.getAppLimits()
    fun createAppLimit(appLimit: AppLimit) {
        viewModelScope.launch(Dispatchers.IO) {
            val lastAppLimit = appLimitRepository.getLastAppLimit()
            var trend = 0
            if (lastAppLimit != null) {
                trend = lastAppLimit.trend + 1
            }
            appLimit.trend = trend

            appLimitRepository.createAppLimit(appLimit)
            LiveEventBus.get(LiveBus.START_SYNC_APP_LIMIT, String::class.java).post("")
        }
    }

    fun updateAppLimit(appLimit: AppLimit, sync: Boolean = true) {
        viewModelScope.launch(Dispatchers.IO) {
            if (appLimit.syncState > 0) {
                appLimit.syncTime = System.currentTimeMillis()
                appLimit.syncState = 1
                appLimit.version += 1
            }

            appLimitRepository.updateAppLimit(appLimit)
            if (sync) LiveEventBus.get(LiveBus.START_SYNC_APP_LIMIT, String::class.java).post("")
        }
    }

    fun deleteAppLimit(appLimit: AppLimit) {
        viewModelScope.launch(Dispatchers.IO) {
            if (appLimit.syncState > 0) {
                appLimit.syncTime = System.currentTimeMillis()
                appLimit.syncState = -1
                appLimit.version += 1
                appLimitRepository.updateAppLimit(appLimit)
                LiveEventBus.get(LiveBus.START_SYNC_APP_LIMIT, String::class.java).post("")
            } else {
                appLimitRepository.deleteAppLimit(appLimit)
            }

        }
    }


    /*获取使用时长*/
    suspend fun getDayUsageLength() = usageRepository.getTodayUsageLength()
    suspend fun getDayUsageLengthWithoutWhite(whiteList: MutableList<WhiteApp>) = usageRepository.getTodayUsageLengthWithoutWhite(whiteList)

    //    suspend fun getAppUsageLength(pkg: String, startTime: Long, endTime: Long) = usageRepository.getTodayUsageLength(pkg, startTime, endTime)
    suspend fun getAppsUsageLength(pkgList: MutableList<String>, startTime: Long, endTime: Long) =
        usageRepository.getPkgListUsageLength(pkgList, startTime, endTime)

    suspend fun getAppUsageLengthFromTrueTime(pkg: String, startTime: Long, endTime: Long) =
        usageRepository.getTodayUsageLengthFromTrueTime(pkg, startTime, endTime)


//    private val allUsedTimeAndSupervisorLiveData = MutableLiveData<AllUsedTimeAndSupervisor>()
//    private val appUsedTimeAndSupervisorLiveData = MutableLiveData<MutableList<AppUsedTimeAndSupervisor>>()
//
//    private val appLimitList = appLimitRepository.getAppLimitList()
//    private val runningPlan = dayLimitRepository.getRunningPlan()

//    fun refreshAll() {
//        viewModelScope.launch {
//            withContext(Dispatchers.Default) {
//                refreshAllTimeLimit()
//                refreshAppLimit()
//            }
//        }
//    }

    //全局限时
//    private val hasAllUsedTime: MutableLiveData<AllUsedTimeAndSupervisor> by lazy {
//        viewModelScope.launch {
//            refreshAllTimeLimit()
//        }
//        allUsedTimeAndSupervisorLiveData
//    }

//    fun getHasAllUsedTime(): LiveData<AllUsedTimeAndSupervisor> {
//        return hasAllUsedTime
//    }

//    fun updateAllTimeLimit(dayLimit: DayLimit) {
//        viewModelScope.launch {
//            withContext(Dispatchers.Default) {
//                dayLimitRepository.updatePlan(dayLimit)
//                refreshAllTimeLimit()
//                LiveBus.getInstance().with(String::class.java).postValue("change")
//            }
//        }
//    }

//    private suspend fun refreshAllTimeLimit() {
//        withContext(Dispatchers.Default) {
//            val allUsedTime = async { usageRepository.getTodayUsageLength(false) }
//            val plan2 = async { dayLimitRepository.getActivePlan() }
//            allUsedTimeAndSupervisorLiveData.postValue(AllUsedTimeAndSupervisor(allUsedTime.await(), plan2.await()))
//        }
//    }


    //app限时
//    private val hasAppUsedTime: MutableLiveData<MutableList<AppUsedTimeAndSupervisor>> by lazy {
//        viewModelScope.launch {
//            Log.d(TAG, "refreshAppLimit")
//            refreshAppLimit()
//        }
//        appUsedTimeAndSupervisorLiveData
//    }

//    fun getHasAppUsedTime(): LiveData<MutableList<AppUsedTimeAndSupervisor>> {
//        return hasAppUsedTime
//    }
//
//    private suspend fun findAppUsage(appPkg: String, appUsedTimes: List<AppTime>): AppTime {
//        appUsedTimes.forEachIndexed { index, appTime ->
//            if (appPkg == appTime.pkg) {
//                return appTime
//            }
//        }
//        return AppTime(0, getAppName(appPkg), getAppIcon(appPkg), appPkg)
//    }
//
//
//    fun createAllAppLimit(appLimit: AppLimit) {
//        viewModelScope.launch {
//            withContext(Dispatchers.Default) {
//                appLimit.planId = dayLimitRepository.getActivePlan().planId
//                appLimitRepository.createAppLimit(appLimit)
//                refreshAppLimit()
//                LiveBus.getInstance().with(String::class.java).postValue("change")
//            }
//        }
//    }
//
//
//    fun updateAllAppLimit(appLimit: AppLimit) {
//        viewModelScope.launch {
//            withContext(Dispatchers.Default) {
//                appLimitRepository.updateAppLimit(appLimit)
//                refreshAppLimit()
//                LiveBus.getInstance().with(String::class.java).postValue("change")
//            }
//        }
//    }
//
//    fun deleteAllAppLimit(appLimit: AppLimit) {
//        viewModelScope.launch {
//            withContext(Dispatchers.Default) {
//                appLimitRepository.deleteAppLimit(appLimit)
//                refreshAppLimit()
//                LiveBus.getInstance().with(String::class.java).postValue("change")
//            }
//        }
//    }
//
//    private suspend fun refreshAppLimit() {
//        withContext(Dispatchers.Default) {
//            val endTime = System.currentTimeMillis()
//            val beginCal = Calendar.getInstance()
//            beginCal.set(Calendar.HOUR_OF_DAY, 0)
//            beginCal.set(Calendar.MINUTE, 0)
//            beginCal.set(Calendar.SECOND, 0)
//            beginCal.set(Calendar.MILLISECOND, 0)
//            val startTime = beginCal.timeInMillis
//
//            val appUsedTimesDeferred = async { usageRepository.getPeriodUsageList(startTime, endTime) }
//            val appLimitsDeferred = async { appLimitRepository.getAppLimits(dayLimitRepository.getActivePlan().planId) }
//            val appUsedTimes = appUsedTimesDeferred.await()
//            val appLimits = appLimitsDeferred.await()
//
//            val list = mutableListOf<AppUsedTimeAndSupervisor>()
//            appLimits.forEachIndexed { index, appLimit ->
//                val usedTime = if (appLimit.ifAllDay) {
//                    appUsedTimes
//                } else {
//                    usageRepository.getPeriodUsageList(startTime + appLimit.startTime * 1000, startTime + appLimit.endTime * 1000)
//                }
//                val appTime = findAppUsage(appLimit.appPkg, usedTime)
//                list.add(AppUsedTimeAndSupervisor(appTime.timeLength / 1000, appLimit))
//            }
//            appUsedTimeAndSupervisorLiveData.postValue(list)
//        }
//
//
//    }


}