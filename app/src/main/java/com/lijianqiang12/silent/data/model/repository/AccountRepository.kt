package com.lijianqiang12.silent.data.model.repository

import android.content.Context
import com.lijianqiang12.silent.data.model.net.MyRetrofitClient

class AccountRepository private constructor(private val appContext: Context) {
    suspend fun queryUserInfo() = MyRetrofitClient.service.queryUserInfo()
    suspend fun updateAvatar(avatar: String) = MyRetrofitClient.service.updateAvatar(avatar)
    suspend fun updateUsername(username: String) = MyRetrofitClient.service.updateUsername(username)
    suspend fun updateGender(gender: String) = MyRetrofitClient.service.updateGender(gender)
    suspend fun updateWord(word: String) = MyRetrofitClient.service.updateWord(word)
    suspend fun updateMobile(mobile: String, code: String) = MyRetrofitClient.service.updateMobile(mobile, code)
    suspend fun bindQQ(qqId: String) = MyRetrofitClient.service.bindQQ(qqId)
    suspend fun bindWX(wxId: String) = MyRetrofitClient.service.bindWX(wxId)
    suspend fun bindSINA(sinaId: String) = MyRetrofitClient.service.bindSINA(sinaId)
    suspend fun deleteQQ() = MyRetrofitClient.service.deleteQQ()
    suspend fun deleteWX() = MyRetrofitClient.service.deleteWX()
    suspend fun deleteSINA() = MyRetrofitClient.service.deleteSINA()
    suspend fun deleteAllAccount() = MyRetrofitClient.service.deleteAllAccount()


//    suspend fun verifyCode(phone: String, code: String) = MyRetrofitClient.service.verifyCode(phone, code)
//    suspend fun socialAccountLogin(socialType: Int, uid: String, username: String, avatar: String, gender: String) = MyRetrofitClient.service.socialAccountLogin(socialType, uid, username, avatar, gender)
//    suspend fun refreshState() = MyRetrofitClient.service.refreshState()


    companion object {

        @Volatile
        private var instance: AccountRepository? = null

        fun getInstance(appContext: Context) = instance ?: synchronized(this) {
            instance ?: AccountRepository(appContext).also { instance = it }
        }
    }
}

