package com.lijianqiang12.silent.data.viewmodel

import androidx.lifecycle.MutableLiveData
import com.lijianqiang12.silent.data.model.net.pojos.ApiResponse
import com.lijianqiang12.silent.data.model.net.pojos.LoginResponse
import com.lijianqiang12.silent.data.model.net.convertHttpRes
import com.lijianqiang12.silent.data.model.net.handlingApiExceptions
import com.lijianqiang12.silent.data.model.net.handlingExceptions
import com.lijianqiang12.silent.data.model.net.handlingHttpResponse
import com.lijianqiang12.silent.data.model.repository.LoginRepository
import dagger.hilt.android.lifecycle.HiltViewModel
import javax.inject.Inject

@HiltViewModel
class LoginViewModel @Inject constructor(val loginRepository: LoginRepository) : BaseViewModel() {

    private val TAG = "LoginViewModel"

    private val _getVerifyCodeLiveData = MutableLiveData<ApiResponse<Any>>()
    private val _verifyCodeLiveData = MutableLiveData<ApiResponse<LoginResponse>>()
    private val _socialAccountLoginLiveData = MutableLiveData<ApiResponse<LoginResponse>>()

    val getVerifyCodeLiveData = _getVerifyCodeLiveData
    val verifyCodeLiveData = _verifyCodeLiveData
    val socialAccountLoginLiveData = _socialAccountLoginLiveData


    fun refreshGetVerifyCode(codeType: Int, phone: String) {
        launchOnIO(
            tryBlock = {
                loginRepository.getVerifyCode(codeType, phone).run {
                    handlingHttpResponse<Any>(
                        convertHttpRes(),
                        successBlock = {
                            _getVerifyCodeLiveData.postValue(ApiResponse(200, "", null))
                        },
                        failureBlock = { ex ->
                            _getVerifyCodeLiveData.postValue(ApiResponse(ex.code, ex.msg, null))
                            handlingApiExceptions(ex)
                        }
                    )
                }
            },
            // 请求异常处理
            catchBlock = { e ->
                _getVerifyCodeLiveData.postValue(ApiResponse(-1, "", null))
                handlingExceptions(e)
            }
        )

    }

    fun refreshVerifyCode(phone: String, code: String, inviteCode: Int) {
        launchOnIO(
            tryBlock = {
                loginRepository.verifyCode(phone, code, inviteCode).run {
                    handlingHttpResponse<LoginResponse>(
                        convertHttpRes(),
                        successBlock = {
                            _verifyCodeLiveData.postValue(ApiResponse(200, "", it))
                        },
                        failureBlock = { ex ->
                            _verifyCodeLiveData.postValue(ApiResponse(ex.code, ex.msg, null))
                            handlingApiExceptions(ex)
                        }
                    )
                }
            },
            // 请求异常处理
            catchBlock = { e ->
                _verifyCodeLiveData.postValue(ApiResponse(-1, "", null))
                handlingExceptions(e)
            }
        )

    }

    fun refreshSocialAccountLogin(socialType: Int, uid: String, username: String, avatar: String, gender: String, inviteCode: Int) {
        launchOnIO(
            tryBlock = {
                loginRepository.socialAccountLogin(socialType, uid, username, avatar, gender, inviteCode).run {
                    handlingHttpResponse<LoginResponse>(
                        convertHttpRes(),
                        successBlock = {
                            _socialAccountLoginLiveData.postValue(ApiResponse(200, "", it))
                        },
                        failureBlock = { ex ->
                            _socialAccountLoginLiveData.postValue(ApiResponse(ex.code, ex.msg, null))
                            handlingApiExceptions(ex)
                        }
                    )
                }
            },
            // 请求异常处理
            catchBlock = { e ->
                _socialAccountLoginLiveData.postValue(ApiResponse(-1, "", null))
                handlingExceptions(e)
            }
        )

    }


}