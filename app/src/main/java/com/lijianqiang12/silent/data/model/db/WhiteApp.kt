package com.lijianqiang12.silent.data.model.db

import android.os.Parcel
import android.os.Parcelable
import androidx.room.ColumnInfo
import androidx.room.Entity
import androidx.room.Ignore
import androidx.room.Index
import androidx.room.PrimaryKey
import com.lijianqiang12.silent.MyConstants
import com.lijianqiang12.silent.utils.MMKVUtils
import com.lijianqiang12.silent.utils.getRandomIndexId

@Entity(indices = [Index("tomatoIndexId"), Index("scheduleIndexId"), Index("whiteAppIndexId", unique = true)])
data class WhiteApp(
    @PrimaryKey(autoGenerate = true) var id: Long,
    var userId: Int,
    var whiteAppIndexId: String,
    var tomatoIndexId: String,
    var scheduleIndexId: String,
    var pkg: String,
    var mainActivity: String,
    var maxLen: Int,//min
    var trend: Int,
    var syncState: Int,
    var syncTime: Long,
    var uuid: Long,
    var version: Int,
) : Parcelable {

    constructor(parcel: Parcel) : this(
        parcel.readLong(),
        parcel.readInt(),
        parcel.readString()!!,
        parcel.readString()!!,
        parcel.readString()!!,
        parcel.readString()!!,
        parcel.readString()!!,
        parcel.readInt(),
        parcel.readInt(),
        parcel.readInt(),
        parcel.readLong(),
        parcel.readLong(),
        parcel.readInt()
    )

    constructor() : this(
        0,
        MMKVUtils.getInt(MyConstants.SP_KEY_USER_ID, -1),
        getRandomIndexId(), "", "", "", "", -1, 0, 0, System.currentTimeMillis(), 0, 0
    )


    @Ignore
    constructor(pkg: String, mainActivity: String)
            : this(
        0,
        MMKVUtils.getInt(MyConstants.SP_KEY_USER_ID, -1),
        getRandomIndexId(), "", "", pkg, mainActivity, -1, 0, 0, System.currentTimeMillis(), 0, 0
    )

    @Ignore
    constructor(pkg: String, mainActivity: String, maxLen: Int)
            : this(
        0,
        MMKVUtils.getInt(MyConstants.SP_KEY_USER_ID, -1),
        getRandomIndexId(), "", "", pkg, mainActivity, maxLen, 0, 0, System.currentTimeMillis(), 0, 0
    )

    @Ignore
    constructor(pkg: String, mainActivity: String, maxLen: Int, trend: Int)
            : this(
        0,
        MMKVUtils.getInt(MyConstants.SP_KEY_USER_ID, -1),
        getRandomIndexId(), "", "", pkg, mainActivity, maxLen, trend, 0, System.currentTimeMillis(), 0, 0
    )

    @Ignore
    constructor(tomatoIndexId: String, pkg: String, mainActivity: String, maxLen: Int)
            : this(
        0,
        MMKVUtils.getInt(MyConstants.SP_KEY_USER_ID, -1),
        getRandomIndexId(), tomatoIndexId, "", pkg, mainActivity, maxLen, 0, 0, System.currentTimeMillis(), 0, 0
    )

    @Ignore
    constructor(tomatoIndexId: String, scheduleIndexId: String, pkg: String, mainActivity: String, maxLen: Int)
            : this(
        0,
        MMKVUtils.getInt(MyConstants.SP_KEY_USER_ID, -1),
        getRandomIndexId(), tomatoIndexId, scheduleIndexId, pkg, mainActivity, maxLen, 0, 0, System.currentTimeMillis(), 0, 0
    )


    override fun equals(other: Any?): Boolean {
        val newObj = other as WhiteApp
        if (newObj.id != this.id) return false
        if (newObj.userId != this.userId) return false
        if (newObj.whiteAppIndexId != this.whiteAppIndexId) return false
        if (newObj.tomatoIndexId != this.tomatoIndexId) return false
        if (newObj.scheduleIndexId != this.scheduleIndexId) return false
        if (newObj.pkg != (this.pkg)) return false
        if (newObj.mainActivity != (this.mainActivity)) return false
        if (newObj.maxLen != this.maxLen) return false
        if (newObj.trend != this.trend) return false
        if (newObj.syncState != this.syncState) return false
        if (newObj.syncTime != this.syncTime) return false
        if (newObj.uuid != this.uuid) return false
        if (newObj.version != this.version) return false
        return true
    }

    override fun hashCode(): Int {
        var result = id.hashCode()
        result = 31 * result + userId
        result = 31 * result + tomatoIndexId.hashCode()
        result = 31 * result + scheduleIndexId.hashCode()
        result = 31 * result + pkg.hashCode()
        result = 31 * result + mainActivity.hashCode()
        result = 31 * result + maxLen
        result = 31 * result + trend
        result = 31 * result + syncState
        result = 31 * result + syncTime.hashCode()
        result = 31 * result + uuid.hashCode()
        result = 31 * result + version
        return result
    }

    override fun writeToParcel(parcel: Parcel, flags: Int) {
        parcel.writeLong(id)
        parcel.writeInt(userId)
        parcel.writeString(whiteAppIndexId)
        parcel.writeString(tomatoIndexId)
        parcel.writeString(scheduleIndexId)
        parcel.writeString(pkg)
        parcel.writeString(mainActivity)
        parcel.writeInt(maxLen)
        parcel.writeInt(trend)
        parcel.writeInt(syncState)
        parcel.writeLong(syncTime)
        parcel.writeLong(uuid)
        parcel.writeInt(version)
    }

    override fun describeContents(): Int {
        return 0
    }

    override fun toString(): String {
        return "WhiteApp(id=$id, userId=$userId, tomatoIndexId='$tomatoIndexId', scheduleIndexId='$scheduleIndexId', pkg='$pkg', mainActivity='$mainActivity', maxLen=$maxLen, trend=$trend, syncState=$syncState, syncTime=$syncTime, uuid=$uuid, version=$version)"
    }


    companion object CREATOR : Parcelable.Creator<WhiteApp> {
        override fun createFromParcel(parcel: Parcel): WhiteApp {
            return WhiteApp(parcel)
        }

        override fun newArray(size: Int): Array<WhiteApp?> {
            return arrayOfNulls(size)
        }
    }

    fun copy(): WhiteApp {
        val result = WhiteApp()

        result.tomatoIndexId = this.tomatoIndexId
        result.scheduleIndexId = this.scheduleIndexId
        result.pkg = this.pkg
        result.mainActivity = this.mainActivity
        result.maxLen = this.maxLen

        return result

    }


}