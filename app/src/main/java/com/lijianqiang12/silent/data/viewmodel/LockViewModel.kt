package com.lijianqiang12.silent.data.viewmodel

import androidx.lifecycle.LiveData
import androidx.lifecycle.MutableLiveData
import androidx.lifecycle.switchMap
import androidx.lifecycle.viewModelScope
import com.blankj.utilcode.util.LogUtils
import com.jeremyliao.liveeventbus.LiveEventBus
import com.lijianqiang12.silent.*
import com.lijianqiang12.silent.component.activity.lock.whiteapp.AppInfo
import com.lijianqiang12.silent.data.model.db.*
import com.lijianqiang12.silent.data.model.net.pojos.ForceUnlockPwd
import com.lijianqiang12.silent.data.model.net.pojos.LockBg
import com.lijianqiang12.silent.data.model.net.pojos.NetworkState
import com.lijianqiang12.silent.data.model.net.convertHttpRes
import com.lijianqiang12.silent.data.model.net.handlingApiExceptions
import com.lijianqiang12.silent.data.model.net.handlingExceptions
import com.lijianqiang12.silent.data.model.net.handlingHttpResponse
import com.lijianqiang12.silent.data.model.repository.LockRepository
import com.lijianqiang12.silent.utils.MMKVUtils
import com.lijianqiang12.silent.utils.getRandomIndexId
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.launch
import kotlinx.coroutines.sync.withLock
import kotlinx.coroutines.withContext

class LockViewModel(val lockRepository: LockRepository) : BaseViewModel() {

    private val TAG = "LockViewModel"

    private val _lockBgLiveData = MutableLiveData<NetworkState<MutableList<LockBg>>>()
    private val _forceUnlockPwd = MutableLiveData<NetworkState<ForceUnlockPwd>>()

    /*同步相关*/
    suspend fun getAllWhiteAppList() = lockRepository.getAllWhiteAppList()
    suspend fun getWhiteAppsWithState(state: Int) = lockRepository.getWhiteAppsWithState(state)
    suspend fun getAllFastList() = lockRepository.getAllFastList()
    suspend fun getFastWithState(state: Int) = lockRepository.getFastWithState(state)

    suspend fun getUnUploadHistory() = lockRepository.getUnUploadHistory()

    suspend fun getAllTomatoList() = lockRepository.getAllTomatoList()
    suspend fun getTomatoWithState(state: Int) = lockRepository.getTomatoWithState(state)

    suspend fun getAllScheduleList() = lockRepository.getAllScheduleList()
    suspend fun getScheduleWithState(state: Int) = lockRepository.getScheduleWithState(state)


    val fastsLiveData = lockRepository.getFasts()
    fun createFast(fast: Fast) {
        viewModelScope.launch {
            withContext(Dispatchers.IO) {
                val lastFast = lockRepository.getLastFast()
                var trend = 0
                if (lastFast != null) {
                    trend = lastFast.trend + 1
                }
                fast.trend = trend

                lockRepository.createFast(fast)
                LiveEventBus.get(LiveBus.START_SYNC_FAST, String::class.java).post("")
            }
        }
    }

    fun updateFast(fast: Fast, sync: Boolean = true) {
        viewModelScope.launch {
            withContext(Dispatchers.IO) {
                if (fast.syncState > 0) {
                    fast.syncTime = System.currentTimeMillis()
                    fast.syncState = 1
                    fast.version += 1
                }
                lockRepository.updateFast(fast)

                if (sync) LiveEventBus.get(LiveBus.START_SYNC_FAST, String::class.java).post("")
            }
        }
    }

    fun deleteFast(fast: Fast) {
        viewModelScope.launch {
            withContext(Dispatchers.IO) {
                if (fast.syncState > 0) {
                    fast.syncTime = System.currentTimeMillis()
                    fast.syncState = -1
                    fast.version = fast.version + 1
                    lockRepository.updateFast(fast)

                    LiveEventBus.get(LiveBus.START_SYNC_FAST, String::class.java).post("")
                } else {
                    lockRepository.deleteFast(fast)
                }
            }
        }
    }


    val tomatoesLiveData = lockRepository.getTomatoesWithSub()

    //    suspend fun getTomatoWithSub(tomatoId: Long) = lockRepository.getTomatoWithSub(tomatoId)
    suspend fun getTomatoWithSub(tomatoId: String) = lockRepository.getTomatoWithSub(tomatoId)
    fun createTomatoWithSub(tomatoWithSub: TomatoWithSub) {
        viewModelScope.launch {

            val lastTomato = lockRepository.getLastTomato()
            var trend = 0
            if (lastTomato != null) {
                trend = lastTomato.trend + 1
            }

            val newTomato = tomatoWithSub.tomato.copy()
            newTomato.trend = trend
            val newTomatoIndexId = getRandomIndexId()
            newTomato.tomatoIndexId = newTomatoIndexId
            lockRepository.createTomato(newTomato)

            val newTomatoWhiteList = mutableListOf<WhiteApp>()
            tomatoWithSub.whiteList.forEachIndexed { index, whiteApp ->
                val newWhiteApp = whiteApp.copy()
                newWhiteApp.tomatoIndexId = newTomatoIndexId
                newWhiteApp.trend = index
                newTomatoWhiteList.add(newWhiteApp)
            }
            lockRepository.createWhiteApps(newTomatoWhiteList)

            LiveEventBus.get(LiveBus.START_SYNC_TOMATO, String::class.java).post("")
            LiveEventBus.get(LiveBus.START_SYNC_WHITE_APP, String::class.java).post("")
        }
    }

    fun updateTomatoWithSub(tomatoWithSub: TomatoWithSub) {
        viewModelScope.launch {
            withContext(Dispatchers.IO) {
                val list = mutableListOf<WhiteApp>()
                val syncTime = System.currentTimeMillis()

                val existWhiteApp = lockRepository.getWhiteAppsWithTomatoIdAtOnce(tomatoWithSub.tomato.tomatoIndexId)
                val newWhiteApp = tomatoWithSub.whiteList
                val needAddWhiteApp = mutableListOf<WhiteApp>()
                val needUpdateWhiteApp = mutableListOf<WhiteApp>()
                val needDeleteWhiteApp = mutableListOf<WhiteApp>()

                newWhiteApp.forEachIndexed { index, whiteApp ->
                    var appExist = false
                    existWhiteApp.forEach { exist ->
                        if (exist.pkg == whiteApp.pkg && exist.mainActivity == whiteApp.mainActivity) {
                            appExist = true
                            if (exist.maxLen != whiteApp.maxLen || exist.trend != whiteApp.trend) {
                                needUpdateWhiteApp.add(whiteApp)
                            }
                        }
                    }
                    if (!appExist) {
                        needAddWhiteApp.add(whiteApp)
                    }
                }

                existWhiteApp.forEachIndexed { index, whiteApp ->
                    var appExist = false
                    newWhiteApp.forEach { new ->
                        if (new.pkg == whiteApp.pkg && new.mainActivity == whiteApp.mainActivity) {
                            appExist = true
                        }
                    }
                    if (!appExist) {
                        needDeleteWhiteApp.add(whiteApp)
                    }
                }

                needDeleteWhiteApp.forEach {
                    if (it.syncState > 0) {
                        it.syncState = -1
                        it.version = it.version + 1
                        it.syncTime = syncTime
                        lockRepository.updateWhiteApp(it)
                    } else {
                        lockRepository.deleteWhiteApp(it)
                    }
                }

                needUpdateWhiteApp.forEach {
                    it.syncState = 1
                    it.version = it.version + 1
                    it.syncTime = syncTime
                    lockRepository.updateWhiteApp(it)
                }

                needAddWhiteApp.forEachIndexed { index, whiteApp ->
                    val white = whiteApp.copy()
                    white.tomatoIndexId = tomatoWithSub.tomato.tomatoIndexId
                    white.syncTime = syncTime
                    white.trend = index
                    list.add(white)
                }

                lockRepository.createWhiteApps(list)

                if (tomatoWithSub.tomato.syncState > 0) {
                    tomatoWithSub.tomato.syncState = 1
                    tomatoWithSub.tomato.syncTime = syncTime
                    tomatoWithSub.tomato.version += 1
                }
                lockRepository.updateTomato(tomatoWithSub.tomato)

                LiveEventBus.get(LiveBus.START_SYNC_TOMATO, String::class.java).post("")
                LiveEventBus.get(LiveBus.START_SYNC_WHITE_APP, String::class.java).post("")
            }
        }
    }

    fun deleteTomatoWithSub(tomatoWithSub: TomatoWithSub) {
        viewModelScope.launch {
            if (tomatoWithSub.tomato.syncState > 0) {
                tomatoWithSub.tomato.syncState = -1
                tomatoWithSub.tomato.version += 1
                tomatoWithSub.tomato.syncTime = System.currentTimeMillis()
                lockRepository.updateTomato(tomatoWithSub.tomato)
            } else {
                lockRepository.deleteTomato(tomatoWithSub.tomato)
            }

            tomatoWithSub.whiteList.forEach {
                if (it.syncState > 0) {
                    it.syncState = -1
                    it.version = it.version + 1
                    it.syncTime = System.currentTimeMillis()
                    lockRepository.updateWhiteApp(it)
                } else {
                    lockRepository.deleteWhiteApp(it)
                }
            }
            LiveEventBus.get(LiveBus.START_SYNC_TOMATO, String::class.java).post("")
            LiveEventBus.get(LiveBus.START_SYNC_WHITE_APP, String::class.java).post("")
        }
    }

    fun updateTomato(tomato: Tomato, sync: Boolean = true) {
        viewModelScope.launch(Dispatchers.IO) {
            if (tomato.syncState > 0) {
                tomato.syncState = 1
                tomato.version += 1
                tomato.syncTime = System.currentTimeMillis()
            }
            lockRepository.updateTomato(tomato)
            if (sync) LiveEventBus.get(LiveBus.START_SYNC_TOMATO, String::class.java).post("")
        }
    }


    private val userIdLiveData = MutableLiveData(MMKVUtils.getInt(MyConstants.SP_KEY_USER_ID, -1))

    val schedulesLiveData: LiveData<MutableList<ScheduleWithSub>> = userIdLiveData.switchMap { userId ->
        lockRepository.getSchedulesWithSub(userId)

    }

    //    val schedulesLiveData = lockRepository.getSchedulesWithSub()
    suspend fun getScheduleWithSub(scheduleId: String) = lockRepository.getScheduleWithSub(scheduleId)
    fun createScheduleWithSub(scheduleWithSub: ScheduleWithSub) {
        viewModelScope.launch {

            val lastSchedule = lockRepository.getLastSchedule()
            var trend = 0
            if (lastSchedule != null) {
                trend = lastSchedule.trend + 1
            }

            val newSchedule = scheduleWithSub.schedule.copy()
            newSchedule.trend = trend
            val scheduleIndexId = getRandomIndexId()
            newSchedule.scheduleIndexId = scheduleIndexId
            lockRepository.createSchedule(newSchedule)

            val newWhiteList = mutableListOf<WhiteApp>()

            scheduleWithSub.whiteList.forEachIndexed { index, whiteApp ->
                val newWhiteApp = whiteApp.copy()
                newWhiteApp.scheduleIndexId = scheduleIndexId
                newWhiteApp.trend = index
                newWhiteList.add(newWhiteApp)
            }
            lockRepository.createWhiteApps(newWhiteList)

            LiveEventBus.get(LiveBus.START_SYNC_SCHEDULE, String::class.java).post("")
            LiveEventBus.get(LiveBus.START_SYNC_WHITE_APP, String::class.java).post("")

            TheApplication.getInstance().globalParams.lastMinute = -1
        }
    }

    fun updateScheduleWithSub(scheduleWithSub: ScheduleWithSub) {
        viewModelScope.launch {
            withContext(Dispatchers.IO) {
                val list = mutableListOf<WhiteApp>()
                val syncTime = System.currentTimeMillis()

                val existWhiteApp = lockRepository.getWhiteAppsWithScheduleIdAtOnce(scheduleWithSub.schedule.scheduleIndexId)
                val newWhiteApp = scheduleWithSub.whiteList
                val needAddWhiteApp = mutableListOf<WhiteApp>()
                val needUpdateWhiteApp = mutableListOf<WhiteApp>()
                val needDeleteWhiteApp = mutableListOf<WhiteApp>()

                newWhiteApp.forEachIndexed { index, whiteApp ->
                    var appExist = false
                    existWhiteApp.forEach { exist ->
                        if (exist.pkg == whiteApp.pkg && exist.mainActivity == whiteApp.mainActivity) {
                            appExist = true
                            if (exist.maxLen != whiteApp.maxLen || exist.trend != whiteApp.trend) {
                                LogUtils.d("update white app ${exist.whiteAppIndexId} ${exist.pkg} ${exist.mainActivity} ${exist.mainActivity}, ${exist.maxLen} ${whiteApp.maxLen}, ${exist.trend} ${whiteApp.trend}")
                                needUpdateWhiteApp.add(whiteApp)
                            }
                        }
                    }
                    if (!appExist) {
                        needAddWhiteApp.add(whiteApp)
                    }
                }

                existWhiteApp.forEachIndexed { index, whiteApp ->
                    var appExist = false
                    newWhiteApp.forEach { new ->
                        if (new.pkg == whiteApp.pkg && new.mainActivity == whiteApp.mainActivity) {
                            appExist = true
                        }
                    }
                    if (!appExist) {
                        needDeleteWhiteApp.add(whiteApp)
                    }
                }

                needDeleteWhiteApp.forEach {
                    if (it.syncState > 0) {
                        it.syncState = -1
                        it.version += 1
                        it.syncTime = syncTime
                        lockRepository.updateWhiteApp(it)
                    } else {
                        lockRepository.deleteWhiteApp(it)
                    }
                }

                needUpdateWhiteApp.forEach {
                    it.syncState = 1
                    it.version += 1
                    it.syncTime = syncTime
                    lockRepository.updateWhiteApp(it)
                }

                needAddWhiteApp.forEachIndexed { index, whiteApp ->
                    val white = whiteApp.copy()
                    white.scheduleIndexId = scheduleWithSub.schedule.scheduleIndexId
                    white.syncTime = syncTime
                    white.trend = index
                    list.add(white)
                }

                lockRepository.createWhiteApps(list)

                if (scheduleWithSub.schedule.syncState > 0) {
                    scheduleWithSub.schedule.syncState = 1
                    scheduleWithSub.schedule.version += 1
                    scheduleWithSub.schedule.syncTime = syncTime
                }
                lockRepository.updateSchedule(scheduleWithSub.schedule)

                LiveEventBus.get(LiveBus.START_SYNC_SCHEDULE, String::class.java).post("")
                LiveEventBus.get(LiveBus.START_SYNC_WHITE_APP, String::class.java).post("")

                TheApplication.getInstance().globalParams.lastMinute = -1
            }
        }
    }

    fun deleteScheduleWithSub(scheduleWithSub: ScheduleWithSub) {
        viewModelScope.launch(Dispatchers.IO) {
            if (scheduleWithSub.schedule.syncState > 0) {
                scheduleWithSub.schedule.syncState = -1
                scheduleWithSub.schedule.version += 1
                scheduleWithSub.schedule.syncTime = System.currentTimeMillis()
                lockRepository.updateSchedule(scheduleWithSub.schedule)
            } else {
                lockRepository.deleteSchedule(scheduleWithSub.schedule)
            }

            scheduleWithSub.whiteList.forEach {
                if (it.syncState > 0) {
                    it.syncState = -1
                    it.version += 1
                    it.syncTime = System.currentTimeMillis()
                    lockRepository.updateWhiteApp(it)
                } else {
                    lockRepository.deleteWhiteApp(it)
                }
            }

            LiveEventBus.get(LiveBus.START_SYNC_SCHEDULE, String::class.java).post("")
            LiveEventBus.get(LiveBus.START_SYNC_WHITE_APP, String::class.java).post("")
        }
    }


    fun updateSchedule(schedule: Schedule, sync: Boolean = true) {
        viewModelScope.launch {
            withContext(Dispatchers.IO) {
                if (schedule.syncState > 0) {
                    schedule.syncState = 1
                    schedule.version += 1
                    schedule.syncTime = System.currentTimeMillis()
                }
                lockRepository.updateSchedule(schedule)

                if (sync) LiveEventBus.get(LiveBus.START_SYNC_SCHEDULE, String::class.java).post("")

                TheApplication.getInstance().globalParams.lastMinute = -1
            }
        }
    }


    suspend fun getGlobalWhiteAppList() = lockRepository.getGlobalWhiteAppList()


    // 供外部调用以更改当前 userId 的方法
    fun setUserId(userId: Int) {
        if (userIdLiveData.value != userId) {
            userIdLiveData.value = userId
        }
    }

    // 根据 userIdLiveData 的变化使用 switchMap 动态查询
    val globalWhiteAppsLiveData: LiveData<MutableList<WhiteApp>> = userIdLiveData.switchMap { userId ->

        // 当 userId 改变时，重新从数据库查询并返回新的 LiveData 对象
        lockRepository.getGlobalWhiteApps(userId)

    }

    //    val globalWhiteAppsLiveData = lockRepository.getGlobalWhiteApps()
    fun deleteGlobalWhiteApp(whiteApp: WhiteApp) {
        viewModelScope.launch {
            withContext(Dispatchers.IO) {
                if (whiteApp.syncState > 0) {
                    whiteApp.syncState = -1
                    whiteApp.version += 1
                    whiteApp.syncTime = System.currentTimeMillis()
                    lockRepository.updateWhiteApp(whiteApp)

//                    LiveEventBus.get(LiveBus.START_SYNC_WHITE_APP, String::class.java).post("")
                } else {
                    lockRepository.deleteWhiteApp(whiteApp)
                }
            }
        }
    }

    fun getWhiteAppsWithTomatoId(tomatoId: String) = lockRepository.getWhiteAppsWithTomatoId(tomatoId)
    fun getWhiteAppsWithScheduleId(scheduleId: String) = lockRepository.getWhiteAppsWithScheduleId(scheduleId)

    private val _allAppsInfo = MutableLiveData<MutableList<AppInfo>>()
    val getAllAppsInfo: MutableLiveData<MutableList<AppInfo>> by lazy {
        viewModelScope.launch {
            withContext(Dispatchers.IO) {
                _allAppsInfo.postValue(lockRepository.getAllAppInfo())
            }
        }
        _allAppsInfo
    }


    fun getAllAppsInfoWithText(text: String): MutableLiveData<MutableList<AppInfo>> {
        viewModelScope.launch {
            withContext(Dispatchers.IO) {
                _allAppsInfo.postValue(lockRepository.getAllAppInfoWithText(text))
            }
        }
        return _allAppsInfo
    }


    //仅用于创建全局白名单
    fun createWhiteApp(whiteApp: WhiteApp) {
        viewModelScope.launch {
            withContext(Dispatchers.IO) {
                if (!lockRepository.isWhiteAppExist("", "", whiteApp.pkg, whiteApp.mainActivity)) {
                    val lastWhiteApp = lockRepository.getLastWhiteApp("", "")
                    var trend = 0
                    if (lastWhiteApp != null) {
                        trend = lastWhiteApp.trend + 1
                    }
                    whiteApp.trend = trend
                    whiteApp.tomatoIndexId = ""
                    whiteApp.scheduleIndexId = ""
                    lockRepository.createWhiteApp(whiteApp)
//                    LiveEventBus.get(LiveBus.START_SYNC_WHITE_APP, String::class.java).post("")
                }
            }
        }
    }

    suspend fun createWhiteAppList(whiteAppList: List<WhiteApp>) {
//        viewModelScope.launch {
        withContext(Dispatchers.IO) {
            val lastWhiteApp = lockRepository.getLastWhiteApp("", "")
            var trend = 0
            if (lastWhiteApp != null) {
                trend = lastWhiteApp.trend + 1
            }
            whiteAppList.forEach { whiteApp ->

                if (!lockRepository.isWhiteAppExist("", "", whiteApp.pkg, whiteApp.mainActivity)) {
                    whiteApp.trend = trend
                    whiteApp.tomatoIndexId = ""
                    whiteApp.scheduleIndexId = ""
                    lockRepository.createWhiteApp(whiteApp)
                    trend++
//                    LiveEventBus.get(LiveBus.START_SYNC_WHITE_APP, String::class.java).post("")
                }
            }
        }
//        }
    }

    fun updateWhiteApp(whiteApp: WhiteApp, sync: Boolean = true) {
        viewModelScope.launch {
            withContext(Dispatchers.IO) {
                if (whiteApp.syncState > 0) {
                    whiteApp.syncState = 1
                    whiteApp.version += 1
                    whiteApp.syncTime = System.currentTimeMillis()
                }
                lockRepository.updateWhiteApp(whiteApp)
                if (sync) LiveEventBus.get(LiveBus.START_SYNC_WHITE_APP, String::class.java).post("")

            }
        }
    }


    fun refreshLockBg(style: Int, lastId: Long) {//0:init 1:add
        launchOnIO(
            tryBlock = {
                lockRepository.getImages(style, lastId, 20).run {
                    handlingHttpResponse<MutableList<LockBg>>(
                        convertHttpRes(),
                        successBlock = {
                            if (it.size == 0) {
                                val list = mutableListOf<LockBg>()
                                if (_lockBgLiveData.value != null) {
                                    list.addAll(_lockBgLiveData.value!!.data as Iterable<LockBg>)
                                }
                                _lockBgLiveData.postValue(NetworkState(1, list))
                            } else {
                                val list = mutableListOf<LockBg>()
                                if (_lockBgLiveData.value != null) {
                                    list.addAll(_lockBgLiveData.value!!.data as Iterable<LockBg>)
                                }
                                list.addAll(it)
                                _lockBgLiveData.postValue(NetworkState(0, list))
                            }
                        },
                        failureBlock = { ex ->
                            handlingApiExceptions(ex)
                            _lockBgLiveData.postValue(NetworkState(2, _lockBgLiveData.value!!.data))
                        }
                    )
                }
            },
            // 请求异常处理
            catchBlock = { e ->
                handlingExceptions(e)
                _lockBgLiveData.postValue(NetworkState(2, mutableListOf()))
            }
        )
    }

    val lockBgLiveData = _lockBgLiveData


    fun createLockHistory(lockHistory: LockHistory) {
        viewModelScope.launch {
            withContext(Dispatchers.IO) {
                TheApplication.getInstance().globalParams.createLockHistoryMutex.withLock {
                    lockRepository.createLockHistory(lockHistory)
                }
            }
        }
    }


    fun updateLockHistory(lockHistory: LockHistory, withSync: Boolean = true) {
        viewModelScope.launch {
            withContext(Dispatchers.IO) {
                lockRepository.updateLockHistory(lockHistory)
                if (withSync) LiveEventBus.get(LiveBus.START_SYNC_HISTORY, String::class.java).post("")

            }
        }
    }

    fun deleteLockHistory(lockHistory: LockHistory) {
        viewModelScope.launch {
            withContext(Dispatchers.IO) {
                lockRepository.deleteLockHistory(lockHistory)
            }
        }
    }

    val unfinishedLockHistoryLiveData = lockRepository.getUnfinishedLockHistory()
//    suspend fun getLastUnfinishedLockHistory() = lockRepository.getUnfinishedLastLockHistory()

    suspend fun globalLockConfig(runningLockHistory: LockHistory): LockConfig {
        val config = LockConfig()

//        LogUtils.d("runningLockHistory=" + runningLockHistory.toString())
        if (runningLockHistory.lockType == 1 && runningLockHistory.simpleLockLength == -1) {
            config.bgUrl = MMKVUtils.getString(MyConstants.SP_KEY_SETTING_MONITOR_BG, DEFAULT_LOCK_MONITOR_BG)
        } else if (runningLockHistory.lockType == 1 && runningLockHistory.simpleLockLength == -2) {
            config.bgUrl = MMKVUtils.getString(MyConstants.SP_KEY_SETTING_NOTICE_BG, DEFAULT_LOCK_NOTICE_BG)
        } else {
            config.bgUrl = MMKVUtils.getString(MyConstants.SP_KEY_SETTING_BG, DEFAULT_LOCK_BG)
        }
//        config.bgUrl = MMKVUtils.getString(MyConstants.SP_KEY_SETTING_BG, DEFAULT_LOCK_BG)

        config.startVoiceNotify = MMKVUtils.getInt(MyConstants.SP_KEY_START_VOICE, 3)
        config.endVoiceNotify = MMKVUtils.getInt(MyConstants.SP_KEY_END_VOICE, 3)
        config.startShakeNotify = MMKVUtils.getLong(MyConstants.SP_KEY_START_SHAKE, 0L)
        config.endShakeNotify = MMKVUtils.getLong(MyConstants.SP_KEY_END_SHAKE, 0L)
        config.isRemoveNotification = MMKVUtils.getBoolean(MyConstants.SP_KEY_REMOVE_NOTIFICATION, false)
        config.isSilent = MMKVUtils.getBoolean(MyConstants.SP_KEY_SILENT, false)

        return config
    }


    val forceUnlockPwd = _forceUnlockPwd
    fun refreshForceUnlockPwd(pwd: String) {
        launchOnIO(
            tryBlock = {
                lockRepository.refreshForceUnlockPwd(pwd).run {
                    handlingHttpResponse<ForceUnlockPwd>(
                        convertHttpRes(),
                        successBlock = {
                            _forceUnlockPwd.postValue(NetworkState(0, it))
                        },
                        failureBlock = { ex ->
                            handlingApiExceptions(ex)
                            _forceUnlockPwd.postValue(NetworkState(2, null))
                        }
                    )
                }
            },
            // 请求异常处理
            catchBlock = { e ->
                handlingExceptions(e)
                _forceUnlockPwd.postValue(NetworkState(2, null))
            }
        )
    }
}