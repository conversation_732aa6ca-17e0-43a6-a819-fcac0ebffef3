package com.lijianqiang12.silent.data.viewmodel

import androidx.lifecycle.MutableLiveData
import com.lijianqiang12.silent.data.model.net.convertHttpRes
import com.lijianqiang12.silent.data.model.net.handlingApiExceptions
import com.lijianqiang12.silent.data.model.net.handlingExceptions
import com.lijianqiang12.silent.data.model.net.handlingHttpResponse
import com.lijianqiang12.silent.data.model.net.pojos.AlipayOrder
import com.lijianqiang12.silent.data.model.net.pojos.BuyHistory
import com.lijianqiang12.silent.data.model.net.pojos.NetworkState
import com.lijianqiang12.silent.data.model.net.pojos.VIPMoney
import com.lijianqiang12.silent.data.model.net.pojos.WxOrder
import com.lijianqiang12.silent.data.model.repository.VIPRepository
import dagger.hilt.android.lifecycle.HiltViewModel
import javax.inject.Inject

@HiltViewModel
class VIPViewModel @Inject constructor(val vipRepository: VIPRepository) : BaseViewModel() {

    private val TAG = "VIPViewModel"
    private val _alipayOrderLiveData = MutableLiveData<NetworkState<AlipayOrder>>()
    private val _wxOrderLiveData = MutableLiveData<NetworkState<WxOrder>>()

    private val _alipayVIPOrderLiveData = MutableLiveData<NetworkState<AlipayOrder>>()
    private val _wxVIPOrderLiveData = MutableLiveData<NetworkState<WxOrder>>()

    private val _orderSuccessLiveData = MutableLiveData<NetworkState<Any>>()
    private val _VIPMoneyLiveData = MutableLiveData<NetworkState<VIPMoney>>()

    private val _buyHistoryLiveData = MutableLiveData<NetworkState<MutableList<BuyHistory>>>()

    val alipayOrderLiveData = _alipayOrderLiveData
    val wxOrderLiveData = _wxOrderLiveData

    val alipayVIPOrderLiveData = _alipayVIPOrderLiveData
    val wxVIPOrderLiveData = _wxVIPOrderLiveData

    val orderSuccessLiveData = _orderSuccessLiveData
    val VIPMoneyLiveData = _VIPMoneyLiveData

    val buyHistoryLiveData = _buyHistoryLiveData

    fun getBuyHistory() {
        launchOnIO(
                tryBlock = {
                    vipRepository.getBuyHistory().run {
                        handlingHttpResponse<MutableList<BuyHistory>>(
                                convertHttpRes(),
                                successBlock = {
                                    buyHistoryLiveData.postValue(NetworkState(0, it))
                                },
                                failureBlock = { ex ->
                                    buyHistoryLiveData.postValue(NetworkState(2, mutableListOf()))
                                    handlingApiExceptions(ex)
                                }
                        )
                    }
                },
                // 请求异常处理
                catchBlock = { e ->
                    buyHistoryLiveData.postValue(NetworkState(2, mutableListOf()))
                    handlingExceptions(e)
                }
        )
    }


//    fun refreshAlipayOrder(amount: Int) {
//        launchOnIO(
//                tryBlock = {
//                    vipRepository.makeAlipayOrderForceUnlock(amount).run {
//                        handlingHttpResponse<AlipayOrder>(
//                                convertHttpRes(),
//                                successBlock = {
//                                    alipayOrderLiveData.postValue(NetworkState(0, it))
//                                },
//                                failureBlock = { ex ->
//                                    alipayOrderLiveData.postValue(NetworkState(2, null))
//                                    handlingApiExceptions(ex)
//                                }
//                        )
//                    }
//                },
//                // 请求异常处理
//                catchBlock = { e ->
//                    alipayOrderLiveData.postValue(NetworkState(2, null))
//                    handlingExceptions(e)
//                }
//        )
//    }

//    fun refreshWxOrder(amount: Int,forceUnlockInfo: String) {
//        launchOnIO(
//                tryBlock = {
//                    vipRepository.makeWXOrderForceUnlock(amount,forceUnlockInfo).run {
//                        handlingHttpResponse<WxOrder>(
//                                convertHttpRes(),
//                                successBlock = {
//                                    wxOrderLiveData.postValue(NetworkState(0, it))
//                                },
//                                failureBlock = { ex ->
//                                    wxOrderLiveData.postValue(NetworkState(2, null))
//                                    handlingApiExceptions(ex)
//                                }
//                        )
//                    }
//                },
//                // 请求异常处理
//                catchBlock = { e ->
//                    wxOrderLiveData.postValue(NetworkState(2, null))
//                    handlingExceptions(e)
//                }
//        )
//    }


    fun refreshAlipayVIPOrder(amount: Int, original: Int) {
        launchOnIO(
                tryBlock = {
                    vipRepository.makeAlipayOrderVIP(amount, original, "", "", "").run {
                        handlingHttpResponse<AlipayOrder>(
                                convertHttpRes(),
                                successBlock = {
                                    alipayVIPOrderLiveData.postValue(NetworkState(0, it))
                                },
                                failureBlock = { ex ->
                                    alipayVIPOrderLiveData.postValue(NetworkState(2, null))
                                    handlingApiExceptions(ex)
                                }
                        )
                    }
                },
                // 请求异常处理
                catchBlock = { e ->
                    alipayVIPOrderLiveData.postValue(NetworkState(2, null))
                    handlingExceptions(e)
                }
        )
    }

    fun refreshWxVIPOrder(amount: Int, original: Int) {
        launchOnIO(
                tryBlock = {
                    vipRepository.makeWXOrderVIP(amount, original, "", "", "").run {
                        handlingHttpResponse<WxOrder>(
                                convertHttpRes(),
                                successBlock = {
                                    wxVIPOrderLiveData.postValue(NetworkState(0, it))
                                },
                                failureBlock = { ex ->
                                    wxVIPOrderLiveData.postValue(NetworkState(2, null))
                                    handlingApiExceptions(ex)
                                }
                        )
                    }
                },
                // 请求异常处理
                catchBlock = { e ->
                    wxVIPOrderLiveData.postValue(NetworkState(2, null))
                    handlingExceptions(e)
                }
        )
    }


    fun refreshOrderSuccess() {
        launchOnIO(
                tryBlock = {
                    vipRepository.queryOrder().run {
                        handlingHttpResponse<Any>(
                                convertHttpRes(),
                                successBlock = {
                                    orderSuccessLiveData.postValue(NetworkState(0, null))
                                },
                                failureBlock = { ex ->
                                    orderSuccessLiveData.postValue(NetworkState(2, null))
                                    handlingApiExceptions(ex)
                                }
                        )
                    }
                },
                // 请求异常处理
                catchBlock = { e ->
                    orderSuccessLiveData.postValue(NetworkState(2, null))
                    handlingExceptions(e)
                }
        )
    }


    fun refreshVIPMoney() {
        launchOnIO(
                tryBlock = {
                    vipRepository.getMoney().run {
                        handlingHttpResponse<VIPMoney>(
                                convertHttpRes(),
                                successBlock = {
                                    _VIPMoneyLiveData.postValue(NetworkState(0, it))
                                },
                                failureBlock = { ex ->
                                    _VIPMoneyLiveData.postValue(NetworkState(2, null))
                                    handlingApiExceptions(ex)
                                }
                        )
                    }
                },
                // 请求异常处理
                catchBlock = { e ->
                    _VIPMoneyLiveData.postValue(NetworkState(2, null))
                    handlingExceptions(e)
                }
        )
    }


}