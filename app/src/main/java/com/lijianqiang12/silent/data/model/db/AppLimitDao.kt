package com.lijianqiang12.silent.data.model.db

import androidx.lifecycle.LiveData
import androidx.room.*
import com.lijianqiang12.silent.MyConstants
import com.lijianqiang12.silent.utils.MMKVUtils


@Dao
interface AppLimitDao {

    @Query("select * from AppLimit where userId = :userId order by trend desc limit 1")
    suspend fun getLastAppLimit(userId: Int): AppLimit?

    @Query("select * From AppLimit Where userId = :userId and syncState = :state order by trend")
    suspend fun getAppLimitWithState(userId: Int, state: Int): MutableList<AppLimit>

    @Query("select * From AppLimit where userId = :userId order by trend")
    suspend fun getAllAppLimitList(userId: Int): MutableList<AppLimit>


    @Query("SELECT * FROM AppLimit where userId = :userId ORDER BY trend")
    fun getAppLimits(userId: Int): LiveData<MutableList<AppLimit>>


    @Insert(onConflict = OnConflictStrategy.IGNORE)
    suspend fun insertAppLimit(appLimits: AppLimit)

    @Update
    suspend fun updateAppLimit(allLimit: AppLimit)

    @Delete
    suspend fun deleteAppLimit(appLimit: AppLimit)

    @Query("delete from AppLimit where userId = :userId")
    suspend fun deleteAll(userId: Int)

    @Query("UPDATE AppLimit SET userId = :newUserId WHERE userId = -1")
    fun updateUserId(newUserId: Int)
}