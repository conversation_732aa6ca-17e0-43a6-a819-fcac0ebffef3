package com.lijianqiang12.silent.data.model.db

import android.content.Context
import androidx.room.AutoMigration
import androidx.room.Database
import androidx.room.Room
import androidx.room.RoomDatabase
import androidx.room.migration.Migration
import androidx.sqlite.db.SupportSQLiteDatabase
import com.blankj.utilcode.util.AppUtils
import com.jeremyliao.liveeventbus.LiveEventBus
import com.lijianqiang12.silent.DATABASE_NAME
import com.lijianqiang12.silent.LiveBus
import com.lijianqiang12.silent.MyConstants
import com.lijianqiang12.silent.utils.MMKVUtils

@Database(
    version = 64,
    entities = [
        Tomato::class,
        WhiteApp::class,
        LockHistory::class,
        Schedule::class,
        Fast::class,
        DayLimit::class,
        AppLimit::class,
        AppUsage::class,
    ],
    autoMigrations = [
        AutoMigration(
            from = 58,
            to = 59,
//            spec = AppLimit::class, //有更改或删除列名或表名的时候需要定义
        ),
        AutoMigration(from = 59, to = 60),
        AutoMigration(from = 60, to = 61),
        AutoMigration(from = 61, to = 62),
        AutoMigration(from = 63, to = 64),
    ],

    exportSchema = true
)
abstract class AppDatabase : RoomDatabase() {

    abstract fun fastDao(): FastDao
    abstract fun tomatoDao(): TomatoDao
    abstract fun scheduleDao(): ScheduleDao

    //    abstract fun lockConfigDao(): LockConfigDao
    abstract fun lockHistoryDao(): LockHistoryDao
    abstract fun whiteAppDao(): WhiteAppDao
    abstract fun dayLimitDao(): DayLimitDao
    abstract fun appLimitDao(): AppLimitDao
    abstract fun appUsageDao(): AppUsageDao

    companion object {
        val TAG = "AppDatabase"

        @Volatile
        private var instance: AppDatabase? = null

        fun getInstance(context: Context): AppDatabase {
            return instance ?: synchronized(this) {
                instance ?: buildDatabase(context).also {
                    instance = it
                }
            }
        }

        private fun buildDatabase(context: Context): AppDatabase {
            return Room.databaseBuilder(context, AppDatabase::class.java, DATABASE_NAME)
                .addCallback(object : Callback() {
                    override fun onDestructiveMigration(db: SupportSQLiteDatabase) {
                        super.onDestructiveMigration(db)

                        val userId = MMKVUtils.getInt(MyConstants.SP_KEY_USER_ID, -1)
                        //数据库升级时，将其置零，以便去服务端拉取数据
                        MMKVUtils.put(MyConstants.SP_KEY_LATEST_PULL_TIME_WHITE_APP + "$userId", 0L)
                        MMKVUtils.put(MyConstants.SP_KEY_LATEST_PULL_TIME_FAST + "$userId", 0L)
                        MMKVUtils.put(MyConstants.SP_KEY_LATEST_PULL_TIME_TOMATO + "$userId", 0L)
                        MMKVUtils.put(MyConstants.SP_KEY_LATEST_PULL_TIME_SCHEDULE + "$userId", 0L)
                        MMKVUtils.put(MyConstants.SP_KEY_LATEST_PULL_TIME_APP_LIMIT + "$userId", 0L)
                        LiveEventBus.get(LiveBus.START_SYNC_ALL, String::class.java).post("update")//升级完立刻拉取，否则用户会无法强制解锁，无法使用白名单
                    }

                    override fun onCreate(db: SupportSQLiteDatabase) {
                        super.onCreate(db)
                        MMKVUtils.put("${AppUtils.getAppVersionCode()}hasRead", true)
                    }


                })
                .fallbackToDestructiveMigration()
                .addMigrations(MIGRATION_62_63)
                .allowMainThreadQueries()
                .setJournalMode(JournalMode.TRUNCATE)//小米手机使用wal模式的时候，强制重启会丢失当前锁机状态数据，所以改为旧版本的TRUNCATE模式
                .build()
        }

        /**
         * fill database with list of cheeses
         */
//        private fun fillInDb(context: Context) {
//            ioThread {
//                getInstance(context).fastDao().addFast(Fast(length = 1))
//                get(context).cheeseDao().insert(
//                        CHEESE_DATA.map { Cheese(id = 0, name = it) })
//            }
//        }
    }

//    object MIGRATION_3_4 : Migration(3, 4) {
//        override fun migrate(database: SupportSQLiteDatabase) {
//            database.execSQL("CREATE TABLE `OffRecord` (`id` INTEGER NOT NULL, `length` INTEGER NOT NULL, `startTime` TEXT NOT NULL, PRIMARY KEY(`id`))");
//        }
//    }
//
//    object MIGRATION_4_5 : Migration(4, 5) {
//        override fun migrate(database: SupportSQLiteDatabase) {
//            database.execSQL("ALTER TABLE Schedule ADD COLUMN trend INTEGER NOT NULL DEFAULT 0")
//            database.execSQL("UPDATE Schedule SET trend = id")
//
//            database.execSQL("ALTER TABLE Tomato ADD COLUMN trend INTEGER NOT NULL DEFAULT 0")
//            database.execSQL("UPDATE Tomato SET trend = id")
//        }
//    }
//
//    object MIGRATION_5_6 : Migration(5, 6) {
//        override fun migrate(database: SupportSQLiteDatabase) {
//            database.execSQL("CREATE TABLE History (id INTEGER NOT NULL, title TEXT NOT NULL, timeLen INTEGER NOT NULL, now INTEGER NOT NULL, trend INTEGER NOT NULL, PRIMARY KEY(id))")
//        }
//    }
//
//    object MIGRATION_6_7 : Migration(6, 7) {
//        override fun migrate(database: SupportSQLiteDatabase) {
//            database.execSQL("ALTER TABLE Tomato ADD COLUMN globalWhite INTEGER NOT NULL DEFAULT 0")
//        }
//    }
//
//    object MIGRATION_7_8 : Migration(7, 8) {
//        override fun migrate(database: SupportSQLiteDatabase) {
//            database.execSQL("ALTER TABLE Schedule ADD COLUMN useTomato INTEGER NOT NULL DEFAULT 1")
//            database.execSQL("ALTER TABLE Schedule ADD COLUMN endHour INTEGER NOT NULL DEFAULT 0")
//            database.execSQL("ALTER TABLE Schedule ADD COLUMN endMinute INTEGER NOT NULL DEFAULT 0")
//        }
//    }

//    object MIGRATION_8_9 : Migration(8, 9) {
//        override fun migrate(database: SupportSQLiteDatabase) {
//            database.execSQL("CREATE TABLE Fast (id INTEGER NOT NULL, title TEXT NOT NULL, length INTEGER NOT NULL, trend INTEGER NOT NULL, PRIMARY KEY(id))")
//        }
//    }

//    object MIGRATION_9_10 : Migration(9, 10) {
//        override fun migrate(database: SupportSQLiteDatabase) {
//        }
//    }

    object MIGRATION_62_63 : Migration(62, 63) {
        override fun migrate(db: SupportSQLiteDatabase) {
//            db.execSQL("CREATE TABLE `OffRecord` (`id` INTEGER NOT NULL, `length` INTEGER NOT NULL, `startTime` TEXT NOT NULL, PRIMARY KEY(`id`))");
            val userId = MMKVUtils.getInt(MyConstants.SP_KEY_USER_ID, -1)
            db.execSQL("ALTER TABLE Fast ADD COLUMN userId INTEGER NOT NULL DEFAULT $userId")
            db.execSQL("ALTER TABLE Tomato ADD COLUMN userId INTEGER NOT NULL DEFAULT $userId")
            db.execSQL("ALTER TABLE Schedule ADD COLUMN userId INTEGER NOT NULL DEFAULT $userId")
            db.execSQL("ALTER TABLE WhiteApp ADD COLUMN userId INTEGER NOT NULL DEFAULT $userId")
//            db.execSQL("ALTER TABLE LockHistory ADD COLUMN userId INTEGER NOT NULL DEFAULT $userId")
            db.execSQL("ALTER TABLE AppLimit ADD COLUMN userId INTEGER NOT NULL DEFAULT $userId")
            db.execSQL("ALTER TABLE DayLimit ADD COLUMN userId INTEGER NOT NULL DEFAULT $userId")
        }
    }

//    }

//    fun onDestroy() {
//        sInstance = null
//    }

}