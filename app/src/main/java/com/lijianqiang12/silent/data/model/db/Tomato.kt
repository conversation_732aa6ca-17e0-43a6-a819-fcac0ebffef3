package com.lijianqiang12.silent.data.model.db

import android.os.Parcel
import android.os.Parcelable
import androidx.room.ColumnInfo
import androidx.room.Embedded
import androidx.room.Entity
import androidx.room.Index
import androidx.room.PrimaryKey
import com.lijianqiang12.silent.MyConstants
import com.lijianqiang12.silent.utils.MMKVUtils

@Entity(indices = [Index("tomatoIndexId", unique = true)])
data class Tomato(
    @PrimaryKey(autoGenerate = true) var id: Long,
    var userId: Int,
    var tomatoIndexId: String,
    var title: String,
    var tomatoWorkLength: Int,
    var tomatoRestLength: Int,
    var tomatoCount: Int,
    var tomatoLongRestPerCount: Int,
    var tomatoLongRestLength: Int,
    var trend: Int,
    var syncState: Int, var syncTime: Long, var uuid: Long, var version: Int,
    @Embedded var lockConfig: LockConfig


) : Parcelable {

    constructor() : this(
        0,
        MMKVUtils.getInt(MyConstants.SP_KEY_USER_ID, -1),
        "",
        "未命名",
        25,
        5,
        4,
        2,
        15,
        0,
        0,
        0,
        0,
        0,
        LockConfig()
    )

    constructor(parcel: Parcel) : this(
        parcel.readLong(),
        parcel.readInt(),
        parcel.readString()!!,
        parcel.readString()!!,
        parcel.readInt(),
        parcel.readInt(),
        parcel.readInt(),
        parcel.readInt(),
        parcel.readInt(),
        parcel.readInt(),
        parcel.readInt(),
        parcel.readLong(),
        parcel.readLong(),
        parcel.readInt(),
        parcel.readParcelable(LockConfig::class.java.classLoader)!!
    )


    override fun toString(): String {
        return "Tomato(id=$id, userId=$userId, tomatoIndexId='$tomatoIndexId', title='$title', tomatoWorkLength=$tomatoWorkLength, tomatoRestLength=$tomatoRestLength, tomatoCount=$tomatoCount, tomatoLongRestPerCount=$tomatoLongRestPerCount, tomatoLongRestLength=$tomatoLongRestLength, trend=$trend, syncState=$syncState, syncTime=$syncTime, uuid=$uuid, version=$version, lockConfig=$lockConfig)"
    }

    override fun writeToParcel(parcel: Parcel, flags: Int) {
        parcel.writeLong(id)
        parcel.writeInt(userId)
        parcel.writeString(tomatoIndexId)
        parcel.writeString(title)
        parcel.writeInt(tomatoWorkLength)
        parcel.writeInt(tomatoRestLength)
        parcel.writeInt(tomatoCount)
        parcel.writeInt(tomatoLongRestPerCount)
        parcel.writeInt(tomatoLongRestLength)
        parcel.writeInt(trend)
        parcel.writeInt(syncState)
        parcel.writeLong(syncTime)
        parcel.writeLong(uuid)
        parcel.writeInt(version)
        parcel.writeParcelable(lockConfig, flags)
    }

    override fun describeContents(): Int {
        return 0
    }

    companion object CREATOR : Parcelable.Creator<Tomato> {
        override fun createFromParcel(parcel: Parcel): Tomato {
            return Tomato(parcel)
        }

        override fun newArray(size: Int): Array<Tomato?> {
            return arrayOfNulls(size)
        }
    }

    fun copy(): Tomato {
        val result = Tomato()

        result.userId = this.userId
        result.title = this.title
        result.tomatoWorkLength = this.tomatoWorkLength
        result.tomatoRestLength = this.tomatoRestLength
        result.tomatoCount = this.tomatoCount
        result.tomatoLongRestPerCount = this.tomatoLongRestPerCount
        result.tomatoLongRestLength = this.tomatoLongRestLength

        result.lockConfig = this.lockConfig.copy()

        return result
    }


}
