package com.lijianqiang12.silent.data.viewmodel

import androidx.lifecycle.ViewModel
import androidx.lifecycle.ViewModelProvider
import com.lijianqiang12.silent.data.model.repository.RoomRepository

@Suppress("UNCHECKED_CAST")
class RoomViewModelFactory(private val roomRepository: RoomRepository) : ViewModelProvider.NewInstanceFactory() {

    override fun <T : ViewModel> create(modelClass: Class<T>): T {
        return RoomViewModel(roomRepository) as T
    }

}