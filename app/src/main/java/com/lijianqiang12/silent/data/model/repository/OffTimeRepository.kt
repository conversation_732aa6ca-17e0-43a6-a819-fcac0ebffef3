package com.lijianqiang12.silent.data.model.repository

import android.content.Context
import com.lijianqiang12.silent.data.model.net.MyRetrofitClient

class OffTimeRepository private constructor(private val appContext: Context) {
    suspend fun yesterdayTop3() = MyRetrofitClient.service.offTimeTop(3, -1)
    suspend fun todayTop100() = MyRetrofitClient.service.offTimeTop(100, 0)
    suspend fun todayTop3() = MyRetrofitClient.service.offTimeTop(3, 0)
    suspend fun lockLength(deltaWeek: Int) = MyRetrofitClient.service.lockLength(deltaWeek)
    suspend fun myTrend(deltaDay:Int) = MyRetrofitClient.service.myTrendNo(deltaDay)


    companion object {

        @Volatile
        private var instance: OffTimeRepository? = null

        fun getInstance(appContext: Context) = instance ?: synchronized(this) {
            instance ?: OffTimeRepository(appContext).also { instance = it }
        }
    }
}

