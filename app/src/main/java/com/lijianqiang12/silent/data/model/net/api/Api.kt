package com.lijianqiang12.silent.data.model.net.api

import com.lijianqiang12.silent.data.model.net.pojos.AddSyncResult
import com.lijianqiang12.silent.data.model.net.pojos.AlipayOrder
import com.lijianqiang12.silent.data.model.net.pojos.AllRoom
import com.lijianqiang12.silent.data.model.net.pojos.ApiResponse
import com.lijianqiang12.silent.data.model.net.pojos.AppUpdate
import com.lijianqiang12.silent.data.model.net.pojos.BaoZangApp
import com.lijianqiang12.silent.data.model.net.pojos.BuyHistory
import com.lijianqiang12.silent.data.model.net.pojos.DeveloperUnlockUserInfo
import com.lijianqiang12.silent.data.model.net.pojos.FastDenyPageExample
import com.lijianqiang12.silent.data.model.net.pojos.ForceUnlockPwd
import com.lijianqiang12.silent.data.model.net.pojos.InvitePageInfo
import com.lijianqiang12.silent.data.model.net.pojos.LaunchDialog
import com.lijianqiang12.silent.data.model.net.pojos.LockBg
import com.lijianqiang12.silent.data.model.net.pojos.LoginResponse
import com.lijianqiang12.silent.data.model.net.pojos.MyConfig
import com.lijianqiang12.silent.data.model.net.pojos.MyJoinedRoom
import com.lijianqiang12.silent.data.model.net.pojos.MyMsg
import com.lijianqiang12.silent.data.model.net.pojos.MyTrend
import com.lijianqiang12.silent.data.model.net.pojos.OffTimeDetail
import com.lijianqiang12.silent.data.model.net.pojos.PullAppLimitResult
import com.lijianqiang12.silent.data.model.net.pojos.PullFastResult
import com.lijianqiang12.silent.data.model.net.pojos.PullScheduleResult
import com.lijianqiang12.silent.data.model.net.pojos.PullTomatoResult
import com.lijianqiang12.silent.data.model.net.pojos.PullWhiteResult
import com.lijianqiang12.silent.data.model.net.pojos.PunchCardMsg
import com.lijianqiang12.silent.data.model.net.pojos.RefreshStateResponse
import com.lijianqiang12.silent.data.model.net.pojos.RoomDetailBoard
import com.lijianqiang12.silent.data.model.net.pojos.RoomDetailData
import com.lijianqiang12.silent.data.model.net.pojos.RoomDetailMember
import com.lijianqiang12.silent.data.model.net.pojos.RoomInfoFromCode
import com.lijianqiang12.silent.data.model.net.pojos.RoomRequestBean
import com.lijianqiang12.silent.data.model.net.pojos.SubUnlockCode
import com.lijianqiang12.silent.data.model.net.pojos.ThePunchCard
import com.lijianqiang12.silent.data.model.net.pojos.UnReadMsg
import com.lijianqiang12.silent.data.model.net.pojos.UpdateAppLimitResult
import com.lijianqiang12.silent.data.model.net.pojos.UpdateFastResult
import com.lijianqiang12.silent.data.model.net.pojos.UpdateScheduleResult
import com.lijianqiang12.silent.data.model.net.pojos.UpdateTomatoResult
import com.lijianqiang12.silent.data.model.net.pojos.UpdateWhiteResult
import com.lijianqiang12.silent.data.model.net.pojos.UserCode
import com.lijianqiang12.silent.data.model.net.pojos.UserInfo
import com.lijianqiang12.silent.data.model.net.pojos.VIPMoney
import com.lijianqiang12.silent.data.model.net.pojos.WellKnowWord
import com.lijianqiang12.silent.data.model.net.pojos.WxOrder
import retrofit2.http.*


interface Api {
    companion object {
        //        const val HOST = "shuge.ltd"
        const val HOST = "shuge888.com"
        const val PORT = 33333
        const val BASE_URL = "https://offphone.${HOST}:${PORT}"
//        const val BASE_URL = "https://offphone.shuge888.com:33333"
    }

    @FormUrlEncoded
    @POST("/account/getVerifyCode")
    suspend fun getVerifyCode(@Field("codeType") codeType: Int, @Field("phone") phone: String): ApiResponse<String>//1：短信 2：电话

    @FormUrlEncoded
    @POST("/account/verifyCode")
    suspend fun verifyCode(
        @Field("phone") phone: String,
        @Field("code") code: String,
        @Field("inviteCode") inviteCode: Int,
    ): ApiResponse<LoginResponse>

    @FormUrlEncoded
    @POST("/account/socialAccountLogin")
    suspend fun socialAccountLogin(
        @Field("socialType") socialType: Int,
        @Field("uid") uid: String,
        @Field("username") username: String,
        @Field("avatar") avatar: String,
        @Field("gender") gender: String,
        @Field("inviteCode") inviteCode: Int,
    ): ApiResponse<LoginResponse>

    //刷新个人信息
    @GET("/account/refreshState")
    suspend fun refreshState(): ApiResponse<RefreshStateResponse>


    @GET("/lock/getImages")
    suspend fun getImages(
        @Query("imgStyle") imgStyle: Int,
        @Query("lastId") lastId: Long,
        @Query("limit") limit: Int
    ): ApiResponse<List<LockBg>>


    @GET("/account/getInvitePageInfo")
    suspend fun getInvitePageInfo(): ApiResponse<InvitePageInfo>

    @GET("/offtime/offTimeTop")
    suspend fun offTimeTop(
        @Query("limit") limit: Int,
        @Query("deltaDay") deltaDay: Int
    ): ApiResponse<List<OffTimeDetail>>

    @GET("/room/myJoinedRooms")
    suspend fun myJoinedRooms(): ApiResponse<List<MyJoinedRoom>>

    @GET("/room/allRooms")
    suspend fun allRooms(
        @Query("type") type: Int,
        @Query("lastRoomId") lastRoomId: Long,
        @Query("lastLevelCount") lastLevelCount: Long
    ): ApiResponse<List<AllRoom>>

    @FormUrlEncoded
    @POST("/room/requestRoom")
    suspend fun requestRoom(
        @Field("roomId") roomId: Int,
        @Field("roomType") roomType: Int,
        @Field("bgUrl") bgUrl: String,
        @Field("roomName") roomName: String,
        @Field("roomDesc") roomDesc: String,
        @Field("roomPwd") roomPwd: String,
        @Field("changeType") changeType: Int //0:创建 1:修改
    ): ApiResponse<Any>


    //房间详情
    @GET("/room/getRoomDesc")
    suspend fun getRoomDesc(@Query("roomId") roomId: Int): ApiResponse<RoomDetailData>

    //生成微信订单
    @FormUrlEncoded
    @POST("/vip/makeWXOrder/v1")
    suspend fun makeWXOrder(
        @Field("currentPrice") currentPrice: Int,
        @Field("originalPrice") originalPrice: Int,
        @Field("name") name: String,
        @Field("phone") phone: String,
        @Field("address") address: String
    ): ApiResponse<WxOrder>

    //生成微信订单
    @FormUrlEncoded
    @POST("/vip/makeWXOrder/v1")
    suspend fun makeWXOrderForceUnlock(
        @Field("typeOrder") typeOrder: Int,
        @Field("forceAmount") forceAmount: Int,
        @Field("forceUnlockInfo") forceUnlockInfo: String,
    ): ApiResponse<WxOrder>

    //生成微信订单
    @FormUrlEncoded
    @POST("/vip/makeWXOrder/v1")
    suspend fun makeWXOrderAppLimit(
        @Field("typeOrder") typeOrder: Int,
        @Field("forceAmount") forceAmount: Long,
        @Field("appLimitUuid") appLimitUuid: Long,
    ): ApiResponse<WxOrder>

//    //查询微信订单
//    @GET("/vip/queryWXOrder/v1")
//    suspend fun queryWXOrder(): ApiResponse<Any>

    //生成支付宝订单
    @FormUrlEncoded
    @POST("/vip/makeAlipayOrder/v1")
    suspend fun makeAlipayOrder(
        @Field("currentPrice") currentPrice: Int,
        @Field("originalPrice") originalPrice: Int,
        @Field("name") name: String,
        @Field("phone") phone: String,
        @Field("address") address: String
    ): ApiResponse<AlipayOrder>

    //生成支付宝订单
    @FormUrlEncoded
    @POST("/vip/makeAlipayOrder/v1")
    suspend fun makeAlipayOrderForceUnlock(
        @Field("typeOrder") typeOrder: Int,
        @Field("forceAmount") forceAmount: Int,
        @Field("forceUnlockInfo") forceUnlockInfo: String,
    ): ApiResponse<AlipayOrder>

    //生成支付宝订单
    @FormUrlEncoded
    @POST("/vip/makeAlipayOrder/v1")
    suspend fun makeAlipayOrderAppLimit(
        @Field("typeOrder") typeOrder: Int,
        @Field("forceAmount") forceAmount: Long,
        @Field("appLimitUuid") appLimitUuid: Long,
    ): ApiResponse<AlipayOrder>

    //查询订单
    @GET("/vip/queryOrder/v1")
    suspend fun queryOrder(): ApiResponse<Any>

    //查询锁机页面展示信息
    @GET("/lock/getWellKnowWord/v1")
    suspend fun getWellKnowWord(): ApiResponse<WellKnowWord>

    //随机生成强制解锁密码
    @FormUrlEncoded
    @POST("/lock/refreshForceUnlockPwd")
    suspend fun refreshForceUnlockPwd(@Field("pwd") pwd: String): ApiResponse<ForceUnlockPwd>


    //查询VIP价格列表
    @GET("/vip/getMoney/v3")
    suspend fun getMoney(): ApiResponse<VIPMoney>

    //历史购买
    @GET("/vip/getBuyHistory/v1")
    suspend fun getBuyHistory(): ApiResponse<MutableList<BuyHistory>>

    //申请补发赠品
    @FormUrlEncoded
    @POST("/vip/applyDelivery/v2")
    suspend fun applyDelivery(
        @Field("name") name: String,
        @Field("phone") phone: String,
        @Field("address") address: String
    ): ApiResponse<Any>

    //查询个人信息
    @GET("/account/queryUserInfo/v1")
    suspend fun queryUserInfo(): ApiResponse<UserInfo>

    //更新头像
    @FormUrlEncoded
    @POST("/account/updateAvatar/v1")
    suspend fun updateAvatar(@Field("avatar") avatar: String): ApiResponse<Any>

    //更新用户名
    @FormUrlEncoded
    @POST("/account/updateUsername/v1")
    suspend fun updateUsername(@Field("username") username: String): ApiResponse<Any>

    //更新性别
    @FormUrlEncoded
    @POST("/account/updateGender/v1")
    suspend fun updateGender(@Field("gender") gender: String): ApiResponse<Any>

    //更新签名
    @FormUrlEncoded
    @POST("/account/updateWord/v1")
    suspend fun updateWord(@Field("word") word: String): ApiResponse<Any>

    //更新手机号
    @FormUrlEncoded
    @POST("/account/updateMobile/v1")
    suspend fun updateMobile(@Field("mobile") mobile: String, @Field("code") code: String): ApiResponse<Any>

    //绑定QQ
    @FormUrlEncoded
    @POST("/account/bindQQ/v1")
    suspend fun bindQQ(@Field("qqId") qqId: String): ApiResponse<Any>

    //绑定微信
    @FormUrlEncoded
    @POST("/account/bindWX/v1")
    suspend fun bindWX(@Field("wxId") wxId: String): ApiResponse<Any>

    //绑定微博
    @FormUrlEncoded
    @POST("/account/bindSINA/v1")
    suspend fun bindSINA(@Field("sinaId") sinaId: String): ApiResponse<Any>

    //解绑QQ
    @POST("/account/deleteQQ/v1")
    suspend fun deleteQQ(): ApiResponse<Any>

    //解绑微信
    @POST("/account/deleteWX/v1")
    suspend fun deleteWX(): ApiResponse<Any>

    //解绑QQ
    @POST("/account/deleteSINA/v1")
    suspend fun deleteSINA(): ApiResponse<Any>

    //注销账号
    @POST("/account/deleteAllAccount/v1")
    suspend fun deleteAllAccount(): ApiResponse<Any>

    //名言点赞
    @FormUrlEncoded
    @POST("/lock/wellKnowWordStar")
    suspend fun wellKnowWordStar(@Field("wordId") wordId: Int): ApiResponse<Any>

    //名言分享
    @FormUrlEncoded
    @POST("/lock/wellKnowWordShare")
    suspend fun wellKnowWordShare(@Field("wordId") wordId: Int): ApiResponse<Any>

    //查询最新app版本信息
    @GET("/console/appUpdate/v1")
    suspend fun appUpdate(): ApiResponse<AppUpdate>


    //向服务器addWhiteApp
    @FormUrlEncoded
    @POST("/sync/addWhiteApp")
    suspend fun addWhiteApp(
        @Field("whiteAppIndexId") whiteAppIndexId: String,
        @Field("tomatoIndexId") tomatoIndexId: String,
        @Field("scheduleIndexId") scheduleIndexId: String,
        @Field("pkg") pkg: String,
        @Field("mainActivity") mainActivity: String,
        @Field("maxLen") maxLen: Int,
        @Field("version") version: Int
    ): ApiResponse<AddSyncResult>

    //向服务器updateWhiteApp
    @FormUrlEncoded
    @POST("/sync/updateWhiteApp")
    suspend fun updateWhiteApp(
        @Field("tomatoIndexId") tomatoIndexId: String,
        @Field("scheduleIndexId") scheduleIndexId: String,
        @Field("pkg") pkg: String,
        @Field("mainActivity") mainActivity: String,
        @Field("maxLen") maxLen: Int,
        @Field("trend") trend: Int,
        @Field("syncTime") syncTime: Long,
        @Field("uuid") uuid: Long,
        @Field("version") version: Int
    ): ApiResponse<UpdateWhiteResult>

    //向服务器deleteWhiteApp
    @FormUrlEncoded
    @POST("/sync/deleteWhiteApp")
    suspend fun deleteWhiteApp(
        @Field("uuid") uuid: Long,
        @Field("version") version: Int,
        @Field("syncTime") syncTime: Long
    ): ApiResponse<Any>

    //向服务器pullWhiteApp
    @FormUrlEncoded
    @POST("/sync/pullWhiteApp")
    suspend fun pullWhiteApp(@Field("latestTime") latestTime: Long): ApiResponse<List<PullWhiteResult>>


    //向服务器addFast
    @FormUrlEncoded
    @POST("/sync/addFast")
    suspend fun addFast(
        @Field("fastIndexId") fastIndexId: String,
        @Field("length") length: Int,
        @Field("version") version: Int
    ): ApiResponse<AddSyncResult>

    //向服务器updateFast
    @FormUrlEncoded
    @POST("/sync/updateFast")
    suspend fun updateFast(
        @Field("length") length: Int,
        @Field("trend") trend: Int,
        @Field("syncTime") syncTime: Long,
        @Field("uuid") uuid: Long,
        @Field("version") version: Int
    ): ApiResponse<UpdateFastResult>

    //向服务器deleteFast
    @FormUrlEncoded
    @POST("/sync/deleteFast")
    suspend fun deleteFast(
        @Field("uuid") uuid: Long,
        @Field("version") version: Int,
        @Field("syncTime") syncTime: Long
    ): ApiResponse<Any>

    //向服务器pullFast
    @FormUrlEncoded
    @POST("/sync/pullFast")
    suspend fun pullFast(@Field("latestTime") latestTime: Long): ApiResponse<List<PullFastResult>>


    //向服务器addTomato
    @FormUrlEncoded
    @POST("/sync/addTomato")
    suspend fun addTomato(
        @Field("tomatoIndexId") tomatoIndexId: String,
        @Field("title") title: String,
        @Field("tomatoWorkLength") tomatoWorkLength: Int,
        @Field("tomatoRestLength") tomatoRestLength: Int,
        @Field("tomatoCount") tomatoCount: Int,
        @Field("tomatoLongRestPerCount") tomatoLongRestPerCount: Int,
        @Field("tomatoLongRestLength") tomatoLongRestLength: Int,

        @Field("bgUrl") bgUrl: String,
        @Field("isRemoveNotification") isRemoveNotification: Boolean,
        @Field("isSilent") isSilent: Boolean,
        @Field("startVoiceNotify") startVoiceNotify: Int,
        @Field("endVoiceNotify") endVoiceNotify: Int,
        @Field("startShakeNotify") startShakeNotify: Long,
        @Field("endShakeNotify") endShakeNotify: Long,
        @Field("whiteFollowGlobal") whiteFollowGlobal: Boolean,
        @Field("bgUrlFollowGlobal") bgUrlFollowGlobal: Boolean,
        @Field("isRemoveNotificationFollowGlobal") isRemoveNotificationFollowGlobal: Boolean,
        @Field("isSilentFollowGlobal") isSilentFollowGlobal: Boolean,
        @Field("startVoiceNotifyFollowGlobal") startVoiceNotifyFollowGlobal: Boolean,
        @Field("endVoiceNotifyFollowGlobal") endVoiceNotifyFollowGlobal: Boolean,
        @Field("startShakeNotifyFollowGlobal") startShakeNotifyFollowGlobal: Boolean,
        @Field("endShakeNotifyFollowGlobal") endShakeNotifyFollowGlobal: Boolean,

        @Field("version") version: Int
    ): ApiResponse<AddSyncResult>

    //向服务器updateTomato
    @FormUrlEncoded
    @POST("/sync/updateTomato")
    suspend fun updateTomato(
        @Field("tomatoIndexId") tomatoIndexId: String,
        @Field("title") title: String,
        @Field("tomatoWorkLength") tomatoWorkLength: Int,
        @Field("tomatoRestLength") tomatoRestLength: Int,
        @Field("tomatoCount") tomatoCount: Int,
        @Field("tomatoLongRestPerCount") tomatoLongRestPerCount: Int,
        @Field("tomatoLongRestLength") tomatoLongRestLength: Int,

        @Field("bgUrl") bgUrl: String,
        @Field("isRemoveNotification") isRemoveNotification: Boolean,
        @Field("isSilent") isSilent: Boolean,
        @Field("startVoiceNotify") startVoiceNotify: Int,
        @Field("endVoiceNotify") endVoiceNotify: Int,
        @Field("startShakeNotify") startShakeNotify: Long,
        @Field("endShakeNotify") endShakeNotify: Long,
        @Field("whiteFollowGlobal") whiteFollowGlobal: Boolean,
        @Field("bgUrlFollowGlobal") bgUrlFollowGlobal: Boolean,
        @Field("isRemoveNotificationFollowGlobal") isRemoveNotificationFollowGlobal: Boolean,
        @Field("isSilentFollowGlobal") isSilentFollowGlobal: Boolean,
        @Field("startVoiceNotifyFollowGlobal") startVoiceNotifyFollowGlobal: Boolean,
        @Field("endVoiceNotifyFollowGlobal") endVoiceNotifyFollowGlobal: Boolean,
        @Field("startShakeNotifyFollowGlobal") startShakeNotifyFollowGlobal: Boolean,
        @Field("endShakeNotifyFollowGlobal") endShakeNotifyFollowGlobal: Boolean,

        @Field("trend") trend: Int,
        @Field("syncTime") syncTime: Long,
        @Field("uuid") uuid: Long,
        @Field("version") version: Int
    ): ApiResponse<UpdateTomatoResult>

    //向服务器deleteTomato
    @FormUrlEncoded
    @POST("/sync/deleteTomato")
    suspend fun deleteTomato(
        @Field("uuid") uuid: Long,
        @Field("version") version: Int,
        @Field("syncTime") syncTime: Long
    ): ApiResponse<Any>

    //向服务器pullTomato
    @FormUrlEncoded
    @POST("/sync/pullTomato")
    suspend fun pullTomato(@Field("latestTime") latestTime: Long): ApiResponse<List<PullTomatoResult>>


    //向服务器addSchedule
    @FormUrlEncoded
    @POST("/sync/addSchedule")
    suspend fun addSchedule(
        @Field("title") title: String,
        @Field("tomatoIndexId") tomatoIndexId: String,
        @Field("scheduleIndexId") scheduleIndexId: String,
        @Field("startHour") startHour: Int,
        @Field("startMinute") startMinute: Int,
        @Field("validate") validate: Boolean,
        @Field("sunday") sunday: Boolean,
        @Field("monday") monday: Boolean,
        @Field("tuesday") tuesday: Boolean,
        @Field("wednesday") wednesday: Boolean,
        @Field("thursday") thursday: Boolean,
        @Field("friday") friday: Boolean,
        @Field("saturday") saturday: Boolean,
        @Field("useTomato") useTomato: Boolean,
        @Field("endHour") endHour: Int,
        @Field("endMinute") endMinute: Int,
        @Field("isRecycle") isRecycle: Boolean,
        @Field("isDenyChange") isDenyChange: Boolean,
        @Field("denyChangeLength") denyChangeLength: Int,

        @Field("bgUrl") bgUrl: String,
        @Field("isRemoveNotification") isRemoveNotification: Boolean,
        @Field("isSilent") isSilent: Boolean,
        @Field("startVoiceNotify") startVoiceNotify: Int,
        @Field("endVoiceNotify") endVoiceNotify: Int,
        @Field("startShakeNotify") startShakeNotify: Long,
        @Field("endShakeNotify") endShakeNotify: Long,
        @Field("whiteFollowGlobal") whiteFollowGlobal: Boolean,
        @Field("bgUrlFollowGlobal") bgUrlFollowGlobal: Boolean,
        @Field("isRemoveNotificationFollowGlobal") isRemoveNotificationFollowGlobal: Boolean,
        @Field("isSilentFollowGlobal") isSilentFollowGlobal: Boolean,
        @Field("startVoiceNotifyFollowGlobal") startVoiceNotifyFollowGlobal: Boolean,
        @Field("endVoiceNotifyFollowGlobal") endVoiceNotifyFollowGlobal: Boolean,
        @Field("startShakeNotifyFollowGlobal") startShakeNotifyFollowGlobal: Boolean,
        @Field("endShakeNotifyFollowGlobal") endShakeNotifyFollowGlobal: Boolean,
        @Field("jumpDate") jumpDate: String,

        @Field("version") version: Int
    ): ApiResponse<AddSyncResult>

    //向服务器updateSchedule
    @FormUrlEncoded
    @POST("/sync/updateSchedule")
    suspend fun updateSchedule(
        @Field("title") title: String,
        @Field("tomatoIndexId") tomatoIndexId: String,
        @Field("scheduleIndexId") scheduleIndexId: String,
        @Field("startHour") startHour: Int,
        @Field("startMinute") startMinute: Int,
        @Field("validate") validate: Boolean,
        @Field("sunday") sunday: Boolean,
        @Field("monday") monday: Boolean,
        @Field("tuesday") tuesday: Boolean,
        @Field("wednesday") wednesday: Boolean,
        @Field("thursday") thursday: Boolean,
        @Field("friday") friday: Boolean,
        @Field("saturday") saturday: Boolean,
        @Field("useTomato") useTomato: Boolean,
        @Field("endHour") endHour: Int,
        @Field("endMinute") endMinute: Int,
        @Field("isRecycle") isRecycle: Boolean,
        @Field("isDenyChange") isDenyChange: Boolean,
        @Field("denyChangeLength") denyChangeLength: Int,

        @Field("bgUrl") bgUrl: String,
        @Field("isRemoveNotification") isRemoveNotification: Boolean,
        @Field("isSilent") isSilent: Boolean,
        @Field("startVoiceNotify") startVoiceNotify: Int,
        @Field("endVoiceNotify") endVoiceNotify: Int,
        @Field("startShakeNotify") startShakeNotify: Long,
        @Field("endShakeNotify") endShakeNotify: Long,
        @Field("whiteFollowGlobal") whiteFollowGlobal: Boolean,
        @Field("bgUrlFollowGlobal") bgUrlFollowGlobal: Boolean,
        @Field("isRemoveNotificationFollowGlobal") isRemoveNotificationFollowGlobal: Boolean,
        @Field("isSilentFollowGlobal") isSilentFollowGlobal: Boolean,
        @Field("startVoiceNotifyFollowGlobal") startVoiceNotifyFollowGlobal: Boolean,
        @Field("endVoiceNotifyFollowGlobal") endVoiceNotifyFollowGlobal: Boolean,
        @Field("startShakeNotifyFollowGlobal") startShakeNotifyFollowGlobal: Boolean,
        @Field("endShakeNotifyFollowGlobal") endShakeNotifyFollowGlobal: Boolean,

        @Field("jumpDate") jumpDate: String,
        @Field("trend") trend: Int,
        @Field("syncTime") syncTime: Long,
        @Field("uuid") uuid: Long,
        @Field("version") version: Int
    ): ApiResponse<UpdateScheduleResult>

    //向服务器deleteSchedule
    @FormUrlEncoded
    @POST("/sync/deleteSchedule")
    suspend fun deleteSchedule(
        @Field("uuid") uuid: Long,
        @Field("version") version: Int,
        @Field("syncTime") syncTime: Long
    ): ApiResponse<Any>

    //向服务器pullSchedule
    @FormUrlEncoded
    @POST("/sync/pullSchedule")
    suspend fun pullSchedule(@Field("latestTime") latestTime: Long, @Field("isUpdateDb") isUpdateDb: Boolean): ApiResponse<List<PullScheduleResult>>


    //向服务器addAppLimit
    @FormUrlEncoded
    @POST("/sync/addAppLimit")
    suspend fun addAppLimit(
        @Field("appLimitIndexId") appLimitIndexId: String,
        @Field("appPkg") appPkg: String,
        @Field("ifAllDay") ifAllDay: Boolean,
        @Field("startTime") startTime: Long,
        @Field("endTime") endTime: Long,
        @Field("limitLength") limitLength: Long,

        @Field("title") title: String,
        @Field("sunday") sunday: Boolean,
        @Field("monday") monday: Boolean,
        @Field("tuesday") tuesday: Boolean,
        @Field("wednesday") wednesday: Boolean,
        @Field("thursday") thursday: Boolean,
        @Field("friday") friday: Boolean,
        @Field("saturday") saturday: Boolean,
        @Field("editStartTime") editStartTime: Long,
        @Field("editEndTime") editEndTime: Long,
        @Field("editMoney") editMoney: Long,

        @Field("valid") valid: Boolean,
        @Field("version") version: Int
    ): ApiResponse<AddSyncResult>


    //向服务器updateFast
    @FormUrlEncoded
    @POST("/sync/updateAppLimit")
    suspend fun updateAppLimit(
        @Field("appPkg") appPkg: String,
        @Field("ifAllDay") ifAllDay: Boolean,
        @Field("startTime") startTime: Long,
        @Field("endTime") endTime: Long,
        @Field("limitLength") limitLength: Long,

        @Field("title") title: String,
        @Field("sunday") sunday: Boolean,
        @Field("monday") monday: Boolean,
        @Field("tuesday") tuesday: Boolean,
        @Field("wednesday") wednesday: Boolean,
        @Field("thursday") thursday: Boolean,
        @Field("friday") friday: Boolean,
        @Field("saturday") saturday: Boolean,
        @Field("editStartTime") editStartTime: Long,
        @Field("editEndTime") editEndTime: Long,
        @Field("editMoney") editMoney: Long,
        @Field("valid") valid: Boolean,

        @Field("trend") trend: Int,
        @Field("syncTime") syncTime: Long,
        @Field("uuid") uuid: Long,
        @Field("version") version: Int
    ): ApiResponse<UpdateAppLimitResult>

    //向服务器deleteAppLimit
    @FormUrlEncoded
    @POST("/sync/deleteAppLimit")
    suspend fun deleteAppLimit(
        @Field("uuid") uuid: Long,
        @Field("version") version: Int,
        @Field("syncTime") syncTime: Long
    ): ApiResponse<Any>

    //向服务器pullAppLimit
    @FormUrlEncoded
    @POST("/sync/pullAppLimit")
    suspend fun pullAppLimit(@Field("latestTime") latestTime: Long): ApiResponse<List<PullAppLimitResult>>


    //向服务器提交误锁信息
    @FormUrlEncoded
    @POST("/console/uploadDenyPkgInfo/v1")
    suspend fun uploadDenyPkgInfo(
        @Field("appName") appName: String,
        @Field("appPkg") appPkg: String,
        @Field("appMainActivity") appMainActivity: String
    ): ApiResponse<Any>


    //向服务器上传锁机记录
    @FormUrlEncoded
    @POST("/offtime/syncHistory")
    suspend fun syncHistory(
        @Field("startTime") startTime: Long,
        @Field("trueStartTime") trueStartTime: Long,
        @Field("timeLength") timeLength: Long,
        @Field("trueTimeLength") trueTimeLength: Long,
        @Field("lockType") lockType: Int,
        @Field("tomatoIndexId") tomatoIndexId: String,
        @Field("scheduleIndexId") scheduleIndexId: String,

        ): ApiResponse<Any>


    //我的排名
    @GET("/offtime/myTrendNo")
    suspend fun myTrendNo(@Query("deltaDay") deltaDay: Int): ApiResponse<MyTrend>

    //我的锁机时长 # 0:上周，1：上上周，每往前一周加1
    @GET("/offtime/lockLength/v2")
    suspend fun lockLength(@Query("deltaWeek") deltaWeek: Int): ApiResponse<MutableList<Int>>


    //兑换码兑换
    @FormUrlEncoded
    @POST("/vip/duihuanma/v1")
    suspend fun duihuanma(@Field("duihuanma") duihuanma: String): ApiResponse<Any>


    //云端配置
    @GET("/console/getConfig/v1")
    suspend fun getConfig(): ApiResponse<MyConfig>

    //锁机在线
    @POST("/offtime/syncOnline/v2")
    suspend fun syncOnline(): ApiResponse<Int>

    //锁机下线
    @POST("/offtime/offline/v1")
    suspend fun offline(): ApiResponse<Any>

    //打卡信息
    @GET("/punchCard/getPunchCardMsg")
    suspend fun getPunchCardMsg(): ApiResponse<PunchCardMsg>


    //打卡
    @FormUrlEncoded
    @POST("/punchCard/punchCard")
    suspend fun punchCard(
        @Field("lockNumber") lockNumber: Int,
        @Field("goOnDays") goOnDays: Int,
        @Field("totalLength") totalLength: Long,
        @Field("length") length: Long,
        @Field("imgUrl") imgUrl: String,
        @Field("word") word: String
    ): ApiResponse<Any>


    //打卡列表
    @GET("/punchCard/getPunchCards")
    suspend fun getPunchCards(@Query("lastId") lastId: Long, @Query("isMyself") isMyself: Boolean): ApiResponse<MutableList<ThePunchCard>>

    //移除打卡
    @FormUrlEncoded
    @POST("/punchCard/removePunchCard")
    suspend fun removePunchCard(@Field("punchId") punchId: Long): ApiResponse<Any>


    //房间内打卡列表
    @GET("/room/getRoomMemberPunch")
    suspend fun getRoomMemberPunch(@Query("lastId") lastId: Long, @Query("roomId") roomId: Long): ApiResponse<MutableList<ThePunchCard>>

    //房间内成员列表
    @GET("/room/getRoomMemberInfo")
    suspend fun getRoomMemberInfo(@Query("lastId") lastId: Long, @Query("roomId") roomId: Long): ApiResponse<MutableList<RoomDetailMember>>

    //房间内留言板
    @GET("/room/getRoomBoard")
    suspend fun getRoomBoard(@Query("lastId") lastId: Long, @Query("roomId") roomId: Long): ApiResponse<MutableList<RoomDetailBoard>>

    //用暗号查房间ID
    @GET("/room/getRoomFromCode")
    suspend fun getRoomFromCode(@Query("roomCode") roomCode: String): ApiResponse<RoomInfoFromCode>

    //加入房间
    @FormUrlEncoded
    @POST("/room/joinRoom")
    suspend fun joinRoom(@Field("roomId") roomId: Int, @Field("pwd") pwd: String): ApiResponse<Any>


    //退出房间
    @FormUrlEncoded
    @POST("/room/deleteRoom")
    suspend fun deleteRoom(@Field("roomId") roomId: Int): ApiResponse<Any>

    //解散房间
    @FormUrlEncoded
    @POST("/room/quitRoom")
    suspend fun quitRoom(@Field("roomId") roomId: Int): ApiResponse<Any>

    //房主移除成员
    @FormUrlEncoded
    @POST("/room/removeMember")
    suspend fun removeMember(@Field("roomId") roomId: Int, @Field("memberId") memberId: Int): ApiResponse<Any>

    //房主移除留言
    @FormUrlEncoded
    @POST("/room/removeBoard")
    suspend fun removeBoard(@Field("roomId") roomId: Int, @Field("boardId") boardId: Int): ApiResponse<Any>

    //留言
    @FormUrlEncoded
    @POST("/room/postBoard")
    suspend fun postBoard(@Field("roomId") roomId: Long, @Field("word") word: String): ApiResponse<Any>

    //验证房间密码
    @FormUrlEncoded
    @POST("/room/verifyRoomPwd")
    suspend fun verifyRoomPwd(@Field("roomId") roomId: Long, @Field("pwd") pwd: String): ApiResponse<Any>


    //分享打卡
    @POST("/punchCard/shareCard")
    suspend fun shareCard(): ApiResponse<Any>


    //更新阅读消息时间
    @POST("/console/readMsg")
    suspend fun readMsg(): ApiResponse<Any>


    //消息列表
    @GET("/console/msgList")
    suspend fun msgList(@Query("lastId") lastId: Long): ApiResponse<MutableList<MyMsg>>

    //未读消息个数
    @GET("/console/unreadMsgCount")
    suspend fun unreadMsgCount(): ApiResponse<UnReadMsg>


    //房间内留言板
    @GET("/room/getRoomRequestList")
    suspend fun getRoomRequestList(): ApiResponse<MutableList<RoomRequestBean>>

    //验证房间申请
    @FormUrlEncoded
    @POST("/room/requestRoomOK")
    suspend fun requestRoomOK(@Field("requestRoomId") requestRoomId: Long, @Field("result") result: Int, @Field("reason") reason: String): ApiResponse<Any>


    //宝藏软件列表
    @GET("/console/getBaozang")
    suspend fun getBaozang(): ApiResponse<MutableList<BaoZangApp>>

    //启动后对话框
    @GET("/console/launchDialog/v2")
    suspend fun getLaunchDialog(): ApiResponse<LaunchDialog>

    //已读对话框消息对话框
    @FormUrlEncoded
    @POST("/console/readLaunchDialogMsg/v2")
    suspend fun readLaunchDialogMsg(@Field("launchDialogMsgId") launchDialogMsgId: Long): ApiResponse<Any>

    //获取快捷屏蔽页面列表
    @GET("/other/getFastDenyPageExample/v2")
    suspend fun getFastDenyPageExample(): ApiResponse<MutableList<FastDenyPageExample>>

    //获取用户码
    @GET("/offtime/getUserCode/v1")
    suspend fun getUserCode(): ApiResponse<UserCode>

    //解锁码解锁
    @FormUrlEncoded
    @POST("/offtime/friendUnlock/v1")
    suspend fun friendUnlock(@Field("userCode") userCode: String, @Field("unlockCode") unlockCode: String): ApiResponse<Any>

    //获取解锁码
    @FormUrlEncoded
    @POST("/offtime/getUnlockCode/v1")
    suspend fun getUnlockCode(@Field("userCode") userCode: String): ApiResponse<SubUnlockCode>


    //获取待解锁用户信息
    @GET("/console/getDeveloperUnlockInfo/v1")
    suspend fun getDeveloperUnlockInfo(@Query("unlockUserId") unlockUserId: Long): ApiResponse<DeveloperUnlockUserInfo>

    //为用户解锁
    @FormUrlEncoded
    @POST("/console/developerUnlock/v1")
    suspend fun developerUnlock(@Field("unlockUserId") unlockUserId: Long): ApiResponse<Any>

//    //是否云端解锁
//    @POST("/console/ifDeveloperUnlock/v1")
//    suspend fun ifDeveloperUnlock(): ApiResponse<Boolean>
}