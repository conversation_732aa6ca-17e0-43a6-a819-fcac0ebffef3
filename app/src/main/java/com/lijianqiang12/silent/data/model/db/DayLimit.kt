package com.lijianqiang12.silent.data.model.db

import androidx.room.ColumnInfo
import androidx.room.Entity
import androidx.room.PrimaryKey
import com.lijianqiang12.silent.MyConstants
import com.lijianqiang12.silent.utils.MMKVUtils


@Entity
data class DayLimit(
    @PrimaryKey(autoGenerate = true) val id: Long,
    var userId: Int,
    var allDayLimit: Long = -1L,
    var isIncludeWhite: Boolean = true,
    var isDenyChange: Boolean = false,
    var jumpDate: String = "",
    @ColumnInfo(defaultValue = "20") var denyChangeLength: Int = 20,


    @ColumnInfo(defaultValue = "true") var isWorkDayLimit: Boolean = true,//是否是工作日限时,
    @ColumnInfo(defaultValue = "true") var monday: Boolean = true,
    @ColumnInfo(defaultValue = "true") var tuesday: Boolean = true,
    @ColumnInfo(defaultValue = "true") var wednesday: Boolean = true,
    @ColumnInfo(defaultValue = "true") var thursday: Boolean = true,
    @ColumnInfo(defaultValue = "true") var friday: Boolean = true,
    @ColumnInfo(defaultValue = "false") var saturday: Boolean = false,
    @ColumnInfo(defaultValue = "false") var sunday: Boolean = false,

    ) {
    //    constructor() : this(0, 24 * 60 * 60 - 60)
    constructor() : this(
        0,
        MMKVUtils.getInt(MyConstants.SP_KEY_USER_ID, -1),

        -1, true, false, "", 20, true, true, true, true, true, true, false, false
    )

    fun copy(): DayLimit {

        return DayLimit(
            id,
            userId,
            allDayLimit,
            isIncludeWhite,
            isDenyChange,
            jumpDate,
            denyChangeLength,
            isWorkDayLimit,
            monday,
            tuesday,
            wednesday,
            thursday,
            friday,
            saturday,
            sunday,
        )
    }
}