package com.lijianqiang12.silent.data.model.net

import com.lijianqiang12.silent.data.model.net.api.Api
import okhttp3.OkHttpClient


object MyRetrofitClient : BaseRetrofitClient() {

    val service by lazy {
        getService(Api::class.java, Api.BASE_URL)
    }
//    val service: Api
//        get() {
//            return getService(Api::class.java, Api.BASE_URL) //如果使用单例，联网失败以后很久都无法联网
//        }

    override fun handleBuilder(builder: OkHttpClient.Builder) {

    }

}

