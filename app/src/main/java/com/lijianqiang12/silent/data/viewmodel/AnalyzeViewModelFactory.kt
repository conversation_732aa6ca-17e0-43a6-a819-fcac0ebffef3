package com.lijianqiang12.silent.data.viewmodel

import androidx.lifecycle.ViewModel
import androidx.lifecycle.ViewModelProvider
import com.lijianqiang12.silent.data.model.repository.OffTimeRepository
import com.lijianqiang12.silent.data.model.repository.UsageRepository

@Suppress("UNCHECKED_CAST")
class AnalyzeViewModelFactory(private val usageRepository: UsageRepository, private val offTimeRepository: OffTimeRepository) : ViewModelProvider.NewInstanceFactory() {

    override fun <T : ViewModel> create(modelClass: Class<T>): T {
        return AnalyzeViewModel(usageRepository, offTimeRepository) as T
    }

}