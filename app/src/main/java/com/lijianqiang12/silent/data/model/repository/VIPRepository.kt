package com.lijianqiang12.silent.data.model.repository

import android.content.Context
import com.lijianqiang12.silent.data.model.net.MyRetrofitClient

class VIPRepository private constructor(private val appContext: Context) {


    suspend fun makeAlipayOrderVIP(amount: Int, original: Int, name: String, phone: String, address: String) = MyRetrofitClient.service.makeAlipayOrder(amount, original, name, phone, address)
    suspend fun makeWXOrderVIP(amount: Int, original: Int, name: String, phone: String, address: String) = MyRetrofitClient.service.makeWXOrder(amount, original, name, phone, address)

    suspend fun makeAlipayOrderForceUnlock(amount: Int,forceUnlockInfo: String) = MyRetrofitClient.service.makeAlipayOrderForceUnlock(1, amount,forceUnlockInfo)
    suspend fun makeWXOrderForceUnlock(amount: Int,forceUnlockInfo: String) = MyRetrofitClient.service.makeWXOrderForceUnlock(1, amount,forceUnlockInfo)

    suspend fun queryOrder() = MyRetrofitClient.service.queryOrder()

    suspend fun getMoney() = MyRetrofitClient.service.getMoney()
    suspend fun getBuyHistory() = MyRetrofitClient.service.getBuyHistory()


    companion object {

        @Volatile
        private var instance: VIPRepository? = null

        fun getInstance(appContext: Context) = instance ?: synchronized(this) {
            instance ?: VIPRepository(appContext).also { instance = it }
        }
    }
}

