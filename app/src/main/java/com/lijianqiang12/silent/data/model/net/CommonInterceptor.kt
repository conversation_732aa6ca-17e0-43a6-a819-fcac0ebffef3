package com.lijianqiang12.silent.data.model.net

import android.os.Build
import com.blankj.utilcode.util.AppUtils
import com.lijianqiang12.silent.utils.MMKVUtils
import com.lijianqiang12.silent.MyConstants
import com.lijianqiang12.silent.utils.MyToastUtil
import com.lijianqiang12.silent.utils.NdkUtils
import com.lijianqiang12.silent.utils.getChannelName
import okhttp3.*
import java.net.URLDecoder
import java.util.*

class CommonInterceptor : Interceptor {
    override fun intercept(chain: Interceptor.Chain): Response {

        //注意！！！，okhttp头中不能出现中文
        val temp = StringBuilder()
        val userId = "" + MMKVUtils.getInt(MyConstants.SP_KEY_USER_ID, -1)
        val token = "" + MMKVUtils.getString(MyConstants.SP_KEY_TOKEN, "")
        val timestamp = "" + System.currentTimeMillis()
        val apiVersion = "v20240322"
        val channel = getChannelName()
        val brand = Build.BRAND
        val model = Build.MODEL
//        val brand = "HONOR"
//        val model = "CDY-AN90"

        temp.append("userId=").append(userId)
            .append("&token=").append(token)
            .append("&timestamp=").append(timestamp)
            .append("&package=").append(AppUtils.getAppPackageName())
            .append("&appVersion=").append(AppUtils.getAppVersionCode())
            .append("&deviceId=").append(deviceId)
            .append("&oaid=").append(oaid)
            .append("&model=").append(model)
            .append("&brand=").append(brand)
            .append("&channel=").append(channel)
            .append("&sdkVersion=").append(Build.VERSION.SDK_INT)
            .append("&apiVersion=").append(apiVersion)

        val request = chain.request()
        val method = request.method
        val requestBody = request.body
        if ("GET" == method) {
            val url = request.url
            val parameterNames = url.queryParameterNames
            val ts = TreeSet(parameterNames)
            for (key in ts) {
                temp.append("&").append(key).append("=").append(URLDecoder.decode(url.queryParameter(key), "utf-8"))
            }
        } else if ("POST" == method || "PUT" == method || "DELETE" == method || "PATCH" == method) {
            if (requestBody is FormBody) {
                val ts: MutableList<MyForm> = mutableListOf()
                for (i in 0 until requestBody.size) {
                    ts.add(MyForm(requestBody.encodedName(i), URLDecoder.decode(requestBody.encodedValue(i), "utf-8")))
                }
                ts.sortWith(Comparator { a, b -> a.key.compareTo(b.key) })
                for (i in 0 until requestBody.size) { // 如果要对已有的参数做进一步处理可以这样拿到参数
                    temp.append("&").append(ts[i].key).append("=").append(ts[i].value)
                }
            }
        }

//        LogUtils.d("aaaaaaaaaaaa:" + temp.toString())

        val sign = NdkUtils().getSign(temp.toString())

        val cc = CacheControl.Builder()
            //不使用缓存，但是会保存缓存数据
            //.noCache()
            //不使用缓存，同时也不保存缓存数据
            .noStore()
            //只使用缓存，（如果我们要加载的数据本身就是本地数据时，可以使用这个，不过目前尚未发现使用场景）
            //.onlyIfCached()
            //手机可以接收响应时间小于当前时间加上10s的响应
//                .minFresh(10,TimeUnit.SECONDS)
            //手机可以接收有效期不大于10s的响应
//            .maxAge(10, TimeUnit.SECONDS)
            //手机可以接收超出60*5s的响应
//            .maxStale(60 * 5, TimeUnit.SECONDS)
            .build()

        val requestBuilder = request.newBuilder()
            .addHeader("userId", userId)
            .addHeader("token", token)
            .addHeader("timestamp", timestamp)
            .addHeader("package", AppUtils.getAppPackageName())
            .addHeader("appVersion", "" + AppUtils.getAppVersionCode())
            .addHeader("deviceId", deviceId)
            .addHeader("oaid", oaid)
            .addHeader("model", model)
            .addHeader("brand", brand)
            .addHeader("channel", channel)
            .addHeader("sdkVersion", "" + Build.VERSION.SDK_INT)
            .addHeader("apiVersion", apiVersion)
            .addHeader("sign", sign)
            .cacheControl(cc)
        val result = requestBuilder.build()
        return chain.proceed(result)
    }

    private val deviceId: String
        get() {
            return MMKVUtils.getString(MyConstants.SP_KEY_UUID, "")
        }

    private val oaid: String
        get() {
            return MMKVUtils.getString(MyConstants.SP_KEY_OAID, "")
        }

    internal inner class MyForm(var key: String, var value: String)

    companion object {
        /**
         * 解析请求参数
         *
         * @param request
         * @return
         */
        fun parseParams(request: Request): Map<String?, String?>? { //GET POST DELETE PUT PATCH
            val method = request.method
            var params: Map<String?, String?>? = null
            if ("GET" == method) {
                params = doGet(request)
            } else if ("POST" == method || "PUT" == method || "DELETE" == method || "PATCH" == method) {
                val body = request.body
                if (body != null && body is FormBody) {
                    params = doForm(request)
                }
            }
            return params
        }

        /**
         * 获取get方式的请求参数
         */
        private fun doGet(request: Request): Map<String?, String?> {
            val params: MutableMap<String?, String?> = HashMap()
            val url = request.url
            val strings = url.queryParameterNames
            val iterator: Iterator<String> = strings.iterator()
            var i = 0
            while (iterator.hasNext()) {
                val name = iterator.next()
                val value = url.queryParameterValue(i)
                params[name] = value
                i++
            }
            return params
        }

        /**
         * 获取表单的请求参数
         */
        private fun doForm(request: Request): Map<String?, String?>? {
            var params: MutableMap<String?, String?>? = null
            var body: FormBody? = null
            try {
                body = request.body as FormBody?
            } catch (c: ClassCastException) {
                MyToastUtil.showError(c.toString())
            }
            if (body != null) {
                val size = body.size
                if (size > 0) {
                    params = HashMap()
                    for (i in 0 until size) {
                        params[body.name(i)] = body.value(i)
                    }
                }
            }
            return params
        }
    }
}