package com.lijianqiang12.silent.data.model.db

import android.os.Parcel
import android.os.Parcelable
import androidx.room.ColumnInfo
import androidx.room.Embedded
import androidx.room.Entity
import androidx.room.Index
import androidx.room.PrimaryKey
import com.lijianqiang12.silent.MyConstants
import com.lijianqiang12.silent.utils.MMKVUtils

@Entity(indices = [Index("scheduleIndexId", unique = true)])
data class Schedule(
    @PrimaryKey(autoGenerate = true) var id: Long,
    var userId: Int,
    var title: String,
    var tomatoIndexId: String,
    var scheduleIndexId: String,
    var startHour: Int,
    var startMinute: Int,
    var validate: Boolean,
    var sunday: Boolean, var monday: Boolean, var tuesday: Boolean, var wednesday: Boolean,
    var thursday: Boolean, var friday: Boolean, var saturday: Boolean,
    var useTomato: Boolean,
    var endHour: Int,
    var endMinute: Int,
    var isRecycle: Boolean,
    var isDenyChange: Boolean,
    @ColumnInfo(defaultValue = "60") var denyChangeLength: Int,
    var jumpDate: String,
    var trend: Int,
    var syncState: Int, var syncTime: Long, var uuid: Long, var version: Int,
    @Embedded var lockConfig: LockConfig
) : Parcelable {

    constructor() : this(
        0,
        MMKVUtils.getInt(MyConstants.SP_KEY_USER_ID, -1),
        "", "", "", 0, 0, false, false, true, true, true, true, true, false, false, 0, 1, true, false, 60, "", 0, 0, 0, 0, 0, LockConfig()
    )

    constructor(parcel: Parcel) : this(
        parcel.readLong(),
        parcel.readInt(),
        parcel.readString()!!,
        parcel.readString()!!,
        parcel.readString()!!,
        parcel.readInt(),
        parcel.readInt(),
        parcel.readByte() != 0.toByte(),
        parcel.readByte() != 0.toByte(),
        parcel.readByte() != 0.toByte(),
        parcel.readByte() != 0.toByte(),
        parcel.readByte() != 0.toByte(),
        parcel.readByte() != 0.toByte(),
        parcel.readByte() != 0.toByte(),
        parcel.readByte() != 0.toByte(),
        parcel.readByte() != 0.toByte(),
        parcel.readInt(),
        parcel.readInt(),
        parcel.readByte() != 0.toByte(),
        parcel.readByte() != 0.toByte(),
        parcel.readInt(),
        parcel.readString()!!,
        parcel.readInt(),
        parcel.readInt(),
        parcel.readLong(),
        parcel.readLong(),
        parcel.readInt(),
        parcel.readParcelable(LockConfig::class.java.classLoader)!!
    )

    override fun writeToParcel(parcel: Parcel, flags: Int) {
        parcel.writeLong(id)
        parcel.writeInt(userId)
        parcel.writeString(title)
        parcel.writeString(tomatoIndexId)
        parcel.writeString(scheduleIndexId)
        parcel.writeInt(startHour)
        parcel.writeInt(startMinute)
        parcel.writeByte(if (validate) 1 else 0)
        parcel.writeByte(if (sunday) 1 else 0)
        parcel.writeByte(if (monday) 1 else 0)
        parcel.writeByte(if (tuesday) 1 else 0)
        parcel.writeByte(if (wednesday) 1 else 0)
        parcel.writeByte(if (thursday) 1 else 0)
        parcel.writeByte(if (friday) 1 else 0)
        parcel.writeByte(if (saturday) 1 else 0)
        parcel.writeByte(if (useTomato) 1 else 0)
        parcel.writeInt(endHour)
        parcel.writeInt(endMinute)
        parcel.writeByte(if (isRecycle) 1 else 0)
        parcel.writeByte(if (isDenyChange) 1 else 0)
        parcel.writeInt(denyChangeLength)
        parcel.writeString(jumpDate)
        parcel.writeInt(trend)
        parcel.writeInt(syncState)
        parcel.writeLong(syncTime)
        parcel.writeLong(uuid)
        parcel.writeInt(version)
        parcel.writeParcelable(lockConfig, flags)
    }

    override fun describeContents(): Int {
        return 0
    }


    companion object CREATOR : Parcelable.Creator<Schedule> {
        override fun createFromParcel(parcel: Parcel): Schedule {
            return Schedule(parcel)
        }

        override fun newArray(size: Int): Array<Schedule?> {
            return arrayOfNulls(size)
        }
    }


    fun copy(): Schedule {
        val result = Schedule()
        result.userId = this.userId
        result.title = this.title
        result.tomatoIndexId = this.tomatoIndexId
        result.startHour = this.startHour
        result.startMinute = this.startMinute
        result.validate = this.validate
        result.sunday = this.sunday
        result.monday = this.monday
        result.tuesday = this.tuesday
        result.wednesday = this.wednesday
        result.thursday = this.thursday
        result.friday = this.friday
        result.saturday = this.saturday
        result.useTomato = this.useTomato
        result.endHour = this.endHour
        result.endMinute = this.endMinute
        result.isRecycle = this.isRecycle
        result.isDenyChange = this.isDenyChange
        result.denyChangeLength = this.denyChangeLength

        result.lockConfig = this.lockConfig.copy()
        return result
    }

    override fun toString(): String {
        return "Schedule(id=$id, userId=$userId, title='$title',tomatoIndexId='$tomatoIndexId', scheduleIndexId='$scheduleIndexId', startHour=$startHour, startMinute=$startMinute, validate=$validate, sunday=$sunday, monday=$monday, tuesday=$tuesday, wednesday=$wednesday, thursday=$thursday, friday=$friday, saturday=$saturday, useTomato=$useTomato, endHour=$endHour, endMinute=$endMinute, isRecycle=$isRecycle, isDenyChange=$isDenyChange, denyChangeLength=$denyChangeLength, trend=$trend, syncState=$syncState, syncTime=$syncTime, uuid=$uuid, version=$version, lockConfig=$lockConfig)"
    }

}
