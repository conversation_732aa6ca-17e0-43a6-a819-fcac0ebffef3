package com.lijianqiang12.silent.data.viewmodel

import androidx.lifecycle.MutableLiveData
import androidx.lifecycle.viewModelScope
import com.lijianqiang12.silent.data.model.net.pojos.MyTrend
import com.lijianqiang12.silent.data.model.net.pojos.OffTimeDetail
import com.lijianqiang12.silent.data.model.net.convertHttpRes
import com.lijianqiang12.silent.data.model.net.handlingApiExceptions
import com.lijianqiang12.silent.data.model.net.handlingExceptions
import com.lijianqiang12.silent.data.model.net.handlingHttpResponse
import com.lijianqiang12.silent.data.model.repository.OffTimeRepository
import com.lijianqiang12.silent.data.model.repository.UsageRepository
import com.lijianqiang12.silent.component.activity.analyze.AppTime
import dagger.hilt.android.lifecycle.HiltViewModel
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.launch
import kotlinx.coroutines.withContext
import javax.inject.Inject

@HiltViewModel
class AnalyzeViewModel @Inject constructor(
    private val usageRepository: UsageRepository,
    private val offTimeRepository: OffTimeRepository
) : BaseViewModel() {

    private val TAG = "AnalyzeViewModel"

    private val _yesterdayTopLiveData = MutableLiveData<MutableList<OffTimeDetail>>()
    private val _todayTopLiveData = MutableLiveData<MutableList<OffTimeDetail>>()
    private val _todayTrendLiveData = MutableLiveData<MutableList<OffTimeDetail>>()
    private val _appUsageTimeTodayLiveData = MutableLiveData<MutableList<AppTime>>()
    private val _appUsageTimeAnalyzeLiveData = MutableLiveData<MutableList<AppTimeAnalyze>>()
    private val _myTrendLiveData = MutableLiveData<MyTrend>()
    private val _myTrendYesterdayLiveData = MutableLiveData<MyTrend>()
    private val _myLockLengthLiveData = MutableLiveData<MutableList<Int>>()

    fun refreshYesterdayTop() {
        launchOnIO(
            tryBlock = {
                offTimeRepository.yesterdayTop3().run {
                    // 进行响应处理
                    handlingHttpResponse<MutableList<OffTimeDetail>>(
                        convertHttpRes(),
                        successBlock = {
                            _yesterdayTopLiveData.postValue(it)
                        },
                        failureBlock = { ex ->
                            _yesterdayTopLiveData.postValue(mutableListOf())
                            handlingApiExceptions(ex)
                        }
                    )
                }
            },
            // 请求异常处理
            catchBlock = { e ->
                _yesterdayTopLiveData.postValue(mutableListOf())
                handlingExceptions(e)
            }
        )

    }

    fun refreshTodayTrend() {
        launchOnIO(
            tryBlock = {
                offTimeRepository.todayTop100().run {
                    // 进行响应处理
                    handlingHttpResponse<MutableList<OffTimeDetail>>(
                        convertHttpRes(),
                        successBlock = {
                            _todayTrendLiveData.postValue(it)
                        },
                        failureBlock = { ex ->
                            _todayTrendLiveData.postValue(mutableListOf())
                            handlingApiExceptions(ex)
                        }
                    )
                }
            },
            // 请求异常处理
            catchBlock = { e ->
                _todayTrendLiveData.postValue(mutableListOf())
                handlingExceptions(e)
            }
        )

    }

    fun refreshTodayTop() {
        launchOnIO(
            tryBlock = {
                offTimeRepository.todayTop3().run {
                    // 进行响应处理
                    handlingHttpResponse<MutableList<OffTimeDetail>>(
                        convertHttpRes(),
                        successBlock = {
                            _todayTopLiveData.postValue(it)
                        },
                        failureBlock = { ex ->
                            _todayTopLiveData.postValue(mutableListOf())
                            handlingApiExceptions(ex)
                        }
                    )
                }
            },
            // 请求异常处理
            catchBlock = { e ->
                _todayTopLiveData.postValue(mutableListOf())
                handlingExceptions(e)
            }
        )

    }


    fun refreshLockLength(deltaWeek: Int) {
        launchOnIO(
            tryBlock = {
                offTimeRepository.lockLength(deltaWeek).run {
                    // 进行响应处理
                    handlingHttpResponse<MutableList<Int>>(
                        convertHttpRes(),
                        successBlock = {
                            _myLockLengthLiveData.postValue(it)
                        },
                        failureBlock = { ex ->
                            _myLockLengthLiveData.postValue(mutableListOf())
                            handlingApiExceptions(ex)
                        }
                    )
                }
            },
            // 请求异常处理
            catchBlock = { e ->
                _myLockLengthLiveData.postValue(mutableListOf())
                handlingExceptions(e)
            }
        )

    }


    fun refreshAppUsageTimeToday(days: Int) {
        viewModelScope.launch {
            withContext(Dispatchers.IO) {
                if (days == 1) {
                    _appUsageTimeTodayLiveData.postValue(usageRepository.getTodayAppUsageTimeRank())
                } else {
                    _appUsageTimeTodayLiveData.postValue(usageRepository.get7DaysAppUsageTimeRank())
                }
            }
        }
    }

    fun refreshAppAnalyzeToday() {
        viewModelScope.launch {
            withContext(Dispatchers.IO) {
                _appUsageTimeAnalyzeLiveData.postValue(usageRepository.getAppAnalyze())
            }
        }
    }


    fun refreshMyTrend() {
        launchOnIO(
            tryBlock = {
                offTimeRepository.myTrend(0).run {
                    // 进行响应处理
                    handlingHttpResponse<MyTrend>(
                        convertHttpRes(),
                        successBlock = {
                            _myTrendLiveData.postValue(it)
                        },
                        failureBlock = { ex ->
                            _myTrendLiveData.postValue(MyTrend(1000))
                            handlingApiExceptions(ex)
                        }
                    )
                }
            },
            // 请求异常处理
            catchBlock = { e ->
                _myTrendLiveData.postValue(MyTrend(1000))
                handlingExceptions(e)
            }
        )
    }

    fun refreshMyTrendYesterday() {
        launchOnIO(
            tryBlock = {
                offTimeRepository.myTrend(-1).run {
                    // 进行响应处理
                    handlingHttpResponse<MyTrend>(
                        convertHttpRes(),
                        successBlock = {
                            _myTrendYesterdayLiveData.postValue(it)
                        },
                        failureBlock = { ex ->
                            _myTrendYesterdayLiveData.postValue(MyTrend(1000))
                            handlingApiExceptions(ex)
                        }
                    )
                }
            },
            // 请求异常处理
            catchBlock = { e ->
                _myTrendYesterdayLiveData.postValue(MyTrend(1000))
                handlingExceptions(e)
            }
        )
    }

    /**
     * 昨日榜首
     */
    val yesterdayTopLiveData: MutableLiveData<MutableList<OffTimeDetail>> by lazy {
//        refreshYesterdayTop()
        _yesterdayTopLiveData
    }

    /**
     * 今日排名
     */
    val todayTrendLiveData: MutableLiveData<MutableList<OffTimeDetail>> by lazy {
        _todayTrendLiveData
    }

    /**
     * 今日榜首
     */
    val todayTopLiveData: MutableLiveData<MutableList<OffTimeDetail>> by lazy {
//        refreshTodayTop()
        _todayTopLiveData
    }

    /**
     * 我的排名
     */
    val myTrendLiveData: MutableLiveData<MyTrend> by lazy {
//        refreshMyTrend()
        _myTrendLiveData
    }

    val myTrendYesterdayLiveData: MutableLiveData<MyTrend> by lazy {
//        refreshMyTrendYesterday()
        _myTrendYesterdayLiveData
    }

    val myLockLengthLiveData: MutableLiveData<MutableList<Int>> by lazy {
//        refreshLockLength(0)
        _myLockLengthLiveData
    }

    /**
     * 当日app使用时长排名
     */
    val appUsageTimeTodayLiveData: MutableLiveData<MutableList<AppTime>> by lazy {
//        refreshAppUsageTimeToday(1)
        _appUsageTimeTodayLiveData
    }


    /**
     * app使用时长变化分析
     */
    val appAnalyzeTodayLiveData: MutableLiveData<MutableList<AppTimeAnalyze>> by lazy {
//        refreshAppAnalyzeToday()
        _appUsageTimeAnalyzeLiveData
    }
}