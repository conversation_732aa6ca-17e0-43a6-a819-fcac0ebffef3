package com.lijianqiang12.silent.data.model.db

import androidx.room.ColumnInfo
import androidx.room.Entity
import androidx.room.PrimaryKey
import com.lijianqiang12.silent.MyConstants
import com.lijianqiang12.silent.utils.MMKVUtils

@Entity
data class LockHistory(
    @PrimaryKey(autoGenerate = true) var id: Long,
//    var userId: Int,
    var title: String,
    @ColumnInfo(defaultValue = "") var content: String, //后加的，要设置默认值
    var startTime: Long,
    var trueStartTime: Long,
    var timeLength: Long,//创建时添加，亮屏时使用当前时间，息屏时使用预设时间，因修改配置而触发的，当前值，结束时修正，正常结束使用设置的时间，强制结束使用当前时间
    var trueTimeLength: Long,//创建时添加，亮屏时使用当前时间，息屏时使用预设时间，因修改配置而触发的，当前值，结束时修正，正常结束使用设置的时间，强制结束使用当前时间
    var lockType: Int,//1：简单锁机 2：番茄 3：定时
    var simpleLockLength: Int,
    var tomatoIndexId: String,
    var scheduleIndexId: String,
    var isFinish: Boolean,//true：已经结束
    var isForceQuit: Boolean,//true：强制退出
    var isSynced: Boolean,//true：已同步到云端
    var isGeneratedCard: Boolean,//true：已打卡
    @ColumnInfo(defaultValue = "[]") var deleteWhiteAppTemp: String,//临时移除的白名单app
) {
    constructor() : this(
        0,
        "", "",0, 0, 0, 0, 0, -1, "", "", false,
        false, false, false, "[]"
    )
}