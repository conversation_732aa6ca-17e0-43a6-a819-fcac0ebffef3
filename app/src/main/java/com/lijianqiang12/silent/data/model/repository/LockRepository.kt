package com.lijianqiang12.silent.data.model.repository

import android.content.Intent
import android.content.pm.ResolveInfo
import androidx.lifecycle.LiveData
import androidx.lifecycle.map
import com.lijianqiang12.silent.MyConstants
import com.lijianqiang12.silent.TheApplication
import com.lijianqiang12.silent.component.activity.lock.whiteapp.AppInfo
import com.lijianqiang12.silent.data.model.db.*
import com.lijianqiang12.silent.data.model.net.MyRetrofitClient
import com.lijianqiang12.silent.utils.MMKVUtils
import java.util.*


class LockRepository private constructor(
    private val fastDao: FastDao, private val tomatoDao: TomatoDao, private val scheduleDao: ScheduleDao,
    private val whiteAppDao: WhiteAppDao, private val lockHistoryDao: LockHistoryDao
) {

    suspend fun getWellKnowWord() = MyRetrofitClient.service.getWellKnowWord()
    suspend fun wellKnowWordStar(wordId: Int) = MyRetrofitClient.service.wellKnowWordStar(wordId)
    suspend fun wellKnowWordShare(wordId: Int) = MyRetrofitClient.service.wellKnowWordShare(wordId)

    /*同步相关*/
    suspend fun getAllWhiteAppList() = whiteAppDao.getAllWhiteAppList(MMKVUtils.getInt(MyConstants.SP_KEY_USER_ID, -1))
    suspend fun getWhiteAppsWithState(state: Int) = whiteAppDao.getWhiteAppsWithState(MMKVUtils.getInt(MyConstants.SP_KEY_USER_ID, -1), state)
    suspend fun getAllFastList() = fastDao.getAllFastList(MMKVUtils.getInt(MyConstants.SP_KEY_USER_ID, -1))
    suspend fun getFastWithState(state: Int) = fastDao.getFastWithState(MMKVUtils.getInt(MyConstants.SP_KEY_USER_ID, -1), state)
    suspend fun getAllTomatoList() = tomatoDao.getAllTomatoList(MMKVUtils.getInt(MyConstants.SP_KEY_USER_ID, -1))
    suspend fun getTomatoWithState(state: Int) = tomatoDao.getTomatoWithState(MMKVUtils.getInt(MyConstants.SP_KEY_USER_ID, -1), state)
    suspend fun getAllScheduleList() = scheduleDao.getAllScheduleList(MMKVUtils.getInt(MyConstants.SP_KEY_USER_ID, -1))
    suspend fun getScheduleWithState(state: Int) = scheduleDao.getScheduleWithState(MMKVUtils.getInt(MyConstants.SP_KEY_USER_ID, -1), state)


    //Fast相关
    fun getFasts() = fastDao.getFasts(MMKVUtils.getInt(MyConstants.SP_KEY_USER_ID, -1))
    suspend fun createFast(fast: Fast) = fastDao.insertFast(fast)
    suspend fun updateFast(fast: Fast) = fastDao.updateFast(fast)
    suspend fun deleteFast(fast: Fast) = fastDao.deleteFast(fast)
    suspend fun getLastFast() = fastDao.getLastFast(MMKVUtils.getInt(MyConstants.SP_KEY_USER_ID, -1))

    //Tomato相关
    fun getTomatoesWithSub(): LiveData<MutableList<TomatoWithSub>> {
        val source = tomatoDao.getTomatoesWithSub(MMKVUtils.getInt(MyConstants.SP_KEY_USER_ID, -1))
        return source.map {
            it.forEach { item ->
                item.whiteList = item.whiteList.filter { whiteApp -> whiteApp.syncState >= 0 }.sortedBy { whiteApp -> whiteApp.trend }.toMutableList()
            }
            it
        }
    }

    suspend fun getTomatoWithSub(tomatoId: String): TomatoWithSub? {
        val source = tomatoDao.getTomatoWithSub(tomatoId)
        source?.let {
            it.whiteList = it.whiteList.filter { whiteApp -> whiteApp.syncState >= 0 }.toMutableList()
        }
        return source
    }

    suspend fun createTomato(tomato: Tomato) = tomatoDao.insertTomato(tomato)
    suspend fun updateTomato(tomato: Tomato) = tomatoDao.updateTomato(tomato)
    suspend fun deleteTomato(tomato: Tomato) = tomatoDao.deleteTomato(tomato)
    suspend fun getLastTomato() = tomatoDao.getLastTomato(MMKVUtils.getInt(MyConstants.SP_KEY_USER_ID, -1))

    //Schedule相关 因为room不支持为Relation过滤，因此只能在此处过滤
    fun getSchedulesWithSub(userId: Int): LiveData<MutableList<ScheduleWithSub>> {
        val source = scheduleDao.getSchedulesWithSub(userId)
        return source.map {
            it.forEach { item ->
                item.whiteList = item.whiteList.filter { whiteApp -> whiteApp.syncState >= 0 }.sortedBy { whiteApp -> whiteApp.trend }.toMutableList()
            }
            it
        }
    }

    suspend fun getScheduleWithSub(scheduleId: String): ScheduleWithSub? {
        val source = scheduleDao.getScheduleWithSub(scheduleId)
        source?.let {
            it.whiteList = it.whiteList.filter { whiteApp -> whiteApp.syncState >= 0 }.toMutableList()
        }
        return source
    }

    suspend fun createSchedule(schedule: Schedule) = scheduleDao.insertSchedule(schedule)
    suspend fun updateSchedule(schedule: Schedule) = scheduleDao.updateSchedule(schedule)
    suspend fun deleteSchedule(schedule: Schedule) = scheduleDao.deleteSchedule(schedule)
    suspend fun getLastSchedule() = scheduleDao.getLastSchedule(MMKVUtils.getInt(MyConstants.SP_KEY_USER_ID, -1))

    //White相关
    suspend fun getGlobalWhiteAppList() = whiteAppDao.getGlobalWhiteAppList(MMKVUtils.getInt(MyConstants.SP_KEY_USER_ID, -1))
    fun getGlobalWhiteApps(userId:Int) = whiteAppDao.getGlobalWhiteApps(userId)
    fun getWhiteAppsWithTomatoId(tomatoId: String) = whiteAppDao.getWhiteAppsWithTomatoId(tomatoId)
    fun getWhiteAppsWithScheduleId(scheduleId: String) = whiteAppDao.getWhiteAppsWithScheduleId(scheduleId)

    fun getWhiteAppsWithTomatoIdAtOnce(tomatoId: String) = whiteAppDao.getWhiteAppsWithTomatoIdAtOnce(tomatoId)
    fun getWhiteAppsWithScheduleIdAtOnce(scheduleId: String) = whiteAppDao.getWhiteAppsWithScheduleIdAtOnce(scheduleId)

    suspend fun createWhiteApp(whiteApp: WhiteApp) = whiteAppDao.insertWhiteApp(whiteApp)
    suspend fun createWhiteApps(whiteApps: MutableList<WhiteApp>) = whiteAppDao.insertWhiteApps(whiteApps)
    suspend fun deleteWhiteApp(whiteApp: WhiteApp) = whiteAppDao.deleteWhiteApp(whiteApp)
    suspend fun updateWhiteApp(whiteApp: WhiteApp) = whiteAppDao.updateWhiteApp(whiteApp)

    suspend fun deleteWhiteApps(whiteApps: MutableList<WhiteApp>) = whiteAppDao.deleteWhiteApps(whiteApps)
    suspend fun getLastWhiteApp(tomatoId: String, scheduleId: String) = whiteAppDao.getLastWhiteApp(tomatoId, scheduleId)

    suspend fun isWhiteAppExist(tomatoId: String, scheduleId: String, pkg: String, mainActivity: String) =
        whiteAppDao.isWhiteAppExist(MMKVUtils.getInt(MyConstants.SP_KEY_USER_ID, -1), tomatoId, scheduleId, pkg, mainActivity)

    suspend fun getAllAppInfo(): MutableList<AppInfo> {
        val list = mutableListOf<AppInfo>()

        try {
            synchronized(TheApplication.getInstance()) {
                val query = Intent(Intent.ACTION_MAIN)
                query.addCategory("android.intent.category.LAUNCHER")
                val resolves = TheApplication.getInstance().packageManager.queryIntentActivities(query, 0)
                Collections.sort(resolves, ResolveInfo.DisplayNameComparator(TheApplication.getInstance().packageManager))
                resolves.forEachIndexed { _, info ->
                    val appInfo = AppInfo(
                        info.activityInfo.packageName,
                        info.activityInfo.name,
                        info.loadIcon(TheApplication.getInstance().packageManager),
                        info.loadLabel(TheApplication.getInstance().packageManager).toString(),
                        false
                    )
                    list.add(appInfo)
                }
            }
        } catch (e: Exception) {
        }
        return list

    }


    suspend fun getAllAppInfoWithText(text: String): MutableList<AppInfo> {
        val list = mutableListOf<AppInfo>()

        try {
            synchronized(TheApplication.getInstance()) {
                val query = Intent(Intent.ACTION_MAIN)
                query.addCategory("android.intent.category.LAUNCHER")
                val resolves = TheApplication.getInstance().packageManager.queryIntentActivities(query, 0)
                Collections.sort(resolves, ResolveInfo.DisplayNameComparator(TheApplication.getInstance().packageManager))
                resolves.forEachIndexed { _, info ->

                    val appName = info.loadLabel(TheApplication.getInstance().packageManager).toString()
                    if (appName.lowercase().contains(text.lowercase())) {
                        val appInfo = AppInfo(
                            info.activityInfo.packageName, info.activityInfo.name,
                            info.loadIcon(TheApplication.getInstance().packageManager), appName, false
                        )
                        list.add(appInfo)
                    }
                }
            }
        } catch (e: Exception) {
        }

//        list.forEach {
//            Log.d("qqqqqqqqq","qqqqqq "+it.appName+" "+it.pkg+" "+it.mainActivity)
//        }
        return list

    }

    //LockHistory相关
    suspend fun getUnUploadHistory() = lockHistoryDao.getUnUploadHistory()
    suspend fun createLockHistory(lockHistory: LockHistory) {
        val existUnFinished = lockHistoryDao.getUnfinishedLastLockHistory()
        if (existUnFinished == null) {
            lockHistoryDao.insertLockHistory(lockHistory)
        }
    }

    suspend fun updateLockHistory(lockHistory: LockHistory) = lockHistoryDao.updateLockHistory(lockHistory)
    suspend fun deleteLockHistory(lockHistory: LockHistory) = lockHistoryDao.deleteLockHistory(lockHistory)
    fun getUnfinishedLockHistory() = lockHistoryDao.getUnfinishedLockHistory()


    suspend fun getImages(style: Int, lastId: Long, limit: Int) = MyRetrofitClient.service.getImages(style, lastId, limit)

    suspend fun refreshForceUnlockPwd(pwd: String) = MyRetrofitClient.service.refreshForceUnlockPwd(pwd)


    companion object {

        @Volatile
        private var instance: LockRepository? = null

        fun getInstance(
            fastDao: FastDao, tomatoDao: TomatoDao, scheduleDao: ScheduleDao, whiteAppDao: WhiteAppDao,
            lockHistoryDao: LockHistoryDao
        ) = instance
            ?: synchronized(this) {
                instance ?: LockRepository(fastDao, tomatoDao, scheduleDao, whiteAppDao, lockHistoryDao).also { instance = it }
            }
    }

}