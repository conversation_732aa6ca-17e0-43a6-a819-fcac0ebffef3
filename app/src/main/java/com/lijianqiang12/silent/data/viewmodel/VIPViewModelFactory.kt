package com.lijianqiang12.silent.data.viewmodel

import androidx.lifecycle.ViewModel
import androidx.lifecycle.ViewModelProvider
import com.lijianqiang12.silent.data.model.repository.VIPRepository

@Suppress("UNCHECKED_CAST")
class VIPViewModelFactory(private val vipRepository: VIPRepository) : ViewModelProvider.NewInstanceFactory() {

    override fun <T : ViewModel> create(modelClass: Class<T>): T {
        return VIPViewModel(vipRepository) as T
    }

}