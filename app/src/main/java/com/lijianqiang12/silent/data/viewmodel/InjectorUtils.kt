package com.lijianqiang12.silent.data.viewmodel

import android.content.Context
import com.lijianqiang12.silent.data.model.db.AppDatabase
import com.lijianqiang12.silent.data.model.repository.*


object InjectorUtils {

    private fun getLockRepository(context: Context): LockRepository {
        val db = AppDatabase.getInstance(context.applicationContext)
        return LockRepository.getInstance(db.fastDao(), db.tomatoDao(), db.scheduleDao(), db.whiteAppDao(), db.lockHistoryDao())
    }

    private fun getPlanRepository(context: Context): DayLimitRepository {
        return DayLimitRepository.getInstance(AppDatabase.getInstance(context.applicationContext).dayLimitDao(), context.applicationContext)
    }

    private fun getAppLimitRepository(context: Context): AppLimitRepository {
        return AppLimitRepository.getInstance(AppDatabase.getInstance(context.applicationContext).appLimitDao())
    }

    private fun getUsageRepository(context: Context): UsageRepository {
        return UsageRepository.getInstance(context.applicationContext, AppDatabase.getInstance(context.applicationContext).appUsageDao())
    }

    private fun getOffTimeRepository(context: Context): OffTimeRepository {
        return OffTimeRepository.getInstance(context.applicationContext)
    }

    private fun getRoomRepository(context: Context): RoomRepository {
        return RoomRepository.getInstance(context.applicationContext)
    }

    private fun getLoginRepository(context: Context): LoginRepository {
        return LoginRepository.getInstance(context.applicationContext)
    }

    private fun getVIPRepository(context: Context): VIPRepository {
        return VIPRepository.getInstance(context.applicationContext)
    }

    private fun getAccountRepository(context: Context): AccountRepository {
        return AccountRepository.getInstance(context.applicationContext)
    }


    fun provideLockViewModelFactory(context: Context): LockViewModelFactory {
        val lockRepository = getLockRepository(context)
        return LockViewModelFactory(lockRepository)
    }

    fun provideMonitorViewModelFactory(context: Context): MonitorViewModelFactory {
        val planRepository = getPlanRepository(context)
        val appLimitRepository = getAppLimitRepository(context)
        val usageRepository = getUsageRepository(context)
        return MonitorViewModelFactory(planRepository, appLimitRepository, usageRepository)
    }

    fun provideAnalyzeViewModelFactory(context: Context): AnalyzeViewModelFactory {
        val usageRepository = getUsageRepository(context)
        val offTimeRepository = getOffTimeRepository(context)
        return AnalyzeViewModelFactory(usageRepository, offTimeRepository)
    }

    fun provideRoomViewModelFactory(context: Context): RoomViewModelFactory {
        val roomRepository = getRoomRepository(context)
        return RoomViewModelFactory(roomRepository)
    }

    fun provideLoginViewModelFactory(context: Context): LoginViewModelFactory {
        val loginRepository = getLoginRepository(context)
        return LoginViewModelFactory(loginRepository)
    }

    fun provideVIPViewModelFactory(context: Context): VIPViewModelFactory {
        val repository = getVIPRepository(context)
        return VIPViewModelFactory(repository)
    }

    fun provideAccountViewModelFactory(context: Context): AccountViewModelFactory {
        val repository = getAccountRepository(context)
        return AccountViewModelFactory(repository)
    }

}
