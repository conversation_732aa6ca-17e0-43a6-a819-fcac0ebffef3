package com.lijianqiang12.silent.data.model.repository

import android.content.Context
import androidx.lifecycle.LiveData
import androidx.lifecycle.MutableLiveData
import com.lijianqiang12.silent.MyConstants
import com.lijianqiang12.silent.data.model.db.DayLimit
import com.lijianqiang12.silent.data.model.db.DayLimitDao
import com.lijianqiang12.silent.utils.MMKVUtils

class DayLimitRepository private constructor(private val dayLimitDao: DayLimitDao, private val appContext: Context) {

    fun getTodayLimits(dayName: String): LiveData<DayLimit?> {
        val userId = MMKVUtils.getInt(MyConstants.SP_KEY_USER_ID, -1)
        return when (dayName) {
            "monday" -> dayLimitDao.getTodayLimitsMonday(userId)
            "tuesday" -> dayLimitDao.getTodayLimitsTuesday(userId)
            "wednesday" -> dayLimitDao.getTodayLimitsWednesday(userId)
            "thursday" -> dayLimitDao.getTodayLimitsThursday(userId)
            "friday" -> dayLimitDao.getTodayLimitsFriday(userId)
            "saturday" -> dayLimitDao.getTodayLimitsSaturday(userId)
            "sunday" -> dayLimitDao.getTodayLimitsSunday(userId)
            else -> MutableLiveData<DayLimit?>(null)
        }
    }

    fun getTodayLimitsImmediately(dayName: String): DayLimit? {
        val userId = MMKVUtils.getInt(MyConstants.SP_KEY_USER_ID, -1)
        return when (dayName) {
            "monday" -> dayLimitDao.getTodayLimitsImmediatelyMonday(userId)
            "tuesday" -> dayLimitDao.getTodayLimitsImmediatelyTuesday(userId)
            "wednesday" -> dayLimitDao.getTodayLimitsImmediatelyWednesday(userId)
            "thursday" -> dayLimitDao.getTodayLimitsImmediatelyThursday(userId)
            "friday" -> dayLimitDao.getTodayLimitsImmediatelyFriday(userId)
            "saturday" -> dayLimitDao.getTodayLimitsImmediatelySaturday(userId)
            "sunday" -> dayLimitDao.getTodayLimitsImmediatelySunday(userId)
            else -> null
        }
    }

    fun getDayLimits(isWorkDay: Boolean) = dayLimitDao.getDayLimits(MMKVUtils.getInt(MyConstants.SP_KEY_USER_ID, -1), isWorkDay)

    suspend fun insertDayLimit(dayLimit: DayLimit) = dayLimitDao.insertDayLimit(dayLimit)
    suspend fun updateDayLimit(dayLimit: DayLimit) = dayLimitDao.updateDayLimit(dayLimit)


//    fun getPlans() = dayLimitDao.getPlans()
//    fun getRunningPlan() = dayLimitDao.getRunningPlan()
//
//    suspend fun getActivePlan(): DayLimit {
//        var plan = dayLimitDao.getActivePlanSub()
//        if (plan == null) {
//            val planList = mutableListOf<DayLimit>()
//            planList.add(DayLimit(1, 24 * 60 * 60, "PLAN1", true))
//            val database = AppDatabase.getInstance(appContext)
//            database.planDao().insertAll(planList)
//
//            plan = dayLimitDao.getActivePlanSub()
//
//        }
//        return plan!!
//    }

//    fun getPlan(planId: String) = planDao.getPlan(planId)

//    suspend fun updatePlan(dayLimit: DayLimit) = dayLimitDao.updatePlan(dayLimit)

    companion object {

        @Volatile
        private var sInstance: DayLimitRepository? = null

        fun getInstance(dayLimitDao: DayLimitDao, appContext: Context) = sInstance ?: synchronized(this) {
            sInstance ?: DayLimitRepository(dayLimitDao, appContext).also { sInstance = it }
        }
    }

}