package com.lijianqiang12.silent.data.model.repository

import android.content.Context
import com.lijianqiang12.silent.data.model.net.MyRetrofitClient

class LoginRepository private constructor(private val appContext: Context) {
    suspend fun getVerifyCode(codeType: Int, phone: String) = MyRetrofitClient.service.getVerifyCode(codeType, phone)
    suspend fun verifyCode(phone: String, code: String, inviteCode: Int) = MyRetrofitClient.service.verifyCode(phone, code, inviteCode)
    suspend fun socialAccountLogin(socialType: Int, uid: String, username: String, avatar: String, gender: String, inviteCode: Int) =
        MyRetrofitClient.service.socialAccountLogin(socialType, uid, username, avatar, gender, inviteCode)

    suspend fun refreshState() = MyRetrofitClient.service.refreshState()


    companion object {

        @Volatile
        private var instance: LoginRepository? = null

        fun getInstance(appContext: Context) = instance ?: synchronized(this) {
            instance ?: LoginRepository(appContext).also { instance = it }
        }
    }
}

