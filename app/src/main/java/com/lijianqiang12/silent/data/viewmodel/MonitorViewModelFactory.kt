package com.lijianqiang12.silent.data.viewmodel

import androidx.lifecycle.ViewModel
import androidx.lifecycle.ViewModelProvider
import com.lijianqiang12.silent.data.model.repository.AppLimitRepository
import com.lijianqiang12.silent.data.model.repository.DayLimitRepository
import com.lijianqiang12.silent.data.model.repository.UsageRepository

@Suppress("UNCHECKED_CAST")
class MonitorViewModelFactory(
        private val dayLimitRepository: DayLimitRepository,
        private val appLimitRepository: AppLimitRepository,
        private val usageRepository: UsageRepository
) : ViewModelProvider.NewInstanceFactory() {

    override fun <T : ViewModel> create(modelClass: Class<T>): T {
        return MonitorViewModel(dayLimitRepository, appLimitRepository, usageRepository) as T
    }

}