package com.lijianqiang12.silent.data.viewmodel

import androidx.lifecycle.ViewModel
import androidx.lifecycle.ViewModelProvider
import com.lijianqiang12.silent.data.model.repository.AccountRepository

@Suppress("UNCHECKED_CAST")
class AccountViewModelFactory(private val accountRepository: AccountRepository) : ViewModelProvider.NewInstanceFactory() {

    override fun <T : ViewModel> create(modelClass: Class<T>): T {
        return AccountViewModel(accountRepository) as T
    }

}