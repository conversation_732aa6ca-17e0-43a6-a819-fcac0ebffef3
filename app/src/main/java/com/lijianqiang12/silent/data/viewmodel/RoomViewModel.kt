package com.lijianqiang12.silent.data.viewmodel

import androidx.lifecycle.MutableLiveData
import com.lijianqiang12.silent.data.model.net.*
import com.lijianqiang12.silent.data.model.net.pojos.AllRoom
import com.lijianqiang12.silent.data.model.net.pojos.MyJoinedRoom
import com.lijianqiang12.silent.data.model.net.pojos.NetworkState
import com.lijianqiang12.silent.data.model.repository.RoomRepository

class RoomViewModel(private val roomRepository: RoomRepository) : BaseViewModel() {

    private val TAG = "RoomViewModel"
    private val _myJoinedRoomsLiveData = MutableLiveData<MutableList<MyJoinedRoom>>()
    private val _allRoomsLiveData = MutableLiveData<NetworkState<MutableList<AllRoom>>>()
//    private val _allRoomsLiveData :LiveData<PagedList<AllRoom>>//= MutableLiveData<PagedList<AllRoom>>()

//    init {
//        // 1.创建DataSource.Factory
//        val factory: DataSource.Factory<RoomIdAndLevel,AllRoom> = RoomAllDataSourceFactory(roomRepository,viewModelScope)
//
//        val config = PagedList.Config.Builder()
//                .setPageSize(15)              // 分页加载的数量
//                .setInitialLoadSizeHint(30)   // 初次加载的数量
//                .setPrefetchDistance(10)      // 预取数据的距离
//                .setEnablePlaceholders(true) // 是否启用占位符
//                .build()
//
//        _allRoomsLiveData = LivePagedListBuilder(factory, config).build()
//    }


    fun refreshMyJoinedRooms() {
        launchOnIO(
                tryBlock = {
                    roomRepository.myJoinedRooms().run {
                        handlingHttpResponse<MutableList<MyJoinedRoom>>(
                                convertHttpRes(),
                                successBlock = {
                                    _myJoinedRoomsLiveData.postValue(it)
                                },
                                failureBlock = { ex ->
                                    _myJoinedRoomsLiveData.postValue(mutableListOf())
                                    handlingApiExceptions(ex)
                                }
                        )
                    }
                },
                // 请求异常处理
                catchBlock = { e ->
                    _myJoinedRoomsLiveData.postValue(mutableListOf())
                    handlingExceptions(e)
                }
        )

    }


    fun refreshAllRooms(type: Int, newOrAdd: Int, lastRoomId: Long, lastLevelCount: Long) {//0:init 1:add
        launchOnIO(
                tryBlock = {
                    roomRepository.allRooms(type, lastRoomId, lastLevelCount).run {
                        handlingHttpResponse<MutableList<AllRoom>>(
                                convertHttpRes(),
                                successBlock = {
                                    if (newOrAdd == 0) {
                                        if (it.size == 0) {
                                            _allRoomsLiveData.postValue(NetworkState(1, it))
                                        } else {
                                            _allRoomsLiveData.postValue(NetworkState(0, it))
                                        }
                                    } else {
                                        if (it.size == 0) {
                                            _allRoomsLiveData.postValue(NetworkState(1, _allRoomsLiveData.value!!.data))
                                        } else {
                                            val list = mutableListOf<AllRoom>()
                                            list.addAll(_allRoomsLiveData.value!!.data as Iterable<AllRoom>)
                                            list.addAll(it)
                                            _allRoomsLiveData.postValue(NetworkState(0, list))
                                        }

                                    }
                                },
                                failureBlock = { ex ->
                                    _allRoomsLiveData.postValue(NetworkState(2, _allRoomsLiveData.value!!.data))
                                    handlingApiExceptions(ex)
                                }
                        )
                    }
                },
                // 请求异常处理
                catchBlock = { e ->
                    _allRoomsLiveData.postValue(NetworkState(2, mutableListOf()))
                    handlingExceptions(e)
                }
        )

    }


    /**
     * 我关注的房间
     */
    val myJoinedRoomsLiveData: MutableLiveData<MutableList<MyJoinedRoom>> by lazy {
        refreshMyJoinedRooms()
        _myJoinedRoomsLiveData
    }

    val allRoomsLiveData = _allRoomsLiveData


}