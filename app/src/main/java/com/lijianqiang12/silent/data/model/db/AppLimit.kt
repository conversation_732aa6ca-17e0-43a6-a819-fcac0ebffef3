package com.lijianqiang12.silent.data.model.db

import android.os.Parcel
import android.os.Parcelable
import androidx.room.ColumnInfo
import androidx.room.Entity
import androidx.room.Index
import androidx.room.PrimaryKey
import com.lijianqiang12.silent.MyConstants
import com.lijianqiang12.silent.utils.MMKVUtils
import com.lijianqiang12.silent.utils.getRandomIndexId


@Entity(indices = [Index("appLimitIndexId", unique = true)])
data class AppLimit(
    @PrimaryKey(autoGenerate = true) var id: Long = 0L,
    var userId: Int,

    var appLimitIndexId: String,
    var appPkg: String = "",
    var ifAllDay: Boolean = true,
    var startTime: Long = 0,//s
    var endTime: Long = 0,//s
//        var endTime: Long = 24 * 60 * 60 - 60,//s
    var limitLength: Long = 2 * 60 * 60,//s

    var title: String = "",
    var sunday: Boolean = true,
    var monday: Boolean = true,
    var tuesday: Boolean = true,
    var wednesday: Boolean = true,
    var thursday: Boolean = true,
    var friday: Boolean = true,
    var saturday: Boolean = true,
    var editStartTime: Long = 28800,
    var editEndTime: Long = 32400,
    var editMoney: Long = -1L,

    var valid: Boolean = true, //58版本新增，表示是否生效

    var trend: Int = 0,
    var syncState: Int = 0, var syncTime: Long = 0, var uuid: Long = 0, var version: Int = 0,


    ) : Parcelable {

    constructor(id: Long, title: String, appPkg: String) : this(
        id,
        MMKVUtils.getInt(MyConstants.SP_KEY_USER_ID, -1),

        getRandomIndexId(),
        appPkg,
        true,
        0,
        0,
        2 * 60 * 60,
        title,
        true,
        true,
        true,
        true,
        true,
        true,
        true,
        28800,
        32400,
        -1L,
        true,
        0,
        0,
        0,
        0,
    )

    constructor(id: Long) : this(
        id,
        MMKVUtils.getInt(MyConstants.SP_KEY_USER_ID, -1),

        getRandomIndexId(),
        "",
        true,
        0,
        0,
        2 * 60 * 60,
        "",
        true,
        true,
        true,
        true,
        true,
        true,
        true,
        28800,
        32400,
        -1L,
        true,
        0,
        0,
        0,
        0,
    )

    constructor() : this(
        0,
        MMKVUtils.getInt(MyConstants.SP_KEY_USER_ID, -1),
        getRandomIndexId(),
        "",
        true,
        0,
        0,
        2 * 60 * 60,
        "",
        true,
        true,
        true,
        true,
        true,
        true,
        true,
        28800,
        32400,
        -1L,
        true,
        0,
        0,
        0,
        0
    )

    constructor(parcel: Parcel) : this(
        parcel.readLong(),
        parcel.readInt(),
        parcel.readString()!!,
        parcel.readString()!!,
        parcel.readByte() != 0.toByte(),
        parcel.readLong(),
        parcel.readLong(),
        parcel.readLong(),
        parcel.readString()!!,
        parcel.readByte() != 0.toByte(),
        parcel.readByte() != 0.toByte(),
        parcel.readByte() != 0.toByte(),
        parcel.readByte() != 0.toByte(),
        parcel.readByte() != 0.toByte(),
        parcel.readByte() != 0.toByte(),
        parcel.readByte() != 0.toByte(),
        parcel.readLong(),
        parcel.readLong(),
        parcel.readLong(),
        parcel.readByte() != 0.toByte(),
        parcel.readInt(),
        parcel.readInt(),
        parcel.readLong(),
        parcel.readLong(),
        parcel.readInt()
    ) {
    }

    override fun writeToParcel(parcel: Parcel, flags: Int) {
        parcel.writeLong(id)
        parcel.writeInt(userId)
        parcel.writeString(appLimitIndexId)
        parcel.writeString(appPkg)
        parcel.writeByte(if (ifAllDay) 1 else 0)
        parcel.writeLong(startTime)
        parcel.writeLong(endTime)
        parcel.writeLong(limitLength)
        parcel.writeString(title)
        parcel.writeByte(if (sunday) 1 else 0)
        parcel.writeByte(if (monday) 1 else 0)
        parcel.writeByte(if (tuesday) 1 else 0)
        parcel.writeByte(if (wednesday) 1 else 0)
        parcel.writeByte(if (thursday) 1 else 0)
        parcel.writeByte(if (friday) 1 else 0)
        parcel.writeByte(if (saturday) 1 else 0)
        parcel.writeLong(editStartTime)
        parcel.writeLong(editEndTime)
        parcel.writeLong(editMoney)

        parcel.writeByte(if (valid) 1 else 0)
        parcel.writeInt(trend)
        parcel.writeInt(syncState)
        parcel.writeLong(syncTime)
        parcel.writeLong(uuid)
        parcel.writeInt(version)
    }

    override fun describeContents(): Int {
        return 0
    }

    override fun toString(): String {
        return "AppLimit(id=$id, userId=$userId, appLimitIndexId='$appLimitIndexId', appPkg='$appPkg', startTime=$startTime, endTime=$endTime, limitLength=$limitLength, title='$title', sunday=$sunday, monday=$monday, tuesday=$tuesday, wednesday=$wednesday, thursday=$thursday, friday=$friday, saturday=$saturday, editStartTime=$editStartTime, editEndTime=$editEndTime, editMoney=$editMoney, valid=$valid, trend=$trend, syncState=$syncState, syncTime=$syncTime, uuid=$uuid, version=$version)"
    }

    companion object CREATOR : Parcelable.Creator<AppLimit> {
        override fun createFromParcel(parcel: Parcel): AppLimit {
            return AppLimit(parcel)
        }

        override fun newArray(size: Int): Array<AppLimit?> {
            return arrayOfNulls(size)
        }
    }


}



