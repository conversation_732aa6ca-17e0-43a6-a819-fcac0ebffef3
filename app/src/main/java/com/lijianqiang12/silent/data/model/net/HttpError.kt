package com.lijianqiang12.silent.data.model.net

import com.google.gson.JsonParseException
import com.lijianqiang12.silent.data.model.net.pojos.ApiResponse
import com.lijianqiang12.silent.utils.MyToastUtil
import java.net.SocketTimeoutException
import java.util.concurrent.CancellationException

/**
 * 处理请求层的错误,对可能的已知的错误进行处理
 */
fun handlingExceptions(e: Throwable) {
    when (e) {
        is CancellationException -> {
            MyToastUtil.showError("请求取消：" + e.message)
        }
        is SocketTimeoutException -> {
            MyToastUtil.showError("请求超时：" + e.message)
        }
        is JsonParseException -> {
            MyToastUtil.showError("解析错误：" + e.message)
        }
        else -> {
            MyToastUtil.showError("未知错误：" + e.message)
        }
    }
}


// 简单说明:密封类结合when让可能情况都是已知的,代码维护性更高。
sealed class HttpResponse

data class Success<out T>(val data: T) : HttpResponse()
data class Failure(val error: HttpError) : HttpResponse()

data class HttpError(val code: Int, val msg: String)

//enum class HttpError(val code: Int, val msg: String?) {
//    //系统类错误
//    DEFAULT_ERROR(20000, "未捕获的错误"),
//    PARAMS_ERROR(20001, "参数错误"),
//    NOT_LOGIN(20002, "未登录"),
//    TOKEN_ERROR(20003, "token错误"),
//    TIME_ERROR(20004, "系统时间不准"),
//    USER_DENY(20005, "封号"),
//
//}

/**
 * 处理响应层的错误
 */
fun handlingApiExceptions(e: HttpError) {
//    when (e) {
//        HttpError.DEFAULT_ERROR -> {
    MyToastUtil.showInfo(e.msg)
    when (e.code) {
        20002, 20003 -> {//需要重新登录


//            LiveBus.getInstance().with(String::class.java).postValue(LiveBus.CLOSE_ALL_ACTIVITY)
//            ActivityUtils.startActivity(LoginActivity::class.java)

        }
    }
//        }
//        HttpError.PARAMS_ERROR -> {
//            MyToastUtil.showInfo(e.msg)
//        }
//        HttpError.NOT_LOGIN -> {
//            MyToastUtil.showInfo(e.msg)
//        }
//        HttpError.TOKEN_ERROR -> {
//            MyToastUtil.showInfo(e.msg)
//        }
//        HttpError.TIME_ERROR -> {
//            MyToastUtil.showInfo(e.msg)
//        }
//    }
}


/**
 * 处理HttpResponse
 * @param res
 * @param successBlock 成功
 * @param failureBlock 失败
 */
fun <T> handlingHttpResponse(
        res: HttpResponse,
        successBlock: (data: T) -> Unit,
        failureBlock: ((error: HttpError) -> Unit)? = null
) {
    when (res) {
        is Success<*> -> {
            successBlock.invoke(res.data as T)
        }
        is Failure -> {
            with(res) {
                failureBlock?.invoke(error) ?: defaultErrorBlock.invoke(error)
            }
        }
    }
}


// 默认的处理方案
val defaultErrorBlock: (error: HttpError) -> Unit = { error ->
    MyToastUtil.showInfo(error.msg ?: "${error.code}")            // 可以根据是否为debug进行拆分处理
}

fun <T : Any> ApiResponse<T>.convertHttpRes(): HttpResponse {
    return if (this.code == 200) {
        data?.let {
            Success(it)
        } ?: Success(Any())
    } else {
        Failure(HttpError(this.code, this.msg))

//        when (this.code) {
//            20000 -> Failure(HttpError.DEFAULT_ERROR)
//            20001 -> Failure(HttpError.PARAMS_ERROR)
//            20002 -> Failure(HttpError.NOT_LOGIN)
//            20003 -> Failure(HttpError.TOKEN_ERROR)
//            20004 -> Failure(HttpError.TIME_ERROR)
//            20005 -> Failure(HttpError.USER_DENY)
//            else -> Failure(HttpError.DEFAULT_ERROR)
//        }
    }
}

