package com.lijianqiang12.silent.data.viewmodel

import androidx.lifecycle.MutableLiveData
import com.lijianqiang12.silent.data.model.net.*
import com.lijianqiang12.silent.data.model.net.pojos.NetworkState
import com.lijianqiang12.silent.data.model.net.pojos.UserInfo
import com.lijianqiang12.silent.data.model.repository.AccountRepository
import dagger.hilt.android.lifecycle.HiltViewModel
import javax.inject.Inject

@HiltViewModel
class AccountViewModel @Inject constructor(val accountRepository: AccountRepository) : BaseViewModel() {

    private val TAG = "AccountViewModel"

    val userInfoLiveData = MutableLiveData<NetworkState<UserInfo>>()
//    val avatarLiveData = MutableLiveData<NetworkState<Any>>()
//    val usernameLiveData = MutableLiveData<NetworkState<Any>>()
//    val genderLiveData = MutableLiveData<NetworkState<Any>>()
//    val wordLiveData = MutableLiveData<NetworkState<Any>>()
//    val mobileLiveData = MutableLiveData<NetworkState<Any>>()
//    val bindQQLiveData = MutableLiveData<NetworkState<Any>>()
//    val bindWXLiveData = MutableLiveData<NetworkState<Any>>()
//    val bindSINALiveData = MutableLiveData<NetworkState<Any>>()
//
//    val deleteQQLiveData = MutableLiveData<NetworkState<Any>>()
//    val deleteWXLiveData = MutableLiveData<NetworkState<Any>>()
//    val deleteSINALiveData = MutableLiveData<NetworkState<Any>>()
//    val deleteAllAccountLiveData = MutableLiveData<NetworkState<Any>>()


    fun refreshUserInfo(){
        launchOnIO(
                tryBlock = {
                    accountRepository.queryUserInfo().run {
                        handlingHttpResponse<UserInfo>(
                                convertHttpRes(),
                                successBlock = {
                                    userInfoLiveData.postValue(NetworkState(0, it))
                                },
                                failureBlock = { ex ->
                                    userInfoLiveData.postValue(NetworkState(2, null))
                                    handlingApiExceptions(ex)
                                }
                        )
                    }
                },
                // 请求异常处理
                catchBlock = { e ->
                    userInfoLiveData.postValue(NetworkState(2, null))
                    handlingExceptions(e)
                }
        )
    }

//    fun updateAvatar(avatar: String){
//        launchOnIO(
//                tryBlock = {
//                    accountRepository.updateAvatar(avatar).run {
//                        handlingHttpResponse<Any>(
//                                convertHttpRes(),
//                                successBlock = {
//                                    avatarLiveData.postValue(NetworkState(0, it))
//                                },
//                                failureBlock = { ex ->
//                                    avatarLiveData.postValue(NetworkState(2, null))
//                                    handlingApiExceptions(ex)
//                                }
//                        )
//                    }
//                },
//                // 请求异常处理
//                catchBlock = { e ->
//                    avatarLiveData.postValue(NetworkState(2, null))
//                    handlingExceptions(e)
//                }
//        )
//    }
//
//    fun updateUsername(username: String){
//        launchOnIO(
//                tryBlock = {
//                    accountRepository.updateUsername(username).run {
//                        handlingHttpResponse<Any>(
//                                convertHttpRes(),
//                                successBlock = {
//                                    usernameLiveData.postValue(NetworkState(0, it))
//                                },
//                                failureBlock = { ex ->
//                                    usernameLiveData.postValue(NetworkState(2, null))
//                                    handlingApiExceptions(ex)
//                                }
//                        )
//                    }
//                },
//                // 请求异常处理
//                catchBlock = { e ->
//                    usernameLiveData.postValue(NetworkState(2, null))
//                    handlingExceptions(e)
//                }
//        )
//    }
//
//    fun updateGender(gender: String){
//        launchOnIO(
//                tryBlock = {
//                    accountRepository.updateGender(gender).run {
//                        handlingHttpResponse<Any>(
//                                convertHttpRes(),
//                                successBlock = {
//                                    genderLiveData.postValue(NetworkState(0, it))
//                                },
//                                failureBlock = { ex ->
//                                    genderLiveData.postValue(NetworkState(2, null))
//                                    handlingApiExceptions(ex)
//                                }
//                        )
//                    }
//                },
//                // 请求异常处理
//                catchBlock = { e ->
//                    genderLiveData.postValue(NetworkState(2, null))
//                    handlingExceptions(e)
//                }
//        )
//    }
//
//    fun updateWord(word: String){
//        launchOnIO(
//                tryBlock = {
//                    accountRepository.updateWord(word).run {
//                        handlingHttpResponse<Any>(
//                                convertHttpRes(),
//                                successBlock = {
//                                    wordLiveData.postValue(NetworkState(0, it))
//                                },
//                                failureBlock = { ex ->
//                                    wordLiveData.postValue(NetworkState(2, null))
//                                    handlingApiExceptions(ex)
//                                }
//                        )
//                    }
//                },
//                // 请求异常处理
//                catchBlock = { e ->
//                    wordLiveData.postValue(NetworkState(2, null))
//                    handlingExceptions(e)
//                }
//        )
//    }
//
//    fun updateMobile(mobile: String){
//        launchOnIO(
//                tryBlock = {
//                    accountRepository.updateMobile(mobile).run {
//                        handlingHttpResponse<Any>(
//                                convertHttpRes(),
//                                successBlock = {
//                                    mobileLiveData.postValue(NetworkState(0, it))
//                                },
//                                failureBlock = { ex ->
//                                    mobileLiveData.postValue(NetworkState(2, null))
//                                    handlingApiExceptions(ex)
//                                }
//                        )
//                    }
//                },
//                // 请求异常处理
//                catchBlock = { e ->
//                    mobileLiveData.postValue(NetworkState(2, null))
//                    handlingExceptions(e)
//                }
//        )
//    }
//
//    fun bindQQ(qqId: String){
//        launchOnIO(
//                tryBlock = {
//                    accountRepository.bindQQ(qqId).run {
//                        handlingHttpResponse<Any>(
//                                convertHttpRes(),
//                                successBlock = {
//                                    bindQQLiveData.postValue(NetworkState(0, it))
//                                },
//                                failureBlock = { ex ->
//                                    bindQQLiveData.postValue(NetworkState(2, null))
//                                    handlingApiExceptions(ex)
//                                }
//                        )
//                    }
//                },
//                // 请求异常处理
//                catchBlock = { e ->
//                    bindQQLiveData.postValue(NetworkState(2, null))
//                    handlingExceptions(e)
//                }
//        )
//    }
//
//    fun bindWX(wxId: String){
//        launchOnIO(
//                tryBlock = {
//                    accountRepository.bindWX(wxId).run {
//                        handlingHttpResponse<Any>(
//                                convertHttpRes(),
//                                successBlock = {
//                                    bindWXLiveData.postValue(NetworkState(0, it))
//                                },
//                                failureBlock = { ex ->
//                                    bindWXLiveData.postValue(NetworkState(2, null))
//                                    handlingApiExceptions(ex)
//                                }
//                        )
//                    }
//                },
//                // 请求异常处理
//                catchBlock = { e ->
//                    bindWXLiveData.postValue(NetworkState(2, null))
//                    handlingExceptions(e)
//                }
//        )
//    }
//
//    fun bindSINA(sinaId: String){
//        launchOnIO(
//                tryBlock = {
//                    accountRepository.bindSINA(sinaId).run {
//                        handlingHttpResponse<Any>(
//                                convertHttpRes(),
//                                successBlock = {
//                                    bindSINALiveData.postValue(NetworkState(0, it))
//                                },
//                                failureBlock = { ex ->
//                                    bindSINALiveData.postValue(NetworkState(2, null))
//                                    handlingApiExceptions(ex)
//                                }
//                        )
//                    }
//                },
//                // 请求异常处理
//                catchBlock = { e ->
//                    bindSINALiveData.postValue(NetworkState(2, null))
//                    handlingExceptions(e)
//                }
//        )
//    }
//
//    fun deleteQQ(){
//        launchOnIO(
//                tryBlock = {
//                    accountRepository.deleteQQ().run {
//                        handlingHttpResponse<Any>(
//                                convertHttpRes(),
//                                successBlock = {
//                                    deleteQQLiveData.postValue(NetworkState(0, it))
//                                },
//                                failureBlock = { ex ->
//                                    deleteQQLiveData.postValue(NetworkState(2, null))
//                                    handlingApiExceptions(ex)
//                                }
//                        )
//                    }
//                },
//                // 请求异常处理
//                catchBlock = { e ->
//                    deleteQQLiveData.postValue(NetworkState(2, null))
//                    handlingExceptions(e)
//                }
//        )
//    }
//
//    fun deleteWX(){
//        launchOnIO(
//                tryBlock = {
//                    accountRepository.deleteWX().run {
//                        handlingHttpResponse<Any>(
//                                convertHttpRes(),
//                                successBlock = {
//                                    deleteWXLiveData.postValue(NetworkState(0, it))
//                                },
//                                failureBlock = { ex ->
//                                    deleteWXLiveData.postValue(NetworkState(2, null))
//                                    handlingApiExceptions(ex)
//                                }
//                        )
//                    }
//                },
//                // 请求异常处理
//                catchBlock = { e ->
//                    deleteWXLiveData.postValue(NetworkState(2, null))
//                    handlingExceptions(e)
//                }
//        )
//    }
//
//    fun deleteSINA(){
//        launchOnIO(
//                tryBlock = {
//                    accountRepository.deleteSINA().run {
//                        handlingHttpResponse<Any>(
//                                convertHttpRes(),
//                                successBlock = {
//                                    deleteSINALiveData.postValue(NetworkState(0, it))
//                                },
//                                failureBlock = { ex ->
//                                    deleteSINALiveData.postValue(NetworkState(2, null))
//                                    handlingApiExceptions(ex)
//                                }
//                        )
//                    }
//                },
//                // 请求异常处理
//                catchBlock = { e ->
//                    deleteSINALiveData.postValue(NetworkState(2, null))
//                    handlingExceptions(e)
//                }
//        )
//    }
//
//    fun deleteAllAccount(){
//        launchOnIO(
//                tryBlock = {
//                    accountRepository.deleteAllAccount().run {
//                        handlingHttpResponse<Any>(
//                                convertHttpRes(),
//                                successBlock = {
//                                    deleteAllAccountLiveData.postValue(NetworkState(0, it))
//                                },
//                                failureBlock = { ex ->
//                                    deleteAllAccountLiveData.postValue(NetworkState(2, null))
//                                    handlingApiExceptions(ex)
//                                }
//                        )
//                    }
//                },
//                // 请求异常处理
//                catchBlock = { e ->
//                    deleteAllAccountLiveData.postValue(NetworkState(2, null))
//                    handlingExceptions(e)
//                }
//        )
//    }

}