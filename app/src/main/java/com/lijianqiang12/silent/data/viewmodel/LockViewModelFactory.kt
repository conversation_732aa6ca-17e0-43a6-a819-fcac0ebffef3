package com.lijianqiang12.silent.data.viewmodel

import androidx.lifecycle.ViewModel
import androidx.lifecycle.ViewModelProvider
import com.lijianqiang12.silent.data.model.repository.LockRepository

class LockViewModelFactory(private val lockRepository: LockRepository) : ViewModelProvider.NewInstanceFactory() {

    override fun <T : ViewModel> create(modelClass: Class<T>): T {
        return LockViewModel(lockRepository) as T
    }

}