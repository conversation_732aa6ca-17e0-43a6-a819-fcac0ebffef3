package com.lijianqiang12.silent.di

import android.content.Context
import com.lijianqiang12.silent.data.model.db.AppDatabase
import dagger.Module
import dagger.Provides
import dagger.hilt.InstallIn
import dagger.hilt.android.qualifiers.ApplicationContext
import dagger.hilt.components.SingletonComponent
import javax.inject.Singleton

/**
 * Hilt模块：提供数据库相关的依赖注入
 * 替换原有的手动依赖注入方式
 */
@Module
@InstallIn(SingletonComponent::class)
object DatabaseModule {

    /**
     * 提供Room数据库实例
     */
    @Provides
    @Singleton
    fun provideAppDatabase(@ApplicationContext context: Context): AppDatabase {
        return AppDatabase.getInstance(context)
    }

    /**
     * 提供各种DAO实例
     */
    @Provides
    fun provideFastDao(database: AppDatabase) = database.fastDao()

    @Provides
    fun provideTomatoDao(database: AppDatabase) = database.tomatoDao()

    @Provides
    fun provideScheduleDao(database: AppDatabase) = database.scheduleDao()

    @Provides
    fun provideWhiteAppDao(database: AppDatabase) = database.whiteAppDao()

    @Provides
    fun provideLockHistoryDao(database: AppDatabase) = database.lockHistoryDao()

    @Provides
    fun provideDayLimitDao(database: AppDatabase) = database.dayLimitDao()

    @Provides
    fun provideAppLimitDao(database: AppDatabase) = database.appLimitDao()

    @Provides
    fun provideAppUsageDao(database: AppDatabase) = database.appUsageDao()
}
