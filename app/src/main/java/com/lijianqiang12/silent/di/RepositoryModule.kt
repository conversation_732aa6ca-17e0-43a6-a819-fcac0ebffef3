package com.lijianqiang12.silent.di

import android.content.Context
import com.lijianqiang12.silent.data.model.db.*
import com.lijianqiang12.silent.data.model.repository.*
import dagger.Module
import dagger.Provides
import dagger.hilt.InstallIn
import dagger.hilt.android.qualifiers.ApplicationContext
import dagger.hilt.components.SingletonComponent
import javax.inject.Singleton

/**
 * Hilt模块：提供Repository相关的依赖注入
 * 替换原有的InjectorUtils手动创建方式
 */
@Module
@InstallIn(SingletonComponent::class)
object RepositoryModule {

    @Provides
    @Singleton
    fun provideLockRepository(
        fastDao: FastDao,
        tomatoDao: TomatoDao,
        scheduleDao: ScheduleDao,
        whiteAppDao: WhiteAppDao,
        lockHistoryDao: LockHistoryDao
    ): LockRepository {
        return LockRepository.getInstance(fastDao, tomatoDao, scheduleDao, whiteAppDao, lockHistoryDao)
    }

    @Provides
    @Singleton
    fun provideDayLimitRepository(
        dayLimitDao: DayLimitDao,
        @ApplicationContext context: Context
    ): DayLimitRepository {
        return DayLimitRepository.getInstance(dayLimitDao, context)
    }

    @Provides
    @Singleton
    fun provideAppLimitRepository(
        appLimitDao: AppLimitDao
    ): AppLimitRepository {
        return AppLimitRepository.getInstance(appLimitDao)
    }

    @Provides
    @Singleton
    fun provideUsageRepository(
        @ApplicationContext context: Context,
        appUsageDao: AppUsageDao
    ): UsageRepository {
        return UsageRepository.getInstance(context, appUsageDao)
    }

    @Provides
    @Singleton
    fun provideOffTimeRepository(
        @ApplicationContext context: Context
    ): OffTimeRepository {
        return OffTimeRepository.getInstance(context)
    }

    @Provides
    @Singleton
    fun provideRoomRepository(
        @ApplicationContext context: Context
    ): RoomRepository {
        return RoomRepository.getInstance(context)
    }

    @Provides
    @Singleton
    fun provideLoginRepository(
        @ApplicationContext context: Context
    ): LoginRepository {
        return LoginRepository.getInstance(context)
    }

    @Provides
    @Singleton
    fun provideVIPRepository(
        @ApplicationContext context: Context
    ): VIPRepository {
        return VIPRepository.getInstance(context)
    }

    @Provides
    @Singleton
    fun provideAccountRepository(
        @ApplicationContext context: Context
    ): AccountRepository {
        return AccountRepository.getInstance(context)
    }
}
