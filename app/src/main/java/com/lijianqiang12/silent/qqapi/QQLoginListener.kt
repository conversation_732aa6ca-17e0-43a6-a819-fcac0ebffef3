package com.lijianqiang12.silent.qqapi

import com.blankj.utilcode.util.GsonUtils
import com.blankj.utilcode.util.LogUtils
import com.blankj.utilcode.util.ToastUtils
import com.google.gson.reflect.TypeToken
import com.jeremyliao.liveeventbus.LiveEventBus
import com.lijianqiang12.silent.BuildConfig
import com.lijianqiang12.silent.LiveBus
import com.lijianqiang12.silent.MyConstants
import com.lijianqiang12.silent.data.model.net.BaseRetrofitClient
import com.lijianqiang12.silent.data.model.net.CommonInterceptor
import com.lijianqiang12.silent.data.model.net.pojos.AllQQUserInfo
import com.lijianqiang12.silent.data.model.net.pojos.QQTokenModel
import com.lijianqiang12.silent.data.model.net.pojos.QQUserInfo
import com.lijianqiang12.silent.utils.MyToastUtil
import com.tencent.tauth.IUiListener
import com.tencent.tauth.UiError
import okhttp3.*
import okhttp3.logging.HttpLoggingInterceptor
import java.io.IOException
import java.util.concurrent.TimeUnit

class QQLoginListener : IUiListener {
    override fun onComplete(result: Any?) {
        LogUtils.d("BaseUiListener onComplete $result")
        result?.let {
            val json = it.toString()
            val qqTokenModel = GsonUtils.fromJson<QQTokenModel>(json, object : TypeToken<QQTokenModel>() {}.type)
            LogUtils.d("qqTokenModel.access_token=${qqTokenModel.access_token} qqTokenModel.openid=${qqTokenModel.openid}}")
            getUserInfo(qqTokenModel.access_token, qqTokenModel.openid)
        }
    }

    private fun getUserInfo(accessToken: String, openid: String) {

        val logging = HttpLoggingInterceptor()
        if (BuildConfig.DEBUG) {
            logging.level = HttpLoggingInterceptor.Level.BODY
        } else {
            logging.level = HttpLoggingInterceptor.Level.BASIC
        }


        val client = OkHttpClient()
        val request = Request.Builder()
            .url("https://graph.qq.com/user/get_user_info?access_token=${accessToken}&openid=${openid}&appid=${MyConstants.QQ_APP_ID}")
            .build()
        client.newCall(request).enqueue(object : Callback {
            override fun onFailure(call: Call, e: IOException) {
                MyToastUtil.showInfo("登录失败：$e")
            }

            override fun onResponse(call: Call, response: Response) {

                if (response.code == 200) {
                    val json = response.body?.string()
                    val qqUserInfo = GsonUtils.fromJson<QQUserInfo>(json, object : TypeToken<QQUserInfo>() {}.type)
                    LogUtils.d("QQEntryActivity qqUserInfo=${qqUserInfo}")

                    LiveEventBus.get(LiveBus.QQ_USER_INFO, AllQQUserInfo::class.java).post(AllQQUserInfo(openid, qqUserInfo))
                } else {
                    MyToastUtil.showInfo("登录失败：${response.message}")
                }
            }
        })
    }

    override fun onError(p0: UiError?) {
        ToastUtils.showLong("登录失败：${p0?.errorMessage}")
    }

    override fun onCancel() {
        ToastUtils.showLong("登录取消")
    }

    override fun onWarning(p0: Int) {
        ToastUtils.showLong("登录警告：$p0")
    }

}
