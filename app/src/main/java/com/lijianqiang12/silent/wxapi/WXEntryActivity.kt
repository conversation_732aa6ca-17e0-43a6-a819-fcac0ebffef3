package com.lijianqiang12.silent.wxapi

import androidx.appcompat.app.AppCompatActivity
import android.content.Intent
import android.os.Bundle
import com.blankj.utilcode.util.GsonUtils
import com.blankj.utilcode.util.LogUtils
import com.google.gson.reflect.TypeToken
import com.jeremyliao.liveeventbus.LiveEventBus
import com.lijianqiang12.silent.LiveBus
import com.lijianqiang12.silent.MyConstants
import com.lijianqiang12.silent.data.model.net.pojos.WXTokenModel
import com.lijianqiang12.silent.data.model.net.pojos.WXUserInfo
import com.lijianqiang12.silent.data.viewmodel.LoginViewModel
import com.lijianqiang12.silent.utils.MyToastUtil
import dagger.hilt.android.AndroidEntryPoint
import javax.inject.Inject
import com.tencent.mm.opensdk.constants.ConstantsAPI
import com.tencent.mm.opensdk.modelbase.BaseReq
import com.tencent.mm.opensdk.modelbase.BaseResp
import com.tencent.mm.opensdk.modelmsg.SendAuth
import com.tencent.mm.opensdk.openapi.IWXAPI
import com.tencent.mm.opensdk.openapi.IWXAPIEventHandler
import com.tencent.mm.opensdk.openapi.WXAPIFactory
import okhttp3.Call
import okhttp3.Callback
import okhttp3.OkHttpClient
import okhttp3.Request
import okhttp3.Response
import java.io.IOException

@AndroidEntryPoint
class WXEntryActivity : Activity(), IWXAPIEventHandler {

    private var api: IWXAPI? = null

    @Inject
    lateinit var loginViewModel: LoginViewModel

    public override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        // LoginViewModel 现在通过 Hilt 自动注入

        api = WXAPIFactory.createWXAPI(this, MyConstants.WX_APP_ID)
        api!!.handleIntent(intent, this)

    }

    override fun onNewIntent(intent: Intent) {
        super.onNewIntent(intent)
        setIntent(intent)
        api!!.handleIntent(intent, this)
    }

    override fun onReq(req: BaseReq) {}

    override fun onResp(resp: BaseResp) {
        LogUtils.d("WXEntryActivity ${resp.errCode} ${resp.errStr} ${resp.type} ${resp.openId} ${resp.transaction} ${resp.checkArgs()}")
//        MyToastUtil.showInfo("WXEntryActivity ${resp.errCode} ${resp.errStr} ${resp.type} ${resp.openId} ${resp.transaction} ${resp.checkArgs()}")
        if (resp.type == ConstantsAPI.COMMAND_SENDAUTH) {
            if (resp.errCode != 0) {
                MyToastUtil.showInfo("登录失败：${resp.errStr}")
                finish()
            } else {
                val sendAuthResp = resp as SendAuth.Resp
                val code = sendAuthResp.code
                getAccessToken(code)


//                when (MMKVUtils.getInt(MyConstants.SP_KEY_PAY_TYPE, 0)) {
//                    0 -> {
//                        LiveEventBus.get(LiveBus.PAY_FOR_VIP_SUCCEED, String::class.java).post("")
//                    }
//                    1 -> {
//                        LiveEventBus.get(LiveBus.USER_FORCE_UNLOCK, String::class.java).post("payFinish")
//                    }
//                    2 -> {
//                        LiveEventBus.get(LiveBus.PAY_FOR_APP_LIMIT_SUCCEED, String::class.java).post("")
//                    }
//                }
//                finish()
            }
        }
    }

    private fun getAccessToken(code: String) {
        val client = OkHttpClient()
        val request = Request.Builder()
            .url("https://api.weixin.qq.com/sns/oauth2/access_token?appid=${MyConstants.WX_APP_ID}&secret=${MyConstants.WX_APP_SECRET}&code=${code}&grant_type=authorization_code")
            .build()
        client.newCall(request).enqueue(object : Callback {
            override fun onFailure(call: Call, e: IOException) {
                MyToastUtil.showInfo("登录失败：${e.toString()}")
                finish()
            }

            override fun onResponse(call: Call, response: Response) {

                if (response.code == 200) {
//                    MyToastUtil.showInfo("response.body()= ${response.body()?.toString()}")
                    val json = response.body?.string()
                    val wxTokenModel = GsonUtils.fromJson<WXTokenModel>(json, object : TypeToken<WXTokenModel>() {}.type)
                    LogUtils.d("WXEntryActivity wxTokenModel.access_token=${wxTokenModel.access_token} wxTokenModel.openid=${wxTokenModel.openid}}")
//                    MyToastUtil.showInfo("WXEntryActivity wxTokenModel.access_token=${wxTokenModel.access_token} wxTokenModel.openid=${wxTokenModel.openid}}")

                    getUserInfo(wxTokenModel.access_token, wxTokenModel.openid)

                } else {
                    MyToastUtil.showInfo("登录失败：${response.message}")
                    finish()
                }
            }
        })
    }


    private fun getUserInfo(access_token: String, openid: String) {
        val client = OkHttpClient()
        val request = Request.Builder()
            .url("https://api.weixin.qq.com/sns/userinfo?access_token=${access_token}&openid=${openid}")
            .build()
        client.newCall(request).enqueue(object : Callback {
            override fun onFailure(call: Call, e: IOException) {
                MyToastUtil.showInfo("登录失败：${e.toString()}")
                finish()
            }

            override fun onResponse(call: Call, response: Response) {

                if (response.code == 200) {

                    val json = response.body?.string()
                    val wxUserInfo = GsonUtils.fromJson<WXUserInfo>(json, object : TypeToken<WXUserInfo>() {}.type)
                    LogUtils.d("WXEntryActivity wxUserInfo=${wxUserInfo}")

                    LiveEventBus.get(LiveBus.WX_USER_INFO, WXUserInfo::class.java).post(wxUserInfo)
                    finish()

                } else {
                    MyToastUtil.showInfo("登录失败：${response.message}")
                    finish()
                }
            }
        })
    }



}