package com.lijianqiang12.silent.wxapi

import android.annotation.SuppressLint
import android.content.Intent
import android.os.Bundle
import com.lijianqiang12.silent.utils.MMKVUtils
import com.jeremyliao.liveeventbus.LiveEventBus
import com.lijianqiang12.silent.LiveBus
import com.lijianqiang12.silent.MyConstants
import com.lijianqiang12.silent.component.activity.base.BaseActivity
import com.lijianqiang12.silent.data.viewmodel.LoginViewModel
import com.lijianqiang12.silent.data.viewmodel.VIPViewModel
import com.tencent.mm.opensdk.constants.ConstantsAPI
import com.tencent.mm.opensdk.modelbase.BaseReq
import com.tencent.mm.opensdk.modelbase.BaseResp
import com.tencent.mm.opensdk.openapi.IWXAPI
import com.tencent.mm.opensdk.openapi.IWXAPIEventHandler
import com.tencent.mm.opensdk.openapi.WXAPIFactory
import dagger.hilt.android.AndroidEntryPoint
import javax.inject.Inject

@AndroidEntryPoint
class WXPayEntryActivity : BaseActivity(), IWXAPIEventHandler {

    private var api: IWXAPI? = null

    @Inject
    lateinit var viewModel: VIPViewModel

    @Inject
    lateinit var loginViewModel: LoginViewModel


    public override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        // ViewModels 现在通过 Hilt 自动注入

        api = WXAPIFactory.createWXAPI(this, MyConstants.WX_APP_ID)
        api!!.handleIntent(intent, this)

    }

    override fun onNewIntent(intent: Intent) {
        super.onNewIntent(intent)
        setIntent(intent)
        api!!.handleIntent(intent, this)
    }

    override fun onReq(req: BaseReq) {}

    @SuppressLint("CheckResult")
    override fun onResp(resp: BaseResp) {
        if (resp.type == ConstantsAPI.COMMAND_PAY_BY_WX) {
            if (resp.errCode == -2) {
                when (MMKVUtils.getInt(MyConstants.SP_KEY_PAY_TYPE, 0)) {
                    0 -> {
                        LiveEventBus.get(LiveBus.PAY_FOR_VIP_SUCCEED, Boolean::class.java).post(false)
                    }
                    1 -> {
                        LiveEventBus.get(LiveBus.USER_FORCE_UNLOCK, String::class.java).post("payFailed")
                    }
                    2 -> {
                        LiveEventBus.get(LiveBus.PAY_FOR_APP_LIMIT_SUCCEED, Boolean::class.java).post(false)
                    }
                }
                finish()
            } else {
                when (MMKVUtils.getInt(MyConstants.SP_KEY_PAY_TYPE, 0)) {
                    0 -> {
                        LiveEventBus.get(LiveBus.PAY_FOR_VIP_SUCCEED, Boolean::class.java).post(true)
                    }

                    1 -> {
                        LiveEventBus.get(LiveBus.USER_FORCE_UNLOCK, String::class.java).post("payFinish")
                    }

                    2 -> {
                        LiveEventBus.get(LiveBus.PAY_FOR_APP_LIMIT_SUCCEED, Boolean::class.java).post(true)
                    }
                }
                finish()
            }
        }
    }
}
