package com.lijianqiang12.silent

//import com.yl.lib.sentry.hook.PrivacyResultCallBack
//import com.yl.lib.sentry.hook.PrivacySentry
//import com.yl.lib.sentry.hook.PrivacySentryBuilder

import android.accessibilityservice.AccessibilityService
import android.app.Application
import android.content.ContentResolver
import android.content.Context
import android.os.Build
import android.provider.Settings
import android.util.Log
import android.view.Gravity
import androidx.annotation.Keep
import com.blankj.utilcode.util.LogUtils
import com.blankj.utilcode.util.ToastUtils
import com.blankj.utilcode.util.Utils
import com.danikula.videocache.HttpProxyCacheServer
import com.danikula.videocache.file.DiskUsage
import com.hjq.permissions.XXPermissions
import com.jeremyliao.liveeventbus.LiveEventBus
import com.lijianqiang12.silent.component.service.background_service.RunState
import com.lijianqiang12.silent.component.service.background_service.WorkState
import com.lijianqiang12.silent.utils.MMKVUtils
import com.lijianqiang12.silent.utils.PermissionInterceptor
import com.lijianqiang12.silent.utils.SP_NAME
import com.lijianqiang12.silent.utils.getChannelName
import com.tencent.bugly.crashreport.CrashReport
import com.tencent.mmkv.MMKV
import com.yl.lib.privacy_annotation.MethodInvokeOpcode
import com.yl.lib.privacy_annotation.PrivacyClassProxy
import com.yl.lib.privacy_annotation.PrivacyMethodProxy
import com.yl.lib.sentry.hook.PrivacyResultCallBack
import com.yl.lib.sentry.hook.PrivacySentry
import com.yl.lib.sentry.hook.PrivacySentryBuilder
import com.yl.lib.sentry.hook.util.PrivacyProxyUtil.Util.doFilePrinter
//import io.sentry.android.core.SentryAndroid
//import io.sentry.android.core.SentryAndroidOptions
import kotlinx.coroutines.sync.Mutex
import java.io.File
import java.util.*


fun initInitial() {
    if (TheApplication.getInstance().init) {
        return
    }

    TheApplication.getInstance().init = true

    Utils.init(TheApplication.getInstance())

    if (!MMKVUtils.contains(MyConstants.SP_KEY_UUID)) {
        val uniqueID = UUID.randomUUID().toString()
        MMKVUtils.put(MyConstants.SP_KEY_UUID, uniqueID)
    }

    //4.9.9.7.5版本一些设置由bool改为int012
    if (MMKVUtils.contains(MyConstants.SP_SETTING_DENY_ASSIST)) {
        val oldDenyAssistant = MMKVUtils.getBoolean(MyConstants.SP_SETTING_DENY_ASSIST, false)
        MMKVUtils.remove(MyConstants.SP_SETTING_DENY_ASSIST)
        MMKVUtils.put(MyConstants.SP_SETTING_DENY_ASSIST_NEW, if (oldDenyAssistant) 1 else 0)
    }
    if (MMKVUtils.contains(MyConstants.SP_SETTING_DENY_SHUTDOWN)) {
        val oldDenyAssistant = MMKVUtils.getBoolean(MyConstants.SP_SETTING_DENY_SHUTDOWN, false)
        MMKVUtils.remove(MyConstants.SP_SETTING_DENY_SHUTDOWN)
        MMKVUtils.put(MyConstants.SP_SETTING_DENY_SHUTDOWN_NEW, if (oldDenyAssistant) 1 else 0)
    }
    if (MMKVUtils.contains(MyConstants.SP_SETTING_DENY_EDIT_BUTTON)) {
        val oldDenyAssistant = MMKVUtils.getBoolean(MyConstants.SP_SETTING_DENY_EDIT_BUTTON, false)
        MMKVUtils.remove(MyConstants.SP_SETTING_DENY_EDIT_BUTTON)
        MMKVUtils.put(MyConstants.SP_SETTING_DENY_EDIT_BUTTON_NEW, if (oldDenyAssistant) 1 else 0)
    }
    if (MMKVUtils.contains(MyConstants.SP_SETTING_DENY_CLOSE_RUNNING_SERVICE)) {
        val oldDenyAssistant = MMKVUtils.getBoolean(MyConstants.SP_SETTING_DENY_CLOSE_RUNNING_SERVICE, false)
        MMKVUtils.remove(MyConstants.SP_SETTING_DENY_CLOSE_RUNNING_SERVICE)
        MMKVUtils.put(MyConstants.SP_SETTING_DENY_CLOSE_RUNNING_SERVICE_NEW, if (oldDenyAssistant) 1 else 0)
    }
    if (MMKVUtils.contains(MyConstants.SP_SETTING_DENY_DROPDOWN)) {
        val oldDenyAssistant = MMKVUtils.getBoolean(MyConstants.SP_SETTING_DENY_DROPDOWN, false)
        MMKVUtils.remove(MyConstants.SP_SETTING_DENY_DROPDOWN)
        MMKVUtils.put(MyConstants.SP_SETTING_DENY_DROPDOWN_NEW, if (oldDenyAssistant) 2 else 0)
    }

    //4.9.9.8.3版本因网信办要求，改为不登录也能使用
//    if (!MMKVUtils.contains(MyConstants.SP_KEY_ALLOW_NO_USER)) {
//        MMKVUtils.put(MyConstants.SP_KEY_ALLOW_NO_USER, true)
//    }

    val userId = MMKVUtils.getInt(MyConstants.SP_KEY_USER_ID, -1)
    if (MMKVUtils.contains(MyConstants.SP_KEY_LATEST_PULL_TIME_WHITE_APP)) {
        val oldData = MMKVUtils.getLong(MyConstants.SP_KEY_LATEST_PULL_TIME_WHITE_APP, 0L)
        MMKVUtils.remove(MyConstants.SP_KEY_LATEST_PULL_TIME_WHITE_APP)
        MMKVUtils.put(MyConstants.SP_KEY_LATEST_PULL_TIME_WHITE_APP + "$userId", oldData)
    }

    if (MMKVUtils.contains(MyConstants.SP_KEY_LATEST_PULL_TIME_FAST)) {
        val oldData = MMKVUtils.getLong(MyConstants.SP_KEY_LATEST_PULL_TIME_FAST, 0L)
        MMKVUtils.remove(MyConstants.SP_KEY_LATEST_PULL_TIME_FAST)
        MMKVUtils.put(MyConstants.SP_KEY_LATEST_PULL_TIME_FAST + "$userId", oldData)
    }

    if (MMKVUtils.contains(MyConstants.SP_KEY_LATEST_PULL_TIME_TOMATO)) {
        val oldData = MMKVUtils.getLong(MyConstants.SP_KEY_LATEST_PULL_TIME_TOMATO, 0L)
        MMKVUtils.remove(MyConstants.SP_KEY_LATEST_PULL_TIME_TOMATO)
        MMKVUtils.put(MyConstants.SP_KEY_LATEST_PULL_TIME_TOMATO + "$userId", oldData)
    }

    if (MMKVUtils.contains(MyConstants.SP_KEY_LATEST_PULL_TIME_SCHEDULE)) {
        val oldData = MMKVUtils.getLong(MyConstants.SP_KEY_LATEST_PULL_TIME_SCHEDULE, 0L)
        MMKVUtils.remove(MyConstants.SP_KEY_LATEST_PULL_TIME_SCHEDULE)
        MMKVUtils.put(MyConstants.SP_KEY_LATEST_PULL_TIME_SCHEDULE + "$userId", oldData)
    }

    if (MMKVUtils.contains(MyConstants.SP_KEY_LATEST_PULL_TIME_APP_LIMIT)) {
        val oldData = MMKVUtils.getLong(MyConstants.SP_KEY_LATEST_PULL_TIME_APP_LIMIT, 0L)
        MMKVUtils.remove(MyConstants.SP_KEY_LATEST_PULL_TIME_APP_LIMIT)
        MMKVUtils.put(MyConstants.SP_KEY_LATEST_PULL_TIME_APP_LIMIT + "$userId", oldData)
    }


    val channel = getChannelName()

//    CondomProcess.installExceptDefaultProcess(TheApplication.getInstance())

//    ShareTrace.disableClipboard()
//    ShareTrace.init(TheApplication.getInstance())


    XXPermissions.setInterceptor(PermissionInterceptor())

    ToastUtils.make().setGravity(Gravity.CENTER, 0, 0)
    LiveEventBus.config().lifecycleObserverAlwaysActive(true).enableLogger(true)

//    UMConfigure.setLogEnabled(AppUtils.isAppDebug())
//    UMConfigure.init(
//        TheApplication.getInstance(),
//        MyConstants.UMENG_APP_KEY,
//        channel,
//        UMConfigure.DEVICE_TYPE_PHONE,
//        MyConstants.UMENG_APP_SECRET
//    )
//    if (!MMKVUtils.contains(MyConstants.SP_KEY_OAID)) {
//        UMConfigure.getOaid(TheApplication.getInstance()) { oaid ->
//            if (oaid != null) {
//                MMKVUtils.put(MyConstants.SP_KEY_OAID, oaid)
//            }
//        }
//    }
//    MobclickAgent.setPageCollectionMode(MobclickAgent.PageMode.AUTO)
//
//    PlatformConfig.setWeixin(MyConstants.WX_APP_ID, MyConstants.WX_APP_SECRET)
//    PlatformConfig.setQQZone(MyConstants.QQ_APP_ID, MyConstants.QQ_APP_SECRET)
//    PlatformConfig.setSinaWeibo(MyConstants.SINA_APP_ID, MyConstants.SINA_APP_SECRET, MyConstants.SINA_REDIRECT_URL)
//    PlatformConfig.setQQFileProvider("${AppUtils.getAppPackageName()}.fileprovider")
//    PlatformConfig.setWXFileProvider("${AppUtils.getAppPackageName()}.fileprovider")
//    PlatformConfig.setSinaFileProvider("${AppUtils.getAppPackageName()}.fileprovider")

    //bugly
    CrashReport.initCrashReport(TheApplication.getInstance(), "074c54d7d0", BuildConfig.DEBUG)
    CrashReport.setDeviceId(TheApplication.getInstance(), MMKVUtils.getString(MyConstants.SP_KEY_UUID, "unknownUuid"))
    CrashReport.setDeviceModel(TheApplication.getInstance(), "${Build.BRAND}-${Build.MODEL}")
    CrashReport.setAppChannel(TheApplication.getInstance(), channel)
    CrashReport.setIsDevelopmentDevice(TheApplication.getInstance(), BuildConfig.DEBUG)

//    SentryAndroid.init(TheApplication.getInstance()) { options: SentryAndroidOptions ->
//        options.dsn = "https://<EMAIL>/2"
////        options.isDebug = BuildConfig.DEBUG
////        options.setDiagnosticLevel(SentryLevel.DEBUG)
//    }
}

data class PageFromAccessibility(val pkg: String, val page: String, val time: Long)
class GlobalParams {

    //作为辅助判断当前页面
//    var currentPageListFromAccessibility = CopyOnWriteArrayList<PageFromAccessibility>()

    @Volatile
    var workState = WorkState.UNKNOWN//是否在工作状态

    @Volatile
    var isRemoveNotify = false//是否移除通知

//    @Volatile
//    var isDenyDropDown = false//是否禁止下拉菜单栏

    @Volatile
    var runState = RunState.UNKNOWN//当前处于什么状态

    @Volatile
    var lastClickHomeTime = 0L//上次点击home键的时间，单位ms，用于显示5秒延迟提示框

//    @Volatile
//    var lastClickHomeOrRecent = 0L//上次点击home或recent键的时间，单位ms，用于防止按home等立刻锁机后会有的闪烁问题

    @Volatile
    var checkServiceInitOk = false//初始化结束

    @Volatile
    var isReverse = false

    @Volatile
    var lastMinute = -1

    @Volatile
    var currentRunningLockWorkInfo = ""

    @Volatile
    var accessibilityService: AccessibilityService? = null

    val createLockHistoryMutex = Mutex()
}

class TheApplication : Application() {

    companion object {

        @Volatile
        private var INSTANCE: Application? = null

        fun getInstance(): TheApplication {
            return INSTANCE as TheApplication
        }

        fun getProxy(context: Context): HttpProxyCacheServer {
            val app = context.applicationContext as TheApplication
            if (app.proxy == null) {
                app.proxy = app.newProxy()
            }
            return app.proxy!!
        }
    }

    @Volatile
    var init = false

    private var proxy: HttpProxyCacheServer? = null
    val globalParams = GlobalParams()

    private fun newProxy(): HttpProxyCacheServer {
        return HttpProxyCacheServer.Builder(this)
            .cacheDirectory(File(this.applicationContext.filesDir.absolutePath + "/whiteNoise/"))
            .diskUsage(MyDiskUsage())
            .build()
    }

    class MyDiskUsage : DiskUsage {
        override fun touch(file: File?) {

        }
    }


    override fun onCreate() {
        super.onCreate()
        INSTANCE = this
        LogUtils.d("MyApp onCreate")

        val dir = filesDir.absolutePath + "mmkv"
        MMKV.initialize(this, dir)

        //把未加密的改成加密的
        val lastMMKV = MMKV.mmkvWithID(MMKVUtils.MMKV_ID, MMKV.SINGLE_PROCESS_MODE, null)
        if (lastMMKV.allKeys() != null && lastMMKV.allKeys()!!.isNotEmpty()) {
            lastMMKV.reKey(MMKVUtils.cryptKey)
        }

        lastMMKV.close()

        if (getSharedPreferences(SP_NAME, MODE_PRIVATE).getBoolean(MyConstants.SP_AGREE_PRIVACY, false)) {
            //sp中已经记录了同意隐私，因此是已经同意过隐私并且还未转换成mmkv的，应该转换，无需每次打开都转换一下
            MMKVUtils.importSharedPreferences()
        }


//        UMConfigure.preInit(this, MyConstants.UMENG_APP_KEY, getChannelName())
    }

    override fun attachBaseContext(base: Context?) {
        super.attachBaseContext(base)
        Log.d("MyApplication", "attachBaseContext")

        //完成功能的初始化
        val builder = PrivacySentryBuilder()
            // 自定义文件结果的输出名
            .configResultFileName("buyer_privacy")
            //  debug打开，可以看到logcat的堆栈日志
            .syncDebug(BuildConfig.DEBUG)
            // 配置写入文件日志 , 线上包这个开关不要打开！！！！，true打开文件输入，false关闭文件输入
            .enableFileResult(BuildConfig.DEBUG)
            // 持续写入文件30分钟
            .configWatchTime(30 * 60 * 1000)
            // 文件输出后的回调
            .configResultCallBack(object : PrivacyResultCallBack {
                override fun onResultCallBack(filePath: String) {
                    LogUtils.d("PrivacySentry", "onResultCallBack: $filePath")
                }
            })

        // 添加默认结果输出，包含log输出和文件输出
        PrivacySentry.Privacy.init(this, builder)

    }


}

/**
 * <AUTHOR>
 * @since 2022-01-13 17:57
 * 主要是两个注解PrivacyClassProxy和PrivacyMethodProxy，PrivacyClassProxy代表要解析的类，PrivacyMethodProxy代表要hook的方法配置
 */
@Keep
open class PrivacyProxyResolver {

    // kotlin里实际解析的是这个PrivacyProxyCall$Proxy 内部类
    @PrivacyClassProxy
    @Keep
    object Proxy {

        // 查询
        @PrivacyMethodProxy(
            originalClass = Settings.Secure::class,   // hook的方法所在的类名
            originalMethod = "getString",   // hook的方法名
            originalOpcode = MethodInvokeOpcode.INVOKESTATIC //hook的方法调用，一般是静态调用和实例调用
        )
        @JvmStatic
        fun getString(
            contentResolver: ContentResolver?, //实例调用的方法需要把声明调用对象，我们默认把对象参数放在第一位
            string: String,
        ): String {
            doFilePrinter("getString", "查询服务: ${string}") // 输入日志到文件
            if (string == Settings.Secure.ANDROID_ID) return ""
            return Settings.Secure.getString(contentResolver, string)
        }

//        @PrivacyMethodProxy(
//            originalClass = UMConfigure::class,   // hook的方法所在的类名
//            originalMethod = "getOaid",   // hook的方法名
//            originalOpcode = MethodInvokeOpcode.INVOKESTATIC //hook的方法调用，一般是静态调用和实例调用
//        )
//        @JvmStatic
//        fun getOaid(
//            context: Context?, //实例调用的方法需要把声明调用对象，我们默认把对象参数放在第一位
//            listener: OnGetOaidListener,
//        ) {
//            doFilePrinter("getString", "查询服务: getOaid") // 输入日志到文件
//
//        }

//        @RequiresApi(Build.VERSION_CODES.O)
//        @PrivacyMethodProxy(
//            originalClass = android.os.Build::class,
//            originalMethod = "getSerial",
//            originalOpcode = MethodInvokeOpcode.INVOKESTATIC //静态调用
//        )
//        @JvmStatic
//        fun getSerial(): String? {
//            var result = ""
//            try {
//                doFilePrinter("getSerial", "读取Serial")
//                if (PrivacySentry.Privacy.getBuilder()?.isVisitorModel() == true) {
//                    return ""
//                }
//                result = Build.getSerial()
//            } catch (e: Exception) {
//                e.printStackTrace()
//            }
//            return result
//        }
    }
}
