package com.lijianqiang12.silent.utils

import com.blankj.utilcode.util.GsonUtils
import com.google.gson.reflect.TypeToken
import com.lijianqiang12.silent.MyConstants
import com.lijianqiang12.silent.component.activity.lock.setting.DENY_PAGE_TYPE_CUSTOM
import com.lijianqiang12.silent.component.activity.lock.setting.TheDenyPage
import com.lijianqiang12.silent.component.activity.lock.setting.TheDenyPage2
import com.lijianqiang12.silent.component.service.background_service.DeleteWhiteAppTemp
import com.lijianqiang12.silent.data.model.net.pojos.FastDenyPageExample
import com.lijianqiang12.silent.data.model.net.pojos.Page

class MyGsonUtil {

    companion object {

        private fun jsonToTheDenyPageList(string: String): MutableList<TheDenyPage> {
            if (string.isEmpty()) return mutableListOf()
            return GsonUtils.fromJson(string, object : TypeToken<MutableList<TheDenyPage>>() {}.type)
        }

        //第二个版本
        private fun jsonToTheDenyPageList2(string: String): MutableList<TheDenyPage2> {
            if (string.isEmpty()) return mutableListOf()
            return GsonUtils.fromJson(string, object : TypeToken<MutableList<TheDenyPage2>>() {}.type)
        }

        fun getDenyPageList(): MutableList<TheDenyPage2> {

            //升级denypage存储数据格式
            val denyPageString = MMKVUtils.getString(MyConstants.SP_KEY_DENY_PAGE, "")
            val denyPageList = MyGsonUtil.jsonToTheDenyPageList(denyPageString)
            if (denyPageList.isNotEmpty()) {
                val newDenyPageList = mutableListOf<TheDenyPage2>()
                denyPageList.forEachIndexed { index, theDenyPage ->
                    newDenyPageList.add(
                        TheDenyPage2(
                            -1L - index, theDenyPage.name, mutableListOf(Page(theDenyPage.pkg, theDenyPage.activity)), theDenyPage.valid, 0,
                            DENY_PAGE_TYPE_CUSTOM
                        )
                    )
                }

                MMKVUtils.put(MyConstants.SP_KEY_DENY_PAGE_V2, GsonUtils.toJson(newDenyPageList))
                denyPageList.clear()
                MMKVUtils.put(MyConstants.SP_KEY_DENY_PAGE, GsonUtils.toJson(denyPageList))
            }

            val denyPageString2 = MMKVUtils.getString(MyConstants.SP_KEY_DENY_PAGE_V2, "")
            return jsonToTheDenyPageList2(denyPageString2)
        }

        private fun jsonToFastDenyPageExampleList(string: String): MutableList<FastDenyPageExample> {
            if (string.isEmpty()) return mutableListOf()
            return GsonUtils.fromJson(string, object : TypeToken<MutableList<FastDenyPageExample>>() {}.type)
        }

        fun jsonToDeleteWhiteAppTempList(string: String): MutableList<DeleteWhiteAppTemp> {
            if (string.isEmpty()) return mutableListOf()
            return GsonUtils.fromJson(string, object : TypeToken<MutableList<DeleteWhiteAppTemp>>() {}.type)
        }
    }
}