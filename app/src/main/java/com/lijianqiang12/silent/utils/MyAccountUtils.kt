package com.lijianqiang12.silent.utils

import com.jeremyliao.liveeventbus.LiveEventBus
import com.lijianqiang12.silent.LiveBus
import com.lijianqiang12.silent.TheApplication
import com.lijianqiang12.silent.MyConstants
import com.lijianqiang12.silent.data.model.db.AppDatabase
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.GlobalScope
import kotlinx.coroutines.launch

fun logoutAll() {
    MMKVUtils.remove(MyConstants.SP_KEY_USER_ID)
    MMKVUtils.remove(MyConstants.SP_KEY_USERNAME)
    MMKVUtils.remove(MyConstants.SP_KEY_TOKEN)
    MMKVUtils.remove(MyConstants.SP_KEY_VIP_STATE)
    MMKVUtils.remove(MyConstants.SP_KEY_VIP_END_TIME)
    MMKVUtils.remove(MyConstants.SP_KEY_AVATAR)
    MMKVUtils.remove(MyConstants.SP_KEY_FORCE_QUITE_PWD)
    MMKVUtils.remove(MyConstants.SP_KEY_BIND_MOBILE)

    LiveEventBus.get(LiveBus.LOGIN, Boolean::class.java).post(false)


    val userId = MMKVUtils.getInt(MyConstants.SP_KEY_USER_ID, -1)
    if (userId == -1) return

    GlobalScope.launch(Dispatchers.IO) {
        val db = AppDatabase.getInstance(TheApplication.getInstance())
        db.fastDao().deleteAll(userId)
        db.tomatoDao().deleteAll(userId)
        db.scheduleDao().deleteAll(userId)
        db.whiteAppDao().deleteAll(userId)
        db.appLimitDao().deleteAll(userId)
        db.dayLimitDao().deleteAll(userId)
//    db.lockHistoryDao().deleteAll()

        MMKVUtils.put(MyConstants.SP_KEY_LATEST_PULL_TIME_WHITE_APP + "$userId", 0L)
        MMKVUtils.put(MyConstants.SP_KEY_LATEST_PULL_TIME_FAST + "$userId", 0L)
        MMKVUtils.put(MyConstants.SP_KEY_LATEST_PULL_TIME_TOMATO + "$userId", 0L)
        MMKVUtils.put(MyConstants.SP_KEY_LATEST_PULL_TIME_SCHEDULE + "$userId", 0L)
        MMKVUtils.put(MyConstants.SP_KEY_LATEST_PULL_TIME_APP_LIMIT + "$userId", 0L)
    }
//    MobclickAgent.onProfileSignOff()
}