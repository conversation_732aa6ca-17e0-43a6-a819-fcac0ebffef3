package com.lijianqiang12.silent.utils

import android.accessibilityservice.AccessibilityService
import android.app.Activity
import android.app.PendingIntent
import android.app.ProgressDialog
import android.content.ComponentName
import android.content.Context
import android.content.Intent
import android.content.SharedPreferences
import android.content.pm.PackageManager
import android.net.Uri
import android.os.Looper
import android.preference.PreferenceManager
import android.view.Gravity
import android.view.LayoutInflater
import android.view.View
import android.widget.ImageView
import androidx.appcompat.app.AppCompatActivity
import androidx.fragment.app.Fragment
import androidx.fragment.app.FragmentActivity
import androidx.lifecycle.lifecycleScope
import com.afollestad.materialdialogs.MaterialDialog
import com.afollestad.materialdialogs.callbacks.onDismiss
import com.afollestad.materialdialogs.customview.customView
import com.blankj.utilcode.util.AppUtils
import com.blankj.utilcode.util.GsonUtils
import com.jeremyliao.liveeventbus.LiveEventBus
import com.lijianqiang12.silent.LiveBus
import com.lijianqiang12.silent.MyConstants
import com.lijianqiang12.silent.R
import com.lijianqiang12.silent.TheApplication
import com.lijianqiang12.silent.component.activity.StartApp1PxActivity
import com.lijianqiang12.silent.component.activity.TheLoginActivity
import com.lijianqiang12.silent.component.activity.custom.dialog.LimitTimeEditDialog
import com.lijianqiang12.silent.component.activity.custom.dialog.NormalDialog
import com.lijianqiang12.silent.component.activity.custom.dialog.OnCancelClickListener
import com.lijianqiang12.silent.component.activity.custom.dialog.OnOKClickListener
import com.lijianqiang12.silent.component.service.MyAccessibilityService
import com.lijianqiang12.silent.data.model.db.AppLimit
import com.lijianqiang12.silent.data.model.db.WhiteApp
import com.lijianqiang12.silent.data.model.net.MyRetrofitClient
import com.tencent.mm.opensdk.modelbiz.WXOpenCustomerServiceChat
import com.tencent.mm.opensdk.openapi.WXAPIFactory
import kotlinx.android.synthetic.main.content_unlock_money_dialog.view.*
import kotlinx.android.synthetic.main.widget_edit_time_limit.tv_edit_time_limit
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.launch
import kotlinx.coroutines.withContext
import java.util.*


/**
 * 获取有效屏蔽页面的个数
 */
fun getDenyPageValidCount(): Int {

    val denyPageList = MyGsonUtil.getDenyPageList()

    var count = 0
    denyPageList.forEach {
        if (it.valid) {
            count++
        }
    }
    return count
}


fun startActivityCompatibleWithIntent(intent: Intent, context: Context, pkg: String, mainActivity: String) {
    try {
        if (MMKVUtils.getBoolean(MyConstants.SP_KEY_RECENT, false)) {
            intent.flags = intent.flags or Intent.FLAG_ACTIVITY_NEW_TASK
            intent.flags = intent.flags or Intent.FLAG_ACTIVITY_EXCLUDE_FROM_RECENTS
        }
        intent.component = ComponentName(pkg, mainActivity)
        val pendingIntent = PendingIntent.getActivity(context.applicationContext, 0, intent, PendingIntent.FLAG_IMMUTABLE)

        pendingIntent.send()
    } catch (e: Exception) {
        e.printStackTrace()
        MyToastUtil.showError("app启动失败，正在切换其它启动方式")
    }
}


fun startActivityForVIVO(context: Context, pkg: String) {
    try {
        val intent = Intent(context.applicationContext, StartApp1PxActivity::class.java)
        intent.putExtra("pkg", pkg)
        intent.addFlags(Intent.FLAG_ACTIVITY_NEW_TASK)
        context.applicationContext.startActivity(intent)
    } catch (e: Exception) {

        e.printStackTrace()
        MyToastUtil.showError("app启动失败，正在切换其它启动方式")
    }
}

fun startActivityCompatible(context: Context, pkg: String) {
    try {
        val intent = Intent(context.applicationContext.packageManager.getLaunchIntentForPackage(pkg))
        intent.flags = intent.flags or Intent.FLAG_ACTIVITY_NEW_TASK
        val pendingIntent = PendingIntent.getActivity(context.applicationContext, 0, intent, PendingIntent.FLAG_IMMUTABLE)
        pendingIntent.send()
    } catch (e: Exception) {
        e.printStackTrace()
        MyToastUtil.showError("app启动失败，正在切换其它启动方式")
    }
}


fun startActivityCompatible(context: Context, pkg: String, mainActivity: String, intent: Intent = Intent()): Boolean {
    try {

        intent.flags = intent.flags or Intent.FLAG_ACTIVITY_NEW_TASK
        if (MMKVUtils.getBoolean(MyConstants.SP_KEY_RECENT, false) && pkg == AppUtils.getAppPackageName()) {
            intent.flags = intent.flags or Intent.FLAG_ACTIVITY_EXCLUDE_FROM_RECENTS
        }

        intent.flags = intent.flags or Intent.FLAG_ACTIVITY_CLEAR_TOP
        intent.flags = intent.flags or Intent.FLAG_ACTIVITY_SINGLE_TOP

        intent.component = ComponentName(pkg, mainActivity)
        val pendingIntent = PendingIntent.getActivity(context.applicationContext, 0, intent, PendingIntent.FLAG_IMMUTABLE)
        pendingIntent.send()

        return true
    } catch (e: Exception) {
        e.printStackTrace()
        MyToastUtil.showError("由于手机系统限制，该应用仅能从系统桌面启动")

        return false
    }
}

fun performBack(){
    MyAccessibilityService.getInstance()?.apply {
        performGlobalAction(AccessibilityService.GLOBAL_ACTION_BACK)
    }
}

fun performHome(context: Context, info: String, useAccessibilityHome: Boolean = true, launchTransparentActivity: Boolean = false) {
//    LiveEventBus.get(LiveBus.DEBUG_MSG, String::class.java).post("performHome $info")

//    LogUtils.d("performHome start $info")
    if (MyAccessibilityService.isAccessibilityActive()) {
        MyAccessibilityService.getInstance()?.apply {
            performGlobalAction(AccessibilityService.GLOBAL_ACTION_BACK)
        }
    }

    //需要弹出一个页面，这样小窗状态的app才会最小化，否则会一直弹屏蔽页面
    if (launchTransparentActivity) {
        try {
            val intent = Intent(context.applicationContext, StartApp1PxActivity::class.java)
            intent.addFlags(Intent.FLAG_ACTIVITY_NEW_TASK)
            context.applicationContext.startActivity(intent)
        } catch (e: Exception) {

            e.printStackTrace()
            MyToastUtil.showError("app启动失败，正在切换其它启动方式")
        }
    }

    //使用home键可能会稍微减慢下次app打开速度
    if (MyAccessibilityService.isAccessibilityActive() && useAccessibilityHome) {
        MyAccessibilityService.getInstance()?.apply {
            performGlobalAction(AccessibilityService.GLOBAL_ACTION_HOME)
        }
    } else {
        val i = Intent(Intent.ACTION_MAIN)
        i.flags = Intent.FLAG_ACTIVITY_NEW_TASK
        i.addCategory(Intent.CATEGORY_HOME)
        context.startActivity(i)
    }
//    LogUtils.d("performHome end $info")
}


fun refreshConfig(activity: AppCompatActivity) {
    activity.lifecycleScope.launch(Dispatchers.IO) {
        try {
            val result = MyRetrofitClient.service.getConfig()
            if (result.code == 200) {
                result.data?.let {
                    MMKVUtils.put(MyConstants.SP_KEY_CONFIG_QQ_NUMBER, it.qqNumber)
                    MMKVUtils.put(MyConstants.SP_KEY_CONFIG_QQ_LINK, it.qqLink)
                    MMKVUtils.put(MyConstants.SP_KEY_CONFIG_SHOW_IAMFINE, it.showIAMFINE)
                    MMKVUtils.put(MyConstants.SP_KEY_CONFIG_SHOW_QQ, it.showQQ)
                    MMKVUtils.put(MyConstants.SP_KEY_CONFIG_SHOW_WX, it.showWX)
                    MMKVUtils.put(MyConstants.SP_KEY_CONFIG_SHOW_ROOM_REQUEST, it.showRoomRequest)
                    MMKVUtils.put(MyConstants.SP_KEY_CONFIG_SHOW_DEVELOPER_UNLOCK, it.showDeveloperUnlock)
                    MMKVUtils.put(MyConstants.SP_KEY_CONFIG_TOMATO_URL, it.tomatoUrl)
                    MMKVUtils.put(MyConstants.SP_KEY_CONFIG_GIFT_PIC_URL, it.giftPicUrl)
                    MMKVUtils.put(MyConstants.SP_KEY_CONFIG_GIFT_TEXT, it.giftText)
                    MMKVUtils.put(MyConstants.SP_KEY_CONFIG_BAOZANG_ICON_URL, it.baozangIconUrl)
                    MMKVUtils.put(MyConstants.SP_KEY_CONFIG_BAOZANG_TEXT, it.baozangText)
                    MMKVUtils.put(MyConstants.SP_KEY_CONFIG_SHOW_VIP_DIALOG, it.showVipDialog)
                    MMKVUtils.put(MyConstants.SP_KEY_CONFIG_CAN_CLOSE_SHOW_PRODUCT, it.canCloseShowProduct)
                    MMKVUtils.put(MyConstants.SP_KEY_CONFIG_SHOW_PRODUCT, it.showProduct)
                    MMKVUtils.put(MyConstants.SP_KEY_CONFIG_SHOW_1, it.show1)
                    MMKVUtils.put(MyConstants.SP_KEY_CONFIG_SHOW_2, it.show2)
                    MMKVUtils.put(MyConstants.SP_KEY_CONFIG_SHOW_3, it.show3)
                    MMKVUtils.put(MyConstants.SP_KEY_SHOW_ALI_SERVER, it.showAlipay)
                    MMKVUtils.put(MyConstants.SP_KEY_ALIPAY_URL, it.alipayUrl)
                    MMKVUtils.put(MyConstants.SP_KEY_SHOW_COMPLAIN, it.showComplain)
                    MMKVUtils.put(MyConstants.SP_KEY_CONFIG_SHOW_WALLPAPER, it.showWallpaper)
                    MMKVUtils.put(MyConstants.SP_KEY_CONFIG_SHOW_BOARD, it.showBoard)
                    MMKVUtils.put(MyConstants.SP_KEY_DEFAULT_CHECK_TOP_APP_METHOD, it.defaultCheckAppMode)
                    MMKVUtils.put(MyConstants.SP_KEY_CONFIG_SHOW_NO_BOARD_NOTICE, it.showNoBoardNotice)
                    MMKVUtils.put(MyConstants.SP_KEY_CONFIG_SHOW_INVITE_GIFT, it.showInviteGift)
                    MMKVUtils.put(MyConstants.SP_KEY_SHOW_XIAOHONGSHU, it.showXiaoHongShu)
                    MMKVUtils.put(MyConstants.SP_KEY_XIAOHONGSHU_URL, it.xiaoHongShuUrl)
                    MMKVUtils.put(MyConstants.SP_KEY_SHOW_ZHIHU, it.showZhiHu)
                    MMKVUtils.put(MyConstants.SP_KEY_ZHIHU_URL, it.zhiHuUrl)
                    MMKVUtils.put(MyConstants.SP_KEY_SHOW_BILIBILI, it.showBilibili)
                    MMKVUtils.put(MyConstants.SP_KEY_BILIBILI_URL, it.bilibiliUrl)
                }
            }
        } catch (e: Exception) {
            MyToastUtil.showInfo(e.message)
        }
    }
}


val SP_NAME = "spUtils"
//val SP_NAME = "offphone_sp"

class MyUtil {
    companion object {

        fun complaint(activity: FragmentActivity) {
            NormalDialog(activity).apply {
                setTitle("订单投诉")
                setContent("1. 如果您之前购买的VIP不见了，绝大多数情况是登错了账号，可将其它登录方式都试一遍，一般都能找回来。\n\n2. 其它支付相关问题，请点击下方按钮联系微信客服。")
                setGravity(Gravity.START)
                setOnNormalOKClickListener("联系客服", object : OnOKClickListener {
                    //                setOnNormalOKClickListener("发送邮件", object : OnOKClickListener {
                    override fun onclick() {

                        openWechatAssistant()

                    }
                })
                setOnNormalCancelClickListener("取消", object : OnCancelClickListener {
                    override fun onclick() {
                    }
                })
                showDialog()
            }
        }

        fun openWechatAssistant() {
            val api = WXAPIFactory.createWXAPI(TheApplication.getInstance(), MyConstants.WX_APP_ID)
            // 判断当前版本是否支持拉起客服会话
            if (api.wxAppSupportAPI >= com.tencent.mm.opensdk.constants.Build.SUPPORT_OPEN_CUSTOMER_SERVICE_CHAT) {
                val req = WXOpenCustomerServiceChat.Req()
                req.corpId = "wwda16120afb170ca5" // 企业ID
                req.url = "https://work.weixin.qq.com/kfid/kfcf2e6078582f115dd" // 客服URL
                api.sendReq(req)
            }
        }

        fun isHarmonyOS(): Boolean {
            var isOhos = false
            try {
                val abilityClass = Class.forName("ohos.aafwk.ability.Ability")
                val classLoader = abilityClass.classLoader

                //如果Ability为系统提供的，其classloader为BootClassLoader
                //如果Ability为伪造的，其classloader一般为PathClassLoader
                println("abilityClassLoader: $classLoader")

                //BootClassLoader的parent为null
                if (classLoader != null && classLoader.parent == null) {
                    isOhos = true
                }
            } catch (ignored: ClassNotFoundException) {
            }

            return isOhos
        }

        fun getSP(context: Context): SharedPreferences {
            return context.getSharedPreferences(SP_NAME, Context.MODE_PRIVATE)
        }


//        fun getSettingSP(context: Context): SharedPreferences {
//            return PreferenceManager.getDefaultSharedPreferences(context)
//        }

        fun isVIP(): Boolean {
            val vipState = MMKVUtils.getInt(MyConstants.SP_KEY_VIP_STATE, 0)
            return vipState == 1 || vipState == 2
        }

        fun checkLoginAndDo(context: FragmentActivity, block: () -> Unit) {
            if (MMKVUtils.getInt(MyConstants.SP_KEY_USER_ID, -1) == -1) {
                NormalDialog(context).apply {
                    setTitle("温馨提示")
                    setContent("使用该功能需要先登录，是否登录？")
                    setGravity(Gravity.CENTER)
                    setOnNormalOKClickListener("去登录", object : OnOKClickListener {
                        override fun onclick() {
                            context.startActivity(Intent(context, TheLoginActivity::class.java))
                        }
                    })
                    setOnNormalCancelClickListener("取消", object : OnCancelClickListener {
                        override fun onclick() {
                        }
                    })
                    showDialog()
                }
            } else {
                block()
            }
        }


        fun initYuanColor(context: Context, v: View) {
            v.tv_0yuan.setTextColor(getColorFromTheme(context, R.attr.custom_attr_app_text_4))
            v.tv_0yuan.background = context.resources.getDrawable(R.drawable.shape_yuan_grey)
            v.tv_2yuan.setTextColor(getColorFromTheme(context, R.attr.custom_attr_app_text_4))
            v.tv_2yuan.background = context.resources.getDrawable(R.drawable.shape_yuan_grey)
            v.tv_5yuan.setTextColor(getColorFromTheme(context, R.attr.custom_attr_app_text_4))
            v.tv_5yuan.background = context.resources.getDrawable(R.drawable.shape_yuan_grey)
            v.tv_10yuan.setTextColor(getColorFromTheme(context, R.attr.custom_attr_app_text_4))
            v.tv_10yuan.background = context.resources.getDrawable(R.drawable.shape_yuan_grey)
            v.tv_20yuan.setTextColor(getColorFromTheme(context, R.attr.custom_attr_app_text_4))
            v.tv_20yuan.background = context.resources.getDrawable(R.drawable.shape_yuan_grey)
            v.tv_50yuan.setTextColor(getColorFromTheme(context, R.attr.custom_attr_app_text_4))
            v.tv_50yuan.background = context.resources.getDrawable(R.drawable.shape_yuan_grey)
            v.tv_100yuan.setTextColor(getColorFromTheme(context, R.attr.custom_attr_app_text_4))
            v.tv_100yuan.background = context.resources.getDrawable(R.drawable.shape_yuan_grey)
            v.et_customyuan.setTextColor(getColorFromTheme(context, R.attr.custom_attr_app_text_4))
            v.et_customyuan.background = context.resources.getDrawable(R.drawable.shape_yuan_grey)
            v.et_customyuan.hint = "自定义"
        }


        interface OnMoneySelectListener {
            fun onMoneySelect(money: Int)
        }

        /**
         * 是否需要提示用户去设置惩罚金额
         */
        fun forceShowChooseMoneyDialog(context: Context, onMoneySelectListener: OnMoneySelectListener?) {
            var money = MMKVUtils.getInt(MyConstants.SP_KEY_FORCE_UNLOCK_PUNISH, 5)
            val customView = LayoutInflater.from(context).inflate(R.layout.content_unlock_money_dialog, null)

            var count = MMKVUtils.getInt(MyConstants.SP_KEY_MAX_UNLOCK_COUNT, 3)
            customView.tv_0_yuan_notice.text = "Tips：默认每月最多${count}次0元解锁，超过${count}次后会变为5元，次月自动恢复，最大次数可在锁机设置中调整。"
            if (money == 0) {
                customView.tv_0_yuan_notice.visibility = View.VISIBLE
            } else {
                customView.tv_0_yuan_notice.visibility = View.GONE
            }

            when (money) {

                0 -> {
                    customView.tv_0yuan.setTextColor(getColorFromTheme(context, R.attr.custom_attr_app_fg))
                    customView.tv_0yuan.background = context.resources.getDrawable(R.drawable.shape_yuan_primary)
                }

                2 -> {
                    customView.tv_2yuan.setTextColor(getColorFromTheme(context, R.attr.custom_attr_app_fg))
                    customView.tv_2yuan.background = context.resources.getDrawable(R.drawable.shape_yuan_primary)
                }

                5 -> {
                    customView.tv_5yuan.setTextColor(getColorFromTheme(context, R.attr.custom_attr_app_fg))
                    customView.tv_5yuan.background = context.resources.getDrawable(R.drawable.shape_yuan_primary)
                }

                10 -> {
                    customView.tv_10yuan.setTextColor(getColorFromTheme(context, R.attr.custom_attr_app_fg))
                    customView.tv_10yuan.background = context.resources.getDrawable(R.drawable.shape_yuan_primary)
                }

                20 -> {
                    customView.tv_20yuan.setTextColor(getColorFromTheme(context, R.attr.custom_attr_app_fg))
                    customView.tv_20yuan.background = context.resources.getDrawable(R.drawable.shape_yuan_primary)
                }

                50 -> {
                    customView.tv_50yuan.setTextColor(getColorFromTheme(context, R.attr.custom_attr_app_fg))
                    customView.tv_50yuan.background = context.resources.getDrawable(R.drawable.shape_yuan_primary)
                }

                100 -> {
                    customView.tv_100yuan.setTextColor(getColorFromTheme(context, R.attr.custom_attr_app_fg))
                    customView.tv_100yuan.background = context.resources.getDrawable(R.drawable.shape_yuan_primary)
                }

                else -> {
                    customView.et_customyuan.setTextColor(getColorFromTheme(context, R.attr.custom_attr_app_fg))
                    customView.et_customyuan.background = context.resources.getDrawable(R.drawable.shape_yuan_primary)
                    customView.et_customyuan.setText("${money}")
                }
            }

            customView.tv_0yuan.setOnClickListener {
                money = 0
                initYuanColor(context, customView)
                customView.tv_0yuan.setTextColor(getColorFromTheme(context, R.attr.custom_attr_app_fg))
                customView.tv_0yuan.background = context.resources.getDrawable(R.drawable.shape_yuan_primary)

                customView.tv_0_yuan_notice.visibility = View.VISIBLE
            }
            customView.tv_2yuan.setOnClickListener {
                money = 2
                initYuanColor(context, customView)
                customView.tv_2yuan.setTextColor(getColorFromTheme(context, R.attr.custom_attr_app_fg))
                customView.tv_2yuan.background = context.resources.getDrawable(R.drawable.shape_yuan_primary)

                customView.tv_0_yuan_notice.visibility = View.GONE
            }
            customView.tv_5yuan.setOnClickListener {
                money = 5
                initYuanColor(context, customView)
                customView.tv_5yuan.setTextColor(getColorFromTheme(context, R.attr.custom_attr_app_fg))
                customView.tv_5yuan.background = context.resources.getDrawable(R.drawable.shape_yuan_primary)

                customView.tv_0_yuan_notice.visibility = View.GONE
            }
            customView.tv_10yuan.setOnClickListener {
                money = 10
                initYuanColor(context, customView)
                customView.tv_10yuan.setTextColor(getColorFromTheme(context, R.attr.custom_attr_app_fg))
                customView.tv_10yuan.background = context.resources.getDrawable(R.drawable.shape_yuan_primary)

                customView.tv_0_yuan_notice.visibility = View.GONE
            }
            customView.tv_20yuan.setOnClickListener {
                money = 20
                initYuanColor(context, customView)
                customView.tv_20yuan.setTextColor(getColorFromTheme(context, R.attr.custom_attr_app_fg))
                customView.tv_20yuan.background = context.resources.getDrawable(R.drawable.shape_yuan_primary)

                customView.tv_0_yuan_notice.visibility = View.GONE
            }
            customView.tv_50yuan.setOnClickListener {
                money = 50
                initYuanColor(context, customView)
                customView.tv_50yuan.setTextColor(getColorFromTheme(context, R.attr.custom_attr_app_fg))
                customView.tv_50yuan.background = context.resources.getDrawable(R.drawable.shape_yuan_primary)

                customView.tv_0_yuan_notice.visibility = View.GONE
            }
            customView.tv_100yuan.setOnClickListener {
                money = 100
                initYuanColor(context, customView)
                customView.tv_100yuan.setTextColor(getColorFromTheme(context, R.attr.custom_attr_app_fg))
                customView.tv_100yuan.background = context.resources.getDrawable(R.drawable.shape_yuan_primary)

                customView.tv_0_yuan_notice.visibility = View.GONE
            }
            customView.et_customyuan.setOnClickListener {
                money = -100
                initYuanColor(context, customView)
                customView.et_customyuan.setTextColor(getColorFromTheme(context, R.attr.custom_attr_app_fg))
                customView.et_customyuan.background = context.resources.getDrawable(R.drawable.shape_yuan_primary)
                customView.et_customyuan.hint = "请输入"

                customView.tv_0_yuan_notice.visibility = View.GONE
            }


            val dialog = MaterialDialog(context)
                .customView(R.layout.content_unlock_money_dialog, customView, false)
                .cancelable(false)
                .onDismiss {

                }

            customView.tv_yuan_dialog_ok.setOnClickListener {
                when (money) {
                    -100 -> {
                        if (customView.et_customyuan.text.isEmpty()) {
                            MyToastUtil.showError("请先输入金额")
                        } else if (customView.et_customyuan.text.toString().toInt() > 10000) {
                            MyToastUtil.showError("金额不能超过10000元")
                        } else if (customView.et_customyuan.text.toString().toInt() < 0) {
                            MyToastUtil.showError("金额不能低于0元")
                        } else {

                            dialog.dismiss()

                            onMoneySelectListener?.onMoneySelect(customView.et_customyuan.text.toString().toInt())
                        }
                    }

                    else -> {

                        dialog.dismiss()

                        onMoneySelectListener?.onMoneySelect(money)
                    }

                }


            }

            dialog.cornerRadius(8.0f)
            dialog.show()

        }


        /**
         * 是否需要提示用户去设置惩罚金额
         */
        fun appLimitShowChooseMoneyDialog(context: Context, title: String, initMoney: Int, onMoneySelectListener: OnMoneySelectListener?) {
            var money = initMoney
            val customView = LayoutInflater.from(context).inflate(R.layout.content_unlock_money_dialog, null)

            customView.textView53.text = title
            customView.tv_0_yuan_notice.text = "Tips：app时间用尽后，当天无任何方法解除限时（包括客服），请慎重选择。"
            if (money == 0) {
                customView.tv_0_yuan_notice.visibility = View.VISIBLE
            } else {
                customView.tv_0_yuan_notice.visibility = View.GONE
            }

            customView.tv_0yuan.text = "禁止更改"

            when (money) {

                0 -> {
                    customView.tv_0yuan.setTextColor(getColorFromTheme(context, R.attr.custom_attr_app_fg))
                    customView.tv_0yuan.background = context.resources.getDrawable(R.drawable.shape_yuan_primary)
                }

                2 -> {
                    customView.tv_2yuan.setTextColor(getColorFromTheme(context, R.attr.custom_attr_app_fg))
                    customView.tv_2yuan.background = context.resources.getDrawable(R.drawable.shape_yuan_primary)
                }

                5 -> {
                    customView.tv_5yuan.setTextColor(getColorFromTheme(context, R.attr.custom_attr_app_fg))
                    customView.tv_5yuan.background = context.resources.getDrawable(R.drawable.shape_yuan_primary)
                }

                10 -> {
                    customView.tv_10yuan.setTextColor(getColorFromTheme(context, R.attr.custom_attr_app_fg))
                    customView.tv_10yuan.background = context.resources.getDrawable(R.drawable.shape_yuan_primary)
                }

                20 -> {
                    customView.tv_20yuan.setTextColor(getColorFromTheme(context, R.attr.custom_attr_app_fg))
                    customView.tv_20yuan.background = context.resources.getDrawable(R.drawable.shape_yuan_primary)
                }

                50 -> {
                    customView.tv_50yuan.setTextColor(getColorFromTheme(context, R.attr.custom_attr_app_fg))
                    customView.tv_50yuan.background = context.resources.getDrawable(R.drawable.shape_yuan_primary)
                }

                100 -> {
                    customView.tv_100yuan.setTextColor(getColorFromTheme(context, R.attr.custom_attr_app_fg))
                    customView.tv_100yuan.background = context.resources.getDrawable(R.drawable.shape_yuan_primary)
                }

                else -> {
                    customView.et_customyuan.setTextColor(getColorFromTheme(context, R.attr.custom_attr_app_fg))
                    customView.et_customyuan.background = context.resources.getDrawable(R.drawable.shape_yuan_primary)
                    customView.et_customyuan.setText("${money}")
                }
            }

            customView.tv_0yuan.setOnClickListener {
                money = 0
                initYuanColor(context, customView)
                customView.tv_0yuan.setTextColor(getColorFromTheme(context, R.attr.custom_attr_app_fg))
                customView.tv_0yuan.background = context.resources.getDrawable(R.drawable.shape_yuan_primary)

                customView.tv_0_yuan_notice.visibility = View.VISIBLE
            }
            customView.tv_2yuan.setOnClickListener {
                money = 2
                initYuanColor(context, customView)
                customView.tv_2yuan.setTextColor(getColorFromTheme(context, R.attr.custom_attr_app_fg))
                customView.tv_2yuan.background = context.resources.getDrawable(R.drawable.shape_yuan_primary)

                customView.tv_0_yuan_notice.visibility = View.GONE
            }
            customView.tv_5yuan.setOnClickListener {
                money = 5
                initYuanColor(context, customView)
                customView.tv_5yuan.setTextColor(getColorFromTheme(context, R.attr.custom_attr_app_fg))
                customView.tv_5yuan.background = context.resources.getDrawable(R.drawable.shape_yuan_primary)

                customView.tv_0_yuan_notice.visibility = View.GONE
            }
            customView.tv_10yuan.setOnClickListener {
                money = 10
                initYuanColor(context, customView)
                customView.tv_10yuan.setTextColor(getColorFromTheme(context, R.attr.custom_attr_app_fg))
                customView.tv_10yuan.background = context.resources.getDrawable(R.drawable.shape_yuan_primary)

                customView.tv_0_yuan_notice.visibility = View.GONE
            }
            customView.tv_20yuan.setOnClickListener {
                money = 20
                initYuanColor(context, customView)
                customView.tv_20yuan.setTextColor(getColorFromTheme(context, R.attr.custom_attr_app_fg))
                customView.tv_20yuan.background = context.resources.getDrawable(R.drawable.shape_yuan_primary)

                customView.tv_0_yuan_notice.visibility = View.GONE
            }
            customView.tv_50yuan.setOnClickListener {
                money = 50
                initYuanColor(context, customView)
                customView.tv_50yuan.setTextColor(getColorFromTheme(context, R.attr.custom_attr_app_fg))
                customView.tv_50yuan.background = context.resources.getDrawable(R.drawable.shape_yuan_primary)

                customView.tv_0_yuan_notice.visibility = View.GONE
            }
            customView.tv_100yuan.setOnClickListener {
                money = 100
                initYuanColor(context, customView)
                customView.tv_100yuan.setTextColor(getColorFromTheme(context, R.attr.custom_attr_app_fg))
                customView.tv_100yuan.background = context.resources.getDrawable(R.drawable.shape_yuan_primary)

                customView.tv_0_yuan_notice.visibility = View.GONE
            }
            customView.et_customyuan.setOnClickListener {
                money = -100
                initYuanColor(context, customView)
                customView.et_customyuan.setTextColor(getColorFromTheme(context, R.attr.custom_attr_app_fg))
                customView.et_customyuan.background = context.resources.getDrawable(R.drawable.shape_yuan_primary)
                customView.et_customyuan.hint = "请输入"

                customView.tv_0_yuan_notice.visibility = View.GONE
            }


            val dialog = MaterialDialog(context)
                .customView(R.layout.content_unlock_money_dialog, customView, false)
                .cancelable(true)
                .onDismiss {

                }

            customView.tv_yuan_dialog_ok.setOnClickListener {
                when (money) {
                    -100 -> {
                        if (customView.et_customyuan.text.isEmpty()) {
                            MyToastUtil.showError("请先输入金额")
                        } else if (customView.et_customyuan.text.toString().toInt() > 10000) {
                            MyToastUtil.showError("金额不能超过10000元")
                        } else if (customView.et_customyuan.text.toString().toInt() < 1) {
                            MyToastUtil.showError("金额不能低于1元")
                        } else {

                            dialog.dismiss()

                            onMoneySelectListener?.onMoneySelect(customView.et_customyuan.text.toString().toInt())
                        }
                    }

                    else -> {

                        dialog.dismiss()

                        onMoneySelectListener?.onMoneySelect(money)
                    }

                }


            }

            dialog.cornerRadius(8.0f)
            dialog.show()

        }


        fun checkPackageInstalled(context: Context, packageName: String, browserUrl: String): Boolean {
            try {
                // 检查是否有客户端
                context.packageManager.getPackageInfo(packageName, 0)
                return true
            } catch (e: PackageManager.NameNotFoundException) {
                // 没有安装，跳转到应用市场
                try {
                    MyToastUtil.showInfo("请先到应用市场安装支付软件")

                    openFromMarket(context, packageName, "")
                } catch (ee: Exception) {// 连应用市场都没有，用浏览器去官网下载
                    try {
                        MyToastUtil.showInfo("请先到官网安装支付软件")
                        val intent = Intent(Intent.ACTION_VIEW)
                        intent.data = Uri.parse(browserUrl)
                        context.startActivity(intent)
                    } catch (eee: Exception) {
                        MyToastUtil.showInfo("请先安装客户端")
                    }

                }

            }

            return false
        }


        fun showDialog(dialog: ProgressDialog, message: String) {
            try {
                dialog.setCancelable(true)
                dialog.setMessage(message)
                dialog.show()
            } catch (e: Exception) {
            }
        }


        fun hideDialog(dialog: ProgressDialog?) {
            if (dialog != null && dialog.isShowing) {
                try {
                    dialog.dismiss()
                } catch (e: Exception) {
                }
            }
        }

        fun loadIcon(context: Fragment, imageView: ImageView, index: Int, whiteList: MutableList<WhiteApp>, maxNum: Int) {

            if (whiteList!!.size > index) {
                if (index == (maxNum - 1) && whiteList!!.size > maxNum) {
                    imageView.setImageResource(R.drawable.ic_more_app)
                } else {
                    context.lifecycleScope.launch(Dispatchers.IO) {
                        val appIcon = getAppIcon(whiteList[index].pkg, whiteList[index].mainActivity)
                        withContext(Dispatchers.Main) {
                            imageView.setImageDrawable(appIcon)
                        }
                    }


                }
            } else {
                if (index == 0) {
                    imageView.setImageResource(R.drawable.ic_none)
                } else {
                    imageView.setImageDrawable(null)
                }
            }
        }

        fun getNotifyLength(index: Int): Int {
            return when (index) {
                1 -> 5
                2 -> 10
                3 -> 20
                4 -> 30
                5 -> 60
                6 -> 300
                else -> 0
            }
        }

        fun getNotifyLengthString(index: Int): String {
            return when (index) {
                1 -> "5秒"
                2 -> "10秒"
                3 -> "20秒"
                4 -> "30秒"
                5 -> "1分钟"
                6 -> "5分钟"
                else -> "不提醒"
            }
        }


        fun getScheduleNotifyLength(index: Int): Int {
            return when (index) {
                1 -> 60
                2 -> 120
                3 -> 300
                4 -> 600
                5 -> 1800
                6 -> 3600
                else -> 0
            }
        }

        fun getScheduleNotifyLengthString(index: Int): String {
            return when (index) {
                1 -> "1分钟"
                2 -> "2分钟"
                3 -> "5分钟"
                4 -> "10分钟"
                5 -> "30分钟"
                6 -> "1小时"
                else -> "不提醒"
            }
        }

        fun isInMainThread(): Boolean {
            return Looper.getMainLooper() == Looper.myLooper()

        }

        fun jsonToPkgList(json: String): MutableList<String> {
            if (json.isEmpty()) {
                return arrayListOf()
            }
            return if (json.startsWith("[")) {
                GsonUtils.fromJson(json, GsonUtils.getListType(String::class.java))
            } else {
                mutableListOf(json)
            }
        }

        fun pkgListToJson(list: MutableList<String>): String {
            return GsonUtils.toJson(list, GsonUtils.getListType(String::class.java))
        }

        fun getAppLimitTitle(appLimit: AppLimit): String {
            val pkgList = jsonToPkgList(appLimit.appPkg)
            return when {
                appLimit.title.isNotEmpty() -> {
                    appLimit.title
                }

                pkgList.size == 1 -> {
                    getAppName(pkgList[0], "")
                }

                else -> {
                    "未命名"
                }
            }
        }

        fun getAppLimitTitle(title: String, appPkg: String): String {
            val pkgList = jsonToPkgList(appPkg)
            return when {
                title.isNotEmpty() -> {
                    title
                }

                pkgList.size == 1 -> {
                    getAppName(pkgList[0], "")
                }

                else -> {
                    "未命名"
                }
            }
        }

        fun getTimeRangeString(start: Long, end: Long): String {
            return "${secondToSimpleHm(start)}-${
                if (start >= end) {
                    "次日"
                } else {
                    ""
                }
            }${secondToSimpleHm(end)}"
        }

        fun isCurrentInTimeRange(start: Long, end: Long, todayValid: Boolean = true, yesterdayValid: Boolean = true): Boolean {

            if (start < 0 || end < 0) return true

            val calendar = Calendar.getInstance()
            val currentSecond = calendar.get(Calendar.HOUR_OF_DAY) * 60 * 60 + calendar.get(Calendar.MINUTE) * 60 + calendar.get(Calendar.SECOND).toLong()

            if (yesterdayValid) {
                if (start >= end) {
                    if (currentSecond < end) return true
                }
            }

            if (todayValid) {
                if (start >= end) {
                    if (currentSecond >= start) return true
                } else {
                    if (currentSecond in start until end) return true
                }

            }

            return false
        }

        fun getDayValid(dayOfWeek: Int, appLimit: AppLimit): Boolean {
            return when (dayOfWeek) {
                1 -> appLimit.sunday
                2 -> appLimit.monday
                3 -> appLimit.tuesday
                4 -> appLimit.wednesday
                5 -> appLimit.thursday
                6 -> appLimit.friday
                7 -> appLimit.saturday
                else -> false
            }
        }

        fun getForceUnlockMoney(): Int {
            var setMoney = MMKVUtils.getInt(MyConstants.SP_KEY_FORCE_UNLOCK_PUNISH, 5)
            if (setMoney > 0) {
                return setMoney
            } else {
                val calendar = Calendar.getInstance()
                val currentMonth = "${calendar.get(Calendar.YEAR)}-${calendar.get(Calendar.MONTH) + 1}"
                val hasUnlockedTime = MMKVUtils.getInt(currentMonth, 0)
                if (hasUnlockedTime < MMKVUtils.getInt(MyConstants.SP_KEY_MAX_UNLOCK_COUNT, 3)) {
                    return 0
                } else {
                    return 5
                }
            }
        }

        fun getTodayCalendarString(): String {
            val calendar = Calendar.getInstance()
            return "${calendar.get(Calendar.YEAR)}-${calendar.get(Calendar.MONTH) + 1}-${calendar.get(Calendar.DAY_OF_MONTH)}"
        }
    }
}