package com.lijianqiang12.silent.utils

import android.app.AlertDialog
import android.content.Intent
import android.graphics.drawable.Drawable
import android.os.Build
import android.os.Handler
import android.os.Looper
import android.view.LayoutInflater
import android.view.View
import android.view.WindowManager
import androidx.appcompat.app.AppCompatActivity
import androidx.fragment.app.Fragment
import com.jeremyliao.liveeventbus.LiveEventBus
import com.lijianqiang12.silent.LiveBus
import com.lijianqiang12.silent.MyConstants
import com.lijianqiang12.silent.TheApplication
import com.lijianqiang12.silent.component.activity.custom.dialog.OnCancelClickListener
import com.lijianqiang12.silent.component.activity.custom.dialog.OnOKClickListener
import com.lijianqiang12.silent.component.activity.custom.dialog.VIPDialog
import com.lijianqiang12.silent.component.activity.me.vip.FROM_WHERE
import com.lijianqiang12.silent.component.activity.me.vip.VIP2Activity
import com.lijianqiang12.silent.databinding.DialogErrorLockBinding

class DialogUtil {

    companion object {
        private var isFloatingDialogShowing = false
        private var lastApp = Pair("", "")

        fun showFloatingAppDialog(appIcon: Drawable, appName: String, pkgName: String, activityName: String) {
            if (!isFloatingDialogShowing && lastApp != Pair(pkgName, activityName)) {
                isFloatingDialogShowing = true
                lastApp = Pair(pkgName, activityName)

                val handler = Handler(Looper.getMainLooper())
                handler.post {
                    val binding = DialogErrorLockBinding.inflate(LayoutInflater.from(TheApplication.getInstance()))
                    val builder = AlertDialog.Builder(TheApplication.getInstance())

                    builder.setView(binding.root)

                    //下面这行代码放到子线程中会 Can't create handler inside thread that has not called Looper.prepare()
                    val dialog = builder.create()
                    dialog.setCancelable(true)
                    dialog.setCanceledOnTouchOutside(true)
                    //8.0系统加强后台管理，禁止在其他应用和窗口弹提醒弹窗，如果要弹，必须使用TYPE_APPLICATION_OVERLAY，否则弹不出
                    if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
                        dialog.window!!.setType(WindowManager.LayoutParams.TYPE_APPLICATION_OVERLAY)
                    } else {
                        dialog.window!!.setType(WindowManager.LayoutParams.TYPE_SYSTEM_ALERT)
                    }

                    binding.ivLockAppIcon.setImageDrawable(appIcon)
                    binding.tvLockAppLockAppName.text = appName
                    binding.tvLockAppLockPkg.text = "包名：${pkgName}"
                    binding.tvLockAppLockMainActivity.text = "界面名：${activityName}"
                    if (isSystemApp(pkgName)) {
                        binding.btnLockViewUploadToOfficial.setOnClickListener {
                            dialog.dismiss()
                            LiveEventBus.get(LiveBus.UPLOAD_DENY, String::class.java).post("")
                        }
                        binding.btnLockViewUploadToOfficial.visibility = View.VISIBLE
                    } else {
                        binding.btnLockViewUploadToOfficial.visibility = View.GONE
                    }
                    binding.btnLockViewOk2.setOnClickListener {
                        dialog.dismiss()
                    }

                    dialog.setOnDismissListener {
                        isFloatingDialogShowing = false
                    }

                    dialog.show()
                }
            }
        }

        fun showFloatingDialog(
            title: String = "温馨提示",
            content: String = "",
            positiveButtonText: String = "确定",
            negativeButtonText: String = "取消",
            cancelable: Boolean = true,
            canceledOnTouchOutside: Boolean = true,
            negativeButtonListener: () -> Unit = {},
            positiveButtonListener: () -> Unit = {},
        ) {
            val handler = Handler(Looper.getMainLooper())
            handler.post {

                val builder = AlertDialog.Builder(TheApplication.getInstance())
//                    .setIcon(android.R.drawable.ic_dialog_info)
                    .setTitle(title)
                    .setMessage(content)
                    .setPositiveButton(
                        positiveButtonText
                    ) { dialog, whichButton ->
                        positiveButtonListener()
                    }
                    .setNegativeButton(
                        negativeButtonText
                    ) { dialog, whichButton ->
                        negativeButtonListener()
                    }
                //下面这行代码放到子线程中会 Can't create handler inside thread that has not called Looper.prepare()
                val dialog = builder.create()
                dialog.setCancelable(cancelable)
                dialog.setCanceledOnTouchOutside(canceledOnTouchOutside)
                //8.0系统加强后台管理，禁止在其他应用和窗口弹提醒弹窗，如果要弹，必须使用TYPE_APPLICATION_OVERLAY，否则弹不出
                if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
                    dialog.window!!.setType(WindowManager.LayoutParams.TYPE_APPLICATION_OVERLAY)
                } else {
                    dialog.window!!.setType(WindowManager.LayoutParams.TYPE_SYSTEM_ALERT)
                }
                dialog.show()
            }
        }

        fun showVIPDialog(
            activity: AppCompatActivity?,
            fragment: Fragment?,
            content: String,
            fromWhere: String,
            title: String = "开通VIP",
            cancelText: String = "",
            positiveText: String = "立即开通",
            negativeText: String = "取消",
            onNegativeListener: OnCancelClickListener? = null,
        ) {

            activity?.let {
                if (MMKVUtils.getBoolean(MyConstants.SP_KEY_CONFIG_SHOW_VIP_DIALOG, true)) {
                    VIPDialog(it).apply {
                        setTitle(title)
                        setContent(content)
                        setPositiveText(positiveText)
                        setNegativeText(negativeText)
                        isCancelable = true
                        setOnOKClickListener(object : OnOKClickListener {
                            override fun onclick() {
                                val intent = Intent(it, VIP2Activity::class.java)
                                intent.putExtra(FROM_WHERE, fromWhere)
                                it.startActivity(intent)
                            }
                        })

                        setOnCancelClickListener(onNegativeListener)
                        show()
                    }
                } else {
                    val intent = Intent(it, VIP2Activity::class.java)
                    intent.putExtra(FROM_WHERE, fromWhere)
                    it.startActivity(intent)
                    MyToastUtil.showInfo(content)
                }
            }
            fragment?.let {
                if (MMKVUtils.getBoolean(MyConstants.SP_KEY_CONFIG_SHOW_VIP_DIALOG, true)) {
                    VIPDialog(it).apply {
                        setTitle(title)
                        setContent(content)
                        setPositiveText(positiveText)
                        setNegativeText(negativeText)
                        isCancelable = true
                        setOnOKClickListener(object : OnOKClickListener {
                            override fun onclick() {
                                val intent = Intent(it.requireContext(), VIP2Activity::class.java)
                                intent.putExtra(FROM_WHERE, fromWhere)
                                it.requireContext().startActivity(intent)
                            }
                        })

                        setOnCancelClickListener(onNegativeListener)
                        show()
                    }
                } else {
                    val intent = Intent(it.requireContext(), VIP2Activity::class.java)
                    intent.putExtra(FROM_WHERE, fromWhere)
                    it.requireContext().startActivity(intent)
                    MyToastUtil.showInfo(content)
                }
            }
        }
    }
}
