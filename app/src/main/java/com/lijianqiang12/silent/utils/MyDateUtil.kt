package com.lijianqiang12.silent.utils

import com.blankj.utilcode.util.LogUtils
import com.lijianqiang12.silent.data.model.db.DayLimit
import java.util.*

class MyDateUtil {

    companion object {

        fun getTodayName(): String {
            val calendar = Calendar.getInstance()
//            LogUtils.d("getTodayName", calendar.get(Calendar.DAY_OF_WEEK))
            return when (calendar.get(Calendar.DAY_OF_WEEK)) {
                Calendar.MONDAY -> "monday"
                Calendar.TUESDAY -> "tuesday"
                Calendar.WEDNESDAY -> "wednesday"
                Calendar.THURSDAY -> "thursday"
                Calendar.FRIDAY -> "friday"
                Calendar.SATURDAY -> "saturday"
                Calendar.SUNDAY -> "sunday"
                else -> ""
            }
        }

        fun isTodayValid(dayLimit: DayLimit): Boolean {
            val calendar = Calendar.getInstance()
            return when (calendar.get(Calendar.DAY_OF_WEEK)) {
                Calendar.MONDAY -> dayLimit.monday
                Calendar.TUESDAY -> dayLimit.tuesday
                Calendar.WEDNESDAY -> dayLimit.wednesday
                Calendar.THURSDAY -> dayLimit.thursday
                Calendar.FRIDAY -> dayLimit.friday
                Calendar.SATURDAY -> dayLimit.saturday
                Calendar.SUNDAY -> dayLimit.sunday
                else -> false
            }
        }
    }
}