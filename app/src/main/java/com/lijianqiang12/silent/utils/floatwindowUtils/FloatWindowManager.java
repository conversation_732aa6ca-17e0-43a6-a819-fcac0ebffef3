/*
 * Copyright (C) 2016 Facishare Technology Co., Ltd. All Rights Reserved.
 */
package com.lijianqiang12.silent.utils.floatwindowUtils;

import android.content.Context;
import android.content.Intent;
import android.net.Uri;
import android.os.Build;
import android.provider.Settings;
import android.util.Log;

import com.lijianqiang12.silent.utils.floatwindowUtils.rom.HuaweiUtils;
import com.lijianqiang12.silent.utils.floatwindowUtils.rom.MeizuUtils;
import com.lijianqiang12.silent.utils.floatwindowUtils.rom.MiuiUtils;
import com.lijianqiang12.silent.utils.floatwindowUtils.rom.QikuUtils;
import com.lijianqiang12.silent.utils.floatwindowUtils.rom.RomUtils;

import java.lang.reflect.Field;
import java.lang.reflect.Method;

/**
 * Description:
 *
 * <AUTHOR>
 * @since 2016-10-17
 */

public class FloatWindowManager {
    private static final String TAG = "FloatWindowManager";


    public static boolean checkPermission(Context context) {
        //6.0 版本之后由于 google 增加了对悬浮窗权限的管理，所以方式就统一了
        if (Build.VERSION.SDK_INT < 23) {
            if (RomUtils.checkIsMiuiRom()) {
                return miuiPermissionCheck(context);
            } else if (RomUtils.checkIsMeizuRom()) {
                return meizuPermissionCheck(context);
            } else if (RomUtils.checkIsHuaweiRom()) {
                return huaweiPermissionCheck(context);
            } else if (RomUtils.checkIs360Rom()) {
                return qikuPermissionCheck(context);
            }
        }
        return commonROMPermissionCheck(context);
    }

    private static boolean huaweiPermissionCheck(Context context) {
        return HuaweiUtils.checkFloatWindowPermission(context);
    }

    private static boolean miuiPermissionCheck(Context context) {
        return MiuiUtils.checkFloatWindowPermission(context);
    }

    private static boolean meizuPermissionCheck(Context context) {
        return MeizuUtils.checkFloatWindowPermission(context);
    }

    private static boolean qikuPermissionCheck(Context context) {
        return QikuUtils.checkFloatWindowPermission(context);
    }

    private static boolean commonROMPermissionCheck(Context context) {
        //最新发现魅族6.0的系统这种方式不好用，天杀的，只有你是奇葩，没办法，单独适配一下
        if (RomUtils.checkIsMeizuRom()) {
            return meizuPermissionCheck(context);
        } else {
            Boolean result = true;
            if (Build.VERSION.SDK_INT >= 23) {
                try {
                    Class clazz = Settings.class;
                    Method canDrawOverlays = clazz.getDeclaredMethod("canDrawOverlays", Context.class);
                    result = (Boolean) canDrawOverlays.invoke(null, context);
                } catch (Exception e) {
                    Log.e(TAG, Log.getStackTraceString(e));
                }
            }
            return result;
        }
    }

    public static void applyPermission(Context context) {
        if (RomUtils.checkIsMeizuRom()) {
            try {
                Class clazz = Settings.class;
                Field field = clazz.getDeclaredField("ACTION_MANAGE_OVERLAY_PERMISSION");
                Intent intent = new Intent(field.get(null).toString());
                intent.setFlags(Intent.FLAG_ACTIVITY_NEW_TASK);
                intent.setData(Uri.parse("package:" + context.getPackageName()));
                context.startActivity(intent);
            } catch (Exception e) {
                Log.e(TAG, Log.getStackTraceString(e));
                MeizuUtils.applyPermission(context);
            }
        } else {
            if (Build.VERSION.SDK_INT < 23) {
                if (RomUtils.checkIsMiuiRom()) {
                    MiuiUtils.applyMiuiPermission(context);
                } else if (RomUtils.checkIsHuaweiRom()) {
                    HuaweiUtils.applyPermission(context);
                } else if (RomUtils.checkIs360Rom()) {
                    QikuUtils.applyPermission(context);
                }
            } else {
                try {
                    Class clazz = Settings.class;
                    Field field = clazz.getDeclaredField("ACTION_MANAGE_OVERLAY_PERMISSION");
                    Intent intent = new Intent(field.get(null).toString());
                    intent.setFlags(Intent.FLAG_ACTIVITY_NEW_TASK);
                    intent.setData(Uri.parse("package:" + context.getPackageName()));
                    context.startActivity(intent);
                } catch (Exception e) {
                    Log.e(TAG, Log.getStackTraceString(e));
                }
            }
        }
    }

//  private static void ROM360PermissionApply(final Context context) {
//    showConfirmDialog(context, new OnConfirmResult() {
//      @Override
//      public void confirmResult(boolean confirm) {
//        if (confirm) {
//          QikuUtils.applyPermission(context);
//        } else {
//          Log.e(TAG, "ROM:360, user manually refuse OVERLAY_PERMISSION");
//        }
//      }
//    });
//  }
//
//  private static void huaweiROMPermissionApply(final Context context) {
//    showConfirmDialog(context, new OnConfirmResult() {
//      @Override
//      public void confirmResult(boolean confirm) {
//        if (confirm) {
//          HuaweiUtils.applyPermission(context);
//        } else {
//          Log.e(TAG, "ROM:huawei, user manually refuse OVERLAY_PERMISSION");
//        }
//      }
//    });
//  }
//
//  private static void meizuROMPermissionApply(final Context context) {
//    showConfirmDialog(context, new OnConfirmResult() {
//      @Override
//      public void confirmResult(boolean confirm) {
//        if (confirm) {
//          MeizuUtils.applyPermission(context);
//        } else {
//          Log.e(TAG, "ROM:meizu, user manually refuse OVERLAY_PERMISSION");
//        }
//      }
//    });
//  }
//
//  private static void miuiROMPermissionApply(final Context context) {
//    showConfirmDialog(context, new OnConfirmResult() {
//      @Override
//      public void confirmResult(boolean confirm) {
//        if (confirm) {
//          MiuiUtils.applyMiuiPermission(context);
//        } else {
//          Log.e(TAG, "ROM:miui, user manually refuse OVERLAY_PERMISSION");
//        }
//      }
//    });
//  }
//
//  /**
//   * 通用 rom 权限申请
//   */
//  private static void commonROMPermissionApply(final Context context) {
//    //这里也一样，魅族系统需要单独适配
//    if (RomUtils.checkIsMeizuRom()) {
//      meizuROMPermissionApply(context);
//    } else {
//      if (Build.VERSION.SDK_INT >= 23) {
//        showConfirmDialog(context, new OnConfirmResult() {
//          @Override
//          public void confirmResult(boolean confirm) {
//            if (confirm) {
//              try {
//                Class clazz = Settings.class;
//                Field field = clazz.getDeclaredField("ACTION_MANAGE_OVERLAY_PERMISSION");
//
//                Intent intent = new Intent(field.get(null).toString());
//                intent.setFlags(Intent.FLAG_ACTIVITY_NEW_TASK);
//                intent.setData(Uri.parse("package:" + context.getPackageName()));
//                context.startActivity(intent);
//              } catch (Exception e) {
//                Log.e(TAG, Log.getStackTraceString(e));
//              }
//            } else {
//              Log.d(TAG, "user manually refuse OVERLAY_PERMISSION");
//              //需要做统计效果
//            }
//          }
//        });
//      }
//    }
//  }
//
//  private static void showConfirmDialog(Context context, OnConfirmResult result) {
//    showConfirmDialog(context, "您的手机没有授予悬浮窗权限，请开启后再试", result);
//  }
//
//  private static void showConfirmDialog(Context context, String message, final OnConfirmResult result) {
//    new AlertDialog.Builder(context).setCancelable(true).setTitle("")
//            .setMessage(message)
//            .setPositiveButton("现在去开启",
//                    new DialogInterface.OnClickListener() {
//                      @Override
//                      public void onClick(DialogInterface dialog, int which) {
//                        result.confirmResult(true);
//                        dialog.dismiss();
//                      }
//                    }).setNegativeButton("暂不开启",
//            new DialogInterface.OnClickListener() {
//
//              @Override
//              public void onClick(DialogInterface dialog, int which) {
//                result.confirmResult(false);
//                dialog.dismiss();
//              }
//            }).create().show();
//  }
//
//  private interface OnConfirmResult {
//    void confirmResult(boolean confirm);
//  }


}
