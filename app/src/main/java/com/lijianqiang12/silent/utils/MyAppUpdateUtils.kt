package com.lijianqiang12.silent.utils

import android.content.Context
import android.content.Intent
import android.net.Uri
import android.view.Gravity
import androidx.lifecycle.lifecycleScope
import com.blankj.utilcode.util.AppUtils
import com.blankj.utilcode.util.RomUtils
import com.lijianqiang12.silent.MyConstants
import com.lijianqiang12.silent.component.activity.base.BaseActivity
import com.lijianqiang12.silent.component.activity.custom.dialog.NormalDialog
import com.lijianqiang12.silent.component.activity.custom.dialog.OnCancelClickListener
import com.lijianqiang12.silent.component.activity.custom.dialog.OnOKClickListener
import com.lijianqiang12.silent.data.model.net.MyRetrofitClient
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.launch
import kotlinx.coroutines.withContext

fun checkUpdate(activity: BaseActivity, forceCheck: Boolean) {

    activity.lifecycleScope.launch(Dispatchers.IO) {
        try {
            val result = MyRetrofitClient.service.appUpdate()
            if (result.code == 200) {
                MMKVUtils.put("${result.data!!.lastestVersionCode}", result.data!!.updateContent)
                if (result.data!!.lastestVersionCode > AppUtils.getAppVersionCode()) {
//            if (true) {
                    if (forceCheck || result.data!!.lastestVersionCode > MMKVUtils.getInt(MyConstants.SP_KEY_JUMP_VERSION,0)) {
                        withContext(Dispatchers.Main) {
                            NormalDialog(activity).apply {
                                setTitle("发现新版本 V${result.data.lastestVersionName}")
                                setContent("${result.data.updateContent}")
                                isCancelable = !result.data.isForce
                                setGravity(Gravity.START)
                                setOnNormalOKClickListener("现在更新", object : OnOKClickListener {
                                    override fun onclick() {
                                        when {
                                            MyRomUtils.isHuawei() -> {
                                                if (result.data.huaweiValid) {
                                                    openFromMarket(activity, result.data.downloadUrl)
                                                } else {
                                                    openFromWebsite(activity, result.data.downloadUrl)
                                                }
                                            }
                                            MyRomUtils.isHonor() -> {
                                                if (result.data.honorValid) {
                                                    openFromMarket(activity, result.data.downloadUrl)
                                                } else {
                                                    openFromWebsite(activity, result.data.downloadUrl)
                                                }
                                            }
                                            MyRomUtils.isXiaomi() -> {
                                                if (result.data.miuiValid) {
                                                    openFromMarket(activity, result.data.downloadUrl)
                                                } else {
                                                    openFromWebsite(activity, result.data.downloadUrl)
                                                }
                                            }
                                            MyRomUtils.isOppo() -> {
                                                if (result.data.oppoValid) {
                                                    openFromMarket(activity, result.data.downloadUrl)
                                                } else {
                                                    openFromWebsite(activity, result.data.downloadUrl)
                                                }
                                            }
                                            MyRomUtils.isVivo() -> {
                                                if (result.data.vivoValid) {
                                                    openFromMarket(activity, result.data.downloadUrl)
                                                } else {
                                                    openFromWebsite(activity, result.data.downloadUrl)
                                                }
                                            }
                                            MyRomUtils.isMeizu() -> {
                                                if (result.data.meizuValid) {
                                                    openFromMarket(activity, result.data.downloadUrl)
                                                } else {
                                                    openFromWebsite(activity, result.data.downloadUrl)
                                                }
                                            }
                                            MyRomUtils.isSamsung() -> {
                                                if (result.data.samsungValid) {
                                                    openFromMarket(activity, result.data.downloadUrl)
                                                } else {
                                                    openFromWebsite(activity, result.data.downloadUrl)
                                                }
                                            }
                                            else -> {
                                                openFromWebsite(activity, result.data.downloadUrl)
                                            }
                                        }
                                    }
                                })
                                if (!result.data.isForce) {
                                    setOnNormalCancelClickListener("忽略此版", object : OnCancelClickListener {
                                        override fun onclick() {
                                            MMKVUtils.put(MyConstants.SP_KEY_JUMP_VERSION, result.data.lastestVersionCode)
                                        }
                                    })
                                }
                                showDialog()
                            }
                        }
                    }
                } else {
                    if (forceCheck) {
                        withContext(Dispatchers.Main) {
                            MyToastUtil.showInfo("已经是最新版本了")
                        }
                    }
                }
            }
        } catch (e: Exception) {
            MyToastUtil.showInfo(e.message)
//            LogUtils.d(e.message)
        }
    }
}


fun openFromWebsite(context: Context, url: String) {
    val uri = Uri.parse(url)
    context.startActivity(Intent(Intent.ACTION_VIEW, uri))
}


fun openFromMarket(context: Context, url: String) {
    openFromMarket(context, AppUtils.getAppPackageName(), url)

}

fun openFromMarket(context: Context, pkg: String, downloadUrl: String) {
    try {
        when {
            RomUtils.isSamsung() -> {
                val intent = Intent(Intent.ACTION_VIEW)
                intent.data = Uri.parse("samsungapps://ProductDetail/$pkg")
//                intent.data = Uri.parse("https://www.samsungapps.com/appquery/appDetail.as?appId=$pkg")
                intent.setPackage("com.sec.android.app.samsungapps");
                //            intent.addFlags(Intent.FLAG_ACTIVITY_NEW_TASK)
                context.startActivity(intent)
            }
//            RomUtils.isSony() -> {
//                intent.data = Uri.parse("https://m.sonyselect.cn/$pkg")
//            }
            MyRomUtils.isHonor() -> {
                val intent = Intent(Intent.ACTION_VIEW)
                intent.data = Uri.parse("honormarket://details?id=$pkg")
                context.startActivity(intent)
            }
            MyRomUtils.isHuawei() -> {
//                val intent = Intent("com.huawei.appmarket.appmarket.intent.action.AppDetail.withid")
//                intent.setPackage("com.huawei.appmarket")
//                intent.putExtra("appId", "C100425597")
//                context.startActivity(intent)
                val intent = Intent("com.huawei.appmarket.intent.action.AppDetail")
                intent.setPackage("com.huawei.appmarket")
                intent.putExtra("APP_PACKAGENAME", pkg)
                context.startActivity(intent)
            }
            else -> {
                val intent = Intent(Intent.ACTION_VIEW)
                intent.data = Uri.parse("market://details?id=$pkg")
                context.startActivity(intent)
            }
        }
    } catch (e: Exception) {
        e.printStackTrace()
        openFromWebsite(context, downloadUrl)
    }


//    try {
//        val ii = Intent(Intent.ACTION_VIEW)
//        ii.data = Uri.parse("market://details?id=" + pkg)
//        activity.startActivity(ii)
//    } catch (e: Exception) {
//        e.printStackTrace()
//        openFromWebsite(activity, downloadUrl)
//    }
}