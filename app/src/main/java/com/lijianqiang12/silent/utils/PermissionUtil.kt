package com.lijianqiang12.silent.utils

import android.annotation.SuppressLint
import android.app.Activity
import android.app.AppOpsManager
import android.app.NotificationManager
import android.app.usage.UsageStatsManager
import android.content.*
import android.content.Context.APP_OPS_SERVICE
import android.content.pm.PackageManager
import android.media.AudioManager
import android.net.Uri
import android.os.Build
import android.os.PowerManager
import android.os.Process
import android.provider.Settings
import android.text.TextUtils
import android.util.Log
import android.view.Gravity
import android.view.LayoutInflater
import androidx.annotation.RequiresApi
import androidx.appcompat.app.AppCompatActivity
import com.afollestad.materialdialogs.MaterialDialog
import com.afollestad.materialdialogs.customview.customView
import com.blankj.utilcode.util.ActivityUtils
import com.blankj.utilcode.util.AppUtils
import com.lijianqiang12.silent.MyConstants
import com.lijianqiang12.silent.R
import com.lijianqiang12.silent.TheApplication
import com.lijianqiang12.silent.component.activity.ToastNoticeActivity
import com.lijianqiang12.silent.component.activity.custom.dialog.ImageDialog
import com.lijianqiang12.silent.component.activity.custom.dialog.OnCancelClickListener
import com.lijianqiang12.silent.component.activity.custom.dialog.OnOKClickListener
import com.lijianqiang12.silent.component.service.MyAccessibilityService
import com.lijianqiang12.silent.component.service.TheNotificationListenerService
import ezy.assist.compat.RomUtil
import ezy.assist.compat.SettingsCompat
import kotlinx.android.synthetic.main.content_alive_huawei_dialog.view.*
import kotlinx.android.synthetic.main.content_alive_miui_dialog.view.*
import kotlinx.android.synthetic.main.content_alive_oppo_dialog.view.*
import kotlinx.android.synthetic.main.content_alive_other_dialog.view.*
import kotlinx.android.synthetic.main.content_alive_vivo_dialog.view.*
import java.lang.reflect.InvocationTargetException
import java.lang.reflect.Method


class PermissionUtil {

    companion object {

        fun hasMustPermission(context: Context): Boolean {
            if (!SettingsCompat.canDrawOverlays(context.applicationContext)) {
                return false
            }
            if (!switched(context.applicationContext)) {
                return false
            }
//            if (RomUtils.isXiaomi() && (!MMKVUtils.getBoolean(MyConstants.SP_KEY_MIUI_BACK, false))) {
//                return false
//            }
            if (MyRomUtils.isXiaomi() && (!isMiuiBackgroundAllowed(context))) {
                return false
            }

//            if (MMKVUtils.getBoolean(MyConstants.SP_SETTING_DENY_DROPDOWN, false)) {
//                if (!isAccessibilitySettingsOn(MyConstants.ACCESS_ABILITY_NAME, context.applicationContext)) {
//                    return false
//                }
//            }
            return true
        }

        fun hasAllPermission2(activity: Activity, showToast: Boolean = false): Int {
            if (!SettingsCompat.canDrawOverlays(activity.applicationContext)) {
                if (showToast) MyToastUtil.showInfo("请先授予悬浮窗权限")
                return 1
            }
            if (!switched(activity.applicationContext)) {
                if (showToast) MyToastUtil.showInfo("请先授予查看使用情况权限权限")
                return 1
            }
            if (MyRomUtils.isXiaomi() && (!isMiuiBackgroundAllowed(activity))) {
                if (showToast) MyToastUtil.showInfo("请先授予后台弹出权限")
                return 1
            }
            if (MMKVUtils.getBoolean(MyConstants.SP_SETTING_DENY_UNINSTALL, false)) {
                if (!isAccessibilitySettingsOn(MyConstants.ACCESS_ABILITY_NAME, activity.applicationContext)) {
                    if (showToast) MyToastUtil.showInfo("屏蔽卸载需要无障碍辅助权限")
                    return 1
                }
            }

            if (MMKVUtils.getInt(MyConstants.SP_SETTING_DENY_ASSIST_NEW, 0) > 0) {
                if (!isAccessibilitySettingsOn(MyConstants.ACCESS_ABILITY_NAME, activity.applicationContext)) {
                    if (showToast) MyToastUtil.showInfo("屏蔽语音助手需要无障碍辅助权限")
                    return 1
                }
            }
            if (MMKVUtils.getInt(MyConstants.SP_SETTING_DENY_SHUTDOWN_NEW, 0) > 0) {
                if (!isAccessibilitySettingsOn(MyConstants.ACCESS_ABILITY_NAME, activity.applicationContext)) {
                    if (showToast) MyToastUtil.showInfo("屏蔽电源键需要无障碍辅助权限")
                    return 1
                }
            }

            if (MMKVUtils.getInt(MyConstants.SP_SETTING_DENY_EDIT_BUTTON_NEW, 0) > 0) {
                if (!isAccessibilitySettingsOn(MyConstants.ACCESS_ABILITY_NAME, activity.applicationContext)) {
                    if (showToast) MyToastUtil.showInfo("屏蔽编辑快捷开关需要无障碍辅助权限")
                    return 1
                }
            }
            if ((MyRomUtils.isOppo() || MyRomUtils.isVivo()) && MMKVUtils.getInt(MyConstants.SP_SETTING_DENY_CLOSE_RUNNING_SERVICE_NEW, 0) > 0) {
                if (!isAccessibilitySettingsOn(MyConstants.ACCESS_ABILITY_NAME, activity.applicationContext)) {
                    if (showToast) MyToastUtil.showInfo("屏蔽关闭正在运行的任务需要无障碍辅助权限")
                    return 1
                }
            }
            if (MMKVUtils.getInt(MyConstants.SP_SETTING_DENY_DROPDOWN_NEW, 0) > 0) {
                if (!isAccessibilitySettingsOn(MyConstants.ACCESS_ABILITY_NAME, activity.applicationContext)) {
                    if (showToast) MyToastUtil.showInfo("屏蔽下拉菜单需要无障碍辅助权限")
                    return 1
                }
            }
            if (MyRomUtils.isVivo() && MMKVUtils.getBoolean(MyConstants.SP_SETTING_DENY_UP, false)) {
                if (!isAccessibilitySettingsOn(MyConstants.ACCESS_ABILITY_NAME, activity.applicationContext)) {
                    if (showToast) MyToastUtil.showInfo("屏蔽上拉菜单需要无障碍辅助权限")
                    return 1
                }
            }

//            if (MMKVUtils.getBoolean(MyConstants.SP_SETTING_DENY_FAST_OPEN, false)) {
//                if (!isAccessibilitySettingsOn(MyConstants.ACCESS_ABILITY_NAME, activity.applicationContext)) {
//                    if (showToast) MyToastUtil.showInfo("屏蔽侧边栏需要无障碍辅助权限")
//                    return 1
//                }
//            }
            if (MMKVUtils.getBoolean(MyConstants.SP_SETTING_DENY_SMALL_WINDOW, false)) {
                if (!isAccessibilitySettingsOn(MyConstants.ACCESS_ABILITY_NAME, activity.applicationContext)) {
                    if (showToast) MyToastUtil.showInfo("适配小窗需要无障碍辅助权限")
                    return 1
                }
            }

//            if (!wallpaperIsUsed(activity.applicationContext)) {
//                return 2
//            }
            return 0
        }

        fun hasAllPermission(activity: Activity, showToast: Boolean = false): Boolean {
            if (!SettingsCompat.canDrawOverlays(activity.applicationContext)) {
                if (showToast) MyToastUtil.showInfo("请先授予悬浮窗权限")
                return false
            }
            if (!switched(activity.applicationContext)) {
                if (showToast) MyToastUtil.showInfo("请先授予查看使用情况权限权限")
                return false
            }
            if (MyRomUtils.isXiaomi() && (!isMiuiBackgroundAllowed(activity))) {
                if (showToast) MyToastUtil.showInfo("请先授予后台弹出权限")
                return false
            }
            if (MMKVUtils.getBoolean(MyConstants.SP_SETTING_DENY_UNINSTALL, false)) {
                if (!isAccessibilitySettingsOn(MyConstants.ACCESS_ABILITY_NAME, activity.applicationContext)) {
                    if (showToast) MyToastUtil.showInfo("屏蔽卸载需要无障碍辅助权限")
                    return false
                }
            }

            if (MMKVUtils.getInt(MyConstants.SP_SETTING_DENY_ASSIST_NEW, 0) > 0) {
                if (!isAccessibilitySettingsOn(MyConstants.ACCESS_ABILITY_NAME, activity.applicationContext)) {
                    if (showToast) MyToastUtil.showInfo("屏蔽语音助手需要无障碍辅助权限")
                    return false
                }
            }
            if (MMKVUtils.getInt(MyConstants.SP_SETTING_DENY_SHUTDOWN_NEW, 0) > 0) {
                if (!isAccessibilitySettingsOn(MyConstants.ACCESS_ABILITY_NAME, activity.applicationContext)) {
                    if (showToast) MyToastUtil.showInfo("屏蔽电源键需要无障碍辅助权限")
                    return false
                }
            }
            if (MMKVUtils.getInt(MyConstants.SP_SETTING_DENY_EDIT_BUTTON_NEW, 0) > 0) {
                if (!isAccessibilitySettingsOn(MyConstants.ACCESS_ABILITY_NAME, activity.applicationContext)) {
                    if (showToast) MyToastUtil.showInfo("屏蔽编辑快捷开关需要无障碍辅助权限")
                    return false
                }
            }
            if ((MyRomUtils.isOppo() || MyRomUtils.isVivo()) && MMKVUtils.getInt(MyConstants.SP_SETTING_DENY_CLOSE_RUNNING_SERVICE_NEW, 0) > 0) {
                if (!isAccessibilitySettingsOn(MyConstants.ACCESS_ABILITY_NAME, activity.applicationContext)) {
                    if (showToast) MyToastUtil.showInfo("屏蔽关闭正在运行的任务需要无障碍辅助权限")
                    return false
                }
            }
            if (MMKVUtils.getInt(MyConstants.SP_SETTING_DENY_DROPDOWN_NEW, 0) > 0) {
                if (!isAccessibilitySettingsOn(MyConstants.ACCESS_ABILITY_NAME, activity.applicationContext)) {
                    if (showToast) MyToastUtil.showInfo("屏蔽下拉菜单需要无障碍辅助权限")
                    return false
                }
            }
            if (MyRomUtils.isVivo() && MMKVUtils.getBoolean(MyConstants.SP_SETTING_DENY_UP, false)) {
                if (!isAccessibilitySettingsOn(MyConstants.ACCESS_ABILITY_NAME, activity.applicationContext)) {
                    if (showToast) MyToastUtil.showInfo("屏蔽上拉菜单需要无障碍辅助权限")
                    return false
                }
            }
//            if (MMKVUtils.getBoolean(MyConstants.SP_SETTING_DENY_FAST_OPEN, false)) {
//                if (!isAccessibilitySettingsOn(MyConstants.ACCESS_ABILITY_NAME, activity.applicationContext)) {
//                    if (showToast) MyToastUtil.showInfo("屏蔽侧边栏需要无障碍辅助权限")
//                    return false
//                }
//            }
            if (MMKVUtils.getBoolean(MyConstants.SP_SETTING_DENY_SMALL_WINDOW, false)) {
                if (!isAccessibilitySettingsOn(MyConstants.ACCESS_ABILITY_NAME, activity.applicationContext)) {
                    if (showToast) MyToastUtil.showInfo("适配小窗需要无障碍辅助权限")
                    return false
                }
            }

//            if (!wallpaperIsUsed(activity.applicationContext)) {
//                if (showToast) {
//                    NormalDialog(activity as AppCompatActivity).apply {
//                        setTitle("温馨提示")
//                        setContent("检测到未设置“壁纸守护”，若锁机异常退出或定时任务失效，请到本页“加固权限”中设置该权限。")
//                        setOnNormalOKClickListener("现在设置", object : OnOKClickListener {
//                            override fun onclick() {
////                                ActivityUtils.startActivity(PermissionActivity::class.java)
//                                startActivity(Intent(requireActivity(), PermissionActivity::class.java))
//                            }
//
//                        })
//                        setOnNormalCancelClickListener("暂时忽略", object : OnCancelClickListener {
//                            override fun onclick() {
//                            }
//                        })
//                        show()
//                    }
//                }
//                return false
//            }
            return true
//                    && isAccessibilitySettingsOn(Constants.ACCESS_ABILITY_NAME, activity.applicationContext)
//                    && NotificationUtil.ifNotificationEnable(activity.applicationContext)
        }

//        fun hasAllForcePermission(activity: Activity): Boolean {
//            return SettingsCompat.canDrawOverlays(activity.applicationContext)
//                    && switched(activity.applicationContext)
//                    && isAccessibilitySettingsOn(Constants.ACCESS_ABILITY_NAME, activity.applicationContext)
//                    && ignoreBatteryOptimization(activity)
//                    && NotificationUtil.ifNotificationEnable(activity.applicationContext)
//        }

        /**
         * 忽略电池优化
         */
        fun ignoreBatteryOptimization(activity: Activity): Boolean {
            return if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.M) {
                val powerManager = activity.applicationContext.getSystemService(Context.POWER_SERVICE) as PowerManager
                powerManager.isIgnoringBatteryOptimizations(activity.applicationContext.packageName)
            } else {
                true
            }

        }

        /**
         * 提示设置保活
         */
        fun showAliveDialog(activity: AppCompatActivity) {

            when {
                MyRomUtils.isHuaweiSeries() -> {
                    val customView = LayoutInflater.from(activity).inflate(R.layout.content_alive_huawei_dialog, null)

                    customView.tv_huawei_power_save_mode.setOnClickListener {
                        ImageUtil.openVideo(activity, "https://offphone-video-1252369707.file.myqcloud.com/huawei_power_save_mode.mp4")
                    }
                    customView.tv_huawei_recent.setOnClickListener {
                        ImageUtil.openVideo(activity, "https://offphone-video-1252369707.file.myqcloud.com/huawei_recent.mp4")
                    }

                    customView.tv_dialog_alive_huawei_msg2.setOnClickListener {
                        try {
                            ActivityUtils.startActivity("com.huawei.systemmanager", "com.huawei.systemmanager.power.ui.HwPowerManagerActivity")
                        } catch (e: Exception) {
//                            MyToastUtil.showError("跳转失败，请自行到系统设置中关闭")
//                            activity.startActivity(Intent(Settings.ACTION_SETTINGS))
                            openNoticeMsgPage(activity, "打开手机系统“设置”-“电池”，关闭“低电量模式”。", true)
                        }
                    }
//                    customView.tv_dialog_alive_huawei_msg3.setOnClickListener {//已经不显示
//                        try {
//                            ActivityUtils.startActivity("com.huawei.systemmanager", "com.huawei.systemmanager.startupmgr.ui.StartupNormalAppListActivity")
//                        } catch (e: Exception) {
//                            MyToastUtil.showError("跳转失败，请自行到系统设置中关闭")
//                        }
//                    }
                    customView.tv_dialog_alive_huawei_msg4.setOnClickListener {
                        showRecent(activity)
                    }
                    val dialog = MaterialDialog(activity).customView(R.layout.content_alive_huawei_dialog, customView, false)
                        .cancelable(true).cornerRadius(8.0f)
                    customView!!.tv_vip_dialog_cancel_huawei.setOnClickListener {
                        dialog.dismiss()
                        MyUtil.getSP(activity.applicationContext).edit().putBoolean(MyConstants.SP_KEY_IS_LOCKING, false).apply()
                        MyUtil.getSP(activity.applicationContext).edit().putBoolean(MyConstants.SP_KEY_RECENT_NOTIFY, true).apply()

                    }

                    dialog.show()
                }

                MyRomUtils.isXiaomi() -> {
                    val customView = LayoutInflater.from(activity).inflate(R.layout.content_alive_miui_dialog, null)
                    customView.tv_miui_power_save_mode.setOnClickListener {
                        ImageUtil.openVideo(activity, "https://offphone-video-1252369707.file.myqcloud.com/xiaomi_power_save_mode.mp4")
                    }
                    customView.tv_miui_recent.setOnClickListener {
                        ImageUtil.openVideo(activity, "https://offphone-video-1252369707.file.myqcloud.com/xiaomi_recent.mp4")
                    }

                    customView.tv_dialog_alive_miui_msg2.setOnClickListener {
                        try {
                            val intent = Intent(Settings.ACTION_APPLICATION_DETAILS_SETTINGS)
                            intent.data = Uri.parse("package:${activity.packageName}")
                            activity.startActivity(intent)
                        } catch (e: Exception) {
                            MyToastUtil.showError("跳转失败，请自行到系统设置中关闭")
                        }
                    }
                    customView.tv_dialog_alive_miui_msg3.setOnClickListener {
                        try {
                            val intent = Intent(Settings.ACTION_APPLICATION_DETAILS_SETTINGS)
                            intent.data = Uri.parse("package:${activity.packageName}")
                            activity.startActivity(intent)
                        } catch (e: Exception) {
                            MyToastUtil.showError("跳转失败，请自行到系统设置中关闭")
                        }
                    }

                    customView.tv_dialog_alive_miui_msg4.setOnClickListener {
                        showRecent(activity)
                    }
                    val dialog = MaterialDialog(activity).customView(R.layout.content_alive_miui_dialog, customView, false)
                        .cancelable(true)
                        .cornerRadius(8.0f)
                    customView!!.tv_vip_dialog_cancel_miui.setOnClickListener {
                        dialog.dismiss()
                        MyUtil.getSP(activity.applicationContext).edit().putBoolean(MyConstants.SP_KEY_IS_LOCKING, false).apply()
                        MyUtil.getSP(activity.applicationContext).edit().putBoolean(MyConstants.SP_KEY_RECENT_NOTIFY, true).apply()

                    }

                    dialog.show()
                }

                RomUtil.isOppo() -> {
                    val customView = LayoutInflater.from(activity).inflate(R.layout.content_alive_oppo_dialog, null)

                    customView.tv_oppo_battery_protect.setOnClickListener {
                        ImageUtil.openVideo(activity, "https://offphone-video-1252369707.file.myqcloud.com/oppo_battery_protect.mp4")
                    }
                    customView.tv_oppo_recent.setOnClickListener {
                        ImageUtil.openVideo(activity, "https://offphone-video-1252369707.file.myqcloud.com/oppo_recent.mp4")
                    }

                    customView.tv_dialog_alive_oppo_msg2.setOnClickListener {
                        try {
                            MyToastUtil.showInfo("请到电池中设置")
                            val intent = Intent(Settings.ACTION_SETTINGS)
                            activity.startActivity(intent)
                        } catch (e: Exception) {
                            MyToastUtil.showError("跳转失败，请自行到系统设置中关闭")
                        }

                    }
                    customView.tv_dialog_alive_oppo_msg3.setOnClickListener {
                        try {
                            MyToastUtil.showInfo("请到电池-应用速冻中设置")
                            val intent = Intent(Settings.ACTION_SETTINGS)
                            activity.startActivity(intent)
                        } catch (e: Exception) {
                            MyToastUtil.showError("跳转失败，请自行到系统设置中关闭")
                        }
                    }
                    customView.tv_dialog_alive_oppo_msg4.setOnClickListener {
                        showRecent(activity)
                    }
                    val dialog = MaterialDialog(activity).customView(R.layout.content_alive_oppo_dialog, customView, false)
                        .cancelable(true).cornerRadius(8.0f)
                    customView!!.tv_vip_dialog_cancel_oppo.setOnClickListener {
                        dialog.dismiss()
                        MyUtil.getSP(activity.applicationContext).edit().putBoolean(MyConstants.SP_KEY_IS_LOCKING, false).apply()
                        MyUtil.getSP(activity.applicationContext).edit().putBoolean(MyConstants.SP_KEY_RECENT_NOTIFY, true).apply()

                    }

                    dialog.show()
                }

                RomUtil.isVivo() -> {
                    val customView = LayoutInflater.from(activity).inflate(R.layout.content_alive_vivo_dialog, null)

                    customView.tv_vivo_power_save_mode.setOnClickListener {
                        ImageUtil.openVideo(activity, "https://offphone-video-1252369707.file.myqcloud.com/vivo_power_save_mode.mp4")
                    }
                    customView.tv_vivo_battery_protect.setOnClickListener {
                        ImageUtil.openVideo(activity, "https://offphone-video-1252369707.file.myqcloud.com/vivo_battery_protect.mp4")
                    }
                    customView.tv_vivo_recent.setOnClickListener {
                        ImageUtil.openVideo(activity, "https://offphone-video-1252369707.file.myqcloud.com/vivo_recent.mp4")
                    }


                    customView.tv_dialog_alive_vivo_msg2.setOnClickListener {
                        try {
                            val packageName = "com.iqoo.powersaving"
                            val className = "com.iqoo.powersaving.PowerSavingManagerActivity"
                            val cn = ComponentName(packageName, className)

                            val intent = Intent()

                            intent.component = cn
                            activity.startActivity(intent)
                        } catch (e: Exception) {
                            MyToastUtil.showError("跳转失败，请自行到系统设置-电池中关闭")
                        }
                    }
                    customView.tv_dialog_alive_vivo_msg3.setOnClickListener {
                        try {
                            val packageName = "com.iqoo.powersaving"
                            val className = "com.iqoo.powersaving.PowerSavingManagerActivity"
                            val cn = ComponentName(packageName, className)

                            val intent = Intent()

                            intent.component = cn
                            activity.startActivity(intent)
                        } catch (e: Exception) {
                            MyToastUtil.showError("跳转失败，请自行到系统设置-电池中关闭")
                        }
                    }
                    customView.tv_dialog_alive_vivo_msg4.setOnClickListener {
                        showRecent(activity)
                    }
                    val dialog = MaterialDialog(activity).customView(R.layout.content_alive_vivo_dialog, customView, false)
                        .cancelable(true).cornerRadius(8.0f)
                    customView!!.tv_vip_dialog_cancel_vivo.setOnClickListener {
                        dialog.dismiss()
                        MyUtil.getSP(activity.applicationContext).edit().putBoolean(MyConstants.SP_KEY_IS_LOCKING, false).apply()
                        MyUtil.getSP(activity.applicationContext).edit().putBoolean(MyConstants.SP_KEY_RECENT_NOTIFY, true).apply()

                    }

                    dialog.show()
                }

                else -> {
                    val customView = LayoutInflater.from(activity).inflate(R.layout.content_alive_other_dialog, null)
                    customView.tv_dialog_alive_other_msg4.setOnClickListener {
                        showRecent(activity)
                    }
                    val dialog = MaterialDialog(activity).customView(R.layout.content_alive_other_dialog, customView, false)
                        .cancelable(true).cornerRadius(8.0f)
                    customView!!.tv_vip_dialog_cancel.setOnClickListener {
                        dialog.dismiss()
                        MyUtil.getSP(activity.applicationContext).edit().putBoolean(MyConstants.SP_KEY_IS_LOCKING, false).apply()
                        MyUtil.getSP(activity.applicationContext).edit().putBoolean(MyConstants.SP_KEY_RECENT_NOTIFY, true).apply()

                    }

                    dialog.show()
                }
            }

        }

        fun showRecent(activity: Activity) {
            val view = LayoutInflater.from(activity).inflate(R.layout.fragment_recent, null)
            MaterialDialog(activity)
                .title(text = "在最近任务中锁定")
                .cornerRadius(8.0f)
                .customView(R.layout.fragment_recent, view, false)
                .positiveButton(text = "我知道了")
                .show()
        }

        //展示提示信息
        fun openNoticeMsgPage(context: Context, msg: String, showJumpToSetting: Boolean = false) {
            val intent = Intent(context, ToastNoticeActivity::class.java)
            intent.putExtra("msg", msg)
            intent.putExtra("showJumpToSetting", showJumpToSetting)
            intent.putExtra("showIKnow", !showJumpToSetting)
            intent.addFlags(Intent.FLAG_ACTIVITY_NEW_TASK)
            context.startActivity(intent)
        }

        //跳转app设置详情页
        fun openAppDetailSetting(context: Context) {
            try {
                val intent = Intent(Settings.ACTION_APPLICATION_DETAILS_SETTINGS)
                intent.data = Uri.parse("package:${context.packageName}")
                context.startActivity(intent)
            } catch (e: Exception) {
                MyToastUtil.showError("跳转失败，请自行到设置中授予该权限。")
            }
        }


//        fun openAppPermissionSetting(context: Activity) {
//            try {
//                val intent = Intent(Settings.ACTION_SETTINGS)
//                intent.addFlags(Intent.FLAG_ACTIVITY_NEW_TASK)
//                intent.action = "android.settings.APPLICATION_DETAILS_SETTINGS";
//                intent.data = Uri.fromParts("package", context.packageName, null);
//                context.startActivity(intent)
//            } catch (e: Exception) {
//                MyToastUtil.showError("跳转失败，请自行到设置中授予该权限。")
//            }
//        }

//        private val POWERMANAGER_INTENTS = arrayOf(
//            Intent().setComponent(ComponentName("com.miui.securitycenter", "com.miui.permcenter.autostart.AutoStartManagementActivity")),
//            Intent().setComponent(ComponentName("com.letv.android.letvsafe", "com.letv.android.letvsafe.AutobootManageActivity")),
//            Intent().setComponent(ComponentName("com.huawei.systemmanager", "com.huawei.systemmanager.startupmgr.ui.StartupNormalAppListActivity")),
//            Intent().setComponent(ComponentName("com.huawei.systemmanager", "com.huawei.systemmanager.optimize.process.ProtectActivity")),
//            Intent().setComponent(ComponentName("com.huawei.systemmanager", "com.huawei.systemmanager.appcontrol.activity.StartupAppControlActivity")),
//            Intent().setComponent(ComponentName("com.coloros.safecenter", "com.coloros.safecenter.permission.startup.StartupAppListActivity")),
//            Intent().setComponent(ComponentName("com.coloros.safecenter", "com.coloros.safecenter.startupapp.StartupAppListActivity")),
//            Intent().setComponent(ComponentName("com.oppo.safe", "com.oppo.safe.permission.startup.StartupAppListActivity")),
//            Intent().setComponent(ComponentName("com.iqoo.secure", "com.iqoo.secure.ui.phoneoptimize.AddWhiteListActivity")),
//            Intent().setComponent(ComponentName("com.iqoo.secure", "com.iqoo.secure.ui.phoneoptimize.BgStartUpManager")),
//            Intent().setComponent(ComponentName("com.vivo.permissionmanager", "com.vivo.permissionmanager.activity.BgStartUpManagerActivity")),
//            Intent().setComponent(ComponentName("com.samsung.android.lool", "com.samsung.android.sm.battery.ui.BatteryActivity")),
//            Intent().setComponent(ComponentName("com.samsung.android.lool", "com.samsung.android.sm.ui.battery.BatteryActivity")),
//            Intent().setComponent(ComponentName("com.htc.pitroad", "com.htc.pitroad.landingpage.activity.LandingPageActivity")),
//            Intent().setComponent(ComponentName("com.asus.mobilemanager", "com.asus.mobilemanager.MainActivity")),
//            Intent().setComponent(ComponentName("com.transsion.phonemanager", "com.itel.autobootmanager.activity.AutoBootMgrActivity"))
//        )

        fun openAutoStartPage(activity: AppCompatActivity) {
            try {

//                for (intent in POWERMANAGER_INTENTS) {
//                    if (activity.packageManager.resolveActivity(intent, PackageManager.MATCH_DEFAULT_ONLY) != null) {
////                        ActivityUtils.startActivity(intent)
//                        LogUtils.d("ljq========${intent.toString()}")
//                    }
//                }

                when {
                    MyRomUtils.isHuawei() -> {
                        ImageDialog(activity).apply {
                            setTitle("操作说明")
                            setContent("请在手机管家的“应用启动管理”中按图示操作。")
                            setImageUrl("https://upic-1252369707.file.myqcloud.com/uPic/2023/02/14/16_04_37_0PCGRh.jpg")
                            setGravity(Gravity.START)
                            isCancelable = false
                            setOnNormalOKClickListener("去设置", object : OnOKClickListener {
                                override fun onclick() {
                                    try {
                                        startActivityCompatible(
                                            TheApplication.getInstance(),
                                            "com.huawei.systemmanager",
                                            "com.huawei.systemmanager.mainscreen.MainScreenActivity"
                                        )
                                    } catch (e: Exception) {
                                        openNoticeMsgPage(
                                            activity,
                                            "打开手机系统“设置”-“应用和服务-应用启动管理”，找到远离手机，关闭“自动管理”，并打开“允许自启动”和“允许后台活动”。",
                                            true
                                        )
                                    }
                                }
                            })
                            setOnNormalCancelClickListener("取消", object : OnCancelClickListener {
                                override fun onclick() {
                                }
                            })
                            show()
                        }


//                        try {
//                            startActivityCompatible(TheApplication.getInstance(),"com.huawei.systemmanager", "com.huawei.systemmanager.startupmgr.ui.StartupNormalAppListActivity")
////                            openNoticeMsgPage(activity, "找到远离手机，关闭“自动管理”，并打开“允许自启动”和“允许后台活动”。")
//                        } catch (e: Exception) {
//                            try {
//                                startActivityCompatible(TheApplication.getInstance(),
//                                    "com.huawei.systemmanager",
//                                    "com.huawei.systemmanager.appcontrol.activity.StartupAppControlActivity"
//                                )
////                                openNoticeMsgPage(activity, "找到远离手机，关闭“自动管理”，并打开“允许自启动”和“允许后台活动”。")
//                            } catch (e: Exception) {
//                                try {
//                                    startActivityCompatible(TheApplication.getInstance(),
//                                        "com.huawei.systemmanager",
//                                        "com.huawei.systemmanager.optimize.bootstart.BootStartActivity"
//                                    )
////                                    openNoticeMsgPage(activity, "找到远离手机，关闭“自动管理”，并打开“允许自启动”和“允许后台活动”。")
//                                } catch (e: Exception) {
//                                    try {
//                                        startActivityCompatible(TheApplication.getInstance(), "com.huawei.systemmanager")
////                                        openNoticeMsgPage(activity, "点击“应用自启动管理”，找到远离手机，关闭“自动管理”，并打开“允许自启动”和“允许后台活动”。")
//                                    } catch (e: Exception) {
//                                        openNoticeMsgPage(activity, "打开手机系统“设置”-“应用-启动管理”，找到远离手机，关闭“自动管理”，并打开“允许自启动”和“允许后台活动”。", true)
//
//                                    }
//                                }
//                            }
//                        }
                    }

                    MyRomUtils.isHonor() -> {
                        ImageDialog(activity).apply {
                            setTitle("操作说明")
                            setContent("请在手机管家的“应用启动管理”中按图示操作。")
                            setImageUrl("https://upic-1252369707.file.myqcloud.com/uPic/2023/02/14/16_04_37_0PCGRh.jpg")
                            setGravity(Gravity.START)
                            isCancelable = false
                            setOnNormalOKClickListener("去设置", object : OnOKClickListener {
                                override fun onclick() {
                                    // 荣耀300上测试无法跳转手机管家，原因未知，所以跳转系统设置
                                    openNoticeMsgPage(
                                        activity,
                                        "打开手机系统“设置”-“应用-应用启动管理”，找到远离手机，关闭“自动管理”，并打开“允许自启动”和“允许后台活动”。",
                                        true
                                    )

//                                    try {
//                                        startActivityCompatible(
//                                            TheApplication.getInstance(),
//                                            "com.hihonor.devicemanager",
//                                            "com.hihonor.devicemanager.mainscreen.MainScreenActivity"
//                                        )
//                                    } catch (e: Exception) {
//                                        openNoticeMsgPage(
//                                            activity,
//                                            "打开手机系统“设置”-“应用-启动管理”，找到远离手机，关闭“自动管理”，并打开“允许自启动”和“允许后台活动”。",
//                                            true
//                                        )
//                                    }
                                }
                            })
                            setOnNormalCancelClickListener("取消", object : OnCancelClickListener {
                                override fun onclick() {
                                }
                            })
                            show()
                        }

                    }

                    RomUtil.isMiui() -> {
                        MyToastUtil.showInfo("请打开自启动开关。")
                        try {
                            val intent = Intent(Settings.ACTION_SETTINGS)
                            intent.addFlags(Intent.FLAG_ACTIVITY_NEW_TASK)
                            intent.action = "android.settings.APPLICATION_DETAILS_SETTINGS";
                            intent.data = Uri.fromParts("package", activity.getPackageName(), null);
                            activity.startActivity(intent)
                        } catch (e: Exception) {
                            MyToastUtil.showError("跳转失败，请自行到设置中授予远离手机后台弹出权限。")
                        }
                    }

                    RomUtil.isSmartisan() -> {
                        val packageName2 = "com.smartisanos.security"
                        val className2 = "com.smartisanos.security.PackageDetail"
                        ActivityUtils.startActivity(packageName2, className2)
                        val intent2 = Intent(activity, ToastNoticeActivity::class.java)
                        intent2.putExtra("msg", "找到远离手机，允许被系统和第三方应用启动，并打开悬浮窗开关。")
                        activity.startActivity(intent2)

                        val packageName = "com.android.settings"
                        val className = "com.android.settings.applications.ManagerApplicationsRunningActiity"
                        ActivityUtils.startActivity(packageName, className)
                        val intent = Intent(activity, ToastNoticeActivity::class.java)
                        intent.putExtra("msg", "找到远离手机，点击它并选择后台常驻。")
                        activity.startActivity(intent)
                    }

                    RomUtil.isFlyme() -> {
                        val packageName2 = "com.meizu.safe"
                        val className2 = "com.meizu.safe.permission.AutoRunActivity"
                        ActivityUtils.startActivity(packageName2, className2)
                        val intent2 = Intent(activity, ToastNoticeActivity::class.java)
                        intent2.putExtra("msg", "找到远离手机，并打开自启开关。")
                        activity.startActivity(intent2)

                        val packageName = "com.meizu.safe"
                        val className = "com.meizu.safe.ramcleaner.RAMCleanerWhiteList"
                        ActivityUtils.startActivity(packageName, className)
                        val intent = Intent(activity, ToastNoticeActivity::class.java)
                        intent.putExtra("msg", "点击添加白名单，并选中远离手机。")
                        activity.startActivity(intent)
                    }

                    RomUtil.isOppo() -> {
                        val intent = Intent(Settings.ACTION_APPLICATION_DETAILS_SETTINGS)
                        intent.data = Uri.parse("package:${activity.packageName}")
                        activity.startActivity(intent)
                        val intent2 = Intent(activity, ToastNoticeActivity::class.java)
                        intent2.putExtra(
                            "msg",
                            "（非常重要）\n在耗电管理中将" + AppUtils.getAppName() + "设为“允许后台运行”，并为其打开自启动等权限。"
                        )
                        activity.startActivity(intent2)
//                        try {
//                            MyToastUtil.showInfo(applicationContext, "请在 应用管理-远离手机 中打开“允许自启动。")
//                            val intent = Intent(Settings.ACTION_SETTINGS)
//                            activity.startActivity(intent)
//                        } catch (e: Exception) {
//                            MyToastUtil.showError(applicationContext, "跳转失败，请自行到系统设置中关闭")
//                        }
                    }

                    RomUtil.isQiku() -> {
                        val packageName2 = "com.qiku.android.security"
                        val className2 = "com.qiku.android.security.ui.activity.autorun.AutoRunList"
                        ActivityUtils.startActivity(packageName2, className2)
                        val intent2 = Intent(activity, ToastNoticeActivity::class.java)
                        intent2.putExtra("msg", "找到远离手机，允许自启。")
                        activity.startActivity(intent2)

                    }

                    RomUtil.isVivo() -> {

                        ImageDialog(activity).apply {
                            setTitle("操作说明")
                            setContent("请在i管家的“应用管理”中按图示操作。")
                            setImageUrl("https://upic-1252369707.file.myqcloud.com/uPic/2023/03/24/15_56_58_PSVCAf.png")
                            setGravity(Gravity.START)
                            isCancelable = false
                            setOnNormalOKClickListener("去设置", object : OnOKClickListener {
                                override fun onclick() {
                                    try {
                                        startActivityCompatible(
                                            TheApplication.getInstance(),
                                            "com.iqoo.secure",
                                            "com.iqoo.secure.MainActivity"
                                        )
                                    } catch (e: Exception) {
                                        openNoticeMsgPage(activity, "打开手机系统“i管家”-“应用管理”-“权限管理”，找到远离手机，开启自启动和后台弹出界面。", true)
                                    }
                                }
                            })
                            setOnNormalCancelClickListener("取消", object : OnCancelClickListener {
                                override fun onclick() {
                                }
                            })
                            show()
                        }


//                        try {
//                            startActivityCompatible(
//                                TheApplication.getInstance(),
//                                "com.iqoo.secure",
//                                "com.iqoo.secure.MainActivity"
//                            )
//                        } catch (e: Exception) {
//                            val intent = Intent(Settings.ACTION_APPLICATION_DETAILS_SETTINGS)
//                            intent.data = Uri.parse("package:${activity.packageName}")
//                            activity.startActivity(intent)
////                            val intent2 = Intent(activity, ToastNoticeActivity::class.java)
////                            intent2.putExtra("msg", "（非常重要）\n\n进入“权限-单项权限设置”，为远离手机开启自启动和后台弹出界面。")
////                            activity.startActivity(intent2)
////                            openNoticeMsgPage(activity, "\"（非常重要）\\n\\n进入“权限-单项权限设置”，为远离手机开启自启动和后台弹出界面。", true)
//                        }

                    }

                    else -> {
                        val intent = Intent(activity, ToastNoticeActivity::class.java)
                        intent.putExtra(
                            "msg",
                            "（非常重要）\n\n请去系统设置中为远离手机授予自启管理，允许后台弹出界面，并允许其在锁屏时后台运行，否则远离手机无法锁机或打卡。"
                        )
                        activity.startActivity(intent)
                    }
                }
            } catch (e: Exception) {
                e.printStackTrace()
                activity.startActivity(Intent(Settings.ACTION_SETTINGS))

                val intent = Intent(activity, ToastNoticeActivity::class.java)
                intent.putExtra(
                    "msg",
                    "（非常重要！）\n请去系统设置中为远离手机授予自启管理，允许后台弹出界面，并允许其在锁屏时后台运行，否则远离手机无法锁机或打卡。"
                )
                activity.startActivity(intent)
            }
        }

//        fun hasOption(context: Context): Boolean {
//            val packageManager = context.applicationContext.packageManager
//            val intent = Intent(Settings.ACTION_USAGE_ACCESS_SETTINGS)
//            val list = packageManager.queryIntentActivities(intent, PackageManager.MATCH_DEFAULT_ONLY)
//            return list.size > 0
//        }

        /**
         * 是否有应用使用详情查看权限
         *
         * @param context context
         * @return true
         */
        @SuppressLint("WrongConstant")
        fun switched(context: Context): Boolean {
            val ts = System.currentTimeMillis()
            val usageStatsManager = context.applicationContext.getSystemService("usagestats") as UsageStatsManager
            val queryUsageStats = usageStatsManager.queryUsageStats(UsageStatsManager.INTERVAL_DAILY, ts - 3600000L, ts)
            return !(queryUsageStats == null || queryUsageStats.isEmpty())
        }

        /**
         * 是否有应用使用详情查看权限
         *
         * @param context context
         * @return true
         */
//        fun hasUsagePermission(context: Context): Boolean {
//            val mode: Int
//            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.LOLLIPOP) {
//                try {
//                    val packageManager = context.packageManager
//                    val applicationInfo = packageManager.getApplicationInfo(context.packageName, 0)
//                    val appOpsManager = context.applicationContext.getSystemService(Context.APP_OPS_SERVICE) as AppOpsManager
//                    mode = appOpsManager.checkOpNoThrow(AppOpsManager.OPSTR_GET_USAGE_STATS, applicationInfo.uid, applicationInfo.packageName)
//                    return mode == AppOpsManager.MODE_ALLOWED
//                } catch (e: Exception) {
//                    e.printStackTrace()
//                    return false
//                }
//
//            }
//            return true
//        }

        fun openUsagePermission(context: Context) {
            try {
                val i = Intent(Settings.ACTION_USAGE_ACCESS_SETTINGS)
                i.flags = Intent.FLAG_ACTIVITY_NEW_TASK

                if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.Q) {//android10可以直接跳转到对应APP的设置中
                    i.data = Uri.parse("package:" + context.packageName)
                }
                context.startActivity(i)


            } catch (e: Exception) {
                e.printStackTrace()

                try {
                    val i = Intent(Settings.ACTION_USAGE_ACCESS_SETTINGS)
                    i.flags = Intent.FLAG_ACTIVITY_NEW_TASK
                    context.startActivity(i)
                } catch (e: Exception) {
                    try {
                        MyToastUtil.showWarning("无法跳转到授权页面")
                        val ii = Intent(Settings.ACTION_SECURITY_SETTINGS)
                        ii.component = ComponentName("com.android.settings", "com.android.settings.Settings\$SecuritySettingsActivity")
                        ii.flags = Intent.FLAG_ACTIVITY_NEW_TASK
                        context.startActivity(ii)
                    } catch (ee: Exception) {
                        ee.printStackTrace()
                    }
                }


            }

        }

        private val ENABLED_NOTIFICATION_LISTENERS = "enabled_notification_listeners"
        fun isNotificationListenersEnabled(context: Context): Boolean {
            val pkgName = context.packageName
            val flat = Settings.Secure.getString(context.contentResolver, ENABLED_NOTIFICATION_LISTENERS)
            if (!TextUtils.isEmpty(flat)) {
                val names = flat.split(":".toRegex()).dropLastWhile { it.isEmpty() }.toTypedArray()
                for (i in names.indices) {
                    val cn = ComponentName.unflattenFromString(names[i])
                    if (cn != null) {
                        if (TextUtils.equals(pkgName, cn.packageName)) {
                            return true
                        }
                    }
                }
            }
            return false
        }

        fun gotoNotificationAccessSetting(context: Context): Boolean {
            try {
                val intent = Intent("android.settings.ACTION_NOTIFICATION_LISTENER_SETTINGS")
                intent.addFlags(Intent.FLAG_ACTIVITY_NEW_TASK)
                context.startActivity(intent)
                return true

            } catch (e: ActivityNotFoundException) {//普通情况下找不到的时候需要再特殊处理找一次
                try {
                    val intent = Intent()
                    intent.addFlags(Intent.FLAG_ACTIVITY_NEW_TASK)
                    val cn = ComponentName("com.android.settings", "com.android.settings.Settings\$NotificationAccessSettingsActivity")
                    intent.component = cn
                    intent.putExtra(":settings:show_fragment", "NotificationAccessSettings")
                    context.startActivity(intent)
                    return true
                } catch (e1: Exception) {
                    e1.printStackTrace()
                }

                MyToastUtil.showError("对不起，您的手机暂不支持")
                e.printStackTrace()
                return false
            }

        }

        fun toggleNotificationListenerService(context: Context) {
            Log.e("PermissionUtil", "toggleNotificationListenerService")
            val pm = context.packageManager
            pm.setComponentEnabledSetting(
                ComponentName(context, TheNotificationListenerService::class.java),
                PackageManager.COMPONENT_ENABLED_STATE_DISABLED, PackageManager.DONT_KILL_APP
            )
            pm.setComponentEnabledSetting(
                ComponentName(context, TheNotificationListenerService::class.java),
                PackageManager.COMPONENT_ENABLED_STATE_ENABLED, PackageManager.DONT_KILL_APP
            )
        }


        fun NotificationPermission(context: Context): Boolean {
            val mAppOps = context.applicationContext.getSystemService(APP_OPS_SERVICE) as AppOpsManager
            val appInfo = context.applicationInfo
            val pkg = context.applicationContext.packageName
            val uid = appInfo.uid

            var appOpsClass: Class<*>? = null
            try {
                appOpsClass = Class.forName(AppOpsManager::class.java.name)
                val checkOpNoThrowMethod = appOpsClass!!.getMethod(
                    "checkOpNoThrow", Integer.TYPE, Integer.TYPE,
                    String::class.java
                )
                val opPostNotificationValue = appOpsClass.getDeclaredField("OP_POST_NOTIFICATION")
                val value = opPostNotificationValue.get(Int::class.java) as Int
                return checkOpNoThrowMethod.invoke(mAppOps, value, uid, pkg) as Int == AppOpsManager.MODE_ALLOWED
            } catch (e: ClassNotFoundException) {
                e.printStackTrace()
            } catch (e: NoSuchMethodException) {
                e.printStackTrace()
            } catch (e: NoSuchFieldException) {
                e.printStackTrace()
            } catch (e: InvocationTargetException) {
                e.printStackTrace()
            } catch (e: IllegalAccessException) {
                e.printStackTrace()
            }

            return false

        }

        fun hasPermissionVolume(context: Context): Boolean {
            val notificationManager = context.applicationContext.getSystemService(Context.NOTIFICATION_SERVICE) as NotificationManager
            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.M && !notificationManager.isNotificationPolicyAccessGranted) {
                return false
            }
            return true
        }

        fun openPermissionVolume(context: Context) {
            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.M) {
                try {
                    val intent = Intent(android.provider.Settings.ACTION_NOTIFICATION_POLICY_ACCESS_SETTINGS)
                    context.startActivity(intent)
                } catch (e: java.lang.Exception) {
                    MyToastUtil.showError("跳转失败，请自行设置。")
                }
            }
        }

        fun silentSwitchOn(context: Context) {
            val audioManager = context.applicationContext.getSystemService(Context.AUDIO_SERVICE) as AudioManager?
            if (audioManager != null) {
                audioManager.ringerMode = AudioManager.RINGER_MODE_SILENT
                audioManager.getStreamVolume(AudioManager.STREAM_RING)
                Log.d("SilentListenerService", "RINGING 已被静音")
            }
        }

        fun silentSwitchOff(context: Context) {
            val audioManager = context.applicationContext.getSystemService(Context.AUDIO_SERVICE) as AudioManager?
            if (audioManager != null) {
                audioManager.ringerMode = AudioManager.RINGER_MODE_NORMAL
                audioManager.getStreamVolume(AudioManager.STREAM_RING)
                Log.d("SilentListenerService", "RINGING 取消静音")
            }
        }


//        fun requestReadPhoneStatePermission(context: AppCompatActivity) {
//            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.M && Build.VERSION.SDK_INT < Build.VERSION_CODES.Q) {
//                val lackedPermission = ArrayList<String>()
//                if (context.checkSelfPermission(Manifest.permission.READ_PHONE_STATE) != PackageManager.PERMISSION_GRANTED) {
//                    lackedPermission.add(Manifest.permission.READ_PHONE_STATE)
//                }
//
//                if (lackedPermission.size > 0) {
//                    val requestPermissions = arrayOfNulls<String>(lackedPermission.size)
//                    lackedPermission.toArray(requestPermissions)
//                    context.requestPermissions(requestPermissions, 1024)
//                }
//            }

//        }


        val TAG = "PermissionUtil"
        fun isAccessibilitySettingsOn(accessibilityServiceName: String, context: Context): Boolean {
            if (MyAccessibilityService.getInstance() != null) {
                return true
            }

            var enable = 0
            try {
                enable = Settings.Secure.getInt(context.contentResolver, Settings.Secure.ACCESSIBILITY_ENABLED)
            } catch (e: Exception) {
                Log.e(TAG, "get accessibility enable failed, the err:" + e.message)
            }
            if (enable == 1) {
                val settingValue = Settings.Secure.getString(context.contentResolver, Settings.Secure.ENABLED_ACCESSIBILITY_SERVICES)
                if (settingValue == null || !settingValue.contains(accessibilityServiceName)) {
                    enable = 0
                }
            }
            return enable == 1
        }

//        fun isAccessibilitySettingsOn(accessibilityServiceName: String, context: Context): Boolean {
//            var accessibilityEnable = 0
//            val serviceName = context.packageName + "/" + accessibilityServiceName
//            try {
//                accessibilityEnable = Settings.Secure.getInt(context.contentResolver, Settings.Secure.ACCESSIBILITY_ENABLED, 0)
//            } catch (e: Exception) {
//                Log.e(TAG, "get accessibility enable failed, the err:" + e.message)
//            }
//
//            if (accessibilityEnable == 1) {
//                val mStringColonSplitter = SimpleStringSplitter(':')
//                val settingValue = Settings.Secure.getString(context.contentResolver, Settings.Secure.ENABLED_ACCESSIBILITY_SERVICES)
//                if (settingValue != null) {
//                    mStringColonSplitter.setString(settingValue)
//                    while (mStringColonSplitter.hasNext()) {
//                        val accessibilityService = mStringColonSplitter.next()
//                        if (accessibilityService.equals(serviceName, ignoreCase = true)) {
//                            Log.v(TAG, "We've found the correct setting - accessibility is switched on!")
//                            return true
//                        }
//                    }
//                }
//            } else {
//                Log.d(TAG, "Accessibility service disable")
//            }
//            return false
//        }

        fun openAccessibility(activity: AppCompatActivity) {
            if (MyRomUtils.isXiaomi()) {
//                MyToastUtil.showInfo("点击“更多已下载服务”，找到远离手机并开启")
                ImageDialog(activity).apply {
                    setTitle("操作说明")
                    setContent("请按图中顺序操作。")
                    setImageUrl("https://upic-1252369707.file.myqcloud.com/uPic/2023/02/20/11_12_08_nWyBhQ.png")
                    setGravity(Gravity.START)
                    isCancelable = false
                    setOnNormalOKClickListener("去设置", object : OnOKClickListener {
                        override fun onclick() {
                            val intent = Intent(Settings.ACTION_ACCESSIBILITY_SETTINGS)
                            activity.startActivity(intent)
                        }
                    })
                    setOnNormalCancelClickListener("取消", object : OnCancelClickListener {
                        override fun onclick() {

                        }
                    })
                    show()
                }

            } else if (MyRomUtils.isHuaweiSeries()) {
//                MyToastUtil.showInfo("点击“更多已下载服务”，找到远离手机并开启")
                ImageDialog(activity).apply {
                    setTitle("操作说明")
                    setContent("请按图中顺序操作。")
                    setImageUrl("https://upic-1252369707.file.myqcloud.com/uPic/2023/02/20/16_36_45_hoxVdE.png")
                    setGravity(Gravity.START)
                    isCancelable = false
                    setOnNormalOKClickListener("去设置", object : OnOKClickListener {
                        override fun onclick() {
                            val intent = Intent(Settings.ACTION_ACCESSIBILITY_SETTINGS)
                            activity.startActivity(intent)
                        }
                    })
                    setOnNormalCancelClickListener("取消", object : OnCancelClickListener {
                        override fun onclick() {

                        }
                    })
                    show()
                }

            } else {
                MyToastUtil.showInfo("向下滚动，找到远离手机并开启")
                val intent = Intent(Settings.ACTION_ACCESSIBILITY_SETTINGS)
                activity.startActivity(intent)
            }

        }

        //小米后台弹出权限
        fun isMiuiBackgroundAllowed(context: Context): Boolean {

            if (Build.VERSION.SDK_INT < Build.VERSION_CODES.M) {
                return true
            }

            if (MMKVUtils.getBoolean(MyConstants.SP_KEY_MIUI_BACK, false)) {
                return true
            }

            val ops = context.applicationContext.getSystemService(APP_OPS_SERVICE) as AppOpsManager
            try {
                val op = 10021
                val method: Method = ops.javaClass.getMethod(
                    "checkOpNoThrow",
                    *arrayOf<Class<*>?>(Int::class.javaPrimitiveType, Int::class.javaPrimitiveType, String::class.java)
                )
                val result = method.invoke(ops, op, Process.myUid(), context.applicationContext.packageName) as Int
//                LogUtils.d("wacao" + (result == AppOpsManager.MODE_ALLOWED))
                return result == AppOpsManager.MODE_ALLOWED
            } catch (e: java.lang.Exception) {
                Log.e(TAG, "not support")
//                LogUtils.d("wacao not support")
            }
            return false
        }

//        private fun isVivoBackgroundAllowed(context: Context): Boolean {
//            return getVivoBackgroundStatus(context) == 0
//        }
//
//        /**
//         * 判断Vivo后台弹出界面状态， 1无权限，0有权限
//         * @param context context
//         */
//        private fun getVivoBackgroundStatus(context: Context): Int {
//            val uri: Uri = Uri.parse("content://com.vivo.permissionmanager.provider.permission/start_bg_activity")
//            val selection = "pkgname = ?"
//            val selectionArgs = arrayOf(context.packageName)
//            var state = 1
//            try {
//                context.contentResolver.query(uri, null, selection, selectionArgs, null)?.use {
//                    if (it.moveToFirst()) {
//                        state = it.getInt(it.getColumnIndex("currentstate"))
//                    }
//                }
//            } catch (e: Exception) {
//                e.printStackTrace()
//            }
//            return state
//        }

//        fun canBackgroundStart(context: Context): Boolean {
//            val ops = context.getSystemService(APP_OPS_SERVICE) as AppOpsManager
//            try {
//                val op = 10021 // >= 23
//                // ops.checkOpNoThrow(op, uid, packageName)
//                val method = ops.javaClass.getMethod("checkOpNoThrow", *arrayOf<Class<*>?>(Int::class.javaPrimitiveType, Int::class.javaPrimitiveType, String::class.java))
//                val result = method.invoke(ops, op, Process.myUid(), context.packageName) as Int
//                return result == AppOpsManager.MODE_ALLOWED
//            } catch (e: java.lang.Exception) {
//                Log.e(TAG, "not support", e)
//            }
//            return false
//        }


        @RequiresApi(Build.VERSION_CODES.M)
        fun openOverlyPermission(context: Context) {
            val intent = Intent(Settings.ACTION_MANAGE_OVERLAY_PERMISSION)
            intent.flags = Intent.FLAG_ACTIVITY_NEW_TASK
            intent.data = Uri.parse("package:" + context.packageName)
            context.startActivity(intent)
        }

        fun openMiuiPrivacyPermissionEditor(context: Context) {

            val xiaomiBackGroundIntent = Intent()
            xiaomiBackGroundIntent.action = "miui.intent.action.APP_PERM_EDITOR"
            xiaomiBackGroundIntent.addCategory(Intent.CATEGORY_DEFAULT)
            xiaomiBackGroundIntent.putExtra("extra_pkgname", context.packageName)
            ActivityUtils.startActivity(xiaomiBackGroundIntent)
        }
    }
}