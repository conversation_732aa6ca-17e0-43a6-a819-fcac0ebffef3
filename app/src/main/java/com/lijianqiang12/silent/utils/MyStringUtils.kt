package com.lijianqiang12.silent.utils

import com.lijianqiang12.silent.utils.MMKVUtils
import com.lijianqiang12.silent.MyConstants
import java.util.*


fun getRandomString(length: Int): String {
    val str = "abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789"
    val random = Random()
    val sb = StringBuffer()
    for (i in 0 until length) {
        val number: Int = random.nextInt(62)
        sb.append(str[number])
    }
    return sb.toString()
}

fun getRandomIndexId(): String {
    return "${MMKVUtils.getInt(MyConstants.SP_KEY_USER_ID, -1)}_${System.currentTimeMillis()}_${getRandomString(10)}"
}