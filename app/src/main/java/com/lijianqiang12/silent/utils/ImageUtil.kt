package com.lijianqiang12.silent.utils

import android.app.Activity
import android.content.Context
import android.content.Intent
import android.graphics.*
import android.graphics.drawable.BitmapDrawable
import android.graphics.drawable.Drawable
import android.net.Uri
import android.os.Environment
import android.provider.MediaStore
import android.view.View
import android.webkit.MimeTypeMap
import androidx.appcompat.app.AppCompatActivity
import com.blankj.utilcode.util.ImageUtils
import com.blankj.utilcode.util.ScreenUtils
import com.lijianqiang12.silent.MyConstants
import com.lijianqiang12.silent.R
import com.lijianqiang12.silent.component.activity.custom.dialog.OnCancelClickListener
import com.lijianqiang12.silent.component.activity.custom.dialog.OnOKClickListener
import com.lijianqiang12.silent.component.activity.custom.dialog.VideoDialog
import java.io.File
import java.io.FileOutputStream
import java.io.IOException
import java.util.Calendar


class ImageUtil {
    companion object {

        fun saveWallpaperImage(context: Activity, drawable: Drawable) {
            try {
                val fileName = "wallpaper" + System.currentTimeMillis() + ".png"
                val fos = context.applicationContext.openFileOutput(fileName, Context.MODE_PRIVATE)
                (drawable as BitmapDrawable).bitmap.compress(Bitmap.CompressFormat.PNG, 100, fos)
                fos.close()
                MMKVUtils.put(MyConstants.SP_KEY_SETTING_WALLPAPER_PATH, context.applicationContext.filesDir.absolutePath + "/" + fileName)
            } catch (e1: IOException) {
                e1.printStackTrace()
            }
        }

        fun saveBgImage(context: Activity, drawable: Drawable): String {
            var path = ""
            try {
                val fileName = "lockBg" + System.currentTimeMillis() + ".png"
                val fos = context.applicationContext.openFileOutput(fileName, Context.MODE_PRIVATE)
                (drawable as BitmapDrawable).bitmap.compress(Bitmap.CompressFormat.PNG, 100, fos)
                fos.close()
                path = context.applicationContext.filesDir.absolutePath + "/" + fileName
            } catch (e1: IOException) {
                e1.printStackTrace()
            }

            return path
        }

        fun measureAndSave(view: View) {
            layoutView(view, ScreenUtils.getScreenWidth(), ScreenUtils.getScreenHeight())
        }

        private fun layoutView(v: View, width: Int, height: Int) {
            // 整个View的大小 参数是左上角 和右下角的坐标
            v.layout(0, 0, width, height)
            val measuredWidth = View.MeasureSpec.makeMeasureSpec(width, View.MeasureSpec.EXACTLY)
            val measuredHeight = View.MeasureSpec.makeMeasureSpec(10000, View.MeasureSpec.AT_MOST)
            /** 当然，measure完后，并不会实际改变View的尺寸，需要调用View.layout方法去进行布局。
             * 按示例调用layout函数后，View的大小将会变成你想要设置成的大小。
             */
            v.measure(measuredWidth, measuredHeight)
            v.layout(0, 0, v.measuredWidth, v.measuredHeight)
        }

        fun viewSaveToImage(context: Context, view: View) {
            view.isDrawingCacheEnabled = true
            view.drawingCacheQuality = View.DRAWING_CACHE_QUALITY_HIGH
            view.drawingCacheBackgroundColor = Color.WHITE // 把一个View转换成图片
            val cachebmp = loadBitmapFromView(view) // 添加水印
            val bitmap = Bitmap.createBitmap(createWatermarkBitmap(context, cachebmp, "远离手机"))
            val fos: FileOutputStream
            try {            // 判断手机设备是否有SD卡
                val isHasSDCard = Environment.getExternalStorageState() == Environment.MEDIA_MOUNTED
                fos = if (isHasSDCard) {                // SD卡根目录
                    val sdRoot = Environment.getExternalStorageDirectory()
                    val file = File(sdRoot, "share.PNG")
                    FileOutputStream(file)
                } else {
                    throw Exception("创建文件失败!")
                }
                bitmap.compress(Bitmap.CompressFormat.PNG, 90, fos)
                fos.flush()
                fos.close()
            } catch (e: Exception) {
                e.printStackTrace()
            }
            view.destroyDrawingCache()
        }

        fun loadBitmapFromView(v: View): Bitmap {
            val w = v.width
            val h = v.height
            val bmp = Bitmap.createBitmap(w, h, Bitmap.Config.ARGB_8888)
            val c = Canvas(bmp)
//            c.drawColor(Color.WHITE)
            //如果不设置canvas画布为白色，则生成透明
            v.layout(0, 0, w, h)
            v.draw(c)
            return bmp
        }

        // 为图片target添加水印
        fun createWatermarkBitmap(context: Context, target: Bitmap, str: String?): Bitmap {
            val w = target.width
            val h = target.height
            val bmp = Bitmap.createBitmap(w, h, Bitmap.Config.ARGB_8888)
            val canvas = Canvas(bmp)
            val p = Paint()
            p.color = context.resources.getColor(R.color.colorSecondaryText)
            p.textSize = 64f
            p.isAntiAlias = true // 去锯齿
            canvas.drawBitmap(target, 0f, 0f, p)
            val waterBitmap = BitmapFactory.decodeResource(context.resources, R.drawable.ic_download)
            canvas.drawBitmap(ImageUtils.compressBySampleSize(waterBitmap, 100, 100), w - 200.toFloat(), h - 200.toFloat(), null)
            canvas.drawText(str!!, w / 2 - 128.toFloat(), h - w / 12 - 32.toFloat(), p)
            canvas.save()
            canvas.restore()
            return bmp
        }


        fun openVideo(context: AppCompatActivity, url: String) {

            VideoDialog(context).apply {
                setTitle("视频教程")
                setContent(url)
                isCancelable = false
                setOnNormalOKClickListener("学会了", object : OnOKClickListener {
                    override fun onclick() {
                    }
                })
                setOnNormalCancelClickListener("使用系统播放器", object : OnCancelClickListener {
                    override fun onclick() {
                        val extension = MimeTypeMap.getFileExtensionFromUrl(url)
                        val mimeType = MimeTypeMap.getSingleton().getMimeTypeFromExtension(extension)
                        val mediaIntent = Intent(Intent.ACTION_VIEW)
                        mediaIntent.setDataAndType(Uri.parse(url), mimeType)
                        context.startActivity(mediaIntent)
                    }
                })
                show()
            }


        }

        fun shareImage(activity: AppCompatActivity, bitmap: Bitmap) {
            val intent = Intent()
            intent.setAction(Intent.ACTION_SEND)
            val uri = Uri.parse(MediaStore.Images.Media.insertImage(activity.contentResolver, bitmap, "IMG" + Calendar.getInstance().time, null))
            intent.setType("image/*")
            intent.putExtra(Intent.EXTRA_STREAM, uri)
            activity.startActivity(Intent.createChooser(intent, "title"))
        }

    }
}