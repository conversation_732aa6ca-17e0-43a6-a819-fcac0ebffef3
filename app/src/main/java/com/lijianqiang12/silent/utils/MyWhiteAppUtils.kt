package com.lijianqiang12.silent.utils

import com.blankj.utilcode.util.AppUtils
import com.lijianqiang12.silent.component.activity.AppLimitUnlockActivity
import com.lijianqiang12.silent.component.activity.FriendUnlockActivity
import com.lijianqiang12.silent.component.activity.PasswordUnlockActivity
import com.lijianqiang12.silent.component.activity.PayUnlockActivity
import com.lijianqiang12.silent.component.activity.TheLoginActivity
import com.lijianqiang12.silent.component.activity.TheMainActivity
import com.lijianqiang12.silent.component.activity.TheWebViewActivity
import com.lijianqiang12.silent.component.activity.me.vip.VIP2Activity
import com.lijianqiang12.silent.component.activity.me.vip.VIPActivity
// TODO: 暂时注释掉，因为使用最小化版本的BackgroundService
// import com.lijianqiang12.silent.component.service.background_service.BackgroundService
import com.lijianqiang12.silent.component.service.background_service.DeleteWhiteAppTemp
import com.lijianqiang12.silent.component.service.background_service.WhitePkgInfo
import com.lijianqiang12.silent.utils.floatwindowUtils.rom.RomUtils
import com.lijianqiang12.silent.wxapi.WXPayEntryActivity

val whitePageList = mutableListOf(
    WhitePkgInfo(AppUtils.getAppPackageName(), PayUnlockActivity::class.java.canonicalName!!.toString()),
    WhitePkgInfo(AppUtils.getAppPackageName(), PasswordUnlockActivity::class.java.canonicalName!!.toString()),
    WhitePkgInfo(AppUtils.getAppPackageName(), FriendUnlockActivity::class.java.canonicalName!!.toString()),
    WhitePkgInfo(AppUtils.getAppPackageName(), VIPActivity::class.java.canonicalName!!.toString()),
    WhitePkgInfo(AppUtils.getAppPackageName(), VIP2Activity::class.java.canonicalName!!.toString()),
    WhitePkgInfo(AppUtils.getAppPackageName(), WXPayEntryActivity::class.java.canonicalName!!.toString()),
    WhitePkgInfo(AppUtils.getAppPackageName(), TheWebViewActivity::class.java.canonicalName!!.toString()),
    WhitePkgInfo(AppUtils.getAppPackageName(), AppLimitUnlockActivity::class.java.canonicalName!!.toString()),
    WhitePkgInfo(AppUtils.getAppPackageName(), TheLoginActivity::class.java.canonicalName!!.toString()),


    //华为
    WhitePkgInfo("com.huawei.filemanager", ""),
    WhitePkgInfo("com.huawei.appmarket", "com.huawei.appgallery.distribution.impl.harmony.fadetail.FADistActivity"),
    WhitePkgInfo("com.huawei.appmarket", "com.huawei.appgallery.systeminstalldistservice.ui.activity.InstallDistActivity"),
    WhitePkgInfo("com.huawei.appmarket", "com.huawei.appmarket.service.thirdupdate.ThirdUpdateActivity"),
    WhitePkgInfo("com.huawei.audioaccessorymanager", ""),
    WhitePkgInfo("com.android.bluetooth", ""),
    WhitePkgInfo("com.huawei.fido.uafclient", ""),
    WhitePkgInfo("com.huawei.iconnect.ui", ""),
    WhitePkgInfo("com.huawei.android.internal.app", ""),
    WhitePkgInfo("com.huawei.android.internal.app", "com.huawei.android.internal.app.HwResolverActivity"),
    WhitePkgInfo("com.huawei.android.internal.app", "com.huawei.android.internal.app.instantshare.HwInstantShareActivity"),//HuaweiShare
    WhitePkgInfo("com.android.deskclock", ""),//闹钟
    WhitePkgInfo("com.android.deskclock", "com.android.deskclock.alarms.AlarmActivity"),//闹钟
    WhitePkgInfo("com.android.deskclock", "com.android.deskclock.alarmclock.LockAlarmFullActivity"),//闹钟
    WhitePkgInfo("com.android.deskclock", "com.android.deskclock.AlarmsMainActivity"),//闹钟
    WhitePkgInfo("com.huawei.android.internal.app", "com.huawei.android.internal.app.HwChooserActivity"),//双开和分享
    WhitePkgInfo("com.huawei.systemmanager", "com.huawei.securitycenter.applock.password.AuthLaunchLockedAppActivity"),//应用锁密码界面
    WhitePkgInfo("com.huawei.systemmanager", "com.huawei.systemmanager.applock.password.AuthLaunchLockedAppActivity"),//应用锁密码界面
    WhitePkgInfo("com.baidu.input_huawei", "com.baidu.input.mpermissions.ImePermissionActivity"),//新建联系人时弹窗
    WhitePkgInfo("com.huawei.hwid", ""),//支付
    WhitePkgInfo("com.huawei.hwpreview", ""),//锁机预览
    WhitePkgInfo("com.android.systemui", "com.android.systemui.flashlight.FlashlightActivity"),//手电筒
    WhitePkgInfo("com.huawei.hitouch", ""),//智慧识屏
    WhitePkgInfo("com.huawei.parentcontrol", "com.huawei.parentcontrol.timeover.TimeOutActivity"),//健康使用手机
    WhitePkgInfo("com.huawei.HwMultiScreenShot", "com.huawei.HwMultiScreenShot.PreviewActivity"),//滚动截屏
    WhitePkgInfo("com.qeexo.smartshot", "com.qeexo.smartshot.CropActivity"),//智能截屏
    WhitePkgInfo("com.android.phone", "com.android.phone.MSimMobileNetworkSettings"),//拨号服务
    WhitePkgInfo("com.huawei.himovie", "com.huawei.hwvplayer.ui.FullscreenActivity"),//华为视频
    WhitePkgInfo("com.huawei.himovie", "com.huawei.component.localplay.impl.ui.FullscreenActivity"),//华为视频com.huawei.him
    WhitePkgInfo("com.huawei.himovie.local", "com.huawei.component.localplay.impl.ui.FullscreenActivity"),//华为视频com.huawei.him
    WhitePkgInfo("com.huawei.hwvplayer.youku", "com.huawei.hwvplayer.service.player.FullscreenActivity"),//华为视频
    WhitePkgInfo("com.huawei.scanner", ""),//华为智慧视觉
    WhitePkgInfo("com.huawei.hiwrite", ""),//华为全局手写
    WhitePkgInfo("com.huawei.wallet", "com.huawei.nfc.carrera.ui.swipe.SwipeActivity"),//华为智慧视觉
    WhitePkgInfo("com.android.deskclock", "com.android.deskclock.alarmclock.SetAlarm"),//闹钟
    WhitePkgInfo("com.android.deskclock", "com.android.deskclock.DeskClockTabActivity"),//时钟
    WhitePkgInfo("com.huawei.android.instantshare", ""),//华为分享
    WhitePkgInfo("com.huawei.android.remotecontroller", ""),//华为遥控器
    WhitePkgInfo("com.huawei.smartshot", ""),//智慧截屏
    WhitePkgInfo("com.huawei.securitymgr", "com.huawei.keychain.ui.HwKeychainSaveActivity"),//密码保险箱
    WhitePkgInfo("com.huawei.android.thememanager", ""),//主题设置
    WhitePkgInfo("com.huawei.dsdscardmanager", ""),//sim卡管理
    WhitePkgInfo("com.huawei.desktop.explorer", ""),//我的文件,共享屏幕
    WhitePkgInfo("com.huawei.desklock", ""),//时钟
    WhitePkgInfo("com.huawei.photos", ""),//图库
    WhitePkgInfo("com.huawei.email", ""),
    WhitePkgInfo("com.huawei.coauthservice", ""), // 用户认证
    WhitePkgInfo("com.huawei.wallet", ""),
    WhitePkgInfo("com.android.gallery3d", ""),//图库
    WhitePkgInfo("com.huawei.camera", ""),//相机
    WhitePkgInfo("com.huawei.HwMultiScreenShot", ""),//滚动截屏
    WhitePkgInfo("com.huawei.iconnect", ""),//设备连接服务
    WhitePkgInfo("com.huawei.contacts", ""),//拨号
    WhitePkgInfo("com.unionpay.tsmservice", ""),//支付控件
    WhitePkgInfo("com.huawei.imedia.sws", ""),//Histen，音效设置
    WhitePkgInfo("com.huawei.motionservice", ""),//手势服务
    WhitePkgInfo("com.huawei.parentcontrol", ""),//健康使用手机
    WhitePkgInfo("com.huawei.printservice", ""),//打印机
    WhitePkgInfo("com.huawei.associateassistant", ""),//多屏协同
    WhitePkgInfo("com.huawei.contactscamcard", ""),//扫名片
    WhitePkgInfo("com.android.settings", "com.android.settings.Settings\$AndroidBeamSettingsActivity"),//nfc
    WhitePkgInfo("com.huawei.mediacontroller", ""),//媒体播控中心
    WhitePkgInfo("com.huawei.screenrecoder", ""),//录屏
    WhitePkgInfo("com.huawei.pcassistant", ""),//华为分享
    WhitePkgInfo("com.huawei.numberidentity", ""),//号码识别
    WhitePkgInfo("com.huawei.hwdockbar", ""),//侧边栏
    WhitePkgInfo("com.huawei.systemmanager", "com.huawei.systemmanager.applock.password.LockScreenLaunchLockedAppActivity"),
    WhitePkgInfo("com.android.permissioncontroller", "com.huawei.permissioncontroller.hwcust.appjump.AppJumpActivity"),//华为跳转其他软件
    WhitePkgInfo("com.android.permissioncontroller", "com.huawei.android.permissioncontroller.hwcust.appjump.AppJumpActivity"),//华为跳转其他软件
    WhitePkgInfo("com.android.permissioncontroller", "com.huawei.permissioncontroller.hwcust.hwpermission.ui.GrantPermissionCountdownActivity"),//权限控制器

    //荣耀
    WhitePkgInfo("com.hihonor.android.internal.app", "com.hihonor.android.internal.app.HwChooserActivity"),//荣耀双开
    WhitePkgInfo("com.hihonor.android.internal.app", "com.hihonor.android.internal.app.HwResolverActivity"),//荣耀双开
    WhitePkgInfo("com.hihonor.android.internal.app", "com.hihonor.android.internal.app.instantshare.HwInstantShareActivity"),//荣耀share
    WhitePkgInfo("com.hihonor.desktop.explorer", ""),//荣耀投屏
    WhitePkgInfo("com.hihonor.HnMultiScreenShot", ""),
    WhitePkgInfo("com.hihonor.dsdscardmanager", ""), // SIM卡管理
    WhitePkgInfo("com.hihonor.hnstartupguide", ""),
    WhitePkgInfo("com.miui.systemAdSolution",""),
    WhitePkgInfo("com.hihonor.photos", ""),
    WhitePkgInfo("com.hihonor.imedia.sws", ""), //音频切换
    WhitePkgInfo("com.hihonor.videoeditor", ""),
    WhitePkgInfo("com.hihonor.hnasm", ""), //fido uaf asm
    WhitePkgInfo("com.hihonor.hnoffice", ""),
    WhitePkgInfo("com.hihonor.parentcontrol", ""),
    WhitePkgInfo("com.hihonor.keychain", ""),
    WhitePkgInfo("com.hihonor.coauthservice", ""),
    WhitePkgInfo("com.hihonor.android.upgradeguide", ""),
    WhitePkgInfo("com.hihonor.lens", ""),//荣耀相机
    WhitePkgInfo("com.hihonor.hndockbar", ""),//侧边栏
    WhitePkgInfo("com.hihonor.iconnect", ""),//设备连接服务
    WhitePkgInfo("com.hihonor.systemmanager", "com.hihonor.securitycenter.appads.AppSplashAdvertiseActivity"),//荣耀
    WhitePkgInfo("com.hihonor.systemmanager", "com.huawei.securitycenter.appads.AppSplashAdvertiseActivity"),//荣耀
    WhitePkgInfo("com.hihonor.systemmanager", "com.hihonor.securitycenter.applock.password.AuthLaunchLockedAppActivity"),//荣耀应用锁
    WhitePkgInfo("com.hihonor.systemmanager", "com.hihonor.securitycenter.applock.password.LockScreenLaunchLockedAppActivity"),//荣耀应用锁

    //小米
    WhitePkgInfo("com.android.deskclock", "com.android.deskclock.alarm.alert.AlarmAlertFullScreenActivity"),//闹钟
    WhitePkgInfo("android", "com.android.internal.app.ResolverActivity"),//双开
    WhitePkgInfo("com.lbe.security.miui", "com.android.packageinstaller.permission.ui.GrantPermissionsActivity"),//权限
    WhitePkgInfo("com.miui.securitycenter", "com.miui.applicationlock.ConfirmAccessControl"),//应用锁密码界面
    WhitePkgInfo("com.miui.securitycenter", "com.miui.wakepath.ui.ConfirmStartActivity"),//申请启动其他软件
    WhitePkgInfo("com.miui.securitycenter", "com.miui.permcenter.permissions.SystemAppPermissionDialogActivity"),//
    WhitePkgInfo("com.miui.securitycenter", "com.miui.antispam.ui.activity.MainActivity"),//
    WhitePkgInfo("com.miui.securitycore", "com.miui.xspace.ui.activity.XSpaceResolveActivity"),//双开
    WhitePkgInfo("com.miui.securitycore", "com.miui.xspace.ui.activity.XSpaceDefaultAppActivity"),//双开
    WhitePkgInfo("com.miui.tsmclient", ""),//刷卡
//    WhitePkgInfo("com.miui.gallery", "com.miui.gallery.activity.HomePageActivity"),//相册
    WhitePkgInfo("com.miui.gallery", "com.miui.gallery.activity.ExternalPhotoPageActivity"),//相册
    WhitePkgInfo("com.miui.gallery", "com.miui.gallery.picker.PickGalleryActivity"),//相册
    WhitePkgInfo("com.miui.gallery", "com.miui.gallery.activity.HomePageActivity"),//相册
    WhitePkgInfo("com.miui.gallery", "com.miui.gallery.activity.InternalPhotoPageActivity"),//相册
    WhitePkgInfo("com.miui.video", "com.miui.video.gallery.galleryvideo.FrameLocalPlayActivity"),//视频
    WhitePkgInfo("com.miui.video", "com.miui.video.localvideoplayer.LocalPlayerActivity"),//视频
    WhitePkgInfo("com.miui.video", "com.miui.videoplayer.VideoPlayerActivity"),//视频
    WhitePkgInfo("com.miui.video", "com.miui.video.gallery.galleryvideo.FrameLocalPlayActivity"),//视频
    WhitePkgInfo("com.miui.virtualsim", "com.miui.flow.main.XFMainActivity"),//全球上网
    WhitePkgInfo("com.miui.virtualsim", "com.miui.virtualsim.ui.MainActivity"),//全球上网
    WhitePkgInfo("com.xiaomi.aiasst.service", ""),//AI电话
    WhitePkgInfo("com.xiaomi.account", ""),//小米账号
    WhitePkgInfo("cn.wps.moffice.lite", ""),//（WPS OPPO定制）
    WhitePkgInfo("cn.wps.moffice_eng.xiaomi.lite", ""),//小米文档查看器（WPS定制）
    WhitePkgInfo("cn.wps.moffice_eng", ""),//（WPS定制）
    WhitePkgInfo("com.miui.notification", ""),//通知管理
    WhitePkgInfo("com.milink.service", ""),//投屏
    WhitePkgInfo("com.miui.cleanmaster", ""),//垃圾清理
    WhitePkgInfo("com.miui.cloudservice", ""),//云服务
    WhitePkgInfo("com.xiaomi.gamecenter.sdk.service", ""),//找不到添加的位置
    WhitePkgInfo("com.xiaomi.misettings", ""),//专注模式
    WhitePkgInfo("com.xiaomi.mslgradp", ""),//PC框架
    WhitePkgInfo("com.miui.mediaeditor", ""),//相册编辑
    WhitePkgInfo("com.miui.mediaviewer", ""),//媒体查看器
    WhitePkgInfo("android", "com.android.internal.app.MiuiResolverActivity"),//miui双开弹窗
    WhitePkgInfo("com.miui.packageinstaller", ""),//安装
    WhitePkgInfo("com.miui.extraphoto", ""),//焦外成像
    WhitePkgInfo("com.miui.screenshot", ""),//截图
    WhitePkgInfo("com.miui.misound", ""),//音质特效
    WhitePkgInfo("com.miui.newmidrive", ""),//小米云盘
    WhitePkgInfo("com.miui.contentextension", ""),//传送门
    WhitePkgInfo("com.fido.xiaomi.uafclient", ""),//线上快速身份验证联盟
    WhitePkgInfo("com.xiaomi.mirror", ""),//Miui+ 协同
    WhitePkgInfo("com.xiaomi.bluetooth", ""),//蓝牙
    WhitePkgInfo("com.mipay.wallet", ""),//
    WhitePkgInfo("com.sohu.inputmethod.sogou.xiaomi", ""),//小米自带搜狗输入法
    WhitePkgInfo("com.android.settings", "com.android.settings.bluetooth.RequestPermissionHelperActivity"),//蓝牙权限申请
    WhitePkgInfo("miui.systemui.plugin", "com.android.systemui.miui.volume.SafeWarningActivity"),//小米耳机音量过大提示


    //oppo
    WhitePkgInfo("com.android.providers.media.module", "com.android.providers.media.photopicker.PhotoPickerActivity"),
    WhitePkgInfo("com.coloros.colordirectservice", ""),//识屏服务
    WhitePkgInfo("com.coloros.childrenspace", ""),//
    WhitePkgInfo("com.oplus.account", ""),//
    WhitePkgInfo("com.oplus.screenrecorder", ""),//屏幕录制
    WhitePkgInfo("com.oplus.screenshot", ""),//屏幕截图
    WhitePkgInfo("com.oplus.ota", ""),//
    WhitePkgInfo("com.android.settings", "com.oplus.settings.privacy.ChooseGenericPrivacy"),//便签-私密笔记-设置密码
    WhitePkgInfo("com.android.settings", "com.oplus.settings.privacy.ConfirmNumberPrivacy"),//便签-私密笔记-输入密码
    WhitePkgInfo("com.coloros.alarmclock", ""),//闹钟
    WhitePkgInfo("com.coloros.safecenter", "com.coloros.safecenter.privacy.view.password.AppUnlockPasswordActivity"),//应用锁
    WhitePkgInfo("com.coloros.safecenter", "com.coloros.safecenter.privacy.view.password.AppUnlockPasswordWhiteBgActivity"),//应用锁
    WhitePkgInfo("com.coloros.safecenter", "com.coloros.safecenter.privacy.view.password.AppUnlockPatternActivity"),//应用锁
    WhitePkgInfo("com.coloros.safecenter", "com.coloros.safecenter.privacy.view.password.AppUnlockPatternWhiteBgActivity"),//应用锁
    WhitePkgInfo("com.coloros.safecenter", "com.coloros.safecenter.privacy.view.password.AppUnlockComplexActivity"),//应用锁
    WhitePkgInfo("com.coloros.safecenter", "com.coloros.safecenter.privacy.view.password.AppUnlockComplexWhiteBgActivity"),//应用锁
    WhitePkgInfo("com.coloros.safecenter", "com.coloros.safecenter.sysfloatwindow.FloatWindowListActivity"),//应用锁
    WhitePkgInfo("com.coloros.directui", "com.coloros.directui.CardPanelImplActivity"),//智慧识屏
    WhitePkgInfo("com.coloros.wirelesssettings", ""),//无线设置
    WhitePkgInfo("com.coloros.simsettings", "com.coloros.datamonitor.ui.TrafficOverDailyRemindAndCloseDialogActivity"),//双卡与移动网络
    WhitePkgInfo("com.coloros.photoeffects", ""),//图片特效
    WhitePkgInfo("com.coloros.video", "com.oppo.video.mycenter.MoviePlayerActivity"),//视频播放器
    WhitePkgInfo("com.coloros.securepay", ""),//支付保护
    WhitePkgInfo("com.coloros.gallery3d", ""),//相册
//    WhitePkgInfo("com.coloros.gallery3d", "com.oppo.gallery3d.app.Gallery"),//相册
    WhitePkgInfo("com.coloros.simsettings", "com.coloros.simsettings.OppoSimSettingsActivity"),//双卡与移动网络
    WhitePkgInfo("com.heytap.htms", ""),//移动服务
    WhitePkgInfo("com.heytap.market", "com.downloader.page.ui.main.activity.DownloaderPageActivity"),//商店更新
    WhitePkgInfo("com.coloros.digitalwellbeing", ""),//应用限时
    WhitePkgInfo("com.coloros.fingerprint", ""),//锁屏密码
    WhitePkgInfo("com.coloros.filemanager", ""),//文件管理
    WhitePkgInfo("com.coloros.eyeprotect", ""),//护眼
    WhitePkgInfo("com.coloros.blacklistapp", ""),//
    WhitePkgInfo("com.coloros.focusmode", ""),//
    WhitePkgInfo("com.coloros.oshare", ""),//分享
    WhitePkgInfo("com.google.android.gms", ""),//
    WhitePkgInfo("com.google.android.captiveportallogin", ""),//
    WhitePkgInfo("com.mgoogle.android.gms", ""),//
    WhitePkgInfo("com.nearme.atlas", ""),//安全支付
    WhitePkgInfo("com.onyx.kreader", ""),//neo reader
    WhitePkgInfo("com.colors.codebook", "com.colors.codebook.book.pad.AuthenticateActivity"),

    //vivo 闹钟只弹窗不全屏
    WhitePkgInfo("com.vivo.doubleinstance", "com.vivo.doubleinstance.DoubleAppResolverActivity"),//android15双开
    WhitePkgInfo("android", "com.android.internal.app.DoubleAppResolverActivity"),//双开
    WhitePkgInfo("com.vivo.doubleinstance", "com.vivo.doubleinstance.DoubleAppResolverActivity"),//双开
    WhitePkgInfo("com.android.packageinstaller", "com.android.packageinstaller.permission.ui.GrantPermissionsActivity"),//权限
    WhitePkgInfo("com.android.settings", "com.vivo.settings.secret.PasswordActivity"),//应用锁密码界面
    WhitePkgInfo("com.android.settings", "com.android.settings.Settings\$ConnectedDeviceDashboardActivity"),//nfc关联的设备
    WhitePkgInfo("com.vivo.vtouch", ""),//智慧相关
    WhitePkgInfo("com.vivo.xspace", ""),//原子隐私系统
    WhitePkgInfo("com.android.documentsui", ""),//文件选择
    WhitePkgInfo("com.vivo.securedaemonservice", "com.iqoo.secure.safeguard.PasswordActivity"),//i管家
    WhitePkgInfo("com.vivo.smartshot", "com.vivo.smartshot.ui.FastShotPreviewActivity"),//超级截屏
    WhitePkgInfo("com.android.photoeditor", "com.android.gallery3d.filtershow.FilterShowActivity"),//编辑照片
    WhitePkgInfo("com.android.VideoPlayer", "com.android.VideoPlayer.MovieViewPublicActivity"),//视频
    WhitePkgInfo("com.vivo.videoeditor", ""),//视频编辑器
    WhitePkgInfo("com.vivo.symmetry", ""),//摄影
    WhitePkgInfo("com.vivo.smartanswer", "com.vivo.smartanswer.ui.record.SmartAnswerRecordActivity"),//电话秘书
    WhitePkgInfo("com.vivo.gallery", ""),//相册
    WhitePkgInfo("com.vivo.gallery", "com.android.gallery3d.app.Gallery"),//相册
    WhitePkgInfo("com.android.tethersettings", ""),//个人热点
    WhitePkgInfo("com.android.bluetoothsettings", ""),
    WhitePkgInfo("com.vivo.fingerprint", ""),//指纹
    WhitePkgInfo("com.vivo.sdkplugin", "com.vivo.sdkplugin.core.compunctions.activity.UnionActivity"),//vivo服务安全插件
    WhitePkgInfo("com.vivo.globalsearch", "com.vivo.globalsearch.SearchActivity"),//全局搜索
    WhitePkgInfo("com.yozo.vivo.office", ""),//vivo文档阅读器
    WhitePkgInfo("com.vivo.smartoffice", ""),//原子笔记
    WhitePkgInfo("com.vivo.appfilter", ""),//打开白名单app时弹窗
    WhitePkgInfo("com.vivo.familycare.local", ""),//健康使用手机
    WhitePkgInfo("com.vivo.defaultPlayer", ""),//默认播放器
    WhitePkgInfo("com.vivo.childrenmode", ""),//
    WhitePkgInfo("com.vivo.wallet", ""),//
    WhitePkgInfo("com.vivo.systemuiplugin", "com.vivo.systemuiplugin.systemui.volume.VivoVolumeDialogImpl\$BriefDialog"),//vivo耳机音量过大提示
    WhitePkgInfo("com.android.vivo.tws.vivotws", ""),//蓝牙耳机
    WhitePkgInfo("com.android.settings", "com.android.settings.Settings\$WifiTetherSettingsActivity"),//热点
    WhitePkgInfo("com.android.settings", "com.vivo.settings.BBKNfcSettingsTwo"),//NFC
    WhitePkgInfo("com.android.settings", "com.vivo.settings.secret.softwarelock.core.verifier.activity.SoftwareVerifierActivity"),//便签加密内容密码输入界面
    WhitePkgInfo("com.android.settings", "com.android.settings.Settings\$MobileNetworkActivity"),//流量，vivo上跳过去会立刻关闭，原因未知
    WhitePkgInfo("com.android.settings", "com.android.settings.Settings\$VivoWifiSettingsActivity"),//
    WhitePkgInfo("com.android.settings", "com.android.bluetoothsettings.Settings\$BluetoothSettingsActivity"),//
    WhitePkgInfo("com.android.settings", "com.android.settings.Settings\$VivoTetherSettingsActivity"),//
    WhitePkgInfo("com.android.settings", "com.vivo.settings.nfc.BBKNfcSettingsTwo"),//
    WhitePkgInfo("com.android.settings", "com.vivo.settings.secret.ChooseSecretLockGeneric"),//vivo密码锁
    WhitePkgInfo("com.android.settings", "com.vivo.settings.secret.ConfirmSecretPinNoTitle"),//vivo密码锁

    //步步高
    WhitePkgInfo("com.android.BBKCrontab", ""),//定时任务
    WhitePkgInfo("com.android.BBKClock", ""),//闹钟
    WhitePkgInfo("com.bbk.account", ""),//账号

    //中兴
    WhitePkgInfo("com.zte.cn.doubleapp", ""),//分身选择
    WhitePkgInfo("com.zui.filemanager", ""),//
    WhitePkgInfo("android", "com.android.internal.app.ResolverActivityZTE"),//分身选择

    //三星
    WhitePkgInfo("com.samsung.android.service.airviewdictionary", ""),//翻译
    WhitePkgInfo("com.samsung.android.incallui", ""),//接电话
    WhitePkgInfo("com.sec.android.mimage.photoretouching", ""),//图片编辑器
    WhitePkgInfo("com.samsung.android.video", ""),//视频
    WhitePkgInfo("com.samsung.android.apps.smartcapture", ""),//截屏
    WhitePkgInfo("com.sec.android.app.clockpackage", "com.sec.android.app.clockpackage.alarm.activity.AlarmAlertActivity"),//闹钟
    WhitePkgInfo("com.samsung.android.applock", ""),//应用锁
    WhitePkgInfo("com.android.settings", "com.android.settings.Settings\$AirplaneModeSettingsActivity"),//飞行模式
    WhitePkgInfo("com.android.phone", "com.samsung.telephonyui.activities.SamsungMobileNetworkSettingsActivity"),//网络设置
    WhitePkgInfo("com.samsung.android.app.telephonyui", "com.samsung.android.app.telephonyui.netsettings.ui.NetSettingsActivity"),//网络设置
    WhitePkgInfo("com.samsung.android.geargplugin", ""),//手表设置
    WhitePkgInfo("com.samsung.android.app.smartcapture", ""),//截屏
    WhitePkgInfo("com.samsung.accessory.zenithmgr", ""),//Galaxy Buds2 Pro Manager
    WhitePkgInfo("com.samsung.android.permissioncontroller", "com.samsung.android.permissioncontroller.permission.ui.GrantPermissionsActivity"),//权限控制器

    //锤子
    WhitePkgInfo("com.smartisanos.textboom", ""),//大爆炸
    WhitePkgInfo("smartisanos", "smartisanos.app.DoppelgangerChooseActivity"),//双开
    WhitePkgInfo("android", "smartisanos.app.DoppelgangerChooseActivity"),//双开

    //魅族
    WhitePkgInfo("com.android.settings", "com.meizu.settings.MzSettingsActivity\$EnterPasswordActivity"),//应用锁
    WhitePkgInfo("com.android.settings", "com.meizu.settings.MzSettingsActivity\$MzConfirmPasswordActivity"),//应用锁
    WhitePkgInfo("com.meizu.picker", ""),//Aicy 识屏
    WhitePkgInfo("com.meizu.flyme.sdkstage", "com.meizu.flyme.sdkstage.activity.TranslationActivity"),//Aicy 识屏翻译
    WhitePkgInfo("com.meizu.connectivitysettings", ""),//网络设置
    WhitePkgInfo("android", "com.android.internal.app.MzResolverActivity"),//Flyme系统服务
    WhitePkgInfo("com.meizu.wifiadmin", "com.meizu.wifiadmin.settings.WifiSettingsActivity"),//wifi服务
    WhitePkgInfo("android", "com.meizu.app.AccessApplication"),//蓝牙服务
    WhitePkgInfo("android", "com.android.internal.app.AppSwitchWarningActivity"),//flyme唤醒其它app的弹窗
    WhitePkgInfo("com.android.permissioncontroller", "com.meizu.safe.newpermission.ui.ReviewNotificationPermissionActivity"),//应用互相唤醒 or 权限申请
    WhitePkgInfo("com.meizu.safe", "com.meizu.safe.blockService.blockui.BlockServiceMainActivity"),//手机管家中的某些页面
    WhitePkgInfo("com.meizu.remotecooperation", ""),//远程协助

    //索尼
    WhitePkgInfo("com.sonymobile.smartcharger", "com.sonymobile.smartcharger.SmartChargeSettings"),//Aicy 识屏

    //酷派

    //联想
    WhitePkgInfo("com.zui.resolver", "com.zui.resolver.ListUserActivity"),//分身选择
    WhitePkgInfo("com.zui.game.service", ""),//游戏助手

    //努比亚
    WhitePkgInfo("cn.nubia.applockmanager", "cn.nubia.applockmanager.AppLockActivity"),//应用锁
    WhitePkgInfo("cn.nubia.photoeditor", ""),//图片编辑器

    //一加
    WhitePkgInfo("com.oneplus.applocker", ""),//一加应用锁
    WhitePkgInfo("com.oneplus.wifiapsettings", ""),//wifi设置
    WhitePkgInfo("com.android.intentresolver", ""),//双开

    //android系统界面
    WhitePkgInfo("com.android.incallui", ""),//来电界面
    WhitePkgInfo("com.android.dialer", ""),//来电界面
    WhitePkgInfo("com.android.packageinstaller", ""),//打包安装程序
    WhitePkgInfo("com.android.stk", ""),//USIM卡应用
    WhitePkgInfo("android", "com.android.internal.app.ChooserActivity"),//分身选择框、分享选择框
    WhitePkgInfo("com.android.systemui", "com.android.systemui.chooser.ChooserActivity"),
    WhitePkgInfo("com.android.settings", "com.android.settings.connecteddevice.usb.UsbMobileChooserActivity"),//usb选项
    WhitePkgInfo("com.android.settings", "com.android.settings.fuelgauge.RequestIgnoreBatteryOptimizations"),//电池权限管理
    WhitePkgInfo("com.android.permissioncontroller", "com.android.packageinstaller.permission.ui.GrantPermissionsActivity"),//权限
    WhitePkgInfo("com.android.permissioncontroller", "com.android.permissioncontroller.permission.ui.GrantPermissionsActivity"),//权限
    WhitePkgInfo("com.google.android.gms", "com.google.android.location.settings.LocationSettingsCheckerActivity"),//google导航
    WhitePkgInfo("com.android.bluetoothsettings", "com.android.bluetoothsettings.bluetooth.RequestPermissionActivity"),//蓝牙设置
    WhitePkgInfo("com.android.wifisettings", ""),//wifi设置
    WhitePkgInfo("com.android.settings", "com.android.settings.Settings\$WifiSettingsActivity"),//wifi设置
    WhitePkgInfo("com.android.settings", "com.android.wifiSettings.Settings\$WifiSettingsActivity"),//wifi设置
    WhitePkgInfo("com.android.phone", "com.android.phone.settings.CallFeaturesSetting"),//电话服务
    WhitePkgInfo("com.android.documentsui", ""),//文件
    WhitePkgInfo("com.android.systemui", "com.android.systemui.media.MediaProjectionPermissionActivity"),//跨应用查词时显示
    WhitePkgInfo("com.android.providers.downloads.ui", "com.android.providers.downloads.ui.activity.BrowserDownloadActivity"),//下载管理
    WhitePkgInfo("com.android.providers.downloads", "com.android.providers.downloads.DDHandler"),//下载管理

    WhitePkgInfo("com.android.settings", "com.android.settings.Settings\$WifiApSettingsActivity"),//wifi设置
    WhitePkgInfo("com.android.settings", "com.android.settings.Settings\$TetherWifiSettingsActivity"),//wifi设置
    WhitePkgInfo("com.lge.wifisettings", ""),//wifi设置

    WhitePkgInfo("com.android.settings", "com.android.settings.Settings\$WirelessSettingsActivity"),//网络设置
    WhitePkgInfo("com.android.phone", ""),//电话服务

    WhitePkgInfo("com.android.settings", "com.android.settings.bluetooth.BluetoothPairingDialog"),//蓝牙配对弹窗
    WhitePkgInfo("com.android.settings", "com.android.settings.Settings\$BluetoothSettingsActivity"),//蓝牙设置
    WhitePkgInfo("com.android.settings", "com.android.settings.Settings\$DeviceProfilesSettingsActivity"),//蓝牙设置
    WhitePkgInfo("com.android.bluetoothsettings", "com.android.bluetoothsettings.Settings\$BluetoothSettingsActivity"),//蓝牙设置

    WhitePkgInfo("com.android.settings", "com.android.settings.Settings\$DisplaySettingsActivity"),//亮度设置

    WhitePkgInfo("com.android.settings", "com.android.settings.Settings"),//飞行模式设置
    WhitePkgInfo("com.android.htmlviewer", "com.android.settings.wifi.openwifi.OpenWifiLogin"),//html查看器-公共网络
    WhitePkgInfo("com.android.settings", "com.android.settings.wifi.WifiSettingsActivity"),//wifi设置
    WhitePkgInfo("com.android.settings", "com.android.settings.bluetooth.BluetoothSettingsActivity"),//蓝牙设置
    WhitePkgInfo("com.android.captiveportallogin", ""),//wifi认证页面
    WhitePkgInfo("com.google.android.documentsui", ""),//文件
    WhitePkgInfo("com.android.contacts", ""),//联系人
    WhitePkgInfo("com.android.server.telecom", ""),//电话服务
    WhitePkgInfo("com.android.calendar", ""),//日历
    WhitePkgInfo("com.android.camera", ""),//相机
    WhitePkgInfo("com.mediatek.camera", ""),//相机
    WhitePkgInfo("com.android.mms", ""),//信息
    WhitePkgInfo("com.android.systemui", "com.android.systemui.usb.UsbConfirmActivity"),//插入耳机的提示
    WhitePkgInfo("com.android.systemui", "com.android.systemui.usb.UsbPermissionActivity"),//插入usb的提示
    WhitePkgInfo("com.android.systemui", "com.android.systemui.pip.phone.PipMenuActivity"),
    WhitePkgInfo("com.android.systemui", "com.android.systemui.clipboardoverlay.OplusEditTextActivity"),
    WhitePkgInfo("android", "com.android.internal.app.DualProcessResolverActivity"),//分享
    WhitePkgInfo("android", "com.android.internal.app.PlatLogoActivity"),
    WhitePkgInfo("android", "com.android.internal.app.IntentForwarderActivity"),
    WhitePkgInfo("com.android.calculator2", ""),//计算器
    WhitePkgInfo("com.android.printspooler", ""),//打印
    WhitePkgInfo("com.android.settings", "com.android.settings.password.ConfirmDeviceCredentialActivity"),//微软app锁
    WhitePkgInfo("com.google.android.gms", ""),//谷歌服务
    WhitePkgInfo("com.google.android.apps.wellbeing", "com.google.android.apps.wellbeing.settings.SettingsActivity"),//Digital Wellbeing
    WhitePkgInfo("android", "com.android.internal.app.ResolverActivityForCts"),//小米原生双开选择
    WhitePkgInfo("android", "com.android.updater.MainActivity"),//系统升级
    WhitePkgInfo("com.android.updater", "com.android.updater.MainActivity"),//系统升级
    WhitePkgInfo("com.android.emergency", ""),//个人紧急信息
    WhitePkgInfo("com.google.android.providers.media.module", ""),//媒体选择工具
    WhitePkgInfo("com.google.android.permissioncontroller", "com.android.permissioncontroller.permission.ui.GrantPermissionsActivity"),
    WhitePkgInfo("com.microsoft.appmanager", ""),//连接至windows
    WhitePkgInfo("com.microsoft.windows.homeapp", ""),//windows模拟android
    WhitePkgInfo("andes.oplus.documentsreader", ""),//文件随心开


    //系统设置相关
    WhitePkgInfo("com.android.settings", "com.android.settings.Settings\$BluetoothSettingsActivity"),
    WhitePkgInfo("com.android.settings", "com.android.settings.Settings\$TetherSettingsActivity"),
    WhitePkgInfo("com.android.settings", "com.android.settings.Settings\$LocationSettingsActivity"),
    WhitePkgInfo("com.android.settings", "com.android.settings.Settings\$PageLayoutActivity"),
    WhitePkgInfo("com.android.settings", "com.android.settings.Settings\$NetworkDashboardActivity"),
    WhitePkgInfo("com.android.settings", "com.coloros.settings.ColorSettings\$FontAndDisplaySettingsActivity"),
    WhitePkgInfo("com.android.settings", "com.android.settings.Settings\$InvertedModeActivity"),
    WhitePkgInfo("com.android.settings", "com.android.settings.Settings\$NightModeActivity"),
    WhitePkgInfo("com.android.settings", "com.android.settings.Settings\$FullScreenRatioSelectActivity"),
    WhitePkgInfo("com.android.settings", "com.android.settings.network.telephony.MobileNetworkActivity"),
    WhitePkgInfo("com.android.settings", "com.vivo.settings.display.FullScreenDisplayActivity"),

    WhitePkgInfo("com.android.settings", "com.android.settings.SubSettings"),
    WhitePkgInfo("com.android.settings", "com.vivo.settings.VivoSubSettings"),
    WhitePkgInfo("com.xiaomi.misettings", "com.xiaomi.misettings.display.RefreshRate.RefreshRateActivity"),
    WhitePkgInfo("com.xiaomi.misettings", "com.xiaomi.misettings.display.AntiFlickerMode.AntiFlickerActivity"),
    WhitePkgInfo("com.huawei.android.dsdscardmanager", "com.huawei.android.dsdscardmanager.HWCardManagerActivity"),
    WhitePkgInfo("com.huawei.systemmanager", "com.huawei.systemmanager.netassistant.netapp.ui.LockScreenAppListActivity"),
    WhitePkgInfo("com.huawei.imedia.sws", ""),
    WhitePkgInfo("com.huawei.iconnect", ""),
    WhitePkgInfo("com.huawei.hitouch", ""),
    WhitePkgInfo("com.huawei.hwstartupguide", ""),
    WhitePkgInfo("com.huawei.hls", ""),//华为PC应用引擎
    WhitePkgInfo("com.huawei.hsl", ""),//华为PC应用引擎
    WhitePkgInfo("com.huawei.videoeditor", ""),//花瓣剪辑
    WhitePkgInfo("com.oneplus.wifiapsettings", ""),
    WhitePkgInfo("com.milink.service", ""),
    WhitePkgInfo("com.bbk.theme", ""),

    WhitePkgInfo("android", "com.android.server.wm.OplusActivityPreloadFakeActivity"),// 不知道是啥，打开白名单时会提示
    WhitePkgInfo("com.oplus.wirelesssettings", ""), // oppo热点设置
    WhitePkgInfo("com.oplus.blacklistapp", ""), // 骚扰拦截
    WhitePkgInfo("com.oplus.location", ""), // 定位权限

    WhitePkgInfo("com.oplus.eyeprotect", ""), // oppo护眼模式
    WhitePkgInfo("com.oplus.melody", ""), // 无线耳机
    WhitePkgInfo("com.oplus.appdetail", "com.oplus.appdetail.model.guide.ui.InstallGuideActivity"), // oppo应用安装页面
    WhitePkgInfo("com.oplus.appdetail", "com.oplus.appdetail.model.entrance.HostCompatibleActivity"), // oppo应用安装页面
    WhitePkgInfo("com.oplus.safecenter", "com.oplus.safecenter.private.view.password.AppUnlockPasswordActivity"), // oppo应用锁
    WhitePkgInfo("com.oplus.safecenter", "com.oplus.safecenter.privacy.view.password.AppUnlockPasswordActivity"), // oppo应用锁
    WhitePkgInfo("com.oplus.securitypermission", "com.oplusos.securitypermission.permission.ui.AppStartConfirmDialogActivity"), // oppo启动app
    WhitePkgInfo("com.oplus.securitypermission", "com.oplusos.securitypermission.permission.ui.handheld.PermissionAppsActivityNew"),
    WhitePkgInfo("com.oplus.ocar", ""), // car+


    //qq
    WhitePkgInfo("com.tencent.mobileqq", "com.tencent.open.agent.PublicFragmentActivityForOpenSDK"),//qq登录
    WhitePkgInfo("com.tencent.mobileqq", "com.tencent.mobileqq.activity.LoginActivity"),//qq登录
    //微信
    WhitePkgInfo("com.tencent.mm", "com.tencent.mm.plugin.webview.ui.tools.SDKOAuthUI"),//微信登录
    WhitePkgInfo("com.tencent.mm", "com.tencent.mm.plugin.wallet_index.ui.OrderHandlerUI"),//支付吊起loading选择框
    WhitePkgInfo("com.tencent.mm", "com.android.internal.app.ChooserActivity"),//支付框
    WhitePkgInfo("com.tencent.mm", "com.tencent.kinda.framework.app.UIPageFragmentActivity"),
    WhitePkgInfo("com.tencent.mm", "com.tencent.mm.plugin.fingerprint.ui.FingerPrintAuthTransparentUI"),
    WhitePkgInfo("com.tencent.mm", "com.tencent.mm.plugin.wallet_index.ui.OrderHandlerUI"),
    WhitePkgInfo("com.tencent.mm", "com.tencent.mm.plugin.base.stub.WXPayEntryActivity"),
    WhitePkgInfo("com.tencent.mm", "com.tencent.mm.plugin.account.ui.SimpleLoginUI"),//登录页
    WhitePkgInfo("com.tencent.mm", "com.tencent.mm.plugin.account.ui.WelcomeActivity"),//欢迎页
    WhitePkgInfo("com.tencent.mm", "com.tencent.mm.framework.app.UIPageFragmentActivity"),//支付确定页

    //支付宝
    WhitePkgInfo("com.eg.android.AlipayGphone", "com.alipay.android.msp.ui.views.MspContainerActivity"),//支付框
    WhitePkgInfo("com.eg.android.AlipayGphone", "com.alipay.mobile.verifyidentity.module.fingerprint.FingerprintCheckActivity"),//指纹输入框
    WhitePkgInfo("com.eg.android.AlipayGphone", "com.alipay.zoloz.toyger.workspace.GarfieldActivity"),//人脸解锁支付
    WhitePkgInfo("com.eg.android.AlipayGphone", "com.alipay.android.msp.ui.views.MspContainerActivity"),
    WhitePkgInfo("com.eg.android.AlipayGphone", "com.alipay.mobile.verifyidentity.module.password.pay.ui.PayPwdDialogActivity"),
    WhitePkgInfo("com.eg.android.AlipayGphone", "com.alipay.mobile.security.login.ui.RecommandAlipayUserLoginActivity"),
    WhitePkgInfo("com.eg.android.AlipayGphone", "com.alipay.android.app.TransProcessPayActivity"),
    WhitePkgInfo("com.eg.android.AlipayGphone", "com.ali.user.mobile.login.ui.AliuserGuideActivity"),//登录页


    //其它app
    WhitePkgInfo("com.guizhoum3.mobile.android", "") //移动公文
)

fun isSystemWhiteApp(whitePkgInfo: WhitePkgInfo): Boolean {
    if (RomUtils.checkIsMiuiRom()) {
        whitePageList.remove(WhitePkgInfo("com.android.settings", "com.android.settings.SubSettings"))
    }
    whitePageList.forEach {
        if ((it.activityName == whitePkgInfo.activityName || it.activityName == "") && it.pkgName == whitePkgInfo.pkgName) {
            return true
        }
    }

    //双开选择页面也算
    return isIntervalPage(whitePkgInfo)
}


val appLimitIgnorePageList = mutableListOf(
    WhitePkgInfo(AppUtils.getAppPackageName(), PayUnlockActivity::class.java.canonicalName!!.toString()),
    WhitePkgInfo(AppUtils.getAppPackageName(), PasswordUnlockActivity::class.java.canonicalName!!.toString()),
    WhitePkgInfo(AppUtils.getAppPackageName(), FriendUnlockActivity::class.java.canonicalName!!.toString()),
    WhitePkgInfo(AppUtils.getAppPackageName(), VIPActivity::class.java.canonicalName!!.toString()),
    WhitePkgInfo(AppUtils.getAppPackageName(), VIP2Activity::class.java.canonicalName!!.toString()),
    WhitePkgInfo(AppUtils.getAppPackageName(), WXPayEntryActivity::class.java.canonicalName!!.toString()),
    WhitePkgInfo(AppUtils.getAppPackageName(), TheWebViewActivity::class.java.canonicalName!!.toString()),
    WhitePkgInfo(AppUtils.getAppPackageName(), AppLimitUnlockActivity::class.java.canonicalName!!.toString()),


    //qq
    WhitePkgInfo("com.tencent.mobileqq", "com.tencent.open.agent.PublicFragmentActivityForOpenSDK"),//qq登录
    //微信
    WhitePkgInfo("com.tencent.mm", "com.tencent.mm.plugin.webview.ui.tools.SDKOAuthUI"),//微信登录
    WhitePkgInfo("com.tencent.mm", "com.tencent.mm.plugin.wallet_index.ui.OrderHandlerUI"),//支付吊起loading选择框
    WhitePkgInfo("com.tencent.mm", "com.android.internal.app.ChooserActivity"),//支付框
    WhitePkgInfo("com.tencent.mm", "com.tencent.kinda.framework.app.UIPageFragmentActivity"),
    WhitePkgInfo("com.tencent.mm", "com.tencent.mm.plugin.fingerprint.ui.FingerPrintAuthTransparentUI"),
    WhitePkgInfo("com.tencent.mm", "com.tencent.mm.plugin.wallet_index.ui.OrderHandlerUI"),
    WhitePkgInfo("com.tencent.mm", "com.tencent.mm.plugin.base.stub.WXPayEntryActivity"),
    WhitePkgInfo("com.tencent.mm", "com.tencent.mm.plugin.account.ui.SimpleLoginUI"),//登录页
    WhitePkgInfo("com.tencent.mm", "com.tencent.mm.plugin.account.ui.WelcomeActivity"),//欢迎页
    WhitePkgInfo("com.tencent.mm", "com.tencent.mm.framework.app.UIPageFragmentActivity"),//支付确定页

    //支付宝
    WhitePkgInfo("com.eg.android.AlipayGphone", "com.alipay.android.msp.ui.views.MspContainerActivity"),//支付框
    WhitePkgInfo("com.eg.android.AlipayGphone", "com.alipay.mobile.verifyidentity.module.fingerprint.FingerprintCheckActivity"),//指纹输入框
    WhitePkgInfo("com.eg.android.AlipayGphone", "com.alipay.zoloz.toyger.workspace.GarfieldActivity"),//人脸解锁支付
    WhitePkgInfo("com.eg.android.AlipayGphone", "com.alipay.android.msp.ui.views.MspContainerActivity"),
    WhitePkgInfo("com.eg.android.AlipayGphone", "com.alipay.mobile.verifyidentity.module.password.pay.ui.PayPwdDialogActivity"),
    WhitePkgInfo("com.eg.android.AlipayGphone", "com.alipay.mobile.security.login.ui.RecommandAlipayUserLoginActivity"),
    WhitePkgInfo("com.eg.android.AlipayGphone", "com.alipay.android.app.TransProcessPayActivity"),
    WhitePkgInfo("com.eg.android.AlipayGphone", "com.ali.user.mobile.login.ui.AliuserGuideActivity"),//登录页
)

fun isWhiteLimitIgnorePage(whitePkgInfo: WhitePkgInfo): Boolean {
    appLimitIgnorePageList.forEach {
        if ((it.activityName == whitePkgInfo.activityName || it.activityName == "") && it.pkgName == whitePkgInfo.pkgName) {
            return true
        }
    }
    return false
}


fun isSystemOrUserWhiteApp(whitePkgInfoList: List<WhitePkgInfo>, deleteWhiteAppTempList: List<DeleteWhiteAppTemp>): Pair<Boolean, WhitePkgInfo> {

    whitePkgInfoList.forEachIndexed { _, whitePkgInfo ->
        var isWhite = false

        if (isSystemWhiteApp(whitePkgInfo)) {
            isWhite = true
        }

        // TODO: 暂时注释掉，因为BackgroundService.runningWhiteAppList在最小化版本中不存在
        /*
        BackgroundService.runningWhiteAppList.forEach {
            deleteWhiteAppTempList.forEachIndexed { _, deleteWhiteAppTemp ->
                if (deleteWhiteAppTemp.pkg == it.pkg) {
                    return@forEach
                }
            }

            if (it.pkg == whitePkgInfo.pkgName) {
                isWhite = true
                return@forEach
            }

        }
        */
        if (!isWhite) {
            return Pair(false, whitePkgInfo)
        }
    }
    return Pair(true, WhitePkgInfo("", ""))
}

val systemBlackPageListIgnoreWhite = mutableListOf(
    WhitePkgInfo(AppUtils.getAppPackageName(), TheMainActivity::class.java.canonicalName!!.toString()),//app首页，锁机时推到前台时使用，不能忽略，否则从白名单返回时能返回到这里
    WhitePkgInfo("com.android.settings", "com.android.settings.Settings\$AppDrawOverlaySettingsActivity"),//在其它应用上层显示的权限提醒
    WhitePkgInfo("com.huawei.systemmanager", "com.huawei.systemmanager.appcontrol.activity.StartupAppControlActivity"),//手机管家中的自启动管理
)

/**
 * 黑名单app，即便设置了白名单，也要禁止，一般是关键权限页
 */
fun isSystemBlackAppIgnoreWhite(whitePkgInfoList: List<WhitePkgInfo>): Boolean {
    whitePkgInfoList.forEach { whitePkgInfo ->
        systemBlackPageListIgnoreWhite.forEach {
            if ((it.activityName == whitePkgInfo.activityName || it.activityName == "") && it.pkgName == whitePkgInfo.pkgName) return true
        }
    }
    return false
}

val systemBlackPageList = mutableListOf(
    WhitePkgInfo("com.android.settings", "")//所有系统设置相关的页面
)

/**
 * 黑名单app
 */
fun isSystemBlackApp(whitePkgInfoList: List<WhitePkgInfo>): Boolean {
    whitePkgInfoList.forEach { whitePkgInfo ->
        systemBlackPageList.forEach {
            if ((it.activityName == whitePkgInfo.activityName || it.activityName == "") && it.pkgName == whitePkgInfo.pkgName) return true
        }
    }
    return false
}


/**
 * 用户设置的屏蔽页面
 */
fun isUserBlackPage(whitePkgInfoList: List<WhitePkgInfo>): Pair<Boolean, String> {

    val denyPageList = MyGsonUtil.getDenyPageList()

    whitePkgInfoList.forEach { whitePkgInfo ->
        denyPageList.forEach {
            if (it.valid) {
                it.pages.forEach { page ->
                    if (whitePkgInfo.activityName.contains(page.activity) && page.pkg == whitePkgInfo.pkgName) return Pair(true, it.name)
                }
            }
        }
    }

    return Pair(false, "")
}

val doNotNeedCalcTopPageList = mutableListOf(
    WhitePkgInfo(AppUtils.getAppPackageName(), TheMainActivity::class.java.canonicalName!!.toString()),//自身，不能忽略，否则日志中没有任何仅打开数据了，将其设置为黑名单即可
    WhitePkgInfo("com.autonavi.minimap", "com.autonavi.map.activity.HicarMapActivity"),//高德地图Hicar车机
    WhitePkgInfo("com.huawei.hicar", ""), WhitePkgInfo("com.xiaomi.misubscreenui", ""),//小米背屏 nil
    WhitePkgInfo("com.android.systemui", "com.android.systemui.volume.VolumeDialogImpl\$CustomDialog")//鸿蒙2.0音量调整时
)

/**
 * 计算时需要忽略的页面
 */
fun isDoNotNeedCalcTopApp(whitePkgInfo: WhitePkgInfo): Boolean {
    doNotNeedCalcTopPageList.forEach {
        if ((it.activityName == whitePkgInfo.activityName || it.activityName == "") && it.pkgName == whitePkgInfo.pkgName) return true
    }
    return false
}

val doNotNeedCalcTimePageList = mutableListOf(
    WhitePkgInfo(AppUtils.getAppPackageName(), ""),//自身
    WhitePkgInfo("com.autonavi.minimap", "com.autonavi.map.activity.HicarMapActivity"),//高德地图Hicar车机
    WhitePkgInfo("com.huawei.hicar", ""),//
    WhitePkgInfo("com.android.systemui", ""),//
)

/**
 * 计算时需要忽略的页面
 */
fun isDoNotNeedCalcTimeApp(whitePkgInfo: WhitePkgInfo, launcherPkg: String): Boolean {
    if (whitePkgInfo.pkgName == launcherPkg) return true
    doNotNeedCalcTimePageList.forEach {
        if ((it.activityName == whitePkgInfo.activityName || it.activityName == "") && it.pkgName == whitePkgInfo.pkgName) return true
    }
    return false
}


val intervalPageList = mutableListOf(
    WhitePkgInfo("com.huawei.android.internal.app", "com.huawei.android.internal.app.HwChooserActivity"),
    WhitePkgInfo("com.huawei.android.internal.app", "com.huawei.android.internal.app.HwResolverActivity"),
    WhitePkgInfo("com.hihonor.android.internal.app", "com.hihonor.android.internal.app.HwChooserActivity"),
    WhitePkgInfo("com.hihonor.android.internal.app", "com.hihonor.android.internal.app.HwResolverActivity"),
    WhitePkgInfo("com.vivo.doubleinstance", "com.vivo.doubleinstance.DoubleAppResolverActivity"),//双开"
    WhitePkgInfo("android", "com.android.internal.app.ResolverActivity"),
    WhitePkgInfo("android", "com.android.internal.app.MiuiResolverActivity"),
    WhitePkgInfo("android", "com.android.internal.app.ResolverActivityForCts"),
    WhitePkgInfo("com.miui.securitycore", "com.miui.xspace.ui.activity.XSpaceResolveActivity"),
    WhitePkgInfo("com.miui.securitycore", "com.miui.xspace.ui.activity.XSpaceDefaultAppActivity"),
    WhitePkgInfo("android", "com.android.internal.app.DoubleAppResolverActivity"),
    WhitePkgInfo("smartisanos", "smartisanos.app.DoppelgangerChooseActivity"),
    WhitePkgInfo("android", "smartisanos.app.DoppelgangerChooseActivity"),
    WhitePkgInfo("android", "com.android.internal.app.ResolverActivityZTE"),
    WhitePkgInfo("android", "com.android.internal.app.ChooserActivity"),//分身选择框、分享选择框
)

fun isIntervalPage(whitePkgInfo: WhitePkgInfo): Boolean {
    intervalPageList.forEach {
        if ((it.activityName == whitePkgInfo.activityName || it.activityName == "") && it.pkgName == whitePkgInfo.pkgName) return true
    }
    return false
}




