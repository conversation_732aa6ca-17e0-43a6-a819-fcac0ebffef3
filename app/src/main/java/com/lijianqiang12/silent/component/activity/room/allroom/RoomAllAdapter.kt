package com.lijianqiang12.silent.component.activity.room.allroom

import android.widget.TextView
import com.bumptech.glide.Glide
import com.bumptech.glide.load.engine.DiskCacheStrategy
import com.chad.library.adapter.base.BaseQuickAdapter
import com.chad.library.adapter.base.module.LoadMoreModule
import com.chad.library.adapter.base.viewholder.BaseViewHolder
import com.lijianqiang12.silent.R
import com.lijianqiang12.silent.data.model.net.pojos.AllRoom

class RoomAllAdapter(layoutRes: Int, item: MutableList<AllRoom>) : BaseQuickAdapter<AllRoom, BaseViewHolder>(layoutRes, item), LoadMoreModule {

    override fun convert(viewHolder: BaseViewHolder, item: AllRoom) {
        val tvRoomName = viewHolder.getView<TextView>(R.id.tv_item_room_all)
        val tvIfJoined = viewHolder.getView<TextView>(R.id.tv_room_item_if_joined)
        if (item.isJoined) {
            tvIfJoined.text = "已加入"
            tvIfJoined.setBackgroundResource(R.drawable.bg_joined)
        } else {
            tvIfJoined.text = "加入"
            tvIfJoined.setBackgroundResource(R.drawable.bg_unjoined)
        }
        tvRoomName.isSelected = true
        tvRoomName.text = item.roomName
        viewHolder.getView<TextView>(R.id.tv_item_room_all_numbers).text = "${item.roomOnlineNumbers}人在线"

        if (item.hasLock) {
//            Glide.with(context).load("https://offphone-1252369707.file.myqcloud.com/2020-07-29-padlock.png").into(viewHolder.getView(R.id.iv_item_room_all))
            Glide.with(context).load("https://offphone-1252369707.file.myqcloud.com/2020-07-29-password.png")
                //.transition(DrawableTransitionOptions.withCrossFade())
                .diskCacheStrategy(DiskCacheStrategy.AUTOMATIC)
                .into(viewHolder.getView(R.id.iv_item_room_all))
        } else {
            Glide.with(context).load(item.roomCoverUrl).into(viewHolder.getView(R.id.iv_item_room_all))
        }

    }
}
