package com.lijianqiang12.silent.component.activity.custom.dialog

import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.LinearLayout
import com.google.android.material.bottomsheet.BottomSheetBehavior
import com.lijianqiang12.silent.R
import com.lijianqiang12.silent.component.activity.base.BaseBottomSheetDialogFragment
import kotlinx.android.synthetic.main.bottom_sheet_dialog.view.*
import kotlinx.android.synthetic.main.item_bottom_sheet_select.view.*

class BottomSingleSelectDialogFragment : BaseBottomSheetDialogFragment() {

    private lateinit var mBehavior: BottomSheetBehavior<View>
    private lateinit var customView: View
    private var showList: MutableList<String> = mutableListOf()
    private var valueList: MutableList<Long> = mutableListOf()
    private var onValueSelectListener: OnValueSelectListener? = null
//    private var myActivity: AppCompatActivity? = null
//    private var fragment: Fragment? = null


    companion object {
        @JvmStatic
        fun newInstance(): BottomSingleSelectDialogFragment {
            return BottomSingleSelectDialogFragment()
        }
    }

//    fun setFragment(fragment: Fragment) {
//        this.fragment = fragment
//    }
//
//    fun setActivity(activity: AppCompatActivity) {
//        this.myActivity = activity
//    }


    override fun onCreateView(inflater: LayoutInflater, container: ViewGroup?, savedInstanceState: Bundle?): View? {
        customView = View.inflate(requireContext(), R.layout.bottom_sheet_dialog, null)

        for (index in valueList.indices) {
            val itemView = LayoutInflater.from(requireContext()).inflate(R.layout.item_bottom_sheet_select, null)
            itemView.tv_title.text = showList[index]
            itemView.setOnClickListener {
                onValueSelectListener?.onSelect(valueList[index], showList[index])
                this.dismiss()
            }
            val params = LinearLayout.LayoutParams(ViewGroup.LayoutParams.MATCH_PARENT, ViewGroup.LayoutParams.WRAP_CONTENT)
            customView.ll_bottom_dialog.addView(itemView, params)
        }


        return customView
    }


    override fun onStart() {
        super.onStart()
        val parentView = customView.parent as View
        parentView.setBackgroundColor(resources.getColor(R.color.colorTranslate))
        mBehavior = BottomSheetBehavior.from(parentView)

        mBehavior.state = BottomSheetBehavior.STATE_EXPANDED
    }

    fun setShowList(showList: MutableList<String>) {
        this.showList = showList
    }

    fun setValueList(valueList: MutableList<Long>) {
        this.valueList = valueList
    }

    fun setOnValueSelectListener(onValueSelectListener: OnValueSelectListener) {
        this.onValueSelectListener = onValueSelectListener
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
    }


    interface OnValueSelectListener {
        fun onSelect(value: Long, show: String)
    }

}