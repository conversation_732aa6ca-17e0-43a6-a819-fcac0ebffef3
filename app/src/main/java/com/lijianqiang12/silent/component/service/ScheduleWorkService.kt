package com.lijianqiang12.silent.component.service

import android.app.job.JobParameters
import android.app.job.JobService
import com.blankj.utilcode.util.LogUtils
import com.lijianqiang12.silent.component.service.background_service.BackgroundService
import com.lijianqiang12.silent.utils.ServiceUtil
import com.lijianqiang12.silent.utils.WorkUtil

class ScheduleWorkService : JobService() {

    override fun onStartJob(params: JobParameters): Boolean {

//        if (ServiceUtil.isServiceRun(applicationContext, CheckService::class.java.canonicalName!!)) {
//            LogUtils.d("onStartJob:Service is running.")
//        } else {
//            val intent = Intent(this, CheckService::class.java)
//            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
//                startForegroundService(intent)
//            } else {
//                startService(intent)
//            }
//            LogUtils.d("onStartJob:Service will be run.")
//        }

        ServiceUtil.checkAndStartService(this, BackgroundService::class.java)

        WorkUtil.startCheckScheduleTaskJob(this, 30 * 1000L)
        return false
    }

    override fun onStopJob(params: JobParameters): Boolean {
        LogUtils.d("onStopJob")
        return false
    }
}
