package com.lijianqiang12.silent.component.activity.room.roomdetail

import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.fragment.app.Fragment
import androidx.lifecycle.lifecycleScope
import androidx.recyclerview.widget.LinearLayoutManager
import androidx.recyclerview.widget.RecyclerView
import com.lijianqiang12.silent.MAX_ID
import com.lijianqiang12.silent.R
import com.lijianqiang12.silent.data.model.net.MyRetrofitClient
import com.lijianqiang12.silent.component.activity.lock.punch.PunchCardAdapter
import com.lijianqiang12.silent.utils.MyToastUtil
import kotlinx.android.synthetic.main.fragment_room_item_news.*
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.launch
import kotlinx.coroutines.withContext


private const val ARG_PARAM1 = "param1"
private const val ARG_PARAM2 = "param2"

class RoomItemNewsFragment : Fragment() {
    private var param1: String? = null
    private var param2: String? = null
    private lateinit var recyclerview: RecyclerView
    private lateinit var adapter: PunchCardAdapter


    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        arguments?.let {
            param1 = it.getString(ARG_PARAM1)
            param2 = it.getString(ARG_PARAM2)
        }
    }

    override fun onCreateView(inflater: LayoutInflater, container: ViewGroup?,
                              savedInstanceState: Bundle?): View? {
        // Inflate the layout for this fragment
        return inflater.inflate(R.layout.fragment_room_item_news, container, false)
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        recyclerview = rv_room_punch_card
        recyclerview.layoutManager = LinearLayoutManager(requireContext())
        adapter = PunchCardAdapter(R.layout.item_punch_card, mutableListOf())
        adapter.animationEnable = true
        adapter.loadMoreModule.setOnLoadMoreListener {
            if (adapter.data.size == 0) {
                getNewData(MAX_ID)
            } else {
                getNewData(adapter.data[adapter.data.size - 1].punchCardId)
            }
        }

        recyclerview.adapter = adapter

        adapter.setOnItemClickListener { adapter, view, position ->

        }

//        srl_punch_card.setOnRefreshListener {
//            getNewData(MAX_ID)
//        }
//
//        srl_punch_card.isRefreshing = true
        getNewData(MAX_ID)

    }

    private fun getNewData(lastId: Long) {
        lifecycleScope.launch(Dispatchers.IO) {
            try {
                val result = MyRetrofitClient.service.getRoomMemberPunch(lastId, currentRoomId)
                withContext(Dispatchers.Main) {
                    if (result.code == 200) {
                        result.data?.let {
                            if (lastId == MAX_ID) {
                                adapter.setNewInstance(it)
                                adapter.loadMoreModule.loadMoreComplete()
                            } else {
                                if (it.isEmpty()) {
                                    adapter.loadMoreModule.loadMoreEnd()
                                } else {
                                    adapter.addData(it)
                                    adapter.loadMoreModule.loadMoreComplete()
                                }
                            }
                            adapter.notifyDataSetChanged()
                        }
                    } else {
                        MyToastUtil.showInfo(result.msg)
                    }
                }
            } catch (e: Exception) {
                withContext(Dispatchers.Main) {
                    MyToastUtil.showInfo(e.message)
                }
            } finally {
//                withContext(Dispatchers.Main) {
//                    srl_punch_card.isRefreshing = false
//                }
            }
        }
    }

    companion object {
        /**
         * Use this factory method to create a new instance of
         * this fragment using the provided parameters.
         *
         * @param param1 Parameter 1.
         * @param param2 Parameter 2.
         * @return A new instance of fragment RoomItemNewsFragment.
         */
        // TODO: Rename and change types and number of parameters
        @JvmStatic
        fun newInstance(param1: String, param2: String) =
                RoomItemNewsFragment().apply {
                    arguments = Bundle().apply {
                        putString(ARG_PARAM1, param1)
                        putString(ARG_PARAM2, param2)
                    }
                }
    }
}