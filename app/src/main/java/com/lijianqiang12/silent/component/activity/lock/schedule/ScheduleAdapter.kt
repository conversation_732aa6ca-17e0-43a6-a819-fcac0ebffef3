package com.lijianqiang12.silent.component.activity.lock.schedule

import android.annotation.SuppressLint
import android.widget.TextView
import com.chad.library.adapter.base.BaseQuickAdapter
import com.chad.library.adapter.base.module.LoadMoreModule
import com.chad.library.adapter.base.viewholder.BaseViewHolder
import com.lijianqiang12.silent.R
import com.lijianqiang12.silent.data.model.db.ScheduleWithSub
import com.lijianqiang12.silent.utils.TimeUtil

class ScheduleAdapter(layoutRes: Int, list: MutableList<ScheduleWithSub>)
    : BaseQuickAdapter<ScheduleWithSub, BaseViewHolder>(layoutRes, list), LoadMoreModule {

    @SuppressLint("CheckResult")
    override fun convert(viewHolder: BaseViewHolder, item: ScheduleWithSub) {
        if (item.schedule.useTomato) {
            if (item.tomatoWithSub == null) {
                viewHolder.getView<TextView>(R.id.item_lock_fast_drag_title).text = "${TimeUtil.formatHHMM(item.schedule.startHour, item.schedule.startMinute)} 执行『已删除』"
            } else {
                viewHolder.getView<TextView>(R.id.item_lock_fast_drag_title).text = "${TimeUtil.formatHHMM(item.schedule.startHour, item.schedule.startMinute)} 执行『 ${item.tomatoWithSub!!.tomato!!.title}』"
            }
        } else {
            if (item.schedule.startHour * 60 + item.schedule.startMinute >= item.schedule.endHour * 60 + item.schedule.endMinute) {
                viewHolder.getView<TextView>(R.id.item_lock_fast_drag_title).text = "${TimeUtil.formatHHMM(item.schedule.startHour, item.schedule.startMinute)} — 次日${TimeUtil.formatHHMM(item.schedule.endHour, item.schedule.endMinute)}"
            } else {
                viewHolder.getView<TextView>(R.id.item_lock_fast_drag_title).text = "${TimeUtil.formatHHMM(item.schedule.startHour, item.schedule.startMinute)} — ${TimeUtil.formatHHMM(item.schedule.endHour, item.schedule.endMinute)}"
//                viewHolder.getView<TextView>(R.id.item_lock_fast_drag_title).text = "${TimeUtil.formatHHMM(item.schedule.startHour, item.schedule.startMinute)} — 当日${TimeUtil.formatHHMM(item.schedule.endHour, item.schedule.endMinute)}"
            }
        }
    }
}