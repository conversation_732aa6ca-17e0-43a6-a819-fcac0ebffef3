package com.lijianqiang12.silent.component.service.windows

import android.widget.ImageView
import android.widget.TextView
import com.chad.library.adapter.base.BaseQuickAdapter
import com.chad.library.adapter.base.module.LoadMoreModule
import com.chad.library.adapter.base.viewholder.BaseViewHolder
import com.lijianqiang12.silent.R
import com.lijianqiang12.silent.component.activity.lock.whiteapp.AppInfo
import com.lijianqiang12.silent.utils.MyUtil

class FloatAppInfoAdapter(layoutRes: Int, list: MutableList<AppInfo>)
    : BaseQuickAdapter<AppInfo, BaseViewHolder>(layoutRes, list), LoadMoreModule {

    override fun convert(viewHolder: BaseViewHolder, item: AppInfo) {
        if (!MyUtil.isVIP() && viewHolder.layoutPosition >= 6) {
            viewHolder.getView<ImageView>(R.id.iv_app_icon).alpha = 0.3f
            viewHolder.getView<TextView>(R.id.tv_app_name).alpha = 0.3f
        }else{
            viewHolder.getView<ImageView>(R.id.iv_app_icon).alpha = 1f
            viewHolder.getView<TextView>(R.id.tv_app_name).alpha = 1f
        }
        viewHolder.getView<TextView>(R.id.tv_app_name).text = item.appName
        viewHolder.getView<ImageView>(R.id.iv_app_icon).setImageDrawable(item.appIcon)
    }
}