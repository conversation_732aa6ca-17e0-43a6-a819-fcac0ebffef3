package com.lijianqiang12.silent.component.service.background_service

import android.annotation.SuppressLint
import android.app.Service
import android.content.Context
import android.media.AudioAttributes
import android.media.AudioFocusRequest
import android.media.AudioManager
import android.os.Build
import com.blankj.utilcode.util.LogUtils
import com.lijianqiang12.silent.MyConstants
import com.lijianqiang12.silent.TheApplication
import com.lijianqiang12.silent.utils.MMKVUtils
import java.lang.reflect.Method

class BackgroundServiceUtil {

    companion object{

        /**
         * 折叠通知栏
         *
         * @param context
         */
        @SuppressLint("WrongConstant")
        fun collapsingNotification(context: Context) {
            val service = context.getSystemService("statusbar") ?: return
            try {
                val clazz = Class.forName("android.app.StatusBarManager")
                val sdkVersion = Build.VERSION.SDK_INT
                var collapse: Method? = null
                collapse = if (sdkVersion <= 16) {
                    clazz.getMethod("collapse")
                } else {
                    clazz.getMethod("collapsePanels")
                }
                collapse.isAccessible = true
                collapse.invoke(service)
            } catch (e: java.lang.Exception) {
                e.printStackTrace()
            }
        }


        //获取音频焦点，以便在开始锁机时停止其他后台音频播放。
//        private fun getAudioFocus(focus: Boolean): Boolean {
//            val ifStopMusic = MMKVUtils.getBoolean(MyConstants.SP_KEY_STOP_MUSIC_SETTINGS, true)
//            if (!ifStopMusic) {
//                return focus
//            }
//
//            val mAudioManager = TheApplication.getInstance().getSystemService(Service.AUDIO_SERVICE) as AudioManager
//            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
//                val focusRequest = AudioFocusRequest.Builder(AudioManager.AUDIOFOCUS_GAIN).run {
//                    setAudioAttributes(AudioAttributes.Builder().run {
//                        setUsage(AudioAttributes.USAGE_GAME)
//                        setContentType(AudioAttributes.CONTENT_TYPE_MUSIC)
//                        build()
//                    })
////                setAcceptsDelayedFocusGain(true)
//                    setOnAudioFocusChangeListener(audioFocusChangeListener, handler)
//                    build()
//                }
//
//                val result = if (focus) {
//                    mAudioManager.requestAudioFocus(focusRequest)
//                } else {
//                    mAudioManager.abandonAudioFocusRequest(focusRequest)
//                }
//                LogUtils.d("getAudioFocus O result = $result")
//                return result == AudioManager.AUDIOFOCUS_REQUEST_GRANTED
//            } else {
//                val result = if (focus) {
//                    mAudioManager.requestAudioFocus(
//                        audioFocusChangeListener, AudioManager.STREAM_MUSIC, AudioManager.AUDIOFOCUS_GAIN
//                    )
//                } else {
//                    mAudioManager.abandonAudioFocus(audioFocusChangeListener)
//                }
//                LogUtils.d("getAudioFocus result = $result")
//                return result == AudioManager.AUDIOFOCUS_REQUEST_GRANTED
//            }
//        }
//
    }


}

