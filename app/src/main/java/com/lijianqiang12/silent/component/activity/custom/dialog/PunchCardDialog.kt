package com.lijianqiang12.silent.component.activity.custom.dialog

import android.graphics.Color
import android.graphics.drawable.ColorDrawable
import android.os.Bundle
import android.util.DisplayMetrics
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.view.WindowManager
import androidx.appcompat.app.AppCompatActivity
import androidx.fragment.app.Fragment
import androidx.lifecycle.lifecycleScope
import com.lijianqiang12.silent.utils.MMKVUtils
import com.bumptech.glide.Glide
import com.bumptech.glide.load.engine.DiskCacheStrategy
import com.lijianqiang12.silent.DIALOG_WIDTH_PERCENT_BIG
import com.lijianqiang12.silent.MyConstants
import com.lijianqiang12.silent.R
import com.lijianqiang12.silent.data.model.net.MyRetrofitClient
import com.lijianqiang12.silent.data.model.net.pojos.PunchCardMsg
import com.lijianqiang12.silent.component.activity.base.BaseDialogFragment
import com.lijianqiang12.silent.utils.MyToastUtil
import com.lijianqiang12.silent.utils.TimeUtil
import kotlinx.android.synthetic.main.dialog_normal.view.tv_dialog_normal_cancel
import kotlinx.android.synthetic.main.dialog_normal.view.tv_dialog_normal_ok
import kotlinx.android.synthetic.main.dialog_punch_card.view.*
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.launch
import kotlinx.coroutines.withContext


class PunchCardDialog() : BaseDialogFragment() {

    constructor(fragment: Fragment) : this() {
        this.fragment = fragment
    }

    constructor(activity: AppCompatActivity) : this() {
        this.activity = activity
    }

    private var okListener: OnPunchCardListener? = null
    private var cancelListener: OnCancelClickListener? = null
    private lateinit var v: View
    private var fragment: Fragment? = null
    private var activity: AppCompatActivity? = null
    private var length = 0L
    private var punchCardMsg: PunchCardMsg? = null


    //    private var cancelable:Boolean = true
    private fun refreshCard() {
        lifecycleScope.launch(Dispatchers.IO) {
            try {
                val result = MyRetrofitClient.service.getPunchCardMsg()
                if (result.code == 200) {
                    result.data?.let {
                        punchCardMsg = it
                        withContext(Dispatchers.Main) {
                            v.tv_punch_card_lock_number.text = "第${it.lockNumber}次锁机"
                            Glide.with(requireContext()).load(it.imgUrl)
                                //.transition(DrawableTransitionOptions.withCrossFade())
                                .diskCacheStrategy(DiskCacheStrategy.AUTOMATIC)
                                .into(v.iv_punch_card_img)
                            v.tv_punch_card_1_1.text = "${it.goOnLock}天"
                            v.tv_punch_card_2_1.text = TimeUtil.formatHHMMSimpleEn((length / 1000).toInt())//分钟
                            if (it.totalLength / 60 / 60 > 0) {
                                v.tv_punch_card_3_1.text = "${it.totalLength / 60 / 60}h"
                            } else if (it.totalLength / 60 > 0) {
                                v.tv_punch_card_3_1.text = "${it.totalLength / 60}m"
                            } else {
                                v.tv_punch_card_3_1.text = "${it.totalLength}s"
                            }
                            v.cb_if_share.text = "分享到社交网络（已被分享${it.shareCount}次）"
                            v.cb_if_share.isChecked = MMKVUtils.getBoolean(MyConstants.SP_KEY_IF_SHARE, true)
                            v.cb_if_share.setOnCheckedChangeListener { buttonView, isChecked ->
                                MMKVUtils.put(MyConstants.SP_KEY_IF_SHARE, isChecked)
                            }
                        }
                    }
                }
            } catch (e: Exception) {

            }
        }
    }

    override fun onCreateView(inflater: LayoutInflater, container: ViewGroup?, savedInstanceState: Bundle?): View? {
        dialog!!.window!!.setBackgroundDrawable(ColorDrawable(Color.TRANSPARENT))
        v = inflater.inflate(R.layout.dialog_punch_card, container, false)

        refreshCard()

        v.iv_refresh_card.setOnClickListener { refreshCard() }

        if (MMKVUtils.getBoolean(MyConstants.SP_KEY_BIND_MOBILE, false)) {
            v.tv_phone_punch_notice.visibility = View.GONE
        } else {
            v.tv_phone_punch_notice.visibility = View.VISIBLE
        }

        okListener?.apply {
            v.tv_dialog_normal_ok.visibility = View.VISIBLE
        }
        cancelListener?.apply {
            v.tv_dialog_normal_cancel.visibility = View.VISIBLE
        }
        v.tv_dialog_normal_ok.setOnClickListener {
            okListener?.apply {
                if (punchCardMsg == null) {
                    MyToastUtil.showInfo("未获取到完整锁机信息，请检查网络")
                } else {
                    if (v.et_punch_card_word.text == null) {
                        okListener?.onClick(punchCardMsg!!, "", length, v.cb_if_share.isChecked)
                    } else {
                        okListener?.onClick(punchCardMsg!!, v.et_punch_card_word.text.toString(), length, v.cb_if_share.isChecked)
                    }
                }
            }
            <EMAIL>()
        }
        v.tv_dialog_normal_cancel.setOnClickListener {
//            cancelListener?.apply {
//                if (punchCardMsg == null) {
//                    MyToastUtil.showInfo("未获取到完整锁机信息，请检查网络")
//                } else {
//                    if (v.et_punch_card_word.text == null) {
//                        cancelListener?.onClick(punchCardMsg!!, "", length)
//                    } else {
//                        cancelListener?.onClick(punchCardMsg!!, v.et_punch_card_word.text.toString(), length)
//                    }
//                }
//            }
            <EMAIL>()
        }



        return v
    }


    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)


    }

    fun setLength(length: Long) {
        this.length = length
    }

    fun show() {
        activity?.apply {
            super.show(this.supportFragmentManager, "NormalDialog")
        }

        fragment?.apply {
            super.show(fragment!!.requireFragmentManager(), "NormalDialog")
        }
    }

    override fun onStart() {
        val params = dialog!!.window!!.attributes
        val dm: DisplayMetrics = resources.displayMetrics
//        val density = dm.density
        val width = dm.widthPixels
//        val height = dm.heightPixels
        params.width = (width * DIALOG_WIDTH_PERCENT_BIG).toInt()//ViewGroup.LayoutParams.MATCH_PARENT
//        params.width = width.coerceAtMost(height) * 3 / 4//ViewGroup.LayoutParams.MATCH_PARENT
        dialog!!.window!!.attributes = params as WindowManager.LayoutParams
        super.onStart()
    }

    fun setOnNormalOKClickListener(okListener: OnPunchCardListener) {
        this.okListener = okListener
    }

    fun setOnNormalCancelClickListener(cancelListener: OnCancelClickListener) {
        this.cancelListener = cancelListener
    }

    interface OnPunchCardListener {
        fun onClick(punchCardMsg: PunchCardMsg, word: String, length: Long, isShare: Boolean)
    }

}