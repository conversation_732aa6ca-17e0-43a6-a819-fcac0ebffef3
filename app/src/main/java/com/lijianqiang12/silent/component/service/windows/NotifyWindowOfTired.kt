package com.lijianqiang12.silent.component.service.windows

import android.animation.ObjectAnimator
import android.annotation.SuppressLint
import android.content.Context
import android.graphics.PixelFormat
import android.os.Build
import android.os.Handler
import android.view.Gravity
import android.view.LayoutInflater
import android.view.View
import android.view.WindowManager
import android.widget.TextView
import com.blankj.utilcode.util.ConvertUtils
import com.blankj.utilcode.util.ScreenUtils
import com.lijianqiang12.silent.R
import com.lijianqiang12.silent.utils.MyToastUtil
import com.lijianqiang12.silent.utils.MyWindowUtil

class NotifyWindowOfTired(val context: Context) {
    //    private var windowCreated = false
//    private var windowShowing = false
    private lateinit var params: WindowManager.LayoutParams
    private lateinit var windowManager: WindowManager
    var layout: View? = null

    @SuppressLint("ClickableViewAccessibility")
    private fun createRestView() {
        params = WindowManager.LayoutParams()

        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
            params.type = WindowManager.LayoutParams.TYPE_APPLICATION_OVERLAY
        } else {
            params.type = WindowManager.LayoutParams.TYPE_SYSTEM_ALERT
        }

        windowManager = context.applicationContext.getSystemService(Context.WINDOW_SERVICE) as WindowManager

        params.format = PixelFormat.TRANSLUCENT

        params.flags = params.flags or WindowManager.LayoutParams.FLAG_NOT_FOCUSABLE or
                WindowManager.LayoutParams.FLAG_NOT_TOUCH_MODAL or
                WindowManager.LayoutParams.FLAG_HARDWARE_ACCELERATED
        params.gravity = Gravity.START or Gravity.TOP
        params.x = ScreenUtils.getScreenWidth() * -1
        params.y = ConvertUtils.dp2px(88f)//+ConvertUtils.dp2px(100f)
        params.width = WindowManager.LayoutParams.WRAP_CONTENT
        params.height = WindowManager.LayoutParams.WRAP_CONTENT

        layout = LayoutInflater.from(context).inflate(R.layout.layout_tired_notify, null)
//        layout?.let {
////            var lastX = 0f
//            var lastY = 0f
//            it.setOnTouchListener { v, event ->
//
//                when (event.action) {
//                    MotionEvent.ACTION_DOWN -> {
//                        it.setBackgroundResource(R.drawable.shape_lock_view_rest)
////                        lastX = event.rawX
//                        lastY = event.rawY
//                    }
//                    MotionEvent.ACTION_MOVE -> {
////                        params.x += (event.rawX - lastX).toInt()
//                        params.y += (event.rawY - lastY).toInt()
//
////                        LogUtils.d("ACTION_MOVE, params.x=" + params.x + ", params.x=" + params.x + ",rawX=" + event.rawX + ",rawY=" + event.rawY + ",lastX=" + lastX + ",lastY=" + lastY)
//
//                        windowManager.updateViewLayout(it, params)
////                        lastX = event.rawX
//                        lastY = event.rawY
//
//                    }
//                    MotionEvent.ACTION_UP -> {
////                        if (it.width / 2 - event.x + event.rawX < ScreenUtils.getScreenWidth() / 2) {//left
////                            params.x = 0
////                            it.setBackgroundResource(R.drawable.shape_lock_view_rest_left)
////                        } else {
////                            params.x = ScreenUtils.getScreenWidth() - it.width
////                            it.setBackgroundResource(R.drawable.shape_lock_view_rest_right)
////                        }
//                        windowManager.updateViewLayout(it, params)
//
//                    }
//                }
//                false
//
//            }
//        }
//        windowCreated = true
    }

    //显示疲劳提醒
    fun showWindow(text: String) {
        //是否显示app限时提醒
//        if (!MMKVUtils.getBoolean(MyConstants.SP_KEY_LIMIT_SETTING_TIRED_NOTIFY, false)) return

        if (layout == null) {
            createRestView()
        }
        layout!!.findViewById<TextView>(R.id.tv_tired_length).text = text
//        layout.findViewById<ImageView>(R.id.civ_monitor_app_icon).setImageDrawable(icon)
        if (!MyWindowUtil.isWindowShowing(layout!!)) {
//            windowShowing = true
            try {
                windowManager.addView(layout!!, params)

                ObjectAnimator.ofFloat(layout!!, "translationX", ScreenUtils.getScreenWidth() * -1f, 0f)
                    .setDuration(2000)
                    .start()

            } catch (e: Exception) {
                MyToastUtil.showError("TiredNotifyWindow未成功获取到悬浮窗权限，请到系统设置或安全管家中重新授予，若已经授予，则可能是系统BUG，请重启手机")
//                windowShowing = false

            }
        }
    }

    //隐藏疲劳提醒
    fun hideWindow() {
        if (layout != null) {
            if (MyWindowUtil.isWindowShowing(layout!!)) {
                try {
                    ObjectAnimator.ofFloat(layout, "translationX", 0f, ScreenUtils.getScreenWidth() * -1f)
                        .setDuration(2000)
                        .start()
                    Handler().postDelayed(kotlinx.coroutines.Runnable {
                        try {
                            windowManager.removeView(layout!!)
                        } catch (e: Exception) {

                        }
//                        windowShowing = false
                    }, 2500)
                } catch (e: Exception) {
                    MyToastUtil.showError("未成功获取到悬浮窗权限，请到系统设置或安全管家中重新授予，若已经授予，则可能是系统BUG，请重启手机")
                }
            }
        }
    }

    fun showAndHide(showContent: String) {
//        if(windowShowing) return
        showWindow(showContent)
        Handler().postDelayed(kotlinx.coroutines.Runnable {
            hideWindow()
        }, 8000)
    }
}