package com.lijianqiang12.silent.component.activity.lock.punch

import android.os.Bundle
import androidx.lifecycle.lifecycleScope
import androidx.recyclerview.widget.LinearLayoutManager
import androidx.recyclerview.widget.RecyclerView
import com.lijianqiang12.silent.MAX_ID
import com.lijianqiang12.silent.R
import com.lijianqiang12.silent.data.model.net.MyRetrofitClient
import com.lijianqiang12.silent.data.model.net.pojos.ThePunchCard
import com.lijianqiang12.silent.component.activity.base.BaseActivity
import com.lijianqiang12.silent.component.activity.custom.dialog.NormalDialog
import com.lijianqiang12.silent.component.activity.custom.dialog.OnCancelClickListener
import com.lijianqiang12.silent.component.activity.custom.dialog.OnOKClickListener
import com.lijianqiang12.silent.utils.MyToastUtil
import kotlinx.android.synthetic.main.activity_punch_card.*
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.launch
import kotlinx.coroutines.withContext

class MyPunchCardActivity : BaseActivity() {

    private lateinit var recyclerview: RecyclerView
    private lateinit var adapter: PunchCardAdapter


    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        setContentView(R.layout.activity_my_punch_card)

        iv_punch_card_return.setOnClickListener { finish() }

        recyclerview = rv_punch_card
        recyclerview.layoutManager = LinearLayoutManager(this)
        adapter = PunchCardAdapter(R.layout.item_punch_card, mutableListOf())
        adapter.animationEnable = true
        adapter.loadMoreModule.setOnLoadMoreListener {
            if (adapter.data.size == 0) {
                getNewData(MAX_ID)
            } else {
                getNewData(adapter.data[adapter.data.size - 1].punchCardId)
            }
        }

        recyclerview.adapter = adapter

        adapter.setOnItemClickListener { adapter, view, position ->

        }


        adapter.setOnItemLongClickListener { adapter, view, position ->
            NormalDialog(this).apply {
                setTitle("删除该打卡")
                setContent("您是否要将此打卡删除？")
                setOnNormalOKClickListener("删除该打卡", object : OnOKClickListener {
                    override fun onclick() {
                        <EMAIL>(Dispatchers.IO) {
                            try {
                                val result = MyRetrofitClient.service.removePunchCard((adapter.data[position] as ThePunchCard).punchCardId)
                                withContext(Dispatchers.Main) {
                                    if (result.code == 200) {
                                        MyToastUtil.showInfo(result.msg)
                                        getNewData(MAX_ID)
                                    } else {
                                        MyToastUtil.showInfo(result.msg)
                                    }
                                }
                            } catch (e: Exception) {
                                withContext(Dispatchers.Main) {
                                    MyToastUtil.showError(e.message)
                                }
                            }
                        }
                    }
                })
                setOnNormalCancelClickListener(object : OnCancelClickListener {
                    override fun onclick() {

                    }
                })
                showDialog()
            }

            true
        }

        srl_punch_card.setOnRefreshListener {
            getNewData(MAX_ID)
        }

        srl_punch_card.isRefreshing = true
        getNewData(MAX_ID)
    }

    private fun getNewData(lastId: Long) {
        lifecycleScope.launch(Dispatchers.IO) {
            try {
                val result = MyRetrofitClient.service.getPunchCards(lastId, true)
                withContext(Dispatchers.Main) {
                    if (result.code == 200) {
                        result.data?.let {
                            if (lastId == MAX_ID) {
                                adapter.setNewInstance(it)
                                adapter.loadMoreModule.loadMoreComplete()
                            } else {
                                if (it.isEmpty()) {
                                    adapter.loadMoreModule.loadMoreEnd()
                                } else {
                                    adapter.addData(it)
                                    adapter.loadMoreModule.loadMoreComplete()
                                }
                            }
                            adapter.notifyDataSetChanged()
                        }
                    } else {
                        MyToastUtil.showInfo(result.msg)
                    }
                }
            } catch (e: Exception) {
                withContext(Dispatchers.Main) {
                    MyToastUtil.showInfo(e.message)
                }
            } finally {
                withContext(Dispatchers.Main) {
                    srl_punch_card.isRefreshing = false
                }
            }
        }
    }
}