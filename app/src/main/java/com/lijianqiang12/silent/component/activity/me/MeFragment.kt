package com.lijianqiang12.silent.component.activity.me


import android.content.Intent
import android.os.Bundle
import android.util.Log
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.lifecycle.lifecycleScope
import com.blankj.utilcode.util.AppUtils
import com.blankj.utilcode.util.TimeUtils
import com.bumptech.glide.Glide
import com.bumptech.glide.load.engine.DiskCacheStrategy
import com.lijianqiang12.silent.*
import com.lijianqiang12.silent.component.activity.TheWebViewActivity
import com.lijianqiang12.silent.component.activity.base.BaseFragment
import com.lijianqiang12.silent.component.activity.me.developer_unlock.DeveloperUnlockActivity
import com.lijianqiang12.silent.component.activity.me.invite_gift.InviteGiftActivity
import com.lijianqiang12.silent.component.activity.me.notice.MsgActivity
import com.lijianqiang12.silent.component.activity.me.qa.QaActivity
import com.lijianqiang12.silent.component.activity.me.room_request.VerifyRoomRequestActivity
import com.lijianqiang12.silent.component.activity.me.setting.SettingsActivity
import com.lijianqiang12.silent.component.activity.me.theme.ThemeActivity
import com.lijianqiang12.silent.component.activity.me.userinfo.UserInfoActivity
import com.lijianqiang12.silent.component.activity.me.vip.FROM_WHERE
import com.lijianqiang12.silent.component.activity.me.vip.VIP2Activity
import com.lijianqiang12.silent.data.model.net.MyRetrofitClient
import com.lijianqiang12.silent.utils.*
import kotlinx.android.synthetic.main.fragment_me.*
import kotlinx.android.synthetic.main.fragment_me.view.*
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.launch
import kotlinx.coroutines.withContext


private const val ARG_PARAM1 = "param1"
private const val ARG_PARAM2 = "param2"

class MeFragment : BaseFragment() {
    private val TAG = "MeFragment"
    private var param1: String? = null
    private var param2: String? = null
    private lateinit var v: View

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        Log.d(TAG, "onCreate")
        arguments?.let {
            param1 = it.getString(ARG_PARAM1)
            param2 = it.getString(ARG_PARAM2)
        }
    }


    override fun onCreateView(inflater: LayoutInflater, container: ViewGroup?, savedInstanceState: Bundle?): View {
        Log.d(TAG, "onCreateView")
        v = inflater.inflate(R.layout.fragment_me, container, false)
        return v
    }

    override fun lazyInit() {
//        val defaultUrl = "https://offphone-1252369707.file.myqcloud.com/2020-08-22-O1CN01qBFTCc2GyWTJTJpDb_--2633239084.png"

        iv_notice_me.setOnClickListener {

            MyUtil.checkLoginAndDo(requireActivity()) {

                lifecycleScope.launch(Dispatchers.IO) {
                    try {
                        val result = MyRetrofitClient.service.readMsg()
                        if (result.code != 200) {
                            MyToastUtil.showInfo(result.msg)
                        }
                    } catch (e: Exception) {

                    }
                }
                startActivity(Intent(requireContext(), MsgActivity::class.java))
            }
        }

        //邀请送会员
        cl_invite_gift.setOnClickListener {
            startActivity(Intent(requireContext(), InviteGiftActivity::class.java))
        }

        //集赞送会员
        cl_invite_xiaohongshu.setOnClickListener {
            val intent = Intent(requireContext(), TheWebViewActivity::class.java)
            intent.putExtra("title", "小红书集赞有奖")
            intent.putExtra("url", MMKVUtils.getString(MyConstants.SP_KEY_XIAOHONGSHU_URL, ""))
            this.startActivity(intent)
        }

        //集赞送会员
        cl_invite_zhihu.setOnClickListener {
            val intent = Intent(requireContext(), TheWebViewActivity::class.java)
            intent.putExtra("title", "知乎集赞有奖")
            intent.putExtra("url", MMKVUtils.getString(MyConstants.SP_KEY_ZHIHU_URL, ""))
            this.startActivity(intent)
        }

        //集赞送会员
        cl_invite_bilibili.setOnClickListener {
            val intent = Intent(requireContext(), TheWebViewActivity::class.java)
            intent.putExtra("title", "Bilibili集赞有奖")
            intent.putExtra("url", MMKVUtils.getString(MyConstants.SP_KEY_BILIBILI_URL, ""))
            this.startActivity(intent)
        }


        v.cl_vip.setOnClickListener {
            val intent = Intent(requireActivity(), VIP2Activity::class.java)
            intent.putExtra(FROM_WHERE, "MeFragmentCard")
            startActivity(intent)
        }

        //主题
        v.cl_me_item_1.setOnClickListener {
            startActivity(Intent(requireActivity(), ThemeActivity::class.java))
        }

        //设置
        v.cl_me_item_setting.setOnClickListener {
            startActivity(Intent(requireActivity(), SettingsActivity::class.java))
        }

        //QQ群
        if (MMKVUtils.getBoolean(MyConstants.SP_KEY_CONFIG_SHOW_QQ, false)) {
            v.mcv_qq.visibility = View.VISIBLE

            v.cl_me_item_2.setOnClickListener {

                MyUtil.checkLoginAndDo(requireActivity()) {
                    startActivity(Intent(requireContext(), UnlockFriendActivity::class.java))
                }

//                LogUtils.d(MMKVUtils.getString(MyConstants.SP_KEY_CONFIG_QQ_LINK, "klCEhalSR1c-RvPCNEQcyeT7KXWyxbke"))

//                openFromWebsite(requireContext(),"${MMKVUtils.getString(MyConstants.SP_KEY_CONFIG_QQ_LINK, "")}")

//                val intent = Intent()
//                intent.data = Uri.parse("${MMKVUtils.getString(MyConstants.SP_KEY_CONFIG_QQ_LINK, "klCEhalSR1c-RvPCNEQcyeT7KXWyxbke")}")
////                intent.data = Uri.parse("https://qun.qq.com/qqweb/qunpro/share?_wv=3&_wwv=128&appChannel=share&inviteCode=25MuDm&from=246611")
////                intent.data = Uri.parse("https://targurl2.clewm.net/middle?coding=FyXEqn&targurl=aHR0cHM6Ly9xdW4ucXEuY29tL3Fxd2ViL3F1bnByby9zaGFyZT9fd3Y9MyZfd3d2PTEyOCZhcHBDaGFubmVsPXNoYXJlJmludml0ZUNvZGU9MVNVZE9HJmZyb209MjQ2NjEx&key=1873f16ac9067777b51837aa56a7a619de898c0044&icp=1&isp=0")
//                try {
//                    startActivity(intent)
//                    MyToastUtil.showInfo("正在跳转QQ群：${MMKVUtils.getString(MyConstants.SP_KEY_CONFIG_QQ_NUMBER, "624329715")}")
//                } catch (e: Exception) {
//                    MyToastUtil.showError("请确认手机已安装QQ")
//                }
            }
        } else {
            v.mcv_qq.visibility = View.GONE
        }

        //微信客服
        if (MMKVUtils.getBoolean(MyConstants.SP_KEY_SHOW_COMPLAIN, false)) {
            v.mcv_wx.visibility = View.VISIBLE
            v.cl_me_item_wx.setOnClickListener {
                MyUtil.complaint(requireActivity())
            }
        } else {
            v.mcv_wx.visibility = View.GONE
        }


        //微信群
//        if (true) {
        if (MMKVUtils.getBoolean(MyConstants.SP_KEY_CONFIG_SHOW_WX, false)) {
            v.mcv_wx.visibility = View.VISIBLE

            v.cl_me_item_wx.setOnClickListener {
                startActivity(Intent(requireContext(), WXActivity::class.java))
//                NormalDialog(this).apply {
//                    setTitle("加入微信群")
//                    setContent("1.复制微信公众号名『每日宝藏软件』\n2.在微信中搜索并关注公众号\n3.向公众号对话框中发送“远离手机加群”")
////                isCancelable = false
//                    setGravity(Gravity.START)
//                    setOnNormalOKClickListener("复制公众号", object : OnOKClickListener {
//                        override fun onclick() {
//                            val clipboard: ClipboardManager =
//                                requireContext().applicationContext.getSystemService(Context.CLIPBOARD_SERVICE) as ClipboardManager
//                            val clipData = ClipData.newPlainText(null, "每日宝藏软件")
//                            clipboard.setPrimaryClip(clipData)
//                            MyToastUtil.showSuccess("复制成功")
//                            try {
//                                val intent = Intent()
//                                val cmp = ComponentName("com.tencent.mm", "com.tencent.mm.ui.LauncherUI")
//                                intent.action = Intent.ACTION_MAIN
//                                intent.addCategory(Intent.CATEGORY_LAUNCHER)
//                                intent.addFlags(Intent.FLAG_ACTIVITY_NEW_TASK)
//                                intent.component = cmp
//                                startActivity(intent)
//                            } catch (e: Exception) {
//
//                            }
//                        }
//                    })
//                    setOnNormalCancelClickListener("一般般", object : OnCancelClickListener {
//                        override fun onclick() {
//                        }
//                    })
//                    show()
//                }
            }
        } else {
            v.mcv_wx.visibility = View.GONE
        }

        //我们的产品
        if (MMKVUtils.getBoolean(MyConstants.SP_KEY_CONFIG_SHOW_PRODUCT, false)
            && (!MMKVUtils.getBoolean(MyConstants.SP_KEY_CONFIG_HAS_CLOSE_SHOW_PRODUCT, false))
        ) {
            v.cl_our_product.visibility = View.VISIBLE

            //关闭按钮
            if (MMKVUtils.getBoolean(MyConstants.SP_KEY_CONFIG_CAN_CLOSE_SHOW_PRODUCT, false)) {
                v.iv_show_product_close.visibility = View.VISIBLE
                v.iv_show_product_close.setOnClickListener {
                    MMKVUtils.put(MyConstants.SP_KEY_CONFIG_HAS_CLOSE_SHOW_PRODUCT, true)
                    v.cl_our_product.visibility = View.GONE
                }
            } else {
                v.iv_show_product_close.visibility = View.GONE
            }

            //青蛙Todo
            if (MMKVUtils.getBoolean(MyConstants.SP_KEY_CONFIG_SHOW_1, false)) {
                v.mcv_frogtodo.visibility = View.VISIBLE
                v.cl_frogtodo.setOnClickListener {
                    openFromMarket(requireContext(), "com.shuge888.frogtodo", "")

//                    try {
                    //                        val ii = Intent(Intent.ACTION_VIEW)
//                        ii.data = Uri.parse("market://details?id=" + "com.shuge888.frogtodo")
//                        startActivity(ii)
//                    } catch (e: Exception) {
//                        MyToastUtil.showError("您的手机上没有安装Android应用市场")
//                        e.printStackTrace()
//                    }
                }
            } else {
                v.mcv_frogtodo.visibility = View.GONE
            }

            //20秒护眼
            if (MMKVUtils.getBoolean(MyConstants.SP_KEY_CONFIG_SHOW_2, false)) {
                v.mcv_protecteyes.visibility = View.VISIBLE
                v.cl_protecteyes.setOnClickListener {
                    openFromMarket(requireContext(), "com.shuge888.protecteyes", "")

//                    try {
//                        val ii = Intent(Intent.ACTION_VIEW)
//                        ii.data = Uri.parse("market://details?id=" + "com.shuge888.protecteyes")
//                        startActivity(ii)
//                    } catch (e: Exception) {
//                        MyToastUtil.showError("您的手机上没有安装Android应用市场")
//                        e.printStackTrace()
//                    }
                }
            } else {
                v.mcv_protecteyes.visibility = View.GONE
            }

            //衣色
            if (MMKVUtils.getBoolean(MyConstants.SP_KEY_CONFIG_SHOW_3, false)) {
                v.mcv_colors.visibility = View.VISIBLE
                v.cl_colors.setOnClickListener {
                    openFromMarket(requireContext(), "com.shuge888.colors", "")

//                    try {
//                        val ii = Intent(Intent.ACTION_VIEW)
//                        ii.data = Uri.parse("market://details?id=" + "com.shuge888.protecteyes")
//                        startActivity(ii)
//                    } catch (e: Exception) {
//                        MyToastUtil.showError("您的手机上没有安装Android应用市场")
//                        e.printStackTrace()
//                    }
                }
            } else {
                v.mcv_colors.visibility = View.GONE
            }

        } else {
            v.cl_our_product.visibility = View.GONE
        }


        //房间审核
        if (MMKVUtils.getBoolean(MyConstants.SP_KEY_CONFIG_SHOW_ROOM_REQUEST, false)) {
            v.mcv_room_request.visibility = View.VISIBLE
            v.cl_me_item_9.setOnClickListener {

//                throw Exception()
                startActivity(Intent(requireContext(), VerifyRoomRequestActivity::class.java))
            }
        } else {
            v.mcv_room_request.visibility = View.GONE
        }

        //开发者解锁
        if (MMKVUtils.getBoolean(MyConstants.SP_KEY_CONFIG_SHOW_DEVELOPER_UNLOCK, false)) {
            v.mcv_developer_unlock.visibility = View.VISIBLE
            v.cl_me_item_developer_unlock.setOnClickListener {
                startActivity(Intent(requireContext(), DeveloperUnlockActivity::class.java))
            }
        } else {
            v.mcv_developer_unlock.visibility = View.GONE
        }

        v.profile_image.setOnClickListener {

            MyUtil.checkLoginAndDo(requireActivity()) {
                if (checkRunState()) {
                    startActivity(Intent(requireActivity(), UserInfoActivity::class.java))
                }
            }
        }
        //账号信息
        v.cl_me_item_3.setOnClickListener {

            MyUtil.checkLoginAndDo(requireActivity()) {
                if (checkRunState()) {
                    startActivity(Intent(requireActivity(), UserInfoActivity::class.java))
                }
            }
        }

        //问题反馈
        v.cl_me_item_4.setOnClickListener {
            startActivity(Intent(requireContext(), QaActivity::class.java))

//            val intent = Intent(requireContext(), WebViewActivity::class.java)
//
//            val openid = MMKVUtils.getInt(MyConstants.SP_KEY_USER_ID) // 用户的openid
//            val nickname = MMKVUtils.getString(MyConstants.SP_KEY_USERNAME) // 用户的nickname
//            val headimgurl = MMKVUtils.getString(MyConstants.SP_KEY_AVATAR)// 用户的头像url
//            var postData = "nickname=$nickname&avatar=$headimgurl&openid=$openid"
//
//            val clientInfo = Build.BRAND + " " + Build.MODEL + " " + getChannelName(MyApp.getInstance())!!
//            val clientVersion = AppUtils.getAppVersionName()
//            val osVersion = Build.VERSION.SDK_INT
//            postData += "&clientInfo=$clientInfo&clientVersion=$clientVersion&osVersion=$osVersion"
//
//            intent.putExtra("title", "问题反馈")
//            intent.putExtra("url", "https://support.qq.com/product/23010")
//            intent.putExtra("type", WebViewActivity.REQUEST_TYPE_POST)
//            intent.putExtra("data", postData)
//            requireContext().startActivity(intent)
        }

        //五星好评
        v.cl_me_item_5.setOnClickListener {
            MyToastUtil.showInfo("我们能够成长是因为有您同行")
            openFromMarket(requireContext(), "")

//            try {
//                val ii = Intent(Intent.ACTION_VIEW)
//                ii.data = Uri.parse("market://details?id=" + AppUtils.getAppPackageName())
//                startActivity(ii)
//            } catch (e: Exception) {
//                MyToastUtil.showError("您的手机上没有安装Android应用市场")
//                e.printStackTrace()
//            }
        }

        //邀请好友
        v.cl_me_item_6.setOnClickListener {
            if (MMKVUtils.getBoolean(MyConstants.SP_KEY_CONFIG_SHOW_INVITE_GIFT, false)) {
                startActivity(Intent(requireContext(), InviteGiftActivity::class.java))
            } else {

                val sharedIntent = Intent()
                //设置动作为Intent.ACTION_SEND
                sharedIntent.action = Intent.ACTION_SEND
                //设置为文本类型
                sharedIntent.type = "text/*"
                sharedIntent.putExtra(
                    Intent.EXTRA_TEXT,
                    "我在用【${AppUtils.getAppName()}】管控手机使用时间，从此告别手机控。"
                            + "https://a.app.qq.com/o/simple.jsp?pkgname=${AppUtils.getAppPackageName()}"
                )

                //设置要分享的内容
                startActivity(Intent.createChooser(sharedIntent, "分享到"))
            }


        }

        //关于
        v.cl_me_item_7.setOnClickListener {
            startActivity(Intent(requireActivity(), AboutActivity::class.java))
        }

        //用户指南
        v.cl_me_item_userguide.setOnClickListener {
            val intent = Intent(requireContext(), TheWebViewActivity::class.java)
            intent.putExtra("title", "用户指南")
            intent.putExtra("url", "https://help-offphone.shuge888.com")
            this.startActivity(intent)
        }

        //互推
//        if (MMKVUtils.getBoolean(MyConstants.SP_KEY_CONFIG_SHOW_IAMFINE, false)) {
//            v.mcv_i_am_fine.visibility = View.VISIBLE
//            v.cl_me_item_8.setOnClickListener {
//                startActivity(Intent(requireActivity(), HutuiActivity::class.java))
//            }
//        } else {
//            v.mcv_i_am_fine.visibility = View.GONE
//        }


    }


    companion object {
        @JvmStatic
        fun newInstance(param1: String, param2: String) =
            MeFragment().apply {
                arguments = Bundle().apply {
                    putString(ARG_PARAM1, param1)
                    putString(ARG_PARAM2, param2)
                }
            }
    }

    override fun onResume() {
        super.onResume()
        Log.d(TAG, "onResume")
//        MobclickAgent.onPageStart("MeFragment")

        val avatarUrl = MMKVUtils.getString(MyConstants.SP_KEY_AVATAR, DEFAULT_AVATAR)
        Glide.with(requireContext()).load(avatarUrl)
            //.transition(DrawableTransitionOptions.withCrossFade())
            .diskCacheStrategy(DiskCacheStrategy.AUTOMATIC)
            .into(v.profile_image)

        val userId = MMKVUtils.getInt(MyConstants.SP_KEY_USER_ID, -1)
        if (userId == -1) {
            v.tv_me_username.text = "未登录"
        } else {
            v.tv_me_username.text = "ID：${userId}    "//空格别删
        }


        when (MMKVUtils.getInt(MyConstants.SP_KEY_VIP_STATE, 0)) {
            VIP_STATE_FREE -> {
                v.cl_vip.setBackgroundResource(R.drawable.shape_me_card_vip)
                v.textView14.setTextColor(resources.getColor(R.color.colorVipEnd))
                v.textView14.text = "立即解锁"
                v.textView12.text = "永久VIP  "
                v.textView13.text = "限时优惠，即将下架"
            }

            VIP_STATE_NORMAL -> {
                v.cl_vip.setBackgroundResource(R.drawable.shape_me_card_vip_1)
                v.textView14.setTextColor(resources.getColor(R.color.colorVipGreyEnd))
                val endTime = MMKVUtils.getString(MyConstants.SP_KEY_VIP_END_TIME, "2020-01-01 00:00:00")
                val days = (TimeUtils.string2Millis(endTime) - System.currentTimeMillis()) / 1000 / 60 / 60 / 24
                v.textView14.text = "剩余${days}天"
                v.textView12.text = "VIP  "
                v.textView13.text = "永久版会员即将下架"
            }

            VIP_STATE_FOREVER -> {
                v.cl_vip.setBackgroundResource(R.drawable.shape_me_card_vip_2)
                v.textView14.setTextColor(resources.getColor(R.color.colorVipBlackEnd))
                v.textView14.text = "永久有效"
                v.textView12.text = "SVIP  "
                v.textView13.text = "永久享有全部服务"
            }
        }

        if (MMKVUtils.getBoolean(MyConstants.SP_KEY_CONFIG_SHOW_INVITE_GIFT, false)) {
            v.cl_invite_gift.visibility = View.VISIBLE
        } else {
            v.cl_invite_gift.visibility = View.GONE
        }

        if (MMKVUtils.getBoolean(MyConstants.SP_KEY_SHOW_XIAOHONGSHU, false)) {
            v.cl_invite_xiaohongshu.visibility = View.VISIBLE
        } else {
            v.cl_invite_xiaohongshu.visibility = View.GONE
        }

        if (MMKVUtils.getBoolean(MyConstants.SP_KEY_SHOW_ZHIHU, false)) {
            v.cl_invite_zhihu.visibility = View.VISIBLE
        } else {
            v.cl_invite_zhihu.visibility = View.GONE
        }

        if (MMKVUtils.getBoolean(MyConstants.SP_KEY_SHOW_BILIBILI, false)) {
            v.cl_invite_bilibili.visibility = View.VISIBLE
        } else {
            v.cl_invite_bilibili.visibility = View.GONE
        }


        lifecycleScope.launch(Dispatchers.IO) {
            try {
                val result = MyRetrofitClient.service.unreadMsgCount()
                withContext(Dispatchers.Main) {
                    if (result.code == 200) {

                        result.data?.let {
                            if (it.msgCount > 0) {
                                this@MeFragment.tv_notice_me_count.visibility = View.VISIBLE
                                this@MeFragment.tv_notice_me_count.text = "${it.msgCount}"
                            } else {
                                this@MeFragment.tv_notice_me_count.visibility = View.GONE
                            }
                        }
                    } else {
                        MyToastUtil.showInfo(result.msg)
                    }
                }
            } catch (e: Exception) {
                withContext(Dispatchers.Main) {
                    MyToastUtil.showInfo(e.message)
                }
            }
        }
    }


//    override fun onPause() {
//        super.onPause()
//        Log.d(TAG, "onPause")
//        MobclickAgent.onPageEnd("MeFragment")
//    }

    override fun onStart() {
        super.onStart()
        Log.d(TAG, "onStart")
    }

    override fun onStop() {
        super.onStop()
        Log.d(TAG, "onStop")
    }

//    private val shareListener = object : UMShareListener {
//        override fun onResult(p0: SHARE_MEDIA?) {
//        }
//
//        override fun onCancel(p0: SHARE_MEDIA?) {
//        }
//
//        override fun onError(p0: SHARE_MEDIA?, p1: Throwable?) {
//        }
//
//        override fun onStart(p0: SHARE_MEDIA?) {
//        }
//    }
}

