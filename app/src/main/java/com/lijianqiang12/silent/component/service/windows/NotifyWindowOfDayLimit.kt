package com.lijianqiang12.silent.component.service.windows

import android.animation.ObjectAnimator
import android.annotation.SuppressLint
import android.content.Context
import android.graphics.PixelFormat
import android.os.Build
import android.os.Handler
import android.view.Gravity
import android.view.LayoutInflater
import android.view.View
import android.view.WindowManager
import android.widget.TextView
import com.blankj.utilcode.util.ConvertUtils
import com.blankj.utilcode.util.ScreenUtils
import com.lijianqiang12.silent.R
import com.lijianqiang12.silent.utils.MyToastUtil
import com.lijianqiang12.silent.utils.MyWindowUtil

class NotifyWindowOfDayLimit(val context: Context) {
    //    private var windowCreated = false
//    private var windowShowing = false
    private lateinit var params: WindowManager.LayoutParams
    private lateinit var windowManager: WindowManager
    private var layout: View? = null

    @SuppressLint("ClickableViewAccessibility")
    private fun createRestView() {
        params = WindowManager.LayoutParams()

        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
            params.type = WindowManager.LayoutParams.TYPE_APPLICATION_OVERLAY
        } else {
            params.type = WindowManager.LayoutParams.TYPE_SYSTEM_ALERT
        }

        windowManager = context.applicationContext.getSystemService(Context.WINDOW_SERVICE) as WindowManager

        params.format = PixelFormat.TRANSLUCENT

        params.flags = params.flags or WindowManager.LayoutParams.FLAG_NOT_FOCUSABLE or
                WindowManager.LayoutParams.FLAG_NOT_TOUCH_MODAL or
                WindowManager.LayoutParams.FLAG_HARDWARE_ACCELERATED
        params.gravity = Gravity.START or Gravity.TOP
        params.x = ScreenUtils.getScreenWidth() * -1
        params.y = ConvertUtils.dp2px(228f)//ScreenUtils.getScreenHeight() / 8+ConvertUtils.dp2px(200f)
        params.width = WindowManager.LayoutParams.WRAP_CONTENT
        params.height = WindowManager.LayoutParams.WRAP_CONTENT

        layout = LayoutInflater.from(context).inflate(R.layout.layout_schedule_notify, null)
//        layout?.let {
////            var lastX = 0f
//            var lastY = 0f
//            it.setOnTouchListener { v, event ->
//
//                when (event.action) {
//                    MotionEvent.ACTION_DOWN -> {
//                        it.setBackgroundResource(R.drawable.shape_lock_view_rest)
////                        lastX = event.rawX
//                        lastY = event.rawY
//                    }
//                    MotionEvent.ACTION_MOVE -> {
////                        params.x += (event.rawX - lastX).toInt()
//                        params.y += (event.rawY - lastY).toInt()
//
////                        LogUtils.d("ACTION_MOVE, params.x=" + params.x + ", params.x=" + params.x + ",rawX=" + event.rawX + ",rawY=" + event.rawY + ",lastX=" + lastX + ",lastY=" + lastY)
//
//                        windowManager.updateViewLayout(it, params)
////                        lastX = event.rawX
//                        lastY = event.rawY
//
//                    }
//                    MotionEvent.ACTION_UP -> {
////                        if (it.width / 2 - event.x + event.rawX < ScreenUtils.getScreenWidth() / 2) {//left
////                            params.x = 0
////                            it.setBackgroundResource(R.drawable.shape_lock_view_rest_left)
////                        } else {
////                            params.x = ScreenUtils.getScreenWidth() - it.width
////                            it.setBackgroundResource(R.drawable.shape_lock_view_rest_right)
////                        }
//                        windowManager.updateViewLayout(it, params)
//
//                    }
//                }
//                false
//
//            }
//        }
//        windowCreated = true
    }

    //显示提醒
    fun showWindow(title: String, text: String) {
        if (layout == null) {
            createRestView()
        }

        if (!MyWindowUtil.isWindowShowing(layout!!)) {
//            windowShowing = true

            layout!!.findViewById<TextView>(R.id.tv_lock_view_rest).text = title
            layout!!.findViewById<TextView>(R.id.tv_tired_length).text = text
//        layout.findViewById<ImageView>(R.id.civ_monitor_app_icon).setImageDrawable(icon)

            try {
                windowManager.addView(layout!!, params)



            } catch (e: Exception) {
//                windowShowing = false
                MyToastUtil.showError("DayLimitNotifyWindow未成功获取到悬浮窗权限，请到系统设置或安全管家中重新授予，若已经授予，则可能是系统BUG，请重启手机")
            }
            ObjectAnimator.ofFloat(layout!!, "translationX", ScreenUtils.getScreenWidth() * -1f, -1 * ConvertUtils.dp2px(2.0f).toFloat())
            .setDuration(2000)
                .start()
        }
    }

    //隐藏疲劳提醒
    fun hideWindow() {
        if (layout != null) {
            if (MyWindowUtil.isWindowShowing(layout!!)) {
                try {
                    ObjectAnimator.ofFloat(layout!!, "translationX", -1 * ConvertUtils.dp2px(2.0f).toFloat(), ScreenUtils.getScreenWidth() * -1f)
                        .setDuration(2000)
                        .start()
                    Handler().postDelayed(kotlinx.coroutines.Runnable {
                        try {
                            windowManager.removeView(layout!!)
                        } catch (e: Exception) {

                        }
//                        windowShowing = false
                    }, 2500)
                } catch (e: Exception) {
                    MyToastUtil.showError("未成功获取到悬浮窗权限，请到系统设置或安全管家中重新授予，若已经授予，则可能是系统BUG，请重启手机")
                }
            }
        }
    }


    fun showAndHide(title: String, showContent: String) {
        if (layout == null || MyWindowUtil.isWindowShowing(layout!!)) return
        showWindow(title, showContent)
        Handler().postDelayed(kotlinx.coroutines.Runnable {
            hideWindow()
        }, 8000)
    }
}