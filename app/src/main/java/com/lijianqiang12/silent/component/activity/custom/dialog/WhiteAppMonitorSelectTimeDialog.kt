package com.lijianqiang12.silent.component.activity.custom.dialog

import android.graphics.Color
import android.graphics.drawable.ColorDrawable
import android.os.Bundle
import android.util.DisplayMetrics
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.view.WindowManager
import androidx.appcompat.app.AppCompatActivity
import androidx.fragment.app.Fragment
import com.lijianqiang12.silent.DIALOG_WIDTH_PERCENT
import com.lijianqiang12.silent.R
import com.lijianqiang12.silent.component.activity.base.BaseDialogFragment
import com.lijianqiang12.silent.component.activity.custom.TimePickerView
import kotlinx.android.synthetic.main.dialog_home_white_app_time.view.*


class WhiteAppMonitorSelectTimeDialog() : BaseDialogFragment() {

    constructor(fragment: Fragment) : this() {
        this.fragment = fragment
    }

    constructor(activity: AppCompatActivity) : this() {
        this.activity = activity
    }

    private var okListener: OnTimeLimitListener? = null
    private var cancelListener: OnCancelClickListener? = null
    private lateinit var v: View
    private var fragment: Fragment? = null
    private var activity: AppCompatActivity? = null

    private var title = ""
    private var content = ""
    private var okText = "确定"
    private var cancelText = "清除"
    private var timeLimitHour = 23
    private var timeLimitMinute = 59

    private var min = 0;
    private var max = 1440;

    fun setMax(arg: Int) {
        max = arg
    }

    fun setMin(arg: Int) {
        min = arg
    }

    fun setOkText(arg:String){
        okText = arg
    }

    fun setCancelText(arg:String){
        cancelText = arg
    }

    override fun onCreateView(inflater: LayoutInflater, container: ViewGroup?, savedInstanceState: Bundle?): View? {
        dialog!!.window!!.setBackgroundDrawable(ColorDrawable(Color.TRANSPARENT))
        if (savedInstanceState != null) {
            title = savedInstanceState.get("title") as String
            content = savedInstanceState.get("content") as String
            timeLimitHour = savedInstanceState.get("timeLimitHour") as Int
            timeLimitMinute = savedInstanceState.get("timeLimitMinute") as Int
        }
        v = inflater.inflate(R.layout.dialog_home_white_app_time, container, false)
        v.tv_dialog_home_all_time_title.text = title
        v.tv_dialog_home_all_time_content.text = content
        v.tv_dialog_home_all_time_ok.text = okText
        v.tv_dialog_home_all_time_cancel.text = cancelText



        val dataHour: MutableList<String> = mutableListOf()
        for (i in 0..23) {
            if (i < 10) {
                dataHour.add("0$i")
            } else {
                dataHour.add("$i")
            }
        }

        val dataMinute: MutableList<String> = mutableListOf()
        for (i in 0..59) {
            if (i < 10) {
                dataMinute.add("0$i")
            } else {
                dataMinute.add("$i")
            }
        }

        v.tpv_lock_fast_hour.setData(dataHour)
        v.tpv_lock_fast_minute.setData(dataMinute)

        v.tpv_lock_fast_hour.setSelected(timeLimitHour)
        v.tpv_lock_fast_minute.setSelected(timeLimitMinute)

        v.tpv_lock_fast_hour.setOnSelectListener(object : TimePickerView.onSelectListener {
            override fun onSelect(text: String?) {
                timeLimitHour = text!!.toInt()
            }
        })

        v.tpv_lock_fast_minute.setOnSelectListener(object : TimePickerView.onSelectListener {
            override fun onSelect(text: String?) {
                timeLimitMinute = text!!.toInt()
            }
        })


        v.tv_dialog_home_all_time_ok.setOnClickListener {
            okListener?.apply {
                onclick(timeLimitHour * 60 + timeLimitMinute)
            }
            <EMAIL>()
        }
        v.tv_dialog_home_all_time_cancel.setOnClickListener {
            cancelListener?.apply {
                onclick()
            }
            <EMAIL>()
        }
        return v
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)


    }

    fun setTitle(arg: String) {
        title = arg
    }

    fun setContent(arg: String) {
        content = arg
    }

    fun setTime(minute: Int) {
        var temp = 0
        if (minute > 60 * 24 - 1) {
            temp = 60 * 24 - 1
        } else {
            temp = minute
        }
        timeLimitHour = temp / 60
        timeLimitMinute = temp % 60
    }


    fun show() {
        fragment?.apply {
            super.show(this.requireFragmentManager(), "NormalDialog")
        }
        activity?.apply {
            super.show(this.supportFragmentManager, "NormalDialog")
        }
    }

    override fun onStart() {
        val params = dialog!!.window!!.attributes
        val dm: DisplayMetrics = resources.displayMetrics
//        val density = dm.density
        val width = dm.widthPixels
//        val height = dm.heightPixels
        params.width = (width * DIALOG_WIDTH_PERCENT).toInt()//ViewGroup.LayoutParams.MATCH_PARENT
//        params.width = width.coerceAtMost(height) * 3 / 4//ViewGroup.LayoutParams.MATCH_PARENT
        dialog!!.window!!.attributes = params as WindowManager.LayoutParams
        super.onStart()
    }

    fun setOnOKClickListener(okListener: OnTimeLimitListener) {
        this.okListener = okListener
    }

    fun setOnCancelClickListener(cancelListener: OnCancelClickListener) {
        this.cancelListener = cancelListener
    }

    interface OnTimeLimitListener {
        fun onclick(minute: Int)
    }

    override fun onSaveInstanceState(outState: Bundle) {
        super.onSaveInstanceState(outState)
        outState.putString("title", title)
        outState.putString("content", content)
        outState.putInt("timeLimitHour", timeLimitHour)
        outState.putInt("timeLimitMinute", timeLimitMinute)
    }

}