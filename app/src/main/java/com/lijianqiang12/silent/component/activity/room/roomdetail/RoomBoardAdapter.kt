package com.lijianqiang12.silent.component.activity.room.roomdetail

import android.view.View
import android.widget.ImageView
import android.widget.TextView
import com.bumptech.glide.Glide
import com.bumptech.glide.load.engine.DiskCacheStrategy
import com.chad.library.adapter.base.BaseQuickAdapter
import com.chad.library.adapter.base.module.LoadMoreModule
import com.chad.library.adapter.base.viewholder.BaseViewHolder
import com.lijianqiang12.silent.R
import com.lijianqiang12.silent.data.model.net.pojos.RoomDetailBoard
import com.lijianqiang12.silent.utils.TimeUtil

class RoomBoardAdapter(layoutRes: Int, list: MutableList<RoomDetailBoard>)
    : BaseQuickAdapter<RoomDetailBoard, BaseViewHolder>(layoutRes, list), LoadMoreModule {

    override fun convert(viewHolder: BaseViewHolder, item: RoomDetailBoard) {
        Glide.with(context).load(item.avatar)
            //.transition(DrawableTransitionOptions.withCrossFade())
            .diskCacheStrategy(DiskCacheStrategy.AUTOMATIC).into(viewHolder.getView(R.id.avatar))
        viewHolder.getView<TextView>(R.id.tv_name).text = item.username


        viewHolder.getView<TextView>(R.id.tv_word).text = item.word

        when (item.vipState) {
            0 -> {
                viewHolder.getView<TextView>(R.id.tv_vip).visibility = View.INVISIBLE
            }
            1 -> {
                viewHolder.getView<TextView>(R.id.tv_vip).visibility = View.VISIBLE
                viewHolder.getView<TextView>(R.id.tv_vip).text = "VIP"
                viewHolder.getView<TextView>(R.id.tv_vip).setTextColor(context.resources.getColor(R.color.colorBackText))
                viewHolder.getView<TextView>(R.id.tv_vip).setBackgroundResource(R.drawable.shape_get_vip_gradient_2dp)
            }
            2 -> {
                viewHolder.getView<TextView>(R.id.tv_vip).visibility = View.VISIBLE
                viewHolder.getView<TextView>(R.id.tv_vip).text = "SVIP"
                viewHolder.getView<TextView>(R.id.tv_vip).setTextColor(context.resources.getColor(R.color.colorWhiteText))
                viewHolder.getView<TextView>(R.id.tv_vip).setBackgroundResource(R.drawable.shape_get_vip_black_gradient_2dp)
            }
        }


        viewHolder.getView<TextView>(R.id.tv_brand).text = "${item.brand}  ${item.model}"
        viewHolder.getView<TextView>(R.id.tv_punch_time).text = TimeUtil.formatTimeLikeQQ(item.time)

        when (item.gender) {
            0 -> {
                viewHolder.getView<ImageView>(R.id.iv_gender).visibility = View.GONE
            }
            1 -> {
                viewHolder.getView<ImageView>(R.id.iv_gender).visibility = View.VISIBLE
                viewHolder.getView<ImageView>(R.id.iv_gender).setImageResource(R.drawable.ic_male)

            }
            2 -> {
                viewHolder.getView<ImageView>(R.id.iv_gender).visibility = View.VISIBLE
                viewHolder.getView<ImageView>(R.id.iv_gender).setImageResource(R.drawable.ic_female)
            }
        }
    }
}