package com.lijianqiang12.silent.component.activity.lock.tomato

import TomatoWhiteAppAdapter
import android.os.Bundle
import android.view.View
import androidx.activity.viewModels
import androidx.core.widget.NestedScrollView
import androidx.lifecycle.lifecycleScope
import androidx.recyclerview.widget.DiffUtil
import androidx.recyclerview.widget.GridLayoutManager
import androidx.recyclerview.widget.RecyclerView
import com.bumptech.glide.Glide
import com.bumptech.glide.load.engine.DiskCacheStrategy
import com.lijianqiang12.silent.*
import com.lijianqiang12.silent.data.model.db.LockConfig
import com.lijianqiang12.silent.data.model.db.Tomato
import com.lijianqiang12.silent.data.model.db.TomatoWithSub
import com.lijianqiang12.silent.data.model.db.WhiteApp
import com.lijianqiang12.silent.component.activity.base.BaseActivity
import com.lijianqiang12.silent.component.activity.custom.dialog.*
import com.lijianqiang12.silent.component.activity.lock.BgBottomSheetDialogFragment
import com.lijianqiang12.silent.component.activity.lock.whiteapp.TomatoWhiteBottomSheetDialogFragment
import com.lijianqiang12.silent.data.viewmodel.LockViewModel
import com.lijianqiang12.silent.utils.*
import com.xw.repo.BubbleSeekBar
import dagger.hilt.android.AndroidEntryPoint
import kotlinx.android.synthetic.main.activity_edit_tomato.*
import kotlinx.android.synthetic.main.include_lock_config.*
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.launch
import kotlinx.coroutines.withContext
import javax.inject.Inject

@AndroidEntryPoint
class EditTomatoActivity : BaseActivity() {

    @Inject
    lateinit var viewModel: LockViewModel

    private var saveMode = SAVE_MODE_CREATE

    private lateinit var tomato: Tomato
    private lateinit var lockConfig: LockConfig
    private lateinit var tomatoWithSub: TomatoWithSub
    private lateinit var whiteList: MutableList<WhiteApp>
    private var changed = false
    private var oldWhiteList: MutableList<WhiteApp> = mutableListOf()

    private lateinit var recyclerview: RecyclerView

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        setContentView(R.layout.activity_edit_tomato)


        if (null == intent.getParcelableExtra("tomatoWithSub")) {
            saveMode = SAVE_MODE_CREATE
            whiteList = mutableListOf()
            tomato = Tomato()
            lockConfig = tomato.lockConfig
            tomatoWithSub = TomatoWithSub(tomato, whiteList)
        } else {
            tomatoWithSub = intent.getParcelableExtra("tomatoWithSub")!!
            saveMode = SAVE_MODE_UPDATE
            tomato = tomatoWithSub.tomato
            lockConfig = tomato.lockConfig
            whiteList = tomatoWithSub.whiteList
        }

        //任务名
        val tvTomatoTitle = et_tomato_title
        tvTomatoTitle.text = tomato.title
        tvTomatoTitle.setOnClickListener {
            val dialog = EditTextDialog(this)
            dialog.run {
                setOnOKListener(object : EditTextDialog.OnOKListener {
                    override fun onclick(text: String) {
                        changed = true
                        tomato.title = text
                        tvTomatoTitle.setText(tomato.title)
                    }
                })
                show()
            }
        }

        //专注时长
        val tvTomatoLength = et_tomato_length
        tvTomatoLength.setText(TimeUtil.formatHHMMSimple(tomato.tomatoWorkLength))
        tvTomatoLength.setOnClickListener {
            val dialog = TimeSelectDialog(this)
            dialog.run {
                setHour(tomato.tomatoWorkLength / 60)
                setMinute(tomato.tomatoWorkLength % 60)
                setOnOKListener(object : TimeSelectDialog.OnOKListener {
                    override fun onclick(length: Int) {
                        if (length > 0) {
                            changed = true
                            tomato.tomatoWorkLength = length
                            tvTomatoLength.text = TimeUtil.formatHHMMSimple(tomato.tomatoWorkLength)
                        } else {
                            MyToastUtil.showError("时长必须大于0")
                        }
                    }
                })
                show()
            }
        }

        //休息时长
        val tvTomatoRestLength = et_tomato_rest_length
        tvTomatoRestLength.setText(TimeUtil.formatHHMMSimple(tomato.tomatoRestLength))
        tvTomatoRestLength.setOnClickListener {
            val dialog = TimeSelectDialog(this)
            dialog.run {
                setHour(tomato.tomatoRestLength / 60)
                setMinute(tomato.tomatoRestLength % 60)
                setOnOKListener(object : TimeSelectDialog.OnOKListener {
                    override fun onclick(length: Int) {
                        changed = true
                        tomato.tomatoRestLength = length
                        tvTomatoRestLength.setText(TimeUtil.formatHHMMSimple(tomato.tomatoRestLength))
                    }
                })
                show()
            }
        }

        //番茄个数
        val tvTomatoCount = et_tomato_count
        tvTomatoCount.setText("${tomato.tomatoCount}个")
        tvTomatoCount.setOnClickListener {
            val dialog = CountSelectDialog(this)
            dialog.run {
                setMax(100)
                setStart(1)
                setInitCount(tomato.tomatoCount)
                setOnOKListener(object : CountSelectDialog.OnOKListener {
                    override fun onclick(count: Int) {
                        changed = true
                        tomato.tomatoCount = count
                        tvTomatoCount.setText("${tomato.tomatoCount}个")
                    }
                })
                show()
            }
        }

        //长休息间隔
        val etTomatoLongRestPerCount = et_tomato_long_rest_per_count
        etTomatoLongRestPerCount.setText("${tomato.tomatoLongRestPerCount}个")
        etTomatoLongRestPerCount.setOnClickListener {
            val dialog = CountSelectDialog(this)
            dialog.run {
                setMax(100)
                setStart(1)
                setInitCount(tomato.tomatoLongRestPerCount)
                setOnOKListener(object : CountSelectDialog.OnOKListener {
                    override fun onclick(count: Int) {
                        changed = true
                        tomato.tomatoLongRestPerCount = count
                        etTomatoLongRestPerCount.setText("${tomato.tomatoLongRestPerCount}个")
                    }
                })
                show()
            }
        }

        //长休息时长
        val etTomatoRestLengthPer = et_tomato_rest_length_per
        etTomatoRestLengthPer.setText(TimeUtil.formatHHMMSimple(tomato.tomatoLongRestLength))
        etTomatoRestLengthPer.setOnClickListener {
            val dialog = TimeSelectDialog(this)
            dialog.run {
                setHour(tomato.tomatoLongRestLength / 60)
                setMinute(tomato.tomatoLongRestLength % 60)
                setOnOKListener(object : TimeSelectDialog.OnOKListener {
                    override fun onclick(length: Int) {
                        changed = true
                        tomato.tomatoLongRestLength = length
                        etTomatoRestLengthPer.setText(TimeUtil.formatHHMMSimple(tomato.tomatoLongRestLength))
                    }
                })
                show()
            }
        }

        //是否使用全局白名单
        if (lockConfig.whiteFollowGlobal) {
            rb_tomato_white_1.isChecked = true
            materialCardView1000.visibility = View.GONE
        } else {
            rb_tomato_white_2.isChecked = true
            materialCardView1000.visibility = View.VISIBLE
        }
        rg_tomato_white.setOnCheckedChangeListener { group, checkedId ->
            changed = true
            when (checkedId) {
                R.id.rb_tomato_white_1 -> {
                    lockConfig.whiteFollowGlobal = true
                    materialCardView1000.visibility = View.GONE
                }

                R.id.rb_tomato_white_2 -> {
                    if (MyUtil.isVIP()) {
                        lockConfig.whiteFollowGlobal = false
                        materialCardView1000.visibility = View.VISIBLE
                    } else {
                        rb_tomato_white_1.isChecked = true
//                        val intent = Intent(this, VIP2Activity::class.java)
//                        intent.putExtra(FROM_WHERE, "EditTomatoActivity_VIP可使用独立设置_白名单")
//                        startActivity(intent)
//                        MyToastUtil.showInfo(applicationContext, "VIP用户可使用独立设置")
                        DialogUtil.showVIPDialog(
                            this,
                            null,
                            "VIP用户可使用独立设置白名单，开通后，即可在不同的锁机任务使用不同的软件。",
                            "EditTomatoActivity_VIP可使用独立设置_白名单"
                        )

                    }
                }
            }
        }

        //白名单点击响应及白名单填充
        cl_edit_tomato_white_click.setOnClickListener {
            if (MyUtil.isVIP()) {
                val tomatoWhiteBottomSheetDialogFragment = TomatoWhiteBottomSheetDialogFragment(tomatoWithSub.whiteList)
                tomatoWhiteBottomSheetDialogFragment.run {
                    setOnDataChangedListener(object : TomatoWhiteBottomSheetDialogFragment.OnDataChangedListener {
                        override fun onDataChanged() {
                            changed = true
                            refreshWhiteListView()
                        }

                    })
                    show(supportFragmentManager, "ScheduleWhiteBottomSheetDialogFragment")
                }
            } else {
                DialogUtil.showVIPDialog(
                    this,
                    null,
                    "VIP用户可使用独立设置白名单，开通后，即可在不同的锁机任务使用不同的软件。",
                    "EditTomatoActivity_VIP可使用独立设置_白名单"
                )

            }

        }

        refreshWhiteListView()

        //背景图片
        if (lockConfig.bgUrlFollowGlobal) {
            rb_bg_1.isChecked = true
            materialCardView10000.visibility = View.GONE
        } else {
            rb_bg_2.isChecked = true
            materialCardView10000.visibility = View.VISIBLE
        }
        rg_bg.setOnCheckedChangeListener { group, checkedId ->
            changed = true
            when (checkedId) {
                R.id.rb_bg_1 -> {
                    lockConfig.bgUrlFollowGlobal = true
                    materialCardView10000.visibility = View.GONE
                }

                R.id.rb_bg_2 -> {
                    if (MyUtil.isVIP()) {
                        lockConfig.bgUrlFollowGlobal = false
                        materialCardView10000.visibility = View.VISIBLE
                    } else {
                        rb_bg_1.isChecked = true
//                        val intent = Intent(this, VIP2Activity::class.java)
//                        intent.putExtra(FROM_WHERE, "EditTomatoActivity_VIP可使用独立设置_背景")
//                        startActivity(intent)
//                        MyToastUtil.showInfo(applicationContext, "VIP用户可使用独立设置")
                        DialogUtil.showVIPDialog(
                            this,
                            null,
                            "VIP用户可使用独立锁机背景，开通后，即可在不同的锁机任务使用不同的背景图片。",
                            "EditTomatoActivity_VIP可使用独立设置_背景"
                        )

                    }

                }
            }
        }

        val glEditBg = gl_edit_bg
        if (lockConfig.bgUrl.isEmpty()) {
            lockConfig.bgUrl = DEFAULT_LOCK_BG
        }
        Glide.with(this@EditTomatoActivity).load(lockConfig.bgUrl)
            //.transition(DrawableTransitionOptions.withCrossFade())
            .diskCacheStrategy(DiskCacheStrategy.AUTOMATIC)
            .into(glEditBg)
        cl_edit_bg.setOnClickListener {
            BgBottomSheetDialogFragment.newInstance(lockConfig.bgUrl, "番茄锁机背景图片").apply {
                setOnBgSelectListener(object : BgBottomSheetDialogFragment.OnBgSelectListener {
                    override fun onSelect(imgUrl: String) {
                        lockConfig.bgUrl = imgUrl
                        Glide.with(this@EditTomatoActivity).load(lockConfig.bgUrl)
                            //.transition(DrawableTransitionOptions.withCrossFade())
                            .diskCacheStrategy(DiskCacheStrategy.AUTOMATIC)
                            .into(glEditBg)
                    }
                })
                show(supportFragmentManager, "BgBottomSheetDialogFragment")
            }
        }


        //开始提示音
        if (lockConfig.startVoiceNotifyFollowGlobal) {
            rb_voice_start_1.isChecked = true
            cl_voice_start.visibility = View.GONE
        } else {
            rb_voice_start_2.isChecked = true
            cl_voice_start.visibility = View.VISIBLE
        }
        rg_voice_start.setOnCheckedChangeListener { group, checkedId ->
            changed = true
            when (checkedId) {
                R.id.rb_voice_start_1 -> {
                    lockConfig.startVoiceNotifyFollowGlobal = true
                    cl_voice_start.visibility = View.GONE
                }

                R.id.rb_voice_start_2 -> {
                    if (MyUtil.isVIP()) {
                        lockConfig.startVoiceNotifyFollowGlobal = false
                        cl_voice_start.visibility = View.VISIBLE
                    } else {
                        rb_voice_start_1.isChecked = true
//                        val intent = Intent(this, VIP2Activity::class.java)
//                        intent.putExtra(FROM_WHERE, "EditTomatoActivity_VIP可使用独立设置_开始音乐")
//                        startActivity(intent)
//                        MyToastUtil.showInfo(applicationContext, "VIP用户可使用独立设置")
                        DialogUtil.showVIPDialog(
                            this,
                            null,
                            "VIP用户可使用独立提示音，开通后，即可在不同的锁机任务使用不同的提示音。",
                            "EditTomatoActivity_VIP可使用独立设置_开始音乐"
                        )

                    }

                }
            }
        }

        val tvVoiceStart = tv_voice_start
        tvVoiceStart.text = RING_NAME_ARRAY[lockConfig.startVoiceNotify]
        cl_voice_start.setOnClickListener {
            RingChooseDialog(this).apply {
                setIndex(lockConfig.startVoiceNotify)
                setOnRingSelectClickListener(object : RingChooseDialog.OnRingSelectListener {
                    override fun onSelect(index: Int) {
                        lockConfig.startVoiceNotify = index
                        tvVoiceStart.text = RING_NAME_ARRAY[lockConfig.startVoiceNotify]
                    }
                })
                show()
            }
        }


        //结束提示音
        if (lockConfig.endVoiceNotifyFollowGlobal) {
            rb_voice_end_1.isChecked = true
            cl_voice_end.visibility = View.GONE
        } else {
            rb_voice_end_2.isChecked = true
            cl_voice_end.visibility = View.VISIBLE
        }
        rg_voice_end.setOnCheckedChangeListener { group, checkedId ->
            changed = true
            when (checkedId) {
                R.id.rb_voice_end_1 -> {
                    lockConfig.endVoiceNotifyFollowGlobal = true
                    cl_voice_end.visibility = View.GONE
                }

                R.id.rb_voice_end_2 -> {
                    if (MyUtil.isVIP()) {
                        lockConfig.endVoiceNotifyFollowGlobal = false
                        cl_voice_end.visibility = View.VISIBLE
                    } else {
                        rb_voice_end_1.isChecked = true
//                        val intent = Intent(this, VIP2Activity::class.java)
//                        intent.putExtra(FROM_WHERE, "EditTomatoActivity_VIP可使用独立设置_结束音乐")
//                        startActivity(intent)
//                        MyToastUtil.showInfo(applicationContext, "VIP用户可使用独立设置")
                        DialogUtil.showVIPDialog(
                            this,
                            null,
                            "VIP用户可使用独立提示音，开通后，即可在不同的锁机任务使用不同的提示音。",
                            "EditTomatoActivity_VIP可使用独立设置_结束音乐"
                        )

                    }
                }
            }
        }
        val tvVoiceEnd = tv_voice_end
        tvVoiceEnd.text = RING_NAME_ARRAY[lockConfig.endVoiceNotify]
        cl_voice_end.setOnClickListener {
            RingChooseDialog(this).apply {
                setIndex(lockConfig.endVoiceNotify)
                setOnRingSelectClickListener(object : RingChooseDialog.OnRingSelectListener {
                    override fun onSelect(index: Int) {
                        lockConfig.endVoiceNotify = index
                        tvVoiceEnd.text = RING_NAME_ARRAY[lockConfig.endVoiceNotify]
                    }
                })
                show()
            }
        }


        //开始振动
        if (lockConfig.startShakeNotifyFollowGlobal) {
            rb_shake_start_1.isChecked = true
            sb_edit_shake_start.visibility = View.GONE
        } else {
            rb_shake_start_2.isChecked = true
            sb_edit_shake_start.visibility = View.VISIBLE
        }
        rg_shake_start.setOnCheckedChangeListener { group, checkedId ->
            changed = true
            when (checkedId) {
                R.id.rb_shake_start_1 -> {
                    lockConfig.startShakeNotifyFollowGlobal = true
                    sb_edit_shake_start.visibility = View.GONE
                }

                R.id.rb_shake_start_2 -> {
                    if (MyUtil.isVIP()) {
                        lockConfig.startShakeNotifyFollowGlobal = false
                        sb_edit_shake_start.visibility = View.VISIBLE
                    } else {
                        rb_shake_start_1.isChecked = true
//                        val intent = Intent(this, VIP2Activity::class.java)
//                        intent.putExtra(FROM_WHERE, "EditTomatoActivity_VIP可使用独立设置_开始震动")
//                        startActivity(intent)
//                        MyToastUtil.showInfo(applicationContext, "VIP用户可使用独立设置")
                        DialogUtil.showVIPDialog(
                            this,
                            null,
                            "VIP用户可使用独立震动强度，开通后，即可在不同的锁机任务使用不同的震动强度。",
                            "EditTomatoActivity_VIP可使用独立设置_开始震动"
                        )

                    }

                }
            }
        }

        sb_edit_shake_start.setCustomSectionTextArray { sectionCount, array ->
            array.clear()
            array.put(0, "不振动")
            array.put(1, "振1s")
            array.put(2, "振2s")
            array.put(3, "振3s")
            array.put(4, "振4s")
            array.put(5, "振5s")
            array
        }
        sb_edit_shake_start.setProgress(lockConfig.startShakeNotify / 1000f)
        sb_edit_shake_start.onProgressChangedListener = object : BubbleSeekBar.OnProgressChangedListenerAdapter() {
            override fun onProgressChanged(bubbleSeekBar: BubbleSeekBar?, progress: Int, progressFloat: Float, fromUser: Boolean) {
                super.onProgressChanged(bubbleSeekBar, progress, progressFloat, fromUser)
                lockConfig.startShakeNotify = (progressFloat * 1000).toLong()
            }
        }


        //结束振动
        if (lockConfig.endShakeNotifyFollowGlobal) {
            rb_shake_end_1.isChecked = true
            sb_edit_shake_end.visibility = View.GONE
        } else {
            rb_shake_end_2.isChecked = true
            sb_edit_shake_end.visibility = View.VISIBLE
        }
        rg_shake_end.setOnCheckedChangeListener { group, checkedId ->
            changed = true
            when (checkedId) {
                R.id.rb_shake_end_1 -> {
                    lockConfig.endShakeNotifyFollowGlobal = true
                    sb_edit_shake_end.visibility = View.GONE
                }

                R.id.rb_shake_end_2 -> {
                    if (MyUtil.isVIP()) {
                        lockConfig.endShakeNotifyFollowGlobal = false
                        sb_edit_shake_end.visibility = View.VISIBLE
                    } else {
                        rb_shake_end_1.isChecked = true
//                        val intent = Intent(this, VIP2Activity::class.java)
//                        intent.putExtra(FROM_WHERE, "EditTomatoActivity_VIP可使用独立设置_结束震动")
//                        startActivity(intent)
//                        MyToastUtil.showInfo(applicationContext, "VIP用户可使用独立设置")
                        DialogUtil.showVIPDialog(
                            this,
                            null,
                            "VIP用户可使用独立震动强度，开通后，即可在不同的锁机任务使用不同的震动强度。",
                            "EditTomatoActivity_VIP可使用独立设置_结束震动"
                        )

                    }

                }
            }
        }
        sb_edit_shake_end.setCustomSectionTextArray({ sectionCount, array ->
            array.clear()
            array.put(0, "不振动")
            array.put(1, "振1s")
            array.put(2, "振2s")
            array.put(3, "振3s")
            array.put(4, "振4s")
            array.put(5, "振5s")
            array
        })
        sb_edit_shake_end.setProgress(lockConfig.endShakeNotify / 1000f)
        sb_edit_shake_end.onProgressChangedListener = object : BubbleSeekBar.OnProgressChangedListenerAdapter() {
            override fun onProgressChanged(bubbleSeekBar: BubbleSeekBar?, progress: Int, progressFloat: Float, fromUser: Boolean) {
                super.onProgressChanged(bubbleSeekBar, progress, progressFloat, fromUser)
                lockConfig.endShakeNotify = (progressFloat * 1000).toLong()
            }
        }


        //屏蔽通知

        rg_tomato_notification.setOnCheckedChangeListener { group, checkedId ->
            changed = true
            when (checkedId) {
                R.id.rb_notification_0 -> {
                    tomatoWithSub.tomato.lockConfig.isRemoveNotificationFollowGlobal = true
                }

                R.id.rb_notification_1 -> {
                    if (MyUtil.isVIP()) {
                        if (PermissionUtil.isNotificationListenersEnabled(this)) {
                            tomatoWithSub.tomato.lockConfig.isRemoveNotificationFollowGlobal = false
                            tomatoWithSub.tomato.lockConfig.isRemoveNotification = true
                        } else {
                            rb_notification_0.isChecked = true
                            NormalDialog(this).run {
                                setTitle("缺少权限")
                                setContent("需要您授予“通知权限”，点击下方“确定”，跳转权限授予页面")
                                setOnNormalOKClickListener(object : OnOKClickListener {
                                    override fun onclick() {
                                        PermissionUtil.gotoNotificationAccessSetting(this@EditTomatoActivity)
                                    }
                                })
                                showDialog()
                            }
                        }
                    } else {
                        rb_notification_0.isChecked = true
//                        val intent = Intent(this, VIP2Activity::class.java)
//                        intent.putExtra(FROM_WHERE, "EditTomatoActivity_VIP可使用独立设置_屏蔽通知")
//                        startActivity(intent)
//                        MyToastUtil.showInfo(applicationContext, "VIP用户可使用独立设置")
                        DialogUtil.showVIPDialog(
                            this,
                            null,
                            "VIP用户可使用独立屏蔽通知设置，开通后，即可为不同的锁机任务分别设置是否屏蔽通知。",
                            "EditTomatoActivity_VIP可使用独立设置_屏蔽通知"
                        )

                    }

                }

                R.id.rb_notification_2 -> {
                    if (MyUtil.isVIP()) {
                        tomatoWithSub.tomato.lockConfig.isRemoveNotificationFollowGlobal = false
                        tomatoWithSub.tomato.lockConfig.isRemoveNotification = false
                    } else {
                        rb_notification_0.isChecked = true
//                        val intent = Intent(this, VIP2Activity::class.java)
//                        intent.putExtra(FROM_WHERE, "EditTomatoActivity_VIP可使用独立设置_屏蔽通知")
//                        startActivity(intent)
//                        MyToastUtil.showInfo(applicationContext, "VIP用户可使用独立设置")
                        DialogUtil.showVIPDialog(
                            this,
                            null,
                            "VIP用户可使用独立屏蔽通知设置，开通后，即可为不同的锁机任务分别设置是否屏蔽通知。",
                            "EditTomatoActivity_VIP可使用独立设置_屏蔽通知"
                        )

                    }

                }
            }
        }

        //静音

        rg_tomato_silent.setOnCheckedChangeListener { group, checkedId ->
            changed = true
            when (checkedId) {
                R.id.rb_silent_0 -> {
                    tomatoWithSub.tomato.lockConfig.isSilentFollowGlobal = true
                }

                R.id.rb_silent_1 -> {
                    if (MyUtil.isVIP()) {
                        if (PermissionUtil.hasPermissionVolume(this)) {
                            tomatoWithSub.tomato.lockConfig.isSilentFollowGlobal = false
                            tomatoWithSub.tomato.lockConfig.isSilent = true
                        } else {
                            rb_silent_0.isChecked = true
                            NormalDialog(this).run {
                                setTitle("缺少权限")
                                setContent("需要您授予“免打扰权限”，点击下方“确定”，跳转权限授予页面")
                                setOnNormalOKClickListener(object : OnOKClickListener {
                                    override fun onclick() {
                                        PermissionUtil.openPermissionVolume(this@EditTomatoActivity)
                                    }
                                })
                                showDialog()
                            }
                        }
                    } else {
                        rb_silent_0.isChecked = true
//                        val intent = Intent(this, VIP2Activity::class.java)
//                        intent.putExtra(FROM_WHERE, "EditTomatoActivity_VIP可使用独立设置_勿扰")
//                        startActivity(intent)
//                        MyToastUtil.showInfo(applicationContext, "VIP用户可使用独立设置")
                        DialogUtil.showVIPDialog(
                            this,
                            null,
                            "VIP用户可使用独立勿扰模式设置，开通后，即可为不同的锁机任务分别设置是否开启勿扰模式。",
                            "EditTomatoActivity_VIP可使用独立设置_勿扰"
                        )

                    }

                }

                R.id.rb_silent_2 -> {
                    if (MyUtil.isVIP()) {
                        tomatoWithSub.tomato.lockConfig.isSilentFollowGlobal = false
                        tomatoWithSub.tomato.lockConfig.isSilent = false
                    } else {
                        rb_silent_0.isChecked = true
//                        val intent = Intent(this, VIP2Activity::class.java)
//                        intent.putExtra(FROM_WHERE, "EditTomatoActivity_VIP可使用独立设置_勿扰")
//                        startActivity(intent)
//                        MyToastUtil.showInfo(applicationContext, "VIP用户可使用独立设置")
                        DialogUtil.showVIPDialog(
                            this,
                            null,
                            "VIP用户可使用独立勿扰模式设置，开通后，即可为不同的锁机任务分别设置是否开启勿扰模式。",
                            "EditTomatoActivity_VIP可使用独立设置_勿扰"
                        )

                    }

                }
            }
        }


        nestedScrollView.setOnScrollChangeListener { v: NestedScrollView?, scrollX: Int, scrollY: Int, oldScrollX: Int, oldScrollY: Int ->
            sb_edit_shake_start.correctOffsetWhenContainerOnScrolling()
            sb_edit_shake_end.correctOffsetWhenContainerOnScrolling()
        }




        iv_edit_tomato_return.setOnClickListener { back() }
        iv_edit_tomato_save.setOnClickListener { save() }


    }

    override fun onResume() {
        super.onResume()
        if (tomatoWithSub.tomato.lockConfig.isRemoveNotificationFollowGlobal) {
            rb_notification_0.isChecked = true
        } else {
            if (tomatoWithSub.tomato.lockConfig.isRemoveNotification && PermissionUtil.isNotificationListenersEnabled(this)) {
                rb_notification_1.isChecked = true
            } else {
                rb_notification_2.isChecked = true
            }
        }
        if (tomatoWithSub.tomato.lockConfig.isSilentFollowGlobal) {
            rb_silent_0.isChecked = true
        } else {
            if (tomatoWithSub.tomato.lockConfig.isSilent && PermissionUtil.hasPermissionVolume(this)) {
                rb_silent_1.isChecked = true
            } else {
                rb_silent_2.isChecked = true
            }
        }
    }

    private fun refreshWhiteListView() {
        recyclerview = rv_tomato_edit_white
        recyclerview.layoutManager = GridLayoutManager(this, MyConstants.WHITE_APP_COLUMN)
        val whiteAppAdapter = TomatoWhiteAppAdapter(this, R.layout.item_gridlayout_selected, mutableListOf())
        whiteAppAdapter.animationEnable = true
        recyclerview.adapter = whiteAppAdapter

        tomatoWithSub.whiteList.let {
            if (it.size == 0) {
                tv_user_white_empty_notice.visibility = View.VISIBLE
            } else {
                tv_user_white_empty_notice.visibility = View.GONE
            }

            lifecycleScope.launch(Dispatchers.Default) {
                val diffResult = DiffUtil.calculateDiff(DiffCallBack(oldWhiteList, it), true)
                oldWhiteList = it
                withContext(Dispatchers.Main) {
                    whiteAppAdapter.setNewInstance(it)
                    diffResult.dispatchUpdatesTo(whiteAppAdapter)
                }
            }
        }

//        val gridLayout = gl_tomato_edit_white
//        gridLayout.removeAllViews()
//        tomatoWithSub.whiteList.forEachIndexed { index, whiteApp ->
//            val itemView = LayoutInflater.from(this).inflate(R.layout.item_gridlayout_selected, null)
//
//            lifecycleScope.launch(Dispatchers.IO) {
//                val appIcon = getAppIcon(whiteApp.pkg, whiteApp.mainActivity)
//                withContext(Dispatchers.Main) {
//                    itemView.iv_app_icon.setImageDrawable(appIcon)
//                }
//            }
//
//
//            val rowSpec: GridLayout.Spec = GridLayout.spec(index / MyConstants.WHITE_APP_COLUMN, 1.0f)
//            val columnSpec: GridLayout.Spec = GridLayout.spec(index % MyConstants.WHITE_APP_COLUMN, 1.0f)
//            val params: GridLayout.LayoutParams = GridLayout.LayoutParams(rowSpec, columnSpec)
//            params.width = 0
//
//            gridLayout.addView(itemView, params)
//        }
//
//        tomatoWithSub.whiteList.let {
//            if (it.size == 0) {
//                tv_user_white_empty_notice.visibility = View.VISIBLE
//            } else {
//                tv_user_white_empty_notice.visibility = View.GONE
//            }
//            for (i in 0..(MyConstants.WHITE_APP_COLUMN - it.size)) {
//                val itemView = LayoutInflater.from(this).inflate(R.layout.item_gridlayout_selected, null)
//                val rowSpec: GridLayout.Spec = GridLayout.spec((i + it.size) / MyConstants.WHITE_APP_COLUMN, 1.0f)
//                val columnSpec: GridLayout.Spec = GridLayout.spec((i + it.size) % MyConstants.WHITE_APP_COLUMN, 1.0f)
//                val params: GridLayout.LayoutParams = GridLayout.LayoutParams(rowSpec, columnSpec)
//                params.width = 0
//
//                gridLayout.addView(itemView, params)
//            }
//        }
    }


    private fun back() {
        if (changed) {
            NormalDialog(this).run {
                setTitle("警告")
                setContent("您有数据尚未保存，是否先保存？")
                setOnNormalOKClickListener("保存并退出", object : OnOKClickListener {
                    override fun onclick() {
                        save()
                    }
                })
                setOnNormalCancelClickListener("直接退出", object : OnCancelClickListener {
                    override fun onclick() {
                        finish()
                    }
                })
                showDialog()
            }
        } else {
            finish()
        }
    }

    private fun save() {

        if (saveMode == SAVE_MODE_CREATE) {
            viewModel.createTomatoWithSub(tomatoWithSub)
        } else {
            viewModel.updateTomatoWithSub(tomatoWithSub)
        }
        finish()
    }

    override fun onBackPressed() {
        back()
    }

    private class DiffCallBack(val oldList: MutableList<WhiteApp>, val newList: MutableList<WhiteApp>) : DiffUtil.Callback() {

        override fun areItemsTheSame(oldItemPosition: Int, newItemPosition: Int): Boolean {
            return (oldList[oldItemPosition].whiteAppIndexId == newList[newItemPosition].whiteAppIndexId)
        }

        override fun getOldListSize(): Int {
            return oldList.size
        }

        override fun getNewListSize(): Int {
            return newList.size
        }

        override fun areContentsTheSame(oldItemPosition: Int, newItemPosition: Int): Boolean {
            return (oldList[oldItemPosition].pkg == newList[newItemPosition].pkg)
                    && (oldList[oldItemPosition].mainActivity == newList[newItemPosition].mainActivity)
                    && (oldList[oldItemPosition].maxLen == newList[newItemPosition].maxLen)
        }

    }
}
