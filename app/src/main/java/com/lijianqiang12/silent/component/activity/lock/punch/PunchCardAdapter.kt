package com.lijianqiang12.silent.component.activity.lock.punch

import android.view.View
import android.widget.ImageView
import android.widget.TextView
import com.bumptech.glide.Glide
import com.bumptech.glide.load.engine.DiskCacheStrategy
import com.chad.library.adapter.base.BaseQuickAdapter
import com.chad.library.adapter.base.module.LoadMoreModule
import com.chad.library.adapter.base.viewholder.BaseViewHolder
import com.lijianqiang12.silent.R
import com.lijianqiang12.silent.data.model.net.pojos.ThePunchCard
import com.lijianqiang12.silent.utils.TimeUtil
import com.lijianqiang12.silent.utils.getColorFromTheme

class PunchCardAdapter(layoutRes: Int, list: MutableList<ThePunchCard>)
    : BaseQuickAdapter<ThePunchCard, BaseViewHolder>(layoutRes, list), LoadMoreModule {

    override fun convert(viewHolder: BaseViewHolder, item: ThePunchCard) {
        Glide.with(context).load(item.avatar)
            //.transition(DrawableTransitionOptions.withCrossFade())
            .diskCacheStrategy(DiskCacheStrategy.AUTOMATIC)
            .into(viewHolder.getView(R.id.avatar))
        viewHolder.getView<TextView>(R.id.tv_name).text = item.username

        when (item.style) {
            0 -> viewHolder.getView<ImageView>(R.id.type).setImageResource(R.drawable.ic_punch_card_normal)
            1 -> viewHolder.getView<ImageView>(R.id.type).setImageResource(R.drawable.ic_punch_card_morning)
            2 -> viewHolder.getView<ImageView>(R.id.type).setImageResource(R.drawable.ic_punch_card_night)
        }


        if (item.word.isEmpty()) {
            viewHolder.getView<TextView>(R.id.tv_word).visibility = View.GONE
        } else {
            viewHolder.getView<TextView>(R.id.tv_word).visibility = View.VISIBLE
            viewHolder.getView<TextView>(R.id.tv_word).text = item.word
        }

        viewHolder.getView<TextView>(R.id.tv_brand).text = item.brand

        when (item.vipState) {
            0 -> {
                viewHolder.getView<TextView>(R.id.tv_vip).visibility = View.INVISIBLE
            }
            1 -> {
                viewHolder.getView<TextView>(R.id.tv_vip).visibility = View.VISIBLE
                viewHolder.getView<TextView>(R.id.tv_vip).text = "VIP"
                viewHolder.getView<TextView>(R.id.tv_vip).setTextColor(context.resources.getColor(R.color.colorBackText))
                viewHolder.getView<TextView>(R.id.tv_vip).setBackgroundResource(R.drawable.shape_get_vip_gradient_2dp)
            }
            2 -> {
                viewHolder.getView<TextView>(R.id.tv_vip).visibility = View.VISIBLE
                viewHolder.getView<TextView>(R.id.tv_vip).text = "SVIP"
                viewHolder.getView<TextView>(R.id.tv_vip).setTextColor(context.resources.getColor(R.color.colorWhiteText))
                viewHolder.getView<TextView>(R.id.tv_vip).setBackgroundResource(R.drawable.shape_get_vip_black_gradient_2dp)
            }
        }


        viewHolder.getView<TextView>(R.id.tv_lock_number).text = "${item.lockNumber}次"
        viewHolder.getView<TextView>(R.id.tv_go_on_days).text = "${item.goOnDays}天"
        viewHolder.getView<TextView>(R.id.tv_length).text = TimeUtil.formatHHMMSimpleEn((item.length))

        when {
            item.totalLength / 60 / 60 > 0 -> {
                viewHolder.getView<TextView>(R.id.tv_total_length).text = "${item.totalLength / 60 / 60}h"
            }
            item.totalLength / 60 > 0 -> {
                viewHolder.getView<TextView>(R.id.tv_total_length).text = "${item.totalLength / 60}m"
            }
            else -> {
                viewHolder.getView<TextView>(R.id.tv_total_length).text = "${item.totalLength}s"
            }
        }

        if (item.isStar) {
            viewHolder.getView<ImageView>(R.id.iv_lock_like).setColorFilter(context.resources.getColor(R.color.colorRed))
        } else {
            viewHolder.getView<ImageView>(R.id.iv_lock_like).setColorFilter(getColorFromTheme(context, R.attr.custom_attr_app_text_7))
        }
        viewHolder.getView<TextView>(R.id.tv_like_count).text = "${item.starCount}"

        viewHolder.getView<TextView>(R.id.tv_punch_time).text = TimeUtil.formatTimeLikeQQ(item.punchCardTime)


        when (item.gender) {
            0 -> {
                viewHolder.getView<ImageView>(R.id.iv_gender).visibility = View.GONE
            }
            1 -> {
                viewHolder.getView<ImageView>(R.id.iv_gender).visibility = View.VISIBLE
                viewHolder.getView<ImageView>(R.id.iv_gender).setImageResource(R.drawable.ic_male)

            }
            2 -> {
                viewHolder.getView<ImageView>(R.id.iv_gender).visibility = View.VISIBLE
                viewHolder.getView<ImageView>(R.id.iv_gender).setImageResource(R.drawable.ic_female)
            }
        }
    }
}