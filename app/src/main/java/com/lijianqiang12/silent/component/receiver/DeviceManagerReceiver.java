package com.lijianqiang12.silent.component.receiver;

import android.app.admin.DeviceAdminReceiver;
import android.content.Context;
import android.content.Intent;

import com.lijianqiang12.silent.utils.MyToastUtil;

public class DeviceManagerReceiver extends DeviceAdminReceiver {

    @Override
    public void onDisabled(Context context, Intent intent) {
        MyToastUtil.Companion.showSuccess("已取消设备管理器");
        super.onDisabled(context, intent);
    }

    @Override
    public void onEnabled(Context context, Intent intent) {
        MyToastUtil.Companion.showSuccess("已添加到设备管理器");
        super.onEnabled(context, intent);
    }

    @Override
    public CharSequence onDisableRequested(Context context, Intent intent) {
//        Toast.makeText(context, "disable dpm request", Toast.LENGTH_SHORT).show();
        return super.onDisableRequested(context, intent);
    }

}
