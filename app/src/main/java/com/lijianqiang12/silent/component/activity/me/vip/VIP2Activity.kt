package com.lijianqiang12.silent.component.activity.me.vip

import android.content.Intent
import android.os.Bundle
import android.os.Handler
import android.os.Message
import android.view.LayoutInflater
import android.view.View
import androidx.core.view.children
import androidx.lifecycle.Observer
import androidx.lifecycle.lifecycleScope
import com.afollestad.materialdialogs.MaterialDialog
import com.afollestad.materialdialogs.input.input
import com.alipay.sdk.app.PayTask
import com.blankj.utilcode.util.ConvertUtils
import com.blankj.utilcode.util.LogUtils
import com.bumptech.glide.Glide
import com.bumptech.glide.load.engine.DiskCacheStrategy
import com.jeremyliao.liveeventbus.LiveEventBus
import com.lijianqiang12.silent.*
import com.lijianqiang12.silent.component.activity.TheWebViewActivity
import com.lijianqiang12.silent.component.activity.base.BaseActivity
import com.lijianqiang12.silent.component.activity.custom.dialog.*
import com.lijianqiang12.silent.data.model.net.MyRetrofitClient
import com.lijianqiang12.silent.data.model.net.pojos.Money
import com.lijianqiang12.silent.data.viewmodel.LoginViewModel
import com.lijianqiang12.silent.data.viewmodel.VIPViewModel
import com.lijianqiang12.silent.utils.*
import dagger.hilt.android.AndroidEntryPoint
import androidx.activity.viewModels
import com.tencent.mm.opensdk.openapi.IWXAPI
import com.tencent.mm.opensdk.openapi.WXAPIFactory
import kotlinx.android.synthetic.main.activity_vip_2.*
import kotlinx.android.synthetic.main.item_gift.view.*
import kotlinx.android.synthetic.main.item_money_2.view.*
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.ObsoleteCoroutinesApi
import kotlinx.coroutines.channels.ticker
import kotlinx.coroutines.launch
import kotlinx.coroutines.withContext

val FROM_WHERE = "from_where"

@AndroidEntryPoint
class VIP2Activity : BaseActivity() {

    private val vipViewModel: VIPViewModel by viewModels()
    private val loginViewModel: LoginViewModel by viewModels()
    private lateinit var dialog: MyProgressDialog
    private var moneyList = mutableListOf<Money>()

    private var vipIndex = -1
    private var notifyForever = false
    private var notifyDeliveryInfo = false
    private lateinit var api: IWXAPI
    private val SDK_PAY_FLAG = 1

    private var strFromWhere: String = ""

    private var daojishi = -1

    @ObsoleteCoroutinesApi
    private val timeTicker = ticker(delayMillis = 1000, initialDelayMillis = 0)

    private fun refreshDaojishi() {
        if (daojishi >= 0) {
            tv_daojishi.visibility = View.VISIBLE
            tv_daojishi.text = "优惠倒计时：${formatHHMMSS(daojishi.toLong())}"
            daojishi--
        } else {
            tv_daojishi.visibility = View.GONE
        }
    }

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        setContentView(R.layout.activity_vip_2)

        lifecycleScope.launch(Dispatchers.Default) {
            repeat(100000000) {
                timeTicker.receive()

                withContext(Dispatchers.Main) {
                    refreshDaojishi()
                }
            }
        }

        val toast = intent.getStringExtra("toast")
        toast?.let {
            MyToastUtil.showInfo(it)
        }

//        val fromWhere = intent.getStringExtra(FROM_WHERE)
//        fromWhere?.let {
//            val value = HashMap<String, Any>()
//            strFromWhere = it
//            value["fromWhere"] = strFromWhere
//            MobclickAgent.onEventObject(this, "startVipActivity", value)
//        }

        tv_tousujianyi.setOnClickListener {
            MyUtil.complaint(this)
        }


        // ViewModels 现在通过 Hilt 自动注入
        api = WXAPIFactory.createWXAPI(this, MyConstants.WX_APP_ID)
        dialog = MyProgressDialog(activity = this)
        iv_close_vip.setOnClickListener { finish() }
        tv_vip_notice.setOnClickListener {
            val intent = Intent(this, TheWebViewActivity::class.java)
            intent.putExtra("title", "")
            intent.putExtra("url", "https://help-offphone.shuge888.com/protocal/vip_agreement")
            startActivity(intent)
        }

        tv_use_code.setOnClickListener {

            MyUtil.checkLoginAndDo(this) {

                when (MMKVUtils.getInt(MyConstants.SP_KEY_VIP_STATE, VIP_STATE_FREE)) {
                    VIP_STATE_FREE, VIP_STATE_NORMAL -> {
                        MaterialDialog(this)
                            .cornerRadius(8.0f)
                            .title(text = "兑换码兑换")
                            .input(hint = "请输入兑换码") { dialog, input ->
                                lifecycleScope.launch(Dispatchers.IO) {
                                    try {
                                        val result = MyRetrofitClient.service.duihuanma(input.toString())
                                        if (result.code == 200) {
                                            withContext(Dispatchers.Main) {
                                                MyToastUtil.showInfo("兑换成功")
                                            }
                                            val result2 = MyRetrofitClient.service.refreshState()
                                            withContext(Dispatchers.Main) {
                                                if (result2.code == 200) {
                                                    MMKVUtils.put(MyConstants.SP_KEY_USERNAME, result2.data!!.username)
                                                    MMKVUtils.put(MyConstants.SP_KEY_VIP_STATE, result2.data.vipState)
                                                    MMKVUtils.put(MyConstants.SP_KEY_VIP_END_TIME, result2.data.vipEndTime)
                                                    MMKVUtils.put(MyConstants.SP_KEY_AVATAR, result2.data.avatar)
                                                    MMKVUtils.put(MyConstants.SP_KEY_FORCE_QUITE_PWD, result2.data.unlockPwd)
                                                    MMKVUtils.put(MyConstants.SP_KEY_BIND_MOBILE, result2.data.bindMobile)
                                                    finish()
                                                } else {
                                                    MyToastUtil.showError(result.msg)
                                                }
                                            }
                                        } else {
                                            withContext(Dispatchers.Main) {
                                                MyToastUtil.showInfo(result.msg)
                                            }
                                        }
                                    } catch (e: Exception) {
                                        withContext(Dispatchers.Main) {
                                            MyToastUtil.showInfo(e.message)
                                        }
                                    }
                                }
                            }.positiveButton(text = "兑换").negativeButton(text = "取消").show()
                    }

                    VIP_STATE_FOREVER -> MyToastUtil.showInfo("您已经是尊贵的永久版用户，无需重复开通")
                }
            }
        }

        tv_buy_history.setOnClickListener {

            MyUtil.checkLoginAndDo(this) {

                startActivity(Intent(this, BuyHistoryActivity::class.java))
            }
        }


        LiveEventBus.get(LiveBus.PAY_FOR_VIP_SUCCEED, Boolean::class.java).observe(this, Observer {
            if (it) {
                paySucceed(PAY_TYPE_WXPAY)
            } else {
                MyToastUtil.showInfo("支付失败")
            }
        })

        val rgMoneys = ll_vip_item_2
        val rgGifts = gl_gift

        vipViewModel.VIPMoneyLiveData.observe(this) { networkState ->
            LogUtils.d(networkState.toString())
            if (networkState.state == 0) {

                daojishi = networkState.data!!.daojishi

                refreshDaojishi()

                if (vipIndex == -1) {
                    vipIndex = networkState.data!!.selectIndex
                }
                notifyForever = networkState.data!!.notifyForever
                notifyDeliveryInfo = networkState.data!!.notifyDeliveryInfo
                moneyList = networkState.data!!.moneyList
                rgMoneys.removeAllViews()
                networkState.data.moneyList.forEachIndexed { index, money ->
                    val item = LayoutInflater.from(this).inflate(R.layout.item_money_2, null)
                    if (money.text2.isEmpty()) {
                        item.tv_vip_text_2.visibility = View.GONE
                    } else {
                        item.tv_vip_text_2.visibility = View.VISIBLE
                        item.cl_money_2.setPadding(0, ConvertUtils.dp2px(8.0f), 0, ConvertUtils.dp2px(8.0f))
                        item.tv_vip_text_2.text = money.text2
                    }

                    item.tv_vip_money_2.text = "¥${money.currentPrice} / ${money.title2}"
                    item.tv_vip_text_1.text = money.text

                    item.setOnClickListener {
                        vipIndex = index
                        chooseMoneyItem(vipIndex)
                    }

                    if (money.giftPicUrl.isEmpty()) {
                        item.iv_vip_item_gift_img.visibility = View.GONE
                        item.tv_vip_item_gift_text.visibility = View.GONE
                    } else {
                        item.iv_vip_item_gift_img.visibility = View.VISIBLE
                        item.tv_vip_item_gift_text.visibility = View.VISIBLE
                        Glide.with(this).load(money.giftPicUrl)
                            //.transition(DrawableTransitionOptions.withCrossFade())
                            .diskCacheStrategy(DiskCacheStrategy.AUTOMATIC)
                            .into(item.iv_vip_item_gift_img)
                        item.tv_vip_item_gift_text.text = money.giftText
                    }

                    rgMoneys.addView(item)
                }

                chooseMoneyItem(vipIndex)
//                btn_vip_2.visibility = View.VISIBLE

                if (networkState.data.showGift) {
                    cl_gift.visibility = View.VISIBLE
                    tv_choujiang_result.text = networkState.data.giftResult
                    tv_choujiang_notice.text = networkState.data.giftRule

                    if (networkState.data.show100) {
                        tv_gift100.visibility = View.VISIBLE
                    } else {
                        tv_gift100.visibility = View.GONE
                    }

                    if (networkState.data.showChangeAddress) {
                        btn_change_address.visibility = View.VISIBLE
                        tv_gift_address.visibility = View.VISIBLE
                        tv_gift_address.text = networkState.data.giftAddress
                        btn_change_address.setOnClickListener {

                            MyUtil.checkLoginAndDo(this) {
                                showDeliveryInfo(true)
                            }
                        }
                    } else {
                        btn_change_address.visibility = View.GONE
                        tv_gift_address.visibility = View.GONE
                    }
                    rgGifts.removeAllViews()
                    networkState.data.giftList.forEachIndexed { index, gift ->
                        val item = LayoutInflater.from(this).inflate(R.layout.item_gift, null)

                        item.tv_gift_title.text = gift.giftTitle
                        item.tv_gift_price.text = "价值 ¥${gift.price}"
                        item.tv_gift_stock.text = gift.stock

                        Glide.with(this).load(gift.giftPic)
                            //.transition(DrawableTransitionOptions.withCrossFade())
                            .diskCacheStrategy(DiskCacheStrategy.AUTOMATIC)
                            .into(item.iv_gift_pic)

                        rgGifts.addView(item)
                    }
                } else {
                    cl_gift.visibility = View.GONE
                }


            } else {
                this.finish()
            }
//            dialog.dismiss()
        }

        btn_vip_2.setOnClickListener {
//            val value = HashMap<String, Any>()
//            value["money"] = moneyList[vipIndex].currentPrice
//            value["moneyStr"] = "" + moneyList[vipIndex].currentPrice
//            value["fromWhere"] = strFromWhere
//            MobclickAgent.onEventObject(this, "clickPay", value)

            MyUtil.checkLoginAndDo(this) {
                choosePayType("", "", "")
            }
        }
    }

    override fun onDestroy() {

        timeTicker.cancel()
        super.onDestroy()

    }

    private fun choosePayType(name: String, phone: String, address: String) {
        SelectPayTypeDialog(this).apply {
            setOnPayTypeListener(object : SelectPayTypeDialog.OnPayTypeListener {
                override fun onChoose(type: Int) {
//                    val value = HashMap<String, Any>()

                    when (type) {
                        PAY_TYPE_ALIPAY -> {
//                            value["payType"] = "支付宝"
                            MMKVUtils.put(MyConstants.SP_KEY_PAY_TYPE, 0)
                            if (MyUtil.checkPackageInstalled(this@VIP2Activity, "com.eg.android.AlipayGphone", MyConstants.URL_ALIPAY)) {
                                if (vipIndex >= 0) {

                                    <EMAIL>(Dispatchers.IO) {
                                        try {
                                            val result = vipViewModel.vipRepository.makeAlipayOrderVIP(
                                                moneyList[vipIndex].currentPrice,
                                                moneyList[vipIndex].originalPrice, name, phone, address
                                            )
                                            withContext(Dispatchers.Main) {
                                                if (result.code == 200) {
                                                    val payRunnable = Runnable {
                                                        val alipay = PayTask(this@VIP2Activity)
                                                        val payResult = alipay.payV2(result.data!!.order, true) as Map<String, String>
                                                        val msg = Message()
                                                        msg.what = SDK_PAY_FLAG
                                                        msg.obj = payResult
                                                        mHandler.sendMessage(msg)
                                                    }
                                                    val payThread = Thread(payRunnable)
                                                    payThread.start()
                                                } else {
                                                    MyToastUtil.showError(result.msg)
                                                }
                                            }
                                        } catch (e: Exception) {
                                            MyToastUtil.showInfo(e.message)
                                        }
                                    }
                                } else {
                                    MyToastUtil.showError("正在拉取VIP价格，请稍候...")
                                }
                            }
                        }

                        PAY_TYPE_WXPAY -> {
//                            value["payType"] = "微信"
                            MMKVUtils.put(MyConstants.SP_KEY_PAY_TYPE, 0)
                            if (MyUtil.checkPackageInstalled(this@VIP2Activity, "com.tencent.mm", MyConstants.URL_WXPAY)) {
                                if (vipIndex >= 0) {

                                    <EMAIL>(Dispatchers.IO) {
                                        try {
                                            val result = vipViewModel.vipRepository.makeWXOrderVIP(
                                                moneyList[vipIndex].currentPrice,
                                                moneyList[vipIndex].originalPrice, name, phone, address
                                            )
                                            withContext(Dispatchers.Main) {
                                                if (result.code == 200) {
                                                    val request = com.tencent.mm.opensdk.modelpay.PayReq()
                                                    request.appId = result.data!!.appId
                                                    request.partnerId = result.data.partnerId
                                                    request.prepayId = result.data.prepayId
                                                    request.packageValue = result.data.packageValue
                                                    request.nonceStr = result.data.nonceStr
                                                    request.timeStamp = result.data.timeStamp
                                                    request.sign = result.data.sign
                                                    api.sendReq(request)
                                                } else {
                                                    MyToastUtil.showError(result.msg)
                                                }
                                            }
                                        } catch (e: Exception) {
                                            MyToastUtil.showInfo(e.message)
                                        }
                                    }
                                } else {
                                    MyToastUtil.showError("正在拉取VIP价格，请稍候...")
                                }
                            }
                        }
                    }

//                    value["money"] = moneyList[vipIndex].currentPrice
//                    value["moneyStr"] = "" + moneyList[vipIndex].currentPrice
//                    value["fromWhere"] = strFromWhere
//                    MobclickAgent.onEventObject(this@VIP2Activity, "selectPayType", value)

                }
            })
            show()
        }
    }

    private val mHandler = Handler {
        if (it.what == SDK_PAY_FLAG) {
            val payResult = PayResult(it.obj as Map<String, String>)
            when (payResult.resultStatus) {
                "6001" -> {
                    MyToastUtil.showInfo("支付失败")
                }

                else -> {
                    paySucceed(PAY_TYPE_ALIPAY)
                }
            }
        }
        true
    }

    override fun onResume() {
        super.onResume()
        refreshVipPage()
    }

    private fun refreshVipPage() {
        vipViewModel.refreshVIPMoney()
        when (MMKVUtils.getInt(MyConstants.SP_KEY_VIP_STATE, VIP_STATE_FREE)) {
            VIP_STATE_FREE, VIP_STATE_NORMAL -> {
                ll_vip_item_2.visibility = View.VISIBLE
                btn_vip_2.visibility = View.VISIBLE
            }

            VIP_STATE_FOREVER -> {
                ll_vip_item_2.visibility = View.GONE
                btn_vip_2.visibility = View.GONE
            }
        }
    }

    private fun chooseMoneyItem(vipIndex: Int) {
        var myVipIndex = 0
        val rgMoneys = ll_vip_item_2
        myVipIndex = if (vipIndex > rgMoneys.children.count()) {
            rgMoneys.children.count()
        } else {
            vipIndex
        }

        rgMoneys.children.forEachIndexed { index, view ->
            if (myVipIndex == index) {
                view.mcv_vip_item_2.strokeColor = resources.getColor(R.color.colorVipEndSmall)
            } else {
                view.mcv_vip_item_2.strokeColor = resources.getColor(R.color.colorTranslate)
            }
        }
    }

    private fun paySucceed(payType: Int) {

//        val value = HashMap<String, Any>()
//        value["fromWhere"] = strFromWhere
//        when (payType) {
//            PAY_TYPE_ALIPAY -> {
//                value["payType"] = "支付宝"
//            }
//            PAY_TYPE_WXPAY -> {
//                value["payType"] = "微信"
//            }
//        }
//
//        value["money"] = moneyList[vipIndex].currentPrice
//        value["moneyStr"] = "" + moneyList[vipIndex].currentPrice
//        MobclickAgent.onEventObject(this, "paySuccess", value)

        lifecycleScope.launch(Dispatchers.IO) {
            try {
                val result = vipViewModel.vipRepository.queryOrder()
                if (result.code == 200) {
                    withContext(Dispatchers.Main) {
                        MyToastUtil.showInfo("支付成功")
                    }


                    val result2 = loginViewModel.loginRepository.refreshState()
                    withContext(Dispatchers.Main) {
                        if (result2.code == 200) {
                            MMKVUtils.put(MyConstants.SP_KEY_USERNAME, result2.data!!.username)
                            MMKVUtils.put(MyConstants.SP_KEY_VIP_STATE, result2.data.vipState)
                            MMKVUtils.put(MyConstants.SP_KEY_VIP_END_TIME, result2.data.vipEndTime)
                            MMKVUtils.put(MyConstants.SP_KEY_AVATAR, result2.data.avatar)
                            MMKVUtils.put(MyConstants.SP_KEY_FORCE_QUITE_PWD, result2.data.unlockPwd)
                            MMKVUtils.put(MyConstants.SP_KEY_BIND_MOBILE, result2.data.bindMobile)

                            LiveEventBus.get(LiveBus.PAY_FOR_VIP_FINISH, String::class.java).post("")
                        } else {
                            MyToastUtil.showError(result.msg)
                        }
                        if (vipIndex == 0 || !notifyForever) {
                            if (notifyDeliveryInfo) {
                                showDeliveryInfo(false)
                            } else {
                                <EMAIL>()
                            }
                        } else {
                            NormalDialog(this@VIP2Activity).apply {
                                setTitle("温馨提示")
                                setContent("购买后，1小时之内可补差价升级到永久版，是否现在升级？")
                                isCancelable = false
                                setOnNormalOKClickListener("现在升级", object : OnOKClickListener {
                                    override fun onclick() {
                                        vipIndex = 0
                                        vipViewModel.refreshVIPMoney()
                                    }
                                })
                                setOnNormalCancelClickListener("我再想想", object : OnCancelClickListener {
                                    override fun onclick() {
//                                        <EMAIL>()
                                    }
                                })
                                showDialog()
                            }
                        }

                    }
                } else {
                    withContext(Dispatchers.Main) {
                        MyToastUtil.showError(result.msg)
                    }
                }
            } catch (e: Exception) {
                MyToastUtil.showInfo(e.message)
            }
        }
    }

    private fun showDeliveryInfo(cancelable: Boolean) {
        VIPApplyDeliveryInfoDialog(this@VIP2Activity).apply {
            isCancelable = cancelable
            setOnApplyDeliveryInfoListener(object : VIPApplyDeliveryInfoDialog.OnApplyDeliveryInfoListener {
                override fun onFinish(name: String, phone: String, address: String) {
                    <EMAIL>(Dispatchers.IO) {
                        try {
                            val result222 = MyRetrofitClient.service.applyDelivery(name, phone, address)
                            withContext(Dispatchers.Main) {
                                if (result222.code == 200) {
                                    MyToastUtil.showInfo("提交成功，请耐心等待开奖")

                                    refreshVipPage()
                                } else {
                                    MyToastUtil.showInfo(result222.msg)
                                }
                            }
                        } catch (e: Exception) {
                            withContext(Dispatchers.Main) {
                                MyToastUtil.showInfo(e.message)
                            }
                        }

                    }
                }
            })
            show()
        }
    }
}