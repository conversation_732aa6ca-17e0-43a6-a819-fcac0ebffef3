package com.lijianqiang12.silent.component.activity.lock.wallpaper

import android.app.Activity
import android.content.Intent
import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.Toast
import com.lijianqiang12.silent.utils.MMKVUtils
import com.bumptech.glide.Glide
import com.bumptech.glide.load.engine.DiskCacheStrategy
import com.bumptech.glide.load.resource.drawable.DrawableTransitionOptions
import com.lijianqiang12.silent.MyConstants
import com.lijianqiang12.silent.R
import com.lijianqiang12.silent.component.activity.REQUEST_CODE_SELECT_SYSTEM_WALLPAPER
import com.lijianqiang12.silent.component.activity.REQUEST_CODE_SET_WALLPAPER
import com.lijianqiang12.silent.component.activity.base.BaseFragment
import com.lijianqiang12.silent.utils.MyToastUtil
import com.lijianqiang12.silent.utils.getDefaultWallpaper
import com.lijianqiang12.silent.utils.setLiveWallpaper
import com.lijianqiang12.silent.utils.wallpaperIsUsed
import kotlinx.android.synthetic.main.fragment_current_wallpaper.view.*
import java.io.File

class CurrentWallpaperFragment : BaseFragment() {

    private lateinit var v: View

    override fun lazyInit() {


    }

    override fun onCreateView(inflater: LayoutInflater, container: ViewGroup?, savedInstanceState: Bundle?): View? {
        v = inflater.inflate(R.layout.fragment_current_wallpaper, container, false)
        return v
    }

    override fun onResume() {
        super.onResume()
        val wallpaperPath = MMKVUtils.getString(MyConstants.SP_KEY_SETTING_WALLPAPER_PATH, "")
        if (wallpaperPath.isNotEmpty() && File(wallpaperPath).exists()) {
            Glide.with(this).load(wallpaperPath)
                //.transition(DrawableTransitionOptions.withCrossFade())
                .diskCacheStrategy(DiskCacheStrategy.AUTOMATIC)
                .into(v.iv_current_wallpaper)
        } else {
            Glide.with(this).load(getDefaultWallpaper(requireContext().applicationContext))
                //.transition(DrawableTransitionOptions.withCrossFade())
                .diskCacheStrategy(DiskCacheStrategy.AUTOMATIC)
                .into(v.iv_current_wallpaper)
        }

        v.iv_current_wallpaper.setOnClickListener {
            if(wallpaperIsUsed(requireContext().applicationContext)){
                MyToastUtil.showInfo("设置成功")
            }else {
                setLiveWallpaper(requireContext().applicationContext, requireActivity(), REQUEST_CODE_SET_WALLPAPER)
            }
        }
    }

    companion object {
        @JvmStatic
        fun newInstance() = CurrentWallpaperFragment()
    }

    override fun onActivityResult(requestCode: Int, resultCode: Int, data: Intent?) {
        super.onActivityResult(requestCode, resultCode, data)
        if (requestCode == REQUEST_CODE_SET_WALLPAPER) {
            if (resultCode == Activity.RESULT_OK) {
                Toast.makeText(requireContext(), "设置动态壁纸成功", Toast.LENGTH_SHORT).show()
//                (parentFragment as WallpaperBottomSheetDialogFragment).dismiss()
            } else {
                Toast.makeText(requireContext(), "取消设置动态壁纸", Toast.LENGTH_SHORT).show()
            }
        } else if (requestCode == REQUEST_CODE_SELECT_SYSTEM_WALLPAPER) {
            if (resultCode == Activity.RESULT_OK) {
                Toast.makeText(requireContext(), "设置系统壁纸成功", Toast.LENGTH_SHORT).show()
            } else {
                Toast.makeText(requireContext(), "取消设置系统壁纸", Toast.LENGTH_SHORT).show()
            }
        }
    }
}