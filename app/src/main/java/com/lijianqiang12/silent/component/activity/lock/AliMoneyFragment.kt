import android.content.Intent
import android.graphics.Bitmap
import android.graphics.drawable.Drawable
import android.net.Uri
import android.os.Build
import android.os.Bundle
import android.provider.MediaStore
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.fragment.app.DialogFragment
import com.lijianqiang12.silent.utils.MMKVUtils
import com.bumptech.glide.Glide
import com.bumptech.glide.load.engine.DiskCacheStrategy
import com.bumptech.glide.load.resource.drawable.DrawableTransitionOptions
import com.bumptech.glide.request.target.SimpleTarget
import com.bumptech.glide.request.transition.Transition
import com.hjq.permissions.OnPermissionCallback
import com.hjq.permissions.XXPermissions
import com.lijianqiang12.silent.MyConstants
import com.lijianqiang12.silent.R
import com.lijianqiang12.silent.component.activity.custom.dialog.MyProgressDialog
import com.lijianqiang12.silent.utils.MyToastUtil
import kotlinx.android.synthetic.main.fragment_ali_money.view.*
import kotlinx.android.synthetic.main.layout_lock_window_land.view.iv_lock_view_bg
import moe.feng.alipay.zerosdk.AlipayZeroSdk
import java.io.FileNotFoundException
import java.util.*


class AliMoneyFragment : DialogFragment() {
    private lateinit var progressDialog: MyProgressDialog
    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
    }

    override fun onCreateView(inflater: LayoutInflater, container: ViewGroup?, savedInstanceState: Bundle?): View? {

        progressDialog = MyProgressDialog(this)
        val v = inflater.inflate(R.layout.fragment_ali_money, null)
        val c = Calendar.getInstance()
        val date = "${c.get(Calendar.YEAR)}-${c.get(Calendar.MONTH) + 1}-${c.get(Calendar.DAY_OF_MONTH)}"

        val alipayImgUrl = MMKVUtils.getString(MyConstants.SP_KEY_ALIPAY_URL, "")
        Glide.with(requireContext()).load(alipayImgUrl)
            .transition(DrawableTransitionOptions.withCrossFade())
            .diskCacheStrategy(DiskCacheStrategy.AUTOMATIC)
            .into(v.iv_alipay_img)

        v.tv_get_money.setOnClickListener {
            if (AlipayZeroSdk.hasInstalledAlipayClient(requireContext())) {
//                XXPermissions.with(this)
//                    .permission(com.hjq.permissions.Permission.MANAGE_EXTERNAL_STORAGE)
//                    .request(object : OnPermissionCallback {
//                        override fun onGranted(permissions: List<String>, all: Boolean) {
                Glide.with(this@AliMoneyFragment).asBitmap()
                    .diskCacheStrategy(DiskCacheStrategy.AUTOMATIC)
//                                .load(R.drawable.ic_alimoney)
                    .load(alipayImgUrl)
//                                .load("https://offphone-1252369707.file.myqcloud.com/ic_alimoney.jpg")
                    .into(object : SimpleTarget<Bitmap>() {
                        override fun onResourceReady(resource: Bitmap, transition: Transition<in Bitmap>?) {
//                                        LogUtils.d("aaaaaaaaaaaaaaaaaaaa a")
                            progressDialog.dismiss()
                            try {
                                val fileName = "支付宝红包二维码.png"
                                MediaStore.Images.Media.insertImage(
                                    <EMAIL>().contentResolver,
                                    resource,
                                    fileName,
                                    null
                                )

//                                            MyToastUtil.showSuccess("已将红包二维码保存至相册")
                                MyToastUtil.showSuccess("打开支付宝“扫一扫”，然后在相册中选择已下载的二维码")

                                val packageManager = requireActivity().applicationContext.packageManager
                                val intent = packageManager.getLaunchIntentForPackage("com.eg.android.AlipayGphone")
                                intent?.apply { startActivity(this) }


                                MMKVUtils.put(MyConstants.SP_KEY_ALI, date)
                                dismiss()
                            } catch (e: FileNotFoundException) {
                                e.printStackTrace()
                                progressDialog.dismiss()
                                MyToastUtil.showInfo("保存失败，请检查是否已经授予存储权限")
                            }

                        }

                        override fun onLoadStarted(placeholder: Drawable?) {
                            super.onLoadStarted(placeholder)
                            progressDialog.show()
                        }

                        override fun onLoadFailed(errorDrawable: Drawable?) {
                            super.onLoadFailed(errorDrawable)
                            progressDialog.dismiss()
                            MyToastUtil.showInfo("下载失败")
                        }
                    })
//                        }
//
//                        override fun onDenied(permissions: List<String>, never: Boolean) {
//                            MyToastUtil.showInfo("该功能需要读写存储权限")
//                        }
//                    })


            } else {
                MyToastUtil.showSuccess("尚未安装支付宝，请先下载安装再领取")
                val uri = Uri.parse("https://m.alipay.com/g7RQpqT")
                startActivity(Intent(Intent.ACTION_VIEW, uri))
            }


        }
        v.tv_break.setOnClickListener {
            MMKVUtils.put(MyConstants.SP_KEY_ALI, date)
            dismiss()
        }
        v.tv_do_not_show.setOnClickListener {
            MMKVUtils.put(MyConstants.SP_KEY_SHOW_ALI, false)
            dismiss()
        }
        return v
    }

    companion object {
        @JvmStatic
        fun newInstance() =
            AliMoneyFragment().apply {
            }
    }
}