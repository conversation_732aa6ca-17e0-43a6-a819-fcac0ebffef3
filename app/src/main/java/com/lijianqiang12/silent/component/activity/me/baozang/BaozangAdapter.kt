package com.lijianqiang12.silent.component.activity.me.baozang

import android.widget.TextView
import com.bumptech.glide.Glide
import com.bumptech.glide.load.engine.DiskCacheStrategy
import com.chad.library.adapter.base.BaseQuickAdapter
import com.chad.library.adapter.base.module.LoadMoreModule
import com.chad.library.adapter.base.viewholder.BaseViewHolder
import com.lijianqiang12.silent.R
import com.lijianqiang12.silent.data.model.net.pojos.BaoZangApp

class BaozangAdapter(layoutRes: Int, item: MutableList<BaoZangApp>)
    : BaseQuickAdapter<BaoZangApp, BaseViewHolder>(layoutRes, item) , LoadMoreModule {

    override fun convert(viewHolder: BaseViewHolder, item: BaoZangApp) {


        viewHolder.getView<TextView>(R.id.tv_hutui_title).text = item.title
        viewHolder.getView<TextView>(R.id.tv_hutui_content).text = item.content
        viewHolder.getView<TextView>(R.id.tv_right_text).text = item.rightText


        Glide.with(context).load(item.iconUrl)
            //.transition(DrawableTransitionOptions.withCrossFade())
            .diskCacheStrategy(DiskCacheStrategy.AUTOMATIC)
            .into(viewHolder.getView(R.id.iv_hutui_appicon))
    }
}