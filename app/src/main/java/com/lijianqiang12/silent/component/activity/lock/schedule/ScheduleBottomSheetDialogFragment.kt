package com.lijianqiang12.silent.component.activity.lock.schedule

import android.content.Context
import android.content.Intent
import android.os.Bundle
import android.view.Gravity
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.fragment.app.viewModels
import androidx.recyclerview.widget.ItemTouchHelper
import androidx.recyclerview.widget.LinearLayoutManager
import androidx.recyclerview.widget.RecyclerView
import com.afollestad.materialdialogs.MaterialDialog
import com.afollestad.materialdialogs.callbacks.onDismiss
import com.afollestad.materialdialogs.customview.customView
import com.blankj.utilcode.util.LogUtils
import com.google.android.material.bottomsheet.BottomSheetBehavior
import com.google.android.material.chip.Chip
import com.lijianqiang12.silent.R
import com.lijianqiang12.silent.component.activity.PermissionActivity
import com.lijianqiang12.silent.component.activity.base.BaseBottomSheetDialogFragment
import com.lijianqiang12.silent.component.activity.custom.dialog.NormalDialog
import com.lijianqiang12.silent.component.activity.custom.dialog.OnCancelClickListener
import com.lijianqiang12.silent.component.activity.custom.dialog.OnOKClickListener
import com.lijianqiang12.silent.component.activity.me.vip.FROM_WHERE
import com.lijianqiang12.silent.component.activity.me.vip.VIP2Activity
import com.lijianqiang12.silent.component.service.background_service.weekValid
import com.lijianqiang12.silent.data.model.db.ScheduleWithSub
import com.lijianqiang12.silent.data.model.db.TomatoWithSub
import com.lijianqiang12.silent.data.viewmodel.LockViewModel
import com.lijianqiang12.silent.utils.*
import dagger.hilt.android.AndroidEntryPoint
import kotlinx.android.synthetic.main.bottom_sheet_lock_schedule.view.*
import kotlinx.android.synthetic.main.dialog_lock_schedule.view.*
import kotlinx.android.synthetic.main.dialog_lock_schedule_drag.view.*
import java.util.*
import javax.inject.Inject

fun checkIfInRange(scheduleWithSub: ScheduleWithSub): Boolean {
    var nn = 0

    if (!scheduleWithSub.schedule.validate) {
        return false
    }

    if (!scheduleWithSub.schedule.isDenyChange) {
        return false
    }

    if (scheduleWithSub.schedule.jumpDate == MyUtil.getTodayCalendarString()) {
        return false
    }

    for (myIndex in 0..scheduleWithSub.schedule.denyChangeLength) {
//    for (myIndex in 0..60) {

        if (scheduleWithSub.schedule.validate) {
            var timeLen = 0
            if (scheduleWithSub.schedule.useTomato) {
                nn = if (scheduleWithSub.schedule.isRecycle) {
                    7
                } else {
                    1
                }
                if (scheduleWithSub.tomatoWithSub != null) {
                    for (i in 1..scheduleWithSub.tomatoWithSub!!.tomato.tomatoCount) {
                        timeLen += scheduleWithSub.tomatoWithSub!!.tomato.tomatoWorkLength
                        if (i < scheduleWithSub.tomatoWithSub!!.tomato.tomatoCount) {
                            timeLen += if (scheduleWithSub.tomatoWithSub!!.tomato.tomatoLongRestPerCount > 0
                                && (i % scheduleWithSub.tomatoWithSub!!.tomato.tomatoLongRestPerCount == 0)
                            ) {
                                scheduleWithSub.tomatoWithSub!!.tomato.tomatoLongRestLength
                            } else {
                                scheduleWithSub.tomatoWithSub!!.tomato.tomatoRestLength
                            }
                        }
                    }
                }
            } else {
                if (scheduleWithSub.schedule.startHour * 60 + scheduleWithSub.schedule.startMinute < scheduleWithSub.schedule.endHour * 60 + scheduleWithSub.schedule.endMinute) {
                    //起始时间小于结束时间
                    nn = 1
                    timeLen =
                        scheduleWithSub.schedule.endHour * 60 + scheduleWithSub.schedule.endMinute - scheduleWithSub.schedule.startHour * 60 - scheduleWithSub.schedule.startMinute
                } else {
                    nn = 2
                    timeLen =
                        scheduleWithSub.schedule.endHour * 60 + scheduleWithSub.schedule.endMinute + 24 * 60 - scheduleWithSub.schedule.startHour * 60 - scheduleWithSub.schedule.startMinute
                }

            }
            //秒为单位
            val startTime = (scheduleWithSub.schedule.startHour * 60 + scheduleWithSub.schedule.startMinute) * 60

            val c = Calendar.getInstance()
            c.add(Calendar.MINUTE, myIndex)
            val hour = c.get(Calendar.HOUR_OF_DAY)
            val minute = c.get(Calendar.MINUTE)
            val second = c.get(Calendar.SECOND)

            val current = hour * 60 * 60 + minute * 60 + second

            for (n in 0 until nn) {
                val date = Calendar.getInstance()
                date.set(Calendar.DATE, date.get(Calendar.DATE) - n)

                if ((weekValid(date.get(Calendar.DAY_OF_WEEK), scheduleWithSub.schedule)) || (!scheduleWithSub.schedule.isRecycle)) {
                    if ((current - startTime + 24 * 60 * 60 * n < timeLen * 60) && (current - startTime + 24 * 60 * 60 * n >= 0)) {
                        return true
                    }
                }
            }
        }
    }
    return false
}

@AndroidEntryPoint
class ScheduleBottomSheetDialogFragment : BaseBottomSheetDialogFragment() {

    private lateinit var mBehavior: BottomSheetBehavior<View>
    private lateinit var customView: View
    private var scheduleWithSubList: MutableList<ScheduleWithSub> = mutableListOf()

    private val viewModel: LockViewModel by viewModels()

    companion object {
        @JvmStatic
        fun newInstance(): ScheduleBottomSheetDialogFragment {
            return ScheduleBottomSheetDialogFragment()
        }
    }


    override fun onCreateView(inflater: LayoutInflater, container: ViewGroup?, savedInstanceState: Bundle?): View? {
        customView = View.inflate(requireContext(), R.layout.bottom_sheet_lock_schedule, null)
        return customView
    }


    override fun onStart() {
        super.onStart()
        val parentView = customView.parent as View
        parentView.setBackgroundColor(resources.getColor(R.color.colorTranslate))
        mBehavior = BottomSheetBehavior.from(parentView)
        mBehavior.state = BottomSheetBehavior.STATE_EXPANDED


    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        viewModel.schedulesLiveData.observe(viewLifecycleOwner) {

//            LogUtils.d("observe scheduleWithSub.whiteList.size = ${it[0].whiteList.size}")

            scheduleWithSubList = it
            customView.cg_lock_schedule.removeAllViews()
            it.forEachIndexed { index, scheduleWithSub ->
                val chip = Chip(requireContext())



                if (scheduleWithSub.schedule.validate) {
                    if (scheduleWithSub.schedule.jumpDate == MyUtil.getTodayCalendarString()) {
                        chip.setChipBackgroundColorResource(R.color.colorTextBackground)
                        chip.setTextColor(resources.getColor(R.color.colorLockScheduleChip))
                        chip.setChipIconResource(R.drawable.ic_lock_view_pause)
                        chip.setChipIconTintResource(R.color.colorLockScheduleChip)
                    } else {
                        chip.setChipBackgroundColorResource(R.color.colorLockScheduleChipGroup)
                        chip.setTextColor(resources.getColor(R.color.colorLockScheduleChip))
                        chip.setChipIconResource(R.drawable.ic_running)
                        chip.setChipIconTintResource(R.color.colorLockScheduleChip)
                    }
                } else {
                    chip.setChipBackgroundColorResource(R.color.colorTextBackground)
                    chip.setTextColor(resources.getColor(R.color.colorThirdText))
                    chip.setChipIconResource(R.drawable.ic_resting)
                    chip.setChipIconTintResource(R.color.colorThirdText)
                }

                var title = ""
                if (scheduleWithSub.schedule.title.isNotEmpty()) {
                    title += "${scheduleWithSub.schedule.title} · "
                }
                if (scheduleWithSub.schedule.useTomato) {
                    if (scheduleWithSub.tomatoWithSub == null) {
                        title += "${TimeUtil.formatHHMM(scheduleWithSub.schedule.startHour, scheduleWithSub.schedule.startMinute)} 执行『🍅已删除』"
                    } else {
                        title += "${
                            TimeUtil.formatHHMM(
                                scheduleWithSub.schedule.startHour,
                                scheduleWithSub.schedule.startMinute
                            )
                        } 执行『🍅${scheduleWithSub.tomatoWithSub!!.tomato!!.title}』"
                    }
                } else {
                    if (scheduleWithSub.schedule.startHour * 60 + scheduleWithSub.schedule.startMinute >= scheduleWithSub.schedule.endHour * 60 +
                        scheduleWithSub.schedule.endMinute
                    ) {
                        title += "${TimeUtil.formatHHMM(scheduleWithSub.schedule.startHour, scheduleWithSub.schedule.startMinute)} — 次日${
                            TimeUtil.formatHHMM(scheduleWithSub.schedule.endHour, scheduleWithSub.schedule.endMinute)
                        }"
                    } else {
                        title += "${TimeUtil.formatHHMM(scheduleWithSub.schedule.startHour, scheduleWithSub.schedule.startMinute)} — ${
//                        title += "${TimeUtil.formatHHMM(scheduleWithSub.schedule.startHour, scheduleWithSub.schedule.startMinute)} — 当日${
                            TimeUtil.formatHHMM(scheduleWithSub.schedule.endHour, scheduleWithSub.schedule.endMinute)
                        }"
                    }
                }

                chip.text = title

                if (index >= 3 && !MyUtil.isVIP()) {
                    chip.alpha = 0.3f
                    chip.text = chip.text.toString() + " (VIP可用)"
                    chip.setOnClickListener {
                        DialogUtil.showVIPDialog(
                            null, this, "VIP用户可使用3个以上定时锁机，开通后，享受无限锁机设置。", "3ScheduleLimit",
                            title = "VIP已过期",
                            positiveText = "续订VIP",
                            negativeText = "删除该项",
                            onNegativeListener = object : OnCancelClickListener {
                                override fun onclick() {
                                    viewModel.deleteScheduleWithSub(scheduleWithSub)
                                    MyToastUtil.showInfo("删除成功")
                                }
                            },
                        )
                    }
                } else {
                    chip.setOnClickListener {
                        showScheduleDialog(scheduleWithSub)
                    }
                }
//                chip.setOnClickListener {
//                    showScheduleDialog(scheduleWithSub)
//                }

                customView.cg_lock_schedule.addView(chip)
            }

        }



        customView.btn_lock_schedule_new.setOnClickListener {
            if ((scheduleWithSubList.size >= 3) && (!MyUtil.isVIP())) {
//                val intent = Intent(requireContext(), VIP2Activity::class.java)
//                intent.putExtra(FROM_WHERE,"3ScheduleLimit")
//                startActivity(intent)
//                MyToastUtil.showInfo(requireContext().applicationContext, "VIP用户可创建3个以上定时锁机")
                DialogUtil.showVIPDialog(null, this, "VIP用户可创建3个以上定时锁机，开通后，享受无限锁机设置。", "3ScheduleLimit")
            } else {
                val intent = Intent(requireContext().applicationContext, EditScheduleActivity::class.java)
                startActivity(intent)
            }
        }


        customView.iv_lock_schedule_sort.setOnClickListener {
            val customView = LayoutInflater.from(requireContext()).inflate(R.layout.dialog_lock_schedule_drag, null)
            val dialog = MaterialDialog(requireContext()).customView(R.layout.dialog_lock_schedule_drag, customView, false).cornerRadius(8.0f)

            val mLayoutManager = LinearLayoutManager(requireContext())
            customView.rv_lock_schedule_drag.layoutManager = mLayoutManager
            val mAdapter = ScheduleAdapter(R.layout.item_lock_schedule_drag, mutableListOf())
            mAdapter.animationEnable = true
            customView.rv_lock_schedule_drag.adapter = mAdapter
            val itemTouchHelper = ItemTouchHelper(ScheduleItemCallback(requireContext(), viewModel))
            itemTouchHelper.attachToRecyclerView(customView.rv_lock_schedule_drag)

            mAdapter.addData(scheduleWithSubList)
            mAdapter.notifyDataSetChanged()
            dialog.onDismiss {
            }
            dialog.show()
        }

        if (MyUtil.isVIP()) {
            customView.tv_vip_flag_schedule.visibility = View.GONE
        } else {
            customView.tv_vip_flag_schedule.visibility = View.VISIBLE
            customView.tv_vip_flag_schedule.setOnClickListener {
//                ActivityUtils.startActivity(VIP2Activity::class.java)

                val intent = Intent(requireActivity(), VIP2Activity::class.java)
                intent.putExtra(FROM_WHERE, "3ScheduleCard")
                startActivity(intent)

            }

        }
    }


    /**
     * type 0:新增 1:修改
     */
    fun showScheduleDialog(scheduleWithSub: ScheduleWithSub) {

        val customView = LayoutInflater.from(activity).inflate(R.layout.dialog_lock_schedule, null)
        val dialog = MaterialDialog(requireContext()).customView(R.layout.dialog_lock_schedule, customView, false).cornerRadius(8.0f)

        if (scheduleWithSub.schedule.useTomato) {
            if (scheduleWithSub.tomatoWithSub == null) {
                customView!!.et_dialog_lock_schedule_title.text =
                    "${TimeUtil.formatHHMM(scheduleWithSub.schedule.startHour, scheduleWithSub.schedule.startMinute)} 执行『🍅已删除』"
            } else {
                customView!!.et_dialog_lock_schedule_title.text = "${
                    TimeUtil.formatHHMM(
                        scheduleWithSub.schedule.startHour,
                        scheduleWithSub.schedule.startMinute
                    )
                } 执行『🍅 " + "${scheduleWithSub.tomatoWithSub!!.tomato!!.title}』"
            }
        } else {
            if (scheduleWithSub.schedule.startHour * 60 + scheduleWithSub.schedule.startMinute >= scheduleWithSub.schedule.endHour * 60 + scheduleWithSub.schedule.endMinute) {
                customView!!.et_dialog_lock_schedule_title.text = "${
                    TimeUtil.formatHHMM(
                        scheduleWithSub.schedule.startHour,
                        scheduleWithSub.schedule.startMinute
                    )
                } — 次日${TimeUtil.formatHHMM(scheduleWithSub.schedule.endHour, scheduleWithSub.schedule.endMinute)}"
            } else {
                customView!!.et_dialog_lock_schedule_title.text = "${
                    TimeUtil.formatHHMM(
                        scheduleWithSub.schedule.startHour,
                        scheduleWithSub.schedule.startMinute
                    )
                } — ${TimeUtil.formatHHMM(scheduleWithSub.schedule.endHour, scheduleWithSub.schedule.endMinute)}"
//                } — 当日${TimeUtil.formatHHMM(scheduleWithSub.schedule.endHour, scheduleWithSub.schedule.endMinute)}"
            }
        }

        if (scheduleWithSub.schedule.isRecycle) {

            if (scheduleWithSub.schedule.monday) {
                customView!!.tv_week_select_1.setBackgroundResource(R.drawable.bg_week_select)
            } else {
                customView!!.tv_week_select_1.setBackgroundResource(R.drawable.bg_week_not_select)
            }

            if (scheduleWithSub.schedule.tuesday) {
                customView!!.tv_week_select_2.setBackgroundResource(R.drawable.bg_week_select)
            } else {
                customView!!.tv_week_select_2.setBackgroundResource(R.drawable.bg_week_not_select)
            }

            if (scheduleWithSub.schedule.wednesday) {
                customView!!.tv_week_select_3.setBackgroundResource(R.drawable.bg_week_select)
            } else {
                customView!!.tv_week_select_3.setBackgroundResource(R.drawable.bg_week_not_select)
            }

            if (scheduleWithSub.schedule.thursday) {
                customView!!.tv_week_select_4.setBackgroundResource(R.drawable.bg_week_select)
            } else {
                customView!!.tv_week_select_4.setBackgroundResource(R.drawable.bg_week_not_select)
            }

            if (scheduleWithSub.schedule.friday) {
                customView!!.tv_week_select_5.setBackgroundResource(R.drawable.bg_week_select)
            } else {
                customView!!.tv_week_select_5.setBackgroundResource(R.drawable.bg_week_not_select)
            }

            if (scheduleWithSub.schedule.saturday) {
                customView!!.tv_week_select_6.setBackgroundResource(R.drawable.bg_week_select)
            } else {
                customView!!.tv_week_select_6.setBackgroundResource(R.drawable.bg_week_not_select)
            }

            if (scheduleWithSub.schedule.sunday) {
                customView!!.tv_week_select_7.setBackgroundResource(R.drawable.bg_week_select)
            } else {
                customView!!.tv_week_select_7.setBackgroundResource(R.drawable.bg_week_not_select)
            }
            customView.tv_schedule_not_recycle.visibility = View.GONE
        } else {
            customView.tv_schedule_not_recycle.visibility = View.VISIBLE
        }

        if (scheduleWithSub.schedule.validate) {
            customView.btn_dialog_lock_schedule_do.text = "取消激活"
        } else {
            customView!!.btn_dialog_lock_schedule_do.text = "激活"
        }


        customView!!.btn_dialog_lock_schedule_edit.setOnClickListener {
            if (checkIfInRange(scheduleWithSub)) {
                MyToastUtil.showError("该定时任务设置了在开始前${scheduleWithSub.schedule.denyChangeLength}分钟内禁止修改")
            } else {
                val intent = Intent(requireContext(), EditScheduleActivity::class.java)

                LogUtils.d("before begin scheduleWithSub.whiteList.size = ${scheduleWithSub.whiteList.size}")
                intent.putExtra("scheduleWithSub", scheduleWithSub)
                startActivity(intent)
                dialog.dismiss()
            }
        }

        customView!!.btn_dialog_lock_schedule_delete.setOnClickListener {
            if (checkIfInRange(scheduleWithSub)) {
                MyToastUtil.showError("该定时任务设置了在开始前${scheduleWithSub.schedule.denyChangeLength}分钟内禁止修改")
            } else {
                NormalDialog(this).apply {
                    setTitle("警告")
                    setContent("确定删除该定时任务吗？")
                    setGravity(Gravity.CENTER)
                    setOnNormalOKClickListener("删除", object : OnOKClickListener {
                        override fun onclick() {
                            viewModel.deleteScheduleWithSub(scheduleWithSub)
                            MyToastUtil.showInfo("删除成功")
                            dialog.dismiss()
                        }
                    })

                    setOnNormalCancelClickListener("取消", object : OnCancelClickListener {
                        override fun onclick() {
                            MyToastUtil.showInfo("取消删除")
                        }
                    })

                    showDialog()
                }

            }
        }

        customView!!.btn_dialog_lock_schedule_do.setOnClickListener {
            if (checkIfInRange(scheduleWithSub)) {
                MyToastUtil.showError("该定时任务设置了在开始前${scheduleWithSub.schedule.denyChangeLength}分钟内禁止修改")
            } else {
                if (scheduleWithSub.schedule.validate || PermissionUtil.hasAllPermission(requireActivity())) {

                    if (scheduleWithSub.schedule.validate) {//取消激活
                        NormalDialog(this).apply {
                            setTitle("取消激活")
                            setContent("请选择取消激活的类型")
//                            setGravity(Gravity.START)
//                            isCancelable = false
                            setOnNormalOKClickListener("仅今天取消", object : OnOKClickListener {
                                override fun onclick() {
                                    scheduleWithSub.schedule.jumpDate = MyUtil.getTodayCalendarString()
                                    viewModel.updateSchedule(scheduleWithSub.schedule)
                                    MyToastUtil.showInfo("今日已取消激活")
                                }
                            })
                            setOnNormalCancelClickListener("全部取消", object : OnCancelClickListener {
                                override fun onclick() {
                                    scheduleWithSub.schedule.validate = false
                                    scheduleWithSub.schedule.jumpDate = ""
                                    viewModel.updateSchedule(scheduleWithSub.schedule)
                                    MyToastUtil.showInfo("已取消激活")
                                }
                            })
                            showDialog()
                        }

                    } else {//激活
                        scheduleWithSub.schedule.validate = true
                        scheduleWithSub.schedule.jumpDate = ""
                        viewModel.updateSchedule(scheduleWithSub.schedule)
                        MyToastUtil.showInfo("已激活")
                    }
//                    scheduleWithSub.schedule.validate = !scheduleWithSub.schedule.validate
//                    scheduleWithSub.schedule.jumpDate = ""
//                    viewModel.updateSchedule(scheduleWithSub.schedule)
//                    if (scheduleWithSub.schedule.validate) {
//                        MyToastUtil.showInfo("已激活")
//                    } else {
//                        MyToastUtil.showInfo("已取消激活")
//                    }
                    dialog.dismiss()
                } else {
                    MyToastUtil.showError("有未授予的权限")
                    startActivity(Intent(requireContext(), PermissionActivity::class.java))
                }
            }
        }

        dialog.show()
    }

    private class ScheduleItemCallback(val context: Context, val viewModel: LockViewModel) : ItemTouchHelper.Callback() {
        override fun getMovementFlags(p0: androidx.recyclerview.widget.RecyclerView, p1: androidx.recyclerview.widget.RecyclerView.ViewHolder): Int {
            return makeMovementFlags(ItemTouchHelper.UP or ItemTouchHelper.DOWN, 0)
        }

        override fun onMove(recycler: RecyclerView, holder1: RecyclerView.ViewHolder, holder2: RecyclerView.ViewHolder): Boolean {

            val fromPosition: Int = holder1.getBindingAdapterPosition()
            val toPosition: Int = holder2.getBindingAdapterPosition()

            if (fromPosition < (recycler.adapter as ScheduleAdapter).data.size &&
                toPosition < (recycler.adapter as ScheduleAdapter).data.size
            ) {

                if (fromPosition < toPosition) {
                    for (i in fromPosition until toPosition) {
                        swapData(recycler, i, i + 1)
                    }
                } else {
                    for (i in fromPosition downTo toPosition + 1) {
                        swapData(recycler, i, i - 1)
                    }
                }

            }

            return true
        }


        override fun onSwiped(recycler: RecyclerView.ViewHolder, p1: Int) {

        }

        override fun isLongPressDragEnabled(): Boolean {
            return true
        }

        fun swapData(recycler: RecyclerView, position1: Int, position2: Int) {
            Collections.swap((recycler.adapter as ScheduleAdapter).data, position1, position2)
            (recycler.adapter as ScheduleAdapter).notifyItemMoved(position1, position2)

            swapDataTrendWithMinimalImpact((recycler.adapter as ScheduleAdapter).data, position1, position2)
        }

        fun swapDataTrendWithMinimalImpact(dataList: MutableList<ScheduleWithSub>, pos1: Int, pos2: Int) {
            if (pos1 !in dataList.indices || pos2 !in dataList.indices) return

            val item1 = dataList[pos1].schedule
            val item2 = dataList[pos2].schedule

            //这里修改pos1的trend，因为拖动的是pos2，这样如果trend都是0，拖一圈就能全改了。
            if (item1.trend == item2.trend) {
                val newTrendForItem1 = generateNearestUniqueTrend(dataList, item1.trend)
                item1.trend = newTrendForItem1
            } else {
                // 如果trend不相等，交换trend值
                item1.trend = item2.trend.also { item2.trend = item1.trend }
            }
            viewModel.updateSchedule(item1, sync = false)
            viewModel.updateSchedule(item2, sync = false)

        }

        private fun generateNearestUniqueTrend(dataList: List<ScheduleWithSub>, baseTrend: Int): Int {
            var newTrend = baseTrend + 1
            while (dataList.any { it.schedule.trend == newTrend }) {
                newTrend += 1
            }
            return newTrend
        }
    }
}