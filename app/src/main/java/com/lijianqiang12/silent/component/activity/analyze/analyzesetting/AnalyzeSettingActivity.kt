package com.lijianqiang12.silent.component.activity.analyze.analyzesetting

import android.os.Bundle
import androidx.appcompat.app.AppCompatActivity
import com.lijianqiang12.silent.MyConstants
import com.lijianqiang12.silent.R
import com.lijianqiang12.silent.component.activity.base.BaseActivity
import com.lijianqiang12.silent.utils.MMKVUtils
import kotlinx.android.synthetic.main.activity_analyze_setting.*
import kotlinx.android.synthetic.main.activity_app_limit_setting.*

class AnalyzeSettingActivity : BaseActivity() {
    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        setContentView(R.layout.activity_analyze_setting)

        iv_setting_return_analyze.setOnClickListener {
            finish()
        }
        switch_show_network_sort.isChecked = MMKVUtils.getBoolean(MyConstants.SP_KEY_ANALYZE_SETTING_SHOW_NETWORK_SORT, true)
        switch_show_network_sort.setOnCheckedChangeListener { buttonView, isChecked ->
            MMKVUtils.put(MyConstants.SP_KEY_ANALYZE_SETTING_SHOW_NETWORK_SORT, isChecked)
        }
    }
}