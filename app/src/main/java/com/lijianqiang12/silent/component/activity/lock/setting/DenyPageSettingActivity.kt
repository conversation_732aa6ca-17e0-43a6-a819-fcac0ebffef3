package com.lijianqiang12.silent.component.activity.lock.setting

import android.content.Intent
import android.os.Bundle
import android.util.Log
import android.view.Gravity
import androidx.recyclerview.widget.LinearLayoutManager
import androidx.recyclerview.widget.RecyclerView
import com.blankj.utilcode.util.GsonUtils
import com.blankj.utilcode.util.ToastUtils
import com.jeremyliao.liveeventbus.LiveEventBus
import com.lijianqiang12.silent.LiveBus
import com.lijianqiang12.silent.MyConstants
import com.lijianqiang12.silent.R
import com.lijianqiang12.silent.component.activity.base.BaseActivity
import com.lijianqiang12.silent.component.activity.custom.dialog.NormalDialog
import com.lijianqiang12.silent.component.activity.custom.dialog.OnCancelClickListener
import com.lijianqiang12.silent.component.activity.custom.dialog.OnOKClickListener
import com.lijianqiang12.silent.component.activity.custom.dialog.OnWarningClickListener
import com.lijianqiang12.silent.utils.*
import kotlinx.android.synthetic.main.activity_deny_page_setting.*


class DenyPageSettingActivity : BaseActivity() {

    private lateinit var recyclerview: RecyclerView
    private lateinit var mAdapter: DenyPageAdapter

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        setContentView(R.layout.activity_deny_page_setting)

        iv_deny_page_return.setOnClickListener { finish() }

        iv_deny_page_add.setOnClickListener {
//            val denyPageString = MMKVUtils.getString(MyConstants.SP_KEY_DENY_PAGE_V2)
//            val denyPageList = MyGsonUtil.jsonToTheDenyPageList(denyPageString)
            if (MyUtil.isVIP() || getDenyPageValidCount() < 3) {
                NormalDialog(this).run {
                    setTitle("添加屏蔽页面")
                    setGravity(Gravity.START)
                    setContent("1.『快速添加』可直接选择“朋友圈”、“公众号”等页面。\n2.『自定义添加』可添加任意页面。\n3. 系统自带分身应用无法屏蔽。")
                    setOnNormalOKClickListener("快速添加", object : OnOKClickListener {
                        override fun onclick() {
                            <EMAIL>(Intent(this@DenyPageSettingActivity, FastDenyPageSettingActivity::class.java))
                        }
                    })
                    setOnNormalCancelClickListener("自定义添加", object : OnCancelClickListener {
                        override fun onclick() {
                            NormalDialog(this@DenyPageSettingActivity).run {
                                setTitle("温馨提示")
                                setGravity(Gravity.START)
                                setContent("1. 点击确定后，手机左上角会显示悬浮窗。\n2. 进入你想屏蔽的页面\n3. 点击悬浮窗“添加”，即可在锁机时屏蔽该页面。")
                                if (!PermissionUtil.isAccessibilitySettingsOn(MyConstants.ACCESS_ABILITY_NAME, <EMAIL>)) {
                                    setWarning("⚠️ 若屏蔽系统页面，请先授予无障碍权限（点我设置）")
                                }
                                setOnWarningClickListener(object : OnWarningClickListener {
                                    override fun onclick() {
                                        if (PermissionUtil.isAccessibilitySettingsOn(MyConstants.ACCESS_ABILITY_NAME, <EMAIL>)) {
                                            ToastUtils.showShort("无障碍权限已开启")
                                        } else {
                                            PermissionUtil.openAccessibility(this@DenyPageSettingActivity)
                                        }
                                    }
                                })
                                setOnNormalOKClickListener("确定", object : OnOKClickListener {
                                    override fun onclick() {
                                        LiveEventBus.get(LiveBus.SHOW_DENY_PAGE_PICK, Boolean::class.java).post(true)
                                    }
                                })
                                setOnNormalCancelClickListener("取消", object : OnCancelClickListener {
                                    override fun onclick() {

                                    }
                                })
                                showDialog()
                            }
                        }
                    })
                    showDialog()
                }
            } else {
                DialogUtil.showVIPDialog(this, null, content = "VIP用户可添加3个以上屏蔽页面。", "DenyPageSettingActivity add")
            }


        }


        recyclerview = rv_deny_page
        recyclerview.layoutManager = LinearLayoutManager(this)
        mAdapter = DenyPageAdapter(R.layout.item_deny_page, mutableListOf())
//        mAdapter.animationEnable = true
        recyclerview.adapter = mAdapter

        srl_deny_page.setOnRefreshListener {
            refreshPage()
        }

        mAdapter.setOnItemClickListener { adapter, view, position ->
            val theDenyPage = adapter.data[position] as TheDenyPage2
            if (MyUtil.isVIP() || getDenyPageValidCount() < 3 || theDenyPage.valid) {
                reverseValidTheDenyPage(theDenyPage)
            } else {
                DialogUtil.showVIPDialog(this, null, content = "VIP用户可添加3个以上屏蔽页面。", "DenyPageSettingActivity reverse")
            }
        }

        mAdapter.setOnItemLongClickListener { adapter, view, position ->
            NormalDialog(this).apply {
                setTitle("删除")
                setContent("是否删除该记录")
                setOnNormalOKClickListener("删除", object : OnOKClickListener {
                    override fun onclick() {
                        removeTheDenyPage(adapter.data[position] as TheDenyPage2)
                    }
                })
                setOnNormalCancelClickListener("取消", object : OnCancelClickListener {
                    override fun onclick() {
                    }
                })
                showDialog()
            }
            true
        }

    }

    private fun reverseValidTheDenyPage(denyPage: TheDenyPage2) {

        val denyPageList = MyGsonUtil.getDenyPageList()

        var changeObj: TheDenyPage2? = null
        denyPageList.forEach {
            if (it == denyPage) {
                changeObj = it
            }
        }

        changeObj?.let {
            it.valid = !it.valid
        }

        MMKVUtils.put(MyConstants.SP_KEY_DENY_PAGE_V2, GsonUtils.toJson(denyPageList))
//        MMKVUtils.put(MyConstants.SP_KEY_DENY_PAGE_V2, GsonUtils.toJson(denyPageList))

//        mAdapter.notifyDataSetChanged()
        mAdapter.setNewInstance(denyPageList)
    }

    private fun removeTheDenyPage(denyPage: TheDenyPage2) {

        val denyPageList = MyGsonUtil.getDenyPageList()

        var deleteObj: TheDenyPage2? = null
        denyPageList.forEach {
            if (it == denyPage) {
                deleteObj = it
            }
        }

        deleteObj?.let {
            denyPageList.remove(it)
            MyToastUtil.showInfo("删除成功")
        }

        MMKVUtils.put(MyConstants.SP_KEY_DENY_PAGE_V2, GsonUtils.toJson(denyPageList))
//        MMKVUtils.put(MyConstants.SP_KEY_DENY_PAGE_V2, GsonUtils.toJson(denyPageList))

//        mAdapter.notifyDataSetChanged()
        mAdapter.setNewInstance(denyPageList)
    }


    override fun onResume() {
        super.onResume()
        refreshPage()

    }

    private fun refreshPage() {
        val denyPageList = MyGsonUtil.getDenyPageList()

        denyPageList.forEach {
            Log.d("DenyPageSettingActivity", "refreshPage: ${it}")
        }
        mAdapter.setNewInstance(denyPageList)


        srl_deny_page.isRefreshing = false
    }
}