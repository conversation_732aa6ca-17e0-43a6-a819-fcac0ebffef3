package com.lijianqiang12.silent.component.activity.custom.dialog

import android.graphics.Color
import android.graphics.drawable.ColorDrawable
import android.os.Bundle
import android.util.DisplayMetrics
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.view.WindowManager
import androidx.appcompat.app.AppCompatActivity
import androidx.fragment.app.Fragment
import com.lijianqiang12.silent.DIALOG_WIDTH_PERCENT
import com.lijianqiang12.silent.R
import com.lijianqiang12.silent.component.activity.base.BaseDialogFragment
import com.lijianqiang12.silent.utils.MyToastUtil
import com.lijianqiang12.silent.utils.secondToSimpleHm
import kotlinx.android.synthetic.main.dialog_limit_time_edit.view.*

class LimitTimeEditDialog() : BaseDialogFragment() {

    constructor(fragment: Fragment) : this() {
        this.fragment = fragment
    }

    constructor(activity: AppCompatActivity) : this() {
        this.activity = activity
    }

    private var okListener: OnOKLimitTimeEditListener? = null
    private var cancelListener: OnCancelLimitTimeEditListener? = null
    private lateinit var v: View
    private var fragment: Fragment? = null
    private var activity: AppCompatActivity? = null
    private var editStartTime: Int = 28800
    private var editEndTime: Int = 32400

    private var title: String = ""


    override fun onCreateView(inflater: LayoutInflater, container: ViewGroup?, savedInstanceState: Bundle?): View {
        dialog!!.window!!.setBackgroundDrawable(ColorDrawable(Color.TRANSPARENT))


        v = inflater.inflate(R.layout.dialog_limit_time_edit, container, false)

        v.tv_dialog_home_app_title.text = title

        v.tv_dialog_app_limit_start_time.text = "${secondToSimpleHm(editStartTime)}"
        v.tv_dialog_app_limit_end_time.text = "${secondToSimpleHm(editEndTime)}"

        v.tv_dialog_app_limit_start_time.setOnClickListener {
            val dialog = WhiteAppMonitorSelectTimeDialog(this)
            dialog.setOnOKClickListener(object : WhiteAppMonitorSelectTimeDialog.OnTimeLimitListener {
                override fun onclick(minute: Int) {

                    editStartTime = (minute * 60)
                    v.tv_dialog_app_limit_start_time.text = "${secondToSimpleHm(minute * 60)}"
                }
            })
            dialog.setTitle("时间设置")
            dialog.setContent("请选择开始时间。")
            dialog.setTime((editStartTime / 60))
            dialog.setCancelText("取消")
            dialog.show()
        }

        v.tv_dialog_app_limit_end_time.setOnClickListener {
            val dialog = WhiteAppMonitorSelectTimeDialog(this)
            dialog.setOnOKClickListener(object : WhiteAppMonitorSelectTimeDialog.OnTimeLimitListener {
                override fun onclick(minute: Int) {

                    editEndTime = (minute * 60)
                    v.tv_dialog_app_limit_end_time.text = "${secondToSimpleHm(minute * 60)}"
                }
            })
            dialog.setTitle("时间设置")
            dialog.setContent("请选择结束时间。")
            dialog.setTime((editEndTime / 60))
            dialog.setCancelText("取消")
            dialog.show()
        }


        v.tv_dialog_home_app_time_ok.setOnClickListener {
            okListener?.apply {
                if (editStartTime >= editEndTime) {
                    MyToastUtil.showError("开始时间必须小于结束时间")
                    return@setOnClickListener
                }
                onclick(editStartTime, editEndTime)
            }
            <EMAIL>()
        }

        v.tv_dialog_home_app_time_cancel.setOnClickListener {
            cancelListener?.apply {
                onclick()
            }
            <EMAIL>()
        }
        return v
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)

    }

    fun setTitle(title: String) {
        this.title = title
    }

    fun setLimitTime(start: Int, end: Int) {
        if (start != -1) {
            this.editStartTime = start
        }
        if (end != -1) {
            this.editEndTime = end
        }
    }

    fun show() {
        fragment?.apply {
            super.show(this.requireFragmentManager(), "LimitTimeEditDialog")
        }
        activity?.apply {
            super.show(this.supportFragmentManager, "LimitTimeEditDialog")
        }
    }

    override fun onStart() {
        val params = dialog!!.window!!.attributes
        val dm: DisplayMetrics = resources.displayMetrics
//        val density = dm.density
        val width = dm.widthPixels
//        val height = dm.heightPixels
        params.width = (width * DIALOG_WIDTH_PERCENT).toInt()//ViewGroup.LayoutParams.MATCH_PARENT
//        params.height = (height * DIALOG_WIDTH_PERCENT).toInt()//ViewGroup.LayoutParams.MATCH_PARENT
//        params.width = width.coerceAtMost(height) * 3 / 4//ViewGroup.LayoutParams.MATCH_PARENT
        dialog!!.window!!.attributes = params as WindowManager.LayoutParams
        super.onStart()
    }

    fun setOnOKClickListener(okListener: OnOKLimitTimeEditListener) {
        this.okListener = okListener
    }

    fun setOnCancelClickListener(cancelListener: OnCancelLimitTimeEditListener) {
        this.cancelListener = cancelListener
    }

    interface OnOKLimitTimeEditListener {
        fun onclick(start: Int, end: Int)
    }

    interface OnCancelLimitTimeEditListener {
        fun onclick()
    }

    override fun onSaveInstanceState(outState: Bundle) {
        super.onSaveInstanceState(outState)
    }

}