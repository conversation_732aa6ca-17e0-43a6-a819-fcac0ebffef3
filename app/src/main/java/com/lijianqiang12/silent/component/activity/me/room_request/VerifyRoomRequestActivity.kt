package com.lijianqiang12.silent.component.activity.me.room_request

import android.os.Bundle
import android.view.View
import androidx.lifecycle.lifecycleScope
import androidx.recyclerview.widget.LinearLayoutManager
import androidx.recyclerview.widget.RecyclerView
import com.lijianqiang12.silent.R
import com.lijianqiang12.silent.data.model.net.MyRetrofitClient
import com.lijianqiang12.silent.data.model.net.pojos.RoomRequestBean
import com.lijianqiang12.silent.component.activity.base.BaseActivity
import com.lijianqiang12.silent.component.activity.custom.dialog.EditTextDialog
import com.lijianqiang12.silent.component.activity.custom.dialog.NormalDialog
import com.lijianqiang12.silent.component.activity.custom.dialog.OnCancelClickListener
import com.lijianqiang12.silent.component.activity.custom.dialog.OnOKClickListener
import com.lijianqiang12.silent.utils.MyToastUtil
import kotlinx.android.synthetic.main.activity_verify_room_request.*
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.launch
import kotlinx.coroutines.withContext

class VerifyRoomRequestActivity : BaseActivity() {

    private lateinit var recyclerview: RecyclerView
    private lateinit var adapter: RoomRequestAdapter

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        setContentView(R.layout.activity_verify_room_request)

        recyclerview = rv_verify_room_request
        recyclerview.layoutManager = LinearLayoutManager(this)
        adapter = RoomRequestAdapter(this, R.layout.item_room_request, mutableListOf())
        adapter.animationEnable = true
//        adapter.loadMoreModule.setOnLoadMoreListener {
//            if (adapter.data.size == 0) {
//                getNewData(MAX_ID)
//            } else {
//                getNewData(adapter.data[adapter.data.size - 1].roomRequestId.toLong())
//            }
//        }

        recyclerview.adapter = adapter

        adapter.setOnItemClickListener { adapter, view, position ->
            NormalDialog(this).apply {
                setTitle("是否通过")
                setContent("是否通过该申请？")
                setOnNormalOKClickListener("通过", object : OnOKClickListener {
                    override fun onclick() {
                        upResult((adapter.data[position] as RoomRequestBean).roomRequestId, 1, "")
                    }
                })
                setOnNormalCancelClickListener("拒绝", object : OnCancelClickListener {
                    override fun onclick() {

                        EditTextDialog(this@VerifyRoomRequestActivity).apply {

                            isCancelable = false
                            setTitle("填写拒绝原因")
                            setOnOKListener(object : EditTextDialog.OnOKListener {
                                override fun onclick(text: String) {
                                    upResult((adapter.data[position] as RoomRequestBean).roomRequestId, 2, text)
                                }
                            })
                            show()
                        }

                    }
                })
                showDialog()
            }
        }

        srl_room_request.setOnRefreshListener {
            getNewData()
        }

        srl_room_request.isRefreshing = true
        getNewData()

    }

    private fun getNewData() {
        this.lifecycleScope.launch(Dispatchers.IO) {
            try {
                val result = MyRetrofitClient.service.getRoomRequestList()
                withContext(Dispatchers.Main) {
                    if (result.code == 200) {
                        result.data?.let {
                            adapter.setNewInstance(it)
                            adapter.notifyDataSetChanged()

                            if (it.size > 0) {

                                this@VerifyRoomRequestActivity.iv_empty.visibility = View.GONE
                            } else {

                                this@VerifyRoomRequestActivity.iv_empty.visibility = View.VISIBLE
                                MyToastUtil.showInfo(result.msg)
                            }
                        }
                        srl_room_request.isRefreshing = false
                    } else {
                        MyToastUtil.showInfo(result.msg)
                        srl_room_request.isRefreshing = false
                    }
                }
            } catch (e: Exception) {
                withContext(Dispatchers.Main) {
                    MyToastUtil.showInfo("异常，原因：" + e.message)
                    srl_room_request.isRefreshing = false
                }
            }
        }
    }

    private fun upResult(roomRequestId: Long, upResult: Int, reason: String) {
        lifecycleScope.launch(Dispatchers.IO) {
            try {
                val result = MyRetrofitClient.service.requestRoomOK(roomRequestId, upResult, reason)
                withContext(Dispatchers.Main) {
                    if (result.code == 200) {
                        MyToastUtil.showInfo(result.msg)
                        getNewData()
                    } else {
                        MyToastUtil.showInfo(result.msg)
                    }
                }
            } catch (e: Exception) {
                withContext(Dispatchers.Main) {
                    MyToastUtil.showInfo(e.message)
                }
            }
        }
    }

}