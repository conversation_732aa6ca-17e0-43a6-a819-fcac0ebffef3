package com.lijianqiang12.silent.component.activity.monitor

import android.animation.ObjectAnimator
import android.content.Context
import android.content.Intent
import android.os.Bundle
import android.os.Handler
import android.os.Message
import android.util.Log
import android.view.Gravity
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.fragment.app.viewModels
import androidx.lifecycle.lifecycleScope
import androidx.recyclerview.widget.DiffUtil
import androidx.recyclerview.widget.GridLayoutManager
import androidx.recyclerview.widget.ItemTouchHelper
import androidx.recyclerview.widget.ListUpdateCallback
import androidx.recyclerview.widget.RecyclerView
import androidx.swiperefreshlayout.widget.SwipeRefreshLayout
import co.mobiwise.materialintro.shape.Focus
import co.mobiwise.materialintro.shape.FocusGravity
import co.mobiwise.materialintro.shape.ShapeType
import co.mobiwise.materialintro.view.MaterialIntroView
import com.alipay.sdk.app.PayTask
import com.blankj.utilcode.util.AppUtils
import com.blankj.utilcode.util.LogUtils
import com.jeremyliao.liveeventbus.LiveEventBus
import com.lijianqiang12.silent.DURATION
import com.lijianqiang12.silent.LiveBus
import com.lijianqiang12.silent.MyConstants
import com.lijianqiang12.silent.R
import com.lijianqiang12.silent.component.activity.base.BaseFragment
import com.lijianqiang12.silent.component.activity.custom.dialog.*
import com.lijianqiang12.silent.component.activity.me.vip.FROM_WHERE
import com.lijianqiang12.silent.component.activity.me.vip.VIP2Activity
import com.lijianqiang12.silent.component.activity.monitor.applimitcreate.AppLimitCreateActivity
import com.lijianqiang12.silent.component.activity.monitor.applimitsetting.AppLimitSettingActivity
import com.lijianqiang12.silent.data.model.db.AppLimit
import com.lijianqiang12.silent.data.model.db.DayLimit
import com.lijianqiang12.silent.data.model.net.MyRetrofitClient
import com.lijianqiang12.silent.data.viewmodel.LockViewModel
import com.lijianqiang12.silent.data.viewmodel.MonitorViewModel
import com.lijianqiang12.silent.utils.*
import com.tencent.mm.opensdk.modelpay.PayReq
import com.tencent.mm.opensdk.openapi.IWXAPI
import com.tencent.mm.opensdk.openapi.WXAPIFactory
import dagger.hilt.android.AndroidEntryPoint
import kotlinx.android.synthetic.main.fragment_monitor.*
import kotlinx.android.synthetic.main.fragment_monitor.view.*
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.launch
import kotlinx.coroutines.withContext
import java.util.Collections
import javax.inject.Inject

private const val ARG_PARAM1 = "param1"
private const val ARG_PARAM2 = "param2"

@AndroidEntryPoint
class MonitorFragment : BaseFragment() {

    private var onCreateTime = 0L

    private var param1: String? = null
    private var param2: String? = null

    private lateinit var swipeRefreshLayout: SwipeRefreshLayout
    private val TAG = "MonitorFragment"
    private lateinit var root: View
    private lateinit var mAdapter: AppLimitAdapter
    private lateinit var mLayoutManager: RecyclerView.LayoutManager
//    private lateinit var innerReceiver: InnerReceiver

//    private var mCurrentDayLimit = DayLimit()

    private var workDayLimit = DayLimit()
    private var restDayLimit = DayLimit().apply {
        isWorkDayLimit = false
        monday = false
        tuesday = false
        wednesday = false
        thursday = false
        friday = false
        saturday = true
        sunday = true
    }
    private var appLimits = mutableListOf<AppLimit>()


    private lateinit var dialog: MyProgressDialog

    private lateinit var api: IWXAPI
    private val SDK_PAY_FLAG = 1

    val viewModel: MonitorViewModel by viewModels()
    private val lockViewModel: LockViewModel by viewModels()

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        arguments?.let {
            param1 = it.getString(ARG_PARAM1)
            param2 = it.getString(ARG_PARAM2)
        }


        onCreateTime = System.currentTimeMillis()
        api = WXAPIFactory.createWXAPI(requireContext(), MyConstants.WX_APP_ID)
        dialog = MyProgressDialog(fragment = this)
//        innerReceiver = InnerReceiver()
//        val intentFilter = IntentFilter()
//        intentFilter.addAction(Intent.ACTION_TIME_TICK)
//        requireActivity().registerReceiver(innerReceiver, intentFilter)

        LiveEventBus.get(LiveBus.PAY_FOR_APP_LIMIT_SUCCEED, Boolean::class.java).observe(this) {
            if (it) {
                paySucceed(PAY_TYPE_WXPAY)
            } else {
                MyToastUtil.showInfo("支付失败")
            }
        }

        LiveEventBus.get(LiveBus.LOGIN, Boolean::class.java).observe(this) {
            viewModel.setUserId(MMKVUtils.getInt(MyConstants.SP_KEY_USER_ID, -1))
        }
    }

    override fun onCreateView(inflater: LayoutInflater, container: ViewGroup?, savedInstanceState: Bundle?): View? {
        root = inflater.inflate(R.layout.fragment_monitor, container, false)
        return root
    }

    override fun lazyInit() {
        swipeRefreshLayout = root.srl_monitor
        swipeRefreshLayout.isRefreshing = true
        mLayoutManager = GridLayoutManager(requireContext(), 2)
        val recyclerview = root.rv_app_limit
        recyclerview.layoutManager = mLayoutManager
        mAdapter = AppLimitAdapter(this, R.layout.item_app_limit, mutableListOf())
        mAdapter.animationEnable = true
        val itemTouchHelper = ItemTouchHelper(ItemCallback(requireContext(), viewModel))
        itemTouchHelper.attachToRecyclerView(recyclerview)
        recyclerview.adapter = mAdapter



        root.mc_day_limit_work.setOnClickListener {

            lifecycleScope.launch(Dispatchers.IO) {

                val dayLength = if (workDayLimit.isIncludeWhite) {
                    viewModel.getDayUsageLength()
                } else {
                    val globalWhiteAppList = lockViewModel.getGlobalWhiteAppList()
                    viewModel.getDayUsageLengthWithoutWhite(globalWhiteAppList)
                }

                withContext(Dispatchers.Main) {
                    if (workDayLimit.isDenyChange &&
                        workDayLimit.allDayLimit != -1L &&
                        dayLength + workDayLimit.denyChangeLength * 60 >= workDayLimit.allDayLimit &&
                        MyDateUtil.isTodayValid(workDayLimit)
                    ) {
                        MyToastUtil.showError("您设置了监督任务在到达限时前${workDayLimit.denyChangeLength}分钟内禁止修改")
                    } else {

                        val dialog = DayMonitorSelectTimeDialog(this@MonitorFragment)

                        dialog.setTitle("工作日使用时长限制")
//                        dialog.setContent("每天手机使用时长超过该时间后，\n将被强制锁定到第二天凌晨0点。")

                        dialog.setDayLimit(workDayLimit)
                        dialog.setOnOKClickListener(object : DayMonitorSelectTimeDialog.OnTimeLimitListener {
                            override fun onclick(dayLimit: DayLimit) {
                                lifecycleScope.launch(Dispatchers.IO) {
                                    val dayLength = if (dayLimit.isIncludeWhite) {
                                        viewModel.getDayUsageLength()
                                    } else {
                                        val globalWhiteAppList = lockViewModel.getGlobalWhiteAppList()
                                        viewModel.getDayUsageLengthWithoutWhite(globalWhiteAppList)
                                    }

                                    withContext(Dispatchers.Main) {
                                        dayLimit.let {

                                            if (it.allDayLimit / 60 < 30) {
                                                MyToastUtil.showError("禁止设置为30分钟以内")
                                            } else if (MyDateUtil.isTodayValid(dayLimit) && (it.allDayLimit - dayLength < 60)) {
                                                NormalDialog(requireActivity()).apply {
                                                    setTitle("警告")
                                                    setContent("当前距离您设置的工作日限时已不到1分钟，确定后将很快进入监督锁机，确定要设置当前限时吗？")
                                                    setGravity(Gravity.START)
                                                    setOnNormalOKClickListener("确定", object : OnOKClickListener {
                                                        override fun onclick() {
                                                            it.jumpDate = ""
                                                            viewModel.updateDayLimit(dayLimit)

                                                            if (workDayLimit.monday != dayLimit.monday || workDayLimit.tuesday != dayLimit.tuesday || workDayLimit.wednesday != dayLimit.wednesday || workDayLimit.thursday != dayLimit.thursday || workDayLimit.friday != dayLimit.friday || workDayLimit.saturday != dayLimit.saturday || workDayLimit.sunday != dayLimit.sunday) {
                                                                restDayLimit.monday = !dayLimit.monday
                                                                restDayLimit.tuesday = !dayLimit.tuesday
                                                                restDayLimit.wednesday = !dayLimit.wednesday
                                                                restDayLimit.thursday = !dayLimit.thursday
                                                                restDayLimit.friday = !dayLimit.friday
                                                                restDayLimit.saturday = !dayLimit.saturday
                                                                restDayLimit.sunday = !dayLimit.sunday
                                                                viewModel.updateDayLimit(restDayLimit)
                                                            }
                                                        }
                                                    })

                                                    setOnNormalCancelClickListener("我再想想", object : OnCancelClickListener {
                                                        override fun onclick() {
                                                        }
                                                    })

                                                    showDialog()
                                                }
                                            } else {
                                                it.jumpDate = ""
                                                viewModel.updateDayLimit(dayLimit)
                                                if (workDayLimit.monday != dayLimit.monday || workDayLimit.tuesday != dayLimit.tuesday || workDayLimit.wednesday != dayLimit.wednesday || workDayLimit.thursday != dayLimit.thursday || workDayLimit.friday != dayLimit.friday || workDayLimit.saturday != dayLimit.saturday || workDayLimit.sunday != dayLimit.sunday) {
                                                    restDayLimit.monday = !dayLimit.monday
                                                    restDayLimit.tuesday = !dayLimit.tuesday
                                                    restDayLimit.wednesday = !dayLimit.wednesday
                                                    restDayLimit.thursday = !dayLimit.thursday
                                                    restDayLimit.friday = !dayLimit.friday
                                                    restDayLimit.saturday = !dayLimit.saturday
                                                    restDayLimit.sunday = !dayLimit.sunday
                                                    viewModel.updateDayLimit(restDayLimit)
                                                }
                                            }
                                        }

                                    }
                                }

                            }
                        })
                        dialog.setOnCancelClickListener(object : OnCancelClickListener {
                            override fun onclick() {
                                workDayLimit.let {
                                    it.jumpDate = ""
                                    it.allDayLimit = -1//24 * 60 * 60L
                                    viewModel.updateDayLimit(it)
                                }
                            }

                        })
                        dialog.show()
                    }
                }


            }


        }


        root.mc_day_limit_rest.setOnClickListener {

            lifecycleScope.launch(Dispatchers.IO) {

                val dayLength = if (restDayLimit.isIncludeWhite) {
                    viewModel.getDayUsageLength()
                } else {
                    val globalWhiteAppList = lockViewModel.getGlobalWhiteAppList()
                    viewModel.getDayUsageLengthWithoutWhite(globalWhiteAppList)
                }

                withContext(Dispatchers.Main) {
                    if (restDayLimit.isDenyChange &&
                        restDayLimit.allDayLimit != -1L &&
                        dayLength + restDayLimit.denyChangeLength * 60 >= restDayLimit.allDayLimit &&
                        MyDateUtil.isTodayValid(restDayLimit)
                    ) {
                        MyToastUtil.showError("您设置了监督任务在到达限时前${restDayLimit.denyChangeLength}分钟内禁止修改")
                    } else {

                        val dialog = DayMonitorSelectTimeDialog(this@MonitorFragment)

                        dialog.setTitle("休息日使用时长限制")
//                        dialog.setContent("每天手机使用时长超过该时间后，\n将被强制锁定到第二天凌晨0点。")

                        dialog.setDayLimit(restDayLimit)
                        dialog.setOnOKClickListener(object : DayMonitorSelectTimeDialog.OnTimeLimitListener {
                            override fun onclick(dayLimit: DayLimit) {
                                lifecycleScope.launch(Dispatchers.IO) {
                                    val dayLength = if (dayLimit.isIncludeWhite) {
                                        viewModel.getDayUsageLength()
                                    } else {
                                        val globalWhiteAppList = lockViewModel.getGlobalWhiteAppList()
                                        viewModel.getDayUsageLengthWithoutWhite(globalWhiteAppList)
                                    }

                                    withContext(Dispatchers.Main) {
                                        dayLimit.let {
                                            if (it.allDayLimit / 60 < 30) {
                                                MyToastUtil.showError("禁止设置为30分钟以内")
                                            } else if (MyDateUtil.isTodayValid(dayLimit) && (it.allDayLimit - dayLength < 60)) {
                                                NormalDialog(requireActivity()).apply {
                                                    setTitle("警告")
                                                    setContent("当前距离您设置的休息日限时已不到1分钟，确定后将很快进入监督锁机，确定要设置当前限时吗？")
                                                    setGravity(Gravity.START)
                                                    setOnNormalOKClickListener("确定", object : OnOKClickListener {
                                                        override fun onclick() {
                                                            it.jumpDate = ""
                                                            viewModel.updateDayLimit(dayLimit)
                                                            if (restDayLimit.monday != dayLimit.monday || restDayLimit.tuesday != dayLimit.tuesday || restDayLimit.wednesday != dayLimit.wednesday || restDayLimit.thursday != dayLimit.thursday || restDayLimit.friday != dayLimit.friday || restDayLimit.saturday != dayLimit.saturday || restDayLimit.sunday != dayLimit.sunday) {
                                                                workDayLimit.monday = !dayLimit.monday
                                                                workDayLimit.tuesday = !dayLimit.tuesday
                                                                workDayLimit.wednesday = !dayLimit.wednesday
                                                                workDayLimit.thursday = !dayLimit.thursday
                                                                workDayLimit.friday = !dayLimit.friday
                                                                workDayLimit.saturday = !dayLimit.saturday
                                                                workDayLimit.sunday = !dayLimit.sunday
                                                                viewModel.updateDayLimit(workDayLimit)
                                                            }
                                                        }
                                                    })

                                                    setOnNormalCancelClickListener("我再想想", object : OnCancelClickListener {
                                                        override fun onclick() {
                                                        }
                                                    })

                                                    showDialog()
                                                }
                                            } else {
                                                it.jumpDate = ""
                                                viewModel.updateDayLimit(dayLimit)
                                                if (restDayLimit.monday != dayLimit.monday || restDayLimit.tuesday != dayLimit.tuesday || restDayLimit.wednesday != dayLimit.wednesday || restDayLimit.thursday != dayLimit.thursday || restDayLimit.friday != dayLimit.friday || restDayLimit.saturday != dayLimit.saturday || restDayLimit.sunday != dayLimit.sunday) {
                                                    workDayLimit.monday = !dayLimit.monday
                                                    workDayLimit.tuesday = !dayLimit.tuesday
                                                    workDayLimit.wednesday = !dayLimit.wednesday
                                                    workDayLimit.thursday = !dayLimit.thursday
                                                    workDayLimit.friday = !dayLimit.friday
                                                    workDayLimit.saturday = !dayLimit.saturday
                                                    workDayLimit.sunday = !dayLimit.sunday
                                                    viewModel.updateDayLimit(workDayLimit)
                                                }
                                            }
                                        }
                                    }
                                }
                            }
                        })
                        dialog.setOnCancelClickListener(object : OnCancelClickListener {
                            override fun onclick() {
                                restDayLimit.let {
                                    it.jumpDate = ""
                                    it.allDayLimit = -1//24 * 60 * 60L
                                    viewModel.updateDayLimit(it)
                                }
                            }

                        })
                        dialog.show()
                    }
                }


            }


        }

        root.btn_goto_usage_permission.setOnClickListener {
            PermissionUtil.openUsagePermission(requireContext().applicationContext)
        }



        mAdapter.setOnItemClickListener { adapter, view, position ->
            val appLimit = adapter.data[position] as AppLimit
            if (appLimit.id == -1L) {
                if (MyUtil.isVIP() || appLimits.size < 6) {
                    val intent = Intent(requireContext(), AppLimitCreateActivity::class.java)
                    requireContext().startActivity(intent)

                } else {
                    DialogUtil.showVIPDialog(
                        activity = null,
                        fragment = this@MonitorFragment,
                        content = "VIP用户可设置5个以上应用时长监督任务，开通后，即可制定更多的app限额计划。",
                        fromWhere = "5MonitorLimit"
                    )
                }
            } else if (appLimit.id == -2L) {
                MyToastUtil.showInfo("示例仅供展示，添加新限时后自动消失")
            } else {

                lifecycleScope.launch(Dispatchers.IO) {
                    val appLimitTitle = MyUtil.getAppLimitTitle(appLimit)
                    withContext(Dispatchers.Main) {
                        if (!appLimit.valid || MyUtil.isCurrentInTimeRange(appLimit.editStartTime, appLimit.editEndTime)) {
                            NormalDialog(this@MonitorFragment).apply {
                                setTitle("温馨提示")
//                                setTitle(MyUtil.getAppLimitTitle(appLimit))
                                setContent("请选择您想要进行的操作")
                                setGravity(Gravity.CENTER)
                                setOnNormalOKClickListener("修改", object : OnOKClickListener {
                                    override fun onclick() {
                                        val intent = Intent(requireContext(), AppLimitCreateActivity::class.java)
                                        intent.putExtra("appLimit", appLimit)
                                        requireContext().startActivity(intent)
                                    }
                                })
                                setOnNormalCancelClickListener("删除", object : OnCancelClickListener {
                                    override fun onclick() {
                                        viewModel.deleteAppLimit(appLimit)
                                    }
                                })
                                showDialog()
                            }
                        } else {
                            NormalDialog(this@MonitorFragment).apply {
                                setTitle("温馨提示")
                                setContent(
                                    "您为『${MyUtil.getAppLimitTitle(appLimit)}』设置的允许修改配置时间段为${
                                        MyUtil.getTimeRangeString(appLimit.editStartTime, appLimit.editEndTime)
                                    }，现在未在该时间段内。"
                                )
                                setGravity(Gravity.CENTER)
                                setOnNormalOKClickListener("强制修改", object : OnOKClickListener {
                                    override fun onclick() {
                                        if (appLimit.editMoney <= 0) {
                                            MyToastUtil.showError("您没有为『${appLimitTitle}』开启强制更改，您可在允许修改配置时间段内为期开启，以防下次遇到此类状况")
                                        } else {
                                            MyUtil.checkLoginAndDo(requireActivity()) {

                                                if (appLimit.uuid <= 0) {
                                                    MyToastUtil.showError("该应用限时任务尚未同步到服务器，请先保持网络通畅并在一分钟后重试")
                                                    LiveEventBus.get(LiveBus.START_SYNC_APP_LIMIT, String::class.java).post("")
                                                }
                                                val bottomDialog = BottomSingleSelectDialogFragment.newInstance()
                                                bottomDialog.setShowList(arrayListOf("支付宝", "微信支付"))
                                                bottomDialog.setValueList(arrayListOf(1, 2))
                                                bottomDialog.setOnValueSelectListener(object : BottomSingleSelectDialogFragment.OnValueSelectListener {
                                                    override fun onSelect(value: Long, show: String) {
                                                        MMKVUtils.put(MyConstants.SP_KEY_PAY_TYPE, 2)
                                                        lifecycleScope.launch(Dispatchers.IO) {
                                                            try {
                                                                when (value) {
                                                                    1L -> {
                                                                        val result =
                                                                            MyRetrofitClient.service.makeAlipayOrderAppLimit(
                                                                                2,
                                                                                appLimit.editMoney / 100,
                                                                                appLimit.uuid
                                                                            )
                                                                        withContext(Dispatchers.Main) {
                                                                            if (result.code == 200) {
                                                                                result.data?.let {
                                                                                    val payRunnable = Runnable {
                                                                                        val alipay = PayTask(<EMAIL>())
                                                                                        val payResult = alipay.payV2(it.order, true) as Map<String, String>
                                                                                        val msg = Message()
                                                                                        msg.what = SDK_PAY_FLAG
                                                                                        msg.obj = payResult
                                                                                        mHandler.sendMessage(msg)
                                                                                    }
                                                                                    val payThread = Thread(payRunnable)
                                                                                    payThread.start()
                                                                                }
                                                                            } else {
                                                                                MyToastUtil.showInfo(result.msg)
                                                                            }
                                                                        }


                                                                    }

                                                                    2L -> {
                                                                        val result =
                                                                            MyRetrofitClient.service.makeWXOrderAppLimit(
                                                                                2,
                                                                                appLimit.editMoney / 100,
                                                                                appLimit.uuid
                                                                            )
                                                                        withContext(Dispatchers.Main) {
                                                                            if (result.code == 200) {
                                                                                result.data?.let {
                                                                                    val request = PayReq()
                                                                                    request.appId = it.appId
                                                                                    request.partnerId = result.data.partnerId
                                                                                    request.prepayId = result.data.prepayId
                                                                                    request.packageValue = result.data.packageValue
                                                                                    request.nonceStr = result.data.nonceStr
                                                                                    request.timeStamp = result.data.timeStamp
                                                                                    request.sign = result.data.sign
                                                                                    api.sendReq(request)
                                                                                }
                                                                            } else {
                                                                                MyToastUtil.showInfo(result.msg)
                                                                            }
                                                                        }
                                                                    }
                                                                }
                                                            } catch (e: Exception) {
                                                                withContext(Dispatchers.Main) {
                                                                    MyToastUtil.showInfo(e.message)
                                                                }
                                                            }
                                                        }
                                                    }
                                                })
                                                bottomDialog.show(fragmentManager!!, "")
                                            }
                                        }
                                    }
                                })
                                setOnNormalCancelClickListener("取消", object : OnCancelClickListener {
                                    override fun onclick() {
                                    }
                                })
                                showDialog()
                            }

                        }
                    }
                }


            }
        }



        viewModel.getDayLimits(true).observe(viewLifecycleOwner) {
            if (it == null) {
                viewModel.insertDayLimit(workDayLimit)
            } else {
                workDayLimit = it
                refreshWorkDayLimitView()
            }
        }

        viewModel.getDayLimits(false).observe(viewLifecycleOwner) {
            if (it == null) {
                viewModel.insertDayLimit(restDayLimit)
            } else {
                restDayLimit = it
                refreshRestDayLimitView()
            }
        }

//        viewModel.setUserId(MMKVUtils.getInt(MyConstants.SP_KEY_USER_ID, -1))
        viewModel.appLimits.observe(viewLifecycleOwner) {
            LogUtils.d("dispatchUpdatesTo it.size:${it.size} appLimits.size:${appLimits.size}")
            //空的状态下有示例和加号，appLimits.size == 2，此时新增一个，it.size为1，应该刷新
            if (!(it.size == 1 && appLimits.size == 2) &&
                (appLimits.size == it.size + 1)
            ) {
                lifecycleScope.launch(Dispatchers.Default) {
                    //有内容，但数量没变，内容可能有变化，比如修改其中某个限时或直接解锁，这种情况需要处理
                    val diffResult = DiffUtil.calculateDiff(DiffCallBack(appLimits, it), true)
                    diffResult.dispatchUpdatesTo(object : ListUpdateCallback {
                        override fun onInserted(position: Int, count: Int) {
                            LogUtils.d("dispatchUpdatesTo onInserted position:$position count:$count")
                        }

                        override fun onRemoved(position: Int, count: Int) {
                            LogUtils.d("dispatchUpdatesTo onRemoved position:$position count:$count")
                        }

                        override fun onMoved(fromPosition: Int, toPosition: Int) {
                            LogUtils.d("dispatchUpdatesTo onMoved fromPosition:$fromPosition toPosition:$toPosition")
                        }

                        override fun onChanged(position: Int, count: Int, payload: Any?) {
                            LogUtils.d("dispatchUpdatesTo onChanged position:$position count:$count payload:$payload")
                            lifecycleScope.launch(Dispatchers.Main) {
                                appLimits = it
                                if (appLimits.size == 0) {
                                    appLimits.add(AppLimit(id = -2L, title = "示例", appPkg = "[\"${AppUtils.getAppPackageName()}\"]"))
                                }
                                appLimits.add(AppLimit(id = -1))

                                mAdapter.setNewInstance(appLimits)
                                diffResult.dispatchUpdatesTo(mAdapter)
                            }
                        }
                    })
                }
            } else {
                lifecycleScope.launch(Dispatchers.Default) {
                    val diffResult = DiffUtil.calculateDiff(DiffCallBack(appLimits, it), true)
                    withContext(Dispatchers.Main) {
                        appLimits = it
                        if (appLimits.size == 0) {
                            appLimits.add(AppLimit(id = -2L, title = "示例", appPkg = "[\"${AppUtils.getAppPackageName()}\"]"))
                        }
                        appLimits.add(AppLimit(id = -1))

                        mAdapter.setNewInstance(appLimits)
                        diffResult.dispatchUpdatesTo(mAdapter)
                    }
                }
            }

        }

        swipeRefreshLayout.setOnRefreshListener {
            refreshView()
        }

        root.tv_vip_flag_monitor.setOnClickListener {
            val intent = Intent(requireContext(), VIP2Activity::class.java)
            intent.putExtra(FROM_WHERE, "5MonitorCard")
            startActivity(intent)
        }

        root.iv_app_limit_setting.setOnClickListener {
            startActivity(Intent(requireContext(), AppLimitSettingActivity::class.java))
        }

        if (PermissionUtil.switched(requireContext().applicationContext)) {
            MaterialIntroView.Builder(requireActivity())
                .enableDotAnimation(true)
                .enableIcon(false)
                .setFocusGravity(FocusGravity.CENTER)
                .setFocusType(Focus.MINIMUM)
                .setDelayMillis(0)
                .setShape(ShapeType.RECTANGLE)
//                .setIdempotent(true)
                .enableFadeAnimation(true)
                .performClick(true)
                .setInfoText("在这里可以设置疲劳提醒。")
//                .setShapeType(ShapeType.CIRCLE)
                .setTarget(root.iv_app_limit_setting)
                .setUsageId(MyConstants.SP_KEY_INTRO_21) //THIS SHOULD BE UNIQUE ID
                .setListener {
                }
                .show()
        }
    }

    private fun refreshView() {
        refreshWorkDayLimitView()
        refreshRestDayLimitView()
        refreshAppLimitView()
    }

    private fun refreshWorkDayLimitView() {


        lifecycleScope.launch(Dispatchers.IO) {

            val dayLength = if (workDayLimit.isIncludeWhite) {
                viewModel.getDayUsageLength()
            } else {
                val globalWhiteAppList = lockViewModel.getGlobalWhiteAppList()
                viewModel.getDayUsageLengthWithoutWhite(globalWhiteAppList)
            }

            withContext(Dispatchers.Main) {
                val isTodayValid = MyDateUtil.isTodayValid(workDayLimit)
                if (isTodayValid) {
                    cl_day_limit_work_cover.visibility = View.GONE
                } else {
                    cl_day_limit_work_cover.visibility = View.VISIBLE
                }
                if (workDayLimit.monday) {
                    tv_day_limit_work_week_select_1.setBackgroundResource(R.drawable.bg_week_select_thin)
                } else {
                    tv_day_limit_work_week_select_1.setBackgroundResource(R.drawable.bg_week_not_select_thin)
                }
                if (workDayLimit.tuesday) {
                    tv_day_limit_work_week_select_2.setBackgroundResource(R.drawable.bg_week_select_thin)
                } else {
                    tv_day_limit_work_week_select_2.setBackgroundResource(R.drawable.bg_week_not_select_thin)
                }
                if (workDayLimit.wednesday) {
                    tv_day_limit_work_week_select_3.setBackgroundResource(R.drawable.bg_week_select_thin)
                } else {
                    tv_day_limit_work_week_select_3.setBackgroundResource(R.drawable.bg_week_not_select_thin)
                }
                if (workDayLimit.thursday) {
                    tv_day_limit_work_week_select_4.setBackgroundResource(R.drawable.bg_week_select_thin)
                } else {
                    tv_day_limit_work_week_select_4.setBackgroundResource(R.drawable.bg_week_not_select_thin)
                }
                if (workDayLimit.friday) {
                    tv_day_limit_work_week_select_5.setBackgroundResource(R.drawable.bg_week_select_thin)
                } else {
                    tv_day_limit_work_week_select_5.setBackgroundResource(R.drawable.bg_week_not_select_thin)
                }
                if (workDayLimit.saturday) {
                    tv_day_limit_work_week_select_6.setBackgroundResource(R.drawable.bg_week_select_thin)
                } else {
                    tv_day_limit_work_week_select_6.setBackgroundResource(R.drawable.bg_week_not_select_thin)
                }
                if (workDayLimit.sunday) {
                    tv_day_limit_work_week_select_7.setBackgroundResource(R.drawable.bg_week_select_thin)
                } else {
                    tv_day_limit_work_week_select_7.setBackgroundResource(R.drawable.bg_week_not_select_thin)
                }

                val leftView = root.tv_home_time_all_left
                var percent = 0f
                if (isTodayValid) {
                    root.tv_home_used_all_time.text = "已用：${secondToHmEnglish(dayLength)}"

                    when {
                        workDayLimit.allDayLimit == -1L -> {
                            leftView.text = "无限制"
                            leftView.background = requireContext().resources.getDrawable(R.drawable.shape_analyze_detactive_bg)
                        }

                        workDayLimit.allDayLimit > dayLength -> {
                            leftView.text = "还剩：${secondToHmEnglish((workDayLimit.allDayLimit / 60 - dayLength / 60) * 60)}"
                            if (dayLength < workDayLimit.allDayLimit / 2) {
                                leftView.background = requireContext().resources.getDrawable(R.drawable.shape_analyze_active_bg)
                            } else if (dayLength < workDayLimit.allDayLimit * 3 / 4) {
                                leftView.background = requireContext().resources.getDrawable(R.drawable.shape_analyze_active_bg_yellow)
                            } else {
                                leftView.background = requireContext().resources.getDrawable(R.drawable.shape_analyze_active_bg_red)
                            }
                        }

                        else -> {
                            leftView.text = "已用完"
                            leftView.background = requireContext().resources.getDrawable(R.drawable.shape_analyze_active_bg_red)
                        }
                    }

                    if (workDayLimit.jumpDate == MyUtil.getTodayCalendarString()) {
//                        leftView.text = leftView.text.toString() + "（今日已关闭，若要开启，请重新设置限时）"
                        leftView.text = "今日暂停，重设生效"
                        leftView.background = requireContext().resources.getDrawable(R.drawable.shape_analyze_detactive_bg)
                    }

                    percent = when {
                        workDayLimit.allDayLimit == -1L -> {
                            -2f
                        }

                        dayLength >= workDayLimit.allDayLimit -> {
                            100f
                        }

                        else -> {
                            dayLength * 100f / workDayLimit.allDayLimit
                        }
                    }
                } else {
                    root.tv_home_used_all_time.text = "不在当前时间"
                    when (workDayLimit.allDayLimit) {
                        -1L -> {
                            leftView.text = "无限制"
                        }

                        else -> {
                            leftView.text = "限时：${secondToHmEnglish(workDayLimit.allDayLimit)}"
                        }
                    }
                    percent = 0f
                }
                startAnimation(root.tv_home_used_all_time)
                startAnimation(leftView)
                val animator: ObjectAnimator = ObjectAnimator.ofFloat(root.view_home_usage_percent, "progress", if (percent >= 0) 0f else percent, percent)
                animator.duration = DURATION
                animator.start()

                if (swipeRefreshLayout.isRefreshing) swipeRefreshLayout.isRefreshing = false
            }
        }
    }

    private fun refreshRestDayLimitView() {


        lifecycleScope.launch(Dispatchers.IO) {

            val dayLength = if (restDayLimit.isIncludeWhite) {
                viewModel.getDayUsageLength()
            } else {
                val globalWhiteAppList = lockViewModel.getGlobalWhiteAppList()
                viewModel.getDayUsageLengthWithoutWhite(globalWhiteAppList)
            }

            withContext(Dispatchers.Main) {
                val isTodayValid = MyDateUtil.isTodayValid(restDayLimit)
                if (isTodayValid) {
                    cl_day_limit_rest_cover.visibility = View.GONE
                } else {
                    cl_day_limit_rest_cover.visibility = View.VISIBLE
                }
                if (restDayLimit.monday) {
                    tv_day_limit_rest_week_select_1.setBackgroundResource(R.drawable.bg_week_select_thin)
                } else {
                    tv_day_limit_rest_week_select_1.setBackgroundResource(R.drawable.bg_week_not_select_thin)
                }
                if (restDayLimit.tuesday) {
                    tv_day_limit_rest_week_select_2.setBackgroundResource(R.drawable.bg_week_select_thin)
                } else {
                    tv_day_limit_rest_week_select_2.setBackgroundResource(R.drawable.bg_week_not_select_thin)
                }
                if (restDayLimit.wednesday) {
                    tv_day_limit_rest_week_select_3.setBackgroundResource(R.drawable.bg_week_select_thin)
                } else {
                    tv_day_limit_rest_week_select_3.setBackgroundResource(R.drawable.bg_week_not_select_thin)
                }
                if (restDayLimit.thursday) {
                    tv_day_limit_rest_week_select_4.setBackgroundResource(R.drawable.bg_week_select_thin)
                } else {
                    tv_day_limit_rest_week_select_4.setBackgroundResource(R.drawable.bg_week_not_select_thin)
                }
                if (restDayLimit.friday) {
                    tv_day_limit_rest_week_select_5.setBackgroundResource(R.drawable.bg_week_select_thin)
                } else {
                    tv_day_limit_rest_week_select_5.setBackgroundResource(R.drawable.bg_week_not_select_thin)
                }
                if (restDayLimit.saturday) {
                    tv_day_limit_rest_week_select_6.setBackgroundResource(R.drawable.bg_week_select_thin)
                } else {
                    tv_day_limit_rest_week_select_6.setBackgroundResource(R.drawable.bg_week_not_select_thin)
                }
                if (restDayLimit.sunday) {
                    tv_day_limit_rest_week_select_7.setBackgroundResource(R.drawable.bg_week_select_thin)
                } else {
                    tv_day_limit_rest_week_select_7.setBackgroundResource(R.drawable.bg_week_not_select_thin)
                }


                val leftView = root.tv_home_time_all_left_rest
                var percent = 0f
                if (isTodayValid) {
                    root.tv_home_used_all_time_rest.text = "已用：${secondToHmEnglish(dayLength)}"

//                    when {
//                        restDayLimit.allDayLimit == -1L -> {
//                            leftView.text = "无限制"
//                        }
//
//                        restDayLimit.allDayLimit > dayLength -> {
//                            leftView.text = "还剩：${secondToHmEnglish((restDayLimit.allDayLimit / 60 - dayLength / 60) * 60)}"
//                        }
//
//                        else -> {
//                            leftView.text = "已用完"
//                        }
//                    }

                    when {
                        restDayLimit.allDayLimit == -1L -> {
                            leftView.text = "无限制"
                            leftView.background = requireContext().resources.getDrawable(R.drawable.shape_analyze_detactive_bg)
                        }

                        restDayLimit.allDayLimit > dayLength -> {
                            leftView.text = "还剩：${secondToHmEnglish((restDayLimit.allDayLimit / 60 - dayLength / 60) * 60)}"
                            if (dayLength < restDayLimit.allDayLimit / 2) {
                                leftView.background = requireContext().resources.getDrawable(R.drawable.shape_analyze_active_bg)
                            } else if (dayLength < restDayLimit.allDayLimit * 3 / 4) {
                                leftView.background = requireContext().resources.getDrawable(R.drawable.shape_analyze_active_bg_yellow)
                            } else {
                                leftView.background = requireContext().resources.getDrawable(R.drawable.shape_analyze_active_bg_red)
                            }
                        }

                        else -> {
                            leftView.text = "已用完"
                            leftView.background = requireContext().resources.getDrawable(R.drawable.shape_analyze_active_bg_red)
                        }
                    }


                    if (restDayLimit.jumpDate == MyUtil.getTodayCalendarString()) {
//                        leftView.text = leftView.text.toString() + "（今日已关闭，若要开启，请重新设置限时）"
                        leftView.text = "今日暂停，重设生效"
                        leftView.background = requireContext().resources.getDrawable(R.drawable.shape_analyze_detactive_bg)
                    }

                    percent = when {
                        restDayLimit.allDayLimit == -1L -> {
                            -2f
                        }

                        dayLength >= restDayLimit.allDayLimit -> {
                            100f
                        }

                        else -> {
                            dayLength * 100f / restDayLimit.allDayLimit
                        }
                    }
                } else {
                    root.tv_home_used_all_time_rest.text = "不在当前时间"
                    when (restDayLimit.allDayLimit) {
                        -1L -> {
                            leftView.text = "无限制"
                        }

                        else -> {
                            leftView.text = "限时：${secondToHmEnglish(restDayLimit.allDayLimit)}"
                        }
                    }
                    percent = 0f
                }

                startAnimation(root.tv_home_used_all_time_rest)
                startAnimation(leftView)
                val animator: ObjectAnimator = ObjectAnimator.ofFloat(root.view_home_usage_percent_rest, "progress", if (percent >= 0) 0f else percent, percent)
                animator.duration = DURATION
                animator.start()

                if (swipeRefreshLayout.isRefreshing) swipeRefreshLayout.isRefreshing = false
            }
        }
    }

    private fun refreshAppLimitView() {
//        mAdapter.data = mutableListOf()
        mAdapter.setNewInstance(appLimits)
        if (swipeRefreshLayout.isRefreshing) swipeRefreshLayout.isRefreshing = false
    }

    private val mHandler = Handler {
        if (it.what == SDK_PAY_FLAG) {
            val payResult = PayResult(it.obj as Map<String, String>)
            when (payResult.resultStatus) {
                "6001" -> {
                    MyToastUtil.showInfo("支付失败")
                }

                else -> {
                    paySucceed(PAY_TYPE_ALIPAY)
                }
            }
        }
        true
    }

    private fun paySucceed(payType: Int) {

        lifecycleScope.launch(Dispatchers.IO) {
            try {
                val result = MyRetrofitClient.service.queryOrder()
                if (result.code == 200) {
                    withContext(Dispatchers.Main) {
                        MyToastUtil.showInfo("支付成功")
                        LiveEventBus.get(LiveBus.START_SYNC_APP_LIMIT, String::class.java).post("")
                    }
                } else {
                    withContext(Dispatchers.Main) {
                        MyToastUtil.showError(result.msg)
                    }
                }
            } catch (e: Exception) {
                MyToastUtil.showInfo(e.message)
            }

        }
    }


    override fun onResume() {
//        MobclickAgent.onPageStart("MonitorFragment")
        super.onResume()
        Log.d(TAG, "onResume")

        if (PermissionUtil.switched(requireContext().applicationContext)) {
            root.srl_monitor.visibility = View.VISIBLE
            root.cl_monitor_empty_view.visibility = View.GONE

            if (onCreateTime + 5000 < System.currentTimeMillis()) {//防止启动时连续两次刷新造成动画重影
                refreshView()
            }
        } else {
            root.srl_monitor.visibility = View.GONE
            root.cl_monitor_empty_view.visibility = View.VISIBLE
            if (swipeRefreshLayout.isRefreshing) swipeRefreshLayout.isRefreshing = false
        }

        if (MyUtil.isVIP()) {
            root.tv_vip_flag_monitor.visibility = View.GONE
        } else {
            root.tv_vip_flag_monitor.visibility = View.VISIBLE
        }
    }

//    override fun onPause() {
//        MobclickAgent.onPageEnd("MonitorFragment")
//        super.onPause()
//    }


    private class ItemCallback(val context: Context, val viewModel: MonitorViewModel) : ItemTouchHelper.Callback() {
        override fun getMovementFlags(p0: RecyclerView, p1: RecyclerView.ViewHolder): Int {
            return makeMovementFlags(ItemTouchHelper.UP or ItemTouchHelper.DOWN or ItemTouchHelper.LEFT or ItemTouchHelper.RIGHT, 0)
        }

        override fun onMove(recycler: RecyclerView, holder1: RecyclerView.ViewHolder, holder2: RecyclerView.ViewHolder): Boolean {
            val fromPosition: Int = holder1.getBindingAdapterPosition()
            val toPosition: Int = holder2.getBindingAdapterPosition()

            val dataList = (recycler.adapter as AppLimitAdapter).data
            if (dataList[fromPosition].id == -1L || dataList[toPosition].id == -1L) {
                return false
            }

            if (fromPosition < (recycler.adapter as AppLimitAdapter).data.size &&
                toPosition < (recycler.adapter as AppLimitAdapter).data.size
            ) {

                if (fromPosition < toPosition) {
                    for (i in fromPosition until toPosition) {
                        swapData(recycler, i, i + 1)
                    }
                } else {
                    for (i in fromPosition downTo toPosition + 1) {
                        swapData(recycler, i, i - 1)
                    }
                }

            }
            return true
        }

        override fun onSwiped(recycler: RecyclerView.ViewHolder, p1: Int) {

        }

        override fun isLongPressDragEnabled(): Boolean {
            return true
        }

        fun swapData(recycler: RecyclerView, position1: Int, position2: Int) {
            Collections.swap((recycler.adapter as AppLimitAdapter).data, position1, position2)
            (recycler.adapter as AppLimitAdapter).notifyItemMoved(position1, position2)

            swapAppLimitTrendWithMinimalImpact((recycler.adapter as AppLimitAdapter).data, position1, position2)
        }

        fun swapAppLimitTrendWithMinimalImpact(dataList: MutableList<AppLimit>, pos1: Int, pos2: Int) {
            if (pos1 !in dataList.indices || pos2 !in dataList.indices) return

            val item1 = dataList[pos1]
            val item2 = dataList[pos2]

            //这里修改pos1的trend，因为拖动的是pos2，这样如果trend都是0，拖一圈就能全改了。
            if (item1.trend == item2.trend) {
                val newTrendForItem1 = generateNearestUniqueTrend(dataList, item1.trend)
                item1.trend = newTrendForItem1
            } else {
                // 如果trend不相等，交换trend值
                item1.trend = item2.trend.also { item2.trend = item1.trend }
            }
            viewModel.updateAppLimit(item1, sync = false)
            viewModel.updateAppLimit(item2, sync = false)

        }

        private fun generateNearestUniqueTrend(appLimits: List<AppLimit>, baseTrend: Int): Int {
            var newTrend = baseTrend + 1
            while (appLimits.any { it.trend == newTrend }) {
                newTrend += 1
            }
            return newTrend
        }

//        fun swapAppLimitTrend(appLimits: MutableList<AppLimit>, pos1: Int, pos2: Int) {
//            // 确保位置合法
//            if (pos1 !in appLimits.indices || pos2 !in appLimits.indices) return
//
//            // 获取两个位置上的AppLimit
//            val item1 = appLimits[pos1]
//            val item2 = appLimits[pos2]
//
//            // 判断两个trend是否相等
//            if (item1.trend == item2.trend) {
//                // 查找除了这两个位置外的最大trend
//                val maxTrend = appLimits.filterIndexed { index, _ -> index != pos1 && index != pos2 }.maxOfOrNull { it.trend } ?: -1
//
//                // 为避免重复，两个元素的trend分别设置为maxTrend+1和maxTrend+2
//                item1.trend = maxTrend + 1
//                item2.trend = maxTrend + 2
//            } else {
//                // 如果trend不相等，简单交换trend值
//                val tempTrend = item1.trend
//                item1.trend = item2.trend
//                item2.trend = tempTrend
//            }
//
//            // 重新按trend进行排序
//            appLimits.sortBy { it.trend }
//        }


//        override fun onSelectedChanged(viewHolder: RecyclerView.ViewHolder?, actionState: Int) {
//            super.onSelectedChanged(viewHolder, actionState)
//            if (actionState == ItemTouchHelper.ACTION_STATE_IDLE) {
//                // 拖动结束
//            }
//            LogUtils.d("onSelectedChanged")
//        }
//
//        override fun clearView(recyclerView: RecyclerView, viewHolder: RecyclerView.ViewHolder) {
//            super.clearView(recyclerView, viewHolder)
//            LogUtils.d("clearView")
//        }
    }


    private class DiffCallBack(val oldList: MutableList<AppLimit>, val newList: MutableList<AppLimit>) : DiffUtil.Callback() {

        override fun areItemsTheSame(oldItemPosition: Int, newItemPosition: Int): Boolean {
            return (oldList[oldItemPosition].appLimitIndexId == newList[newItemPosition].appLimitIndexId)
        }

        override fun getOldListSize(): Int {
            return oldList.size
        }

        override fun getNewListSize(): Int {
            return newList.size
        }

        override fun areContentsTheSame(oldItemPosition: Int, newItemPosition: Int): Boolean {
            return (oldList[oldItemPosition].title == newList[newItemPosition].title) &&
                    (oldList[oldItemPosition].appPkg == newList[newItemPosition].appPkg) &&
                    (oldList[oldItemPosition].ifAllDay == newList[newItemPosition].ifAllDay) &&
                    (oldList[oldItemPosition].startTime == newList[newItemPosition].startTime) &&
                    (oldList[oldItemPosition].endTime == newList[newItemPosition].endTime) &&
                    (oldList[oldItemPosition].limitLength == newList[newItemPosition].limitLength) &&
                    (oldList[oldItemPosition].sunday == newList[newItemPosition].sunday) &&
                    (oldList[oldItemPosition].monday == newList[newItemPosition].monday) &&
                    (oldList[oldItemPosition].tuesday == newList[newItemPosition].tuesday) &&
                    (oldList[oldItemPosition].wednesday == newList[newItemPosition].wednesday) &&
                    (oldList[oldItemPosition].thursday == newList[newItemPosition].thursday) &&
                    (oldList[oldItemPosition].friday == newList[newItemPosition].friday) &&
                    (oldList[oldItemPosition].saturday == newList[newItemPosition].saturday) &&
                    (oldList[oldItemPosition].valid == newList[newItemPosition].valid)
        }

    }

    companion object {
        @JvmStatic
        fun newInstance(param1: String, param2: String) =
            MonitorFragment().apply {
                arguments = Bundle().apply {
                    putString(ARG_PARAM1, param1)
                    putString(ARG_PARAM2, param2)
                }
            }
    }


}
