package com.lijianqiang12.silent.component.activity.lock.setting

import android.os.Bundle
import androidx.lifecycle.lifecycleScope
import androidx.recyclerview.widget.LinearLayoutManager
import androidx.recyclerview.widget.RecyclerView
import com.blankj.utilcode.util.GsonUtils
import com.lijianqiang12.silent.utils.MMKVUtils
import com.lijianqiang12.silent.MyConstants
import com.lijianqiang12.silent.R
import com.lijianqiang12.silent.data.model.net.MyRetrofitClient
import com.lijianqiang12.silent.data.model.net.pojos.FastDenyPageExample
import com.lijianqiang12.silent.component.activity.base.BaseActivity
import com.lijianqiang12.silent.utils.*
import kotlinx.android.synthetic.main.activity_fast_deny_page_setting.*
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.launch
import kotlinx.coroutines.withContext

class FastDenyPageSettingActivity : BaseActivity() {

    private lateinit var recyclerview: RecyclerView
    private lateinit var adapter: FastDenyPageExampleAdapter

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        setContentView(R.layout.activity_fast_deny_page_setting)

        iv_fast_deny_page_return.setOnClickListener { finish() }

        recyclerview = rv_fast_deny_page
        recyclerview.layoutManager = LinearLayoutManager(this)
        adapter = FastDenyPageExampleAdapter(R.layout.item_fast_deny_page_example, mutableListOf())
        adapter.animationEnable = true
        recyclerview.adapter = adapter

        adapter.setOnItemClickListener { adapter, view, position ->

            val denyPageList = MyGsonUtil.getDenyPageList()

            var theDenyPage: TheDenyPage2? = null
            val fastDenyPageExample = adapter.data[position] as FastDenyPageExample

            run denyPageListLoop@{
                denyPageList.forEach {
                    if (it.id == fastDenyPageExample.id) {
                        theDenyPage = it
                        return@denyPageListLoop
                    }
                }
            }

            if (theDenyPage != null && theDenyPage!!.valid) {
                theDenyPage!!.valid = false

                MMKVUtils.put(MyConstants.SP_KEY_DENY_PAGE_V2, GsonUtils.toJson(denyPageList))
//                MMKVUtils.put(MyConstants.SP_KEY_DENY_PAGE_V2, GsonUtils.toJson(denyPageList))
                adapter.notifyDataSetChanged()
            } else {
                if (MyUtil.isVIP() || getDenyPageValidCount() < 3) {
                    if (theDenyPage == null) {
                        denyPageList.add(
                            TheDenyPage2(
                                fastDenyPageExample.id,
                                fastDenyPageExample.name,
                                fastDenyPageExample.pages,
                                true,
                                fastDenyPageExample.version,
                                DENY_PAGE_TYPE_FAST
                            )
                        )
                        MMKVUtils.put(MyConstants.SP_KEY_DENY_PAGE_V2, GsonUtils.toJson(denyPageList))
//                        MMKVUtils.put(MyConstants.SP_KEY_DENY_PAGE_V2, GsonUtils.toJson(denyPageList))
                        adapter.notifyDataSetChanged()
                    } else {
                        theDenyPage!!.valid = true

                        MMKVUtils.put(MyConstants.SP_KEY_DENY_PAGE_V2, GsonUtils.toJson(denyPageList))
//                        MMKVUtils.put(MyConstants.SP_KEY_DENY_PAGE_V2, GsonUtils.toJson(denyPageList))
                        adapter.notifyDataSetChanged()
                    }
                } else {
                    DialogUtil.showVIPDialog(this, null, content = "VIP用户可添加3个以上屏蔽页面。", "FaseDenyPageSettingActivity reverse")
                }
            }


        }

        srl_fast_deny_page.setOnRefreshListener {
            refreshPage()
        }


        refreshPage()
    }

    override fun onResume() {
        super.onResume()

    }

    private fun refreshPage() {
        srl_fast_deny_page.isRefreshing = true

        lifecycleScope.launch(Dispatchers.IO) {
            try {
                val result = MyRetrofitClient.service.getFastDenyPageExample()
                if (result.code == 200) {
                    withContext(Dispatchers.Main) {
                        result.data?.apply {
                            adapter.setNewInstance(this)

                        }
                    }
                } else {
                    MyToastUtil.showError(result.msg)
                }
            } catch (e: Exception) {
                MyToastUtil.showError(e.message)
            } finally {

                srl_fast_deny_page.isRefreshing = false
            }
        }


    }
}