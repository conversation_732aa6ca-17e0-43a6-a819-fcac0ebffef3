package com.lijianqiang12.silent.component.activity.monitor

import android.graphics.Color
import android.os.Bundle
import android.os.Handler
import android.util.TypedValue
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.TextView
import androidx.appcompat.app.AppCompatActivity
import androidx.appcompat.widget.SearchView
import androidx.fragment.app.viewModels
import androidx.lifecycle.lifecycleScope
import androidx.recyclerview.widget.DiffUtil
import androidx.recyclerview.widget.GridLayoutManager
import androidx.recyclerview.widget.RecyclerView
import com.lijianqiang12.silent.utils.MMKVUtils
import com.google.android.material.bottomsheet.BottomSheetBehavior
import com.google.android.material.bottomsheet.BottomSheetDialogFragment
import com.lijianqiang12.silent.MyConstants
import com.lijianqiang12.silent.R
import com.lijianqiang12.silent.component.activity.lock.whiteapp.AppInfo
import com.lijianqiang12.silent.component.activity.lock.whiteapp.AppInfoAdapter
import com.lijianqiang12.silent.data.viewmodel.LockViewModel
import com.lijianqiang12.silent.utils.dpToPixel
import dagger.hilt.android.AndroidEntryPoint
import kotlinx.android.synthetic.main.bottom_sheet_lock_white.view.*
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.launch
import kotlinx.coroutines.withContext
import javax.inject.Inject

@AndroidEntryPoint
class AppBottomSheetDialogFragment() : BottomSheetDialogFragment() {


    private var appSelectListener: OnAppSelectListener? = null
    private lateinit var mBehavior: BottomSheetBehavior<View>
    private lateinit var customView: View
    private lateinit var recyclerview: RecyclerView
    private var appInfoList: MutableList<AppInfo> = mutableListOf()

    private val viewModel: LockViewModel by viewModels()


    companion object {
        @JvmStatic
        fun newInstance(): AppBottomSheetDialogFragment {
            return AppBottomSheetDialogFragment()
        }
    }

    interface OnAppSelectListener {
        fun onclick(pkg: String, appName: String)
    }

    fun setOnAppSelectListener(appSelectListener: OnAppSelectListener) {
        this.appSelectListener = appSelectListener
    }

    override fun onCreateView(inflater: LayoutInflater, container: ViewGroup?, savedInstanceState: Bundle?): View? {
        customView = View.inflate(requireContext(), R.layout.bottom_sheet_lock_app, null)
        return customView
    }

    override fun onStart() {
        super.onStart()
        val parentView = customView.parent as View
//        parentView.setBackgroundColor(resources.getColor(R.color.colorTranslate))
        Handler().postDelayed( {
            parentView.setBackgroundColor(resources.getColor(R.color.colorTranslate))//直接写没有圆角，不知道为什么
        },0)

        //设置父窗口为屏幕高度
        val layoutParams = parentView.layoutParams
        layoutParams.height = (requireActivity() as AppCompatActivity).findViewById<ViewGroup>(android.R.id.content).height - dpToPixel(MMKVUtils.getFloat(MyConstants.SP_KEY_STATUS_BAR_HEIGHT, 32f)).toInt()

        //设置初始状态为填充满
        mBehavior = BottomSheetBehavior.from(parentView)
        mBehavior.state = BottomSheetBehavior.STATE_EXPANDED



    }


    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)

        recyclerview = customView.rv_lock_global_white
        recyclerview.layoutManager = GridLayoutManager(requireContext(), 6)
        val adapter = AppInfoAdapter(R.layout.item_bottom_sheet_edit, mutableListOf())
        adapter.animationEnable = true
        recyclerview.adapter = adapter

        (recyclerview.adapter as AppInfoAdapter).setOnItemClickListener { adapter, view, position ->
            appSelectListener?.apply {
                onclick((adapter.getItem(position) as AppInfo).pkg, (adapter.getItem(position) as AppInfo).appName)
            }
            dismiss()
        }


        viewModel.getAllAppsInfo.observe(viewLifecycleOwner) {
            lifecycleScope.launch(Dispatchers.Default) {
                val diffResult = DiffUtil.calculateDiff(DiffCallBack1(appInfoList, it), true)
                withContext(Dispatchers.Main) {
                    (recyclerview.adapter as AppInfoAdapter).setNewInstance(it)
                    appInfoList = it
                    diffResult.dispatchUpdatesTo(recyclerview.adapter as AppInfoAdapter)
                    customView.liv_white.hide()
                }
            }
        }


        customView.searchView.maxWidth = Int.MAX_VALUE//宽度铺满屏幕

        customView.searchView.findViewById<View>(androidx.appcompat.R.id.search_plate)?.setBackgroundColor(Color.TRANSPARENT)//删除底部横线
        customView.searchView.findViewById<TextView>(androidx.appcompat.R.id.search_src_text)?.setTextSize(TypedValue.COMPLEX_UNIT_SP, 16f)
        customView.searchView.setOnQueryTextListener(object : SearchView.OnQueryTextListener {
            override fun onQueryTextSubmit(query: String?): Boolean {
                return true
            }

            override fun onQueryTextChange(newText: String?): Boolean {
                var result = ""
                if (newText != null) {
                    result = newText
                }
                viewModel.getAllAppsInfoWithText(result)
                return true
            }

        })
    }





    private class DiffCallBack1(val oldList: MutableList<AppInfo>, val newList: MutableList<AppInfo>) : DiffUtil.Callback() {

        override fun areItemsTheSame(oldItemPosition: Int, newItemPosition: Int): Boolean {
            return (oldList[oldItemPosition].pkg == newList[newItemPosition].pkg)
                    && (oldList[oldItemPosition].mainActivity == newList[newItemPosition].mainActivity)
        }

        override fun getOldListSize(): Int {
            return oldList.size
        }

        override fun getNewListSize(): Int {
            return newList.size
        }

        override fun areContentsTheSame(oldItemPosition: Int, newItemPosition: Int): Boolean {
            return (oldList[oldItemPosition].pkg == newList[newItemPosition].pkg)
                    && (oldList[oldItemPosition].mainActivity == newList[newItemPosition].mainActivity)
        }

    }



//    fun show() {
//        activity?.apply {
//            super.show(this.supportFragmentManager, "NormalDialog")
//        }
//
//        fragment?.apply {
//            super.show(fragment!!.requireFragmentManager(), "NormalDialog")
//        }
//    }


}