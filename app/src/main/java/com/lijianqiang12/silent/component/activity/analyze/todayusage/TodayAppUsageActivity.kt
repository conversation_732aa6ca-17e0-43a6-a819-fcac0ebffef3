package com.lijianqiang12.silent.component.activity.analyze.todayusage

import android.os.Bundle
import androidx.activity.viewModels
import androidx.recyclerview.widget.LinearLayoutManager
import com.lijianqiang12.silent.R
import com.lijianqiang12.silent.component.activity.base.BaseActivity
import com.lijianqiang12.silent.data.viewmodel.AnalyzeViewModel
import dagger.hilt.android.AndroidEntryPoint
import kotlinx.android.synthetic.main.activity_today_app_usage.*
import javax.inject.Inject

@AndroidEntryPoint
class TodayAppUsageActivity : BaseActivity() {
    private lateinit var mAdapter: TodayAppUsageAdapter
    private lateinit var mLayoutManager: androidx.recyclerview.widget.RecyclerView.LayoutManager

    @Inject
    lateinit var viewModel: AnalyzeViewModel

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        setContentView(R.layout.activity_today_app_usage)

        var selectAppUsageDays = intent.getIntExtra("selectAppUsageDays", 1)

        iv_return_app_usage.setOnClickListener { finish() }

        mLayoutManager = LinearLayoutManager(this)
        rv_today_app_usage.layoutManager = mLayoutManager
        mAdapter = TodayAppUsageAdapter(0, R.layout.item_today_length, mutableListOf())
        mAdapter.animationEnable = true
        rv_today_app_usage.adapter = mAdapter


        viewModel.appUsageTimeTodayLiveData.observe(this) {
            var longest = 0L
            it.forEach { appTime ->
                longest += appTime.timeLength
            }
            mAdapter.longest = longest
            mAdapter.setNewInstance(it)
            mAdapter.notifyDataSetChanged()
        }

        viewModel.refreshAppUsageTimeToday(selectAppUsageDays)
        if (selectAppUsageDays == 1) {
            rb_day_detail.isChecked = true
        } else {
            rb_week_detail.isChecked = true
        }

        rg_app_usage_time_detail.setOnCheckedChangeListener { _, checkedId ->
            when (checkedId) {
                R.id.rb_day_detail -> {
                    selectAppUsageDays = 1
                    viewModel.refreshAppUsageTimeToday(1)
                }

                R.id.rb_week_detail -> {
                    selectAppUsageDays = 7
                    viewModel.refreshAppUsageTimeToday(7)
                }
            }
        }

    }
}