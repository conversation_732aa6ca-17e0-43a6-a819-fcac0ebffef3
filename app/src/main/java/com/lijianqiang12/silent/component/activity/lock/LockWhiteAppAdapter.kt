
import android.view.View
import android.widget.ImageView
import android.widget.TextView
import androidx.fragment.app.Fragment
import androidx.lifecycle.lifecycleScope
import com.chad.library.adapter.base.BaseQuickAdapter
import com.chad.library.adapter.base.module.LoadMoreModule
import com.chad.library.adapter.base.viewholder.BaseViewHolder
import com.lijianqiang12.silent.R
import com.lijianqiang12.silent.data.model.db.WhiteApp
import com.lijianqiang12.silent.utils.MyUtil
import com.lijianqiang12.silent.utils.getAppIcon
import com.lijianqiang12.silent.utils.secondToSimpleHm
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.launch
import kotlinx.coroutines.withContext

class LockWhiteAppAdapter(val fragment: Fragment, layoutRes: Int, list: MutableList<WhiteApp>) : BaseQuickAdapter<WhiteApp, BaseViewHolder>(layoutRes, list),
    LoadMoreModule {

    override fun convert(holder: BaseViewHolder, item: WhiteApp) {


        fragment.lifecycleScope.launch(Dispatchers.IO) {
            val appIcon = getAppIcon(item.pkg, item.mainActivity)
            withContext(Dispatchers.Main) {
                holder.getView<ImageView>(R.id.iv_app_icon).setImageDrawable(appIcon)

                if (!MyUtil.isVIP() && getItemPosition(item) >= 6) {
                    holder.getView<ImageView>(R.id.iv_app_icon).alpha = 0.3f
                }else{
                    holder.getView<ImageView>(R.id.iv_app_icon).alpha = 1f
                }
            }
        }

        if (item.maxLen == -1) {
            holder.getView<TextView>(R.id.tv_app_limit).visibility = View.GONE
        } else {
            holder.getView<TextView>(R.id.tv_app_limit).visibility = View.VISIBLE
            holder.getView<TextView>(R.id.tv_app_limit).text = secondToSimpleHm(item.maxLen * 60)
        }


    }
}