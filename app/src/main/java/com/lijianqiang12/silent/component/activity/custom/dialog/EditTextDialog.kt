package com.lijianqiang12.silent.component.activity.custom.dialog

import android.graphics.Color
import android.graphics.drawable.ColorDrawable
import android.os.Bundle
import android.util.DisplayMetrics
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.view.WindowManager
import androidx.appcompat.app.AppCompatActivity
import androidx.fragment.app.Fragment
import com.lijianqiang12.silent.DIALOG_WIDTH_PERCENT_MIDDLE
import com.lijianqiang12.silent.R
import com.lijianqiang12.silent.component.activity.base.BaseDialogFragment
import com.lijianqiang12.silent.utils.MyToastUtil
import kotlinx.android.synthetic.main.dialog_edit_text.*
import kotlinx.android.synthetic.main.dialog_edit_text.view.*

class EditTextDialog() : BaseDialogFragment() {

    constructor(fragment: Fragment) : this() {
        this.fragment = fragment
    }

    constructor(activity: AppCompatActivity) : this() {
        this.myActivity = activity
    }

    private var okListener: OnOKListener? = null
    private lateinit var v: View
    private var myActivity: AppCompatActivity? = null
    private var fragment: Fragment? = null
    private var title = "请输入"

    private var canEmpty = false


    override fun onCreateView(inflater: LayoutInflater, container: ViewGroup?, savedInstanceState: Bundle?): View? {

        v = inflater.inflate(R.layout.dialog_edit_text, container, false)

        return v
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        dialog!!.window!!.setBackgroundDrawable(ColorDrawable(Color.TRANSPARENT))

        v.tv_dialog_home_app_title.text = title

        v.tv_dialog_edit_ok.setOnClickListener {

            if (!canEmpty && et_dialog_edit_text.text.isEmpty()) {
                MyToastUtil.showInfo("输入不能为空")
            } else {
                okListener?.onclick(et_dialog_edit_text.text.toString())
                dismiss()
            }
        }
        v.tv_dialog_edit_cancel.setOnClickListener {
            dismiss()
        }
    }

    fun setCanEmpty(canEmpty: Boolean) {
        this.canEmpty = canEmpty
    }

    fun setTitle(title: String) {
        this.title = title
    }

    fun show() {
        fragment?.let {
            super.show(fragment!!.requireFragmentManager(), "TimeSelectDialog")
        }
        this.myActivity?.let {
            super.show(it.supportFragmentManager, "TimeSelectDialog")
        }
    }

    override fun onStart() {
        val params = dialog!!.window!!.attributes
        val dm: DisplayMetrics = resources.displayMetrics
        val width = dm.widthPixels
        params.width = (width * DIALOG_WIDTH_PERCENT_MIDDLE).toInt()//ViewGroup.LayoutParams.MATCH_PARENT
        dialog!!.window!!.attributes = params as WindowManager.LayoutParams
        super.onStart()
    }


    fun setOnOKListener(listener: OnOKListener) {
        this.okListener = listener
    }


    interface OnOKListener {
        fun onclick(text: String)
    }


}