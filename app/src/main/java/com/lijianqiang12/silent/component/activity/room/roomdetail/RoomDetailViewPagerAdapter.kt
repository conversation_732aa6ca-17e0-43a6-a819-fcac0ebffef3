package com.lijianqiang12.silent.component.activity.room.roomdetail

import androidx.fragment.app.Fragment
import androidx.fragment.app.FragmentActivity
import androidx.viewpager2.adapter.FragmentStateAdapter
import com.lijianqiang12.silent.MyConstants
import com.lijianqiang12.silent.utils.MMKVUtils

class RoomDetailViewPagerAdapter(context: FragmentActivity) : FragmentStateAdapter(context) {

    override fun getItemCount(): Int = if (MMKVUtils.getBoolean(MyConstants.SP_KEY_CONFIG_SHOW_BOARD, false)) 4 else 3

    override fun createFragment(position: Int): Fragment {
        return when (position) {
            0 -> RoomItemInfoFragment.newInstance("", "")
            1 -> RoomItemNewsFragment.newInstance("", "")
            2 -> RoomItemMemberFragment.newInstance("", "")
            3 -> RoomItemBoardFragment.newInstance("", "")
            else -> {
                Fragment()
            }
        }
    }
}

