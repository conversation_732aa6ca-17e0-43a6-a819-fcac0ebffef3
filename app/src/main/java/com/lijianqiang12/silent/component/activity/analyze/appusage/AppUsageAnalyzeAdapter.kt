package com.lijianqiang12.silent.component.activity.analyze.appusage

import android.widget.ImageView
import android.widget.TextView
import com.chad.library.adapter.base.BaseQuickAdapter
import com.chad.library.adapter.base.module.LoadMoreModule
import com.chad.library.adapter.base.viewholder.BaseViewHolder
import com.lijianqiang12.silent.R
import com.lijianqiang12.silent.data.viewmodel.AppTimeAnalyze
import com.lijianqiang12.silent.utils.secondToHm

class AppUsageAnalyzeAdapter(layoutRes: Int, item: MutableList<AppTimeAnalyze>)
    : BaseQuickAdapter<AppTimeAnalyze, BaseViewHolder>(layoutRes, item), LoadMoreModule {

    override fun convert(viewHolder: BaseViewHolder, item: AppTimeAnalyze) {

        viewHolder.getView<ImageView>(R.id.iv_analyze_item_icon).setImageDrawable(item.icon)
        viewHolder.getView<TextView>(R.id.tv_analyze_item_appname).text = item.name
        viewHolder.getView<TextView>(R.id.tv_analyze_app_usage_3days).text = "近24小时：${secondToHm(item.timeLengthPer3Days / 1000)}"
        viewHolder.getView<TextView>(R.id.tv_analyze_app_usage_7days).text = "近7天日均：${secondToHm(item.timeLengthPer7Days / 1000)}"

        viewHolder.getView<ImageView>(R.id.iv_analyze_is_up).setImageResource(if (item.isUp) {
            R.drawable.ic_go_up
        } else {
            R.drawable.ic_go_down
        })

        if (item.changeLength < 1000 * 60) {
            viewHolder.getView<TextView>(R.id.tv_analyze_app_usage_change_time).text = "小于1分钟"
        } else {
            viewHolder.getView<TextView>(R.id.tv_analyze_app_usage_change_time).text = secondToHm(item.changeLength / 1000)
        }

//        val tvEmpty = viewHolder.getView<TextView>(R.id.tv_today_length_item_line_empty)
//        val tvLine = viewHolder.getView<TextView>(R.id.tv_today_length_item_line)
//
//        val calendar = Calendar.getInstance()
//        val longest = calendar.get(Calendar.HOUR_OF_DAY) * 1000 * 60 * 60 + calendar.get(Calendar.MINUTE) * 1000 * 60 + calendar.get(Calendar.SECOND) * 1000 + calendar.get(Calendar.MILLISECOND)
//        val lp = tvEmpty.layoutParams
//        (lp as LinearLayout.LayoutParams).weight = (longest.toFloat() - item.timeLength.toFloat()) / item.timeLength.toFloat()
//        tvEmpty.layoutParams = lp
//
//        Palette.from(drawableToBitmap((item.icon)!!)!!).generate {
//            it?.apply {
//                tvLine.setBackgroundResource(R.drawable.shape_item_line1)
//                val myGrad = tvLine.background as GradientDrawable
//                myGrad.setColor(this.dominantSwatch!!.rgb)
//            }
//        }
    }
}