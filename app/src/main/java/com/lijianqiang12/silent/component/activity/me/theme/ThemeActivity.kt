package com.lijianqiang12.silent.component.activity.me.theme

import android.content.res.ColorStateList
import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import androidx.core.view.children
import com.lijianqiang12.silent.utils.MMKVUtils
import com.jeremyliao.liveeventbus.LiveEventBus
import com.lijianqiang12.silent.LiveBus
import com.lijianqiang12.silent.MyConstants
import com.lijianqiang12.silent.R
import com.lijianqiang12.silent.component.activity.base.BaseActivity
import com.lijianqiang12.silent.utils.DialogUtil
import com.lijianqiang12.silent.utils.MyToastUtil
import com.lijianqiang12.silent.utils.MyUtil
import kotlinx.android.synthetic.main.activity_theme.*
import kotlinx.android.synthetic.main.item_theme.view.*

class ThemeActivity : BaseActivity(canReCreate = false) {
    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        setContentView(R.layout.activity_theme)
        iv_return_theme.setOnClickListener { finish() }

        val isVip = MyUtil.isVIP()

        val currentThemeIndex = MMKVUtils.getInt(MyConstants.SP_SETTING_THEME, 0)

        themeAttr.forEachIndexed { index, themeInfo ->
            val view = LayoutInflater.from(this).inflate(R.layout.item_theme, null)
            view.iv_color_example.setBackgroundColor(resources.getColor(themeInfo.colorId))
            view.tv_color_name.text = themeInfo.colorName
            view.tv_theme_describe.text = themeInfo.colorDescribe

            if (index == currentThemeIndex) {
                view.iv_theme_selector.setImageResource(R.drawable.ic_select_yes)
                view.iv_theme_selector.imageTintList = ColorStateList.valueOf(resources.getColor(themeInfo.colorId))
            } else {
                view.iv_theme_selector.setImageResource(R.drawable.ic_select_no)
                view.iv_theme_selector.imageTintList = ColorStateList.valueOf(resources.getColor(R.color.custom_color_app_text_3_default))
            }

            if (isVip || index < 4) {
                view.tv_vip_flag.visibility = View.GONE
                view.setOnClickListener {
                    ll_theme_container.children.forEachIndexed { index2, child ->

                        if (index2 == index) {
                            child.iv_theme_selector.setImageResource(R.drawable.ic_select_yes)
                            child.iv_theme_selector.imageTintList = ColorStateList.valueOf(resources.getColor(themeInfo.colorId))
                        } else {
                            child.iv_theme_selector.setImageResource(R.drawable.ic_select_no)
                            child.iv_theme_selector.imageTintList = ColorStateList.valueOf(resources.getColor(R.color.custom_color_app_text_3_default))
                        }
                    }
                    MMKVUtils.put(MyConstants.SP_SETTING_THEME, index)
                    LiveEventBus.get(LiveBus.CHANGE_THEME, String::class.java).post("")
                    MyToastUtil.showInfo("主题设置成功")
                }
            } else {
                view.tv_vip_flag.visibility = View.VISIBLE
                view.setOnClickListener {


                    DialogUtil.showVIPDialog(this, null, content = "VIP用户可使用全部主题，开通后，即可选择自己喜爱的颜色。", "theme")

                }
            }

            ll_theme_container.addView(view)
        }


    }


}