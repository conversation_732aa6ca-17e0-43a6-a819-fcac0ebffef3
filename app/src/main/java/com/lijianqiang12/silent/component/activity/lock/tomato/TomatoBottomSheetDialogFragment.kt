package com.lijianqiang12.silent.component.activity.lock.tomato

import android.content.Context
import android.content.Intent
import android.os.Bundle
import android.view.Gravity
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.appcompat.app.AppCompatActivity
import androidx.fragment.app.viewModels
import androidx.recyclerview.widget.ItemTouchHelper
import androidx.recyclerview.widget.LinearLayoutManager
import androidx.recyclerview.widget.RecyclerView
import com.afollestad.materialdialogs.MaterialDialog
import com.afollestad.materialdialogs.customview.customView
import com.google.android.material.bottomsheet.BottomSheetBehavior
import com.google.android.material.chip.Chip
import com.lijianqiang12.silent.MyConstants
import com.lijianqiang12.silent.R
import com.lijianqiang12.silent.component.activity.PermissionActivity
import com.lijianqiang12.silent.component.activity.base.BaseBottomSheetDialogFragment
import com.lijianqiang12.silent.component.activity.custom.dialog.NormalDialog
import com.lijianqiang12.silent.component.activity.custom.dialog.OnCancelClickListener
import com.lijianqiang12.silent.component.activity.custom.dialog.OnOKClickListener
import com.lijianqiang12.silent.component.activity.me.vip.FROM_WHERE
import com.lijianqiang12.silent.component.activity.me.vip.VIP2Activity
import com.lijianqiang12.silent.data.model.db.Tomato
import com.lijianqiang12.silent.data.model.db.LockHistory
import com.lijianqiang12.silent.data.model.db.TomatoWithSub
import com.lijianqiang12.silent.data.viewmodel.LockViewModel
import com.lijianqiang12.silent.utils.*
import dagger.hilt.android.AndroidEntryPoint
import kotlinx.android.synthetic.main.bottom_sheet_lock_tomato.view.*
import kotlinx.android.synthetic.main.dialog_lock_tomato.view.*
import kotlinx.android.synthetic.main.dialog_lock_tomato_drag.view.*
import java.util.*
import javax.inject.Inject

@AndroidEntryPoint
class TomatoBottomSheetDialogFragment : BaseBottomSheetDialogFragment() {

    private lateinit var mBehavior: BottomSheetBehavior<View>
    private lateinit var customView: View
    private var tomatoWithSubList: MutableList<TomatoWithSub> = mutableListOf()

    private val viewModel: LockViewModel by viewModels()

    companion object {
        @JvmStatic
        fun newInstance(): TomatoBottomSheetDialogFragment {
            return TomatoBottomSheetDialogFragment()
        }
    }


    override fun onCreateView(inflater: LayoutInflater, container: ViewGroup?, savedInstanceState: Bundle?): View? {
        customView = View.inflate(requireContext(), R.layout.bottom_sheet_lock_tomato, null)
        return customView
    }


    override fun onStart() {
        super.onStart()
        val parentView = customView.parent as View
        parentView.setBackgroundColor(resources.getColor(R.color.colorTranslate))
        mBehavior = BottomSheetBehavior.from(parentView)
        mBehavior.state = BottomSheetBehavior.STATE_EXPANDED


    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        viewModel.tomatoesLiveData.observe(viewLifecycleOwner) {

//            LogUtils.d("observe tomatoWithSubList.whiteList.size = ${it[0].whiteList.size}")

            tomatoWithSubList = it
            customView.cg_lock_tomato.removeAllViews()
            it.forEachIndexed { index, tomatoWithSub ->
                val chip = Chip(requireContext())
                chip.setChipBackgroundColorResource(R.color.colorLockTomatoChipGroup)
                chip.setTextColor(resources.getColor(R.color.colorLockTomatoChip))
                chip.text = tomatoWithSub.tomato.title

                if (index >= 3 && !MyUtil.isVIP()) {
                    chip.text = chip.text.toString() + " (VIP可用)"
                    chip.alpha = 0.3f
                    chip.setOnClickListener {
                        DialogUtil.showVIPDialog(
                            null, this, "VIP用户可使用3个以上番茄锁机，开通后，享受无限锁机设置。", "3ScheduleLimit",
                            title = "VIP已过期",
                            positiveText = "续订VIP",
                            negativeText = "删除该项",
                            onNegativeListener = object : OnCancelClickListener {
                                override fun onclick() {
                                    viewModel.deleteTomatoWithSub(tomatoWithSub)
                                    MyToastUtil.showInfo("删除成功")
                                }
                            },
                        )
                    }
                } else {
                    chip.setOnClickListener {
                        showTomatoDialog(tomatoWithSub)
                    }
                }
//                chip.setOnClickListener {
//                    showTomatoDialog(tomatoWithSub)
//                }

                customView.cg_lock_tomato.addView(chip)
            }


        }

        if (MMKVUtils.getBoolean(MyConstants.SP_KEY_SHOW_TOMATO_EXPLAIN, true)) {
            customView.cl_tomato_explain.visibility = View.VISIBLE
            customView.cl_tomato_explain.setOnClickListener {
                ImageUtil.openVideo(
                    requireActivity() as AppCompatActivity, MMKVUtils.getString(
                        MyConstants.SP_KEY_CONFIG_TOMATO_URL,
                        "https://offphone-video-1252369707.file.myqcloud.com/TomatoExplain.mp4"
                    )
                )
            }
            customView.iv_hide_tomato_explain.setOnClickListener {
                customView.cl_tomato_explain.visibility = View.GONE
                MMKVUtils.put(MyConstants.SP_KEY_SHOW_TOMATO_EXPLAIN, false)
            }
        } else {
            customView.cl_tomato_explain.visibility = View.GONE
        }

        customView.btn_lock_tomato_new.setOnClickListener {
            if ((tomatoWithSubList.size >= 3) && (!MyUtil.isVIP())) {
//                val intent = Intent(requireContext(), VIP2Activity::class.java)
//                intent.putExtra(FROM_WHERE, "3TomatoLimit")
//                startActivity(intent)
//                MyToastUtil.showInfo(requireContext(), "VIP用户可创建3个以上番茄工作法")
                DialogUtil.showVIPDialog(null, this, "VIP用户可创建3个以上番茄锁机，开通后，享受无限锁机设置。", "3TomatoLimit")
            } else {
                val intent = Intent(requireContext(), EditTomatoActivity::class.java)
                startActivity(intent)
            }
        }


        customView.iv_lock_tomato_sort.setOnClickListener {
            val customView = LayoutInflater.from(requireContext()).inflate(R.layout.dialog_lock_tomato_drag, null)
            val dialog = MaterialDialog(requireContext()).customView(R.layout.dialog_lock_tomato_drag, customView, false).cornerRadius(8.0f)

            val mLayoutManager = LinearLayoutManager(requireContext())
            customView.rv_lock_tomato_drag.layoutManager = mLayoutManager
            val mAdapter = TomatoAdapter(R.layout.item_lock_tomato_drag, mutableListOf())
            mAdapter.animationEnable = true
            customView.rv_lock_tomato_drag.adapter = mAdapter
            val itemTouchHelper = ItemTouchHelper(TomatoItemCallback(requireContext(), viewModel))
            itemTouchHelper.attachToRecyclerView(customView.rv_lock_tomato_drag)

            mAdapter.addData(tomatoWithSubList)
            mAdapter.notifyDataSetChanged()
            dialog.show()
        }

        if (MyUtil.isVIP()) {
            customView.tv_vip_flag_tomato.visibility = View.GONE
        } else {
            customView.tv_vip_flag_tomato.visibility = View.VISIBLE
            customView.tv_vip_flag_tomato.setOnClickListener {
//                ActivityUtils.startActivity(VIP2Activity::class.java)
                val intent = Intent(requireContext(), VIP2Activity::class.java)
                intent.putExtra(FROM_WHERE, "3TomatoCard")
                startActivity(intent)
            }

        }
    }

    /**
     * type 0:新增 1:修改
     */
    private fun showTomatoDialog(tomatoWithSub: TomatoWithSub) {

        val customView = LayoutInflater.from(activity).inflate(R.layout.dialog_lock_tomato, null)
        val dialog = MaterialDialog(requireContext()).customView(R.layout.dialog_lock_tomato, customView, false).cornerRadius(8.0f)

        customView!!.et_dialog_lock_tomato_title.text = tomatoWithSub.tomato.title

        val current = System.currentTimeMillis()

        var timeLenTemp = 0L
        for (i in 1..tomatoWithSub!!.tomato.tomatoCount) {
            timeLenTemp += tomatoWithSub!!.tomato.tomatoWorkLength
            if (i < tomatoWithSub!!.tomato.tomatoCount) {
                if (tomatoWithSub!!.tomato.tomatoLongRestPerCount > 0
                    && (i % tomatoWithSub!!.tomato.tomatoLongRestPerCount == 0)
                ) {
                    timeLenTemp += tomatoWithSub!!.tomato.tomatoLongRestLength
                } else {
                    timeLenTemp += tomatoWithSub!!.tomato.tomatoRestLength
                }
            }
        }
        timeLenTemp *= 60 * 1000
        customView.tv_tomato_end_time.text = "预计结束时间：${formatRelativeTime(Calendar.getInstance(), current, current + timeLenTemp)}"


//        var s = ""
//        tomatoWithSub.subTimeList.forEachIndexed { index, subTime ->
//            if (subTime.type == 1) {
//                s += "锁" + subTime.time + "min"
//            } else {
//                s += "休" + subTime.time + "min"
//            }
//            if (index != tomatoWithSub.subTimeList.size - 1) {
//                s += " - "
//            }
//        }
//        customView.tv_dialog_lock_tomato_work_length.text = s

        customView.tv_dialog_lock_tomato_work_length.text = TimeUtil.formatHHMMSimple(tomatoWithSub.tomato.tomatoWorkLength)
        customView.tv_dialog_lock_tomato_work_count.text = "" + tomatoWithSub.tomato.tomatoCount + "个"

        if (tomatoWithSub.tomato.lockConfig.whiteFollowGlobal) {
            customView.tv_white_global.visibility = View.VISIBLE
            customView.iv_global_white_1.setImageDrawable(null)
            customView.iv_global_white_2.setImageDrawable(null)
            customView.iv_global_white_3.setImageDrawable(null)
            customView.iv_global_white_4.setImageDrawable(null)
            customView.iv_global_white_5.setImageDrawable(null)
            customView.iv_global_white_6.setImageDrawable(null)
            customView.iv_global_white_7.setImageDrawable(null)
            customView.iv_global_white_8.setImageDrawable(null)
            customView.iv_global_white_9.setImageDrawable(null)
        } else {
            customView.tv_white_global.visibility = View.GONE
            MyUtil.loadIcon(this, customView.iv_global_white_1, 0, tomatoWithSub.whiteList, 9)
            MyUtil.loadIcon(this, customView.iv_global_white_2, 1, tomatoWithSub.whiteList, 9)
            MyUtil.loadIcon(this, customView.iv_global_white_3, 2, tomatoWithSub.whiteList, 9)
            MyUtil.loadIcon(this, customView.iv_global_white_4, 3, tomatoWithSub.whiteList, 9)
            MyUtil.loadIcon(this, customView.iv_global_white_5, 4, tomatoWithSub.whiteList, 9)
            MyUtil.loadIcon(this, customView.iv_global_white_6, 5, tomatoWithSub.whiteList, 9)
            MyUtil.loadIcon(this, customView.iv_global_white_7, 6, tomatoWithSub.whiteList, 9)
            MyUtil.loadIcon(this, customView.iv_global_white_8, 7, tomatoWithSub.whiteList, 9)
            MyUtil.loadIcon(this, customView.iv_global_white_9, 8, tomatoWithSub.whiteList, 9)
        }


        customView.btn_dialog_lock_tomato_edit.setOnClickListener {
            val intent = Intent(requireContext(), EditTomatoActivity::class.java)
            intent.putExtra("tomatoWithSub", tomatoWithSub)
            startActivity(intent)
            dialog.dismiss()
        }

        customView.btn_dialog_lock_tomato_clone.setOnClickListener {
            if (!MyUtil.isVIP()) {
                if (tomatoWithSubList.size >= 3) {
                    DialogUtil.showVIPDialog(null, this, "VIP用户可创建3个以上番茄锁机，开通后，享受无限锁机设置。", "3TomatoLimit")
                } else {
                    val tomatoWithSubCopy = tomatoWithSub.copy()
                    tomatoWithSubCopy.tomato.title = tomatoWithSubCopy.tomato.title + "（克隆）"
                    viewModel.createTomatoWithSub(tomatoWithSubCopy)
                    dialog.dismiss()
                    MyToastUtil.showSuccess("克隆成功")
                }
            } else {
                val tomatoWithSubCopy = tomatoWithSub.copy()
                tomatoWithSubCopy.tomato.title = tomatoWithSubCopy.tomato.title + "（克隆）"
                viewModel.createTomatoWithSub(tomatoWithSubCopy)
                dialog.dismiss()
                MyToastUtil.showSuccess("克隆成功")
            }
        }

        customView.btn_dialog_lock_tomato_delete.setOnClickListener {
            NormalDialog(this).apply {
                setTitle("警告")
                setContent("确定删除该番茄任务吗？")
                setGravity(Gravity.CENTER)
                setOnNormalOKClickListener("删除", object : OnOKClickListener {
                    override fun onclick() {
                        viewModel.deleteTomatoWithSub(tomatoWithSub)
                        MyToastUtil.showInfo("删除成功")
                        dialog.dismiss()
                    }
                })

                setOnNormalCancelClickListener("取消", object : OnCancelClickListener {
                    override fun onclick() {
                        MyToastUtil.showInfo("取消删除")
                    }
                })

                showDialog()
            }

        }

        customView.btn_dialog_lock_tomato_do.setOnClickListener {

            if (PermissionUtil.hasAllPermission(requireActivity())) {
                if (isLockRunning()) {
                    MyToastUtil.showInfo("有一个锁机任务还在运行中")
                } else {
                    var timeLen = 0L
                    for (i in 1..tomatoWithSub!!.tomato.tomatoCount) {
                        timeLen += tomatoWithSub!!.tomato.tomatoWorkLength
                        if (i < tomatoWithSub!!.tomato.tomatoCount) {
                            if (tomatoWithSub!!.tomato.tomatoLongRestPerCount > 0
                                && (i % tomatoWithSub!!.tomato.tomatoLongRestPerCount == 0)
                            ) {
                                timeLen += tomatoWithSub!!.tomato.tomatoLongRestLength
                            } else {
                                timeLen += tomatoWithSub!!.tomato.tomatoRestLength
                            }
                        }
                    }
                    timeLen *= 60 * 1000
                    val current = System.currentTimeMillis()
                    val lockHistory = LockHistory(
                        0, "番茄锁机：🍅${tomatoWithSub.tomato.title}", "", current, current, timeLen, timeLen, 2,
                        -1, tomatoWithSub.tomato.tomatoIndexId, "", isFinish = false, isForceQuit = false,
                        isSynced = false, isGeneratedCard = false,
                        deleteWhiteAppTemp = "[]"
                    )
                    viewModel.createLockHistory(lockHistory)
                    dialog.dismiss()
                    MyToastUtil.showInfo("锁机即将启动")

//                    requireActivity().finish()
                }
//                }
            } else {
                MyToastUtil.showError("有未授予的权限")
                requireContext().startActivity(Intent(requireContext(), PermissionActivity::class.java))
            }
        }

        dialog.show()
    }


    private class TomatoItemCallback(val context: Context, val viewModel: LockViewModel) : ItemTouchHelper.Callback() {

        override fun getMovementFlags(p0: androidx.recyclerview.widget.RecyclerView, p1: androidx.recyclerview.widget.RecyclerView.ViewHolder): Int {
            return makeMovementFlags(ItemTouchHelper.UP or ItemTouchHelper.DOWN, 0)
        }

        override fun onMove(recycler: RecyclerView, holder1: RecyclerView.ViewHolder, holder2: RecyclerView.ViewHolder): Boolean {

            val fromPosition: Int = holder1.getBindingAdapterPosition()
            val toPosition: Int = holder2.getBindingAdapterPosition()

            if (fromPosition < (recycler.adapter as TomatoAdapter).data.size &&
                toPosition < (recycler.adapter as TomatoAdapter).data.size
            ) {

                if (fromPosition < toPosition) {
                    for (i in fromPosition until toPosition) {
                        swapData(recycler, i, i + 1)
                    }
                } else {
                    for (i in fromPosition downTo toPosition + 1) {
                        swapData(recycler, i, i - 1)
                    }
                }

            }

            return true
        }


        override fun onSwiped(recycler: RecyclerView.ViewHolder, p1: Int) {

        }

        override fun isLongPressDragEnabled(): Boolean {
            return true
        }

        fun swapData(recycler: RecyclerView, position1: Int, position2: Int) {
            Collections.swap((recycler.adapter as TomatoAdapter).data, position1, position2)
            (recycler.adapter as TomatoAdapter).notifyItemMoved(position1, position2)

            swapDataTrendWithMinimalImpact((recycler.adapter as TomatoAdapter).data, position1, position2)
        }

        fun swapDataTrendWithMinimalImpact(dataList: MutableList<TomatoWithSub>, pos1: Int, pos2: Int) {
            if (pos1 !in dataList.indices || pos2 !in dataList.indices) return

            val item1 = dataList[pos1].tomato
            val item2 = dataList[pos2].tomato

            //这里修改pos1的trend，因为拖动的是pos2，这样如果trend都是0，拖一圈就能全改了。
            if (item1.trend == item2.trend) {
                val newTrendForItem1 = generateNearestUniqueTrend(dataList, item1.trend)
                item1.trend = newTrendForItem1
            } else {
                // 如果trend不相等，交换trend值
                item1.trend = item2.trend.also { item2.trend = item1.trend }
            }
            viewModel.updateTomato(item1, sync = false)
            viewModel.updateTomato(item2, sync = false)

        }

        private fun generateNearestUniqueTrend(dataList: List<TomatoWithSub>, baseTrend: Int): Int {
            var newTrend = baseTrend + 1
            while (dataList.any { it.tomato.trend == newTrend }) {
                newTrend += 1
            }
            return newTrend
        }

    }

}