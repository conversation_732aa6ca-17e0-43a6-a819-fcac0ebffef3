package com.lijianqiang12.silent.component.activity.analyze.todaytrend

import android.os.Bundle
import androidx.activity.viewModels
import androidx.lifecycle.Observer
import androidx.recyclerview.widget.LinearLayoutManager
import com.lijianqiang12.silent.R
import com.lijianqiang12.silent.component.activity.base.BaseActivity
import com.lijianqiang12.silent.data.viewmodel.AnalyzeViewModel
import com.lijianqiang12.silent.data.viewmodel.InjectorUtils
import kotlinx.android.synthetic.main.activity_today_lock_trend.*

class TodayLockTrendActivity : BaseActivity() {
    private lateinit var mAdapter: TodayLockTrendAdapter
    private lateinit var mLayoutManager: androidx.recyclerview.widget.RecyclerView.LayoutManager

    private val viewModel: AnalyzeViewModel by viewModels {
        InjectorUtils.provideAnalyzeViewModelFactory(this)
    }

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        setContentView(R.layout.activity_today_lock_trend)

        iv_return_today_lock_trend.setOnClickListener { finish() }

        mLayoutManager = LinearLayoutManager(this)
        rv_today_lock_trend.layoutManager = mLayoutManager
        mAdapter = TodayLockTrendAdapter( R.layout.item_analyze_today, mutableListOf())
        mAdapter.animationEnable = true
        rv_today_lock_trend.adapter = mAdapter

//        mAdapter.setOnItemClickListener { adapter, view, position ->
//        }

        viewModel.todayTrendLiveData.observe(this, Observer {
            mAdapter.setNewInstance(it)
            mAdapter.notifyDataSetChanged()
            srl_today_lock_trend.isRefreshing = false
        })

        srl_today_lock_trend.setOnRefreshListener {
            viewModel.refreshTodayTrend()
        }
        srl_today_lock_trend.isRefreshing = true
        viewModel.refreshTodayTrend()
    }
}