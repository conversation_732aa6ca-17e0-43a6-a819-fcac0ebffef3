package com.lijianqiang12.silent.component.activity.lock.schedule

import TomatoWhiteAppAdapter
import android.annotation.SuppressLint
import android.os.Bundle
import android.view.Gravity
import android.view.LayoutInflater
import android.view.View
import android.widget.Toast
import androidx.activity.viewModels
import androidx.core.widget.NestedScrollView
import androidx.lifecycle.lifecycleScope
import androidx.recyclerview.widget.DiffUtil
import androidx.recyclerview.widget.GridLayoutManager
import androidx.recyclerview.widget.LinearLayoutManager
import androidx.recyclerview.widget.RecyclerView
import com.afollestad.materialdialogs.MaterialDialog
import com.afollestad.materialdialogs.customview.customView
import com.blankj.utilcode.util.LogUtils
import com.bumptech.glide.Glide
import com.bumptech.glide.load.engine.DiskCacheStrategy
import com.lijianqiang12.silent.*
import com.lijianqiang12.silent.component.activity.base.BaseActivity
import com.lijianqiang12.silent.component.activity.custom.dialog.*
import com.lijianqiang12.silent.component.activity.lock.BgBottomSheetDialogFragment
import com.lijianqiang12.silent.component.activity.lock.tomato.TomatoAdapter
import com.lijianqiang12.silent.component.activity.lock.whiteapp.TomatoWhiteBottomSheetDialogFragment
import com.lijianqiang12.silent.data.model.db.LockConfig
import com.lijianqiang12.silent.data.model.db.Schedule
import com.lijianqiang12.silent.data.model.db.ScheduleWithSub
import com.lijianqiang12.silent.data.model.db.TomatoWithSub
import com.lijianqiang12.silent.data.model.db.WhiteApp
import com.lijianqiang12.silent.data.viewmodel.InjectorUtils
import com.lijianqiang12.silent.data.viewmodel.LockViewModel
import com.lijianqiang12.silent.utils.*
import com.xw.repo.BubbleSeekBar
import kotlinx.android.synthetic.main.activity_edit_schedule.*
import kotlinx.android.synthetic.main.activity_edit_schedule.layout_lock_config
import kotlinx.android.synthetic.main.dialog_lock_tomato_drag.view.*
import kotlinx.android.synthetic.main.include_lock_config.*
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.launch
import kotlinx.coroutines.withContext


class EditScheduleActivity : BaseActivity() {

    private lateinit var schedule: Schedule
    private lateinit var lockConfig: LockConfig
    private lateinit var scheduleWithSub: ScheduleWithSub
    private var saveMode = SAVE_MODE_CREATE
    private var changed = false
    private var item = -1
    private var oldWhiteList: MutableList<WhiteApp> = mutableListOf()

    private lateinit var recyclerview: RecyclerView
    private val viewModel: LockViewModel by viewModels {
        InjectorUtils.provideLockViewModelFactory(this)
    }

    @SuppressLint("CheckResult")
    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        setContentView(R.layout.activity_edit_schedule)


        if (null == intent.getParcelableExtra<ScheduleWithSub>("scheduleWithSub")) {
            saveMode = SAVE_MODE_CREATE
            schedule = Schedule()
            lockConfig = schedule.lockConfig
            scheduleWithSub = ScheduleWithSub(null, mutableListOf(), schedule)
        } else {
            saveMode = SAVE_MODE_UPDATE
            scheduleWithSub = intent.getParcelableExtra<ScheduleWithSub>("scheduleWithSub")!!
            schedule = scheduleWithSub.schedule
            lockConfig = schedule.lockConfig
        }

        LogUtils.d("begin scheduleWithSub.whiteList.size = ${scheduleWithSub.whiteList.size}")


        /*初始化显示*/
        //任务名
        val tvScheduleTitle = et_schedule_title
        tvScheduleTitle.text = schedule.title.ifEmpty {
            "未命名"
        }
//        LogUtils.d("!!!!!!!!!!!!1"+schedule.title)
        tvScheduleTitle.setOnClickListener {
            val dialog = EditTextDialog(this)
            dialog.run {
                setCanEmpty(true)
                setOnOKListener(object : EditTextDialog.OnOKListener {
                    override fun onclick(text: String) {
                        changed = true
                        schedule.title = text
                        tvScheduleTitle.text = text.ifEmpty {
                            "未命名"
                        }
                    }
                })
                show()
            }
        }

        //开始时间
        val startTimeView = tv_schedule_start_time
        startTimeView.text = TimeUtil.formatHHMM(schedule.startHour, schedule.startMinute)
        startTimeView.setOnClickListener {
            TimeSelectDialog(this).run {
                setHour(schedule.startHour)
                setMinute(schedule.startMinute)
                setOnOKListener(object : TimeSelectDialog.OnOKListener {
                    override fun onclick(length: Int) {
                        schedule.startHour = length / 60
                        schedule.startMinute = length % 60
                        startTimeView.text = TimeUtil.formatHHMM(schedule.startHour, schedule.startMinute)
                        changed = true
                    }

                })
                show()
            }
        }

        //结束时间
        val endTimeView = tv_schedule_end_time
        var relativeText = ""
        relativeText = if (schedule.startHour * 60 + schedule.startMinute >= schedule.endHour * 60 + schedule.endMinute) {
            "次日"
        } else {
            ""
//            "当日"
        }
        endTimeView.text = relativeText + TimeUtil.formatHHMM(schedule.endHour, schedule.endMinute)
        endTimeView.setOnClickListener {
            TimeSelectDialog(this).run {
                setHour(schedule.endHour)
                setMinute(schedule.endMinute)
                setOnOKListener(object : TimeSelectDialog.OnOKListener {
                    override fun onclick(length: Int) {
                        schedule.endHour = length / 60
                        schedule.endMinute = length % 60
                        relativeText = if (schedule.startHour * 60 + schedule.startMinute >= schedule.endHour * 60 + schedule.endMinute) {
                            "次日"
                        } else {
                            ""
//                            "当日"
                        }
                        endTimeView.text = relativeText + TimeUtil.formatHHMM(schedule.endHour, schedule.endMinute)
                        changed = true
                    }
                })
                show()
            }
        }

        //是否使用番茄
        if (schedule.useTomato) {
            rb_2.isChecked = true

            textView34.visibility = View.GONE
            tv_schedule_end_time.visibility = View.GONE
            layout_lock_config.visibility = View.GONE

            textView83.visibility = View.VISIBLE
            tv_schedule_tomato_name.visibility = View.VISIBLE
        } else {
            rb_1.isChecked = true

            textView34.visibility = View.VISIBLE
            tv_schedule_end_time.visibility = View.VISIBLE
            layout_lock_config.visibility = View.VISIBLE

            textView83.visibility = View.GONE
            tv_schedule_tomato_name.visibility = View.GONE
        }
        rg_schedule_end.setOnCheckedChangeListener { group, checkedId ->
            changed = true
            when (checkedId) {
                R.id.rb_1 -> {
                    schedule.useTomato = false

                    textView34.visibility = View.VISIBLE
                    tv_schedule_end_time.visibility = View.VISIBLE
                    layout_lock_config.visibility = View.VISIBLE

                    textView83.visibility = View.GONE
                    tv_schedule_tomato_name.visibility = View.GONE
                }

                R.id.rb_2 -> {
                    schedule.useTomato = true

                    textView34.visibility = View.GONE
                    tv_schedule_end_time.visibility = View.GONE
                    layout_lock_config.visibility = View.GONE

                    textView83.visibility = View.VISIBLE
                    tv_schedule_tomato_name.visibility = View.VISIBLE
                }

            }
        }

        //番茄选择
        viewModel.tomatoesLiveData.observe(this) {
            if (schedule.tomatoIndexId == "") {
                tv_schedule_tomato_name.text = "未选择"
            } else {
                var str = "已删除"
                for (tomatoWithWub in it) {
                    if (tomatoWithWub.tomato.tomatoIndexId == schedule.tomatoIndexId) {
                        str = tomatoWithWub.tomato.title
                    }
                }
                tv_schedule_tomato_name.text = str
            }

            tv_schedule_tomato_name.setOnClickListener { _ ->
                val customView = LayoutInflater.from(this).inflate(R.layout.dialog_lock_tomato_drag, null)
                customView.textView211.text = "选择要定时运行的番茄任务"
                val dialog = MaterialDialog(this).customView(R.layout.dialog_lock_tomato_drag, customView, false).cornerRadius(8.0f)

                val mLayoutManager = LinearLayoutManager(this)
                customView.rv_lock_tomato_drag.layoutManager = mLayoutManager
                val mAdapter = TomatoAdapter(R.layout.item_lock_tomato_drag, mutableListOf())
                mAdapter.animationEnable = true
                customView.rv_lock_tomato_drag.adapter = mAdapter
                mAdapter.setOnItemClickListener { adapter, view, position ->
                    schedule.tomatoIndexId = (adapter.data[position] as TomatoWithSub).tomato.tomatoIndexId
                    tv_schedule_tomato_name.text = (adapter.data[position] as TomatoWithSub).tomato.title
                    changed = true
                    dialog.dismiss()
                }

                mAdapter.addData(it)
                mAdapter.notifyDataSetChanged()
                dialog.show()
            }
        }


//        refreshWhiteListView()


        //是否重复
        if (schedule.isRecycle) {
            rb_recycle_1.isChecked = true
            rb_recycle_2.isChecked = false
            weeks.visibility = View.VISIBLE
            textView84.visibility = View.GONE
        } else {
            rb_recycle_1.isChecked = false
            rb_recycle_2.isChecked = true
            weeks.visibility = View.GONE
            textView84.visibility = View.VISIBLE
        }
        rg_schedule_recycle.setOnCheckedChangeListener { group, checkedId ->
            changed = true
            when (checkedId) {
                R.id.rb_recycle_1 -> {
                    schedule.isRecycle = true
                    weeks.visibility = View.VISIBLE
                    textView84.visibility = View.GONE
                }

                R.id.rb_recycle_2 -> {
                    schedule.isRecycle = false
                    weeks.visibility = View.GONE
                    textView84.visibility = View.VISIBLE
                }
            }
        }

        //按星期循环
        if (schedule.monday) {
            tv_week_select_1.setBackgroundResource(R.drawable.bg_week_select)
        } else {
            tv_week_select_1.setBackgroundResource(R.drawable.bg_week_not_select)
        }
        if (schedule.tuesday) {
            tv_week_select_2.setBackgroundResource(R.drawable.bg_week_select)
        } else {
            tv_week_select_2.setBackgroundResource(R.drawable.bg_week_not_select)
        }
        if (schedule.wednesday) {
            tv_week_select_3.setBackgroundResource(R.drawable.bg_week_select)
        } else {
            tv_week_select_3.setBackgroundResource(R.drawable.bg_week_not_select)
        }
        if (schedule.thursday) {
            tv_week_select_4.setBackgroundResource(R.drawable.bg_week_select)
        } else {
            tv_week_select_4.setBackgroundResource(R.drawable.bg_week_not_select)
        }
        if (schedule.friday) {
            tv_week_select_5.setBackgroundResource(R.drawable.bg_week_select)
        } else {
            tv_week_select_5.setBackgroundResource(R.drawable.bg_week_not_select)
        }
        if (schedule.saturday) {
            tv_week_select_6.setBackgroundResource(R.drawable.bg_week_select)
        } else {
            tv_week_select_6.setBackgroundResource(R.drawable.bg_week_not_select)
        }
        if (schedule.sunday) {
            tv_week_select_7.setBackgroundResource(R.drawable.bg_week_select)
        } else {
            tv_week_select_7.setBackgroundResource(R.drawable.bg_week_not_select)
        }
        tv_week_select_1.setOnClickListener {
            changed = true
            schedule.monday = !schedule.monday
            if (schedule.monday) {
                tv_week_select_1.setBackgroundResource(R.drawable.bg_week_select)
            } else {
                tv_week_select_1.setBackgroundResource(R.drawable.bg_week_not_select)
            }
        }
        tv_week_select_2.setOnClickListener {
            changed = true
            schedule.tuesday = !schedule.tuesday
            if (schedule.tuesday) {
                tv_week_select_2.setBackgroundResource(R.drawable.bg_week_select)
            } else {
                tv_week_select_2.setBackgroundResource(R.drawable.bg_week_not_select)
            }
        }
        tv_week_select_3.setOnClickListener {
            changed = true
            schedule.wednesday = !schedule.wednesday
            if (schedule.wednesday) {
                tv_week_select_3.setBackgroundResource(R.drawable.bg_week_select)
            } else {
                tv_week_select_3.setBackgroundResource(R.drawable.bg_week_not_select)
            }
        }
        tv_week_select_4.setOnClickListener {
            changed = true
            schedule.thursday = !schedule.thursday
            if (schedule.thursday) {
                tv_week_select_4.setBackgroundResource(R.drawable.bg_week_select)
            } else {
                tv_week_select_4.setBackgroundResource(R.drawable.bg_week_not_select)
            }
        }
        tv_week_select_5.setOnClickListener {
            changed = true
            schedule.friday = !schedule.friday
            if (schedule.friday) {
                tv_week_select_5.setBackgroundResource(R.drawable.bg_week_select)
            } else {
                tv_week_select_5.setBackgroundResource(R.drawable.bg_week_not_select)
            }
        }
        tv_week_select_6.setOnClickListener {
            changed = true
            schedule.saturday = !schedule.saturday
            if (schedule.saturday) {
                tv_week_select_6.setBackgroundResource(R.drawable.bg_week_select)
            } else {
                tv_week_select_6.setBackgroundResource(R.drawable.bg_week_not_select)
            }
        }
        tv_week_select_7.setOnClickListener {
            changed = true
            schedule.sunday = !schedule.sunday
            if (schedule.sunday) {
                tv_week_select_7.setBackgroundResource(R.drawable.bg_week_select)
            } else {
                tv_week_select_7.setBackgroundResource(R.drawable.bg_week_not_select)
            }
        }


        //是否激活
        if (schedule.validate) {
            rb_active_1.isChecked = true
            rb_active_2.isChecked = false
        } else {
            rb_active_1.isChecked = false
            rb_active_2.isChecked = true
        }
        rg_schedule_active.setOnCheckedChangeListener { group, checkedId ->
            changed = true
            when (checkedId) {
                R.id.rb_active_1 -> {
                    schedule.validate = true
                }

                R.id.rb_active_2 -> {
                    schedule.validate = false
                }
            }
        }

        //禁止更改时长
        tv_schedule_deny_length.text = "${schedule.denyChangeLength}分钟"
        tv_schedule_deny_length.setOnClickListener {
            val bottomDialog = BottomSingleSelectDialogFragment.newInstance()
            bottomDialog.setShowList(arrayListOf("5分钟", "10分钟", "20分钟", "30分钟", "45分钟", "60分钟", "120分钟", "180分钟", "240分钟", "300分钟", "600分钟"))
            bottomDialog.setValueList(arrayListOf(5, 10, 20, 30, 45, 60, 120, 180, 240, 300, 600))
            bottomDialog.setOnValueSelectListener(object : BottomSingleSelectDialogFragment.OnValueSelectListener {
                override fun onSelect(value: Long, show: String) {
                    schedule.denyChangeLength = value.toInt()
                    tv_schedule_deny_length.text = show
                }
            })
            bottomDialog.show(supportFragmentManager, "")
        }

        //是否一小时内禁止修改
        if (schedule.isDenyChange) {
            rb_deny_change_1.isChecked = true
            rb_deny_change_2.isChecked = false
        } else {
            rb_deny_change_1.isChecked = false
            rb_deny_change_2.isChecked = true
        }
        rg_schedule_deny_change.setOnCheckedChangeListener { group, checkedId ->
            changed = true
            when (checkedId) {
                R.id.rb_deny_change_1 -> {
                    schedule.isDenyChange = true
                }

                R.id.rb_deny_change_2 -> {
                    schedule.isDenyChange = false
                }
            }
        }


        //是否使用全局白名单
        if (lockConfig.whiteFollowGlobal) {
            rb_tomato_white_1.isChecked = true
            materialCardView1000.visibility = View.GONE
        } else {
            rb_tomato_white_2.isChecked = true
            materialCardView1000.visibility = View.VISIBLE
        }
        rg_tomato_white.setOnCheckedChangeListener { group, checkedId ->
            changed = true
            when (checkedId) {
                R.id.rb_tomato_white_1 -> {
                    lockConfig.whiteFollowGlobal = true
                    materialCardView1000.visibility = View.GONE
                }

                R.id.rb_tomato_white_2 -> {
                    if (MyUtil.isVIP()) {
                        lockConfig.whiteFollowGlobal = false
                        materialCardView1000.visibility = View.VISIBLE
                    } else {
                        rb_tomato_white_1.isChecked = true
//                        val intent = Intent(this, VIP2Activity::class.java)
//                        intent.putExtra(FROM_WHERE, "EditScheduleActivity_VIP可使用独立设置_白名单")
//                        startActivity(intent)
//                        MyToastUtil.showInfo(applicationContext, "VIP用户可使用独立设置")
                        DialogUtil.showVIPDialog(
                            this,
                            null,
                            "VIP用户可使用独立白名单，开通后，即可在不同的锁机任务使用不同的软件。",
                            "EditScheduleActivity_VIP可使用独立设置_白名单"
                        )

                    }
                }
            }
        }

        //白名单点击响应及白名单填充
        cl_edit_tomato_white_click.setOnClickListener {
            if (MyUtil.isVIP()) {
                val tomatoWhiteBottomSheetDialogFragment = TomatoWhiteBottomSheetDialogFragment(scheduleWithSub.whiteList)
                tomatoWhiteBottomSheetDialogFragment.run {
                    setOnDataChangedListener(object : TomatoWhiteBottomSheetDialogFragment.OnDataChangedListener {
                        override fun onDataChanged() {

//                        LogUtils.d("change scheduleWithSub.whiteList.size = ${scheduleWithSub.whiteList.size}")
                            changed = true
                            refreshWhiteListView()
                        }

                    })
                    show(supportFragmentManager, "ScheduleWhiteBottomSheetDialogFragment")
                }
            } else {
                DialogUtil.showVIPDialog(
                    this,
                    null,
                    "VIP用户可使用独立设置白名单，开通后，即可在不同的锁机任务使用不同的软件。",
                    "EditScheduleActivity_VIP可使用独立设置_白名单"
                )

            }

        }

        refreshWhiteListView()

        //背景图片
        if (lockConfig.bgUrlFollowGlobal) {
            rb_bg_1.isChecked = true
            materialCardView10000.visibility = View.GONE
        } else {
            rb_bg_2.isChecked = true
            materialCardView10000.visibility = View.VISIBLE
        }
        rg_bg.setOnCheckedChangeListener { group, checkedId ->
            changed = true
            when (checkedId) {
                R.id.rb_bg_1 -> {
                    lockConfig.bgUrlFollowGlobal = true
                    materialCardView10000.visibility = View.GONE
                }

                R.id.rb_bg_2 -> {
                    if (MyUtil.isVIP()) {
                        lockConfig.bgUrlFollowGlobal = false
                        materialCardView10000.visibility = View.VISIBLE
                    } else {
                        rb_bg_1.isChecked = true
//                        val intent = Intent(this, VIP2Activity::class.java)
//                        intent.putExtra(FROM_WHERE, "EditScheduleActivity_VIP可使用独立设置_背景")
//                        startActivity(intent)
//                        MyToastUtil.showInfo(applicationContext, "VIP用户可使用独立设置")
                        DialogUtil.showVIPDialog(
                            this,
                            null,
                            "VIP用户可使用独立锁机背景，开通后，即可在不同的锁机任务使用不同的背景图片。",
                            "EditScheduleActivity_VIP可使用独立设置_背景"
                        )

                    }
                }
            }
        }

        val glEditBg = gl_edit_bg
        if (lockConfig.bgUrl.isEmpty()) {
            lockConfig.bgUrl = DEFAULT_LOCK_BG
        }
        Glide.with(this@EditScheduleActivity).load(lockConfig.bgUrl)
            //.transition(DrawableTransitionOptions.withCrossFade())
            .diskCacheStrategy(DiskCacheStrategy.AUTOMATIC)
            .into(glEditBg)
        cl_edit_bg.setOnClickListener {
            BgBottomSheetDialogFragment.newInstance(lockConfig.bgUrl, "定时锁机背景图片").apply {
                setOnBgSelectListener(object : BgBottomSheetDialogFragment.OnBgSelectListener {
                    override fun onSelect(imgUrl: String) {
                        lockConfig.bgUrl = imgUrl
                        Glide.with(this@EditScheduleActivity).load(lockConfig.bgUrl)
                            //.transition(DrawableTransitionOptions.withCrossFade())
                            .diskCacheStrategy(DiskCacheStrategy.AUTOMATIC)
                            .into(glEditBg)
                    }
                })
                show(supportFragmentManager, "BgBottomSheetDialogFragment")
            }
        }


        //开始提示音
        if (lockConfig.startVoiceNotifyFollowGlobal) {
            rb_voice_start_1.isChecked = true
            cl_voice_start.visibility = View.GONE
        } else {
            rb_voice_start_2.isChecked = true
            cl_voice_start.visibility = View.VISIBLE
        }
        rg_voice_start.setOnCheckedChangeListener { group, checkedId ->
            changed = true
            when (checkedId) {
                R.id.rb_voice_start_1 -> {
                    lockConfig.startVoiceNotifyFollowGlobal = true
                    cl_voice_start.visibility = View.GONE
                }

                R.id.rb_voice_start_2 -> {
                    if (MyUtil.isVIP()) {
                        lockConfig.startVoiceNotifyFollowGlobal = false
                        cl_voice_start.visibility = View.VISIBLE
                    } else {
                        rb_voice_start_1.isChecked = true
//                        val intent = Intent(this, VIP2Activity::class.java)
//                        intent.putExtra(FROM_WHERE, "EditScheduleActivity_VIP可使用独立设置_开始音乐")
//                        startActivity(intent)
//                        MyToastUtil.showInfo(applicationContext, "VIP用户可使用独立设置")
                        DialogUtil.showVIPDialog(
                            this,
                            null,
                            "VIP用户可使用独立提示音，开通后，即可在不同的锁机任务使用不同的提示音。",
                            "EditScheduleActivity_VIP可使用独立设置_开始音乐"
                        )

                    }

                }
            }
        }

        val tvVoiceStart = tv_voice_start
        tvVoiceStart.text = RING_NAME_ARRAY[lockConfig.startVoiceNotify]
        cl_voice_start.setOnClickListener {
            RingChooseDialog(this).apply {
                setIndex(lockConfig.startVoiceNotify)
                setOnRingSelectClickListener(object : RingChooseDialog.OnRingSelectListener {
                    override fun onSelect(index: Int) {
                        lockConfig.startVoiceNotify = index
                        tvVoiceStart.text = RING_NAME_ARRAY[lockConfig.startVoiceNotify]
                    }
                })
                show()
            }
        }


        //结束提示音
        if (lockConfig.endVoiceNotifyFollowGlobal) {
            rb_voice_end_1.isChecked = true
            cl_voice_end.visibility = View.GONE
        } else {
            rb_voice_end_2.isChecked = true
            cl_voice_end.visibility = View.VISIBLE
        }
        rg_voice_end.setOnCheckedChangeListener { group, checkedId ->
            changed = true
            when (checkedId) {
                R.id.rb_voice_end_1 -> {
                    lockConfig.endVoiceNotifyFollowGlobal = true
                    cl_voice_end.visibility = View.GONE
                }

                R.id.rb_voice_end_2 -> {
                    if (MyUtil.isVIP()) {
                        lockConfig.endVoiceNotifyFollowGlobal = false
                        cl_voice_end.visibility = View.VISIBLE
                    } else {
                        rb_voice_end_1.isChecked = true
//                        val intent = Intent(this, VIP2Activity::class.java)
//                        intent.putExtra(FROM_WHERE, "EditScheduleActivity_VIP可使用独立设置_结束音乐")
//                        startActivity(intent)
//                        MyToastUtil.showInfo(applicationContext, "VIP用户可使用独立设置")
                        DialogUtil.showVIPDialog(
                            this,
                            null,
                            "VIP用户可使用独立提示音，开通后，即可在不同的锁机任务使用不同的提示音。",
                            "EditScheduleActivity_VIP可使用独立设置_结束音乐"
                        )

                    }

                }
            }
        }
        val tvVoiceEnd = tv_voice_end
        tvVoiceEnd.text = RING_NAME_ARRAY[lockConfig.endVoiceNotify]
        cl_voice_end.setOnClickListener {
            RingChooseDialog(this).apply {
                setIndex(lockConfig.endVoiceNotify)
                setOnRingSelectClickListener(object : RingChooseDialog.OnRingSelectListener {
                    override fun onSelect(index: Int) {
                        lockConfig.endVoiceNotify = index
                        tvVoiceEnd.text = RING_NAME_ARRAY[lockConfig.endVoiceNotify]
                    }
                })
                show()
            }
        }


        //开始振动
        if (lockConfig.startShakeNotifyFollowGlobal) {
            rb_shake_start_1.isChecked = true
            sb_edit_shake_start.visibility = View.GONE
        } else {
            rb_shake_start_2.isChecked = true
            sb_edit_shake_start.visibility = View.VISIBLE
        }
        rg_shake_start.setOnCheckedChangeListener { group, checkedId ->
            changed = true
            when (checkedId) {
                R.id.rb_shake_start_1 -> {
                    lockConfig.startShakeNotifyFollowGlobal = true
                    sb_edit_shake_start.visibility = View.GONE
                }

                R.id.rb_shake_start_2 -> {
                    if (MyUtil.isVIP()) {
                        lockConfig.startShakeNotifyFollowGlobal = false
                        sb_edit_shake_start.visibility = View.VISIBLE
                    } else {
                        rb_shake_start_1.isChecked = true
//                        val intent = Intent(this, VIP2Activity::class.java)
//                        intent.putExtra(FROM_WHERE, "EditScheduleActivity_VIP可使用独立设置_开始震动")
//                        startActivity(intent)
//                        MyToastUtil.showInfo(applicationContext, "VIP用户可使用独立设置")
                        DialogUtil.showVIPDialog(
                            this,
                            null,
                            "VIP用户可使用独立震动强度，开通后，即可在不同的锁机任务使用不同的震动强度。",
                            "EditScheduleActivity_VIP可使用独立设置_开始震动"
                        )

                    }

                }
            }
        }

        sb_edit_shake_start.setCustomSectionTextArray { sectionCount, array ->
            array.clear()
            array.put(0, "不振动")
            array.put(1, "振1s")
            array.put(2, "振2s")
            array.put(3, "振3s")
            array.put(4, "振4s")
            array.put(5, "振5s")
            array
        }
        sb_edit_shake_start.setProgress(lockConfig.startShakeNotify / 1000f)
        sb_edit_shake_start.onProgressChangedListener = object : BubbleSeekBar.OnProgressChangedListenerAdapter() {
            override fun onProgressChanged(bubbleSeekBar: BubbleSeekBar?, progress: Int, progressFloat: Float, fromUser: Boolean) {
                super.onProgressChanged(bubbleSeekBar, progress, progressFloat, fromUser)
                lockConfig.startShakeNotify = (progressFloat * 1000).toLong()
            }
        }


        //结束振动
        if (lockConfig.endShakeNotifyFollowGlobal) {
            rb_shake_end_1.isChecked = true
            sb_edit_shake_end.visibility = View.GONE
        } else {
            rb_shake_end_2.isChecked = true
            sb_edit_shake_end.visibility = View.VISIBLE
        }
        rg_shake_end.setOnCheckedChangeListener { group, checkedId ->
            changed = true
            when (checkedId) {
                R.id.rb_shake_end_1 -> {
                    lockConfig.endShakeNotifyFollowGlobal = true
                    sb_edit_shake_end.visibility = View.GONE
                }

                R.id.rb_shake_end_2 -> {
                    if (MyUtil.isVIP()) {
                        lockConfig.endShakeNotifyFollowGlobal = false
                        sb_edit_shake_end.visibility = View.VISIBLE
                    } else {
                        rb_shake_end_1.isChecked = true
//                        val intent = Intent(this, VIP2Activity::class.java)
//                        intent.putExtra(FROM_WHERE, "EditScheduleActivity_VIP可使用独立设置_结束震动")
//                        startActivity(intent)
//                        MyToastUtil.showInfo(applicationContext, "VIP用户可使用独立设置")
                        DialogUtil.showVIPDialog(
                            this,
                            null,
                            "VIP用户可使用独立震动强度，开通后，即可在不同的锁机任务使用不同的震动强度。",
                            "EditScheduleActivity_VIP可使用独立设置_结束震动"
                        )

                    }

                }
            }
        }
        sb_edit_shake_end.setCustomSectionTextArray { sectionCount, array ->
            array.clear()
            array.put(0, "不振动")
            array.put(1, "振1s")
            array.put(2, "振2s")
            array.put(3, "振3s")
            array.put(4, "振4s")
            array.put(5, "振5s")
            array
        }
        sb_edit_shake_end.setProgress(lockConfig.endShakeNotify / 1000f)
        sb_edit_shake_end.onProgressChangedListener = object : BubbleSeekBar.OnProgressChangedListenerAdapter() {
            override fun onProgressChanged(bubbleSeekBar: BubbleSeekBar?, progress: Int, progressFloat: Float, fromUser: Boolean) {
                super.onProgressChanged(bubbleSeekBar, progress, progressFloat, fromUser)
                lockConfig.endShakeNotify = (progressFloat * 1000).toLong()
            }
        }


        //屏蔽通知

        rg_tomato_notification.setOnCheckedChangeListener { group, checkedId ->
            changed = true
            when (checkedId) {
                R.id.rb_notification_0 -> {
                    scheduleWithSub.schedule.lockConfig.isRemoveNotificationFollowGlobal = true
                }

                R.id.rb_notification_1 -> {
                    if (MyUtil.isVIP()) {
                        if (PermissionUtil.isNotificationListenersEnabled(this)) {
                            scheduleWithSub.schedule.lockConfig.isRemoveNotificationFollowGlobal = false
                            scheduleWithSub.schedule.lockConfig.isRemoveNotification = true
                        } else {
                            rb_notification_0.isChecked = true
                            NormalDialog(this).run {
                                setTitle("缺少权限")
                                setContent("需要您授予“通知权限”，点击下方“确定”，跳转权限授予页面")
                                setOnNormalOKClickListener(object : OnOKClickListener {
                                    override fun onclick() {
                                        PermissionUtil.gotoNotificationAccessSetting(this@EditScheduleActivity)
                                    }
                                })
                                showDialog()
                            }
                        }
                    } else {
                        rb_notification_0.isChecked = true
//                        val intent = Intent(this, VIP2Activity::class.java)
//                        intent.putExtra(FROM_WHERE, "EditScheduleActivity_VIP可使用独立设置_屏蔽通知")
//                        startActivity(intent)
//                        MyToastUtil.showInfo(applicationContext, "VIP用户可使用独立设置")
                        DialogUtil.showVIPDialog(
                            this,
                            null,
                            "VIP用户可使用独立屏蔽通知设置，开通后，即可为不同的锁机任务分别设置是否屏蔽通知。",
                            "EditScheduleActivity_VIP可使用独立设置_屏蔽通知"
                        )

                    }

                }

                R.id.rb_notification_2 -> {
                    if (MyUtil.isVIP()) {
                        scheduleWithSub.schedule.lockConfig.isRemoveNotificationFollowGlobal = false
                        scheduleWithSub.schedule.lockConfig.isRemoveNotification = false
                    } else {
                        rb_notification_0.isChecked = true
//                        val intent = Intent(this, VIP2Activity::class.java)
//                        intent.putExtra(FROM_WHERE, "EditScheduleActivity_VIP可使用独立设置_屏蔽通知")
//                        startActivity(intent)
//                        MyToastUtil.showInfo(applicationContext, "VIP用户可使用独立设置")
                        DialogUtil.showVIPDialog(
                            this,
                            null,
                            "VIP用户可使用独立屏蔽通知设置，开通后，即可为不同的锁机任务分别设置是否屏蔽通知。",
                            "EditScheduleActivity_VIP可使用独立设置_屏蔽通知"
                        )

                    }
                }
            }
        }

        //静音

        rg_tomato_silent.setOnCheckedChangeListener { group, checkedId ->
            changed = true
            when (checkedId) {
                R.id.rb_silent_0 -> {
                    scheduleWithSub.schedule.lockConfig.isSilentFollowGlobal = true
                }

                R.id.rb_silent_1 -> {
                    if (MyUtil.isVIP()) {
                        if (PermissionUtil.hasPermissionVolume(this)) {
                            scheduleWithSub.schedule.lockConfig.isSilentFollowGlobal = false
                            scheduleWithSub.schedule.lockConfig.isSilent = true
                        } else {
                            rb_silent_0.isChecked = true
                            NormalDialog(this).run {
                                setTitle("缺少权限")
                                setContent("需要您授予“免打扰权限”，点击下方“确定”，跳转权限授予页面")
                                setOnNormalOKClickListener(object : OnOKClickListener {
                                    override fun onclick() {
                                        PermissionUtil.openPermissionVolume(this@EditScheduleActivity)
                                    }
                                })
                                showDialog()
                            }
                        }
                    } else {
                        rb_silent_0.isChecked = true
//                        val intent = Intent(this, VIP2Activity::class.java)
//                        intent.putExtra(FROM_WHERE, "EditScheduleActivity_VIP可使用独立设置_勿扰")
//                        startActivity(intent)
//                        MyToastUtil.showInfo(applicationContext, "VIP用户可使用独立设置")
                        DialogUtil.showVIPDialog(
                            this,
                            null,
                            "VIP用户可使用独立勿扰模式设置，开通后，即可为不同的锁机任务分别设置是否开启勿扰模式。",
                            "EditScheduleActivity_VIP可使用独立设置_勿扰"
                        )

                    }

                }

                R.id.rb_silent_2 -> {
                    if (MyUtil.isVIP()) {
                        scheduleWithSub.schedule.lockConfig.isSilentFollowGlobal = false
                        scheduleWithSub.schedule.lockConfig.isSilent = false
                    } else {
                        rb_silent_0.isChecked = true
//                        val intent = Intent(this, VIP2Activity::class.java)
//                        intent.putExtra(FROM_WHERE, "EditScheduleActivity_VIP可使用独立设置_勿扰")
//                        startActivity(intent)
//                        MyToastUtil.showInfo(applicationContext, "VIP用户可使用独立设置")
                        DialogUtil.showVIPDialog(
                            this,
                            null,
                            "VIP用户可使用独立勿扰模式设置，开通后，即可为不同的锁机任务分别设置是否开启勿扰模式。",
                            "EditScheduleActivity_VIP可使用独立设置_勿扰"
                        )

                    }

                }
            }
        }
        nestedScrollView_schedule.setOnScrollChangeListener { v: NestedScrollView?, scrollX: Int, scrollY: Int, oldScrollX: Int, oldScrollY: Int ->
            sb_edit_shake_start.correctOffsetWhenContainerOnScrolling()
            sb_edit_shake_end.correctOffsetWhenContainerOnScrolling()
        }


        iv_edit_schedule_return.setOnClickListener {
            back()
        }
        //保存
        iv_edit_schedule_save.setOnClickListener {
            save()
        }
    }

    private fun back() {
        if (changed) {
            NormalDialog(this).run {
                setTitle("警告")
                setContent("您有数据尚未保存，是否先保存？")
                setOnNormalOKClickListener("保存并退出", object : OnOKClickListener {
                    override fun onclick() {
                        save()
                    }
                })
                setOnNormalCancelClickListener("直接退出", object : OnCancelClickListener {
                    override fun onclick() {
                        finish()
                    }
                })
                showDialog()
            }
        } else {
            finish()
        }
    }

    //刷新白名单列表
    private fun refreshWhiteListView() {

        recyclerview = rv_tomato_edit_white
        recyclerview.layoutManager = GridLayoutManager(this, MyConstants.WHITE_APP_COLUMN)
        val whiteAppAdapter = TomatoWhiteAppAdapter(this, R.layout.item_gridlayout_selected, mutableListOf())
        whiteAppAdapter.animationEnable = true
        recyclerview.adapter = whiteAppAdapter

        scheduleWithSub.whiteList.let {
            if (it.size == 0) {
                tv_user_white_empty_notice.visibility = View.VISIBLE
            } else {
                tv_user_white_empty_notice.visibility = View.GONE
            }

            lifecycleScope.launch(Dispatchers.Default) {
                val diffResult = DiffUtil.calculateDiff(DiffCallBack(oldWhiteList, it), true)
                oldWhiteList = it
                withContext(Dispatchers.Main) {
                    whiteAppAdapter.setNewInstance(it)
                    diffResult.dispatchUpdatesTo(whiteAppAdapter)
                }
            }
        }


//        val gridLayout = gl_tomato_edit_white
//        gridLayout.removeAllViews()
//        scheduleWithSub.whiteList.forEachIndexed { index, whiteApp ->
//            val itemView = LayoutInflater.from(this).inflate(R.layout.item_gridlayout_selected, null)
//
//            lifecycleScope.launch(Dispatchers.IO) {
//                val appIcon = getAppIcon(whiteApp.pkg, whiteApp.mainActivity)
//                withContext(Dispatchers.Main) {
//                    itemView.iv_app_icon.setImageDrawable(appIcon)
//                }
//            }
//
//            val rowSpec: GridLayout.Spec = GridLayout.spec(index / MyConstants.WHITE_APP_COLUMN, 1.0f)
//            val columnSpec: GridLayout.Spec = GridLayout.spec(index % MyConstants.WHITE_APP_COLUMN, 1.0f)
//            val params: GridLayout.LayoutParams = GridLayout.LayoutParams(rowSpec, columnSpec)
//            params.width = 0
//
//            gridLayout.addView(itemView, params)
//        }
//
//        //补齐不足一行的空格，否则会在中间显示
//        scheduleWithSub.whiteList.let {
//            if (it.size == 0) {
//                tv_user_white_empty_notice.visibility = View.VISIBLE
//            } else {
//                tv_user_white_empty_notice.visibility = View.GONE
//            }
//            for (i in 0..(MyConstants.WHITE_APP_COLUMN - 1 - it.size)) {
//                val itemView = LayoutInflater.from(this).inflate(R.layout.item_gridlayout_selected, null)
//                val rowSpec: GridLayout.Spec = GridLayout.spec((i + it.size) / MyConstants.WHITE_APP_COLUMN, 1.0f)
//                val columnSpec: GridLayout.Spec = GridLayout.spec((i + it.size) % MyConstants.WHITE_APP_COLUMN, 1.0f)
//                val params: GridLayout.LayoutParams = GridLayout.LayoutParams(rowSpec, columnSpec)
//                params.width = 0
//
//                gridLayout.addView(itemView, params)
//            }
//        }
    }

    private fun save() {
//        LogUtils.d("!!!!!!!!!!!!2"+schedule.title)

        LogUtils.d("save scheduleWithSub.whiteList.size = ${scheduleWithSub.whiteList.size}")

        if (schedule.useTomato) {
            if (schedule.tomatoIndexId == "") {
                MyToastUtil.showWarning("还没选择番茄任务哦")
                return
            }


        } else {
            if (schedule.sunday && schedule.monday && schedule.tuesday && schedule.wednesday && schedule.thursday && schedule.friday && schedule.saturday) {
                if (schedule.startHour * 60 + schedule.startMinute == schedule.endHour * 60 + schedule.endMinute) {
                    MyToastUtil.showWarning("开始时间不能等于结束时间", Toast.LENGTH_LONG)
                    return
                }

                if ((schedule.startHour * 60 + schedule.startMinute) - (schedule.endHour * 60 + schedule.endMinute) in 1..schedule.denyChangeLength //>=1 <=60
                    && schedule.isDenyChange
                ) {
                    MyToastUtil.showWarning("请缩小锁机时间范围或关闭禁止${schedule.denyChangeLength}分钟内更改，否则激活后将会无法变更", Toast.LENGTH_LONG)
                    return
                }
            }
        }

        NormalDialog(this).apply {
            setTitle("温馨提示")
            setContent("请确定您设置的所有定时任务之间没有时间重叠，否则将会有无限锁机无法解锁的风险，请仔细检查。")
            setGravity(Gravity.START)
            isCancelable = false
            setOnNormalOKClickListener("我确定", object : OnOKClickListener {
                override fun onclick() {
                    if (saveMode == SAVE_MODE_CREATE) {
                        viewModel.createScheduleWithSub(scheduleWithSub)
                    } else {
                        viewModel.updateScheduleWithSub(scheduleWithSub)
                    }
                    finish()
                }
            })
            setOnNormalCancelClickListener("不确定", object : OnCancelClickListener {
                override fun onclick() {

                }
            })
            showDialog()
        }


    }

    override fun onResume() {
        super.onResume()
        if (scheduleWithSub.schedule.lockConfig.isRemoveNotificationFollowGlobal) {
            rb_notification_0.isChecked = true
        } else {
            if (scheduleWithSub.schedule.lockConfig.isRemoveNotification && PermissionUtil.isNotificationListenersEnabled(this)) {
                rb_notification_1.isChecked = true
            } else {
                rb_notification_2.isChecked = true
            }
        }
        if (scheduleWithSub.schedule.lockConfig.isSilentFollowGlobal) {
            rb_silent_0.isChecked = true
        } else {
            if (scheduleWithSub.schedule.lockConfig.isSilent && PermissionUtil.hasPermissionVolume(this)) {
                rb_silent_1.isChecked = true
            } else {
                rb_silent_2.isChecked = true
            }
        }
    }

    override fun onBackPressed() {
        back()
    }

    private class DiffCallBack(val oldList: MutableList<WhiteApp>, val newList: MutableList<WhiteApp>) : DiffUtil.Callback() {

        override fun areItemsTheSame(oldItemPosition: Int, newItemPosition: Int): Boolean {
            return (oldList[oldItemPosition].whiteAppIndexId == newList[newItemPosition].whiteAppIndexId)
        }

        override fun getOldListSize(): Int {
            return oldList.size
        }

        override fun getNewListSize(): Int {
            return newList.size
        }

        override fun areContentsTheSame(oldItemPosition: Int, newItemPosition: Int): Boolean {
            return (oldList[oldItemPosition].pkg == newList[newItemPosition].pkg)
                    && (oldList[oldItemPosition].mainActivity == newList[newItemPosition].mainActivity)
                    && (oldList[oldItemPosition].maxLen == newList[newItemPosition].maxLen)
        }

    }
}
