package com.lijianqiang12.silent.component.activity.lock.whiteapp

import android.widget.ImageView
import android.widget.TextView
import com.chad.library.adapter.base.BaseQuickAdapter
import com.chad.library.adapter.base.module.LoadMoreModule
import com.chad.library.adapter.base.viewholder.BaseViewHolder
import com.lijianqiang12.silent.R
import com.lijianqiang12.silent.utils.MyUtil

class AppInfoAdapter( layoutRes: Int, list: MutableList<AppInfo>)
    : BaseQuickAdapter<AppInfo, BaseViewHolder>(layoutRes, list), LoadMoreModule {

    override fun convert(viewHolder: BaseViewHolder, item: AppInfo) {
        viewHolder.getView<TextView>(R.id.tv_app_name).text = item.appName
        viewHolder.getView<ImageView>(R.id.iv_app_icon).setImageDrawable(item.appIcon)
    }
}