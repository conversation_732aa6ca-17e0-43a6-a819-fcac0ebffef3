package com.lijianqiang12.silent.component.activity.analyze.appusage

import android.os.Bundle
import androidx.activity.viewModels
import androidx.lifecycle.Observer
import androidx.recyclerview.widget.LinearLayoutManager
import com.lijianqiang12.silent.R
import com.lijianqiang12.silent.component.activity.base.BaseActivity
import com.lijianqiang12.silent.data.viewmodel.AnalyzeViewModel
import dagger.hilt.android.AndroidEntryPoint
import kotlinx.android.synthetic.main.activity_app_usage_analyze.*
import kotlinx.android.synthetic.main.activity_today_app_usage.*
import kotlinx.android.synthetic.main.fragment_analyze.*
import kotlinx.android.synthetic.main.item_analyze_app_used.view.*
import javax.inject.Inject

@AndroidEntryPoint
class AppUsageAnalyzeActivity : BaseActivity() {
    private lateinit var mAdapter: AppUsageAnalyzeAdapter
    private lateinit var mLayoutManager: androidx.recyclerview.widget.RecyclerView.LayoutManager

    @Inject
    lateinit var viewModel: AnalyzeViewModel

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        setContentView(R.layout.activity_app_usage_analyze)
        iv_return_app_usage_analyze.setOnClickListener { finish() }

        mLayoutManager = LinearLayoutManager(this)
        rv_app_usage_analyze.layoutManager = mLayoutManager
        mAdapter = AppUsageAnalyzeAdapter( R.layout.item_analyze_app_used, mutableListOf())
        mAdapter.animationEnable = true
        rv_app_usage_analyze.adapter = mAdapter

//        mAdapter.setOnItemClickListener { adapter, view, position ->
//        }


        viewModel.appAnalyzeTodayLiveData.observe(this, Observer {
            mAdapter.setNewInstance(it)
            mAdapter.notifyDataSetChanged()

        })


        viewModel.refreshAppAnalyzeToday()

    }
}