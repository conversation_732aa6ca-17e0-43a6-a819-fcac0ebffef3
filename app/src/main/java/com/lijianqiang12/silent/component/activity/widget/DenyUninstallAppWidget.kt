package com.lijianqiang12.silent.component.activity.widget

import android.app.PendingIntent
import android.appwidget.AppWidgetManager
import android.appwidget.AppWidgetProvider
import android.content.ComponentName
import android.content.Context
import android.content.Intent
import android.widget.RemoteViews
import com.blankj.utilcode.util.AppUtils
import com.lijianqiang12.silent.utils.MMKVUtils
import com.lijianqiang12.silent.MyConstants
import com.lijianqiang12.silent.R
import com.lijianqiang12.silent.component.activity.IconSplashActivity


/**
 * Implementation of App Widget functionality.
 */
class DenyUninstallAppWidget : AppWidgetProvider() {
    override fun onUpdate(context: Context, appWidgetManager: AppWidgetManager, appWidgetIds: IntArray) {
//        for (appWidgetId in appWidgetIds) {
        updateAppWidget(context, appWidgetManager)
//        }
    }

    override fun onEnabled(context: Context) {
        // Enter relevant functionality for when the first widget is created
        MMKVUtils.put(MyConstants.SP_HOME_WIDGET_EXIST_1,true)
    }

    override fun onDisabled(context: Context) {
        // Enter relevant functionality for when the last widget is disabled
        MMKVUtils.put(MyConstants.SP_HOME_WIDGET_EXIST_1,false)
    }


    companion object {
        fun updateAppWidget(context: Context, appWidgetManager: AppWidgetManager) {
            var localWord = MMKVUtils.getString(MyConstants.SP_KEY_SETTING_WORD, "")
            if (localWord.isEmpty()) {
                localWord = MMKVUtils.getString(MyConstants.SP_KEY_WELL_KNOW_WORD, "") + "\n——" + MMKVUtils.getString(MyConstants.SP_KEY_WELL_KNOW_WORD_AUTHOR, "")
            }

//            val temp = MMKVUtils.getString(MyConstants.SP_KEY_WELL_KNOW_WORD, "") + "\n——" + MMKVUtils.getString(MyConstants.SP_KEY_WELL_KNOW_WORD_AUTHOR, "")

            var widgetText = "我是防卸载小部件（设置个性锁机语后，将同步到此处）"

            if (localWord.isNotEmpty()) {
                widgetText = localWord
            }

            val views = RemoteViews(context.packageName, R.layout.deny_uninstall_app_widget)
//            views.setImageViewResource(R.id.iv_icon, R.mipmap.ic_launcher)
            views.setTextViewText(R.id.tv_text, widgetText)


            val intent = Intent(Intent.ACTION_MAIN)
            intent.component = ComponentName(AppUtils.getAppPackageName(), IconSplashActivity::class.java.canonicalName!!)
//    intent.addFlags(Intent.FLAG_ACTIVITY_NEW_TASK)

            val pendingIntent = PendingIntent.getActivity(context, 0, intent, PendingIntent.FLAG_IMMUTABLE)
            views.setOnClickPendingIntent(R.id.rl_widget, pendingIntent)


            val appWidgetIds = appWidgetManager.getAppWidgetIds(ComponentName(context, DenyUninstallAppWidget::class.java))
            for (appWidgetId in appWidgetIds) {
                appWidgetManager.updateAppWidget(appWidgetId, views)
            }

        }
    }

}


