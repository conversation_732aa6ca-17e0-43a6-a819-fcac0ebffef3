package com.lijianqiang12.silent.component.activity

import android.content.Intent
import android.os.Bundle
import android.provider.Settings
import android.view.View
import com.lijianqiang12.silent.R
import com.lijianqiang12.silent.component.activity.base.BaseActivity
import kotlinx.android.synthetic.main.activity_notice.*

class ToastNoticeActivity : BaseActivity() {

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        setContentView(R.layout.activity_notice)
        val msg = intent.getStringExtra("msg")
        val showJumpToSetting = intent.getBooleanExtra("showJumpToSetting", false)
        val showIKnow = intent.getBooleanExtra("showIKnow", true)
        tv_msg.text = msg


        if (showJumpToSetting) {
            btn_jump_to_setting.visibility = View.VISIBLE
            btn_jump_to_setting.setOnClickListener {
                startActivity(Intent(Settings.ACTION_SETTINGS))
                finish()
            }
        }

        if (showIKnow) {
            button_notice.setOnClickListener {
                finish()
            }
        } else {
            button_notice.visibility = View.GONE
        }
    }
}
