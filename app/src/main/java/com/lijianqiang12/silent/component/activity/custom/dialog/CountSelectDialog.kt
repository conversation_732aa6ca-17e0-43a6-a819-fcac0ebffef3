package com.lijianqiang12.silent.component.activity.custom.dialog

import android.graphics.Color
import android.graphics.drawable.ColorDrawable
import android.os.Bundle
import android.util.DisplayMetrics
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.view.WindowManager
import androidx.appcompat.app.AppCompatActivity
import androidx.fragment.app.Fragment
import com.lijianqiang12.silent.DIALOG_WIDTH_PERCENT_MIDDLE
import com.lijianqiang12.silent.R
import com.lijianqiang12.silent.component.activity.base.BaseDialogFragment
import kotlinx.android.synthetic.main.dialog_lock_app_limit_time.view.btn_ok
import kotlinx.android.synthetic.main.dialog_select_count.view.*

class CountSelectDialog() : BaseDialogFragment() {

    constructor(fragment: Fragment) : this() {
        this.fragment = fragment
    }

    constructor(activity: AppCompatActivity) : this() {
        this.myActivity = activity
    }

    private var okListener: OnOKListener? = null
    private lateinit var v: View
    private var myActivity: AppCompatActivity? = null
    private var fragment: Fragment? = null
    private var count = 1
    private var max = 10
    private var start = 1

    fun setInitCount(count: Int) {
        this.count = count
    }

    fun setMax(max: Int) {
        this.max = max
    }

    fun setStart(start: Int) {
        this.start = start
    }


    override fun onCreateView(inflater: LayoutInflater, container: ViewGroup?, savedInstanceState: Bundle?): View? {
        dialog!!.window!!.setBackgroundDrawable(ColorDrawable(Color.TRANSPARENT))

        v = inflater.inflate(R.layout.dialog_select_count, container, false)

        val dataCount: MutableList<String> = mutableListOf()
        for (i in start..max) {
            dataCount.add("$i")
        }


        v.tpv_count.setData(dataCount)

        if (count < start) count = start
        if (count > max) count = max

        v.tpv_count.setSelected(count - start)

        v.tpv_count.setOnSelectListener { text -> count = text!!.toInt() }



        v.btn_ok.setOnClickListener {
            okListener?.onclick(count)
            dismiss()
        }

        return v
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)

    }


    fun show() {
        fragment?.let {
            super.show(fragment!!.requireFragmentManager(), "TimeSelectDialog")
        }
        this.myActivity?.let {
            super.show(it.supportFragmentManager, "TimeSelectDialog")
        }
    }

    override fun onStart() {
        val params = dialog!!.window!!.attributes
        val dm: DisplayMetrics = resources.displayMetrics
        val width = dm.widthPixels
        params.width = (width * DIALOG_WIDTH_PERCENT_MIDDLE).toInt()//ViewGroup.LayoutParams.MATCH_PARENT
        dialog!!.window!!.attributes = params as WindowManager.LayoutParams
        super.onStart()
    }


    fun setOnOKListener(listener: OnOKListener) {
        this.okListener = listener
    }


    interface OnOKListener {
        fun onclick(count: Int)
    }


}