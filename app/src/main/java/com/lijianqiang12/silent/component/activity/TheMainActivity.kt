package com.lijianqiang12.silent.component.activity

import android.annotation.SuppressLint
import android.appwidget.AppWidgetManager
import android.content.Intent
import android.os.Bundle
import android.view.Gravity
import android.view.View
import androidx.core.content.res.ResourcesCompat
import androidx.fragment.app.Fragment
import androidx.lifecycle.lifecycleScope
import com.blankj.utilcode.util.AppUtils
import com.blankj.utilcode.util.ConvertUtils
import com.blankj.utilcode.util.GsonUtils
import com.blankj.utilcode.util.LogUtils
import com.blankj.utilcode.util.ServiceUtils
import com.iammert.library.readablebottombar.BottomBarItemConfig
import com.iammert.library.readablebottombar.ReadableBottomBar
import com.jeremyliao.liveeventbus.LiveEventBus
import com.lijianqiang12.silent.LiveBus
import com.lijianqiang12.silent.MyConstants
import com.lijianqiang12.silent.R
import com.lijianqiang12.silent.TheApplication
import com.lijianqiang12.silent.component.activity.analyze.AnalyzeFragment
import com.lijianqiang12.silent.component.activity.base.BaseActivity
import com.lijianqiang12.silent.component.activity.custom.dialog.NormalDialog
import com.lijianqiang12.silent.component.activity.custom.dialog.OnCancelClickListener
import com.lijianqiang12.silent.component.activity.custom.dialog.OnOKClickListener
import com.lijianqiang12.silent.component.activity.lock.LockFragment
import com.lijianqiang12.silent.component.activity.lock.punch.PunchCardActivity
import com.lijianqiang12.silent.component.activity.lock.setting.DENY_PAGE_TYPE_FAST
import com.lijianqiang12.silent.component.activity.me.MeFragment
import com.lijianqiang12.silent.component.activity.monitor.MonitorFragment
import com.lijianqiang12.silent.component.activity.room.myjoined.RoomFragment
import com.lijianqiang12.silent.component.activity.widget.DenyUninstallAppWidget
import com.lijianqiang12.silent.component.activity.widget.DenyUninstallAppWidget2
import com.lijianqiang12.silent.component.activity.widget.DenyUninstallAppWidget3
import com.lijianqiang12.silent.component.activity.widget.DenyUninstallAppWidget4
import com.lijianqiang12.silent.component.service.background_service.BackgroundService
import com.lijianqiang12.silent.data.model.net.MyRetrofitClient
import com.lijianqiang12.silent.data.viewmodel.InjectorUtils
import com.lijianqiang12.silent.data.viewmodel.LockViewModel
import com.lijianqiang12.silent.data.viewmodel.LoginViewModel
import com.lijianqiang12.silent.initInitial
import com.lijianqiang12.silent.utils.DialogUtil
import com.lijianqiang12.silent.utils.MMKVUtils
import com.lijianqiang12.silent.utils.MyGsonUtil
import com.lijianqiang12.silent.utils.MyToastUtil
import com.lijianqiang12.silent.utils.MyUtil
import com.lijianqiang12.silent.utils.ServiceUtil
import com.lijianqiang12.silent.utils.checkUpdate
import com.lijianqiang12.silent.utils.isLockRunning
import com.lijianqiang12.silent.utils.logoutAll
import com.lijianqiang12.silent.utils.openFromMarket
import com.lijianqiang12.silent.utils.refreshConfig
//import io.sentry.Sentry
//import io.sentry.protocol.User
import dagger.hilt.android.AndroidEntryPoint
import kotlinx.android.synthetic.main.activity_main.bottom_navigation_bar
import kotlinx.android.synthetic.main.activity_main.tv_unread_count
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.launch
import kotlinx.coroutines.withContext
import javax.inject.Inject

@AndroidEntryPoint
class TheMainActivity : BaseActivity() {

    private val fragments = mutableListOf<Fragment>()

    @Inject
    lateinit var viewModel: LoginViewModel

    @Inject
    lateinit var lockViewModel: LockViewModel

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        setContentView(R.layout.activity_main)

        initInitial()

//        Sentry.setUser(User().apply {
//            id = MMKVUtils.getInt(MyConstants.SP_KEY_USER_ID, -1).toString()
//            username = MMKVUtils.getString(MyConstants.SP_KEY_USERNAME, "")
//        })

        //测试跳转
//        startActivityCompatible(this,"com.android.settings","com.android.settings.Settings\$MobileNetworkActivity")

//        val breakWorld = Button(this).apply {
//            text = "Break the world"
//            setOnClickListener {
//                Sentry.captureException(RuntimeException("This app uses Sentry!！！！！！！！！！！！！！ :)"))
//            }
//        }
//        addContentView(breakWorld, ViewGroup.LayoutParams(ViewGroup.LayoutParams.MATCH_PARENT, ViewGroup.LayoutParams.MATCH_PARENT))

//        try {
//            throw java.lang.Exception("This is ljq test.")
//        } catch (e: java.lang.Exception) {
//            Sentry.captureException(e)
//        }


//        val button = Button(this)
//        button.text = "test"
//        button.setOnClickListener {
//            Sentry.captureException(Exception("Test Exception 20240119-5"))
//        }
//        frame_layout.addView(button)

//        ToastUtils.showLong("channel=${getChannelName()}")

        //刷新已存储页面屏蔽page
        lifecycleScope.launch(Dispatchers.IO) {
            try {
                val result = MyRetrofitClient.service.getFastDenyPageExample()
                if (result.code == 200) {
                    val localDenyPageList = MyGsonUtil.getDenyPageList()
                    var update = false
                    result.data?.onEach { denyPageExample ->
                        localDenyPageList.forEach { localDenyPage ->
                            if (localDenyPage.type == DENY_PAGE_TYPE_FAST && localDenyPage.id == denyPageExample.id && denyPageExample.version > localDenyPage.version) {
                                localDenyPage.name = denyPageExample.name
                                localDenyPage.pages = denyPageExample.pages
                                localDenyPage.version = denyPageExample.version
                                update = true
                            }
                        }
                    }
                    if (update) {
                        MMKVUtils.put(MyConstants.SP_KEY_DENY_PAGE_V2, GsonUtils.toJson(localDenyPageList))
                    }
                } else {
                    withContext(Dispatchers.Main) {
                        MyToastUtil.showError(result.msg)
                    }
                }
            } catch (e: Exception) {
                withContext(Dispatchers.Main) {
                    MyToastUtil.showError(e.message)
                }
            }
        }


//        cl_invite_gift.setOnClickListener {
//            startActivity(Intent(this, InviteGiftActivity::class.java))
//        }

        //刷新桌面组件
        DenyUninstallAppWidget.updateAppWidget(this, AppWidgetManager.getInstance(this))
        DenyUninstallAppWidget2.updateAppWidget(this, AppWidgetManager.getInstance(this))
        DenyUninstallAppWidget3.updateAppWidget(this, AppWidgetManager.getInstance(this))
        DenyUninstallAppWidget4.updateAppWidget(this, AppWidgetManager.getInstance(this))

        //异常退出的时候，再打开，给个提示，引导用户去设置
        val openTimes = MMKVUtils.getLong(MyConstants.SP_KEY_OPEN_TIMES, 0)
        MMKVUtils.put(MyConstants.SP_KEY_OPEN_TIMES, openTimes + 1)

        if (!ServiceUtils.isServiceRunning(BackgroundService::class.java) && openTimes > 0) {
//            MMKVUtils.put(MyConstants.SP_KEY_SHOW_KILL_NOTIFY, true)

            NormalDialog(this).apply {
                setTitle("⚠️锁机失效警告⚠️")
                setContent("十分抱歉，近期${AppUtils.getAppName()}app后台可能因系统省电策略被清理，这会导致您设置的锁机或监督功能失效。强烈建议您去“加固权限”中进行防异常退出设置。")
                setGravity(Gravity.START)
                isCancelable = false
                setOnNormalOKClickListener("现在设置", object : OnOKClickListener {
                    override fun onclick() {
                        startActivity(Intent(requireContext(), PermissionActivity::class.java))
                    }
                })
                setOnNormalCancelClickListener("我知道了", object : OnCancelClickListener {
                    override fun onclick() {
                    }
                })
                showDialog()
            }
        }

        ServiceUtil.checkAndStartService(this, BackgroundService::class.java)
        TheApplication.getInstance().globalParams.checkServiceInitOk = true

        LiveEventBus.get(LiveBus.CLOSE_MAIN_ACTIVITY, String::class.java).observe(this) {
            LogUtils.d("finish")
            this.finish()
        }

        if (MMKVUtils.getBoolean(MyConstants.SP_KEY_FINISH_LOCK, false)) {
            MMKVUtils.put(MyConstants.SP_KEY_FINISH_LOCK, false)
            showPunchCard(MMKVUtils.getLong(MyConstants.SP_KEY_FINISH_LOCK_LENGTH, 0))
        }

        // ViewModels 现在通过 Hilt 自动注入，无需手动创建
        var lastPageIndex = MMKVUtils.getInt(MyConstants.SP_KEY_PAGE_INDEX, 0)

//        bottom_navigation_bar.setItemListVisible(
//            arrayListOf(
//                true,
//                true,
//                MMKVUtils.getBoolean(MyConstants.SP_KEY_SHOW_STATISTIC, true),
//                MMKVUtils.getBoolean(MyConstants.SP_KEY_SHOW_ROOM, true),
//                true
//            )
//        )

        fragments.add(LockFragment.newInstance("", ""))
        fragments.add(MonitorFragment.newInstance("", ""))
        if (MMKVUtils.getBoolean(MyConstants.SP_KEY_SHOW_STATISTIC, true)) {
            fragments.add(AnalyzeFragment.newInstance("", ""))
        }
        if (MMKVUtils.getBoolean(MyConstants.SP_KEY_SHOW_ROOM, true)) {
            fragments.add(RoomFragment.newInstance("", ""))
        }
        fragments.add(MeFragment.newInstance("", ""))


        var maxIndex = 4
        if (!MMKVUtils.getBoolean(MyConstants.SP_KEY_SHOW_STATISTIC, true)) maxIndex--
        if (!MMKVUtils.getBoolean(MyConstants.SP_KEY_SHOW_ROOM, true)) maxIndex--
        lastPageIndex = maxIndex.coerceAtMost(lastPageIndex)
        loadFragmentsTransaction(R.id.frame_layout, lastPageIndex, supportFragmentManager, fragments)
//        bottom_navigation_bar.

        val bottomBarItemConfigList = mutableListOf<BottomBarItemConfig>()
        var index = 0
        bottomBarItemConfigList.add(
            BottomBarItemConfig(resources.getString(R.string.tab1), ResourcesCompat.getDrawable(resources, R.drawable.ic_tab_lock, theme)!!, index),
        )
        index++
        bottomBarItemConfigList.add(
            BottomBarItemConfig(resources.getString(R.string.tab2), ResourcesCompat.getDrawable(resources, R.drawable.ic_tab_monitor, theme)!!, index),
        )
        index++
        if (MMKVUtils.getBoolean(MyConstants.SP_KEY_SHOW_STATISTIC, true)) {
            bottomBarItemConfigList.add(
                BottomBarItemConfig(resources.getString(R.string.tab3), ResourcesCompat.getDrawable(resources, R.drawable.ic_tab_analyze, theme)!!, index),
            )
            index++
        }
        if (MMKVUtils.getBoolean(MyConstants.SP_KEY_SHOW_ROOM, true)) {
            bottomBarItemConfigList.add(
                BottomBarItemConfig(resources.getString(R.string.tab4), ResourcesCompat.getDrawable(resources, R.drawable.ic_tab_room, theme)!!, index),
            )
            index++
        }
        bottomBarItemConfigList.add(
            BottomBarItemConfig(resources.getString(R.string.tab5), ResourcesCompat.getDrawable(resources, R.drawable.ic_tab_me, theme)!!, index),
        )
        bottom_navigation_bar.setInitialIndex(lastPageIndex, bottomBarItemConfigList)

//        bottom_navigation_bar.setOnTabSelectListener(object :AnimatedBottomBar.OnTabSelectListener{
//            override fun onTabSelected(lastIndex: Int, lastTab: AnimatedBottomBar.Tab?, newIndex: Int, newTab: AnimatedBottomBar.Tab) {
//                showHideFragmentTransaction(supportFragmentManager, fragments[newIndex])
//                MMKVUtils.put(MyConstants.SP_KEY_PAGE_INDEX, newIndex)
//            }
//        })
        bottom_navigation_bar.setOnItemSelectListener(object : ReadableBottomBar.ItemSelectListener {
            override fun onItemSelected(index: Int) {
                showHideFragmentTransaction(supportFragmentManager, fragments[index])
                MMKVUtils.put(MyConstants.SP_KEY_PAGE_INDEX, index)
            }
        })

        lifecycleScope.launch(Dispatchers.IO) {
            if (!MyUtil.isVIP()
                && (System.currentTimeMillis() > MMKVUtils.getLong(MyConstants.SP_KEY_NEXT_NOTIFY_VIP_MILL_TIME, 0L))
            ) {
                val fastSize = lockViewModel.lockRepository.getAllFastList().size
                val tomatoSize = lockViewModel.lockRepository.getAllTomatoList().size
                val scheduleSize = lockViewModel.lockRepository.getAllScheduleList().size
                val whiteAppSize = lockViewModel.lockRepository.getGlobalWhiteAppList().size
                if (fastSize > 3) {
                    withContext(Dispatchers.Main) {
                        DialogUtil.showVIPDialog(
                            activity = this@TheMainActivity,
                            fragment = null,
                            content = "您设置了${fastSize}个简单锁机，已超过免费用户3个的数量限制，现在续订即可继续享受无限个数的简单锁机设置。",
                            fromWhere = "3FastLimit",
                            title = "VIP已过期",
                            positiveText = "续订VIP",
                            negativeText = "我知道了",
                            onNegativeListener = object : OnCancelClickListener {
                                override fun onclick() {
                                    MMKVUtils.put(MyConstants.SP_KEY_NEXT_NOTIFY_VIP_MILL_TIME, System.currentTimeMillis() + 7 * 24 * 60 * 60 * 1000)
                                }
                            },
                        )

                    }
                } else if (tomatoSize > 3) {
                    withContext(Dispatchers.Main) {
                        DialogUtil.showVIPDialog(
                            activity = this@TheMainActivity,
                            fragment = null,
                            content = "您设置了${tomatoSize}个番茄锁机，已超过免费用户3个的数量限制，现在续订即可继续享受无限个数的番茄锁机设置。",
                            fromWhere = "3TomatoLimit",
                            title = "VIP已过期",
                            positiveText = "续订VIP",
                            negativeText = "我知道了",
                            onNegativeListener = object : OnCancelClickListener {
                                override fun onclick() {
                                    MMKVUtils.put(MyConstants.SP_KEY_NEXT_NOTIFY_VIP_MILL_TIME, System.currentTimeMillis() + 7 * 24 * 60 * 60 * 1000)
                                }
                            },
                        )

                    }
                } else if (scheduleSize > 3) {
                    withContext(Dispatchers.Main) {
                        DialogUtil.showVIPDialog(
                            activity = this@TheMainActivity,
                            fragment = null,
                            content = "您设置了${tomatoSize}个定时锁机，已超过免费用户3个的数量限制，现在续订即可继续享受无限个数的定时锁机设置。",
                            fromWhere = "3ScheduleLimit",
                            title = "VIP已过期",
                            positiveText = "续订VIP",
                            negativeText = "我知道了",
                            onNegativeListener = object : OnCancelClickListener {
                                override fun onclick() {
                                    MMKVUtils.put(MyConstants.SP_KEY_NEXT_NOTIFY_VIP_MILL_TIME, System.currentTimeMillis() + 7 * 24 * 60 * 60 * 1000)
                                }
                            },
                        )

                    }
                } else if (whiteAppSize > 6) {
                    withContext(Dispatchers.Main) {
                        DialogUtil.showVIPDialog(
                            activity = this@TheMainActivity,
                            fragment = null,
                            content = "您设置了${whiteAppSize}个全局白名单，已超过免费用户6个的数量限制，现在续订即可继续享受无限个数的全局白名单设置。",
                            fromWhere = "3WhiteAppLimit",
                            title = "VIP已过期",
                            positiveText = "续订VIP",
                            negativeText = "我知道了",
                            onNegativeListener = object : OnCancelClickListener {
                                override fun onclick() {
                                    MMKVUtils.put(MyConstants.SP_KEY_NEXT_NOTIFY_VIP_MILL_TIME, System.currentTimeMillis() + 7 * 24 * 60 * 60 * 1000)
                                }
                            },
                        )

                    }
                }
            }
        }


        //检查是否有新更新内容
        val updateContent = MMKVUtils.getString("${AppUtils.getAppVersionCode()}", "")
        val hasRead = MMKVUtils.getBoolean("${AppUtils.getAppVersionCode()}hasRead", false)
        if (updateContent.isNotEmpty() && !hasRead) {
            NormalDialog(this).apply {
                setTitle("本次更新内容")
                setContent(updateContent)
                setGravity(Gravity.START)
                isCancelable = false
                setOnNormalOKClickListener("我知道了", object : OnOKClickListener {
                    override fun onclick() {
                        MMKVUtils.put("${AppUtils.getAppVersionCode()}hasRead", true)
                    }
                })
                setOnNormalCancelClickListener("给个好评", object : OnCancelClickListener {
                    override fun onclick() {
                        MMKVUtils.put("${AppUtils.getAppVersionCode()}hasRead", true)
                        openFromMarket(this@TheMainActivity, "https://www.coolapk.com/apk/${AppUtils.getAppPackageName()}")
                    }
                })
                showDialog()
            }
        } else {
            checkUpdate(this, false)
        }




        lifecycleScope.launch(Dispatchers.IO) {
            var height = 0
            val resourceId = applicationContext.resources.getIdentifier("status_bar_height", "dimen", "android")
            if (resourceId > 0) {
                height = applicationContext.resources.getDimensionPixelSize(resourceId)
            }
            MMKVUtils.put(MyConstants.SP_KEY_STATUS_BAR_HEIGHT, ConvertUtils.px2dp(height.toFloat() + 24).toFloat())
        }


//        val i = Intent(Intent.ACTION_MAIN)
//        i.flags = Intent.FLAG_ACTIVITY_NEW_TASK
//        i.addCategory(Intent.CATEGORY_HOME)
//        startActivity(i)
//        MyToastUtil.showError("弹出桌面")
    }


    override fun onNewIntent(intent: Intent) {
        super.onNewIntent(intent)
        setIntent(intent)
        if (MMKVUtils.getBoolean(MyConstants.SP_KEY_FINISH_LOCK, false)) {
            MMKVUtils.put(MyConstants.SP_KEY_FINISH_LOCK, false)
            showPunchCard(MMKVUtils.getLong(MyConstants.SP_KEY_FINISH_LOCK_LENGTH, 0))
        }
    }


    @SuppressLint("CheckResult")
    override fun onResume() {
        super.onResume()

        if (MMKVUtils.getInt(MyConstants.SP_KEY_USER_ID, -1) != -1) {
            lifecycleScope.launch(Dispatchers.IO) {
                try {
                    val result = viewModel.loginRepository.refreshState()
                    if (result.code == 200) {
                        MMKVUtils.put(MyConstants.SP_KEY_USERNAME, result.data!!.username)
                        MMKVUtils.put(MyConstants.SP_KEY_VIP_STATE, result.data!!.vipState)
                        MMKVUtils.put(MyConstants.SP_KEY_VIP_END_TIME, result.data!!.vipEndTime)
                        MMKVUtils.put(MyConstants.SP_KEY_AVATAR, result.data!!.avatar)
                        MMKVUtils.put(MyConstants.SP_KEY_FORCE_QUITE_PWD, result.data!!.unlockPwd)
                        MMKVUtils.put(MyConstants.SP_KEY_BIND_MOBILE, result.data!!.bindMobile)

                        LiveEventBus.get(LiveBus.START_SYNC_ALL, String::class.java).post("")
                    } else if (result.code == 20002 || result.code == 20003) {
                        withContext(Dispatchers.IO) {
                            if (!isLockRunning()) {
                                logoutAll()
                            }
                        }
                        MyToastUtil.showInfo(result.msg)
                        startActivity(Intent(this@TheMainActivity, TheLoginActivity::class.java))
                        <EMAIL>()
                    }
                } catch (e: Exception) {
                    withContext(Dispatchers.Main) {
                        MyToastUtil.showInfo(e.message)
                    }
                }
            }
        }

        refreshConfig(this)
        //放在刷新配置后
//        if (MMKVUtils.getBoolean(MyConstants.SP_KEY_CONFIG_SHOW_INVITE_GIFT, false)) {
//            cl_invite_gift.visibility = View.VISIBLE
//        } else {
//            cl_invite_gift.visibility = View.GONE
//        }

        lifecycleScope.launch(Dispatchers.IO) {
            try {
                val result = MyRetrofitClient.service.unreadMsgCount()
                withContext(Dispatchers.Main) {
                    if (result.code == 200) {

                        result.data?.let {
                            if (it.msgCount > 0) {
                                this@TheMainActivity.tv_unread_count.visibility = View.VISIBLE
                                this@TheMainActivity.tv_unread_count.text = "${it.msgCount}"
                            } else {
                                this@TheMainActivity.tv_unread_count.visibility = View.GONE
                            }
                        }
                    } else {
                        MyToastUtil.showInfo(result.msg)
                    }
                }
            } catch (e: Exception) {
                withContext(Dispatchers.Main) {
                    MyToastUtil.showInfo(e.message)
                }
            }
        }
    }


    private fun showPunchCard(length: Long) {//dialog需要在页面完全显示后才弹出，锁屏或黑屏时弹出会显示不全，不知是不是系统的bug
        if (
            MMKVUtils.getBoolean(MyConstants.SP_KEY_SELECT_MONEY, false)
            && MMKVUtils.getBoolean(MyConstants.SP_KEY_ALLOW_NOTIFY_PUNCH, true) &&
            MMKVUtils.getInt(MyConstants.SP_KEY_USER_ID, -1) != -1
        ) {

            if (MMKVUtils.getBoolean(MyConstants.SP_KEY_INTRO_12, false)) {
                NormalDialog(this).apply {
                    setTitle("恭喜您完成锁机")
                    isCancelable = true
                    setContent("每一滴汗水，都值得被见证。\n每一次努力，都应该被铭记。\n打个卡，为努力的自己加油！")
                    setOnNormalOKClickListener("去打卡", object : OnOKClickListener {
                        override fun onclick() {
                            val intent = Intent(this@TheMainActivity, PunchCardActivity::class.java)
                            intent.putExtra("showDialog", true)
                            intent.putExtra("length", length)
                            startActivity(intent)
                        }
                    })
                    setOnNormalCancelClickListener("下次吧", object : OnCancelClickListener {
                        override fun onclick() {
                        }
                    })
                    showDialog()
                }
            }
        }
    }


}


