package com.lijianqiang12.silent.component.activity.lock.wallpaper

import android.app.Activity
import android.content.Intent
import android.graphics.drawable.Drawable
import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.Toast
import androidx.fragment.app.viewModels
import androidx.lifecycle.lifecycleScope
import androidx.recyclerview.widget.DiffUtil
import androidx.recyclerview.widget.GridLayoutManager
import androidx.recyclerview.widget.RecyclerView
import com.blankj.utilcode.util.ScreenUtils
import com.bumptech.glide.Glide
import com.bumptech.glide.load.engine.DiskCacheStrategy
import com.bumptech.glide.request.target.SimpleTarget
import com.bumptech.glide.request.transition.Transition
import com.lijianqiang12.silent.MAX_ID
import com.lijianqiang12.silent.R
import com.lijianqiang12.silent.component.activity.REQUEST_CODE_SELECT_SYSTEM_WALLPAPER
import com.lijianqiang12.silent.component.activity.REQUEST_CODE_SET_WALLPAPER
import com.lijianqiang12.silent.component.activity.base.BaseFragment
import com.lijianqiang12.silent.component.activity.custom.dialog.MyProgressDialog
import com.lijianqiang12.silent.component.activity.lock.LockBgAdapter
import com.lijianqiang12.silent.data.model.net.pojos.LockBg
import com.lijianqiang12.silent.data.viewmodel.LockViewModel
import com.lijianqiang12.silent.utils.ImageUtil
import dagger.hilt.android.AndroidEntryPoint
import javax.inject.Inject
import com.lijianqiang12.silent.utils.MyToastUtil
import com.lijianqiang12.silent.utils.setLiveWallpaper
import com.lijianqiang12.silent.utils.wallpaperIsUsed
import kotlinx.android.synthetic.main.bottom_sheet_lock_bg.view.*
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.launch
import kotlinx.coroutines.withContext

@AndroidEntryPoint
class NetworkWallpaperFragment : BaseFragment() {


    private lateinit var dialog: MyProgressDialog

    private lateinit var recyclerview: RecyclerView
    private var bgList: MutableList<LockBg> = mutableListOf()
    private lateinit var customView: View

    private val viewModel: LockViewModel by viewModels()


    override fun onCreateView(inflater: LayoutInflater, container: ViewGroup?, savedInstanceState: Bundle?): View? {
        customView = inflater.inflate(R.layout.fragment_network_wallpaper, container, false)
        return customView
    }


    override fun lazyInit() {
        dialog = MyProgressDialog(fragment = this, cancelable = false)
        recyclerview = customView.rv_lock_bg
        recyclerview.layoutManager = GridLayoutManager(requireContext(), 3)
        val adapter = LockBgAdapter(R.layout.item_lock_bg, mutableListOf(), "")
        adapter.animationEnable = true
        adapter.loadMoreModule.setOnLoadMoreListener {
            viewModel.refreshLockBg(0, bgList.last().imgId)
        }

        recyclerview.adapter = adapter

        (recyclerview.adapter as LockBgAdapter).setOnItemClickListener { adapter, view, position ->

            Glide.with(this)
                .load((adapter.data[position] as LockBg).imgUrl)
                //.transition(DrawableTransitionOptions.withCrossFade())
                .diskCacheStrategy(DiskCacheStrategy.AUTOMATIC)
                .override(ScreenUtils.getScreenWidth(), ScreenUtils.getScreenHeight())
                .centerCrop()
                .into(object : SimpleTarget<Drawable>() {
                    override fun onResourceReady(resource: Drawable, transition: Transition<in Drawable>?) {
                        lifecycleScope.launch(Dispatchers.IO) {
                            ImageUtil.saveWallpaperImage(requireActivity(), resource)
                            withContext(Dispatchers.Main) {
                                if (wallpaperIsUsed(requireContext().applicationContext)) {
                                    MyToastUtil.showInfo("设置成功")
                                } else {
                                    setLiveWallpaper(requireContext().applicationContext, requireActivity(), REQUEST_CODE_SET_WALLPAPER)
                                }
                                dialog.dismiss()
                            }
                        }
                    }

                    override fun onLoadStarted(placeholder: Drawable?) {
                        super.onLoadStarted(placeholder)
                        dialog.show()
                    }

                    override fun onLoadFailed(errorDrawable: Drawable?) {
                        super.onLoadFailed(errorDrawable)
                        dialog.dismiss()
                    }
                })
        }

        viewModel.lockBgLiveData.observe(viewLifecycleOwner, androidx.lifecycle.Observer {
            lifecycleScope.launch(Dispatchers.Default) {
                val diffResult = DiffUtil.calculateDiff(DiffCallBack(bgList, it.data!!), true)
                withContext(Dispatchers.Main) {
                    (recyclerview.adapter as LockBgAdapter).setNewInstance(it.data)
                    bgList = it.data
                    diffResult.dispatchUpdatesTo(recyclerview.adapter as LockBgAdapter)
                    when (it.state) {
                        0 -> adapter.loadMoreModule.loadMoreComplete()
                        1 -> adapter.loadMoreModule.loadMoreEnd()
                    }
                }
            }
        })
        viewModel.refreshLockBg(0, MAX_ID)

    }


    private class DiffCallBack(val oldList: MutableList<LockBg>, val newList: MutableList<LockBg>) : DiffUtil.Callback() {

        override fun areItemsTheSame(oldItemPosition: Int, newItemPosition: Int): Boolean {
            return (oldList[oldItemPosition].imgId == newList[newItemPosition].imgId)
        }

        override fun getOldListSize(): Int {
            return oldList.size
        }

        override fun getNewListSize(): Int {
            return newList.size
        }

        override fun areContentsTheSame(oldItemPosition: Int, newItemPosition: Int): Boolean {
            return (oldList[oldItemPosition].imgUrl == newList[newItemPosition].imgUrl)
        }

    }

    override fun onActivityResult(requestCode: Int, resultCode: Int, data: Intent?) {
        super.onActivityResult(requestCode, resultCode, data)
        if (requestCode == REQUEST_CODE_SET_WALLPAPER) {
            if (resultCode == Activity.RESULT_OK) {
                Toast.makeText(requireContext(), "设置动态壁纸成功", Toast.LENGTH_SHORT).show()
//                (parentFragment as WallpaperBottomSheetDialogFragment).dismiss()
            } else {
                Toast.makeText(requireContext(), "取消设置动态壁纸", Toast.LENGTH_SHORT).show()
            }
        } else if (requestCode == REQUEST_CODE_SELECT_SYSTEM_WALLPAPER) {
            if (resultCode == Activity.RESULT_OK) {
                Toast.makeText(requireContext(), "设置系统壁纸成功", Toast.LENGTH_SHORT).show()
            } else {
                Toast.makeText(requireContext(), "取消设置系统壁纸", Toast.LENGTH_SHORT).show()
            }
        }
    }

    companion object {
        @JvmStatic
        fun newInstance() = NetworkWallpaperFragment()
    }
}