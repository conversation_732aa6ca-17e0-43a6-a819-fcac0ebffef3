package com.lijianqiang12.silent.component.service.windows

import android.app.Service
import android.content.Context
import android.content.pm.ActivityInfo
import android.graphics.PixelFormat
import android.os.Build
import android.view.Gravity
import android.view.LayoutInflater
import android.view.View
import android.view.WindowManager
import com.blankj.utilcode.util.ScreenUtils
import com.lijianqiang12.silent.MyConstants
import com.lijianqiang12.silent.R
import com.lijianqiang12.silent.TheApplication
import com.lijianqiang12.silent.utils.MyScreenUtils
import com.lijianqiang12.silent.utils.MyToastUtil
import com.lijianqiang12.silent.utils.MyWindowUtil
import com.lijianqiang12.silent.utils.PermissionUtil

class FloatWindowOfProtectStart(val context: Context) {

    //    private var ifPerformHome = true
//    private var protectWindowCreated = false
//    private var protectWindowShowing = false
    private lateinit var protectParams: WindowManager.LayoutParams
    private lateinit var protectWindowManager: WindowManager
    private var protectLayout: View? = null

    private fun createStartProtectWindow() {
        protectParams = WindowManager.LayoutParams()

        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.LOLLIPOP_MR1
            && PermissionUtil.isAccessibilitySettingsOn(MyConstants.ACCESS_ABILITY_NAME, context.applicationContext)
            && TheApplication.getInstance().globalParams.accessibilityService != null
        ) {
            protectParams.type = WindowManager.LayoutParams.TYPE_ACCESSIBILITY_OVERLAY
            protectWindowManager = TheApplication.getInstance().globalParams.accessibilityService!!.getSystemService(Context.WINDOW_SERVICE) as WindowManager
        } else if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
            protectParams.type = WindowManager.LayoutParams.TYPE_APPLICATION_OVERLAY
            protectWindowManager = context.applicationContext.getSystemService(Context.WINDOW_SERVICE) as WindowManager
        } else {
            protectParams.type = WindowManager.LayoutParams.TYPE_SYSTEM_ERROR
            protectWindowManager = context.applicationContext.getSystemService(Context.WINDOW_SERVICE) as WindowManager
        }

        protectParams.format = PixelFormat.TRANSLUCENT
        protectParams.flags = protectParams.flags or WindowManager.LayoutParams.FLAG_FULLSCREEN or
                WindowManager.LayoutParams.FLAG_LAYOUT_IN_SCREEN or
                WindowManager.LayoutParams.FLAG_LAYOUT_NO_LIMITS or
                WindowManager.LayoutParams.FLAG_HARDWARE_ACCELERATED
        protectParams.gravity = Gravity.START or Gravity.TOP
        protectParams.x = 0
        protectParams.y = 0
        protectParams.screenOrientation = if (MyScreenUtils.isScreenOrientationPortrait(context as Service)) {
            ActivityInfo.SCREEN_ORIENTATION_PORTRAIT
        } else {
            ActivityInfo.SCREEN_ORIENTATION_LANDSCAPE
        }
        protectParams.height = ScreenUtils.getScreenHeight()
        protectParams.width = ScreenUtils.getScreenWidth()

        protectLayout = LayoutInflater.from(context).inflate(R.layout.layout_start_protect, null)
//        protectWindowCreated = true
    }

    fun showProtectWindow() {
        if (protectLayout == null) {
            createStartProtectWindow()
        }
        if (!MyWindowUtil.isWindowShowing(protectLayout!!)) {
            try {
                protectWindowManager.addView(protectLayout!!, protectParams)

//                protectWindowShowing = true
            } catch (e: Exception) {
//                MyToastUtil.showError(e.message!!)
//                LogUtils.d(e.message!!)
                MyToastUtil.showError("ProtectStartFloatWindow未成功获取到悬浮窗权限，请到系统设置或安全管家中重新授予，若已经授予，则可能是系统BUG，请重启手机")

            }
        }

    }

    fun hideProtectWindow() {
        if (protectLayout != null) {
            if (MyWindowUtil.isWindowShowing(protectLayout!!)) {
//                ifPerformHome = false
//                protectWindowShowing = false
                try {
                    protectWindowManager.removeView(protectLayout!!)
                } catch (e: Exception) {
                    MyToastUtil.showError(e.message!!)
                }
            }
        }
    }
}