package com.lijianqiang12.silent.component.activity.custom.dialog

import android.graphics.Color
import android.graphics.drawable.ColorDrawable
import android.media.AudioAttributes
import android.media.AudioManager
import android.media.MediaPlayer
import android.os.Bundle
import android.util.DisplayMetrics
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.view.WindowManager
import androidx.appcompat.app.AppCompatActivity
import androidx.fragment.app.Fragment
import androidx.lifecycle.lifecycleScope
import com.lijianqiang12.silent.DIALOG_WIDTH_PERCENT_MIDDLE
import com.lijianqiang12.silent.R
import com.lijianqiang12.silent.component.activity.base.BaseDialogFragment
import com.lijianqiang12.silent.utils.MyToastUtil
import com.lijianqiang12.silent.utils.UriUtil
import kotlinx.android.synthetic.main.dialog_ring_choose.view.*
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.launch


class RingChooseDialog() : BaseDialogFragment() {

    constructor(fragment: Fragment) : this() {
        this.fragment = fragment
    }

    constructor(activity: AppCompatActivity) : this() {
        this.activity = activity
    }

    private var listener: OnRingSelectListener? = null
    private lateinit var v: View
    private var fragment: Fragment? = null
    private var activity: AppCompatActivity? = null
    private var index = 0
    private var initFinish = false

    private val mediaPlayerList = mutableListOf<MediaPlayer>()
    private val mediaSource = mutableListOf<Int>()


    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        mediaSource.add(R.raw.ring1_ding)
        mediaSource.add(R.raw.ring2_dong)
        mediaSource.add(R.raw.ring3_complete)
        mediaSource.add(R.raw.ring4_clear)
        mediaSource.add(R.raw.ring5_piano)
        mediaSource.add(R.raw.ring6_kanoon)
        mediaSource.add(R.raw.ring7_box)

        lifecycleScope.launch(Dispatchers.IO) {

            mediaSource.forEachIndexed { index, i ->
                MediaPlayer().apply {
                    setDataSource(requireContext().applicationContext, UriUtil.res2Uri(mediaSource[index]))
                    setAudioAttributes(AudioAttributes.Builder()
                            .setLegacyStreamType(AudioManager.STREAM_NOTIFICATION)
                            .setUsage(AudioAttributes.USAGE_NOTIFICATION)
                            .setContentType(AudioAttributes.CONTENT_TYPE_SONIFICATION)
                            .build())
                    isLooping = true
                    prepare()
                    mediaPlayerList.add(this)
                }
            }

            initFinish = true
        }
    }

    override fun onCreateView(inflater: LayoutInflater, container: ViewGroup?, savedInstanceState: Bundle?): View? {
        dialog!!.window!!.setBackgroundDrawable(ColorDrawable(Color.TRANSPARENT))
        v = inflater.inflate(R.layout.dialog_ring_choose, container, false)
        return v
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)

        when (index) {
            0 -> v.rb_ring_0.isChecked = true
            1 -> v.rb_ring_1.isChecked = true
            2 -> v.rb_ring_2.isChecked = true
            3 -> v.rb_ring_3.isChecked = true
            4 -> v.rb_ring_4.isChecked = true
            5 -> v.rb_ring_5.isChecked = true
            6 -> v.rb_ring_6.isChecked = true
            7 -> v.rb_ring_7.isChecked = true
        }

        v.rg_ring.setOnCheckedChangeListener { group, checkedId ->
            if (initFinish) {
                when (checkedId) {
                    R.id.rb_ring_0 -> {
                        index = 0
                        runMedia(index)
                    }
                    R.id.rb_ring_1 -> {
                        index = 1
                        runMedia(index)
                    }
                    R.id.rb_ring_2 -> {
                        index = 2
                        runMedia(index)
                    }
                    R.id.rb_ring_3 -> {
                        index = 3
                        runMedia(index)
                    }
                    R.id.rb_ring_4 -> {
                        index = 4
                        runMedia(index)
                    }
                    R.id.rb_ring_5 -> {
                        index = 5
                        runMedia(index)
                    }
                    R.id.rb_ring_6 -> {
                        index = 6
                        runMedia(index)
                    }
                    R.id.rb_ring_7 -> {
                        index = 7
                        runMedia(index)
                    }
                }
            } else {
                MyToastUtil.showInfo("铃声加载中，请稍候...")
            }
        }

        v.btn_ok.setOnClickListener {
            listener?.onSelect(index)
            dismiss()
        }
    }

    private fun runMedia(index: Int) {
        mediaPlayerList.forEach {
            if (it.isPlaying) {
                it.pause()
            }
        }
        if (index > 0) {
            mediaPlayerList[index - 1].start()
        }
    }

    fun setIndex(index: Int) {
        this.index = index
    }

    fun show() {
        activity?.apply {
            super.show(this.supportFragmentManager, "NormalDialog")
        }

        fragment?.apply {
            super.show(fragment!!.requireFragmentManager(), "NormalDialog")
        }
    }

    override fun onStart() {
        val params = dialog!!.window!!.attributes
        val dm: DisplayMetrics = resources.displayMetrics
        val width = dm.widthPixels
        params.width = (width * DIALOG_WIDTH_PERCENT_MIDDLE).toInt()//ViewGroup.LayoutParams.MATCH_PARENT
        dialog!!.window!!.attributes = params as WindowManager.LayoutParams
        super.onStart()
    }

    fun setOnRingSelectClickListener(listener: OnRingSelectListener) {
        this.listener = listener
    }


    interface OnRingSelectListener {
        fun onSelect(index: Int)
    }


    override fun onDestroy() {
        mediaPlayerList.forEach {
            it.stop()
            it.release()
        }
        super.onDestroy()
    }

}