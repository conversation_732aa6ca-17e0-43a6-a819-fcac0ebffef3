package com.lijianqiang12.silent.component.receiver

import android.content.BroadcastReceiver
import android.content.Context
import android.content.Intent
import android.util.Log
import com.lijianqiang12.silent.component.service.background_service.BackgroundService
import com.lijianqiang12.silent.utils.ServiceUtil

class RebootReceiver : BroadcastReceiver() {

    override fun onReceive(context: Context, intent: Intent) {
        Log.d("BootReceiver:", "intent:" + intent.action)
        ServiceUtil.checkAndStartService(context, BackgroundService::class.java)

    }
}
