package com.lijianqiang12.silent.component.activity

import android.content.Intent
import android.os.Build
import android.os.Bundle
import android.os.Handler
import android.os.Message
import android.text.method.LinkMovementMethod
import androidx.lifecycle.lifecycleScope
import com.alipay.sdk.app.PayTask
import com.blankj.utilcode.util.LogUtils
import com.jeremyliao.liveeventbus.LiveEventBus
import com.lijianqiang12.silent.LiveBus
import com.lijianqiang12.silent.MyConstants
import com.lijianqiang12.silent.component.activity.base.BaseActivity
import com.lijianqiang12.silent.component.activity.custom.dialog.PAY_TYPE_ALIPAY
import com.lijianqiang12.silent.component.activity.custom.dialog.PAY_TYPE_WXPAY
import com.lijianqiang12.silent.data.model.db.AppLimit
import com.lijianqiang12.silent.data.model.net.MyRetrofitClient
import com.lijianqiang12.silent.databinding.ActivityPayForceUnlockBinding
import com.lijianqiang12.silent.utils.MMKVUtils
import com.lijianqiang12.silent.utils.MyToastUtil
import com.lijianqiang12.silent.utils.MyUtil
import com.lijianqiang12.silent.utils.PayResult
import com.tencent.mm.opensdk.modelpay.PayReq
import com.tencent.mm.opensdk.openapi.IWXAPI
import com.tencent.mm.opensdk.openapi.WXAPIFactory
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.launch
import kotlinx.coroutines.withContext

class AppLimitUnlockActivity : BaseActivity() {

    private lateinit var binding: ActivityPayForceUnlockBinding
    private val SDK_PAY_FLAG = 1
    private lateinit var api: IWXAPI
    private var uuid = -1L
    private var title = ""
    private var appPkg = ""
    private var editMoney = -1L

    private fun checkIntent(intent: Intent) {
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.TIRAMISU) {
            intent.getParcelableExtra("appLimit", AppLimit::class.java)
        } else {
            intent.getParcelableExtra("appLimit")
        }

        uuid = intent.getLongExtra("uuid", -1)
        title = intent.getStringExtra("title") ?: ""
        appPkg = intent.getStringExtra("appPkg") ?: ""
        editMoney = intent.getLongExtra("editMoney", -1)

    }

    override fun onNewIntent(intent: Intent) {
        super.onNewIntent(intent)
        LogUtils.d("AppLimitUnlockActivity onNewIntent intent ${intent}")
        if (intent == null) {
            MyToastUtil.showError("intent不能为空")
        } else {
            checkIntent(intent)
        }
    }


    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        LogUtils.d("AppLimitUnlockActivity onCreate intent ${intent}")
        checkIntent(intent)

        if (editMoney <= 0) {
            MyToastUtil.showError("您没有为『${title}』开启强制更改，您可在允许修改配置时间段内为期开启，以防下次遇到此类状况")
            <EMAIL>()
            return
        }
        if (uuid <= 0) {
            MyToastUtil.showError("该应用限时任务尚未同步到服务器，请先保持网络通畅并在一分钟后重试")
            LiveEventBus.get(LiveBus.START_SYNC_APP_LIMIT, String::class.java).post("")
            <EMAIL>()
            return
        }

        api = WXAPIFactory.createWXAPI(this, MyConstants.WX_APP_ID)

        LiveEventBus.get(LiveBus.PAY_FOR_APP_LIMIT_SUCCEED, Boolean::class.java).observe(this) {
            if (it) {
                paySucceed(PAY_TYPE_WXPAY)
            } else {
                MyToastUtil.showInfo("支付失败")
//                finish()
            }
        }

        binding = ActivityPayForceUnlockBinding.inflate(layoutInflater)
        setContentView(binding.root)

        binding.tvPayTitle.text = "解除『${MyUtil.getAppLimitTitle(title, appPkg)}』限时"
        val str =
            "1.解除app限时需要联网。\n2.解除限时后会暂停该限时。\n3.罚金解锁金额可在“相应的app限时配置”中调整。\n4.缴纳罚金后在1小时内可补差价购买永久版vip。"

        binding.tvPayNotice.text = str
        //一定要记得设置这个方法  不是不起作用
        binding.tvPayNotice.movementMethod = LinkMovementMethod.getInstance()

//        binding.root.setOnClickListener { view ->
//            finish()
//        }

        binding.btnVipAlipay.setOnClickListener {
            MyUtil.checkLoginAndDo(this) {

                MMKVUtils.put(MyConstants.SP_KEY_PAY_TYPE, 2)
                lifecycleScope.launch(Dispatchers.IO) {
                    try {
                        val result = MyRetrofitClient.service.makeAlipayOrderAppLimit(2, editMoney / 100, uuid)
                        withContext(Dispatchers.Main) {
                            if (result.code == 200) {
                                result.data?.let {
                                    val payRunnable = Runnable {
                                        val alipay = PayTask(this@AppLimitUnlockActivity)
                                        val payResult = alipay.payV2(it.order, true) as Map<String, String>
                                        val msg = Message()
                                        msg.what = SDK_PAY_FLAG
                                        msg.obj = payResult
                                        mHandler.sendMessage(msg)
                                    }
                                    val payThread = Thread(payRunnable)
                                    payThread.start()
                                }
                            } else {
                                MyToastUtil.showInfo(result.msg)
//                            <EMAIL>()
                            }
                        }


                    } catch (e: Exception) {
                        withContext(Dispatchers.Main) {
                            MyToastUtil.showInfo(e.message)
//                        <EMAIL>()
                        }
                    }
                }
            }
        }


        binding.btnVipWxpay.setOnClickListener {
            MyUtil.checkLoginAndDo(this) {

                MMKVUtils.put(MyConstants.SP_KEY_PAY_TYPE, 2)
                lifecycleScope.launch(Dispatchers.IO) {
                    try {

                        val result = MyRetrofitClient.service.makeWXOrderAppLimit(2, editMoney / 100, uuid)
                        withContext(Dispatchers.Main) {
                            if (result.code == 200) {
                                result.data?.let { wxOrder ->
                                    val request = PayReq()
                                    request.appId = wxOrder.appId
                                    request.partnerId = result.data.partnerId
                                    request.prepayId = result.data.prepayId
                                    request.packageValue = result.data.packageValue
                                    request.nonceStr = result.data.nonceStr
                                    request.timeStamp = result.data.timeStamp
                                    request.sign = result.data.sign
                                    api.sendReq(request)
                                }
//                                                <EMAIL>()
                            } else {
                                MyToastUtil.showInfo(result.msg)
//                            <EMAIL>()
                            }
                        }

                    } catch (e: Exception) {
                        withContext(Dispatchers.Main) {
                            MyToastUtil.showInfo(e.message)
//                        <EMAIL>()
                        }
                    }
                }
            }
        }


    }

    private val mHandler = Handler {
        if (it.what == SDK_PAY_FLAG) {
            val payResult = PayResult(it.obj as Map<String, String>)
            when (payResult.resultStatus) {
                "6001" -> {
                    MyToastUtil.showInfo("支付失败")
//                    <EMAIL>()
                }

                else -> {
                    paySucceed(PAY_TYPE_ALIPAY)
                }
            }
        }
        true
    }

    private fun paySucceed(payType: Int) {

        lifecycleScope.launch(Dispatchers.IO) {
            try {
                val result = MyRetrofitClient.service.queryOrder()
                if (result.code == 200) {
                    withContext(Dispatchers.Main) {
                        MyToastUtil.showInfo("支付成功")
                        LiveEventBus.get(LiveBus.START_SYNC_APP_LIMIT, String::class.java).post("")
                        <EMAIL>()
                    }
                } else {
                    withContext(Dispatchers.Main) {
                        MyToastUtil.showError(result.msg)
//                        <EMAIL>()
                    }
                }
            } catch (e: Exception) {
                MyToastUtil.showInfo(e.message)
//                <EMAIL>()
            }
        }
    }
}