package com.lijianqiang12.silent.component.activity.room.roomdetail

import android.view.View
import android.widget.ImageView
import android.widget.TextView
import com.bumptech.glide.Glide
import com.bumptech.glide.load.engine.DiskCacheStrategy
import com.chad.library.adapter.base.BaseQuickAdapter
import com.chad.library.adapter.base.module.LoadMoreModule
import com.chad.library.adapter.base.viewholder.BaseViewHolder
import com.lijianqiang12.silent.TheApplication
import com.lijianqiang12.silent.R
import com.lijianqiang12.silent.data.model.net.pojos.RoomDetailMember

class RoomMemberAdapter(layoutRes: Int, list: MutableList<RoomDetailMember>)
    : BaseQuickAdapter<RoomDetailMember, BaseViewHolder>(layoutRes, list), LoadMoreModule {

    override fun convert(viewHolder: BaseViewHolder, item: RoomDetailMember) {
        Glide.with(context).load(item.avatar)
            //.transition(DrawableTransitionOptions.withCrossFade())
            .diskCacheStrategy(DiskCacheStrategy.AUTOMATIC)
            .into(viewHolder.getView(R.id.avatar))
        viewHolder.getView<TextView>(R.id.tv_name).text = item.username

        if (item.isOnline) {
            viewHolder.getView<TextView>(R.id.tv_word).text = "锁机中"
            viewHolder.getView<TextView>(R.id.tv_word).setTextColor(TheApplication.getInstance().resources.getColor(R.color.colorWhiteText))
            viewHolder.getView<TextView>(R.id.tv_word).setBackgroundResource(R.drawable.shape_online)
        } else {

            viewHolder.getView<TextView>(R.id.tv_word).setBackgroundResource(R.drawable.shape_punch_word)
            viewHolder.getView<TextView>(R.id.tv_word).setTextColor(TheApplication.getInstance().resources.getColor(R.color.custom_color_app_text_2_default))
            viewHolder.getView<TextView>(R.id.tv_word).text = "休息中"
        }

        when (item.vipState) {
            0 -> {
                viewHolder.getView<TextView>(R.id.tv_vip).visibility = View.INVISIBLE
            }
            1 -> {
                viewHolder.getView<TextView>(R.id.tv_vip).visibility = View.VISIBLE
                viewHolder.getView<TextView>(R.id.tv_vip).text = "VIP"
                viewHolder.getView<TextView>(R.id.tv_vip).setTextColor(context.resources.getColor(R.color.colorBackText))
                viewHolder.getView<TextView>(R.id.tv_vip).setBackgroundResource(R.drawable.shape_get_vip_gradient_2dp)
            }
            2 -> {
                viewHolder.getView<TextView>(R.id.tv_vip).visibility = View.VISIBLE
                viewHolder.getView<TextView>(R.id.tv_vip).text = "SVIP"
                viewHolder.getView<TextView>(R.id.tv_vip).setTextColor(context.resources.getColor(R.color.colorWhiteText))
                viewHolder.getView<TextView>(R.id.tv_vip).setBackgroundResource(R.drawable.shape_get_vip_black_gradient_2dp)
            }
        }


        viewHolder.getView<TextView>(R.id.tv_lock_number).text = "${item.lockNumber}次"
        viewHolder.getView<TextView>(R.id.tv_go_on_days).text = "${item.goOnDays}天"
        viewHolder.getView<TextView>(R.id.tv_length).text = "第${item.joinRoomLength}天"

        when {
            item.totalLength / 60 / 60 > 0 -> {
                viewHolder.getView<TextView>(R.id.tv_total_length).text = "${item.totalLength / 60 / 60}h"
            }
            item.totalLength / 60 > 0 -> {
                viewHolder.getView<TextView>(R.id.tv_total_length).text = "${item.totalLength / 60}m"
            }
            else -> {
                viewHolder.getView<TextView>(R.id.tv_total_length).text = "${item.totalLength}s"
            }
        }

        when (item.gender) {
            0 -> {
                viewHolder.getView<ImageView>(R.id.iv_gender).visibility = View.GONE
            }
            1 -> {
                viewHolder.getView<ImageView>(R.id.iv_gender).visibility = View.VISIBLE
                viewHolder.getView<ImageView>(R.id.iv_gender).setImageResource(R.drawable.ic_male)

            }
            2 -> {
                viewHolder.getView<ImageView>(R.id.iv_gender).visibility = View.VISIBLE
                viewHolder.getView<ImageView>(R.id.iv_gender).setImageResource(R.drawable.ic_female)
            }
        }


    }
}