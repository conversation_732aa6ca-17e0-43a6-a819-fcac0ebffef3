package com.lijianqiang12.silent.component.activity

import android.app.Activity
import android.content.Intent
import android.net.Uri
import android.os.Build
import android.os.Bundle
import android.view.View
import android.view.ViewGroup
import android.webkit.*
import androidx.appcompat.app.AlertDialog
import com.blankj.utilcode.util.ConvertUtils
import com.lijianqiang12.silent.R
import com.lijianqiang12.silent.component.activity.base.BaseActivity
import com.lijianqiang12.silent.component.activity.custom.ProgressView
import kotlinx.android.synthetic.main.activity_webview.*


class TheWebViewActivity : BaseActivity() {
    private val FILECHOOSER_RESULTCODE = 1001
    private val FILECHOOSER_RESULTCODE_FOR_ANDROID_5 = 1002
    private var mUploadMessage: ValueCallback<Uri?>? = null
    private var mUploadMessageForAndroid5: ValueCallback<Array<Uri>>? = null
    private var progressView: ProgressView? = null

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        setContentView(R.layout.activity_webview)

        intent.getStringExtra("title")?.apply {
            webview_toolbar.visibility = View.VISIBLE
            tv_webview_title.text = this
            iv_webview_return.setOnClickListener { finish() }
        }

        val url = intent.getStringExtra("url")!!
        val type = intent.getIntExtra("type", REQUEST_TYPE_GET)

//        if(Build.VERSION.SDK_INT >= Build.VERSION_CODES.M) {
//            window.decorView.systemUiVisibility = View.SYSTEM_UI_FLAG_LAYOUT_FULLSCREEN or View.SYSTEM_UI_FLAG_LIGHT_STATUS_BAR //黑色字体
//            window.statusBarColor = getColor(android.R.color.transparent) //透明背景
//        }


        //初始化进度条
        progressView = ProgressView(this)
        progressView!!.layoutParams = ViewGroup.LayoutParams(ViewGroup.LayoutParams.MATCH_PARENT, ConvertUtils.dp2px(3f))
        progressView!!.setColor(resources.getColor(R.color.custom_color_app_text_1_default))
        progressView!!.setProgress(10)
        //把进度条加到WebView中
        web_view.addView(progressView)
        //初始化设置
        initWebSettings()
        web_view.webChromeClient = MyWebChromeClient()
        web_view.webViewClient = MyWebViewClient()

        if (type == REQUEST_TYPE_GET) {
            web_view.loadUrl(url)
        } else {
            web_view.postUrl(url, intent.getStringExtra("data")!!.toByteArray())
        }
    }


    private fun initWebSettings() {
        web_view.settings.javaScriptEnabled = true
        web_view.settings.domStorageEnabled = true
        web_view.settings.useWideViewPort = true
        web_view.settings.loadWithOverviewMode = true
        web_view.settings.allowFileAccess = true
        web_view.settings.javaScriptCanOpenWindowsAutomatically = true //支持通过JS打开新窗口
        web_view.settings.loadsImagesAutomatically = true //支持自动加载图片
        web_view.settings.defaultTextEncodingName = "utf-8"//设置编码格式
    }

    private inner class MyWebChromeClient : WebChromeClient() {
        override fun onProgressChanged(view: WebView, newProgress: Int) {
            if (newProgress == 100) {
                //加载完毕进度条消失
                progressView!!.visibility = View.GONE
            } else {
                //更新进度
                progressView!!.setProgress(newProgress)
            }
            super.onProgressChanged(view, newProgress)
        }

        //扩展支持alert事件   //否则无法选择图片
        override fun onJsAlert(view: WebView, url: String?, message: String?, result: JsResult): Boolean {
            val builder = AlertDialog.Builder(view.context)
            builder.setTitle("提示").setMessage(message).setPositiveButton("确定", null)
            builder.setCancelable(false)
            builder.setIcon(R.mipmap.ic_launcher)
            val dialog: AlertDialog = builder.create()
            dialog.show()
            result.confirm()
            return true
        }

        // For Android > 5.0   //否则无法选择图片
        override fun onShowFileChooser(web_view: WebView?, uploadMsg: ValueCallback<Array<Uri>>, fileChooserParams: FileChooserParams?): Boolean {
            openFileChooserImplForAndroid5(uploadMsg)
            return true
        }


    }


    private inner class MyWebViewClient : WebViewClient() {

//        override fun shouldOverrideUrlLoading(view: WebView, url: String): Boolean {
//            super.shouldOverrideUrlLoading(view, url)
//            if (url == null) {
//                return false
//            }
//            try {
//                if (url.startsWith("weixin://")) {
//                    val intent = Intent(Intent.ACTION_VIEW, Uri.parse(url + "?d-wx-push=1"))//加它才会push给微信
//                    view.context.startActivity(intent)
//                    return true
//                }
//            } catch (e: Exception) {
//                return false
//            }
//            view.loadUrl(url)
//            return true
//        }

    }


    override fun onActivityResult(requestCode: Int, resultCode: Int, intent: Intent?) {
        super.onActivityResult(requestCode, resultCode, intent)
        if (requestCode == FILECHOOSER_RESULTCODE) {
            if (null == mUploadMessage) return
            val result = if (intent == null || resultCode != Activity.RESULT_OK) null else intent.data
            mUploadMessage!!.onReceiveValue(result)
            mUploadMessage = null
        } else if (requestCode == FILECHOOSER_RESULTCODE_FOR_ANDROID_5) {
            if (null == mUploadMessageForAndroid5) return
            val result = if (intent == null || resultCode != Activity.RESULT_OK) null else intent.data
            if (result != null) {
                mUploadMessageForAndroid5!!.onReceiveValue(arrayOf(result))
            } else {
                mUploadMessageForAndroid5!!.onReceiveValue(arrayOf())
            }
            mUploadMessageForAndroid5 = null
        }
    }

    override fun onBackPressed() {
        if (web_view!!.canGoBack()) {
            web_view!!.goBack()
        } else {
            super.onBackPressed()
        }
    }

    private fun openFileChooserImplForAndroid5(uploadMsg: ValueCallback<Array<Uri>>) {
        mUploadMessageForAndroid5 = uploadMsg
        val contentSelectionIntent = Intent(Intent.ACTION_GET_CONTENT)
        contentSelectionIntent.addCategory(Intent.CATEGORY_OPENABLE)
        contentSelectionIntent.type = "image/*"
        val chooserIntent = Intent(Intent.ACTION_CHOOSER)
        chooserIntent.putExtra(Intent.EXTRA_INTENT, contentSelectionIntent)
        chooserIntent.putExtra(Intent.EXTRA_TITLE, "Image Chooser")
        startActivityForResult(chooserIntent, FILECHOOSER_RESULTCODE_FOR_ANDROID_5)

//        val intent = Intent(Intent.ACTION_PICK, MediaStore.Images.Media.EXTERNAL_CONTENT_URI)
//        startActivityForResult(intent, requestPicCode)
    }

//    override fun onOptionsItemSelected(item: MenuItem): Boolean {
//        when (item.itemId) {
//            android.R.id.home -> {
//                finish()
//                return true
//            }
//            else -> {
//            }
//        }
//        return super.onOptionsItemSelected(item)
//    }

    override fun onDestroy() {
        web_view!!.loadDataWithBaseURL(null, "", "text/html", "utf-8", null)
        web_view!!.clearHistory()
        (web_view!!.parent as ViewGroup).removeView(web_view)
        web_view!!.destroy()
        super.onDestroy()
    }

    companion object {
        var REQUEST_TYPE_GET = 0
        var REQUEST_TYPE_POST = 1
    }
}