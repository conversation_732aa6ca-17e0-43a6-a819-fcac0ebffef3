package com.lijianqiang12.silent.component.activity.widget

import android.app.PendingIntent
import android.appwidget.AppWidgetManager
import android.appwidget.AppWidgetProvider
import android.content.ComponentName
import android.content.Context
import android.content.Intent
import android.widget.RemoteViews
import com.blankj.utilcode.util.AppUtils
import com.lijianqiang12.silent.MyConstants
import com.lijianqiang12.silent.R
import com.lijianqiang12.silent.component.activity.IconSplashActivity
import com.lijianqiang12.silent.utils.MMKVUtils

/**
 * Implementation of App Widget functionality.
 */
class DenyUninstallAppWidget4 : AppWidgetProvider() {
    override fun onUpdate(context: Context, appWidgetManager: AppWidgetManager, appWidgetIds: IntArray) {
//        for (appWidgetId in appWidgetIds) {
            updateAppWidget(context, appWidgetManager)
//        }
    }

    override fun onEnabled(context: Context) {
        // Enter relevant functionality for when the first widget is created
        MMKVUtils.put(MyConstants.SP_HOME_WIDGET_EXIST_4, true)
    }

    override fun onDisabled(context: Context) {
        // Enter relevant functionality for when the last widget is disabled
        MMKVUtils.put(MyConstants.SP_HOME_WIDGET_EXIST_4, false)
    }

    companion object {
        internal fun updateAppWidget(context: Context, appWidgetManager: AppWidgetManager) {
            val views = RemoteViews(context.packageName, R.layout.deny_uninstall_app_widget2)
            views.setImageViewResource(R.id.iv_icon2, R.mipmap.ic_launcher)


            val intent = Intent(Intent.ACTION_MAIN)
            intent.component = ComponentName(AppUtils.getAppPackageName(), IconSplashActivity::class.java.canonicalName!!)
//    intent.addFlags(Intent.FLAG_ACTIVITY_NEW_TASK)

            val pendingIntent = PendingIntent.getActivity(context, 0, intent, PendingIntent.FLAG_IMMUTABLE)
            views.setOnClickPendingIntent(R.id.rl_widget2, pendingIntent)

            val appWidgetIds = appWidgetManager.getAppWidgetIds(ComponentName(context, DenyUninstallAppWidget4::class.java))
            for (appWidgetId in appWidgetIds) {
                appWidgetManager.updateAppWidget(appWidgetId, views)
            }
        }

    }
}

