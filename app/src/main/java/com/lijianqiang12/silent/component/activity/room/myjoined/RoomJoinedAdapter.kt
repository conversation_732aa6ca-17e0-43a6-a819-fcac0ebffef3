package com.lijianqiang12.silent.component.activity.room.myjoined

import android.widget.TextView
import com.bumptech.glide.Glide
import com.bumptech.glide.load.engine.DiskCacheStrategy
import com.chad.library.adapter.base.BaseQuickAdapter
import com.chad.library.adapter.base.module.LoadMoreModule
import com.chad.library.adapter.base.viewholder.BaseViewHolder
import com.lijianqiang12.silent.R
import com.lijianqiang12.silent.data.model.net.pojos.MyJoinedRoom

class RoomJoinedAdapter( layoutRes: Int, item: MutableList<MyJoinedRoom>)
    : BaseQuickAdapter<MyJoinedRoom, BaseViewHolder>(layoutRes, item) , LoadMoreModule {

    override fun convert(viewHolder: BaseViewHolder, item: MyJoinedRoom) {
        val tvRoomName=viewHolder.getView<TextView>(R.id.tv_item_room_joined)
        tvRoomName.isSelected=true
        tvRoomName.text = item.roomName
        viewHolder.getView<TextView>(R.id.tv_item_room_joined_numbers).text = "${item.roomOnlineNumbers}人在线"
        Glide.with(context).load(item.roomCoverUrl)
            //.transition(DrawableTransitionOptions.withCrossFade())
            .diskCacheStrategy(DiskCacheStrategy.AUTOMATIC)
            .into(viewHolder.getView(R.id.iv_item_room_joined))
    }
}