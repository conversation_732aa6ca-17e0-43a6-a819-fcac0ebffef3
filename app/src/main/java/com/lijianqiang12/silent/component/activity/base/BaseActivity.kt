package com.lijianqiang12.silent.component.activity.base

import android.animation.ObjectAnimator
import android.annotation.SuppressLint
import android.content.Context
import android.content.res.Resources
import android.graphics.Color
import android.os.Build
import android.os.Bundle
import android.view.View
import android.view.Window
import android.view.WindowManager
import androidx.annotation.IdRes
import androidx.appcompat.app.AppCompatActivity
import androidx.fragment.app.Fragment
import androidx.fragment.app.FragmentManager
import androidx.lifecycle.Lifecycle
import com.lijianqiang12.silent.utils.MMKVUtils
import com.jeremyliao.liveeventbus.LiveEventBus
import com.lijianqiang12.silent.LiveBus
import com.lijianqiang12.silent.MyConstants
import com.lijianqiang12.silent.R
import com.lijianqiang12.silent.component.activity.me.theme.ThemeInfo

open class BaseActivity() : AppCompatActivity() {
    private var light = true
    private var canReCreate = true

    val themeAttr = mutableListOf(
        ThemeInfo("深邃绿", "最保护眼睛，最舒服的颜色", R.color.custom_color_app_fg_default, R.style.AppTheme_Day),
        ThemeInfo("知乎蓝", "令人觉得可靠、安全", R.color.custom_color_app_fg_1, R.style.AppTheme_Color1),//0084ff
        ThemeInfo("哔哩粉", "( ゜- ゜)つロ乾杯~", R.color.custom_color_app_fg_2, R.style.AppTheme_Color2),//f588a8
        ThemeInfo("酷安绿", "轻快活泼，轻松如酷安", R.color.custom_color_app_fg_3, R.style.AppTheme_Color3),//11b667
        ThemeInfo("静谧灰", "安静、优雅，令人舒适", R.color.custom_color_app_fg_4, R.style.AppTheme_Color4),//#454d66
        ThemeInfo("高贵紫", "如天使一般高贵冷艳", R.color.custom_color_app_fg_5, R.style.AppTheme_Color5),//#272643
        ThemeInfo("樱桃红", "清淡，典雅，温柔、可爱", R.color.custom_color_app_fg_6, R.style.AppTheme_Color6),//#e74645
        ThemeInfo("神秘棕", "健康、向上、无畏、勇敢", R.color.custom_color_app_fg_7, R.style.AppTheme_Color7),//#361d32
        ThemeInfo("淡入黄", "将自己融入背景，低调又沉稳", R.color.custom_color_app_fg_8, R.style.AppTheme_Color8)//##fdebd3
    )

    // 移除带默认参数的构造函数以支持Hilt
    // 如果需要设置light和canReCreate，可以通过方法调用
    fun setLightMode(isLight: Boolean) {
        this.light = isLight
    }

    fun setCanReCreate(canReCreate: Boolean) {
        this.canReCreate = canReCreate
    }

//    private val observer = CloseAllActivityObserver()

    override fun onCreate(savedInstanceState: Bundle?) {


        setTheme(themeAttr[MMKVUtils.getInt(MyConstants.SP_SETTING_THEME, 0)].colorThemeId)

        if (canReCreate) {
            LiveEventBus.get(LiveBus.CHANGE_THEME, String::class.java).observe(this) {
                recreate()
            }
        }

//        when (MMKVUtils.getInt(MyConstants.SP_SETTING_THEME, 0)) {
//            1 -> setTheme(R.style.AppTheme_Night)
//            else -> setTheme(R.style.AppTheme_Day)
//        }

        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.M) {
            window.decorView.systemUiVisibility = window.decorView.systemUiVisibility or View.SYSTEM_UI_FLAG_LAYOUT_FULLSCREEN

            if (light) {
                window.decorView.systemUiVisibility = window.decorView.systemUiVisibility or View.SYSTEM_UI_FLAG_LIGHT_STATUS_BAR
            } else {
                window.decorView.systemUiVisibility = window.decorView.systemUiVisibility or View.SYSTEM_UI_FLAG_LAYOUT_STABLE
            }

            val window: Window = window
            window.clearFlags(WindowManager.LayoutParams.FLAG_TRANSLUCENT_STATUS or WindowManager.LayoutParams.FLAG_TRANSLUCENT_NAVIGATION)
            window.addFlags(WindowManager.LayoutParams.FLAG_DRAWS_SYSTEM_BAR_BACKGROUNDS)
            window.statusBarColor = Color.TRANSPARENT
            window.navigationBarColor = Color.WHITE
        } else {
            window.addFlags(WindowManager.LayoutParams.FLAG_TRANSLUCENT_STATUS)
        }

        super.onCreate(savedInstanceState)

//        LiveBus.getInstance().with(String::class.java).observeForever(observer,false)


//        window.decorView.systemUiVisibility = View.SYSTEM_UI_FLAG_LAYOUT_FULLSCREEN or View.SYSTEM_UI_FLAG_LAYOUT_HIDE_NAVIGATION or View.SYSTEM_UI_FLAG_LAYOUT_STABLE
//
//
//
////        透明状态栏 / 导航栏
//        window.statusBarColor = Color.TRANSPARENT
//        window.navigationBarColor = Color.TRANSPARENT

//        window.setFlags(Window.Fi , FITS_SYSTEM_WINDOWS)

//        PushAgent.getInstance(this).onAppStart()
//        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.M) {
//            window.decorView.systemUiVisibility = window.decorView.systemUiVisibility and (View.SYSTEM_UI_FLAG_LIGHT_STATUS_BAR)
//        }
    }

    override fun getResources(): Resources {

//        val scale = 0.8f

        val res = super.getResources()
        if (res != null) {
            val config = res.configuration
//            LogUtils.d("!!!!!!!!"+config.fontScale)
            if (config != null && config.fontScale > 1.0f) {
                config.fontScale = 1.0f
                res.updateConfiguration(config, res.displayMetrics)
            }
        }
        return res!!

    }

    /**
     * 使用add+show+hide模式加载fragment
     *
     * 默认显示位置[showPosition]的Fragment，最大Lifecycle为Lifecycle.State.RESUMED
     * 其他隐藏的Fragment，最大Lifecycle为Lifecycle.State.STARTED
     *
     *@param containerViewId 容器id
     *@param showPosition  fragments
     *@param fragmentManager FragmentManager
     *@param fragments  控制显示的Fragments
     */
    protected fun loadFragmentsTransaction(@IdRes containerViewId: Int, showPosition: Int, fragmentManager: FragmentManager, fragments: List<Fragment>) {
        if (fragments.isNotEmpty()) {
            fragmentManager.beginTransaction().apply {
                for (index in fragments.indices) {
                    val fragment = fragments[index]
                    add(containerViewId, fragment, fragment.javaClass.name)
                    if (showPosition == index) {
                        setMaxLifecycle(fragment, Lifecycle.State.RESUMED)
                        ObjectAnimator.ofFloat(fragment.view, "alpha", 0.0f, 1.0f).setDuration(500).start()
                    } else {
                        hide(fragment)
                        setMaxLifecycle(fragment, Lifecycle.State.STARTED)
                    }
                }
            }.commit()

        } else {
            throw IllegalStateException("fragments must not empty")
        }
    }

    /** 显示需要显示的Fragment[showFragment]，并设置其最大Lifecycle为Lifecycle.State.RESUMED。
     *  同时隐藏其他Fragment,并设置最大Lifecycle为Lifecycle.State.STARTED
     * @param fragmentManager
     * @param showFragment
     */
    protected fun showHideFragmentTransaction(fragmentManager: FragmentManager, showFragment: Fragment) {
        fragmentManager.beginTransaction().apply {
            show(showFragment)
            setMaxLifecycle(showFragment, Lifecycle.State.RESUMED)
            ObjectAnimator.ofFloat(showFragment.view, "alpha", 0.0f, 1.0f).setDuration(500).start()
            //获取其中所有的fragment,其他的fragment进行隐藏
            val fragments = fragmentManager.fragments
            for (fragment in fragments) {
                if (fragment != showFragment) {
                    hide(fragment)
                    setMaxLifecycle(fragment, Lifecycle.State.STARTED)
                }
            }
        }.commit()

    }


    @SuppressLint("MissingSuperCall")
    override fun onSaveInstanceState(outState: Bundle) {
//        super.onSaveInstanceState(outState)//防止activity销毁时fragment没销毁，从而造成恢复时重叠
    }

    override fun onResume() {
        super.onResume()
//        MobclickAgent.onResume(this)

        //获取刷新率并设置刷最高新率,11以后才有该api，仅能在activity中有效，bottomsheetdialog弹出时又恢复为60
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.R) {
            val windowManager = getSystemService(Context.WINDOW_SERVICE) as WindowManager
            val maxRefreshRate = windowManager.defaultDisplay.supportedRefreshRates.max()
            val params = window.attributes
            params.preferredRefreshRate = maxRefreshRate
            window.attributes = params
        }
    }

//    override fun onPause() {
//        super.onPause()
//        MobclickAgent.onPause(this)
//}

//    override fun onDestroy() {
//        LiveBus.getInstance().with(String::class.java).removeObserver(observer)
//        super.onDestroy()
//    }

//    internal inner class CloseAllActivityObserver : Observer<String> {
//        override fun onChanged(t: String) {
//            if (t == LiveBus.CLOSE_ALL_ACTIVITY) {
//                finish()
//            }
//        }
//    }
}
