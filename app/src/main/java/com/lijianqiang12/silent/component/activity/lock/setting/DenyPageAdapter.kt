package com.lijianqiang12.silent.component.activity.lock.setting

import android.view.View
import android.widget.ImageView
import android.widget.TextView
import androidx.constraintlayout.widget.ConstraintLayout
import com.chad.library.adapter.base.BaseQuickAdapter
import com.chad.library.adapter.base.module.LoadMoreModule
import com.chad.library.adapter.base.viewholder.BaseViewHolder
import com.lijianqiang12.silent.R
import com.lijianqiang12.silent.utils.getAppIcon
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.GlobalScope
import kotlinx.coroutines.launch
import kotlinx.coroutines.withContext

class DenyPageAdapter(layoutRes: Int, list: MutableList<TheDenyPage2>) : BaseQuickAdapter<TheDenyPage2, BaseViewHolder>(layoutRes, list), LoadMoreModule {

    override fun convert(holder: BaseViewHolder, item: TheDenyPage2) {
        GlobalScope.launch(Dispatchers.IO) {
            val pages = item.pages
            if (pages.isNotEmpty()) {
                val page = pages.first()
                val image = getAppIcon(page.pkg, page.activity)
                withContext(Dispatchers.Main) {
                    holder.getView<ImageView>(R.id.iv_item_deny_page_icon).setImageDrawable(image)
                }
            }
        }

        holder.getView<TextView>(R.id.tv_item_deny_page_name).text = "${item.name}（${if (item.type == DENY_PAGE_TYPE_FAST) "Fast" else "Custom"}）"
        holder.getView<TextView>(R.id.tv_item_deny_page_pkg).text = item.pages.toString()
//        holder.getView<TextView>(R.id.tv_item_deny_page_activity).text = item.activity

        if (item.valid) {
            holder.getView<ConstraintLayout>(R.id.cl_deny_page_cover_red).visibility = View.VISIBLE
            holder.getView<ConstraintLayout>(R.id.cl_deny_page_cover_grey).visibility = View.GONE
        } else {
            holder.getView<ConstraintLayout>(R.id.cl_deny_page_cover_red).visibility = View.GONE
            holder.getView<ConstraintLayout>(R.id.cl_deny_page_cover_grey).visibility = View.VISIBLE
        }


//        Glide.with(context).load(item.icon).into(holder.getView(R.id.iv_item_fast_deny_page_example_icon))
//        holder.getView<TextView>(R.id.tv_item_fast_deny_page_example_name).text = item.name
//        holder.getView<TextView>(R.id.tv_item_fast_deny_page_example_pkg).text = item.pkg
//        holder.getView<TextView>(R.id.tv_item_fast_deny_page_example_activity).text = item.activity


//        viewHolder.getView<TextView>(R.id.tv_name).text = item.username
//
//
//        viewHolder.getView<TextView>(R.id.tv_word).text = item.word
//
//        when (item.vipState) {
//            0 -> {
//                viewHolder.getView<TextView>(R.id.tv_vip).visibility = View.INVISIBLE
//            }
//            1 -> {
//                viewHolder.getView<TextView>(R.id.tv_vip).visibility = View.VISIBLE
//                viewHolder.getView<TextView>(R.id.tv_vip).text = "VIP"
//                viewHolder.getView<TextView>(R.id.tv_vip).setTextColor(context.resources.getColor(R.color.colorBackText))
//                viewHolder.getView<TextView>(R.id.tv_vip).setBackgroundResource(R.drawable.shape_get_vip_gradient_2dp)
//            }
//            2 -> {
//                viewHolder.getView<TextView>(R.id.tv_vip).visibility = View.VISIBLE
//                viewHolder.getView<TextView>(R.id.tv_vip).text = "SVIP"
//                viewHolder.getView<TextView>(R.id.tv_vip).setTextColor(context.resources.getColor(R.color.colorWhiteText))
//                viewHolder.getView<TextView>(R.id.tv_vip).setBackgroundResource(R.drawable.shape_get_vip_black_gradient_2dp)
//            }
//        }
//
//
//        viewHolder.getView<TextView>(R.id.tv_brand).text = "${item.brand}  ${item.model}"
//        viewHolder.getView<TextView>(R.id.tv_punch_time).text = TimeUtil.formatTimeLikeQQ(item.time)
//
//        when (item.gender) {
//            0 -> {
//                viewHolder.getView<ImageView>(R.id.iv_gender).visibility = View.GONE
//            }
//            1 -> {
//                viewHolder.getView<ImageView>(R.id.iv_gender).visibility = View.VISIBLE
//                viewHolder.getView<ImageView>(R.id.iv_gender).setImageResource(R.drawable.ic_male)
//
//            }
//            2 -> {
//                viewHolder.getView<ImageView>(R.id.iv_gender).visibility = View.VISIBLE
//                viewHolder.getView<ImageView>(R.id.iv_gender).setImageResource(R.drawable.ic_female)
//            }
//        }
    }
}