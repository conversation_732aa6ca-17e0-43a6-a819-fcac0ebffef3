package com.lijianqiang12.silent.component.service

import android.accessibilityservice.AccessibilityService
import android.accessibilityservice.GestureDescription
import android.content.Intent
import android.graphics.Path
import android.os.Build
import android.util.Log
import android.view.accessibility.AccessibilityEvent
import android.view.accessibility.AccessibilityNodeInfo
import com.blankj.utilcode.util.AppUtils
import com.blankj.utilcode.util.LogUtils
import com.blankj.utilcode.util.ScreenUtils
import com.jeremyliao.liveeventbus.LiveEventBus
import com.lijianqiang12.silent.LiveBus
import com.lijianqiang12.silent.MyConstants
import com.lijianqiang12.silent.TheApplication
import com.lijianqiang12.silent.component.service.background_service.BackgroundServiceMinimal
import com.lijianqiang12.silent.component.service.windows.FloatWindowOfNotice
import com.lijianqiang12.silent.component.service.windows.FloatWindowOfProtectStart
import com.lijianqiang12.silent.utils.MMKVUtils
import com.lijianqiang12.silent.utils.MyRomUtils
import com.lijianqiang12.silent.utils.PermissionUtil
import com.lijianqiang12.silent.utils.ServiceUtil
import com.lijianqiang12.silent.utils.isLockRunning
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.GlobalScope
import kotlinx.coroutines.delay
import kotlinx.coroutines.launch
import kotlinx.coroutines.withContext


class MyAccessibilityService : AccessibilityService() {


    private lateinit var noticeFloatWindow: FloatWindowOfNotice
    private lateinit var mStartFloat: FloatWindowOfProtectStart

    override fun onInterrupt() {
        Log.d("AccessibilityService", "onInterrupt")
        TheApplication.getInstance().globalParams.accessibilityService = null
    }

    override fun onUnbind(intent: Intent?): Boolean {
        // 在这里执行清理操作
        TheApplication.getInstance().globalParams.accessibilityService = null
        // 调用父类的onUnbind方法是好的做法，尽管在当前的API级别它只是默认返回false
        return super.onUnbind(intent)
    }

    override fun onCreate() {
        super.onCreate()
        LogUtils.d("AccessibilityService onCreate")

        TheApplication.getInstance().globalParams.accessibilityService = this@MyAccessibilityService

        mStartFloat = FloatWindowOfProtectStart(this@MyAccessibilityService)

        noticeFloatWindow = FloatWindowOfNotice(this@MyAccessibilityService)

        ServiceUtil.checkAndStartService(this@MyAccessibilityService, BackgroundServiceMinimal::class.java)


    }

    override fun onStartCommand(intent: Intent, flags: Int, startId: Int): Int {
        LogUtils.d("AccessibilityService onStartCommand")
        return START_STICKY
    }

//    override fun onKeyEvent(event: KeyEvent?): Boolean {
//        Log.d("Accessibility KeyEvent", event.toString())
//        return super.onKeyEvent(event)
//    }

    override fun onAccessibilityEvent(originalEvent: AccessibilityEvent?) {
        //经常丢失，在这里加固一下
        TheApplication.getInstance().globalParams.accessibilityService = this@MyAccessibilityService


        //先copy再处理，否则event会变，原因未知
        originalEvent?.let { oriEvent ->
            //深拷贝一下，否则会有线程安全问题
            val event = if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.R) {
                AccessibilityEvent(oriEvent)
            } else {
                AccessibilityEvent.obtain(oriEvent)
            }

            GlobalScope.launch(Dispatchers.IO) {


                if (event.packageName == null || event.className == null) {
                    return@launch
                }

                Log.d("Accessibility event", event.toString())

                //信息太多，只保留点击事件
                if (event.packageName != AppUtils.getAppPackageName() || event.eventType == AccessibilityEvent.TYPE_VIEW_CLICKED) {
                    LiveEventBus.get(LiveBus.DEBUG_MSG, String::class.java)
                        .post("${event.eventType} ${event.packageName} ${event.className} t:${event.text} c:${event.contentDescription.toString()}")
                }

                //屏蔽卸载
                if (MMKVUtils.getBoolean(MyConstants.SP_SETTING_DENY_UNINSTALL, false)) {
                    if (
                    //华为桌面卸载
                        (event.eventType == AccessibilityEvent.TYPE_WINDOW_STATE_CHANGED && event.packageName == "com.huawei.android.launcher" && event.className == "android.app.AlertDialog" && (event.text.toString()
                            .contains("卸载") || event.text.toString().contains("Uninstall")) && event.text.toString().contains(AppUtils.getAppName()))
                        //华为设置进入详情页
                        || (event.eventType == AccessibilityEvent.TYPE_VIEW_CLICKED && event.packageName == "com.android.settings" && event.className == "android.widget.LinearLayout" && event.text.toString()
                            .contains(AppUtils.getAppName()))
                        //华为桌面直接进入
                        || (event.eventType == AccessibilityEvent.TYPE_WINDOW_STATE_CHANGED && event.packageName == "com.android.settings" && event.className == "com.android.settings.applications.InstalledAppDetailsTop" && event.text.toString()
                            .contains("应用信息") && exploreNodes(rootInActiveWindow).contains(AppUtils.getAppName()))
                        //华为无障碍辅助页面
                        || (event.eventType == AccessibilityEvent.TYPE_VIEW_CLICKED && event.packageName == "com.android.settings" && event.className == "android.widget.TableLayout" && event.text.toString()
                            .contains("😘【${AppUtils.getAppName()}】😘") && event.text.toString().contains("已开启"))
                        //华为从通知进入无障碍辅助页面
                        || (event.eventType == AccessibilityEvent.TYPE_WINDOW_STATE_CHANGED && event.packageName == "com.android.settings" && event.className == "com.android.settings.CleanSubSettings" && event.text.toString()
                            .contains("😘【${AppUtils.getAppName()}】😘"))
                        //华为无障碍设置页面“恢复默认设置按钮"
                        || (event.eventType == AccessibilityEvent.TYPE_VIEW_CLICKED && event.packageName == "com.android.settings" && event.className == "android.widget.Button" &&
                                event.contentDescription.toString().contains("更多选项"))

                        //小米桌面卸载
                        || (event.eventType == AccessibilityEvent.TYPE_VIEW_CLICKED && event.packageName == "com.miui.home" && event.className == "com.miui.home.launcher.uninstall.DeleteDialog" && (event.text.toString()
                            .contains("卸载") || event.text.toString().contains("Uninstall")) && event.text.toString().contains(AppUtils.getAppName()))
                        //小米设置进入详情页
                        || (event.eventType == AccessibilityEvent.TYPE_VIEW_CLICKED && event.packageName == "com.miui.securitycenter" && event.className == "android.widget.LinearLayout" && event.text.toString()
                            .contains(AppUtils.getAppName()))
                        //小米桌面直接进入详情页
                        || (event.eventType == AccessibilityEvent.TYPE_WINDOW_STATE_CHANGED && event.packageName == "com.miui.securitycenter" && event.className == "com.miui.appmanager.ApplicationsDetailsActivity" && event.text.toString()
                            .contains("应用信息") && exploreNodes(rootInActiveWindow).contains(AppUtils.getAppName()))
                        //小米无障碍辅助页面
                        || (event.eventType == AccessibilityEvent.TYPE_VIEW_CLICKED && event.packageName == "com.android.settings" && event.className == "android.widget.LinearLayout" && event.text.toString()
                            .contains("😘【${AppUtils.getAppName()}】😘") && event.text.toString().contains("已开启"))
                        //小米从通知进入无障碍辅助页面
                        || (event.eventType == AccessibilityEvent.TYPE_WINDOW_STATE_CHANGED && event.packageName == "com.android.settings" && event.className == "com.android.settings.SubSettings" && event.text.toString()
                            .contains("😘【${AppUtils.getAppName()}】😘"))
                        //小米从market卸载
                        || (event.eventType == AccessibilityEvent.TYPE_VIEW_CLICKED && event.packageName == "com.xiaomi.market" && event.className == "android.widget.LinearLayout" && event.text.toString()
                            .contains("应用卸载"))
                        //小米自启动管理列表
                        || (event.eventType == AccessibilityEvent.TYPE_WINDOW_STATE_CHANGED && event.packageName == "com.miui.securitycenter" && event.className == "com.miui.permcenter.autostart.AutoStartManagementActivity")

                        //OPPO桌面卸载
                        || (event.eventType == AccessibilityEvent.TYPE_WINDOW_STATE_CHANGED && event.packageName == "com.android.launcher" && event.className == "androidx.appcompat.app.AlertDialog" && event.text.toString()
                            .contains("卸载“${AppUtils.getAppName()}”"))

                        //OPPO设置进入详情页
                        || (event.eventType == AccessibilityEvent.TYPE_VIEW_CLICKED && event.packageName == "com.android.settings" && event.className == "android.widget.FrameLayout" && event.text.toString()
                            .contains(AppUtils.getAppName()))
                        //OPPO无障碍辅助页面
                        || (event.eventType == AccessibilityEvent.TYPE_VIEW_CLICKED && event.packageName == "com.android.settings" && event.className == "android.widget.LinearLayout" && event.text.toString()
                            .contains("😘【${AppUtils.getAppName()}】😘") && event.text.toString().contains("已开启"))
                        //OPPO从通知进入无障碍辅助页面
                        || (event.eventType == AccessibilityEvent.TYPE_WINDOW_STATE_CHANGED && event.packageName == "com.android.settings" && event.className == "com.android.settings.SubSettings" && event.text.toString()
                            .contains("😘【${AppUtils.getAppName()}】😘"))

                        //VIVO桌面卸载
                        || (event.eventType == AccessibilityEvent.TYPE_WINDOW_STATE_CHANGED && event.packageName == "com.bbk.launcher2" && event.className == "android.app.AlertDialog" && (event.text.toString()
                            .contains("卸载") || event.text.toString().contains("Uninstall")) && event.text.toString().contains(AppUtils.getAppName()))
                        //VIVO设置进入详情页
                        || (event.eventType == AccessibilityEvent.TYPE_VIEW_CLICKED && event.packageName == "com.android.settings" && event.className == "android.widget.RelativeLayout" && event.text.toString()
                            .contains(AppUtils.getAppName()))
                        //VIVO桌面直接进入详情页
                        || (event.eventType == AccessibilityEvent.TYPE_WINDOW_STATE_CHANGED && event.packageName == "com.android.settings" && event.className == "com.vivo.settings.applications.InstalledAppDetailsTop" && event.text.toString()
                            .contains("应用信息") && exploreNodes(rootInActiveWindow).contains(AppUtils.getAppName()))
                        //VIVO无障碍辅助页面
                        || (event.eventType == AccessibilityEvent.TYPE_VIEW_CLICKED && event.packageName == "com.android.settings" && event.className == "android.widget.LinearLayout" && event.text.toString()
                            .contains("😘【${AppUtils.getAppName()}】😘") && event.text.toString().contains("已开启"))
                        //VIVO从通知进入无障碍辅助页面
                        || (event.eventType == AccessibilityEvent.TYPE_WINDOW_STATE_CHANGED && event.packageName == "com.android.settings" && event.className == "com.vivo.settings.VivoSubSettings" && event.text.toString()
                            .contains("😘【${AppUtils.getAppName()}】😘"))
                    ) {
                        withContext(Dispatchers.Main) {
                            noticeFloatWindow.showDenyWindow("您设置了屏蔽卸载操作", 2000, allowSmallWindowToast = false) {}
                        }
                        performGlobalAction(GLOBAL_ACTION_BACK)
                        performGlobalAction(GLOBAL_ACTION_HOME)
                        return@launch
                    } else if (event.eventType == AccessibilityEvent.TYPE_VIEW_CLICKED &&
                        event.packageName == "com.android.launcher" &&
                        event.className == "android.widget.FrameLayout" && event.text.toString().contains("应用详情")
                    ) {
                        withContext(Dispatchers.Main) {
                            noticeFloatWindow.showDenyWindow("您设置了屏蔽卸载操作，\n无法直接从桌面进入app详情页，\n请从系统设置进入", 3000, allowSmallWindowToast = false) {}
                        }
                        performGlobalAction(GLOBAL_ACTION_BACK)
                        performGlobalAction(GLOBAL_ACTION_HOME)
                        return@launch
                    } else {
                        //OPPO桌面直接进入设置详情页（全部app都会触发）
                        val rootNode = rootInActiveWindow
                        rootNode?.let { nodeInfo ->
                            var str = exploreNodes(nodeInfo)
                            Log.d("AccessibilityNodeInfo", "resultString: $str")
                            str = str.replace(" ", "")
                            //从详情页进入卸载页
                            if ((event.eventType == AccessibilityEvent.TYPE_WINDOW_STATE_CHANGED &&
                                        event.packageName == "com.oplus.appdetail" && event.className == "com.oplus.appdetail.model.uninstall.UninstallPackageActivity" &&
                                        str.contains(AppUtils.getAppName()))

                                || str.contains("卸载“${AppUtils.getAppName()}”")
                                || str.contains("卸载${AppUtils.getAppName()}")
                                || str.contains("应用详情${AppUtils.getAppName()}")
                                || str.contains("应用信息${AppUtils.getAppName()}")
                                || str.contains("😘【${AppUtils.getAppName()}】😘😘【${AppUtils.getAppName()}】😘")
                                || str.contains("😘如果提示被应用遮挡无法开启，请检查是否开启了悬浮球或全面屏手势，如果是，请暂时关闭；")
                            ) {
                                withContext(Dispatchers.Main) {
                                    noticeFloatWindow.showDenyWindow("您设置了屏蔽卸载操作", 2000) {}
                                }
                                performGlobalAction(GLOBAL_ACTION_BACK)
                                performGlobalAction(GLOBAL_ACTION_HOME)
                                return@launch
                            }
                        }
                    }
                }

                //暂停
                if (MMKVUtils.getLong(MyConstants.SP_KEY_FORCE_UNLOCK_PAUSE, 0L) > System.currentTimeMillis()) return@launch

                //屏蔽语音助手
                val denyAssist = MMKVUtils.getInt(MyConstants.SP_SETTING_DENY_ASSIST_NEW, 0)
                if ((1 == denyAssist && isLockRunning()) || 2 == denyAssist) {
                    if (mutableListOf(
                            "com.huawei.vassistant",
                            "com.miui.voiceassist",
                            "com.coloros.speechassist",
                            "com.heytap.speechassist",
                            "com.vivo.assistant",
                            "com.vivo.agent",
                            "com.vivo.ai.copilot",
                        ).contains(event.packageName)
                    ) {
                        withContext(Dispatchers.Main) {
                            noticeFloatWindow.showDenyWindow("您设置了屏蔽语音助手", 2000) {}
                        }
                        performGlobalAction(GLOBAL_ACTION_BACK)
                        performGlobalAction(GLOBAL_ACTION_HOME)
                        return@launch
                    }
                }


                //屏蔽电源键
                val denyShutdown = MMKVUtils.getInt(MyConstants.SP_SETTING_DENY_SHUTDOWN_NEW, 0)
                if ((1 == denyShutdown && isLockRunning()) || 2 == denyShutdown) {

                    if ((MyRomUtils.isHuaweiSeries() && event.packageName == "android" && mutableListOf("com.huawei.PowerOffWindow").contains(event.className)) ||
                        //被最底下的通用替代
//                        (MyRomUtils.isHuawei() && event.packageName == "android" && mutableListOf("android.widget.RelativeLayout").contains(event.className)
//                                && event.contentDescription != null && (event.contentDescription!!.contains("关机") || event.contentDescription!!.contains(
//                            "重启"
//                        ))) ||
                        (MyRomUtils.isXiaomi() && event.packageName == "miui.systemui.plugin" && mutableListOf(
                            "android.app.Dialog",
                            "android.widget.FrameLayout"
                        ).contains(event.className) && event.contentDescription == null && (event.text.toString().contains("上滑关机") || event.text.toString()
                            .contains("下滑重启"))) ||

                        (MyRomUtils.isOppo() && event.packageName == "com.android.systemui" && mutableListOf("com.android.systemui.globalactions.GlobalActionsDialog\$ActionsDialog").contains(
                            event.className
                        )) ||
                        (MyRomUtils.isOppo() && event.packageName == "com.android.systemui" && mutableListOf("com.oplus.systemui.shutdown.OplusGlobalActionsDialog\$ActionsDialog").contains(
                            event.className
                        )) ||
                        (MyRomUtils.isVivo() && event.packageName == "com.android.systemui" && mutableListOf("com.vivo.systemui.statusbar.globalactions.VivoShutdownActionDialog").contains(
                            event.className
                        )) ||
                        (MyRomUtils.isVivo() && event.packageName == "com.vivo.systemuiplugin" && mutableListOf("com.vivo.systemuiplugin.systemui.statusbar.globalactions.VivoShutdownActionDialog").contains(
                            event.className
                        )) ||
                        (MyRomUtils.isVivo() && event.packageName == "com.android.systemui" && mutableListOf("com.vivo.systemui.statusbar.globalactions.VivoGlobalActionsDialog").contains(
                            event.className
                        )) ||
                        (event.packageName == "android" && mutableListOf("android.widget.RelativeLayout").contains(event.className)
                                && event.contentDescription != null && (event.contentDescription!!.contains("关机") || event.contentDescription!!.contains(
                            "重启"
                        ))) ||
                        (event.packageName == "com.android.systemui" && mutableListOf("android.widget.RelativeLayout").contains(event.className) && (event.text!!.contains(
                            "关机"
                        ) || event.text.contains("重启")))

                    ) {
                        withContext(Dispatchers.Main) {
                            noticeFloatWindow.showDenyWindow("您设置了屏蔽电源键", 2000) {}
                        }
                        performGlobalAction(GLOBAL_ACTION_BACK)
                        performGlobalAction(GLOBAL_ACTION_HOME)
                        return@launch
                    }
                }

                //屏蔽侧边栏
//                        if (MMKVUtils.getBoolean(MyConstants.SP_SETTING_DENY_FAST_OPEN, false)) {
//                            if (
//                                (event.packageName == "com.miui.securitycenter" && mutableListOf("android.widget.LinearLayout").contains(event.className))
//                                || (event.packageName == "com.huawei.hwdockbar" && mutableListOf("android.widget.RelativeLayout").contains(event.className))
//                                || (event.packageName == "com.hihonor.hndockbar" && mutableListOf("android.widget.RelativeLayout").contains(event.className))
//                                || (event.packageName == "com.coloros.smartsidebar" && mutableListOf("android.widget.RelativeLayout").contains(event.className))
//                                || (event.packageName == "com.vivo.upslide" && mutableListOf("android.widget.FrameLayout").contains(event.className))
//                                || (event.packageName == "com.samsung.android.app.cocktailbarservice" && mutableListOf("android.widget.FrameLayout").contains(
//                                    event.className
//                                ))
//                                || (event.packageName == "com.coloros.smartsidebar" && mutableListOf("android.view.ViewGroup").contains(event.className))
//                            ) {
//                                LogUtils.d("AccessibilityService DENY_FAST_OPEN")
//                                tapToCloseFastOpenDialog()
//                                performGlobalAction(GLOBAL_ACTION_HOME)
//                                withContext(Dispatchers.Main) {
//                                    noticeTranslateFloatWindow.showDenyWindow("您设置了屏蔽侧边栏")
//                                }
//                                delay(1000)
//                                withContext(Dispatchers.Main) {
//                                    noticeTranslateFloatWindow.hideDenyWindow()
//                                    noticeTranslateFloatWindow.showClickableDenyWindow("您设置了屏蔽侧边栏", true)
//                                }
//                                return@launch
//                            }
//                        }

                //屏蔽小窗 开启无障碍后，已经能识别小窗和分屏，就不需要重复判定了
//                        if (MMKVUtils.getBoolean(MyConstants.SP_SETTING_DENY_SMALL_WINDOW, false)) {
//
//                            val isMyself = event.packageName == TheApplication.getInstance().packageName
//                            var isWhite = false
//
//                            BackgroundService.runningWhiteAppList.forEach {
//                                //event.packageName加上tostring就会偶发报错空指针，距离上方一行打印时间不到1秒，原因未知、（线程安全问题）
//                                if (it.pkg == event.packageName) {
//                                    isWhite = true
//                                }
//                            }
//
//
//                            if (!isMyself && !isWhite && isLaunchedApp) {
//                                withContext(Dispatchers.Main) {
//                                    noticeFloatWindow.showDenyWindow("您设置了屏蔽小窗", 2000) {}
//                                }
//                                performGlobalAction(GLOBAL_ACTION_HOME)
//                                return@launch
//                            }
//                        }

                //屏蔽上拉菜单
                if (isLockRunning() && MyRomUtils.isVivo() && MMKVUtils.getBoolean(MyConstants.SP_SETTING_DENY_UP, false)) {
                    if (mutableListOf("com.vivo.upslide").contains(event.packageName)) { //放在最后，因为和侧边栏冲突，vivo都有com.vivo.upslide，而侧边栏还需要检测class名称，范围更小
                        withContext(Dispatchers.Main) {
                            noticeFloatWindow.showDenyWindow("您设置了禁止上拉菜单栏", 2000) {}
                        }
                        performGlobalAction(GLOBAL_ACTION_BACK)
                        performGlobalAction(GLOBAL_ACTION_HOME)
                        return@launch
                    }
                }

                //屏蔽下拉菜单
                //miui从右侧下拉控制中心没有明显标志，不好限制，刚开机的时候也可以限制
                if (MMKVUtils.getInt(MyConstants.SP_SETTING_DENY_DROPDOWN_NEW, 0) == 1) {
                    if (isLockRunning() &&
                        (event.packageName == "com.android.systemui" &&
                                mutableListOf("android.widget.FrameLayout").contains(event.className) &&
                                (event.text.toString().contains("通知栏") || event.text.toString().contains("控制中心") || event.text.toString().contains("打开设置")))
                    ) {
                        withContext(Dispatchers.Main) {
                            noticeFloatWindow.showDenyWindow("您设置了禁止下拉菜单栏", 2000) {}
                        }
                        performGlobalAction(GLOBAL_ACTION_BACK)
                        performGlobalAction(GLOBAL_ACTION_HOME)
                        return@launch
                    }
                }


                //屏蔽编辑开关
                val denyEditButton = MMKVUtils.getInt(MyConstants.SP_SETTING_DENY_EDIT_BUTTON_NEW, 0)
                if ((1 == denyEditButton && isLockRunning()) || 2 == denyEditButton) {
                    var isEditPage = false
                    if (MyRomUtils.isHuaweiSeries() &&
                        event.packageName == "com.android.systemui" &&
                        event.className == "android.widget.LinearLayout" &&
                        event.text.toString().contains("快捷设置编辑器")
                    ) {
                        isEditPage = true
                    } else if (MyRomUtils.isXiaomi() &&
                        event.packageName == "miui.systemui.plugin" &&
                        event.className == "android.widget.ImageView" &&
                        event.text.toString().contains("编辑")
                    ) {
                        isEditPage = true
                    } else if (MyRomUtils.isOppo() &&
                        event.packageName == "com.android.systemui" &&
                        mutableListOf(
                            "android.widget.ImageButton",
                            "android.widget.FrameLayout"
                        ).contains(event.className) &&
                        event.text.toString().contains("编辑开关")
                    ) {
                        isEditPage = true
                    } else if (MyRomUtils.isVivo() &&
                        event.packageName == "com.vivo.systemuiplugin" &&
                        event.className == "android.widget.Button" &&
                        event.contentDescription != null &&
                        event.contentDescription!!.contains("编辑控制中心") //注意这里用的是describeContents，不是text
                    ) {
                        isEditPage = true
                    } else if ((event.packageName == "com.android.systemui" &&
                                mutableListOf(
                                    "android.app.Dialog",
                                    "android.widget.FrameLayout",
                                    "android.widget.LinearLayout"
                                ).contains(event.className))
                        && (event.text.toString().contains("编辑开关") ||
                                event.text.toString().contains("编辑快捷开关") ||
                                event.text.toString().contains("快捷设置编辑器") ||
                                event.text.toString().contains("编辑控制中心")
                                )
                        && event.text.toString().length < 100
                    ) {
                        isEditPage = true
                    }

                    if (isEditPage) {
                        withContext(Dispatchers.Main) {
                            noticeFloatWindow.showDenyWindow("您设置了屏蔽编辑开关", 2000) {}
                        }
                        performGlobalAction(GLOBAL_ACTION_BACK)
                        performGlobalAction(GLOBAL_ACTION_HOME)
                        return@launch
                    }
                }

                //屏蔽正在运行的服务
                if (MyRomUtils.isOppo() || MyRomUtils.isVivo()) {
                    val denyCloseRunningService = MMKVUtils.getInt(MyConstants.SP_SETTING_DENY_CLOSE_RUNNING_SERVICE_NEW, 0)
                    if ((1 == denyCloseRunningService && isLockRunning()) || 2 == denyCloseRunningService) {
                        if ((event.packageName == "com.android.systemui" && mutableListOf(
                                "android.app.Dialog",
                                "android.widget.FrameLayout",
                                "android.widget.LinearLayout",
                                "com.android.systemui.statusbar.phone.SystemUIDialog"
                            ).contains(event.className))
                            && event.text.toString().contains("正在运行的服务")
                        ) {
                            withContext(Dispatchers.Main) {
                                noticeFloatWindow.showDenyWindow("您设置了屏蔽正在运行的服务", 2000) {}
                            }
                            performGlobalAction(GLOBAL_ACTION_BACK)
                            performGlobalAction(GLOBAL_ACTION_HOME)
                            return@launch
                        }
                    }
                }
            }
        }
    }


    override fun onServiceConnected() {
        super.onServiceConnected()

        LogUtils.d("AccessibilityService onServiceConnected")
        TheApplication.getInstance().globalParams.accessibilityService = this@MyAccessibilityService

        if (!TheApplication.getInstance().globalParams.checkServiceInitOk) {
            //防止重启手机后快速下拉菜单栏，这样即便锁机了，菜单栏也不会收起
            performGlobalAction(GLOBAL_ACTION_BACK)
            performGlobalAction(GLOBAL_ACTION_HOME)

            mStartFloat.showProtectWindow()
            GlobalScope.launch(Dispatchers.IO) {
                delay(15000) //15秒后或加载完成后就会消失
                withContext(Dispatchers.Main) {
                    mStartFloat.hideProtectWindow()
                }
            }
        }

        LiveEventBus.get(LiveBus.START_OVER, String::class.java).observeStickyForever {
            mStartFloat.hideProtectWindow()
        }

        Log.d("MyAccessibility", "onServiceConnected")

    }

    private fun exploreNodes(nodeInfo: AccessibilityNodeInfo?, depth: Int = 3): String {
        if (depth <= 0 || nodeInfo == null) return ""

        val text = nodeInfo.text
        var resultString = ""
        if (text != null && text.isNotEmpty()) {
            resultString += text
        }

        // 遍历子节点
        for (i in 0 until nodeInfo.childCount) {
            val childNode = nodeInfo.getChild(i) ?: continue
            val subStr = exploreNodes(childNode, depth = depth - 1)
            resultString += subStr
            childNode.recycle() // 回收节点以节省资源
        }

        return resultString
    }


    private fun tapToCloseFastOpenDialog() {
        tap(ScreenUtils.getScreenWidth() / 2.toFloat(), ScreenUtils.getScreenHeight() / 2.toFloat())
    }

    /**
     * 模拟点击事件
     *
     * @param x
     * @param y
     */
    private fun tap(x: Float, y: Float) {
        Log.e("Tag", "模拟点击事件")

        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.N) {
            val builder: GestureDescription.Builder = GestureDescription.Builder()
            val p = Path()
            p.moveTo(x, y)
            builder.addStroke(GestureDescription.StrokeDescription(p, 0L, 500L))
            val gesture: GestureDescription = builder.build()
            dispatchGesture(gesture, object : GestureResultCallback() {
                override fun onCompleted(gestureDescription: GestureDescription?) {
                    super.onCompleted(gestureDescription)
                    Log.e("Tag", "onCompleted: 完成..........")
                }

                override fun onCancelled(gestureDescription: GestureDescription?) {
                    super.onCancelled(gestureDescription)
                    Log.e("Tag", "onCompleted: 取消..........")
                }
            }, null)

        }
    }


    companion object {

        fun getInstance(): AccessibilityService? {
            return TheApplication.getInstance().globalParams.accessibilityService
        }

        fun isAccessibilityActive(): Boolean {
            return getInstance() != null && PermissionUtil.isAccessibilitySettingsOn(MyConstants.ACCESS_ABILITY_NAME, TheApplication.getInstance())
        }


    }


}