package com.lijianqiang12.silent.component.service.windows

import android.annotation.SuppressLint
import android.app.Service
import android.content.Context
import android.content.Intent
import android.content.pm.ActivityInfo
import android.content.res.AssetFileDescriptor
import android.graphics.PixelFormat
import android.media.MediaPlayer
import android.os.Build
import android.os.PowerManager
import android.view.Gravity
import android.view.LayoutInflater
import android.view.View
import android.view.WindowManager
import androidx.core.view.children
import androidx.gridlayout.widget.GridLayout
import androidx.recyclerview.widget.GridLayoutManager
import com.blankj.utilcode.util.*
import com.bumptech.glide.Glide
import com.bumptech.glide.load.engine.DiskCacheStrategy
import com.bumptech.glide.load.resource.drawable.DrawableTransitionOptions
import com.jeremyliao.liveeventbus.LiveEventBus
import com.lijianqiang12.silent.*
import com.lijianqiang12.silent.component.activity.*
import com.lijianqiang12.silent.component.activity.lock.whiteapp.AppInfo
import com.lijianqiang12.silent.component.activity.me.vip.FROM_WHERE
import com.lijianqiang12.silent.component.activity.me.vip.VIP2Activity
import com.lijianqiang12.silent.component.service.MyAccessibilityService
import com.lijianqiang12.silent.component.service.background_service.DeleteWhiteAppTemp
import com.lijianqiang12.silent.data.model.db.LockConfig
import com.lijianqiang12.silent.data.model.db.LockHistory
import com.lijianqiang12.silent.data.model.db.WhiteApp
import com.lijianqiang12.silent.data.repository.LockRepository
import com.lijianqiang12.silent.utils.*
import kotlinx.android.synthetic.main.item_white_noise.view.*
import kotlinx.android.synthetic.main.layout_lock_window_land.view.*
import kotlinx.android.synthetic.main.layout_lock_window_sub_apps.view.*
import kotlinx.android.synthetic.main.layout_lock_window_sub_light.view.*
import kotlinx.android.synthetic.main.layout_lock_window_sub_music.view.*
import kotlinx.android.synthetic.main.layout_lock_window_sub_stop_land.view.*
import kotlinx.coroutines.*
import java.text.SimpleDateFormat
import java.util.*


class FloatWindowOfLock(
    val context: Context,
    var runningWhiteAppList: MutableList<WhiteApp>,
    var runningLockConfig: LockConfig,
    var runningLockHistory: LockHistory,
    private val lockRepository: LockRepository
) {

    private lateinit var lockParams: WindowManager.LayoutParams
    private lateinit var lockWindowManager: WindowManager
    var containerLayout: View? = null
    private var gridLayout: GridLayout? = null
    var initViewApps = false
    var initViewLight = false
    var initViewMusic = false
    var initViewStop = false

    var wakeLockType = 0//0：息屏 1：常亮 2：亮屏变暗

    var lastVIPState = false
    var debugMode = false
    var debugTextHeader = ""
    var debugText = ""
    var lastDebugText = ""

    /*系统对象*/
    private lateinit var pm: PowerManager
    private var wakeLock: PowerManager.WakeLock? = null
    private lateinit var adapter: FloatAppInfoAdapter
    var whiteNoiseIndex = 0//正在播放的白噪声序号
    val mediaPlayerList = mutableListOf<MediaPlayer>()
    private val mediaSource = mutableListOf<String>()
//    private val mediaSource = mutableListOf<Uri>()

    /*屏幕尺寸及方向*/
    private var screenHeight = 0
    private var screenWidth = 0
    private var orientation = 0

    //旋转后的
    private var screenHeightRotated = 0
    private var screenWidthRotated = 0
    private var orientationRotated = 0

    // LockRepository 现在通过构造函数注入


    fun releaseLockView() {
        containerLayout = null
    }

    fun isShowing(): Boolean {
        return containerLayout != null && MyWindowUtil.isWindowShowing(containerLayout!!)
    }

    fun refreshId(rootView: View) {
        if (MMKVUtils.getInt(MyConstants.SP_KEY_USER_ID, -1) != -1) {
            rootView.tv_force_unlock_user_id.text = "您的ID是：${MMKVUtils.getInt(MyConstants.SP_KEY_USER_ID, -1)}"
        } else {
            rootView.tv_force_unlock_user_id.text = "未登录"
        }
    }

    init {
        //荣耀部分平板尺寸获取出来比实际的小
        if (Build.BRAND.equals("honor", ignoreCase = true) && Build.MODEL.equals("rod-w09", ignoreCase = true)) {
            if (ScreenUtils.getScreenHeight() > ScreenUtils.getScreenWidth()) {
                screenHeight = 2560
                screenWidth = 1600
            } else {
                screenHeight = 1600
                screenWidth = 2560
            }
        } else {
            screenHeight = ScreenUtils.getScreenHeight()
            screenWidth = ScreenUtils.getScreenWidth()
        }


        screenHeightRotated = screenWidth
        screenWidthRotated = screenHeight

        orientation = if (MyScreenUtils.isScreenOrientationPortrait(context as Service)) {
            ActivityInfo.SCREEN_ORIENTATION_PORTRAIT
        } else {
            ActivityInfo.SCREEN_ORIENTATION_LANDSCAPE
        }

        orientationRotated = if (orientation == ActivityInfo.SCREEN_ORIENTATION_PORTRAIT) {
            ActivityInfo.SCREEN_ORIENTATION_LANDSCAPE
        } else {
            ActivityInfo.SCREEN_ORIENTATION_PORTRAIT
        }
    }

    private fun getCurrentScreenHeight() = if (TheApplication.getInstance().globalParams.isReverse) screenHeightRotated else screenHeight
    private fun getCurrentScreenWidth() = if (TheApplication.getInstance().globalParams.isReverse) screenWidthRotated else screenWidth
    private fun getCurrentOrientation() = if (TheApplication.getInstance().globalParams.isReverse) orientationRotated else orientation

    @SuppressLint("WrongConstant")
    private fun createLockView() {
        LogUtils.d("createLockView start")

        lastVIPState = MyUtil.isVIP()
        lockParams = WindowManager.LayoutParams()

        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
            lockParams.type = WindowManager.LayoutParams.TYPE_APPLICATION_OVERLAY
        } else {
            lockParams.type = WindowManager.LayoutParams.TYPE_SYSTEM_ALERT
        }

        lockWindowManager = context.applicationContext.getSystemService(Context.WINDOW_SERVICE) as WindowManager

        lockParams.format = PixelFormat.TRANSLUCENT
        lockParams.flags = lockParams.flags or WindowManager.LayoutParams.FLAG_FULLSCREEN or
                WindowManager.LayoutParams.FLAG_LAYOUT_IN_SCREEN or
                WindowManager.LayoutParams.FLAG_LAYOUT_NO_LIMITS or
                WindowManager.LayoutParams.FLAG_HARDWARE_ACCELERATED
        lockParams.gravity = Gravity.START or Gravity.TOP
        lockParams.x = 0
        lockParams.y = 0

        containerLayout = if (getCurrentScreenHeight() > getCurrentScreenWidth()) {
//        containerLayout = if (TheApplication.getInstance().globalParams.isPortrait) {
            LayoutInflater.from(context.applicationContext).inflate(R.layout.layout_lock_window, null)
        } else {
            LayoutInflater.from(context.applicationContext).inflate(R.layout.layout_lock_window_land, null)
        }

        containerLayout?.apply {

            LiveEventBus.get(LiveBus.LOGIN, Boolean::class.java).observeForever {
                refreshId(this)
            }

            if (!PermissionUtil.isAccessibilitySettingsOn(MyConstants.ACCESS_ABILITY_NAME, context)) {
                var text = "1.缺少无障碍权限，"
                var show = false

                if (MMKVUtils.getInt(MyConstants.SP_SETTING_DENY_ASSIST_NEW, 0) > 0) {
                    show = true
                    text += "屏蔽语音助手、"
                }
                if (MMKVUtils.getInt(MyConstants.SP_SETTING_DENY_SHUTDOWN_NEW, 0) > 0) {
                    show = true
                    text += "屏蔽电源键、"
                }
                if (MMKVUtils.getInt(MyConstants.SP_SETTING_DENY_EDIT_BUTTON_NEW, 0) > 0) {
                    show = true
                    text += "屏蔽编辑快捷开关、"
                }
                if ((MyRomUtils.isOppo() || MyRomUtils.isVivo()) && MMKVUtils.getInt(MyConstants.SP_SETTING_DENY_CLOSE_RUNNING_SERVICE_NEW, 0) > 0) {
                    show = true
                    text += "屏蔽关闭正在运行的任务、"
                }
                if (MMKVUtils.getInt(MyConstants.SP_SETTING_DENY_DROPDOWN_NEW, 0) > 0) {
                    show = true
                    text += "屏蔽下拉菜单栏、"
                }
                if (MyRomUtils.isVivo() && MMKVUtils.getBoolean(MyConstants.SP_SETTING_DENY_UP, false)) {
                    show = true
                    text += "屏蔽上拉菜单栏、"
                }

//                if (MMKVUtils.getBoolean(MyConstants.SP_SETTING_DENY_FAST_OPEN, false)) {
//                    show = true
//                    text += "屏蔽侧边栏、"
//                }
//                if (MMKVUtils.getBoolean(MyConstants.SP_SETTING_DENY_SMALL_WINDOW, false)) {
//                    show = true
//                    text += "屏蔽小窗、"
//                }

                if (show) {
                    close_text.text = text.substring(0, text.length - 1) + "失效。\n" +
                            "2.您可以在锁机结束后重新授予无障碍权限来恢复。\n" +
                            "3.为防止系统杀后台，请在解锁后前往软件首页的“加固权限”中根据视频教程提示设置好“自启动”和“防止异常退出”。"
                    cl_close.visibility = View.VISIBLE
                    close_notification.setOnClickListener {
                        cl_close.visibility = View.GONE
                    }
                }
            }
            //检测是否无障碍服务是否启动
            else if (TheApplication.getInstance().globalParams.accessibilityService == null) {
                var text = "1.无障碍服务因应用被系统杀后台而停止，"
                var show = false

                if (MMKVUtils.getInt(MyConstants.SP_SETTING_DENY_ASSIST_NEW, 0) > 0) {
                    show = true
                    text += "屏蔽语音助手、"
                }
                if (MMKVUtils.getInt(MyConstants.SP_SETTING_DENY_SHUTDOWN_NEW, 0) > 0) {
                    show = true
                    text += "屏蔽电源键、"
                }
                if (MMKVUtils.getInt(MyConstants.SP_SETTING_DENY_EDIT_BUTTON_NEW, 0) > 0) {
                    show = true
                    text += "屏蔽编辑快捷开关、"
                }
                if ((MyRomUtils.isOppo() || MyRomUtils.isVivo()) && MMKVUtils.getInt(MyConstants.SP_SETTING_DENY_CLOSE_RUNNING_SERVICE_NEW, 0) > 0) {
                    show = true
                    text += "屏蔽关闭正在运行的任务、"
                }
                if (MMKVUtils.getInt(MyConstants.SP_SETTING_DENY_DROPDOWN_NEW, 0) > 0) {
                    show = true
                    text += "屏蔽下拉菜单栏、"
                }
                if (MyRomUtils.isVivo() && MMKVUtils.getBoolean(MyConstants.SP_SETTING_DENY_UP, false)) {
                    show = true
                    text += "屏蔽上拉菜单栏、"
                }
//                if (MMKVUtils.getBoolean(MyConstants.SP_SETTING_DENY_FAST_OPEN, false)) {
//                    show = true
//                    text += "屏蔽侧边栏、"
//                }
//                if (MMKVUtils.getBoolean(MyConstants.SP_SETTING_DENY_SMALL_WINDOW, false)) {
//                    show = true
//                    text += "屏蔽小窗、"
//                }
                if (show) {
                    close_text.text =
                        text.substring(
                            0,
                            text.length - 1
                        ) + "失效。\n2.您可以通过直接重启手机或解锁后重新授予无障碍权限来恢复。\n3.为防止系统杀后台，请在解锁后前往软件首页的“加固权限”中根据视频教程提示设置好“自启动”和“防止异常退出”。"
                    cl_close.visibility = View.VISIBLE
                    close_notification.setOnClickListener {
                        cl_close.visibility = View.GONE
                    }
                }
            }



            if (MMKVUtils.getBoolean(MyConstants.SP_KEY_SHOW_LANDSCAPE, true)) {
                this.iv_orientation.visibility = View.VISIBLE
                iv_orientation.setOnClickListener {
                    TheApplication.getInstance().globalParams.isReverse = !TheApplication.getInstance().globalParams.isReverse
                    refreshLockView()
                }
            } else {
                this.iv_orientation.visibility = View.GONE
            }


            var bgUrl = runningLockConfig.bgUrl
//            LogUtils.d("bgUrl=" + bgUrl)


            if (runningLockHistory.lockType == 1 && runningLockHistory.simpleLockLength == -1) {
                if (bgUrl.isEmpty()) bgUrl = DEFAULT_LOCK_MONITOR_BG
            } else if (runningLockHistory.lockType == 1 && runningLockHistory.simpleLockLength == -2) {
                if (bgUrl.isEmpty()) bgUrl = DEFAULT_LOCK_NOTICE_BG
            } else {
                if (bgUrl.isEmpty()) bgUrl = DEFAULT_LOCK_BG
            }

            //设置锁机背景
            Glide.with(context.applicationContext).load(bgUrl)
                .transition(DrawableTransitionOptions.withCrossFade())
                .diskCacheStrategy(DiskCacheStrategy.AUTOMATIC)
                .into(this.iv_lock_view_bg)


            //页面切换
            this.iv_lock_view_home.setOnClickListener {
                changePage(R.id.cl_lock_view_home_inflate, R.id.iv_lock_view_home)
            }



            this.iv_lock_view_apps.setOnClickListener {
                if (!initViewApps) {
                    this.cl_lock_view_apps.inflate()
                    //屏蔽下拉后默认显示快捷菜单
                    val denyDropDown = MMKVUtils.getInt(MyConstants.SP_SETTING_DENY_DROPDOWN_NEW, 0) > 0
                    val showSystemSetting1 = MMKVUtils.getBoolean(MyConstants.SP_KEY_SHOW_SYSTEM_SETTINGS1, true)
                    val showSystemSetting2 = MMKVUtils.getBoolean(MyConstants.SP_KEY_SHOW_SYSTEM_SETTINGS2, true)
                    val showSystemSetting3 = MMKVUtils.getBoolean(MyConstants.SP_KEY_SHOW_SYSTEM_SETTINGS3, true)
                    val showSystemSetting4 = MMKVUtils.getBoolean(MyConstants.SP_KEY_SHOW_SYSTEM_SETTINGS4, true)
                    val showSystemSetting5 = MMKVUtils.getBoolean(MyConstants.SP_KEY_SHOW_SYSTEM_SETTINGS5, true)
                    val showSystemSetting6 = MMKVUtils.getBoolean(MyConstants.SP_KEY_SHOW_SYSTEM_SETTINGS6, true)
                    val showSystemSetting7 = MMKVUtils.getBoolean(MyConstants.SP_KEY_SHOW_SYSTEM_SETTINGS7, true)
                    val showSystemSetting8 = MMKVUtils.getBoolean(MyConstants.SP_KEY_SHOW_SYSTEM_SETTINGS8, true)
                    if (MMKVUtils.getBoolean(
                            MyConstants.SP_KEY_SHOW_SYSTEM_SETTINGS,
                            denyDropDown
                        ) && (showSystemSetting1 || showSystemSetting2 || showSystemSetting3 || showSystemSetting4 || showSystemSetting5 || showSystemSetting6 || showSystemSetting7 || showSystemSetting8)
                    ) {
                        this.cl_system_settings.visibility = View.VISIBLE
                        if (showSystemSetting1) {
                            this.iv_lock_view_home1.visibility = View.VISIBLE
                            this.iv_lock_view_home1.setOnClickListener {
                                try {
                                    val intent = Intent(android.provider.Settings.ACTION_WIFI_SETTINGS)
                                    intent.addFlags(Intent.FLAG_ACTIVITY_NEW_TASK)
                                    context.applicationContext.startActivity(intent)
                                } catch (e: Exception) {

                                }
                            }
                        } else {
                            this.iv_lock_view_home1.visibility = View.GONE
                        }
                        if (showSystemSetting2) {
                            this.iv_lock_view_home2.visibility = View.VISIBLE
                            this.iv_lock_view_home2.setOnClickListener {
                                try {
                                    val intent = Intent(android.provider.Settings.ACTION_DATA_ROAMING_SETTINGS)
                                    intent.addFlags(Intent.FLAG_ACTIVITY_NEW_TASK)
                                    context.applicationContext.startActivity(intent)
                                } catch (e: Exception) {

                                }
                            }
                        } else {
                            this.iv_lock_view_home2.visibility = View.GONE
                        }

                        if (showSystemSetting3) {
                            this.iv_lock_view_home3.visibility = View.VISIBLE
                            this.iv_lock_view_home3.setOnClickListener {
                                try {
                                    val intent = Intent(android.provider.Settings.ACTION_BLUETOOTH_SETTINGS)
                                    intent.addFlags(Intent.FLAG_ACTIVITY_NEW_TASK)
                                    context.applicationContext.startActivity(intent)
                                } catch (e: Exception) {

                                }
                            }
                        } else {
                            this.iv_lock_view_home3.visibility = View.GONE
                        }

                        if (showSystemSetting4) {
                            this.iv_lock_view_home4.visibility = View.VISIBLE
                            this.iv_lock_view_home4.setOnClickListener {
                                try {
                                    val intent = Intent(android.provider.Settings.ACTION_DISPLAY_SETTINGS)
                                    intent.addFlags(Intent.FLAG_ACTIVITY_NEW_TASK)
                                    context.applicationContext.startActivity(intent)
                                } catch (e: Exception) {

                                }
                            }
                        } else {
                            this.iv_lock_view_home4.visibility = View.GONE
                        }

                        if (showSystemSetting5) {
                            this.iv_lock_view_home5.visibility = View.VISIBLE
                            this.iv_lock_view_home5.setOnClickListener {
                                try {
                                    val intent = Intent(android.provider.Settings.ACTION_AIRPLANE_MODE_SETTINGS)
                                    intent.addFlags(Intent.FLAG_ACTIVITY_NEW_TASK)
                                    context.applicationContext.startActivity(intent)
                                } catch (e: Exception) {

                                }
                            }
                        } else {
                            this.iv_lock_view_home5.visibility = View.GONE
                        }

                        if (showSystemSetting6) {
                            this.iv_lock_view_home6.visibility = View.VISIBLE
                            this.iv_lock_view_home6.setOnClickListener {
                                try {
                                    val intent = Intent(android.provider.Settings.ACTION_LOCATION_SOURCE_SETTINGS)
                                    intent.addFlags(Intent.FLAG_ACTIVITY_NEW_TASK)
                                    context.applicationContext.startActivity(intent)
                                } catch (e: Exception) {

                                }
                            }
                        } else {
                            this.iv_lock_view_home6.visibility = View.GONE
                        }

                        if (showSystemSetting7) {
                            this.iv_lock_view_home7.visibility = View.VISIBLE
                            this.iv_lock_view_home7.setOnClickListener {
                                try {
                                    if (MyRomUtils.isXiaomi() || MyRomUtils.isVivo()) {
                                        val intent = Intent("com.android.settings.WIFI_TETHER_SETTINGS")//action被隐藏，只能用string
                                        intent.addFlags(Intent.FLAG_ACTIVITY_NEW_TASK)
                                        context.applicationContext.startActivity(intent)
                                    } else {
                                        val intent = Intent(android.provider.Settings.ACTION_AIRPLANE_MODE_SETTINGS)
                                        intent.addFlags(Intent.FLAG_ACTIVITY_NEW_TASK)
                                        context.applicationContext.startActivity(intent)
                                    }
                                } catch (e: Exception) {

                                }
                            }
                        } else {
                            this.iv_lock_view_home7.visibility = View.GONE
                        }

                        if (showSystemSetting8) {
                            this.iv_lock_view_home8.visibility = View.VISIBLE
                            this.iv_lock_view_home8.setOnClickListener {
                                try {

                                    val intent = Intent(android.provider.Settings.ACTION_NFC_SETTINGS)
                                    intent.addFlags(Intent.FLAG_ACTIVITY_NEW_TASK)
                                    context.applicationContext.startActivity(intent)

                                } catch (e: Exception) {

                                }
                            }
                        } else {
                            this.iv_lock_view_home8.visibility = View.GONE
                        }
                    } else {
                        this.cl_system_settings.visibility = View.GONE
                    }


                    val recyclerview = this.rv_lock_view_apps
                    var spanCount = 0
                    if (getCurrentScreenHeight() > getCurrentScreenWidth()) {
//                    if (TheApplication.getInstance().globalParams.isPortrait) {
                        spanCount = MyConstants.WHITE_APP_COLUMN
                    } else {
                        spanCount = MyConstants.WHITE_APP_COLUMN + 2
                    }
                    val manager = GridLayoutManager(context.applicationContext, spanCount)
//                    manager.reverseLayout = true

                    recyclerview.layoutManager = manager
                    adapter = FloatAppInfoAdapter(R.layout.item_service_white, mutableListOf())

                    val openActivity = HashMap<String, String>()

                    adapter.setOnItemClickListener { adapter, view, position ->

                        if (!MyUtil.isVIP() && position >= 6) {
                            DialogUtil.showFloatingDialog(
                                title = "VIP已过期",
                                content = "VIP用户可使用6个以上白名单软件，开通后，享受无限白名单设置。",
                                positiveButtonText = "去续订",
                                positiveButtonListener = {
                                    val intent = Intent(context, VIP2Activity::class.java)
                                    intent.flags = intent.flags or Intent.FLAG_ACTIVITY_NEW_TASK
                                    intent.putExtra(FROM_WHERE, "lockScreenApps")
                                    context.startActivity(intent)
                                })
                        } else {
                            //防止无障碍屏蔽侧边栏时误触白名单
//                            if (System.currentTimeMillis() - MMKVUtils.getLong(MyConstants.SP_KEY_LAST_ACCESSIBLE_CLICK, 0L) < 1000) {
//                                return@setOnItemClickListener
//                            }

                            //部分红米设备redmi12 turbo 在多任务页面打不开app，但是检测到的结果却是打开了，会造成能够回到桌面或多任务页面的问题，能够从多任务页面把非白名单以小窗模式打开
//                            performHome(context, "openApp") //没卵用

                            val pkg = (adapter.data[position] as AppInfo).pkg
                            val mainActivity = (adapter.data[position] as AppInfo).mainActivity

                            if (getLauncherActivityCount(pkg) > 1 && openActivity[pkg] != mainActivity) {//上次打开的不是这个activity

                                if (startActivityCompatible(context, pkg, mainActivity)) {
                                    openActivity[pkg] = mainActivity
                                }

                            } else {

                                if (MMKVUtils.getBoolean(MyConstants.SP_KEY_OPEN_WHITE_VIVO, false)) {
                                    startActivityForVIVO(context, pkg)
                                } else {
                                    startActivityCompatible(context, pkg)
                                }
                            }

                            //判断当前启动模式是否成功，没成功的话换一种启动方式
                            GlobalScope.launch(Dispatchers.IO) {
                                var canRun = true
                                val time = System.currentTimeMillis() - TheApplication.getInstance().globalParams.lastClickHomeTime
                                var delta = 0L
                                if (time < 5000) {
                                    delta = (5000 - time)
                                }

                                val theTime = System.currentTimeMillis()
                                val deleteWhiteAppTempList = MyGsonUtil.jsonToDeleteWhiteAppTempList(runningLockHistory.deleteWhiteAppTemp)

                                while (System.currentTimeMillis() - theTime < 3000L + delta) {
                                    val currentAppInfoList =
                                        UsageUtil.getTopPkgInfoList(context.applicationContext, useAccessibility = true, retainChoosePage = true)
                                    if (currentAppInfoList.map { it.pkgName }.toList().contains(pkg)
                                        || isSystemOrUserWhiteApp(currentAppInfoList, deleteWhiteAppTempList).first
                                    ) {
                                        canRun = false
                                        break
                                    }
                                    delay(100)
                                }
                                if (canRun) {
                                    val mode = MMKVUtils.getBoolean(MyConstants.SP_KEY_OPEN_WHITE_VIVO, false)
                                    MMKVUtils.put(MyConstants.SP_KEY_OPEN_WHITE_VIVO, !mode)

                                    if (MMKVUtils.getBoolean(MyConstants.SP_KEY_OPEN_WHITE_VIVO, false)) {
                                        startActivityForVIVO(context, pkg)
                                    } else {
                                        startActivityCompatible(context, pkg)
                                    }
                                }
                            }

                        }

                        if (lastVIPState != MyUtil.isVIP()) {
                            adapter.notifyDataSetChanged()
                        }
                    }

                    //显示删除白名单提示
                    val showDeleteWhiteApp = MMKVUtils.getBoolean(MyConstants.SP_KEY_ALLOW_DELETE_WHITE_APP, true)
                    if (showDeleteWhiteApp) {
                        this.app_notice.visibility = View.VISIBLE
                        adapter.setOnItemLongClickListener { adapter, view, position ->
                            //防止无障碍屏蔽侧边栏时误触白名单
//                            if (System.currentTimeMillis() - MMKVUtils.getLong(MyConstants.SP_KEY_LAST_ACCESSIBLE_CLICK, 0L) > 1000) {
                            val appInfo = (adapter.data[position] as AppInfo)
                            val pkg = appInfo.pkg
                            val mainActivity = appInfo.mainActivity

                            DialogUtil.showFloatingDialog(
                                content = "是否临时移除白名单『${appInfo.appName}』？（下次锁机时自动恢复）",
                                positiveButtonText = "移除",
                                positiveButtonListener = {
                                    adapter.removeAt(position)
                                    adapter.notifyDataSetChanged()
                                    GlobalScope.launch(Dispatchers.IO) {
                                        val deleteWhiteAppTempList = MyGsonUtil.jsonToDeleteWhiteAppTempList(runningLockHistory.deleteWhiteAppTemp)
                                        deleteWhiteAppTempList.add(DeleteWhiteAppTemp(pkg, mainActivity))
                                        runningLockHistory.deleteWhiteAppTemp = GsonUtils.toJson(deleteWhiteAppTempList)
                                        lockViewModel.updateLockHistory(runningLockHistory, withSync = false)

                                    }
                                })
//                            }
                            true
                        }
                    } else {
                        this.app_notice.visibility = View.GONE
                    }


                    val appInfoList = mutableListOf<AppInfo>()

                    if (runningWhiteAppList.size - MyGsonUtil.jsonToDeleteWhiteAppTempList(runningLockHistory.deleteWhiteAppTemp).size <= 0) {
                        this.tv_white_empty_notice.visibility = View.VISIBLE
                    } else {
                        this.tv_white_empty_notice.visibility = View.GONE
                    }

                    GlobalScope.launch(Dispatchers.IO) {


                        val deleteWhiteAppTempList = MyGsonUtil.jsonToDeleteWhiteAppTempList(runningLockHistory.deleteWhiteAppTemp)


                        runningWhiteAppList.forEach {
                            var isDelete = false
                            deleteWhiteAppTempList.forEach { deleteWhiteAppTemp ->
                                if (deleteWhiteAppTemp.pkg == it.pkg && deleteWhiteAppTemp.activity == it.mainActivity) {
                                    isDelete = true
                                }
                            }

                            if (!isDelete && it.pkg != AppUtils.getAppPackageName()) {
                                val appName = getAppName(it.pkg, it.mainActivity)
                                if (appName != "已卸载") {
                                    withContext(Dispatchers.Main) {
                                        appInfoList.add(AppInfo(it.pkg, it.mainActivity, getAppIcon(it.pkg, it.mainActivity), appName, true))
                                    }
                                }
                            }

                        }

                        withContext(Dispatchers.Main) {
                            adapter.setNewInstance(appInfoList)
                        }


                    }


                    recyclerview.adapter = adapter

                    //5秒延迟提示
                    this.btn_lock_view_never_show.setOnClickListener {
                        TheApplication.getInstance().globalParams.lastClickHomeTime = 0
                        MMKVUtils.put(MyConstants.SP_KEY_APPS_NOTIFY, false)
                    }
                    this.btn_lock_view_ok.setOnClickListener {
                        TheApplication.getInstance().globalParams.lastClickHomeTime = 0
                    }

                    initViewApps = true
                }
                changePage(R.id.cl_lock_view_apps_inflate, R.id.iv_lock_view_apps)
            }
            this.iv_lock_view_music.setOnClickListener {
                if (!initViewMusic) {
                    this.cl_lock_view_music.inflate()

                    gridLayout = this.gl_lock_view
                    val titleArray = arrayListOf("无声", "春雨", "溪流", "钟表", "森林", "鸟鸣", "大海", "炉火", "风声", "雷雨", "催眠", "宇宙")
                    val imgArray = arrayListOf(
                        0, R.drawable.white_noise_rain, R.drawable.white_noise_river, R.drawable.white_noise_clock,
                        R.drawable.white_noise_forest, R.drawable.white_noise_bird, R.drawable.white_noise_ocean, R.drawable.white_noise_fire,
                        R.drawable.white_noise_wind, R.drawable.white_noise_big_rain, R.drawable.white_noise_sleep, R.drawable.white_noise_universe
                    )

                    for (i in 0..11) {
                        val itemView = LayoutInflater.from(context.applicationContext).inflate(R.layout.item_white_noise, null)
                        if (i < 4 || MyUtil.isVIP()) {
                            itemView.tv_vip_flag.visibility = View.GONE
                        } else {
                            itemView.tv_vip_flag.visibility = View.VISIBLE
                        }

                        itemView.tv_item_noise.text = titleArray[i]
                        if (imgArray[i] == 0) {
                            itemView.iv_item_noise.setImageResource(R.color.colorWhiteBackground)
                        } else {
                            itemView.iv_item_noise.setImageResource(imgArray[i])
                        }

                        var rowCount = 0
                        if (getCurrentScreenHeight() > getCurrentScreenWidth()) {
//                        if (TheApplication.getInstance().globalParams.isPortrait) {
                            rowCount = 2
                        } else {
                            rowCount = 3
                        }
                        val rowSpec: GridLayout.Spec = GridLayout.spec(i / rowCount, 1.0f)
                        val columnSpec: GridLayout.Spec = GridLayout.spec(i % rowCount, 1.0f)
                        val params: GridLayout.LayoutParams = GridLayout.LayoutParams(rowSpec, columnSpec)
                        params.width = 0
                        gridLayout!!.addView(itemView, params)

                        itemView.setOnClickListener {
                            if (i < 4 || MyUtil.isVIP()) {
                                stopAllWhitePlaying()
                                startWhiteNoise(i)
                                whiteNoiseIndex = i
                            } else {
                                var toast = ""
                                if (MMKVUtils.getBoolean("WhiteNoisePlay$i", false)) {
                                } else {
                                    MMKVUtils.put("WhiteNoisePlay$i", true)
                                    stopAllWhitePlaying()
                                    startWhiteNoise(i)
                                    whiteNoiseIndex = i

                                }
                                toast = "该白噪声为VIP用户专享\n普通用户仅可试听一次"
                                val intent = Intent()
                                intent.putExtra(FROM_WHERE, "whiteNoise")
                                intent.putExtra("toast", toast)
                                startActivityCompatibleWithIntent(
                                    intent,
                                    context,
                                    AppUtils.getAppPackageName(),
                                    VIP2Activity::class.java.canonicalName.toString()
                                )


                            }
                        }
                    }

                    initViewMusic = true
                }
                changePage(R.id.cl_lock_view_music_inflate, R.id.iv_lock_view_music)
            }
            this.iv_lock_view_light.setOnClickListener {
                if (!initViewLight) {
                    this.cl_lock_view_light.inflate()

                    this.rg_light.setOnCheckedChangeListener { group, checkedId ->
                        when (checkedId) {
                            R.id.rb_light_1 -> {
                                wakeLockType = 1
                            }

                            R.id.rb_light_2 -> {
                                wakeLockType = 2
                            }

                            R.id.rb_light_3 -> {
                                wakeLockType = 0
                            }
                        }
                        getWakeLock()
                    }

                    initViewLight = true
                }
                changePage(R.id.cl_lock_view_light_inflate, R.id.iv_lock_view_light)
            }

            LiveEventBus.get(LiveBus.DEBUG_MSG, String::class.java).observeForever {
                if (!debugMode) return@observeForever
                LogUtils.d("receive debug_msg lastDebugText: $lastDebugText")
                LogUtils.d("receive debug_msg it: $it")
                if (lastDebugText != it) {

                    LogUtils.d("!=")
                    lastDebugText = it

                    debugText = "${debugText}\n${TimeUtil.getDebugTime()}: $it"
                    if (debugText.length > 10000) {
                        debugText = debugText.takeLast(10000)
                    }

                    LogUtils.d("debugText.length: ${debugText.length}")

                    this.tv_debug_msg.text = debugText
                    LogUtils.d("set debugText: $debugText")

                    this.sv_debug_msg.post { this.sv_debug_msg.fullScroll(View.FOCUS_DOWN) }

                    // 滚动到底部
//                    val scrollAmount = this.tv_debug_msg.layout.getLineTop(this.tv_debug_msg.lineCount) - this.tv_debug_msg.height
//                    if (scrollAmount > 0)
//                        this.tv_debug_msg.scrollTo(0, scrollAmount)
//                    else
//                        this.tv_debug_msg.scrollTo(0, 0)
                } else {
                    LogUtils.d("==")
                }
            }

            this.iv_lock_view_stop.setOnClickListener {
                if (!initViewStop) {
                    this.cl_lock_view_stop.inflate()

                    this.btn_developer_mode.setOnClickListener {
                        if (debugMode) {
                            DialogUtil.showFloatingDialog(
                                title = "关闭调试模式",
                                content = "关闭调试模式后，不再记录手机操作日志，是否关闭？",
                                positiveButtonText = "关闭",
                            ) {
                                debugMode = false
                                debugTextHeader = ""
                                debugText = ""
                                this.tv_debug_msg_header.text = ""
                                this.tv_debug_msg.text = ""
                                this.tv_debug_msg_header.visibility = View.GONE
                                this.sv_debug_msg.visibility = View.GONE
                                MyToastUtil.showSuccess("已关闭调试模式")
                            }
                        } else {
                            DialogUtil.showFloatingDialog(
                                title = "开启调试模式",
                                content = "调试模式会临时记录当前一段时间的手机操作日志，直到关闭该模式。为保护您的隐私，此信息不会直接上传到云端，需要您把主动把信息复制截图提供给客服，是否开启？",
                                positiveButtonText = "开启",
                            ) {
                                debugMode = true
                                debugTextHeader = "调试模式已开启 ${TimeUtil.getDebugTime()}\n" +
                                        "机型：${Build.BRAND} ${Build.MODEL}，系统：${Build.VERSION.SDK_INT}(${Build.VERSION.RELEASE})，应用版本：${AppUtils.getAppVersionName()}\n" +
                                        "无障碍：${MyAccessibilityService.isAccessibilityActive()}\n" +
                                        "若有白名单闪退或漏屏蔽页面或操作等情况，请将出问题时间段的日志截图并在app内反馈给客服。\n" +
                                        "----------------------------------"
                                this.tv_debug_msg_header.text = debugTextHeader
                                this.tv_debug_msg.text = debugText
                                this.tv_debug_msg_header.visibility = View.VISIBLE
                                this.sv_debug_msg.visibility = View.VISIBLE
                                MyToastUtil.showSuccess("已开启调试模式")
                            }
                        }
                    }

                    refreshId(this)

                    if (MMKVUtils.getBoolean(MyConstants.SP_KEY_SHOW_DIALOG_WHY_PUNISH, true)) {
                        this.mcv_app_punish_notify.visibility = View.VISIBLE
                        this.btn_lock_view_punish_ok.setOnClickListener {
                            MMKVUtils.put(MyConstants.SP_KEY_SHOW_DIALOG_WHY_PUNISH, false)
                            this.mcv_app_punish_notify.visibility = View.GONE
                        }
                    }

                    this.tv_how_much.text = "￥${MyUtil.getForceUnlockMoney()}"
                    if (MMKVUtils.getInt(MyConstants.SP_KEY_FORCE_UNLOCK_PUNISH, 5) == 0) {
                        this.tv_how_many_left.visibility = View.VISIBLE

                        val calendar = Calendar.getInstance()
                        val currentMonth = "${calendar.get(Calendar.YEAR)}-${calendar.get(Calendar.MONTH) + 1}"
                        val hasUnlockedTime = MMKVUtils.getInt(currentMonth, 0)
                        val allUnlockTime = MMKVUtils.getInt(MyConstants.SP_KEY_MAX_UNLOCK_COUNT, 3)
                        val left = allUnlockTime - hasUnlockedTime

                        if (left > 0) {
                            this.tv_how_many_left.text = "本月0元解锁剩余${left}次"
                        } else {
                            this.tv_how_many_left.text = "本月0元解锁${allUnlockTime}次已用完，恢复为5元"
                        }

                    } else {
                        this.tv_how_many_left.visibility = View.GONE
                    }

                    if (MMKVUtils.getBoolean(MyConstants.SP_KEY_ALLOW_FINANCIAL_PUNISH, true) ||
                        MMKVUtils.getBoolean(MyConstants.SP_KEY_ALLOW_PWD_UNLOCK, false) ||
                        MMKVUtils.getBoolean(MyConstants.SP_KEY_ALLOW_FRIEND_UNLOCK, true)
                    ) {
                        rg_force_stop_type.visibility = View.VISIBLE
                        tv_no_unlock_notice.visibility = View.GONE
                    } else {
                        rg_force_stop_type.visibility = View.GONE
                        tv_no_unlock_notice.visibility = View.VISIBLE
                    }

                    if (MMKVUtils.getBoolean(MyConstants.SP_KEY_ALLOW_FINANCIAL_PUNISH, true)) {
                        this.rb_stop_1.visibility = View.VISIBLE

                        this.rb_stop_1.setOnClickListener {
//                        var forceUnlockType = 0
                            val forceUnlockType = when (this.rg_force_stop_type.checkedRadioButtonId) {
                                R.id.rb_force_stop_type_1 -> 1
                                R.id.rb_force_stop_type_2 -> 2
                                R.id.rb_force_stop_type_3 -> 3
                                R.id.rb_force_stop_type_4 -> 4
                                R.id.rb_force_stop_type_5 -> 5
                                R.id.rb_force_stop_type_6 -> 6
                                R.id.rb_force_stop_type_7 -> 7
                                R.id.rb_force_stop_type_8 -> 8
                                R.id.rb_force_stop_type_9 -> 9
                                else -> 0
                            }

                            //                            if (forceUnlockType == 0) {
//                                MyToastUtil.showWarning("请先选中一个解锁类型")
//                            } else {
                            fun checkIfNeedNotifyLockType(forceUnlockType: Int, doPositive: () -> Unit) {
                                when (forceUnlockType) {
                                    0 -> {
                                        MyToastUtil.showWarning("请先选中一个解锁类型")
                                    }

                                    in arrayOf(1, 2, 3, 4) -> {
                                        DialogUtil.showFloatingDialog(
                                            title = "温馨提示",
                                            content = "本次解锁仅${getUnlockInfo(forceUnlockType)}，过后会自动恢复锁机，是否暂停锁机？",
                                            positiveButtonText = "暂停锁机",
                                        ) {
                                            doPositive.invoke()
                                        }
                                    }

                                    in arrayOf(6, 8) -> {
                                        DialogUtil.showFloatingDialog(
                                            title = "温馨提示",
                                            content = "本次解锁仅${getUnlockInfo(forceUnlockType)}，次日0点会自动恢复锁机，是否暂停锁机？",
                                            positiveButtonText = "暂停锁机",
                                        ) {
                                            doPositive.invoke()
                                        }
                                    }

                                    else -> {
                                        doPositive.invoke()
                                    }
                                }
                            }

                            checkIfNeedNotifyLockType(forceUnlockType) {
                                MMKVUtils.put(MyConstants.SP_KEY_FORCE_UNLOCK_TYPE, forceUnlockType)
                                if (MMKVUtils.getInt(MyConstants.SP_KEY_FORCE_UNLOCK_PUNISH, 5) > 0) {
                                    startActivityCompatible(context, AppUtils.getAppPackageName(), PayUnlockActivity::class.java.canonicalName.toString())
                                } else {
                                    val calendar = Calendar.getInstance()
                                    val currentMonth = "${calendar.get(Calendar.YEAR)}-${calendar.get(Calendar.MONTH) + 1}"
                                    val hasUnlockedTime = MMKVUtils.getInt(currentMonth, 0)
                                    val maxUnlockedTime = MMKVUtils.getInt(MyConstants.SP_KEY_MAX_UNLOCK_COUNT, 3)
                                    if (hasUnlockedTime < maxUnlockedTime) {
                                        DialogUtil.showFloatingDialog(
                                            title = "解锁确认",
                                            content = "本月还可0元解锁${maxUnlockedTime - hasUnlockedTime}次，确定要现在使用吗？建议您将其留到紧急情况时使用。",
                                            positiveButtonText = "立刻解锁",
                                        ) {
                                            MMKVUtils.put(currentMonth, hasUnlockedTime + 1)
                                            LiveEventBus.get(LiveBus.FORCE_QUIT, String::class.java).post("")
                                        }

                                    } else {
                                        startActivityCompatible(context, AppUtils.getAppPackageName(), PayUnlockActivity::class.java.canonicalName.toString())
                                    }
                                }
                            }
//                            }
                        }
                    } else {
                        this.rb_stop_1.visibility = View.GONE
                    }


                    if (MMKVUtils.getBoolean(MyConstants.SP_KEY_ALLOW_PWD_UNLOCK, false)) {
                        this.cl_stop_2.visibility = View.VISIBLE
                        this.rb_stop_2.setOnClickListener {
//                            var forceUnlockType = 0
                            val forceUnlockType = when (this.rg_force_stop_type.checkedRadioButtonId) {
                                R.id.rb_force_stop_type_1 -> 1
                                R.id.rb_force_stop_type_2 -> 2
                                R.id.rb_force_stop_type_3 -> 3
                                R.id.rb_force_stop_type_4 -> 4
                                R.id.rb_force_stop_type_5 -> 5
                                R.id.rb_force_stop_type_6 -> 6
                                R.id.rb_force_stop_type_7 -> 7
                                R.id.rb_force_stop_type_8 -> 8
                                R.id.rb_force_stop_type_9 -> 9
                                else -> 0
                            }
//                            val intent = Intent(context, PwdForceUnlockActivity::class.java)
//                            intent.putExtra("forceUnlockType", forceUnlockType)
//                            context.startActivity(intent)
                            if (forceUnlockType == 0) {
                                MyToastUtil.showWarning("请先选中一个解锁类型")
                            } else {
                                MMKVUtils.put(MyConstants.SP_KEY_FORCE_UNLOCK_TYPE, forceUnlockType)
                                startActivityCompatible(context, AppUtils.getAppPackageName(), PasswordUnlockActivity::class.java.canonicalName.toString())
//                                val intent = Intent(context, PasswordUnlockActivity::class.java)
//                                intent.flags = intent.flags or Intent.FLAG_ACTIVITY_NEW_TASK
//                                context.startActivity(intent)
                            }
                        }
                        this.iv_close_pwd_unlock_button.setOnClickListener {
                            DialogUtil.showFloatingDialog(
                                title = "关闭密码解锁",
                                content = "关闭密码解锁后，您将无法再使用密码解锁码来解锁手机，您可以到“锁机设置”中重新开启。",
                                positiveButtonText = "确定关闭",
                                positiveButtonListener = {
                                    MMKVUtils.put(MyConstants.SP_KEY_ALLOW_PWD_UNLOCK, false)
                                    this.cl_stop_2.visibility = View.GONE
                                })
                        }
                    } else {
                        this.cl_stop_2.visibility = View.GONE
                    }

                    if (MMKVUtils.getBoolean(MyConstants.SP_KEY_ALLOW_FRIEND_UNLOCK, true)) {
                        this.cl_stop_3.visibility = View.VISIBLE
                        this.rb_stop_3.setOnClickListener {
                            if (MMKVUtils.getInt(MyConstants.SP_KEY_USER_ID, -1) != -1) {

                                val forceUnlockType = when (this.rg_force_stop_type.checkedRadioButtonId) {
                                    R.id.rb_force_stop_type_1 -> 1
                                    R.id.rb_force_stop_type_2 -> 2
                                    R.id.rb_force_stop_type_3 -> 3
                                    R.id.rb_force_stop_type_4 -> 4
                                    R.id.rb_force_stop_type_5 -> 5
                                    R.id.rb_force_stop_type_6 -> 6
                                    R.id.rb_force_stop_type_7 -> 7
                                    R.id.rb_force_stop_type_8 -> 8
                                    R.id.rb_force_stop_type_9 -> 9
                                    else -> 0
                                }
                                if (forceUnlockType == 0) {
                                    MyToastUtil.showWarning("请先选中一个解锁类型")
                                } else {
                                    MMKVUtils.put(MyConstants.SP_KEY_FORCE_UNLOCK_TYPE, forceUnlockType)
                                    startActivityCompatible(context, AppUtils.getAppPackageName(), FriendUnlockActivity::class.java.canonicalName.toString())
                                }
                            } else {
                                DialogUtil.showFloatingDialog(
                                    title = "温馨提示",
                                    content = "使用该功能需要先登录，是否登录？",
                                    positiveButtonText = "去登录",
                                    positiveButtonListener = {
                                        startActivityCompatible(context, AppUtils.getAppPackageName(), TheLoginActivity::class.java.canonicalName.toString())
                                    })
                            }
                        }
                        this.iv_close_friend_unlock_button.setOnClickListener {
                            DialogUtil.showFloatingDialog(
                                title = "关闭好友解锁",
                                content = "关闭好友解锁后，您将无法再使用好友解锁码来解锁手机，您可以到“锁机设置”中重新开启。",
                                positiveButtonText = "确定关闭",
                                positiveButtonListener = {
                                    MMKVUtils.put(MyConstants.SP_KEY_ALLOW_FRIEND_UNLOCK, false)
                                    this.cl_stop_3.visibility = View.GONE
                                })
                        }
                    } else {
                        this.cl_stop_3.visibility = View.GONE
                    }

                    initViewStop = true
                }

//                LogUtils.d("ljq=========="+runningLockHistory.toString())
                if (runningLockHistory.lockType == 3) {
                    this.rb_force_stop_type_5.visibility = View.GONE
                    this.rb_force_stop_type_6.visibility = View.VISIBLE
                    this.rb_force_stop_type_7.visibility = View.VISIBLE
                    this.rb_force_stop_type_8.visibility = View.GONE
                    this.rb_force_stop_type_9.visibility = View.GONE
                } else if (runningLockHistory.lockType == 1 && runningLockHistory.simpleLockLength == -1) {
                    this.rb_force_stop_type_5.visibility = View.GONE
                    this.rb_force_stop_type_6.visibility = View.GONE
                    this.rb_force_stop_type_7.visibility = View.GONE
                    this.rb_force_stop_type_8.visibility = View.VISIBLE
                    this.rb_force_stop_type_9.visibility = View.VISIBLE
                } else {
                    this.rb_force_stop_type_5.visibility = View.VISIBLE
                    this.rb_force_stop_type_6.visibility = View.GONE
                    this.rb_force_stop_type_7.visibility = View.GONE
                    this.rb_force_stop_type_8.visibility = View.GONE
                    this.rb_force_stop_type_9.visibility = View.GONE
                }

                changePage(R.id.cl_lock_view_stop_inflate, R.id.iv_lock_view_stop)
            }
        }


//        lockWindowCreated = true
        refreshWordAndDestiny()
//        LogUtils.d("createLockView end")
    }

    var lastMaintainTime = 0L //上次锁屏时设置的保持到什么时候

    //maintain显示后保持多久才能被清除
    fun showLockWindow(maintain: Long = 0L) {
//        LogUtils.d(" lock showLockWindow")
        if (maintain != 0L) {
            lastMaintainTime = System.currentTimeMillis() + maintain
        }
//        LogUtils.d("lastMaintainTime=${lastMaintainTime}")
//        if (!lockWindowCreated) {
        if (containerLayout == null) {
            createLockView()
        }
        if (!MyWindowUtil.isWindowShowing(containerLayout!!)) {
            TheApplication.getInstance().globalParams.checkServiceInitOk = true
//            lockWindowShowing = true

//            val windowManager = context.getSystemService(WINDOW_SERVICE) as WindowManager
//            val metrics = DisplayMetrics()

//            windowManager.defaultDisplay.getMetrics(metrics)
//            val width = metrics.widthPixels
//            val height = metrics.heightPixels


//            LogUtils.d("height=${height},width=${width}")
            LogUtils.d(
                "height=${ScreenUtils.getScreenHeight()},width=${ScreenUtils.getScreenWidth()}, screen isPortrait=${
                    MyScreenUtils.isScreenOrientationPortrait(
                        context as
                                Service
                    )
                }, globalParams.isReverse=${TheApplication.getInstance().globalParams.isReverse}"
            )

            //适配荣耀平板
//            if (Build.BRAND.equals("honor", ignoreCase = true) && Build.MODEL.equals("rod-w09", ignoreCase = true)) {
//                if (TheApplication.getInstance().globalParams.isReverse) {
//                    lockParams.screenOrientation = ActivityInfo.SCREEN_ORIENTATION_PORTRAIT
//                    lockParams.height = 2560
//                    lockParams.width = 1600
//                } else {
//                    lockParams.screenOrientation = ActivityInfo.SCREEN_ORIENTATION_LANDSCAPE
//                    lockParams.height = 1600
//                    lockParams.width = 2560
//                }
//            } else {
            if (getCurrentScreenHeight() > getCurrentScreenWidth()) {
                lockParams.screenOrientation = getCurrentOrientation()
                lockParams.height = getCurrentScreenHeight()
                lockParams.width = getCurrentScreenWidth()
            } else {
                lockParams.screenOrientation = getCurrentOrientation()
                lockParams.height = getCurrentScreenHeight()
                lockParams.width = getCurrentScreenWidth()
            }
//            }
            try {

                lockWindowManager.addView(containerLayout, lockParams)
            } catch (e: Exception) {
                MyToastUtil.showError("LockFloatWindow未成功获取到悬浮窗权限，请到系统设置或安全管家中重新授予，若已经授予，则可能是系统BUG，请重启手机")
//                lockWindowShowing = false

            }
        }
    }

    fun hideLockWindow(taskStop: Boolean = false) {
//        LogUtils.d("lastMaintainTime=${lastMaintainTime}")
//        LogUtils.d("System.currentTimeMillis()=${System.currentTimeMillis()}")
//        LogUtils.d("lastMaintainTime-System.currentTimeMillis()=${lastMaintainTime-System.currentTimeMillis()}")
        if (containerLayout == null) {
            return
        }
//        if (lockWindowCreated)
//        if (containerLayout != null) {
        if (taskStop || System.currentTimeMillis() > lastMaintainTime
//            || (System.currentTimeMillis() - TheApplication.getInstance().globalParams.lastClickHomeOrRecent > 300)
        ) {
//                if (lockWindowShowing) {
            if (MyWindowUtil.isWindowShowing(containerLayout!!)) {
//                    lockWindowShowing = false
                try {
                    lockWindowManager.removeView(containerLayout)
                } catch (e: Exception) {
                    MyToastUtil.showError("未成功获取到悬浮窗权限，请到系统设置或安全管家中重新授予，若已经授予，则可能是系统BUG，请重启手机")
                }
            }
        }
//        }
    }

    fun refreshLockView() {
        if (containerLayout != null) {
            if (MyWindowUtil.isWindowShowing(containerLayout!!)) {
                hideLockWindow()
                wakeLockType = 0
                removeWakeLock()
//                lockWindowCreated = false
                containerLayout = null
                initViewApps = false
                initViewLight = false
                initViewMusic = false
                initViewStop = false
                whiteNoiseIndex = 0
                stopAllWhitePlaying()
                showLockWindow()
            } else {
                wakeLockType = 0
                removeWakeLock()
//                lockWindowCreated = false
                containerLayout = null
                initViewApps = false
                initViewLight = false
                initViewMusic = false
                initViewStop = false
                whiteNoiseIndex = 0
                stopAllWhitePlaying()
            }
        }
    }

    val proxy = TheApplication.getProxy(context)
    fun proxyUrl(url: String): String {
        val proxyUrl = proxy.getProxyUrl(url)
        return proxyUrl
    }


    fun initMediaSource() {
        mediaSource.add("whiteNoise/white_noise_1_rain.mp3")
        mediaSource.add("whiteNoise/white_noise_2_river.mp3")
        mediaSource.add("whiteNoise/white_noise_3_clock.mp3")
        mediaSource.add("whiteNoise/white_noise_4_forest.mp3")
        mediaSource.add("whiteNoise/white_noise_5_bird.mp3")
        mediaSource.add("whiteNoise/white_noise_6_far_ocean.mp3")
        mediaSource.add("whiteNoise/white_noise_7_fire.mp3")
        mediaSource.add("whiteNoise/white_noise_8_wind.mp3")
        mediaSource.add("whiteNoise/white_noise_9_big_rain.mp3")
        mediaSource.add("whiteNoise/white_noise_10_sleep.mp3")
        mediaSource.add("whiteNoise/white_noise_11_universe.mp3")

//        mediaSource.add(Uri.parse(proxyUrl("https://offphone-music-1252369707.file.myqcloud.com/whiteNoise/white_noise_1_rain.mp3")))
//        mediaSource.add(Uri.parse(proxyUrl("https://offphone-music-1252369707.file.myqcloud.com/whiteNoise/white_noise_2_river.mp3")))
//        mediaSource.add(Uri.parse(proxyUrl("https://offphone-music-1252369707.file.myqcloud.com/whiteNoise/white_noise_3_clock.mp3")))
//        mediaSource.add(Uri.parse(proxyUrl("https://offphone-music-1252369707.file.myqcloud.com/whiteNoise/white_noise_4_forest.mp3")))
//        mediaSource.add(Uri.parse(proxyUrl("https://offphone-music-1252369707.file.myqcloud.com/whiteNoise/white_noise_5_bird.mp3")))
//        mediaSource.add(Uri.parse(proxyUrl("https://offphone-music-1252369707.file.myqcloud.com/whiteNoise/white_noise_6_far_ocean.mp3")))
//        mediaSource.add(Uri.parse(proxyUrl("https://offphone-music-1252369707.file.myqcloud.com/whiteNoise/white_noise_7_fire.mp3")))
//        mediaSource.add(Uri.parse(proxyUrl("https://offphone-music-1252369707.file.myqcloud.com/whiteNoise/white_noise_8_wind.mp3")))
//        mediaSource.add(Uri.parse(proxyUrl("https://offphone-music-1252369707.file.myqcloud.com/whiteNoise/white_noise_9_big_rain.mp3")))
//        mediaSource.add(Uri.parse(proxyUrl("https://offphone-music-1252369707.file.myqcloud.com/whiteNoise/white_noise_10_sleep.mp3")))
//        mediaSource.add(Uri.parse(proxyUrl("https://offphone-music-1252369707.file.myqcloud.com/whiteNoise/white_noise_11_universe.mp3")))

    }

    fun initWhiteNoise() {

        mediaSource.forEachIndexed { index, _ ->
            initNoiseFromAssets(index, true)
//            initNoiseFromNet(index, true)
        }

    }

    private fun initNoiseFromAssets(n: Int, ifAsync: Boolean) {
        try {
            val assetManager = context.applicationContext.assets
            val assetFileDescriptor: AssetFileDescriptor = assetManager.openFd(mediaSource[n])
            MediaPlayer().apply {
                setDataSource(assetFileDescriptor.fileDescriptor, assetFileDescriptor.startOffset, assetFileDescriptor.getLength())
//                setAudioAttributes(
//                    AudioAttributes.Builder()
//                        .setLegacyStreamType(AudioManager.STREAM_MUSIC)
//                        .setUsage(AudioAttributes.USAGE_MEDIA)
//                        .setContentType(AudioAttributes.CONTENT_TYPE_MUSIC)
//                        .build()
//                )
                isLooping = true
                if (ifAsync) {
                    prepareAsync()
                } else {
                    prepare()
                }
                setOnPreparedListener {
                    isPreparedArray[n] = true
                }
                mediaPlayerList.add(n, this)
            }
        } catch (e: Exception) {
            LogUtils.d("initNoiseFromAssets error $e")
        }
    }

//    private fun initNoiseFromNet(n: Int, ifAsync: Boolean) {
//        try {
//            MediaPlayer().apply {
//                setDataSource(context.applicationContext, mediaSource[n])
//                setAudioAttributes(
//                    AudioAttributes.Builder()
//                        .setLegacyStreamType(AudioManager.STREAM_MUSIC)
//                        .setUsage(AudioAttributes.USAGE_MEDIA)
//                        .setContentType(AudioAttributes.CONTENT_TYPE_MUSIC)
//                        .build()
//                )
//                isLooping = true
//                if (ifAsync) {
//                    prepareAsync()
//                } else {
//                    prepare()
//                }
//                setOnPreparedListener {
//                    isPreparedArray[n] = true
//                }
//                mediaPlayerList.add(n, this)
//            }
//        } catch (e: Exception) {
//
//        }
//    }


    private val isPreparedArray = arrayOf(false, false, false, false, false, false, false, false, false, false, false, false)

    fun startWhiteNoise(i: Int) {

        if (i > 0) {
            gridLayout?.apply {
                val itemView = this.getChildAt(i)
                if (isPreparedArray[i - 1]) {
                    itemView.lav_white_noise_playing.visibility = View.VISIBLE
                    itemView.lav_white_noise_playing.playAnimation()
                    mediaPlayerList[i - 1].start()
                } else {
                    itemView.tv_no_net.visibility = View.VISIBLE
                    if (NetworkUtils.isConnected()) {
                        itemView.tv_no_net.text = "缓存中..."
                        initNoiseFromAssets(i - 1, false)
                        itemView.tv_no_net.visibility = View.GONE
                        itemView.lav_white_noise_playing.visibility = View.VISIBLE
                        itemView.lav_white_noise_playing.playAnimation()
                        mediaPlayerList[i - 1].start()
                    } else {
                        itemView.tv_no_net.text = "无网络..."
                    }
                }

            }
        }
    }


    fun stopAllWhitePlaying() {
        if (containerLayout != null) {
            gridLayout?.apply {
                this.children.forEach {
                    it.lav_white_noise_playing.visibility = View.GONE
                    it.tv_no_net.visibility = View.GONE
                    if (it.lav_white_noise_playing.isAnimating) {
                        it.lav_white_noise_playing.cancelAnimation()
                    }
                }
                mediaPlayerList.forEach {
                    if (it.isPlaying) {
                        it.pause()
                    }
                }
            }
        }
    }

    //切换页面
    private fun changePage(containerId: Int, iconId: Int) {
        containerLayout?.apply {
            val containers = arrayListOf(
                R.id.cl_lock_view_home_inflate, R.id.cl_lock_view_apps_inflate,
                R.id.cl_lock_view_music_inflate, R.id.cl_lock_view_light_inflate, R.id.cl_lock_view_stop_inflate
            )
            val icons = arrayListOf(
                R.id.iv_lock_view_home, R.id.iv_lock_view_apps,
                R.id.iv_lock_view_music, R.id.iv_lock_view_light, R.id.iv_lock_view_stop
            )

            containers.forEach {
                this.findViewById<View>(it)?.apply {
                    if (it == containerId) this.visibility = View.VISIBLE
                    else this.visibility = View.GONE
                }
            }

            icons.forEach {
                this.findViewById<View>(it)?.apply {
                    if (it == iconId) this.alpha = 0.6f
                    else this.alpha = 0.3f
                }
            }
        }
    }

    //刷新倒数日显示，用于日交替时刷新
    fun refreshWordAndDestiny() {
        if (containerLayout != null) {
            containerLayout?.let {
                if (MMKVUtils.getBoolean(MyConstants.SP_KEY_SETTING_DESTINY, false)) {
                    it.tv_lock_view_date_word.visibility = View.VISIBLE
                    val calendar = Calendar.getInstance()
                    val nextYear = calendar.get(Calendar.YEAR) + 1
                    val goalDate = MMKVUtils.getString(MyConstants.SP_KEY_SETTING_DESTINY_DATE, "01/01/${nextYear}")
                    val fmt = SimpleDateFormat("MM/dd/yyyy")
                    val date = fmt.parse(goalDate)
                    val restDats = (date.time - calendar.time.time + calendar.get(Calendar.HOUR_OF_DAY) * 1000 * 60 * 60 +
                            calendar.get(Calendar.MINUTE) * 1000 * 60 + calendar.get(Calendar.SECOND) * 1000 + calendar.get(Calendar.MILLISECOND)) / 1000 / 60 / 60 / 24
                    if (restDats > 0L) {
                        it.tv_lock_view_date_word.text = "距离${MMKVUtils.getString(MyConstants.SP_KEY_SETTING_DESTINY_NAME, "$nextYear")}还剩${restDats}天"
                    } else if (restDats == 0L) {
                        it.tv_lock_view_date_word.text = "今天${MMKVUtils.getString(MyConstants.SP_KEY_SETTING_DESTINY_NAME, "$nextYear")}"
                    } else {
                        it.tv_lock_view_date_word.text =
                            "${MMKVUtils.getString(MyConstants.SP_KEY_SETTING_DESTINY_NAME, "$nextYear")}已过去${restDats * -1}天"
                    }
                } else {
                    it.tv_lock_view_date_word.visibility = View.GONE
                }

                var localWord = MMKVUtils.getString(MyConstants.SP_KEY_SETTING_WORD, "")
                if (localWord.isEmpty()) {
//                    lockViewModel.refreshLockPageMsg()
                    localWord = MMKVUtils.getString(MyConstants.SP_KEY_WELL_KNOW_WORD, "")
                }

                if (runningLockHistory.lockType == 1 && runningLockHistory.simpleLockLength == -1) {
                    it.tv_lock_view_personal_word.text = "已到达您设置的每日时长限制，进入锁机状态，明天00:00自动解除"
                } else if (runningLockHistory.lockType == 1 && runningLockHistory.simpleLockLength == -2) {
                    it.tv_lock_view_personal_word.text = "疲劳锁机中，休息一下吧~"
                } else {
                    if (localWord.isBlank()) {
                        it.textView61.visibility = View.GONE
                    }
                    it.tv_lock_view_personal_word.text = localWord
                }

            }
        }

    }

//    internal inner class LockWordObserver : Observer<NetworkState<LockMsg>> {
//        override fun onChanged(t: NetworkState<LockMsg>) {
//            if (t.state == 0) {
//                if (lockWindowCreated) {
//                    containerLayout.tv_lock_view_personal_word.text = t.data!!.word
//                }
//            }
//        }
//    }

    fun initWakeLock() {
        pm = context.applicationContext.getSystemService(Context.POWER_SERVICE) as PowerManager
    }

    fun getWakeLock() {
        removeWakeLock()
        when (wakeLockType) {
            1 -> {
                wakeLock = pm.newWakeLock(PowerManager.SCREEN_BRIGHT_WAKE_LOCK or PowerManager.ON_AFTER_RELEASE, "demo:tag")
                wakeLock?.acquire(24 * 60 * 60 * 1000L)
            }

            2 -> {
                wakeLock = pm.newWakeLock(PowerManager.SCREEN_DIM_WAKE_LOCK or PowerManager.ON_AFTER_RELEASE, "demo:tag")
                wakeLock?.acquire(24 * 60 * 60 * 1000L)
            }

            else -> {
                return
            }
        }
    }

    fun removeWakeLock() {
        wakeLock?.apply {
            this.isHeld.let {
                if (it) {
                    this.release()
                }
            }
        }
    }

    public fun refreshUnlockRadioButton() {
        if (initViewStop) {
            containerLayout?.apply {
                this.rb_force_stop_type_1.isChecked = false
                this.rb_force_stop_type_2.isChecked = false
                this.rb_force_stop_type_3.isChecked = false
                this.rb_force_stop_type_4.isChecked = false
                this.rb_force_stop_type_5.isChecked = false
                this.rb_force_stop_type_6.isChecked = false
                this.rb_force_stop_type_7.isChecked = false
                this.rb_force_stop_type_8.isChecked = false
                this.rb_force_stop_type_9.isChecked = false
            }
        }
    }
}