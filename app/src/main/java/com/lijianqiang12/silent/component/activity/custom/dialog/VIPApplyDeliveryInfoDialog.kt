package com.lijianqiang12.silent.component.activity.custom.dialog

import android.content.DialogInterface
import android.graphics.Color
import android.graphics.drawable.ColorDrawable
import android.os.Bundle
import android.util.DisplayMetrics
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.view.WindowManager
import androidx.appcompat.app.AppCompatActivity
import androidx.fragment.app.Fragment
import com.lijianqiang12.silent.utils.MMKVUtils
import com.lijianqiang12.silent.DIALOG_WIDTH_PERCENT_BIG
import com.lijianqiang12.silent.MyConstants
import com.lijianqiang12.silent.R
import com.lijianqiang12.silent.component.activity.base.BaseDialogFragment
import com.lijianqiang12.silent.utils.MyToastUtil
import kotlinx.android.synthetic.main.dialog_receiver_info.view.*


class VIPApplyDeliveryInfoDialog() : BaseDialogFragment() {

    constructor(fragment: Fragment) : this() {
        this.fragment = fragment
    }

    constructor(activity: AppCompatActivity) : this() {
        this.activity = activity
    }

    private var deliveryInfoListener: OnApplyDeliveryInfoListener? = null
    private lateinit var v: View
    private var fragment: Fragment? = null
    private var activity: AppCompatActivity? = null


    override fun onCreateView(inflater: LayoutInflater, container: ViewGroup?, savedInstanceState: Bundle?): View? {
        dialog!!.window!!.setBackgroundDrawable(ColorDrawable(Color.TRANSPARENT))
        v = inflater.inflate(R.layout.dialog_apply_delivery_info, container, false)

        return v
    }

    override fun onDismiss(dialog: DialogInterface) {
        super.onDismiss(dialog)
        MMKVUtils.put(MyConstants.SP_KEY_DELIVERY_NAME, v.et_receiver_name.text.toString())
        MMKVUtils.put(MyConstants.SP_KEY_DELIVERY_PHONE, v.et_receiver_phone.text.toString())
        MMKVUtils.put(MyConstants.SP_KEY_DELIVERY_ADDRESS, v.et_receiver_address.text.toString())
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)

        v.et_receiver_name.setText(MMKVUtils.getString(MyConstants.SP_KEY_DELIVERY_NAME, ""))
        v.et_receiver_phone.setText(MMKVUtils.getString(MyConstants.SP_KEY_DELIVERY_PHONE, ""))
        v.et_receiver_address.setText(MMKVUtils.getString(MyConstants.SP_KEY_DELIVERY_ADDRESS, ""))

        v.btn_delivery_ok.setOnClickListener {
            deliveryInfoListener?.apply {
                val name = v.et_receiver_name.text.toString()
                if (name.isEmpty()) {
                    MyToastUtil.showInfo("请先填写收件人姓名")
                    return@setOnClickListener
                }
                val phone = v.et_receiver_phone.text.toString()
                if (phone.isEmpty()) {
                    MyToastUtil.showInfo("请先填写收件人电话")
                    return@setOnClickListener
                }
                val address = v.et_receiver_address.text.toString()
                if (address.isEmpty()) {
                    MyToastUtil.showInfo("请先填写收件人地址")
                    return@setOnClickListener
                }
                if (address.length <= 5) {
                    MyToastUtil.showInfo("请填写详细的收件人地址")
                    return@setOnClickListener
                }
                onFinish(name, phone, address)
                <EMAIL>()
            }
        }
    }


    fun show() {
        activity?.apply {
            super.show(this.supportFragmentManager, "NormalDialog")
        }

        fragment?.apply {
            super.show(fragment!!.requireFragmentManager(), "NormalDialog")
        }
    }

    override fun onStart() {
        val params = dialog!!.window!!.attributes
        val dm: DisplayMetrics = resources.displayMetrics
        val width = dm.widthPixels
        params.width = (width * DIALOG_WIDTH_PERCENT_BIG).toInt()//ViewGroup.LayoutParams.MATCH_PARENT
        dialog!!.window!!.attributes = params as WindowManager.LayoutParams
        super.onStart()
    }


    fun setOnApplyDeliveryInfoListener(deliveryInfoListener: OnApplyDeliveryInfoListener) {
        this.deliveryInfoListener = deliveryInfoListener
    }


    interface OnApplyDeliveryInfoListener {
        fun onFinish(name: String, phone: String, address: String)
    }
}