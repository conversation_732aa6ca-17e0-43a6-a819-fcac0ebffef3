package com.lijianqiang12.silent.component.activity.me.baozang

import android.content.Intent
import android.net.Uri
import android.os.Bundle
import androidx.lifecycle.lifecycleScope
import androidx.recyclerview.widget.LinearLayoutManager
import androidx.recyclerview.widget.RecyclerView
import com.lijianqiang12.silent.R
import com.lijianqiang12.silent.data.model.net.MyRetrofitClient
import com.lijianqiang12.silent.data.model.net.pojos.BaoZangApp
import com.lijianqiang12.silent.component.activity.base.BaseActivity
import com.lijianqiang12.silent.component.activity.custom.dialog.NormalDialog
import com.lijianqiang12.silent.component.activity.custom.dialog.OnCancelClickListener
import com.lijianqiang12.silent.component.activity.custom.dialog.OnOKClickListener
import com.lijianqiang12.silent.utils.MyToastUtil
import com.lijianqiang12.silent.utils.openFromMarket
import kotlinx.android.synthetic.main.activity_hutui.*
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.launch
import kotlinx.coroutines.withContext

class HutuiActivity : BaseActivity() {

    private lateinit var mAdapter: BaozangAdapter
    private lateinit var mLayoutManager: RecyclerView.LayoutManager

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        setContentView(R.layout.activity_hutui)
        iv_return_hutui.setOnClickListener { finish() }

        mLayoutManager = LinearLayoutManager(this)
        val recyclerview = rv_bzaozang
        recyclerview.layoutManager = mLayoutManager
        mAdapter = BaozangAdapter(R.layout.item_baozang_app, mutableListOf())
        mAdapter.animationEnable = true
        recyclerview.adapter = mAdapter

        mAdapter.setOnItemClickListener { adapter, view, position ->

            NormalDialog(this).apply {
                setTitle((adapter.data[position] as BaoZangApp).title)
                setContent((adapter.data[position] as BaoZangApp).content)
                setOnNormalOKClickListener("查看折扣", object : OnOKClickListener {
                    override fun onclick() {
                        val uri = Uri.parse((adapter.data[position] as BaoZangApp).taobaoUrl)
                        startActivity(Intent(Intent.ACTION_VIEW, uri))
                    }
                })
                setOnNormalCancelClickListener("下载试用", object : OnCancelClickListener {
                    override fun onclick() {
                        if ((adapter.data[position] as BaoZangApp).downloadUrl.isEmpty()) {
                            openFromMarket(this@HutuiActivity, (adapter.data[position] as BaoZangApp).pkg, "https://shop446228813.taobao.com/?spm=a230r.7195193.1997079397.2.714530d68VuHLF")
                        } else {
                            val uri = Uri.parse((adapter.data[position] as BaoZangApp).downloadUrl)
                            startActivity(Intent(Intent.ACTION_VIEW, uri))
                        }
                    }
                })
                showDialog()
            }

        }

        val swipeRefreshLayout = srl_baozang
        swipeRefreshLayout.setOnRefreshListener {
            getNewData()
        }
        swipeRefreshLayout.isRefreshing = true
        getNewData()

//        cl_hutui_20s.setOnClickListener {
//            openFromMarket(this, "com.shuge888.protecteyes", "https://www.coolapk.com/apk/com.shuge888.protecteyes")
//        }
//
////        cl_hutui_chunchun.setOnClickListener {
////            if (com.blankj.utilcode.util.RomUtils.isHuawei()) {
////                openFromMarket(this, "com.drakeet.purewriter", "https://www.coolapk.com/apk/com.drakeet.purewriter")
////            } else {
////                val uri = Uri.parse("https://www.coolapk.com/apk/com.drakeet.purewriter")
////                startActivity(Intent(Intent.ACTION_VIEW, uri))
////            }
////        }
//
//        cl_hutui_maque.setOnClickListener {
//            if (com.blankj.utilcode.util.RomUtils.isHuawei() || RomUtil.isMiui() || RomUtil.isOppo() || RomUtil.isVivo()) {
//                openFromMarket(this, "com.chrissen.card", "https://www.coolapk.com/apk/com.chrissen.card")
//            } else {
//                val uri = Uri.parse("https://www.coolapk.com/apk/204800")
//                startActivity(Intent(Intent.ACTION_VIEW, uri))
//            }
//        }
//
////        cl_hutui_qianji.setOnClickListener {
////            if (com.blankj.utilcode.util.RomUtils.isHuawei()) {
////                openFromMarket(this, "com.mutangtech.qianji", "https://www.coolapk.com/apk/com.mutangtech.qianji")
////            } else {
////                val uri = Uri.parse("https://www.coolapk.com/apk/com.mutangtech.qianji")
////                startActivity(Intent(Intent.ACTION_VIEW, uri))
////            }
////        }
//
////        cl_hutui_secai.setOnClickListener {
////            if (com.blankj.utilcode.util.RomUtils.isHuawei()) {
////                openFromMarket(this, "com.wizeyes.colorcapture", "https://www.coolapk.com/apk/231885")
////            } else {
////                val uri = Uri.parse("https://www.coolapk.com/apk/231885")
////                startActivity(Intent(Intent.ACTION_VIEW, uri))
////            }
////        }
//
//        cl_hutui_todo.setOnClickListener {
//
//            val uri = Uri.parse("https://www.evestudio.cn/iamfine_ylsj")
//            startActivity(Intent(Intent.ACTION_VIEW, uri))
//
//        }
//
//        cl_hutui_alipay.setOnClickListener {
//
//            val uri = Uri.parse("https://render.alipay.com/p/opx/normal-k3ojh74v/recv-redbag.html?bizSource=zhaijia&partnerId=&sceneCode=C2C_APP_NEW&shareChannel=QRCode&shareUserId=2088602033670979&sharedUserId=")
//            startActivity(Intent(Intent.ACTION_VIEW, uri))
//
//        }
//
//        cl_hutui_bilibili.setOnClickListener {
//
//            val uri = Uri.parse("https://wedmoment.cn/zhgd71wJp8d9mAMhq1594194420/activity-IYJ579aCV.html?token=1c208e72ea554845ea4470f6f2402da5&_ts=1594298919455&channel=weixin&share_medium=android&share_source=weixin&bbid=XY1D3CD16B1961E13D2F185A956326B744D1C&ts=1594298919484")
//            startActivity(Intent(Intent.ACTION_VIEW, uri))
//
//        }
//
//        cl_hutui_eleme.setOnClickListener {
//
//            val uri = Uri.parse("https://h5.ele.me/fire/water/?refer_id=45525482&refer_code=7fc04e1938e47ff3bf92d48a4144920d&refer_channel_code=1&refer_channel_type=3")
//            startActivity(Intent(Intent.ACTION_VIEW, uri))
//
//        }
//
//        cl_hutui_yunshanfu.setOnClickListener {
//
//            val uri = Uri.parse("https://wallet.95516.com/s/wl/webV3/activity/vInvite/html/snsIndex.html?r=557fe29f46da9c77efc7650576131001&code=ctoc00000000089&channel=3")
//            startActivity(Intent(Intent.ACTION_VIEW, uri))
//
//        }

    }

    private fun getNewData() {
        lifecycleScope.launch(Dispatchers.IO) {
            try {
                val result = MyRetrofitClient.service.getBaozang()
                withContext(Dispatchers.Main) {
                    if (result.code == 200) {
                        result.data?.let {
                            mAdapter.setNewInstance(it)
                            mAdapter.notifyDataSetChanged()
                        }
                    } else {
                        MyToastUtil.showInfo(result.msg)
                    }
                    srl_baozang.isRefreshing = false
                }
            } catch (e: Exception) {
                withContext(Dispatchers.Main) {
                    MyToastUtil.showInfo(e.message)
                    srl_baozang.isRefreshing = false
                }
            } finally {
                withContext(Dispatchers.Main) {
                    srl_baozang.isRefreshing = false
                }
            }
        }
    }
}
