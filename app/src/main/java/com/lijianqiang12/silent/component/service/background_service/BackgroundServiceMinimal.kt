package com.lijianqiang12.silent.component.service.background_service

import android.app.Service
import android.content.Intent
import android.os.IBinder
import android.util.Log

/**
 * 最小化的BackgroundService版本
 * 只保留必要的枚举和基本结构，避免ViewModel依赖问题
 * TODO: 后续需要重构为Repository模式
 */
class BackgroundServiceMinimal : Service() {

    override fun onCreate() {
        super.onCreate()
        Log.d("BackgroundService", "最小化版本启动 - 功能受限")
    }

    override fun onStartCommand(intent: Intent?, flags: Int, startId: Int): Int {
        Log.d("BackgroundService", "onStartCommand - 最小化版本")
        return START_STICKY
    }

    override fun onBind(intent: Intent?): IBinder? {
        return null
    }

    override fun onDestroy() {
        super.onDestroy()
        Log.d("BackgroundService", "最小化版本销毁")
    }
}

// 保留必要的枚举，供其他文件使用
enum class RunState {
    RUNNING, STOP
}

enum class WorkState {
    WORK, REST
}

// 保留必要的扩展函数
fun weekValid(week: String): Bo<PERSON>an {
    return week.isNotEmpty() && week.length == 7 && week.all { it == '0' || it == '1' }
}
