package com.lijianqiang12.silent.component.activity.room.myjoined


import android.content.Intent
import android.os.Bundle
import android.util.Log
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.fragment.app.viewModels
import androidx.lifecycle.lifecycleScope
import androidx.recyclerview.widget.DiffUtil
import androidx.recyclerview.widget.GridLayoutManager
import com.jeremyliao.liveeventbus.LiveEventBus
import com.lijianqiang12.silent.LiveBus
import com.lijianqiang12.silent.MyConstants
import com.lijianqiang12.silent.R
import com.lijianqiang12.silent.data.model.net.pojos.MyJoinedRoom
import com.lijianqiang12.silent.component.activity.base.BaseFragment
import com.lijianqiang12.silent.component.activity.room.allroom.RoomAllActivity
import com.lijianqiang12.silent.component.activity.room.createroom.CreateRoomActivity
import com.lijianqiang12.silent.component.activity.room.roomdetail.DetailRoomActivity
import com.lijianqiang12.silent.data.viewmodel.RoomViewModel
import com.lijianqiang12.silent.utils.MMKVUtils
import com.lijianqiang12.silent.utils.MyUtil
import dagger.hilt.android.AndroidEntryPoint
import kotlinx.android.synthetic.main.fragment_room.view.*
import kotlinx.android.synthetic.main.header_room_joined.view.*
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.launch
import javax.inject.Inject
import kotlinx.coroutines.withContext


private const val ARG_PARAM1 = "param1"
private const val ARG_PARAM2 = "param2"

@AndroidEntryPoint
class RoomFragment : BaseFragment() {
    private val TAG = "RoomFragment"
    private var param1: String? = null
    private var param2: String? = null
    private lateinit var v: View
    private lateinit var mAdapter: RoomJoinedAdapter
    private lateinit var mLayoutManager: androidx.recyclerview.widget.RecyclerView.LayoutManager
    private lateinit var header: View
    private var myJoinedRooms: MutableList<MyJoinedRoom> = mutableListOf()

    private val viewModel: RoomViewModel by viewModels()


    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        arguments?.let {
            param1 = it.getString(ARG_PARAM1)
            param2 = it.getString(ARG_PARAM2)
        }
    }

    override fun onCreateView(inflater: LayoutInflater, container: ViewGroup?, savedInstanceState: Bundle?): View {
        v = inflater.inflate(R.layout.fragment_room, container, false)
        return v
    }

    override fun lazyInit() {
//        for(i in 0..1000){
//            NdkUtils().getSign("temp.toString()")
//        }

        mLayoutManager = GridLayoutManager(requireContext(), 3)
        val recyclerview = v.rv_room_joined
        val emptyView = v.cl_room_empty_view
        recyclerview.layoutManager = mLayoutManager
        mAdapter = RoomJoinedAdapter(R.layout.item_room_joined, mutableListOf())
        mAdapter.animationEnable = true
        header = LayoutInflater.from(requireContext()).inflate(R.layout.header_room_joined, recyclerview, false)
        mAdapter.setHeaderView(header)
        recyclerview.adapter = mAdapter

        mAdapter.setOnItemClickListener { adapter, view, position ->
            val intent = Intent(requireContext(), DetailRoomActivity::class.java)
            intent.putExtra("roomId", (adapter.data[position] as MyJoinedRoom).roomId)
            startActivity(intent)
        }


        header.btn_room_all.setOnClickListener {
//            ActivityUtils.startActivity(RoomAllActivity::class.java)
            startActivity(Intent(requireActivity(), RoomAllActivity::class.java))
        }

        emptyView.btn_empty_view_join.setOnClickListener {

            MyUtil.checkLoginAndDo(requireActivity()) {
                startActivity(Intent(requireActivity(), RoomAllActivity::class.java))
            }
        }
        emptyView.btn_empty_view_create.setOnClickListener {
            MyUtil.checkLoginAndDo(requireActivity()) {
                startActivity(Intent(requireActivity(), CreateRoomActivity::class.java))
            }
        }

        val swipeRefreshLayout = v.srl_room
        swipeRefreshLayout.setOnRefreshListener {
            viewModel.refreshMyJoinedRooms()
        }
        swipeRefreshLayout.isRefreshing = true
        viewModel.myJoinedRoomsLiveData.observe(viewLifecycleOwner) {
            emptyView.visibility = View.GONE
            recyclerview.visibility = View.GONE
            if (it.isEmpty()) {
                emptyView.visibility = View.VISIBLE
                recyclerview.visibility = View.GONE

            } else {
                emptyView.visibility = View.GONE
                recyclerview.visibility = View.VISIBLE
            }

            lifecycleScope.launch(Dispatchers.Default) {
                val diffResult = DiffUtil.calculateDiff(DiffCallBack(myJoinedRooms, it), true)
                withContext(Dispatchers.Main) {
                    (recyclerview.adapter as RoomJoinedAdapter).setNewInstance(it)
                    myJoinedRooms = it
                    diffResult.dispatchUpdatesTo(recyclerview.adapter as RoomJoinedAdapter)
                }
            }

            swipeRefreshLayout.isRefreshing = false
        }

        LiveEventBus.get(LiveBus.REFRESH_JOINED_ROOM_PAGE, Boolean::class.java).observe(this) {
            viewModel.refreshMyJoinedRooms()
        }

//        v.cl_room_empty_view.setOnClickListener {
        //            viewModel.refreshMyJoinedRooms()
//        }


    }

    private class DiffCallBack(val oldList: MutableList<MyJoinedRoom>, val newList: MutableList<MyJoinedRoom>) : DiffUtil.Callback() {

        override fun areItemsTheSame(oldItemPosition: Int, newItemPosition: Int): Boolean {
            return (oldList[oldItemPosition].roomId == newList[newItemPosition].roomId)
        }

        override fun getOldListSize(): Int {
            return oldList.size
        }

        override fun getNewListSize(): Int {
            return newList.size
        }

        override fun areContentsTheSame(oldItemPosition: Int, newItemPosition: Int): Boolean {
            return (oldList[oldItemPosition].roomName == newList[newItemPosition].roomName)
                    && (oldList[oldItemPosition].roomOnlineNumbers == newList[newItemPosition].roomOnlineNumbers)
                    && (oldList[oldItemPosition].roomCoverUrl == newList[newItemPosition].roomCoverUrl)
        }

    }

    companion object {

        @JvmStatic
        fun newInstance(param1: String, param2: String) =
            RoomFragment().apply {
                arguments = Bundle().apply {
                    putString(ARG_PARAM1, param1)
                    putString(ARG_PARAM2, param2)
                }
            }
    }

    override fun onResume() {
        super.onResume()
        Log.d(TAG, "onResume")


//        viewModel.refreshMyJoinedRooms()


////        MobclickAgent.onPageStart("RoomFragment")
//        if (MMKVUtils.getInt(MyConstants.SP_KEY_USER_ID, -1) == -1) {
//            v.cl_room_need_login.visibility = View.VISIBLE
//        } else {
//            v.cl_room_need_login.visibility = View.GONE
//
//        }
    }
//
//
//    override fun onPause() {
//        super.onPause()
//        MobclickAgent.onPageEnd("RoomFragment")
//    }
}
