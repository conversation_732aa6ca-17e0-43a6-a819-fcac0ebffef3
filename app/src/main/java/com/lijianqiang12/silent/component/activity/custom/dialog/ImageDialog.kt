package com.lijianqiang12.silent.component.activity.custom.dialog

import android.graphics.Color
import android.graphics.drawable.ColorDrawable
import android.os.Bundle
import android.util.DisplayMetrics
import android.view.*
import androidx.appcompat.app.AppCompatActivity
import androidx.fragment.app.Fragment
import com.bumptech.glide.Glide
import com.bumptech.glide.load.engine.DiskCacheStrategy
import com.bumptech.glide.load.resource.drawable.DrawableTransitionOptions
import com.lijianqiang12.silent.DIALOG_WIDTH_PERCENT_MIDDLE
import com.lijianqiang12.silent.R
import com.lijianqiang12.silent.component.activity.base.BaseDialogFragment
import kotlinx.android.synthetic.main.dialog_image.view.*
import kotlinx.android.synthetic.main.dialog_normal.view.tv_dialog_normal_cancel
import kotlinx.android.synthetic.main.dialog_normal.view.tv_dialog_normal_ok
import kotlinx.android.synthetic.main.dialog_normal.view.tv_dialog_normal_title


class ImageDialog() : BaseDialogFragment() {

    constructor(fragment: Fragment) : this() {
        this.fragment = fragment
    }

    constructor(activity: AppCompatActivity) : this() {
        this.activity = activity
    }

    private var okListener: OnOKClickListener? = null
    private var cancelListener: OnCancelClickListener? = null
    private lateinit var v: View
    private var title = ""
    private var content = ""
    private var imageUrl = ""
    private var fragment: Fragment? = null
    private var activity: AppCompatActivity? = null
    private var okText = "确定"
    private var cancelText = "取消"
    private var gravity = Gravity.CENTER

//    private var cancelable:Boolean = true


    override fun onCreateView(inflater: LayoutInflater, container: ViewGroup?, savedInstanceState: Bundle?): View? {
        v = inflater.inflate(R.layout.dialog_image, container, false)
        return v
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)

        dialog!!.window!!.setBackgroundDrawable(ColorDrawable(Color.TRANSPARENT))
        if (savedInstanceState != null) {
            title = savedInstanceState.get("title") as String
            content = savedInstanceState.get("content") as String
            imageUrl = savedInstanceState.get("imageUrl") as String
        }


        v.tv_dialog_normal_title.text = title

        if (content.isEmpty()) {
            v.tv_dialog_normal_content.visibility = View.GONE
        } else {
            v.tv_dialog_normal_content.visibility = View.VISIBLE
            v.tv_dialog_normal_content.text = content
            v.tv_dialog_normal_content.gravity = gravity
        }

        Glide.with(this).load(imageUrl)
            //.transition(DrawableTransitionOptions.withCrossFade())
            .diskCacheStrategy(DiskCacheStrategy.AUTOMATIC)
            .into(v.iv_dialog_image)

        v.tv_dialog_normal_ok.text = okText
        v.tv_dialog_normal_cancel.text = cancelText

        okListener?.apply {
            v.tv_dialog_normal_ok.visibility = View.VISIBLE
        }
        cancelListener?.apply {
            v.tv_dialog_normal_cancel.visibility = View.VISIBLE
        }
        v.tv_dialog_normal_ok.setOnClickListener {
            okListener?.apply {
                onclick()
            }
            <EMAIL>()
        }
        v.tv_dialog_normal_cancel.setOnClickListener {
            cancelListener?.apply {
                onclick()
            }
            <EMAIL>()
        }
    }

    fun setGravity(gravity: Int) {
        this.gravity = gravity
    }

    fun setTitle(arg: String) {
        title = arg
    }

    fun setContent(arg: String) {
        content = arg
    }

    fun setImageUrl(arg: String) {
        imageUrl = arg
    }


    fun show() {
        activity?.apply {
            super.show(this.supportFragmentManager, "NormalDialog")
        }

        fragment?.apply {
            super.show(fragment!!.requireFragmentManager(), "NormalDialog")
        }
    }

    override fun onStart() {
        val params = dialog!!.window!!.attributes
        val dm: DisplayMetrics = resources.displayMetrics
//        val density = dm.density
        val width = dm.widthPixels
//        val height = dm.heightPixels
        params.width = (width * DIALOG_WIDTH_PERCENT_MIDDLE).toInt()//ViewGroup.LayoutParams.MATCH_PARENT
//        params.width = width.coerceAtMost(height) * 3 / 4//ViewGroup.LayoutParams.MATCH_PARENT
        dialog!!.window!!.attributes = params as WindowManager.LayoutParams
        super.onStart()
    }

    fun setOnNormalOKClickListener(okListener: OnOKClickListener) {
        this.okListener = okListener
    }

    fun setOnNormalCancelClickListener(cancelListener: OnCancelClickListener) {
        this.cancelListener = cancelListener
    }

    fun setOnNormalOKClickListener(okText: String, okListener: OnOKClickListener) {
        this.okText = okText
        this.okListener = okListener
    }

    fun setOnNormalCancelClickListener(cancelText: String, cancelListener: OnCancelClickListener) {
        this.cancelText = cancelText
        this.cancelListener = cancelListener
    }

//    fun setCancelable(){
//
//    }


    override fun onSaveInstanceState(outState: Bundle) {
        super.onSaveInstanceState(outState)
        outState.putString("title", title)
        outState.putString("content", content)
        outState.putString("imageUrl", imageUrl)
    }

}