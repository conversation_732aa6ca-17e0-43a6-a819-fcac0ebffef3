package com.lijianqiang12.silent.component.service.windows

import android.accessibilityservice.AccessibilityService
import android.annotation.SuppressLint
import android.content.Context
import android.graphics.PixelFormat
import android.os.Build
import android.view.*
import com.blankj.utilcode.util.ConvertUtils
import com.blankj.utilcode.util.LogUtils
import com.lijianqiang12.silent.MyConstants
import com.lijianqiang12.silent.R
import com.lijianqiang12.silent.TheApplication
import com.lijianqiang12.silent.utils.*
import kotlinx.android.synthetic.main.layout_deny_drop_lock.view.*

class FloatWindowOfDenyDrop(val context: Context) {

    //    var denyDropWindowCreated = false
//    private var denyDropWindowShowing = false
    private lateinit var denyDropParams: WindowManager.LayoutParams
    private lateinit var denyWindowManager: WindowManager
    private var denyDropLayout: View? = null
//    private var ifPerformHome = false

    @SuppressLint("ClickableViewAccessibility")
    private fun createDenyDropWindow() {

        denyDropParams = WindowManager.LayoutParams()

        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
//        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.LOLLIPOP_MR1) {
            denyDropParams.type = WindowManager.LayoutParams.TYPE_ACCESSIBILITY_OVERLAY
        } else {
            denyDropParams.type = WindowManager.LayoutParams.TYPE_SYSTEM_ERROR
        }
        denyWindowManager = TheApplication.getInstance().globalParams.accessibilityService!!.getSystemService(Context.WINDOW_SERVICE) as WindowManager
        denyDropParams.format = PixelFormat.TRANSLUCENT

        denyDropParams.flags = denyDropParams.flags or WindowManager.LayoutParams.FLAG_NOT_FOCUSABLE or
                WindowManager.LayoutParams.FLAG_NOT_TOUCH_MODAL or
                WindowManager.LayoutParams.FLAG_LAYOUT_IN_SCREEN or
                WindowManager.LayoutParams.FLAG_LAYOUT_NO_LIMITS or
                WindowManager.LayoutParams.FLAG_HARDWARE_ACCELERATED

        denyDropParams.gravity = Gravity.START or Gravity.TOP
        denyDropParams.x = 0
        denyDropParams.y = 0
//        denyDropParams.screenOrientation = ActivityInfo.SCREEN_ORIENTATION_PORTRAIT
        denyDropParams.width = WindowManager.LayoutParams.MATCH_PARENT
        denyDropParams.height = WindowManager.LayoutParams.WRAP_CONTENT

        denyDropLayout = LayoutInflater.from(context.applicationContext).inflate(R.layout.layout_deny_drop_lock, null)
        denyDropLayout?.let {
            val lp = it.tv_drop_text.layoutParams
//            lp.height = ConvertUtils.dp2px(100f)
            lp.height = ConvertUtils.dp2px(MMKVUtils.getFloat(MyConstants.SP_KEY_STATUS_BAR_HEIGHT, 32f))
            it.tv_drop_text.layoutParams = lp
        }
        denyDropLayout?.setOnTouchListener { v, event ->
            when (event.action) {
                MotionEvent.ACTION_DOWN -> {
                    denyDropLayout?.let {
                        it.fl_tiao.setBackgroundColor(context.resources.getColor(R.color.colorRed))
                        it.tv_drop_text.text = "您设置了禁止下拉状态栏"
                        TheApplication.getInstance().globalParams.accessibilityService?.performGlobalAction(AccessibilityService.GLOBAL_ACTION_HOME)
                    }
                }
                MotionEvent.ACTION_UP -> {
                    denyDropLayout?.let {
                        it.fl_tiao.setBackgroundColor(context.resources.getColor(R.color.colorTranslate))
                        it.tv_drop_text.text = ""
                        TheApplication.getInstance().globalParams.accessibilityService?.performGlobalAction(AccessibilityService.GLOBAL_ACTION_HOME)
                    }
                }
            }

            false
        }
//        denyDropWindowCreated = true
    }

    fun showDenyWindow() {
        if (denyDropLayout == null) {
            createDenyDropWindow()
        }
//        LogUtils.d("show DenyDropFloatWindow")
        if (!MyWindowUtil.isWindowShowing(denyDropLayout!!)) {
            LogUtils.d("show true DenyDropFloatWindow")
//            GlobalScope.launch(Dispatchers.IO) {
//                ifPerformHome = true
//                while (ifPerformHome) {
//                    collapsingNotification(context)
//                    performHome(context)
//                    delay(100)
//                }
//            }
//            denyDropWindowShowing = true
            try {
                denyWindowManager.addView(denyDropLayout, denyDropParams)
            } catch (e: Exception) {
//                MyToastUtil.showError(e.message!!)

                LogUtils.d("DenyDropFloatWindow未成功获取到悬浮窗权限，请到系统设置或安全管家中重新授予，若已经授予，则可能是系统BUG，请重启手机\n\n" + e.toString())
                MyToastUtil.showError("DenyDropFloatWindow未成功获取到悬浮窗权限，请到系统设置或安全管家中重新授予，若已经授予，则可能是系统BUG，请重启手机\n\n" + e.toString())


                DialogUtil.showFloatingDialog(
                    title = "屏蔽下拉菜单栏失败",
                    content = "DenyDropFloatWindow未成功获取到无障碍权限，请到系统设置中重新授予，若已经授予，则可能是系统BUG，请重启手机\n\n" + e.toString(),
                )

            }
        }

    }

    fun hideDenyWindow() {
        if (denyDropLayout != null) {
            if (MyWindowUtil.isWindowShowing(denyDropLayout!!)) {
//                ifPerformHome = false
//                denyDropWindowShowing = false
                try {
                    denyWindowManager.removeView(denyDropLayout)
                } catch (e: Exception) {
                    MyToastUtil.showError(e.message!!)
                }
            }
        }
    }
}