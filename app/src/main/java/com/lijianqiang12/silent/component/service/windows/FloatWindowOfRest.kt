package com.lijianqiang12.silent.component.service.windows

import android.annotation.SuppressLint
import android.content.Context
import android.graphics.PixelFormat
import android.os.Build
import android.view.*
import android.widget.TextView
import com.blankj.utilcode.util.LogUtils
import com.blankj.utilcode.util.ScreenUtils
import com.lijianqiang12.silent.R
import com.lijianqiang12.silent.utils.MyToastUtil
import com.lijianqiang12.silent.utils.MyWindowUtil
import kotlinx.android.synthetic.main.layout_lock_view_rest.view.*

class FloatWindowOfRest(val context: Context) {

    //     var windowCreated = false
//    private var windowShowing = false
    private lateinit var params: WindowManager.LayoutParams
    private lateinit var windowManager: WindowManager
    var layout: View? = null
    var showRest = true

    @SuppressLint("ClickableViewAccessibility")
    private fun createView() {
        params = WindowManager.LayoutParams()

        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
            params.type = WindowManager.LayoutParams.TYPE_APPLICATION_OVERLAY
        } else {
            params.type = WindowManager.LayoutParams.TYPE_SYSTEM_ALERT
        }

        windowManager = context.applicationContext.getSystemService(Context.WINDOW_SERVICE) as WindowManager

        params.format = PixelFormat.TRANSLUCENT

        params.flags = params.flags or WindowManager.LayoutParams.FLAG_NOT_FOCUSABLE or
                WindowManager.LayoutParams.FLAG_NOT_TOUCH_MODAL or
                WindowManager.LayoutParams.FLAG_HARDWARE_ACCELERATED
        params.gravity = Gravity.START or Gravity.TOP
        params.x = 0
        params.y = 0
        params.width = WindowManager.LayoutParams.WRAP_CONTENT
        params.height = WindowManager.LayoutParams.WRAP_CONTENT

        layout = LayoutInflater.from(context).inflate(R.layout.layout_lock_view_rest, null)
        layout?.let {
            it.iv_rest_view_close.setOnClickListener {
                showRest = false
            }
            var lastX = 0f
            var lastY = 0f
            it.setOnTouchListener { v, event ->

                when (event.action) {
                    MotionEvent.ACTION_DOWN -> {
                        it.setBackgroundResource(R.drawable.shape_lock_view_rest)
                        lastX = event.rawX
                        lastY = event.rawY
                    }
                    MotionEvent.ACTION_MOVE -> {
                        params.x += (event.rawX - lastX).toInt()
                        params.y += (event.rawY - lastY).toInt()

                        LogUtils.d("ACTION_MOVE, params.x=" + params.x + ", params.x=" + params.x + ",rawX=" + event.rawX + ",rawY=" + event.rawY + ",lastX=" + lastX + ",lastY=" + lastY)

                        windowManager.updateViewLayout(it, params)
                        lastX = event.rawX
                        lastY = event.rawY

                    }
                    MotionEvent.ACTION_UP -> {
                        if (it.width / 2 - event.x + event.rawX < ScreenUtils.getScreenWidth() / 2) {//left
                            params.x = 0
                            it.setBackgroundResource(R.drawable.shape_lock_view_rest_left)
                        } else {
                            params.x = ScreenUtils.getScreenWidth() - it.width
                            it.setBackgroundResource(R.drawable.shape_lock_view_rest_right)
                        }
                        windowManager.updateViewLayout(it, params)

                    }
                }
                false

            }
        }
//        windowCreated = true
    }

    suspend fun releaseLockView() {
        layout = null
    }

    fun refreshText(text: String) {
        if (layout != null && MyWindowUtil.isWindowShowing(layout!!)) {
            layout!!.findViewById<TextView>(R.id.tv_lock_view_rest).text = text
        }
    }

    fun showWindow() {
        if (layout == null) {
            createView()
        }
        if (!MyWindowUtil.isWindowShowing(layout!!)) {
//            windowShowing = true
            try {
                windowManager.addView(layout, params)
            } catch (e: Exception) {
                MyToastUtil.showError("RestFloatWindow未成功获取到悬浮窗权限，请到系统设置或安全管家中重新授予，若已经授予，则可能是系统BUG，请重启手机")
//                windowShowing = false

            }

        }
    }

    fun hideWindow() {
        if (layout != null) {
            if (MyWindowUtil.isWindowShowing(layout!!)) {
                try {
                    windowManager.removeView(layout)
//                    windowShowing = false
                } catch (e: Exception) {
                    MyToastUtil.showError("未成功获取到悬浮窗权限，请到系统设置或安全管家中重新授予，若已经授予，则可能是系统BUG，请重启手机")
                }
            }
        }
    }
}