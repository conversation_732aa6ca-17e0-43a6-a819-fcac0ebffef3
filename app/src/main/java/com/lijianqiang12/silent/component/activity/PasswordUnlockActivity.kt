package com.lijianqiang12.silent.component.activity

import android.os.Bundle
import androidx.lifecycle.Observer
import com.jeremyliao.liveeventbus.LiveEventBus
import com.lijianqiang12.silent.LiveBus
import com.lijianqiang12.silent.MyConstants
import com.lijianqiang12.silent.R
import com.lijianqiang12.silent.component.activity.base.BaseActivity
import com.lijianqiang12.silent.utils.MMKVUtils
import kotlinx.android.synthetic.main.activity_pwd_force_unlock.*

class PasswordUnlockActivity : BaseActivity() {


//    private var forceUnlockType = ""

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        setContentView(R.layout.activity_pwd_force_unlock)
//        intent.getStringExtra("forceUnlockType")?.let {
//            forceUnlockType = it
//        }
        LiveEventBus.get(LiveBus.FORCE_UNLOCK_PAGE_FINISH, String::class.java).observe(this, Observer {
            finish()
        })

        btn_force_unlock_pwd.setOnClickListener {


            if (et_force_unlock_pwd.text.isEmpty()) {
                et_force_unlock_pwd.error = "密码不能为空"
//                MyToastUtil.showError("密码不能为空")
            } else {
                val rightPwd = MMKVUtils.getString(MyConstants.SP_KEY_FORCE_QUITE_PWD, "OFFPHONE")
                if (rightPwd == et_force_unlock_pwd.text.toString()) {
                    LiveEventBus.get(LiveBus.FORCE_QUIT, String::class.java).post("")
//                    LiveBus.getInstance().with(String::class.java).postValue(LiveBus.FORCE_QUIT)
                    finish()
                } else {
                    et_force_unlock_pwd.error = "密码错误"
//                    MyToastUtil.showError("密码错误")
                }
            }
        }
    }
}