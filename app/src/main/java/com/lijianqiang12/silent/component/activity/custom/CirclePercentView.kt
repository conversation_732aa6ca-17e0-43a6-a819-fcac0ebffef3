package com.lijianqiang12.silent.component.activity.custom

import android.content.Context
import android.graphics.Canvas
import android.graphics.Paint
import android.graphics.RectF
import android.util.AttributeSet
import android.view.View
import com.lijianqiang12.silent.R
import com.lijianqiang12.silent.utils.dpToPixel
import com.lijianqiang12.silent.utils.getColorFromTheme

class CirclePercentView : View {

    private var progress = 0f
    private val paint = Paint(Paint.ANTI_ALIAS_FLAG)
    private val paint2 = Paint(Paint.ANTI_ALIAS_FLAG)
    private val textPaint = Paint(Paint.ANTI_ALIAS_FLAG)
    private var arcRectF = RectF()

    init {
        textPaint.textSize = dpToPixel(10f)
        textPaint.textAlign = Paint.Align.CENTER
    }

    constructor(context: Context) : super(context)
    constructor(context: Context, attrs: AttributeSet?) : super(context, attrs)
    constructor(context: Context, attrs: AttributeSet?, defStyleAttr: Int) : super(
        context,
        attrs,
        defStyleAttr
    )

    fun getProgress(): Float {
        return progress
    }

    fun setProgress(progress: Float) {
        this.progress = progress
        invalidate()
    }

    override fun onDraw(canvas: Canvas) {
        super.onDraw(canvas)

        //画背景圈
        val centerX = width / 2.toFloat()
        val centerY = height / 2.toFloat()
//        val marginHorizontal = height / 2.toFloat()
        val strokeWidth = width.coerceAtMost(height) / 10f
        val radius = (width.coerceAtMost(height) - strokeWidth) / 2

        paint.color = getColorFromTheme(context,R.attr.custom_attr_app_outer_line)//ContextCompat.getColor(context, R.color.colorDivider)
        paint.style = Paint.Style.STROKE
        paint.strokeCap = Paint.Cap.ROUND
        paint.strokeWidth = strokeWidth

        arcRectF.set(centerX - radius, centerY - radius, centerX + radius, centerY + radius)
        canvas!!.drawArc(
            arcRectF,
            270f,
            360f,
            false,
            paint
        )

        //画前景圈
        paint2.color = getColorFromTheme(context,R.attr.custom_attr_app_inner_line)//ContextCompat.getColor(context, R.color.colorPrimary)
        paint2.style = Paint.Style.STROKE
        paint2.strokeCap = Paint.Cap.BUTT
        paint2.strokeWidth = strokeWidth


        arcRectF.set(centerX - radius, centerY - radius, centerX + radius, centerY + radius)
        canvas.drawArc(
            arcRectF,
            270f,
            progress * 3.6f,
            false,
            paint2
        )


        //写字
        textPaint.color =  getColorFromTheme(context,R.attr.custom_attr_app_text_1)

        textPaint.style = Paint.Style.FILL
        canvas.drawText(
            " " + progress.toInt().toString() + "%",
            centerX,
            centerY - (textPaint.ascent() + textPaint.descent()) / 2,
            textPaint
        )

    }
}