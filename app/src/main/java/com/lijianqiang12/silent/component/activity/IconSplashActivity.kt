package com.lijianqiang12.silent.component.activity

import android.content.Intent
import android.os.Bundle
import androidx.core.splashscreen.SplashScreen.Companion.installSplashScreen
import com.lijianqiang12.silent.utils.MMKVUtils
import com.lijianqiang12.silent.MyConstants
import com.lijianqiang12.silent.component.activity.base.BaseActivity

class IconSplashActivity : BaseActivity() {

    override fun onCreate(savedInstanceState: Bundle?) {
        val splashScreen = installSplashScreen()
        super.onCreate(savedInstanceState)
        splashScreen.setKeepOnScreenCondition { true }


        if (MMKVUtils.getBoolean(MyConstants.SP_KEY_SHOW_START_PAGE, true)) {
            startActivity(Intent(this, StartActivity::class.java))
        } else {
            startActivity(Intent(this, TheMainActivity::class.java))
        }

        this.finish()
    }
}

