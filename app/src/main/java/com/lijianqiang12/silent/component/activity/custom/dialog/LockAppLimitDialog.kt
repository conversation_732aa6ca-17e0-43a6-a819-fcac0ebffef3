package com.lijianqiang12.silent.component.activity.custom.dialog

import android.graphics.Color
import android.graphics.drawable.ColorDrawable
import android.os.Bundle
import android.util.DisplayMetrics
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.view.WindowManager
import androidx.fragment.app.Fragment
import androidx.lifecycle.lifecycleScope
import com.lijianqiang12.silent.DIALOG_WIDTH_PERCENT_MIDDLE
import com.lijianqiang12.silent.R
import com.lijianqiang12.silent.data.model.db.WhiteApp
import com.lijianqiang12.silent.component.activity.base.BaseDialogFragment
import com.lijianqiang12.silent.component.activity.custom.TimePickerView
import com.lijianqiang12.silent.utils.getAppName
import kotlinx.android.synthetic.main.dialog_lock_app_limit_time.view.*
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.launch
import kotlinx.coroutines.withContext

class LockAppLimitDialog() : BaseDialogFragment() {

    constructor(fragment: Fragment) : this() {
        this.fragment = fragment
    }

    private var deleteWhiteListener: OnDeleteWhiteListener? = null
    private var deleteLimitListener: OnDeleteLimitListener? = null
    private var okListener: OnOKListener? = null
    private lateinit var v: View
    private var fragment: Fragment? = null
    private lateinit var whiteApp: WhiteApp
    private var timeLimitHour = 2
    private var timeLimitMinute = 0


    override fun onCreateView(inflater: LayoutInflater, container: ViewGroup?, savedInstanceState: Bundle?): View? {
        dialog!!.window!!.setBackgroundDrawable(ColorDrawable(Color.TRANSPARENT))

        v = inflater.inflate(R.layout.dialog_lock_app_limit_time, container, false)

        lifecycleScope.launch(Dispatchers.IO) {
            val appName = getAppName(whiteApp.pkg, whiteApp.mainActivity)
            withContext(Dispatchers.Main) {
                v.tv_dialog_home_app_title.text = appName
            }
        }

        val dataHour: MutableList<String> = mutableListOf()
        for (i in 0..23) {
            if (i < 10) {
                dataHour.add("0$i")
            } else {
                dataHour.add("$i")
            }
        }

        val dataMinute: MutableList<String> = mutableListOf()
        for (i in 0..59) {
            if (i < 10) {
                dataMinute.add("0$i")
            } else {
                dataMinute.add("$i")
            }
        }

        v.tpv_lock_fast_hour.setData(dataHour)
        v.tpv_lock_fast_minute.setData(dataMinute)

        v.tpv_lock_fast_hour.setSelected(timeLimitHour)
        v.tpv_lock_fast_minute.setSelected(timeLimitMinute)

        v.tpv_lock_fast_hour.setOnSelectListener(object : TimePickerView.onSelectListener {
            override fun onSelect(text: String?) {
                timeLimitHour = text!!.toInt()
            }
        })

        v.tpv_lock_fast_minute.setOnSelectListener(object : TimePickerView.onSelectListener {
            override fun onSelect(text: String?) {
                timeLimitMinute = text!!.toInt()
            }
        })


        v.btn_delete_white.setOnClickListener {
            deleteWhiteListener?.onclick()
            dismiss()
        }

        v.btn_delete_limit_time.setOnClickListener {
            deleteLimitListener?.onclick()
            dismiss()
        }

        v.btn_ok.setOnClickListener {
            okListener?.onclick(timeLimitHour * 60 + timeLimitMinute)
            dismiss()
        }

        return v
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)

    }

    fun setWhiteApp(whiteApp: WhiteApp) {
        this.whiteApp = whiteApp
        if (whiteApp.maxLen != -1) {
            timeLimitHour = whiteApp.maxLen / 60
            timeLimitMinute = whiteApp.maxLen % 60
        }
    }

    fun show() {
        super.show(fragment!!.requireFragmentManager(), "LockAppLimitDialog")
    }

    override fun onStart() {
        val params = dialog!!.window!!.attributes
        val dm: DisplayMetrics = resources.displayMetrics
        val width = dm.widthPixels
        params.width = (width * DIALOG_WIDTH_PERCENT_MIDDLE).toInt()//ViewGroup.LayoutParams.MATCH_PARENT
        dialog!!.window!!.attributes = params as WindowManager.LayoutParams
        super.onStart()
    }

    fun setOnDeleteWhiteListener(listener: OnDeleteWhiteListener) {
        this.deleteWhiteListener = listener
    }

    fun setOnDeleteLimitListener(listener: OnDeleteLimitListener) {
        this.deleteLimitListener = listener
    }

    fun setOnOKListener(listener: OnOKListener) {
        this.okListener = listener
    }

    interface OnDeleteWhiteListener {
        fun onclick()
    }

    interface OnDeleteLimitListener {
        fun onclick()
    }

    interface OnOKListener {
        fun onclick(length: Int)
    }


}