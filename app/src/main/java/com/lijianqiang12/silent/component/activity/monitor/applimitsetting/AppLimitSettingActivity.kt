package com.lijianqiang12.silent.component.activity.monitor.applimitsetting

import android.os.Bundle
import android.view.View
import com.afollestad.materialdialogs.MaterialDialog
import com.afollestad.materialdialogs.list.listItems
import com.bumptech.glide.Glide
import com.bumptech.glide.load.engine.DiskCacheStrategy
import com.lijianqiang12.silent.*
import com.lijianqiang12.silent.component.activity.base.BaseActivity
import com.lijianqiang12.silent.component.activity.custom.dialog.BottomSingleSelectDialogFragment
import com.lijianqiang12.silent.component.activity.custom.dialog.WhiteAppMonitorSelectTimeDialog
import com.lijianqiang12.silent.component.activity.lock.BgBottomSheetDialogFragment
import com.lijianqiang12.silent.utils.*
import kotlinx.android.synthetic.main.activity_app_limit_setting.*
import kotlinx.android.synthetic.main.activity_app_limit_setting.iv_setting_return

class AppLimitSettingActivity : BaseActivity() {
    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        setContentView(R.layout.activity_app_limit_setting)

        iv_setting_return.setOnClickListener { finish() }

        //背景图片
        var currentUrl = MMKVUtils.getString(MyConstants.SP_KEY_SETTING_MONITOR_BG, DEFAULT_LOCK_MONITOR_BG)
        val bg = iv_monitor_background
        Glide.with(this).load(currentUrl)
            //.transition(DrawableTransitionOptions.withCrossFade())
            .diskCacheStrategy(DiskCacheStrategy.AUTOMATIC)
            .into(bg)
        btn_monitor_background.setOnClickListener {
            BgBottomSheetDialogFragment.newInstance(currentUrl, "监督锁机背景图片").apply {

                setOnBgSelectListener(object : BgBottomSheetDialogFragment.OnBgSelectListener {
                    override fun onSelect(imgUrl: String) {
                        currentUrl = imgUrl
                        MMKVUtils.put(MyConstants.SP_KEY_SETTING_MONITOR_BG, currentUrl)
                        Glide.with(this@AppLimitSettingActivity).load(currentUrl)
                            //.transition(DrawableTransitionOptions.withCrossFade())
                            .diskCacheStrategy(DiskCacheStrategy.AUTOMATIC)
                            .into(bg)
                    }
                })
                show(supportFragmentManager, "BgBottomSheetDialogFragment")
            }
        }


        refreshState()
        switch_app_left.setOnCheckedChangeListener { buttonView, isChecked ->
            MMKVUtils.put(MyConstants.SP_KEY_LIMIT_SETTING_APP_LEFT, isChecked)
            refreshState()
        }

        //白名单限时到达前提醒
        btn_app_limit_notify.setOnClickListener {
            if (MyUtil.isVIP()) {

                val myItems = listOf(
                    MyUtil.getNotifyLengthString(0),
                    MyUtil.getNotifyLengthString(1),
                    MyUtil.getNotifyLengthString(2),
                    MyUtil.getNotifyLengthString(3),
                    MyUtil.getNotifyLengthString(4),
                    MyUtil.getNotifyLengthString(5),
                    MyUtil.getNotifyLengthString(6),
                )
                MaterialDialog(this)
                    .cornerRadius(8.0f).show {
                        listItems(items = myItems) { dialog, index, text ->
                            MMKVUtils.put(MyConstants.SP_KEY_APP_LIMIT_NOTIFY, index)
                            refreshState()
                        }
                    }
            } else {
                DialogUtil.showVIPDialog(this, null, content = "自定义提前提醒时间为VIP专享功能，开通后，即可根据自身习惯更改提前提醒时间。", "时长耗尽前提醒")
            }
        }

        switch_tired_notify.setOnCheckedChangeListener { buttonView, isChecked ->
            MMKVUtils.put(MyConstants.SP_KEY_LIMIT_SETTING_TIRED_NOTIFY, isChecked)
            refreshState()
            if (isChecked) {
                MyToastUtil.showInfo("非锁机状态下才会提醒")
            }
        }

        switch_lock_after_tired.setOnCheckedChangeListener { buttonView, isChecked ->
            MMKVUtils.put(MyConstants.SP_KEY_LIMIT_SETTING_TIRED_AUTO_LOCK, isChecked)
            refreshState()
        }

        btn_tired_notify_length.setOnClickListener {
            if (MyUtil.isVIP()) {
                val dialog = WhiteAppMonitorSelectTimeDialog(this)
                dialog.setOnOKClickListener(object : WhiteAppMonitorSelectTimeDialog.OnTimeLimitListener {
                    override fun onclick(minute: Int) {
                        if (minute < 1) {
                            MyToastUtil.showError("禁止设置为1分钟以内")
                        } else {
                            MMKVUtils.put(MyConstants.SP_KEY_LIMIT_SETTING_TIRED_NOTIFY_LENGTH, minute)
                            refreshState()
                        }
                    }
                })
                dialog.setTitle("连续使用时长")
                dialog.isCancelable = false
                dialog.setContent("设置连续使用多久后提醒")
                dialog.setCancelText("取消")
                dialog.setTime(MMKVUtils.getInt(MyConstants.SP_KEY_LIMIT_SETTING_TIRED_NOTIFY_LENGTH, 30))
                dialog.show()
            } else {
                DialogUtil.showVIPDialog(
                    activity = this,
                    fragment = null,
                    content = "自定义连续使用时长为VIP专享功能，开通后，即可根据自身需求个性化定制，更科学的管理手机使用时间。",
                    fromWhere = "设置连续使用时长"
                )
            }
        }



        btn_auto_lock.setOnClickListener {
            if (MyUtil.isVIP()) {
                val dialog = WhiteAppMonitorSelectTimeDialog(this)
                dialog.setOnOKClickListener(object : WhiteAppMonitorSelectTimeDialog.OnTimeLimitListener {
                    override fun onclick(minute: Int) {
                        if (minute < 1) {
                            MyToastUtil.showError("禁止设置为1分钟以内")
                        } else {
                            MMKVUtils.put(MyConstants.SP_KEY_LIMIT_SETTING_TIRED_AUTO_LOCK_LENGTH, minute)
                            refreshState()
                        }
                    }
                })
                dialog.setTitle("自动锁机时长")
                dialog.setContent("设置超过疲劳时长后锁机多久")
                dialog.setCancelText("取消")
                dialog.setTime(MMKVUtils.getInt(MyConstants.SP_KEY_LIMIT_SETTING_TIRED_AUTO_LOCK_LENGTH, 1))
                dialog.show()
            } else {
                DialogUtil.showVIPDialog(
                    activity = this,
                    fragment = null,
                    content = "自定义疲劳锁机时长为VIP专享功能，开通后，即可根据自身需求个性化定制，更科学的管理手机使用时间。",
                    fromWhere = "设置疲劳锁机时长"
                )
            }
        }

//        tv_vip_flag_tired_length.setOnClickListener {
//            startActivity(Intent(this, VIP2Activity::class.java))
//        }
//        tv_vip_flag_auto_lock_length.setOnClickListener {
//            startActivity(Intent(this, VIP2Activity::class.java))
//        }
//        tv_vip_flag_app_limit_notify.setOnClickListener {
//            startActivity(Intent(this, VIP2Activity::class.java))
//        }

    }

    fun refreshState() {
        val limitSetting = MMKVUtils.getBoolean(MyConstants.SP_KEY_LIMIT_SETTING_APP_LEFT, true)
        switch_app_left.isChecked = limitSetting
        if (limitSetting) {
            btn_app_limit_notify.visibility = View.GONE
        } else {
            btn_app_limit_notify.visibility = View.VISIBLE
            tv_app_limit_notify_length.text = MyUtil.getNotifyLengthString(MMKVUtils.getInt(MyConstants.SP_KEY_APP_LIMIT_NOTIFY, 5))
        }

        if (MMKVUtils.getBoolean(MyConstants.SP_KEY_LIMIT_SETTING_TIRED_NOTIFY, false)) {
            switch_tired_notify.isChecked = true
            ll_app_left_son.visibility = View.VISIBLE
            tv_tired_notify_length.text = "${TimeUtil.formatHHMMSimple(MMKVUtils.getInt(MyConstants.SP_KEY_LIMIT_SETTING_TIRED_NOTIFY_LENGTH, 30))}"

            if (MMKVUtils.getBoolean(MyConstants.SP_KEY_LIMIT_SETTING_TIRED_AUTO_LOCK, false)) {
                switch_lock_after_tired.isChecked = true
                btn_auto_lock.visibility = View.VISIBLE
                tv_auto_lock_length.text = "${TimeUtil.formatHHMMSimple(MMKVUtils.getInt(MyConstants.SP_KEY_LIMIT_SETTING_TIRED_AUTO_LOCK_LENGTH, 1))}"

                btn_notice_background.visibility = View.VISIBLE

                //背景图片
                var currentUrl = MMKVUtils.getString(MyConstants.SP_KEY_SETTING_NOTICE_BG, DEFAULT_LOCK_NOTICE_BG)
                val bg = iv_notice_background
                Glide.with(this).load(currentUrl)
                    //.transition(DrawableTransitionOptions.withCrossFade())
                    .diskCacheStrategy(DiskCacheStrategy.AUTOMATIC)
                    .into(bg)
                btn_notice_background.setOnClickListener {
                    BgBottomSheetDialogFragment.newInstance(currentUrl, "疲劳锁机背景图片").apply {
                        setOnBgSelectListener(object : BgBottomSheetDialogFragment.OnBgSelectListener {
                            override fun onSelect(imgUrl: String) {
                                currentUrl = imgUrl
                                MMKVUtils.put(MyConstants.SP_KEY_SETTING_NOTICE_BG, currentUrl)
                                Glide.with(this@AppLimitSettingActivity).load(currentUrl)
                                    //.transition(DrawableTransitionOptions.withCrossFade())
                                    .diskCacheStrategy(DiskCacheStrategy.AUTOMATIC)
                                    .into(bg)
                            }
                        })
                        show(supportFragmentManager, "BgBottomSheetDialogFragment")
                    }
                }

            } else {
                switch_lock_after_tired.isChecked = false
                btn_auto_lock.visibility = View.GONE
                btn_notice_background.visibility = View.GONE
            }
        } else {
            switch_tired_notify.isChecked = false
            ll_app_left_son.visibility = View.GONE
        }

        tv_who_first.text = when (MMKVUtils.getString(MyConstants.SP_KEY_LIMIT_SETTING_WHO_FIRST, MyConstants.SP_KEY_LIMIT_SETTING_WHITE_FIRST)) {
            MyConstants.SP_KEY_LIMIT_SETTING_WHITE_FIRST -> "白名单内限时优先"
            MyConstants.SP_KEY_LIMIT_SETTING_LIMIT_FIRST -> "APP监督限时优先"
            MyConstants.SP_KEY_LIMIT_SETTING_LESS_FIRST -> "剩余时间少的优先"
            else -> ""
        }
        btn_who_first.setOnClickListener {
            val bottomDialog = BottomSingleSelectDialogFragment.newInstance()
            bottomDialog.setShowList(arrayListOf("白名单内限时优先", "APP监督限时优先", "剩余时间少的优先"))
            bottomDialog.setValueList(arrayListOf(0, 1, 2))
            bottomDialog.setOnValueSelectListener(object : BottomSingleSelectDialogFragment.OnValueSelectListener {
                override fun onSelect(value: Long, show: String) {
                    when (value) {
                        0L -> {
                            MMKVUtils.put(MyConstants.SP_KEY_LIMIT_SETTING_WHO_FIRST, MyConstants.SP_KEY_LIMIT_SETTING_WHITE_FIRST)
                        }
                        1L -> {
                            MMKVUtils.put(MyConstants.SP_KEY_LIMIT_SETTING_WHO_FIRST, MyConstants.SP_KEY_LIMIT_SETTING_LIMIT_FIRST)
                        }
                        2L -> {
                            MMKVUtils.put(MyConstants.SP_KEY_LIMIT_SETTING_WHO_FIRST, MyConstants.SP_KEY_LIMIT_SETTING_LESS_FIRST)
                        }
                    }
                    tv_who_first.text = when (MMKVUtils.getString(MyConstants.SP_KEY_LIMIT_SETTING_WHO_FIRST, MyConstants.SP_KEY_LIMIT_SETTING_WHITE_FIRST)) {
                        MyConstants.SP_KEY_LIMIT_SETTING_WHITE_FIRST -> "白名单内限时优先"
                        MyConstants.SP_KEY_LIMIT_SETTING_LIMIT_FIRST -> "APP监督限时优先"
                        MyConstants.SP_KEY_LIMIT_SETTING_LESS_FIRST -> "剩余时间少的优先"
                        else -> ""
                    }
                }
            })
            bottomDialog.show(supportFragmentManager, "")
        }
    }

    override fun onResume() {
        super.onResume()
        if (MyUtil.isVIP()) {
            tv_vip_flag_tired_length.visibility = View.GONE
            tv_vip_flag_auto_lock_length.visibility = View.GONE
            tv_vip_flag_app_limit_notify.visibility = View.GONE
        } else {
            tv_vip_flag_tired_length.visibility = View.VISIBLE
            tv_vip_flag_auto_lock_length.visibility = View.VISIBLE
            tv_vip_flag_app_limit_notify.visibility = View.VISIBLE
        }
    }
}