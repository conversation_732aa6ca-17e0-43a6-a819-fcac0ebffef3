package com.lijianqiang12.silent.component.activity.room.allroom

import android.content.Intent
import android.os.Bundle
import androidx.lifecycle.lifecycleScope
import com.google.android.material.tabs.TabLayoutMediator
import com.lijianqiang12.silent.R
import com.lijianqiang12.silent.data.model.net.MyRetrofitClient
import com.lijianqiang12.silent.component.activity.base.BaseActivity
import com.lijianqiang12.silent.component.activity.custom.dialog.EditTextDialog
import com.lijianqiang12.silent.component.activity.room.createroom.CreateRoomActivity
import com.lijianqiang12.silent.component.activity.room.roomdetail.DetailRoomActivity
import com.lijianqiang12.silent.utils.MyToastUtil
import kotlinx.android.synthetic.main.activity_room_all.*
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.launch
import kotlinx.coroutines.withContext

class RoomAllActivity : BaseActivity() {
    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        setContentView(R.layout.activity_room_all)
        viewpager_rooms.adapter = RoomAllViewPagerAdapter(this)
        TabLayoutMediator(tabLayout_rooms, viewpager_rooms) { tab, position ->
            tab.text = when (position) {
                0 -> "最热"
                1 -> "最新"
                2 -> "考研"
                3 -> "高考"
                4 -> "中考"
                5 -> "英语"
                6 -> "早睡"
                7 -> "情侣"
                8 -> "班级"
                9 -> "未分类"
                else -> "Error"
            }

        }.attach()

        btn_room_create.setOnClickListener {
//            if (MyUtil.isVIP(this.applicationContext)) {
            startActivity(Intent(this, CreateRoomActivity::class.java))
//            } else {
//                MyToastUtil.showInfo(this, "VIP用户可创建房间，请先升级VIP")
//                startActivity(Intent(this, VIP2Activity::class.java))
//            }
        }

        iv_back.setOnClickListener {
            finish()
        }

        fab_find_room.setOnClickListener {
            EditTextDialog(this).run {
                isCancelable = false
                setTitle("请输入房间暗号")
                setOnOKListener(object : EditTextDialog.OnOKListener {
                    override fun onclick(text: String) {
                        if (text.isNotEmpty()) {
                            <EMAIL>(Dispatchers.IO) {
                                try {
                                    val result = MyRetrofitClient.service.getRoomFromCode(text)
                                    withContext(Dispatchers.Main) {
                                        if (result.code == 200) {


                                            result.data?.let {
                                                if (it.hasPwd) {
                                                    EditTextDialog(this@RoomAllActivity).run {
                                                        isCancelable = false
                                                        setTitle("该房间需要密码")
                                                        setOnOKListener(object : EditTextDialog.OnOKListener {
                                                            override fun onclick(text: String) {
                                                                if (text.isNotEmpty()) {
                                                                    <EMAIL>(Dispatchers.IO) {
                                                                        try {
                                                                            val result2 = MyRetrofitClient.service.verifyRoomPwd(it.roomId.toLong(), text)
                                                                            withContext(Dispatchers.Main) {
                                                                                if (result2.code == 200) {

                                                                                    val intent = Intent(this@RoomAllActivity, DetailRoomActivity::class.java)
                                                                                    intent.putExtra("roomId", it.roomId)
                                                                                    intent.putExtra("pwd", text)
                                                                                    <EMAIL>(intent)


                                                                                } else {
                                                                                    MyToastUtil.showInfo(result2.msg)
                                                                                }

                                                                            }
                                                                        } catch (e: Exception) {
                                                                            withContext(Dispatchers.Main) {
                                                                                MyToastUtil.showInfo(e.message)
                                                                            }
                                                                        }
                                                                    }
                                                                } else {
                                                                    MyToastUtil.showInfo("密码不能为空")
                                                                }

                                                            }
                                                        })

                                                        show()
                                                    }
                                                } else {
                                                    val intent = Intent(this@RoomAllActivity, DetailRoomActivity::class.java)
                                                    intent.putExtra("roomId", it.roomId)
                                                    <EMAIL>(intent)

                                                }
                                            }


                                        } else {
                                            MyToastUtil.showInfo(result.msg)
                                        }

                                    }
                                } catch (e: Exception) {
                                    withContext(Dispatchers.Main) {
                                        MyToastUtil.showInfo(e.message)
                                    }
                                } finally {
                                }
                            }
                        } else {
                            MyToastUtil.showInfo("暗号不能为空")
                        }

                    }
                })

                show()
            }
        }
    }


}