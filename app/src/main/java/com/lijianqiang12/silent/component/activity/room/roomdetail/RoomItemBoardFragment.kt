package com.lijianqiang12.silent.component.activity.room.roomdetail

import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.fragment.app.Fragment
import androidx.lifecycle.lifecycleScope
import androidx.recyclerview.widget.LinearLayoutManager
import androidx.recyclerview.widget.RecyclerView
import com.blankj.utilcode.util.LogUtils
import com.lijianqiang12.silent.utils.MMKVUtils
import com.lijianqiang12.silent.MAX_ID
import com.lijianqiang12.silent.MyConstants
import com.lijianqiang12.silent.R
import com.lijianqiang12.silent.data.model.net.MyRetrofitClient
import com.lijianqiang12.silent.data.model.net.pojos.RoomDetailBoard
import com.lijianqiang12.silent.component.activity.custom.dialog.NormalDialog
import com.lijianqiang12.silent.component.activity.custom.dialog.OnCancelClickListener
import com.lijianqiang12.silent.component.activity.custom.dialog.OnOKClickListener
import com.lijianqiang12.silent.utils.MyToastUtil
import kotlinx.android.synthetic.main.fragment_room_item_board.*
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.launch
import kotlinx.coroutines.withContext

private const val ARG_PARAM1 = "param1"
private const val ARG_PARAM2 = "param2"

private lateinit var recyclerview: RecyclerView
private lateinit var adapter: RoomBoardAdapter

class RoomItemBoardFragment : Fragment() {
    private var param1: String? = null
    private var param2: String? = null

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        arguments?.let {
            param1 = it.getString(ARG_PARAM1)
            param2 = it.getString(ARG_PARAM2)
        }
    }

    override fun onCreateView(inflater: LayoutInflater, container: ViewGroup?,
                              savedInstanceState: Bundle?): View? {
        return inflater.inflate(R.layout.fragment_room_item_board, container, false)
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        recyclerview = rv_room_board
        recyclerview.layoutManager = LinearLayoutManager(requireContext())
        adapter = RoomBoardAdapter(R.layout.item_room_board, mutableListOf())
        adapter.animationEnable = true
        adapter.loadMoreModule.setOnLoadMoreListener {
            if (adapter.data.size == 0) {
                getNewData(MAX_ID)
                LogUtils.d("!!!!!!!!!!a1")
            } else {
                getNewData(adapter.data[adapter.data.size - 1].boardId.toLong())
                LogUtils.d("!!!!!!!!!!a2")
            }
        }

        recyclerview.adapter = adapter

//        recyclerview.addOnScrollListener(object : RecyclerView.OnScrollListener() {
//            override fun onScrollStateChanged(recyclerView: RecyclerView, newState: Int) {
//                super.onScrollStateChanged(recyclerView, newState)
//            }
//
//            override fun onScrolled(recyclerView: RecyclerView, dx: Int, dy: Int) {
//                super.onScrolled(recyclerView, dx, dy)
//LogUtils.d("zzzzzzzzzzzzz")
//            }
//        })

        adapter.setOnItemClickListener { adapter, view, position ->

        }


        adapter.setOnItemLongClickListener { adapter, view, position ->
            if (ifMine || (adapter.data[position] as RoomDetailBoard).userId == MMKVUtils.getInt(MyConstants.SP_KEY_USER_ID, -1)) {
                NormalDialog(this).apply {
                    setTitle("删除该留言")
                    setContent("您是否要将此留言从房间中删除？")
                    setOnNormalOKClickListener("删除该留言", object : OnOKClickListener {
                        override fun onclick() {
                            <EMAIL>(Dispatchers.IO) {
                                try {
                                    val result = MyRetrofitClient.service.removeBoard(currentRoomId.toInt(),
                                            (adapter.data[position] as RoomDetailBoard).boardId)
                                    withContext(Dispatchers.Main) {
                                        if (result.code == 200) {
                                            MyToastUtil.showInfo(result.msg)
                                            getNewData(MAX_ID)
                                        } else {
                                            MyToastUtil.showInfo(result.msg)
                                        }
                                    }
                                } catch (e: Exception) {
                                    withContext(Dispatchers.Main) {
                                        MyToastUtil.showInfo(e.message)
                                    }
                                }
                            }
                        }
                    })
                    setOnNormalCancelClickListener(object : OnCancelClickListener {
                        override fun onclick() {

                        }
                    })
                    showDialog()
                }
            }

            true
        }


//        srl_punch_card.setOnRefreshListener {
//            getNewData(MAX_ID)
//        }
//
//        srl_punch_card.isRefreshing = true

        getNewData(MAX_ID)
        LogUtils.d("!!!!!!!!!!a")
    }

    private fun getNewData(lastId: Long) {
        lifecycleScope.launch(Dispatchers.IO) {
            try {
                val result = MyRetrofitClient.service.getRoomBoard(lastId, currentRoomId)
                withContext(Dispatchers.Main) {
                    if (result.code == 200) {
                        result.data?.let {
                            if (lastId == MAX_ID) {
                                adapter.setNewInstance(it)
                                adapter.loadMoreModule.loadMoreComplete()
                            } else {
                                if (it.isEmpty()) {
                                    adapter.loadMoreModule.loadMoreEnd()
                                } else {
                                    adapter.addData(it)
                                    adapter.loadMoreModule.loadMoreComplete()
                                }
                            }
                            adapter.notifyDataSetChanged()
                        }
                    } else {
                        MyToastUtil.showInfo(result.msg)
                    }
                }
            } catch (e: Exception) {
                withContext(Dispatchers.Main) {
                    MyToastUtil.showInfo(e.message)
                }
            } finally {
//                withContext(Dispatchers.Main) {
//                    srl_punch_card.isRefreshing = false
//                }
            }
        }
    }

    override fun onResume() {
        super.onResume()
        if (MMKVUtils.getBoolean(MyConstants.SP_AGREE_BOARD, true)) {
            NormalDialog(this).apply {
                setTitle("协议")
                isCancelable = false
                setContent("请勿在留言板中发布色情低俗、暴力血腥、政治谣言等各类违反法律法规及相关政策规定的信息，一旦发现，我们将严厉打击和处理，严重者将进行封号处理。净化网络环境，从你我做起。")
                setOnNormalOKClickListener("同意", object : OnOKClickListener {
                    override fun onclick() {
                        MMKVUtils.put(MyConstants.SP_AGREE_BOARD, false)
                    }
                })
                setOnNormalCancelClickListener("不同意", object : OnCancelClickListener {
                    override fun onclick() {
                        requireActivity().finish()
                    }
                })
                showDialog()
            }
        }
    }

    companion object {
        /**
         * Use this factory method to create a new instance of
         * this fragment using the provided parameters.
         *
         * @param param1 Parameter 1.
         * @param param2 Parameter 2.
         * @return A new instance of fragment RoomItemBoardFragment.
         */
        // TODO: Rename and change types and number of parameters
        @JvmStatic
        fun newInstance(param1: String, param2: String) =
                RoomItemBoardFragment().apply {
                    arguments = Bundle().apply {
                        putString(ARG_PARAM1, param1)
                        putString(ARG_PARAM2, param2)
                    }
                }
    }
}