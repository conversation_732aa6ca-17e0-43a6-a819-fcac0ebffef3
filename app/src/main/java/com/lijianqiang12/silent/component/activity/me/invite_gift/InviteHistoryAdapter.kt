package com.lijianqiang12.silent.component.activity.me.invite_gift

import android.widget.TextView
import com.chad.library.adapter.base.BaseQuickAdapter
import com.chad.library.adapter.base.module.LoadMoreModule
import com.chad.library.adapter.base.viewholder.BaseViewHolder
import com.lijianqiang12.silent.R
import com.lijianqiang12.silent.data.model.net.pojos.ResultMyInviteHistory


class InviteHistoryAdapter(layoutRes: Int, list: MutableList<ResultMyInviteHistory>) : BaseQuickAdapter<ResultMyInviteHistory, BaseViewHolder>(layoutRes, list),
    LoadMoreModule {

    override fun convert(holder: BaseViewHolder, item: ResultMyInviteHistory) {
        holder.getView<TextView>(R.id.tv_content).text = item.username
        holder.getView<TextView>(R.id.tv_time).text = item.time


    }
}