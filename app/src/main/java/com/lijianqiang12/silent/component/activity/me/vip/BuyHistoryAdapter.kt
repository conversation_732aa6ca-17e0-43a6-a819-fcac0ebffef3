package com.lijianqiang12.silent.component.activity.me.vip

import android.widget.TextView
import com.chad.library.adapter.base.BaseQuickAdapter
import com.chad.library.adapter.base.module.LoadMoreModule
import com.chad.library.adapter.base.viewholder.BaseViewHolder
import com.lijianqiang12.silent.R
import com.lijianqiang12.silent.data.model.net.pojos.BuyHistory

class BuyHistoryAdapter(layoutRes: Int, list: MutableList<BuyHistory>)
    : BaseQuickAdapter<BuyHistory, BaseViewHolder>(layoutRes, list), LoadMoreModule {

    override fun convert(viewHolder: BaseViewHolder, item: BuyHistory) {
        var str = ""

        str += "订单号：" + item.orderId
        str += "\n金额：¥" + item.amount.toFloat() / 100
//        str += "\n支付状态：" + when (item.state) {
//            1 -> "已支付"
//            else -> "未支付"
//        }
        str += "\n支付方式：" + when (item.payType) {
            1 -> "支付宝"
            2 -> "微信"
            else -> "其它"
        }
        str += "\nVIP时长：" + when (item.length) {
            -100 -> "永久"
            else -> "" + item.length + "天"
        }
        str += "\n购买时间：" + item.time

        if (item.name.isNotEmpty()) {
            str += "\n赠品收货人姓名：" + item.name
            str += "\n赠品收货人电话：" + item.phone
            str += "\n赠品收货人地址：" + item.address
            str += "\n赠品快递名称：" + item.deliveryName
            str += "\n赠品快递单号：" + item.deliveryId
        }

        viewHolder.getView<TextView>(R.id.tv_buy_history).text = str

//        if (item.canApply) {
//            viewHolder.getView<TextView>(R.id.btn_apply_delivery).visibility = View.VISIBLE
//        } else {
//            viewHolder.getView<TextView>(R.id.btn_apply_delivery).visibility = View.GONE
//        }
    }
}