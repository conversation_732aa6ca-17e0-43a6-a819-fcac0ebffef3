package com.lijianqiang12.silent.component.service.background_service

import android.app.*
import android.appwidget.AppWidgetManager
import android.content.BroadcastReceiver
import android.content.Context
import android.content.Intent
import android.content.IntentFilter
import android.content.res.Configuration
import android.graphics.Color
import android.graphics.drawable.Drawable
import android.media.AudioAttributes
import android.media.AudioFocusRequest
import android.media.AudioManager
import android.media.MediaPlayer
import android.os.*
import android.util.Log
import android.view.View
import androidx.lifecycle.Observer
import com.blankj.utilcode.util.AppUtils
import com.blankj.utilcode.util.LogUtils
import com.blankj.utilcode.util.RomUtils
import com.jeremyliao.liveeventbus.LiveEventBus
import com.lijianqiang12.silent.LiveBus
import com.lijianqiang12.silent.MyConstants
import com.lijianqiang12.silent.R
import com.lijianqiang12.silent.TheApplication
import com.lijianqiang12.silent.component.activity.TheMainActivity
import com.lijianqiang12.silent.component.activity.widget.DenyUninstallAppWidget3
import com.lijianqiang12.silent.component.service.windows.*
import com.lijianqiang12.silent.data.model.db.*
import com.lijianqiang12.silent.data.model.net.MyRetrofitClient
import com.lijianqiang12.silent.data.viewmodel.LockViewModel
import com.lijianqiang12.silent.data.viewmodel.MonitorViewModel
import com.lijianqiang12.silent.initInitial
import com.lijianqiang12.silent.sync.SyncHelper
import com.lijianqiang12.silent.utils.*
import dagger.hilt.android.AndroidEntryPoint
import kotlinx.android.synthetic.main.layout_lock_window_land.view.*
import kotlinx.android.synthetic.main.layout_lock_window_sub_apps.view.*
import kotlinx.coroutines.*
import java.util.*
import kotlin.math.abs
import javax.inject.Inject


//当前运行状态
enum class RunState {
    UNKNOWN, LOCKING, CHECK_SCHEDULE
}

//当前工作状态，用于番茄钟
enum class WorkState {
    UNKNOWN, WORKING, RESTING, ENDING
}

//当前页面类型
enum class CurrentPageType {
    UNKNOWN_APP, USER_WHITE_APP, USER_BLACK_PAGE, SYSTEM_WHITE_PAGE, SYSTEM_BLACK_PAGE, SYSTEM_BLACK_IGNORE_WHITE_PAGE
}

class MyOnAudioFocusChangeListener : AudioManager.OnAudioFocusChangeListener {
    override fun onAudioFocusChange(focusChange: Int) {
        LogUtils.d("focusChange = $focusChange")
    }
}

@AndroidEntryPoint
class BackgroundService : Service() {

    /*floatWindow*/
    private lateinit var lockFloatWindow: FloatWindowOfLock
    private lateinit var noticeFloatWindow: FloatWindowOfNotice

    //    private lateinit var noticeFloatTranslateWindow: FloatTranslateWindowOfNotice
    private lateinit var restFloatWindow: FloatWindowOfRest
    private val monitorFloatWindow = mutableListOf<FloatWindowOfAppMonitor>()
    private lateinit var monitorNotifyFloatWindow: FloatWindowOfMonitorNotify
    private lateinit var tiredNotifyWindow: NotifyWindowOfTired
    private lateinit var scheduleNotifyWindow: NotifyWindowOfSchedule
    private lateinit var dayLimitNotifyWindow: NotifyWindowOfDayLimit
    private lateinit var denyDropFloatWindow: FloatWindowOfDenyDrop
    private lateinit var chooseDenyPageFloatWindow: FloatWindowOfChooseDenyPage
    private lateinit var denyPageNoticeFloatWindow: FloatWindowOfDenyPageNotice

    private val handler = Handler(Looper.getMainLooper())
    private val audioFocusChangeListener = MyOnAudioFocusChangeListener()

    companion object {
        @Volatile
        var runningWhiteAppList = mutableListOf<WhiteApp>()
    }

    //获取音频焦点，以便在开始锁机时停止其他后台音频播放。
    private fun getAudioFocus(focus: Boolean): Boolean {
        val ifStopMusic = MMKVUtils.getBoolean(MyConstants.SP_KEY_STOP_MUSIC_SETTINGS, true)
        if (!ifStopMusic) {
            return focus
        }

        val mAudioManager = this.applicationContext.getSystemService(AUDIO_SERVICE) as AudioManager
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
            val focusRequest = AudioFocusRequest.Builder(AudioManager.AUDIOFOCUS_GAIN).run {
                setAudioAttributes(AudioAttributes.Builder().run {
                    setUsage(AudioAttributes.USAGE_GAME)
                    setContentType(AudioAttributes.CONTENT_TYPE_MUSIC)
                    build()
                })
//                setAcceptsDelayedFocusGain(true)
                setOnAudioFocusChangeListener(audioFocusChangeListener, handler)
                build()
            }

            val result = if (focus) {
                mAudioManager.requestAudioFocus(focusRequest)
            } else {
                mAudioManager.abandonAudioFocusRequest(focusRequest)
            }
            return result == AudioManager.AUDIOFOCUS_REQUEST_GRANTED
        } else {
            val result = if (focus) {
                mAudioManager.requestAudioFocus(
                    audioFocusChangeListener, AudioManager.STREAM_MUSIC, AudioManager.AUDIOFOCUS_GAIN
                )
            } else {
                mAudioManager.abandonAudioFocus(audioFocusChangeListener)
            }
            return result == AudioManager.AUDIOFOCUS_REQUEST_GRANTED
        }
    }


    /*flags*/
    @Volatile
    private var startedOver = false//是否已经启动完成

//    @Volatile
//    private var ifShouldShowDenyWindow = false//是否应该显示禁止下拉菜单的透明横条

    @Volatile
    private var isSilent = false//锁机期间是否静音

    @Volatile
    private var lastDay = 0//上次记录的day，用于刷新锁机页面的倒数日

    //    private var ifStarted = false
//    @Volatile
//    private var isScreenOn = true
//    private var isWhiteApp = false

    @Volatile
    private var currentPageType = CurrentPageType.UNKNOWN_APP

    @Volatile
    private var hasOpenMinute = -1 //已经连续使用的分钟数，用于疲劳提醒

    //剩余时长提醒，上次显示的时间
    @Volatile
    private var lastLeftMinute = -1L //上次提醒监督锁机的分钟数，以防止连续提醒


    @Volatile
    private var timeLen = 0 //需要锁的时长，单位分钟

    @Volatile
    private var leftTime = 0L //剩余时长，单位ms


    /*资源数据列表*/
    private var schedulesWithSub = mutableListOf<ScheduleWithSub>()
    private var lockHistory: LockHistory? = null
    private val notifySource = mutableListOf<Int>()
    private val notifyPlayerList = mutableListOf<MediaPlayer>()

    /*监听*/
    private val scheduleWithSubObserver = ScheduleWithSubObserver()
    private val lockHistoryObserver = LockHistoryObserver()

    //    private val globalWhiteAppsObserver = GlobalWhiteAppsObserver()
//    private val liveBusObserver = LiveBusObserver()
    private lateinit var innerReceiver: InnerReceiver

    @Inject
    lateinit var lockViewModel: LockViewModel

    @Inject
    lateinit var monitorViewModel: MonitorViewModel

    /*当前对象*/
//    private var dayLimit: DayLimit? = DayLimit()
    private var appLimits = mutableListOf<AppLimit>()
    private var runningLockHistory: LockHistory = LockHistory()
    private lateinit var runningTomatoWithSub: TomatoWithSub
    private lateinit var runningScheduleWithSub: ScheduleWithSub
//    private var runningWhiteAppList = mutableListOf<WhiteApp>()

    @Volatile
    private var runningSimpleLength = 0
    private var runningLockConfig: LockConfig = LockConfig()

    private var globalWhiteAppList = mutableListOf<WhiteApp>()
    private lateinit var globalLockConfig: LockConfig
    private lateinit var currentCalendar: Calendar

    @Inject
    lateinit var syncHelper: SyncHelper

    private var currentDenyAppInfo: WhitePkgInfo? = null

    @Volatile
    private var syncOnlineIndex = 0

    @Volatile
    private var syncOnlineMax = 60

    @Volatile
    private var second60Index = 0

    @Volatile
    private var launcherPkg = ""


    @Volatile
    private var whileOnline = true

    @Volatile
    private var whileChooseDenyPage = true

    @Volatile
    private var whileMonitor = true

    @Volatile
    private var whileLockState = true

    @Volatile
    private var whileSchedule = true

    fun isScreenOn(): Boolean {
        val mKeyguardManager: KeyguardManager = getSystemService(KEYGUARD_SERVICE) as KeyguardManager
        val isKeyguardLocked: Boolean = mKeyguardManager.isKeyguardLocked
        val pm: PowerManager = getSystemService(POWER_SERVICE) as PowerManager
        return pm.isInteractive && !isKeyguardLocked
    }

    private var lastAppLeftTime = 0L
    private var lastSecondFlag = -1

    //显示白名单app限时
    private suspend fun showWhiteAppLimitNotice(whiteLeftTime: Long, whiteApp: WhiteApp, icon: Drawable, currentApp: WhitePkgInfo, index: Int) {
        for (i in monitorFloatWindow.size until index + 1) {
            monitorFloatWindow.add(FloatWindowOfAppMonitor(this@BackgroundService, i))
        }
//        if (monitorFloatWindow.size < index + 1) {
//            monitorFloatWindow.add(FloatWindowOfAppMonitor(this@BackgroundService, index))
//        }

        //分身应用因为系统不会计时，因此需要在这里计时
        if (currentApp.useAccessibility && lastAppLeftTime == whiteLeftTime) {
            val currentSecondFlag = Calendar.getInstance().get(Calendar.SECOND)
            if (currentSecondFlag != lastSecondFlag) {
                lastSecondFlag = currentSecondFlag
                monitorViewModel.insertAppUsage(AppUsage(appPkg = currentApp.pkgName, timestamp = System.currentTimeMillis()))
                lastAppLeftTime = whiteLeftTime - 1
            }
        } else {
            lastAppLeftTime = whiteLeftTime
        }
        //-------------------
        if (whiteLeftTime > 0) {
            if (MMKVUtils.getBoolean(MyConstants.SP_KEY_LIMIT_SETTING_APP_LEFT, true)) {
                monitorFloatWindow[index].showWindow("剩余 ${formatHHMMSS(whiteLeftTime)}", icon)
            } else {
                val appLimitNotify = MMKVUtils.getInt(MyConstants.SP_KEY_APP_LIMIT_NOTIFY, 5)

                if (appLimitNotify > 0) {
                    if (abs(whiteLeftTime - MyUtil.getNotifyLength(appLimitNotify).toLong()) < 3) {
                        monitorNotifyFloatWindow.showAndHide("剩余 ${formatHHMMSS(whiteLeftTime)}", icon)
                    }
                }
            }
        } else {

            if (MMKVUtils.getBoolean(MyConstants.SP_KEY_LIMIT_SETTING_APP_LEFT, true)) {
                monitorFloatWindow[index].hideWindow()
            }

            //防止支付页面因app超时被锁住
            if (!isWhiteLimitIgnorePage(currentApp)) {
                noticeFloatWindow.showDenyWindow("『${getAppName(whiteApp.pkg, whiteApp.mainActivity)}』已超过您在白名单中设置的限制时长", 3000) {
                    //倒计时结束后再弹出，否则锁机时锁机页面也会弹出来
//                    performHome(this@BackgroundService, "1")
                }
            }

        }
    }

    //显示app限时
    private suspend fun showAppLimitNotice(appLeftTime: Long, appLimit: AppLimit, icon: Drawable, currentApp: WhitePkgInfo, index: Int) {

        for (i in monitorFloatWindow.size until index + 1) {
            monitorFloatWindow.add(FloatWindowOfAppMonitor(this@BackgroundService, i))
        }
//        if (monitorFloatWindow.size < index + 1) {
//            monitorFloatWindow.add(FloatWindowOfAppMonitor(this@BackgroundService, index))
//        }

        //分身应用因为系统不会计时，因此需要在这里计时
        if (currentApp.useAccessibility && lastAppLeftTime == appLeftTime) {
            //小窗状态，将已使用时长写入数据库
            val currentSecondFlag = Calendar.getInstance().get(Calendar.SECOND)
            if (currentSecondFlag != lastSecondFlag) {
                lastSecondFlag = currentSecondFlag
                monitorViewModel.insertAppUsage(AppUsage(appPkg = currentApp.pkgName, timestamp = System.currentTimeMillis()))
                lastAppLeftTime = appLeftTime - 1
            }
        } else {
            lastAppLeftTime = appLeftTime
        }
        //-------------------
        if (appLeftTime > 0) {
            if (MMKVUtils.getBoolean(MyConstants.SP_KEY_LIMIT_SETTING_APP_LEFT, true)) {
                monitorFloatWindow[index].showWindow("『${MyUtil.getAppLimitTitle(appLimit)}』${formatHHMMSS(appLeftTime)}", icon)
            } else {
                val appLimitNotify = MMKVUtils.getInt(MyConstants.SP_KEY_APP_LIMIT_NOTIFY, 5)
                if (appLimitNotify > 0) {
                    if (abs(appLeftTime - MyUtil.getNotifyLength(appLimitNotify).toLong()) < 3) {
                        monitorNotifyFloatWindow.showAndHide("『${MyUtil.getAppLimitTitle(appLimit)}』剩余 ${formatHHMMSS(appLeftTime)}", icon)
                    }
                }
            }

        } else {
            //防止支付页面因app超时被锁住
            if (!isWhiteLimitIgnorePage(currentApp)) {

                if (MMKVUtils.getBoolean(MyConstants.SP_KEY_LIMIT_SETTING_APP_LEFT, true)) {
                    monitorFloatWindow[index].hideWindow()
                }
                //倒计时结束后再弹出，否则锁机时锁机页面也会弹出来
//                    performHome(this@BackgroundService, "2")
                noticeFloatWindow.showDenyWindow("『${MyUtil.getAppLimitTitle(appLimit)}』已超过您在app监督中设置的限制时长", 3000, true, appLimit) {

                }

            }
        }
    }

    override fun onCreate() {
        super.onCreate()

        Log.d("BackgroundService:", "onCreate")

        initInitial()//重启后初始化

//        isScreenOn = isScreenOn()
        syncHelper = SyncHelper(this)
        initData()


//        initNotification(AppUtils.getAppName(), "守护服务运行中")

        initBroadcast()
        launcherPkg = UsageUtil.getLauncherPackageName(applicationContext)
        WorkUtil.startCheckScheduleTaskJob(this@BackgroundService, 30 * 1000L)
        isSilent = isSilentMode(this@BackgroundService)
        Log.d("BackgroundService:", "onCreate end")

        //禁止下拉菜单栏
//        GlobalScope.launch(Dispatchers.IO) {
//            while (whileLockState) {
//                if (isLockRunning() && isWorkState() && TheApplication.getInstance().globalParams.isDenyDropDown && MMKVUtils.getLong(
//                        MyConstants.SP_KEY_FORCE_UNLOCK_PAUSE,
//                        0L
//                    ) <= System.currentTimeMillis()
//                ) {
//                    BackgroundServiceUtil.collapsingNotification(<EMAIL>)
//                    delay(100)
//                } else {
//                    delay(1000)
//                }
//            }
//        }

        //刷新在线时间
        GlobalScope.launch(Dispatchers.IO) {
            while (whileOnline) {
                if (syncOnlineIndex >= syncOnlineMax) {
                    syncOnlineIndex = 0
                    if (TheApplication.getInstance().globalParams.runState == RunState.LOCKING) {
                        try {
                            val result = MyRetrofitClient.service.syncOnline()
                            if (result.code == 200) {
                                if (result.data == 2) {//用户支付解锁
                                    MMKVUtils.put(MyConstants.SP_KEY_PAY_UNLOCK, true)
                                    forceUnlock(MMKVUtils.getInt(MyConstants.SP_KEY_FORCE_UNLOCK_TYPE, 0))
                                } else if (result.data == 1) {//开发者解锁
                                    forceUnlock(0)
                                }
                            }
                        } catch (e: Exception) {

                        }
                    }
                } else {
                    syncOnlineIndex++
                }
                delay(1000)
            }
        }

        //选取app屏蔽页面
        GlobalScope.launch(Dispatchers.IO) {
            while (whileChooseDenyPage) {
                if (chooseDenyPageFloatWindow.isShowing()) {
                    val whitePkgInfo = UsageUtil.getTopPkgInfo(applicationContext, useAccessibility = false, retainChoosePage = false)
                    val icon = getAppIcon(whitePkgInfo.pkgName, whitePkgInfo.activityName)
                    val name = getAppName(whitePkgInfo.pkgName, whitePkgInfo.activityName)

                    withContext(Dispatchers.Main) {
                        chooseDenyPageFloatWindow.refreshWindow(icon, name, whitePkgInfo.pkgName, whitePkgInfo.activityName)
                    }
                }
                delay(200)
            }
        }

        //监督app使用时长
        GlobalScope.launch(Dispatchers.IO) {
            var lastSecondFlag = -1
            val whiteAppList = mutableListOf<WhiteApp>()
            val appLimitList = mutableListOf<AppLimit>()
            while (whileMonitor) {
                if (isScreenOn()) {
                    val currentSecondFlag = Calendar.getInstance().get(Calendar.SECOND)
                    if (currentSecondFlag != lastSecondFlag) {

                        val currentAppList = UsageUtil.getTopPkgInfoList(applicationContext, useAccessibility = true, retainChoosePage = false, useLogSecond = 600)
//                        currentAppList.forEachIndexed { index, it ->
//                            Log.d("BackgroundService:", "currentTop[${index}]:$it")
//                            if(it.pkgName=="com.coloros.smartsidebar") performHome(TheApplication.getInstance(),"sdfsdf")
//                        }
                        val showIndexSet = mutableSetOf<Int>()
                        for (i in currentAppList.indices) {
                            var isWhiteLimited = false
                            var isAppLimited = false
                            var whiteLeftTime = 0L
                            var appLeftTime = 0L
                            if (whiteAppList.size < i + 1) {
                                whiteAppList.add(WhiteApp())
                            }
                            if (appLimitList.size < i + 1) {
                                appLimitList.add(AppLimit())
                            }

                            val currentApp = currentAppList[i]


                            //锁机状态下，锁机白名单的限时
                            if (TheApplication.getInstance().globalParams.runState == RunState.LOCKING) {
                                //锁机暂停
                                if (MMKVUtils.getLong(MyConstants.SP_KEY_FORCE_UNLOCK_PAUSE, 0L) > System.currentTimeMillis()) {
                                    //暂停中，无需屏蔽
                                } else {
                                    run whiteForEach@{
                                        runningWhiteAppList.forEach {
                                            if (it.maxLen != -1 && it.pkg == currentApp.pkgName) {
                                                isWhiteLimited = true
                                                whiteAppList[i] = it
                                                val usedLength =
                                                    monitorViewModel.getAppUsageLengthFromTrueTime(it.pkg, lockHistory!!.startTime, System.currentTimeMillis())
                                                whiteLeftTime = it.maxLen * 60 - usedLength//s

                                                return@whiteForEach
                                            }
                                        }
                                    }
                                }
                            }

                            val whiteAppLeftTimeList = mutableListOf<Long>()
                            //监督app限时
                            run whiteForEach@{
                                appLimits.forEach {
                                    if (it.valid) {
                                        val pkgList = MyUtil.jsonToPkgList(it.appPkg)
                                        if (pkgList.contains(currentApp.pkgName)) {
                                            val calendar = Calendar.getInstance()
                                            val today = calendar.get(Calendar.DAY_OF_WEEK)
                                            calendar.add(Calendar.DAY_OF_YEAR, -1)
                                            val yesterday = calendar.get(Calendar.DAY_OF_WEEK)
                                            if (MyUtil.isCurrentInTimeRange(
                                                    it.startTime, it.endTime,
                                                    todayValid = MyUtil.getDayValid(today, it), yesterdayValid = MyUtil.getDayValid(yesterday, it)
                                                )
                                            ) {
                                                isAppLimited = true
                                                appLimitList[i] = it
                                                val usedLength = monitorViewModel.getAppsUsageLength(pkgList, it.startTime, it.endTime)
                                                whiteAppLeftTimeList.add(it.limitLength - usedLength)
                                            }
                                        }
                                    }
                                }
                            }
                            //若有多个限时，取最小值
                            if (whiteAppLeftTimeList.isNotEmpty()) {
                                appLeftTime = whiteAppLeftTimeList.min()
                            }

                            if (isWhiteLimited && isAppLimited) {
                                val icon = getAppIcon(currentApp.pkgName, currentApp.activityName)
                                when (MMKVUtils.getString(MyConstants.SP_KEY_LIMIT_SETTING_WHO_FIRST, MyConstants.SP_KEY_LIMIT_SETTING_WHITE_FIRST)) {
                                    MyConstants.SP_KEY_LIMIT_SETTING_WHITE_FIRST -> {
                                        withContext(Dispatchers.Main) {
                                            showWhiteAppLimitNotice(whiteLeftTime, whiteAppList[i], icon, currentApp, i)
                                        }
                                    }

                                    MyConstants.SP_KEY_LIMIT_SETTING_LIMIT_FIRST -> {
                                        withContext(Dispatchers.Main) {
                                            showAppLimitNotice(appLeftTime, appLimitList[i], icon, currentApp, i)
                                        }
                                    }

                                    MyConstants.SP_KEY_LIMIT_SETTING_LESS_FIRST -> {
                                        withContext(Dispatchers.Main) {
                                            if (whiteLeftTime <= appLeftTime) {
                                                showWhiteAppLimitNotice(whiteLeftTime, whiteAppList[i], icon, currentApp, i)
                                            } else {
                                                showAppLimitNotice(appLeftTime, appLimitList[i], icon, currentApp, i)
                                            }
                                        }
                                    }
                                }

                                showIndexSet.add(i)
                            } else if (isWhiteLimited) {
                                val icon = getAppIcon(currentApp.pkgName, currentApp.activityName)
                                withContext(Dispatchers.Main) {
                                    showWhiteAppLimitNotice(whiteLeftTime, whiteAppList[i], icon, currentApp, i)
                                }
                                showIndexSet.add(i)
                            } else if (isAppLimited) {
                                val icon = getAppIcon(currentApp.pkgName, currentApp.activityName)
                                withContext(Dispatchers.Main) {
                                    showAppLimitNotice(appLeftTime, appLimitList[i], icon, currentApp, i)
                                }
                                showIndexSet.add(i)
                            } else {
//                            withContext(Dispatchers.Main) {
//                                monitorFloatWindow[i].hideWindow()
//                                //                                lastSmallWindowPkg = ""
//                            }
                            }
                        }

                        monitorFloatWindow.forEach {
                            if (!showIndexSet.contains(it.index)) {
                                withContext(Dispatchers.Main) { monitorFloatWindow[it.index].hideWindow() }
                            }
                        }


                        lastSecondFlag = currentSecondFlag
                    }
                    delay(100)
                } else {
                    delay(1000)
                }

            }
        }

        //锁机状态检查
        GlobalScope.launch(Dispatchers.IO) {

            while (whileLockState) {//检查工作状态
                if (TheApplication.getInstance().globalParams.runState == RunState.LOCKING) {
                    val currentWorkState = calculateAndRefreshWindow()
                    if (isScreenOn()) {
                        when (currentWorkState) {
                            WorkState.WORKING -> {
                                //是否显示禁止下拉区域
                                if (PermissionUtil.isAccessibilitySettingsOn(MyConstants.ACCESS_ABILITY_NAME, applicationContext)
                                    && TheApplication.getInstance().globalParams.accessibilityService != null
                                    && MMKVUtils.getInt(MyConstants.SP_SETTING_DENY_DROPDOWN_NEW, 0) == 2
                                ) {
                                    withContext(Dispatchers.Main) {
                                        //锁机暂停
                                        if (MMKVUtils.getLong(MyConstants.SP_KEY_FORCE_UNLOCK_PAUSE, 0L) > System.currentTimeMillis()) {
                                            denyDropFloatWindow.hideDenyWindow()
                                        } else {
                                            denyDropFloatWindow.showDenyWindow()
                                        }
                                    }
                                }

                                withContext(Dispatchers.Main) {
                                    restFloatWindow.hideWindow()
                                }

                                val isWhiteAppResult = isWhiteApp()
                                currentPageType = isWhiteAppResult.first
                                when (currentPageType) {
                                    CurrentPageType.UNKNOWN_APP, CurrentPageType.SYSTEM_BLACK_PAGE, CurrentPageType.SYSTEM_BLACK_IGNORE_WHITE_PAGE -> {
                                        //若是已知黑名单就直接弹回桌面
                                        if (currentPageType in arrayOf(CurrentPageType.SYSTEM_BLACK_PAGE, CurrentPageType.SYSTEM_BLACK_IGNORE_WHITE_PAGE)) {
                                            performHome(TheApplication.getInstance(), "BackgroundService not white Page")
                                        }

                                        //显示锁机页面
                                        withContext(Dispatchers.Main) {
                                            lockFloatWindow.showLockWindow()
                                            denyPageNoticeFloatWindow.hideWindow()
                                            ServiceUtil.setTopApp(applicationContext)
                                        }

                                        //刷新倒数日
                                        if (Calendar.getInstance().get(Calendar.DAY_OF_MONTH) != lastDay) {
                                            withContext(Dispatchers.Main) {
                                                lockFloatWindow.refreshWordAndDestiny()
                                                DenyUninstallAppWidget3.updateAppWidget(
                                                    this@BackgroundService, AppWidgetManager.getInstance(this@BackgroundService)
                                                )
                                            }
                                            lastDay = Calendar.getInstance().get(Calendar.DAY_OF_MONTH)
                                        }

                                        //刷新5s提醒时间
                                        if (lockFloatWindow.initViewApps) {
                                            val currentTime = System.currentTimeMillis()
                                            if (MMKVUtils.getBoolean(MyConstants.SP_KEY_APPS_NOTIFY, true) &&
                                                (currentTime - TheApplication.getInstance().globalParams.lastClickHomeTime < 5000)
                                            ) {
                                                withContext(Dispatchers.Main) {
                                                    lockFloatWindow.containerLayout?.let {
                                                        it.mcv_app_notify.visibility = View.VISIBLE
                                                        it.mcv_app_notify.tv_daojishi.text =
                                                            "${5 - (currentTime - TheApplication.getInstance().globalParams.lastClickHomeTime) / 1000}"
                                                    }
                                                }
                                            } else {
                                                withContext(Dispatchers.Main) {
                                                    lockFloatWindow.containerLayout?.let {
                                                        it.mcv_app_notify.visibility = View.GONE
                                                    }
                                                }
                                            }
//                                            LogUtils.d("isSystemApp = ${isSystemApp(currentDenyAppInfo!!.pkgName)}")

                                            currentDenyAppInfo?.apply {
                                                //当前在桌面或自身app正在显示，则不用弹出
                                                if (
                                                    currentDenyAppInfo!!.pkgName == launcherPkg ||
                                                    currentDenyAppInfo!!.pkgName == AppUtils.getAppPackageName() ||
                                                    currentDenyAppInfo!!.pkgName in arrayOf(
                                                        "com.android.launcher",
                                                        "com.android.launcher3",
                                                        "com.huawei.android.launcher",
                                                        "com.hihonor.android.launcher",
                                                        "com.oppo.launcher",
                                                        "com.miui.home",
                                                        "com.sonymobile.launcher",
                                                        "com.teslacoilsw.launcher",
                                                        "bitpit.launcher",
                                                        "com.tblenovo.launcher",
                                                        "com.gionee.amisystem",
                                                        "com.hp.xuetanglauncher",
                                                        "jp.co.sharp.android.launcher3",
                                                        "com.sec.android.emergencylauncher",
                                                        "com.zte.mifavor.launcher",
                                                        "com.lge.launcher3",
                                                        "com.microsoft.launcher",
                                                        "com.sec.android.app.launcher",
                                                        "com.bbk.launcher2",
                                                        "com.android.systemui",
                                                        "com.zte.mifavor.launcher",
                                                        "shinado.indi.piping",
                                                        "com.bbk.studyos.launcher",
                                                        "com.meizu.flyme.launcher",
                                                        "com.tblenovo.studentlauncher",
                                                        "com.zui.launcher",
                                                        "com.sprd.powersavemodelauncher",
                                                        "com.noahedu.launcher",
                                                        "ginlemon.flowerfree",
                                                        "com.xrz.ebook.launcher",
                                                        "com.bbk.scene.launcher.theme",
                                                        "com.hmct.vision",
                                                        "com.motorola.launcher3",
                                                        "com.qi.studycomputer.launcher",
                                                        "cn.nubia.launcher",
                                                        "net.oneplus.launcher",
                                                        "com.freeme.launcher",
                                                        "com.nttdocomo.android.homezozo",
                                                        "com.qiku.android.launcher3",
                                                        "com.pri.launcher3",
                                                        "com.vivo.simplelauncher",
                                                        "com.smartisanos.launcher",
                                                        "app.lawnchair",
                                                        "com.tcl.android.launcher",
                                                        "com.blackshark.launcher",
                                                        "com.hsc.launcher3",
                                                        "app.olauncher",
                                                        "com.asus.launcher",
                                                        "com.sgtc.launcher",
                                                        "com.asus.launcher3",
                                                        "com.noahedu.launcher3",
                                                        "com.vivo.privacylauncher",
                                                        "com.zui.desktoplauncher",
                                                        "com.readboy.launcher_c10_primary",
                                                        "com.google.android.apps.nexuslauncher",
                                                        "com.huawei.watch.home",//辅屏桌面
                                                        "com.jxw.launcher",//学生平板
                                                        "com.miui.securityadd"//超级省电
                                                    )
                                                ) {
                                                    //桌面和自身，不显示上报弹窗，也不返回桌面
                                                } else {
                                                    performHome(TheApplication.getInstance(), "BackgroundService unknownApp $pkgName $activityName")
//                                                    LogUtils.d("currentDenyAppInfo!!.pkgName = ${currentDenyAppInfo!!.pkgName}")
                                                    //多任务页面和侧边栏只弹出，不提示上报
                                                    if (!currentDenyAppInfo!!.activityName.contains("recents", true) &&
                                                        currentDenyAppInfo!!.pkgName != "com.huawei.hwdockbar" &&
                                                        currentDenyAppInfo!!.pkgName != "com.hihonor.hndockbar" &&
                                                        currentDenyAppInfo!!.pkgName != "com.coloros.smartsidebar"
                                                    ) {
                                                        val appName = getAppName(pkgName, activityName)
                                                        val appIcon = getAppIcon(pkgName, activityName)

                                                        DialogUtil.showFloatingAppDialog(appIcon, appName, pkgName, activityName)
                                                    }
                                                }


                                            }


                                        }
                                    }

                                    CurrentPageType.USER_WHITE_APP, CurrentPageType.SYSTEM_WHITE_PAGE -> {
                                        withContext(Dispatchers.Main) {
                                            lockFloatWindow.hideLockWindow()
                                            denyPageNoticeFloatWindow.hideWindow()
                                        }
                                    }

                                    CurrentPageType.USER_BLACK_PAGE -> {
                                        withContext(Dispatchers.Main) {
                                            lockFloatWindow.hideLockWindow(taskStop = true)
                                            denyPageNoticeFloatWindow.showWindow(isWhiteAppResult.second)
                                        }
                                    }
                                }

                            }

                            WorkState.RESTING -> {
                                withContext(Dispatchers.Main) {
                                    lockFloatWindow.hideLockWindow(taskStop = true)
                                    denyPageNoticeFloatWindow.hideWindow()
                                    denyDropFloatWindow.hideDenyWindow()
                                    if (restFloatWindow.showRest) {
                                        restFloatWindow.showWindow()
                                    } else {
                                        restFloatWindow.hideWindow()
                                    }
                                }
                            }

                            else -> {}
                        }
                    } else {
                        LogUtils.d("isScreenOn = false，real is ${isScreenOn()}")
                        delay(800)
                    }

                    if (TheApplication.getInstance().globalParams.workState != currentWorkState) {
                        when (currentWorkState) {
                            WorkState.WORKING -> {
                                getAudioFocus(true)
                                currentPageType = isWhiteApp().first
                                when (currentPageType) {
                                    CurrentPageType.UNKNOWN_APP, CurrentPageType.SYSTEM_BLACK_PAGE, CurrentPageType.SYSTEM_BLACK_IGNORE_WHITE_PAGE -> {
                                        performHome(this@BackgroundService, "BackgroundService lockStart")
                                    }

                                    else -> {}
                                }


                                lockFloatWindow.getWakeLock()
                                withContext(Dispatchers.Main) {
                                    lockFloatWindow.startWhiteNoise(lockFloatWindow.whiteNoiseIndex)
                                }
                                startNotify()
                                startShake()


                                isSilent = isSilentMode(this@BackgroundService)
                                if (runningLockConfig.isSilent && PermissionUtil.hasPermissionVolume(applicationContext)) {
                                    PermissionUtil.silentSwitchOn(applicationContext)
                                }
                            }

                            WorkState.RESTING -> {
                                getAudioFocus(false)
                                lockFloatWindow.removeWakeLock()
                                withContext(Dispatchers.Main) {
                                    lockFloatWindow.stopAllWhitePlaying()
                                }
                                endNotify()
                                endShake()

                                if (runningLockConfig.isSilent && PermissionUtil.hasPermissionVolume(applicationContext)) {
                                    if (!isSilent) PermissionUtil.silentSwitchOff(applicationContext)
                                }
                            }

                            WorkState.ENDING -> {
                                getAudioFocus(false)
                                lockFloatWindow.removeWakeLock()
                                withContext(Dispatchers.Main) {
                                    lockFloatWindow.stopAllWhitePlaying()
                                }
                                endNotify()
                                endShake()

                                if (runningLockConfig.isSilent && PermissionUtil.hasPermissionVolume(applicationContext)) {
                                    if (!isSilent) PermissionUtil.silentSwitchOff(applicationContext)
                                }

                                lockRunEnd()
                            }

                            else -> {}
                        }

                        TheApplication.getInstance().globalParams.workState = currentWorkState
                    }

                    if (!startedOver) {
                        startedOver = true
                        LiveEventBus.get(LiveBus.START_OVER, String::class.java).post("")
                    }
                    delay(200)
                } else {
                    //为防止出现解锁后突然又复锁的bug
                    lockFloatWindow.hideLockWindow(taskStop = true)
                    delay(1000)
                }
            }
        }


        //检查Schedule
        GlobalScope.launch(Dispatchers.IO) {
            while (whileSchedule) {

//                var isMultiScreen = false
//                if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.R) {
//                    isMultiScreen=<EMAIL>
//                }else{
//                    isMultiScreen=WindowManagerGlobal.getWindowManagerService().getDockedStackSide()  !=  WindowManager.DOCKED_INVALID
//                }

                if (TheApplication.getInstance().globalParams.runState == RunState.CHECK_SCHEDULE) {
                    val currentMinute = Calendar.getInstance().get(Calendar.MINUTE)
                    if (currentMinute != TheApplication.getInstance().globalParams.lastMinute) {
                        TheApplication.getInstance().globalParams.lastMinute = currentMinute
                        if (!checkSchedule()) {
                            checkMonitor()
                        }
                    }
                    if (!startedOver) {
                        startedOver = true
                        LiveEventBus.get(LiveBus.START_OVER, String::class.java).post("")
                    }
                }
                delay(1000)

            }
        }
    }

    override fun onBind(intent: Intent?): IBinder? {
        return null
    }

    override fun onStartCommand(intent: Intent?, flags: Int, startId: Int): Int {
        Log.d("BackgroundService:", "onStartCommand")
        return START_STICKY
    }


    override fun onDestroy() {

        Log.d("BackgroundService:", "onDestroy")

        lockViewModel.schedulesLiveData.removeObserver(scheduleWithSubObserver)
        lockViewModel.unfinishedLockHistoryLiveData.removeObserver(lockHistoryObserver)
        unregisterReceiver(innerReceiver)
        lockFloatWindow.mediaPlayerList.forEach {
            it.stop()
            it.release()
        }
        notifyPlayerList.forEach {
            it.stop()
            it.release()
        }

        second60Index = 0
        whileOnline = false
        whileChooseDenyPage = false
        whileMonitor = false
        whileLockState = false
        whileSchedule = false

        super.onDestroy()
    }

    private var lastOrientation = -1
    override fun onConfigurationChanged(newConfig: Configuration) {
        super.onConfigurationChanged(newConfig)

        if (newConfig.orientation != lastOrientation) {
            // Checks the orientation of the screen
            when (newConfig.orientation) {
                Configuration.ORIENTATION_LANDSCAPE -> {
                    LogUtils.d("Device is in Landscape mode.")
//                    TheApplication.getInstance().globalParams.isPortrait = false
                }

                Configuration.ORIENTATION_PORTRAIT -> {
                    LogUtils.d("Device is in Portrait mode.")
//                    TheApplication.getInstance().globalParams.isPortrait = true
                }

                Configuration.ORIENTATION_SQUARE -> {

                    LogUtils.d("Device is in ORIENTATION_SQUARE mode.")
                }

                Configuration.ORIENTATION_UNDEFINED -> {
                    LogUtils.d("Device is in ORIENTATION_UNDEFINED mode.")
                }
            }
            lastOrientation = newConfig.orientation

//            TheApplication.getInstance().globalParams.isReverse = !TheApplication.getInstance().globalParams.isReverse


//            lockFloatWindow.refreshLockView()
        }
    }


    private fun initNotifyList() {
        notifySource.add(R.raw.ring1_ding)
        notifySource.add(R.raw.ring2_dong)
        notifySource.add(R.raw.ring3_complete)
        notifySource.add(R.raw.ring4_clear)
        notifySource.add(R.raw.ring5_piano)
        notifySource.add(R.raw.ring6_kanoon)
        notifySource.add(R.raw.ring7_box)

        notifySource.forEachIndexed { index, i ->
            MediaPlayer().apply {
                setDataSource(applicationContext, UriUtil.res2Uri(notifySource[index]))
                setAudioAttributes(
                    AudioAttributes.Builder().setLegacyStreamType(AudioManager.STREAM_NOTIFICATION).setUsage(AudioAttributes.USAGE_NOTIFICATION)
                        .setContentType(AudioAttributes.CONTENT_TYPE_SONIFICATION).build()
                )
                prepare()
                notifyPlayerList.add(this)
            }
        }
    }


    private fun initData() {
        // ViewModels 现在通过 Hilt 自动注入，无需手动创建

        lockFloatWindow = FloatWindowOfLock(this@BackgroundService, runningWhiteAppList, runningLockConfig, runningLockHistory)
        restFloatWindow = FloatWindowOfRest(this@BackgroundService)
        denyDropFloatWindow = FloatWindowOfDenyDrop(this@BackgroundService)
        chooseDenyPageFloatWindow = FloatWindowOfChooseDenyPage(this@BackgroundService)
        lockFloatWindow.initMediaSource()
        lockFloatWindow.initWhiteNoise()
        noticeFloatWindow = FloatWindowOfNotice(this@BackgroundService, monitorViewModel)


        monitorNotifyFloatWindow = FloatWindowOfMonitorNotify(this@BackgroundService)
        tiredNotifyWindow = NotifyWindowOfTired(this@BackgroundService)
        scheduleNotifyWindow = NotifyWindowOfSchedule(this@BackgroundService)
        dayLimitNotifyWindow = NotifyWindowOfDayLimit(this@BackgroundService)
        denyPageNoticeFloatWindow = FloatWindowOfDenyPageNotice(this@BackgroundService)

        initNotifyList()
        lockFloatWindow.initWakeLock()

//        syncHelper.startSyncAll()//第一次启动接收不到mainactivity发过来的，所以在这里同步一下

        LiveEventBus.get(LiveBus.SHOW_DENY_PAGE_PICK, Boolean::class.java).observeForever {
            if (it) {
                chooseDenyPageFloatWindow.showWindow()
            } else {
                chooseDenyPageFloatWindow.hideWindow()
            }
        }


//        LiveEventBus.get(LiveBus.PICK_DENY_PAGE, String::class.java).observeForever {
//            val denyPageString = MMKVUtils.getString(MyConstants.SP_KEY_DENY_PAGE_V2)
//            val denyPageList = MyGsonUtil.jsonToTheDenyPageList(denyPageString)
//
//            val pickDenyPage = TheDenyPage(it, "", "", true)
//            var ifContains = false
//            denyPageList.forEach {
//                if (it.pkg == pickDenyPage.pkg && it.activity == pickDenyPage.activity) {
//                    ifContains = true
//                    it.valid = true
//                }
//            }
//
//            if (ifContains) {
//                MyToastUtil.showError("该屏蔽设置已存在，无需重复添加")
//            } else {
//                MyToastUtil.showSuccess("添加成功")
//                denyPageList.add(pickDenyPage)
//            }
//
//
//            MMKVUtils.put(MyConstants.SP_KEY_DENY_PAGE_V2, GsonUtils.toJson(denyPageList))
//            denyPageFloatWindow.changeToSmallView()
//        }

        LiveEventBus.get(LiveBus.USER_FORCE_UNLOCK, String::class.java).observeForever {
//            MyToastUtil.showError("aaaaaaaaaaaaaa${it}")
            if (isLockRunning()) {
                if (it == "payFailed") {
                    MyToastUtil.showInfo("支付失败")
                } else if (it == "payFinish") {
                    //先刷新云端的支付状态，否则可能支付结果未及时返回
                    GlobalScope.launch(Dispatchers.IO) {
                        try {
                            val result = MyRetrofitClient.service.queryOrder()
                            if (result.code == 200) {
                                withContext(Dispatchers.Main) {

                                    if (second60Index > 0) {
                                        second60Index = 60
                                    } else {
                                        GlobalScope.launch(Dispatchers.IO) {
                                            syncOnlineMax = 1
                                            second60Index = 60
                                            while (second60Index > 0) {
                                                delay(1000)
                                                second60Index--
                                            }
                                            syncOnlineMax = 60
                                        }
                                    }

                                }
                            } else {
                                withContext(Dispatchers.Main) {
                                    MyToastUtil.showError(result.msg)
                                }
                            }
                        } catch (e: Exception) {
                            MyToastUtil.showInfo(e.message)
                        }
                    }
                } else {
                    if (second60Index > 0) {
                        second60Index = 60
                    } else {
                        GlobalScope.launch(Dispatchers.IO) {
                            syncOnlineMax = 1
                            second60Index = 60
                            while (second60Index > 0) {
                                delay(1000)
                                second60Index--
                            }
                            syncOnlineMax = 60
                        }
                    }

                }

            }
        }

        LiveEventBus.get(LiveBus.FORCE_QUIT, String::class.java).observeForever {
            forceUnlock(MMKVUtils.getInt(MyConstants.SP_KEY_FORCE_UNLOCK_TYPE, 0))
        }

        LiveEventBus.get(LiveBus.UPLOAD_DENY, String::class.java).observeForever {
            currentDenyAppInfo?.let {
                GlobalScope.launch(Dispatchers.IO) {
                    try {
                        val result = MyRetrofitClient.service.uploadDenyPkgInfo(getAppName(it.pkgName, it.activityName), it.pkgName, it.activityName)
                        if (result.code == 200) {
                            MyToastUtil.showSuccess(result.msg)
                        } else {
                            MyToastUtil.showWarning(result.msg)
                        }
                    } catch (e: Exception) {
                        MyToastUtil.showError(e.message)
                    }
                }
            }
        }

        LiveEventBus.get(LiveBus.START_SYNC_ALL, String::class.java).observeForever {
            syncHelper.startSyncAll(s = it)
        }
        LiveEventBus.get(LiveBus.START_SYNC_WHITE_APP, String::class.java).observeForever {
            syncHelper.startSyncWhiteApp()
        }
        LiveEventBus.get(LiveBus.START_SYNC_FAST, String::class.java).observeForever {
            syncHelper.startSyncFast()
        }
        LiveEventBus.get(LiveBus.START_SYNC_TOMATO, String::class.java).observeForever {
            syncHelper.startSyncTomato()
        }
        LiveEventBus.get(LiveBus.START_SYNC_SCHEDULE, String::class.java).observeForever {
            syncHelper.startSyncSchedule()
        }
        LiveEventBus.get(LiveBus.START_SYNC_APP_LIMIT, String::class.java).observeForever {
            syncHelper.startSyncAppLimit()
        }

        LiveEventBus.get(LiveBus.START_SYNC_HISTORY, String::class.java).observeForever {
            syncHelper.startSyncHistory()
        }

        LiveEventBus.get(LiveBus.LOGIN, Boolean::class.java).observeForever {
            lockViewModel.setUserId(MMKVUtils.getInt(MyConstants.SP_KEY_USER_ID, -1))
            monitorViewModel.setUserId(MMKVUtils.getInt(MyConstants.SP_KEY_USER_ID, -1))
        }

        lockViewModel.schedulesLiveData.observeForever(scheduleWithSubObserver)
        lockViewModel.unfinishedLockHistoryLiveData.observeForever(lockHistoryObserver)
//        lockViewModel.globalWhiteAppsLiveData.observeForever(globalWhiteAppsObserver)

        monitorViewModel.getTodayLimits(MyDateUtil.getTodayName()).observeForever {
//            dayLimit = it

            LogUtils.d("checkMonitor getTodayLimits $it")
            GlobalScope.launch(Dispatchers.IO) {
                refreshScreenUsedTime(it)
            }
        }

//        monitorViewModel.setUserId(MMKVUtils.getInt(MyConstants.SP_KEY_USER_ID, -1))
        monitorViewModel.appLimits.observeForever {
            appLimits = it
        }
    }


    private suspend fun refreshScreenUsedTime(dayLimit: DayLimit?) {
        val usedLength = if (dayLimit == null) {
            monitorViewModel.getDayUsageLength()
        } else {
            if (dayLimit.isIncludeWhite) {
                monitorViewModel.getDayUsageLength()
            } else {
                val globalWhiteAppList = lockViewModel.getGlobalWhiteAppList()
                monitorViewModel.getDayUsageLengthWithoutWhite(globalWhiteAppList)
            }
        }
        initNotification("屏幕使用时间", "今天已使用${TimeUtil.formatHHMMSimple((usedLength / 60).toInt())}")
    }

    private fun initBroadcast() {
        innerReceiver = InnerReceiver()
        val intentFilter = IntentFilter()
        intentFilter.addAction(Intent.ACTION_CLOSE_SYSTEM_DIALOGS)
        intentFilter.addAction(Intent.ACTION_TIME_TICK)
        intentFilter.addAction(Intent.ACTION_USER_PRESENT)
        intentFilter.addAction(Intent.ACTION_SCREEN_ON)
        intentFilter.addAction(Intent.ACTION_SCREEN_OFF)
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.TIRAMISU) {
            registerReceiver(innerReceiver, intentFilter, RECEIVER_NOT_EXPORTED)
        } else {
            registerReceiver(innerReceiver, intentFilter)
        }
    }

    private fun initNotification(title: String, content: String) {
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
            val channelId = "channel_daemon"
            val channelName = "守护进程"
            val chan = NotificationChannel(channelId, channelName, NotificationManager.IMPORTANCE_LOW)
            chan.lightColor = Color.BLUE
            chan.lockscreenVisibility = Notification.VISIBILITY_PRIVATE
            val service = applicationContext.getSystemService(Context.NOTIFICATION_SERVICE) as NotificationManager
            service.createNotificationChannel(chan)
            val notificationBuilder = Notification.Builder(applicationContext, channelId)
            val notification =
                notificationBuilder.setOngoing(true).setSmallIcon(R.mipmap.ic_launcher).setCategory(Notification.CATEGORY_SERVICE).setContentTitle(title)
                    .setContentText(content).setVisibility(Notification.VISIBILITY_SECRET).build()
            startForeground(1000, notification)//该方法已创建通知管理器，设置为前台优先级后，点击通知不再自动取消
        } else {
            val mBuilder = Notification.Builder(applicationContext).setSmallIcon(R.mipmap.ic_launcher).setContentTitle(title).setContentText(content)
                .setVisibility(Notification.VISIBILITY_SECRET)
            val notification = mBuilder.build()
            startForeground(1000, notification)//该方法已创建通知管理器，设置为前台优先级后，点击通知不再自动取消
        }
    }

    private suspend fun lockRunEnd() {
        withContext(Dispatchers.Main) {
            MMKVUtils.put(MyConstants.SP_KEY_FLAG_HAS_LOCKED, true)
            lockFloatWindow.stopAllWhitePlaying()
            lockFloatWindow.hideLockWindow(taskStop = true)
            restFloatWindow.hideWindow()
            denyPageNoticeFloatWindow.hideWindow()
            denyDropFloatWindow.hideDenyWindow()
            if ((!MMKVUtils.getBoolean(MyConstants.SP_KEY_INTRO_8, false)) || (MMKVUtils.getBoolean(MyConstants.SP_KEY_ALLOW_AUTO_LAUNCH, true))) {
//                val intent = Intent(this@CheckService, MainActivity::class.java)
//                intent.addFlags(Intent.FLAG_ACTIVITY_NEW_TASK)
//                startActivity(intent)
                MMKVUtils.put(MyConstants.SP_KEY_FINISH_LOCK, true)
                MMKVUtils.put(MyConstants.SP_KEY_FINISH_LOCK_LENGTH, runningLockHistory.trueTimeLength)
                //ljqljqljq
//                ServiceUtil.setTopApp(applicationContext)
                //关闭已经存在的页面，否则打卡提示弹不出来
                LiveEventBus.get(LiveBus.CLOSE_MAIN_ACTIVITY, String::class.java).post("")

                startActivityCompatible(this@BackgroundService, AppUtils.getAppPackageName(), TheMainActivity::class.java.canonicalName!!.toString())
            }


            refreshScreenUsedTime(monitorViewModel.getTodayLimitsImmediately(MyDateUtil.getTodayName()))


        }
        lastDay = 0
        lockFloatWindow.wakeLockType = 0
        lockFloatWindow.removeWakeLock()
        restFloatWindow.showRest = true
//        lockFloatWindow.lockWindowCreated = false
//        lockFloatWindow.containerLayout = null
        lockFloatWindow.releaseLockView()
//        restFloatWindow.windowCreated = false
        restFloatWindow.releaseLockView()
//        restFloatWindow.layout = null

        lockFloatWindow.initViewApps = false
        lockFloatWindow.initViewLight = false
        lockFloatWindow.initViewMusic = false
        lockFloatWindow.initViewStop = false
//        denyDropFloatWindow.denyDropWindowCreated = false
        lockFloatWindow.whiteNoiseIndex = 0

        if (runningLockHistory.lockType == 3) {
            if (!runningScheduleWithSub.schedule.isRecycle) {
                runningScheduleWithSub.schedule.validate = false
                lockViewModel.updateSchedule(runningScheduleWithSub.schedule)
            }
        }

        runningLockHistory.isFinish = true

        endNotify()
        endShake()

        TheApplication.getInstance().globalParams.runState = RunState.UNKNOWN //-1
        TheApplication.getInstance().globalParams.workState = WorkState.UNKNOWN
        LiveEventBus.get(LiveBus.FORCE_UNLOCK_PAGE_FINISH, String::class.java).post("")
//        syncOnlineChanging = false
        second60Index = 0
        lockViewModel.updateLockHistory(runningLockHistory)
        hasOpenMinute = -1
        try {
            MyRetrofitClient.service.offline()
        } catch (e: Exception) {

        }
    }

    private fun refreshSameView() {
        lockFloatWindow.containerLayout?.let {
            currentCalendar = Calendar.getInstance()
            val time = System.currentTimeMillis()
            it.tv_lock_view_range.text = "${formatRelativeTime(currentCalendar, time, runningLockHistory.startTime)} —— ${
                formatRelativeTime(
                    currentCalendar, time, runningLockHistory.startTime + runningLockHistory.timeLength
                )
            }"

        }
    }

    private suspend fun calculateAndRefreshWindow(): WorkState {
        leftTime = 0L
        var result = WorkState.WORKING
        when (runningLockHistory.lockType) {
            1 -> {
//                leftTime = runningLockHistory.timeLength - (System.currentTimeMillis() - runningLockHistory.startTime)
                val pastTime = System.currentTimeMillis() - runningLockHistory.startTime
                leftTime = if (pastTime >= 0) {
                    runningLockHistory.timeLength - pastTime
                } else {
                    //防止用户调整系统时间，导致当前时间小于起始时间，从而timeLength过大
                    0
                }
                if (leftTime > 0) {
                    if (isScreenOn() && lockFloatWindow.isShowing()) {
                        withContext(Dispatchers.Main) {
                            lockFloatWindow.containerLayout?.let {
                                it.tv_lock_view_time_left.text = formatHHMMSS(leftTime / 1000)
                                it.tv_lock_view_next.text = "结束"
                                it.cpv_lock_view.setProgress((System.currentTimeMillis() - runningLockHistory.startTime) * 100 / runningLockHistory.timeLength.toFloat())

                                refreshSameView()
//                                it.tv_lock_view_name.text = runningLockHistory.title
                                when (runningLockHistory.simpleLockLength) {
                                    -1 -> {
                                        it.tv_lock_view_name.text = "监督锁机"
                                    }

                                    -2 -> {
                                        it.tv_lock_view_name.text = "疲劳锁机"
                                    }

                                    else -> {
                                        it.tv_lock_view_name.text = "简单锁机"
                                    }
                                }
                            }
                        }
                    }
                    result = WorkState.WORKING
                } else {
                    result = WorkState.ENDING
                }
            }

            2 -> {
                val tomato = runningTomatoWithSub.tomato
                var showLeftTime = 0L
                var restLeftTime = 0L
                timeLen = 0
                var percent = 0f
                var str = ""
                val current = System.currentTimeMillis()
                for (i in 1..tomato.tomatoCount) {
                    if (current >= runningLockHistory.startTime + timeLen * 60 * 1000 && current < runningLockHistory.startTime + timeLen * 60 * 1000 + tomato.tomatoWorkLength * 60 * 1000) {
                        percent = ((current - runningLockHistory.startTime - timeLen * 60 * 1000) * 100.toFloat() / (tomato.tomatoWorkLength * 60 * 1000))
                        showLeftTime = (timeLen + tomato.tomatoWorkLength) * 60 * 1000 - (current - runningLockHistory.startTime)
                        result = WorkState.WORKING
                        if (i < tomato.tomatoCount) {
                            var tempTimeLen = 0
                            if (tomato.tomatoLongRestPerCount > 0 && (i % tomato.tomatoLongRestPerCount == 0)) {
                                tempTimeLen = tomato.tomatoLongRestLength
                            } else {
                                tempTimeLen = tomato.tomatoRestLength
                            }
                            str = "休息${secondToHmSimpleSimple(tempTimeLen * 60.toLong())}"
                        } else {
                            str = "结束"
                        }
                    } else {
                        var tempTimeLen = 0
                        if (tomato.tomatoLongRestPerCount > 0 && (i % tomato.tomatoLongRestPerCount == 0)) {
                            tempTimeLen = tomato.tomatoLongRestLength
                        } else {
                            tempTimeLen = tomato.tomatoRestLength
                        }
                        if (current >= runningLockHistory.startTime + (timeLen + tomato.tomatoWorkLength) * 60 * 1000 && current < runningLockHistory.startTime + (timeLen + tomato.tomatoWorkLength + tempTimeLen) * 60 * 1000) {
                            restLeftTime = runningLockHistory.startTime + (timeLen + tomato.tomatoWorkLength + tempTimeLen) * 60 * 1000 - current
                            result = WorkState.RESTING
                        }
                    }

                    timeLen += tomato.tomatoWorkLength

                    if (i < tomato.tomatoCount) {
                        var tempTimeLen = 0
                        if (tomato.tomatoLongRestPerCount > 0 && (i % tomato.tomatoLongRestPerCount == 0)) {
                            tempTimeLen = tomato.tomatoLongRestLength
                        } else {
                            tempTimeLen = tomato.tomatoRestLength
                        }

                        timeLen += tempTimeLen
                    }
                }

//                leftTime = runningLockHistory.timeLength - (current - runningLockHistory.startTime)
                val pastTime = current - runningLockHistory.startTime
                leftTime = if (pastTime >= 0) {
                    runningLockHistory.timeLength - pastTime
                } else {
                    //防止用户调整系统时间，导致当前时间小于起始时间，从而timeLength过大
                    0
                }
                if (leftTime > 0) {
                    if (isScreenOn()) {
                        withContext(Dispatchers.Main) {
                            if (result == WorkState.WORKING) {
                                if (lockFloatWindow.isShowing()) {
                                    lockFloatWindow.containerLayout?.let {
                                        it.tv_lock_view_time_left.text = formatHHMMSS(showLeftTime / 1000)
                                        it.tv_lock_view_next.text = str
                                        it.cpv_lock_view.setProgress(percent)
                                        refreshSameView()
                                        it.tv_lock_view_name.text = "番茄锁机【🍅${runningTomatoWithSub.tomato.title}】"
                                    }
                                }
                            } else if (result == WorkState.RESTING) {
                                restFloatWindow.refreshText("休息中 ${formatHHMMSS(restLeftTime / 1000)}")
                            }
                        }
                    }
                } else {
                    result = WorkState.ENDING
                }

            }

            3 -> {
                if (runningScheduleWithSub.schedule.useTomato) {
                    val tomato = runningScheduleWithSub.tomatoWithSub!!.tomato
                    var showLeftTime = 0L
                    var restLeftTime = 0L
                    timeLen = 0
                    var percent = 0f
                    var str = ""
                    val current = System.currentTimeMillis()
                    for (i in 1..tomato.tomatoCount) {
                        if (current >= runningLockHistory.startTime + timeLen * 60 * 1000 && current < runningLockHistory.startTime + timeLen * 60 * 1000 + tomato.tomatoWorkLength * 60 * 1000) {
                            percent = ((current - runningLockHistory.startTime - timeLen * 60 * 1000) * 100.toFloat() / (tomato.tomatoWorkLength * 60 * 1000))
                            showLeftTime = (timeLen + tomato.tomatoWorkLength) * 60 * 1000 - (current - runningLockHistory.startTime)
                            result = WorkState.WORKING
                            if (i < tomato.tomatoCount) {
                                var tempTimeLen = 0
                                if (tomato.tomatoLongRestPerCount > 0 && (i % tomato.tomatoLongRestPerCount == 0)) {
                                    tempTimeLen = tomato.tomatoLongRestLength
                                } else {
                                    tempTimeLen = tomato.tomatoRestLength
                                }
                                str = "休息${secondToHmSimpleSimple(tempTimeLen * 60.toLong())}"
                            } else {
                                str = "结束"
                            }
                        } else {
                            var tempTimeLen = 0
                            if (tomato.tomatoLongRestPerCount > 0 && (i % tomato.tomatoLongRestPerCount == 0)) {
                                tempTimeLen = tomato.tomatoLongRestLength
                            } else {
                                tempTimeLen = tomato.tomatoRestLength
                            }
                            if (current >= runningLockHistory.startTime + (timeLen + tomato.tomatoWorkLength) * 60 * 1000 && current < runningLockHistory.startTime + (timeLen + tomato.tomatoWorkLength + tempTimeLen) * 60 * 1000) {
                                restLeftTime = runningLockHistory.startTime + (timeLen + tomato.tomatoWorkLength + tempTimeLen) * 60 * 1000 - current
                                result = WorkState.RESTING
                            }
                        }

                        timeLen += tomato.tomatoWorkLength

                        if (i < tomato.tomatoCount) {
                            var tempTimeLen = 0
                            if (tomato.tomatoLongRestPerCount > 0 && (i % tomato.tomatoLongRestPerCount == 0)) {
                                tempTimeLen = tomato.tomatoLongRestLength
                            } else {
                                tempTimeLen = tomato.tomatoRestLength
                            }

                            timeLen += tempTimeLen
                        }
                    }

//                    leftTime = runningLockHistory.timeLength - (current - runningLockHistory.startTime)
                    val pastTime = current - runningLockHistory.startTime
                    leftTime = if (pastTime >= 0) {
                        runningLockHistory.timeLength - pastTime
                    } else {
                        //防止用户调整系统时间，导致当前时间小于起始时间，从而timeLength过大
                        0
                    }
                    if (leftTime > 0) {
                        if (isScreenOn()) {
                            withContext(Dispatchers.Main) {
                                if (result == WorkState.WORKING) {
                                    if (lockFloatWindow.isShowing()) {
                                        lockFloatWindow.containerLayout?.let {
                                            it.tv_lock_view_time_left.text = formatHHMMSS(showLeftTime / 1000)
                                            it.tv_lock_view_next.text = str
                                            it.cpv_lock_view.setProgress(percent)
                                            refreshSameView()
                                            runningScheduleWithSub.tomatoWithSub?.let { tomatoWithSub ->
                                                tomatoWithSub.tomato.let { tomato ->
                                                    if (runningScheduleWithSub.schedule.title.isEmpty()) {
                                                        it.tv_lock_view_name.text = "定时锁机【🍅${tomato.title}】"
                                                    } else {
                                                        it.tv_lock_view_name.text = "定时锁机『${runningScheduleWithSub.schedule.title}』·【🍅${tomato.title}】"
                                                    }
                                                }
                                            }
                                        }
                                    }
                                } else if (result == WorkState.RESTING) {
                                    restFloatWindow.refreshText("休息中 ${formatHHMMSS(restLeftTime / 1000)}")
                                }
                            }
                        }
                    } else {
                        result = WorkState.ENDING
                    }

                } else {
                    timeLen = 0
                    timeLen =
                        if (runningScheduleWithSub.schedule.startHour * 60 + runningScheduleWithSub.schedule.startMinute < runningScheduleWithSub.schedule.endHour * 60 + runningScheduleWithSub.schedule.endMinute) {
                            //起始时间小于结束时间
                            runningScheduleWithSub.schedule.endHour * 60 + runningScheduleWithSub.schedule.endMinute - runningScheduleWithSub.schedule.startHour * 60 - runningScheduleWithSub.schedule.startMinute
                        } else {
                            runningScheduleWithSub.schedule.endHour * 60 + runningScheduleWithSub.schedule.endMinute + 24 * 60 - runningScheduleWithSub.schedule.startHour * 60 - runningScheduleWithSub.schedule.startMinute
                        }

                    val pastTime = System.currentTimeMillis() - runningLockHistory.startTime
                    leftTime = if (pastTime >= 0) {
                        runningLockHistory.timeLength - pastTime
                    } else {
                        //防止用户调整系统时间，导致当前时间小于起始时间，从而timeLength过大
                        0
                    }
                    if (leftTime > 0) {
                        if (isScreenOn()) {
                            withContext(Dispatchers.Main) {
                                if (lockFloatWindow.isShowing()) {
                                    lockFloatWindow.containerLayout?.let {
                                        it.tv_lock_view_time_left.text = formatHHMMSS(leftTime / 1000)
                                        it.tv_lock_view_next.text = "结束"
                                        it.cpv_lock_view.setProgress((System.currentTimeMillis() - runningLockHistory.startTime) * 100 / runningLockHistory.timeLength.toFloat())
                                        refreshSameView()

                                        if (runningScheduleWithSub.schedule.title.isEmpty()) {
                                            it.tv_lock_view_name.text = "定时锁机"
                                        } else {
                                            it.tv_lock_view_name.text = "定时锁机『${runningScheduleWithSub.schedule.title}』"
                                        }

                                    }
                                }
                            }
                        }
                    } else {
                        result = WorkState.ENDING
                    }
                }
            }
        }
        return result
    }


    private var lastCurrentTopList = listOf<WhitePkgInfo>()
    private fun isWhiteApp(): Pair<CurrentPageType, String> {
        //锁机暂停
        if (MMKVUtils.getLong(MyConstants.SP_KEY_FORCE_UNLOCK_PAUSE, 0L) > System.currentTimeMillis()) {
            return Pair(CurrentPageType.SYSTEM_WHITE_PAGE, "")
        }

        //华为无需无障碍就能判断小窗app,oppo在performHome后一直能检测到siderbar正在显示，原因未知
        val currentTopList = if (MyRomUtils.isXiaomi()) {
            UsageUtil.getTopPkgInfoList(applicationContext, useAccessibility = true, retainChoosePage = true, useLogSecond = 1)
        } else {
            UsageUtil.getTopPkgInfoList(applicationContext, useAccessibility = false, retainChoosePage = true)
        }


        if (lastCurrentTopList != currentTopList) {
            lastCurrentTopList = currentTopList
            currentTopList.forEachIndexed { index, it ->
                Log.d("BackgroundService:", "currentTop[${index}]:$it")

                LiveEventBus.get(LiveBus.DEBUG_MSG, String::class.java).post("currentTop[${index}] ${it.pkgName}\n                ${it.activityName}")

            }
        }

        val isUserBlackPageResult = isUserBlackPage(currentTopList)
        if (isUserBlackPageResult.first) {
            currentDenyAppInfo = null
//            lockFloatWindow.shouldShowDeny = true
            return Pair(CurrentPageType.USER_BLACK_PAGE, isUserBlackPageResult.second)
        }


        if (isSystemBlackAppIgnoreWhite(currentTopList)) {
            currentDenyAppInfo = null
//            lockFloatWindow.shouldShowDeny = true
//            performHome(this, "SystemBlackAppIgnoreWhite")
            return Pair(CurrentPageType.SYSTEM_BLACK_IGNORE_WHITE_PAGE, "")
        }


        val deleteWhiteAppTempList = MyGsonUtil.jsonToDeleteWhiteAppTempList(runningLockHistory.deleteWhiteAppTemp)

        val isWhiteAppResult = isSystemOrUserWhiteApp(currentTopList, deleteWhiteAppTempList)

        if (isWhiteAppResult.first) {
            //用户白名单
            currentDenyAppInfo = null
//            lockFloatWindow.shouldShowDeny = false
            return Pair(CurrentPageType.USER_WHITE_APP, "")
        } else {
            //非白名单
            currentDenyAppInfo = isWhiteAppResult.second
//            lockFloatWindow.shouldShowDeny = true
        }


        if (isSystemBlackApp(currentTopList)) {
            currentDenyAppInfo = null
//            lockFloatWindow.shouldShowDeny = true
//            performHome(this, "SystemBlackApp")
            return Pair(CurrentPageType.SYSTEM_BLACK_PAGE, "")
        }

        //这里不用处理，否则在桌面的时候也会不断触发home按键
//        performHome(this, "UNKNOWN_APP")
        return Pair(CurrentPageType.UNKNOWN_APP, "UNKNOWN_APP")


    }


    private suspend fun checkLockHistory(): Boolean {
//        Log.d("CheckService:", "checkLockHistory")
        lockHistory?.let {
            if (!it.isFinish) {
                runningLockHistory = it
                globalLockConfig = lockViewModel.globalLockConfig(runningLockHistory)
                globalWhiteAppList = lockViewModel.getGlobalWhiteAppList()
                lockFloatWindow.runningLockHistory = runningLockHistory
                when (runningLockHistory.lockType) {
                    1 -> {
                        runningSimpleLength = runningLockHistory.simpleLockLength
//                        runningLockConfig.copyFrom(globalLockConfig)
                        runningWhiteAppList = globalWhiteAppList
                        runningLockConfig = globalLockConfig
//                        runningWhiteAppList.clear()
//                        runningWhiteAppList.addAll(globalWhiteAppList)
                        lockFloatWindow.runningLockConfig = runningLockConfig
                        lockFloatWindow.runningWhiteAppList = runningWhiteAppList
                    }

                    2 -> {
                        runningTomatoWithSub = lockViewModel.getTomatoWithSub(runningLockHistory.tomatoIndexId)!!
//                        LogUtils.d(runningTomatoWithSub.tomato.lockConfig.toString())
                        runningLockConfig = LockConfig()
                        if (runningTomatoWithSub.tomato.lockConfig.isSilentFollowGlobal) {
                            runningLockConfig.isSilent = globalLockConfig.isSilent
                        } else {
                            runningLockConfig.isSilent = runningTomatoWithSub.tomato.lockConfig.isSilent
                        }

                        if (runningTomatoWithSub.tomato.lockConfig.bgUrlFollowGlobal) {
                            runningLockConfig.bgUrl = globalLockConfig.bgUrl
                        } else {
                            runningLockConfig.bgUrl = runningTomatoWithSub.tomato.lockConfig.bgUrl
                        }

                        if (runningTomatoWithSub.tomato.lockConfig.isRemoveNotificationFollowGlobal) {
                            runningLockConfig.isRemoveNotification = globalLockConfig.isRemoveNotification
                        } else {
                            runningLockConfig.isRemoveNotification = runningTomatoWithSub.tomato.lockConfig.isRemoveNotification
                        }

                        if (runningTomatoWithSub.tomato.lockConfig.startVoiceNotifyFollowGlobal) {
                            runningLockConfig.startVoiceNotify = globalLockConfig.startVoiceNotify
                        } else {
                            runningLockConfig.startVoiceNotify = runningTomatoWithSub.tomato.lockConfig.startVoiceNotify
                        }

                        if (runningTomatoWithSub.tomato.lockConfig.endVoiceNotifyFollowGlobal) {
                            runningLockConfig.endVoiceNotify = globalLockConfig.endVoiceNotify
                        } else {
                            runningLockConfig.endVoiceNotify = runningTomatoWithSub.tomato.lockConfig.endVoiceNotify
                        }

                        if (runningTomatoWithSub.tomato.lockConfig.startShakeNotifyFollowGlobal) {
                            runningLockConfig.startShakeNotify = globalLockConfig.startShakeNotify
                        } else {
                            runningLockConfig.startShakeNotify = runningTomatoWithSub.tomato.lockConfig.startShakeNotify
                        }

                        if (runningTomatoWithSub.tomato.lockConfig.endShakeNotifyFollowGlobal) {
                            runningLockConfig.endShakeNotify = globalLockConfig.endShakeNotify
                        } else {
                            runningLockConfig.endShakeNotify = runningTomatoWithSub.tomato.lockConfig.endShakeNotify
                        }

                        if (runningTomatoWithSub.tomato.lockConfig.whiteFollowGlobal) {
                            runningWhiteAppList = globalWhiteAppList
//                            runningWhiteAppList.clear()
//                            runningWhiteAppList.addAll(globalWhiteAppList)
                        } else {
                            runningWhiteAppList = runningTomatoWithSub.whiteList
//                            runningWhiteAppList.clear()
//                            runningWhiteAppList.addAll(runningTomatoWithSub.whiteList)
                        }

                        lockFloatWindow.runningLockConfig = runningLockConfig
                        lockFloatWindow.runningWhiteAppList = runningWhiteAppList
//                        LogUtils.d("!!!!!!!!!!!!!set"+runningLockConfig.toString())
                    }

                    3 -> {
                        runningScheduleWithSub = lockViewModel.getScheduleWithSub(runningLockHistory.scheduleIndexId)!!
                        runningLockConfig = LockConfig()
                        if (runningScheduleWithSub.schedule.useTomato) {
                            if (runningScheduleWithSub.tomatoWithSub!!.tomato.lockConfig.isSilentFollowGlobal) {
                                runningLockConfig.isSilent = globalLockConfig.isSilent
                            } else {
                                runningLockConfig.isSilent = runningScheduleWithSub.tomatoWithSub!!.tomato.lockConfig.isSilent
                            }
                        } else {
                            if (runningScheduleWithSub.schedule.lockConfig.isSilentFollowGlobal) {
                                runningLockConfig.isSilent = globalLockConfig.isSilent
                            } else {
                                runningLockConfig.isSilent = runningScheduleWithSub.schedule.lockConfig.isSilent
                            }
                        }

                        if (runningScheduleWithSub.schedule.useTomato) {
                            if (runningScheduleWithSub.tomatoWithSub!!.tomato.lockConfig.bgUrlFollowGlobal) {
                                runningLockConfig.bgUrl = globalLockConfig.bgUrl
                            } else {
                                runningLockConfig.bgUrl = runningScheduleWithSub.tomatoWithSub!!.tomato.lockConfig.bgUrl
                            }
                        } else {
                            if (runningScheduleWithSub.schedule.lockConfig.bgUrlFollowGlobal) {
                                runningLockConfig.bgUrl = globalLockConfig.bgUrl
                            } else {
                                runningLockConfig.bgUrl = runningScheduleWithSub.schedule.lockConfig.bgUrl
                            }
                        }

                        if (runningScheduleWithSub.schedule.useTomato) {
                            if (runningScheduleWithSub.tomatoWithSub!!.tomato.lockConfig.isRemoveNotificationFollowGlobal) {
                                runningLockConfig.isRemoveNotification = globalLockConfig.isRemoveNotification
                            } else {
                                runningLockConfig.isRemoveNotification = runningScheduleWithSub.tomatoWithSub!!.tomato.lockConfig.isRemoveNotification
                            }
                        } else {
                            if (runningScheduleWithSub.schedule.lockConfig.isRemoveNotificationFollowGlobal) {
                                runningLockConfig.isRemoveNotification = globalLockConfig.isRemoveNotification
                            } else {
                                runningLockConfig.isRemoveNotification = runningScheduleWithSub.schedule.lockConfig.isRemoveNotification
                            }
                        }

                        if (runningScheduleWithSub.schedule.useTomato) {
                            if (runningScheduleWithSub.tomatoWithSub!!.tomato.lockConfig.startVoiceNotifyFollowGlobal) {
                                runningLockConfig.startVoiceNotify = globalLockConfig.startVoiceNotify
                            } else {
                                runningLockConfig.startVoiceNotify = runningScheduleWithSub.tomatoWithSub!!.tomato.lockConfig.startVoiceNotify
                            }
                        } else {
                            if (runningScheduleWithSub.schedule.lockConfig.startVoiceNotifyFollowGlobal) {
                                runningLockConfig.startVoiceNotify = globalLockConfig.startVoiceNotify
                            } else {
                                runningLockConfig.startVoiceNotify = runningScheduleWithSub.schedule.lockConfig.startVoiceNotify
                            }
                        }

                        if (runningScheduleWithSub.schedule.useTomato) {
                            if (runningScheduleWithSub.tomatoWithSub!!.tomato.lockConfig.endVoiceNotifyFollowGlobal) {
                                runningLockConfig.endVoiceNotify = globalLockConfig.endVoiceNotify
                            } else {
                                runningLockConfig.endVoiceNotify = runningScheduleWithSub.tomatoWithSub!!.tomato.lockConfig.endVoiceNotify
                            }
                        } else {
                            if (runningScheduleWithSub.schedule.lockConfig.endVoiceNotifyFollowGlobal) {
                                runningLockConfig.endVoiceNotify = globalLockConfig.endVoiceNotify
                            } else {
                                runningLockConfig.endVoiceNotify = runningScheduleWithSub.schedule.lockConfig.endVoiceNotify
                            }
                        }

                        if (runningScheduleWithSub.schedule.useTomato) {
                            if (runningScheduleWithSub.tomatoWithSub!!.tomato.lockConfig.startShakeNotifyFollowGlobal) {
                                runningLockConfig.startShakeNotify = globalLockConfig.startShakeNotify
                            } else {
                                runningLockConfig.startShakeNotify = runningScheduleWithSub.tomatoWithSub!!.tomato.lockConfig.startShakeNotify
                            }
                        } else {
                            if (runningScheduleWithSub.schedule.lockConfig.startShakeNotifyFollowGlobal) {
                                runningLockConfig.startShakeNotify = globalLockConfig.startShakeNotify
                            } else {
                                runningLockConfig.startShakeNotify = runningScheduleWithSub.schedule.lockConfig.startShakeNotify
                            }
                        }

                        if (runningScheduleWithSub.schedule.useTomato) {
                            if (runningScheduleWithSub.tomatoWithSub!!.tomato.lockConfig.endShakeNotifyFollowGlobal) {
                                runningLockConfig.endShakeNotify = globalLockConfig.endShakeNotify
                            } else {
                                runningLockConfig.endShakeNotify = runningScheduleWithSub.tomatoWithSub!!.tomato.lockConfig.endShakeNotify
                            }
                        } else {
                            if (runningScheduleWithSub.schedule.lockConfig.endShakeNotifyFollowGlobal) {
                                runningLockConfig.endShakeNotify = globalLockConfig.endShakeNotify
                            } else {
                                runningLockConfig.endShakeNotify = runningScheduleWithSub.schedule.lockConfig.endShakeNotify
                            }
                        }

                        if (runningScheduleWithSub.schedule.useTomato) {
                            if (runningScheduleWithSub.tomatoWithSub!!.tomato.lockConfig.whiteFollowGlobal) {
                                runningWhiteAppList = globalWhiteAppList
//                                runningWhiteAppList.clear()
//                                runningWhiteAppList.addAll(globalWhiteAppList)
                            } else {
                                runningWhiteAppList = runningScheduleWithSub.tomatoWithSub!!.whiteList
//                                runningWhiteAppList.clear()
//                                runningWhiteAppList.addAll(runningScheduleWithSub.tomatoWithSub!!.whiteList)
                            }
                        } else {
                            if (runningScheduleWithSub.schedule.lockConfig.whiteFollowGlobal) {
                                runningWhiteAppList = globalWhiteAppList
//                                runningWhiteAppList.clear()
//                                runningWhiteAppList.addAll(globalWhiteAppList)
                            } else {
                                runningWhiteAppList = runningScheduleWithSub.whiteList
//                                runningWhiteAppList.clear()
//                                runningWhiteAppList.addAll(runningScheduleWithSub.whiteList)
                            }
                        }

                        lockFloatWindow.runningLockConfig = runningLockConfig
                        lockFloatWindow.runningWhiteAppList = runningWhiteAppList

                    }
                }

                TheApplication.getInstance().globalParams.currentRunningLockWorkInfo = lockHistory.toString()
                return true
            }
        }


        return false
    }

    private suspend fun checkMonitor() {
        //强制解锁1分钟内不再监督锁机
        val lastLockTime = MMKVUtils.getLong(MyConstants.SP_KEY_FORCE_UNLOCK_TIME_LAST, 0)
        val now = System.currentTimeMillis()
        val delta = 60 * 1000 //1分钟
        if (now > lastLockTime - delta && now < lastLockTime + delta) return

        if (!PermissionUtil.hasMustPermission(this)) return
        monitorViewModel.getTodayLimitsImmediately(MyDateUtil.getTodayName())?.let {
//            LogUtils.d("checkMonitor getTodayLimitsImmediately $it")

            if (it.allDayLimit != -1L && it.jumpDate != MyUtil.getTodayCalendarString()) {

                var usedLength = if (it.isIncludeWhite) {
                    monitorViewModel.getDayUsageLength()
                } else {
                    val globalWhiteAppList = lockViewModel.getGlobalWhiteAppList()
                    monitorViewModel.getDayUsageLengthWithoutWhite(globalWhiteAppList)
                }

                val currentMills = System.currentTimeMillis()
                val calendar = Calendar.getInstance()
                val todayGoneTime =
                    (calendar.get(Calendar.HOUR_OF_DAY) * 60 * 60 * 1000 + calendar.get(Calendar.MINUTE) * 60 * 1000 + calendar.get(Calendar.SECOND) * 1000 + calendar.get(
                        Calendar.MILLISECOND
                    )).toLong()

                if (usedLength * 1000 > todayGoneTime) usedLength = todayGoneTime / 1000

                val leftLength = 24 * 60 * 60 * 1000 - todayGoneTime + 1000 * 59//加60留出一分钟，第二天结束，防止第一天结束后又锁住

                //提醒
                val scheduleNotify = MMKVUtils.getInt(MyConstants.SP_KEY_SCHEDULE_NOTIFY, 5)
                if (scheduleNotify > 0) {
//                    LogUtils.d("!!!!!!!!!" + scheduleNotify)
//                    LogUtils.d("!!!!!!!!!" + (it.allDayLimit - usedLength))
                    val realScheduleNotify = MyUtil.getScheduleNotifyLength(scheduleNotify)
                    if (it.allDayLimit - usedLength - realScheduleNotify in IntRange(0, 60)) {
                        withContext(Dispatchers.Main) {
                            if (usedLength != lastLeftMinute) {
                                dayLimitNotifyWindow.showAndHide(
                                    "${MyUtil.getScheduleNotifyLengthString(scheduleNotify)}后监督锁机",
                                    "即将到达手机当日使用上限"
                                )

                            }
                        }
                    }
                    lastLeftMinute = usedLength

                }

                if (usedLength >= it.allDayLimit) {
                    val lockHistory = LockHistory()

                    lockHistory.title = "监督：今日使用时长已用完，进入锁机模式"
                    lockHistory.content = "usedLength:$usedLength, allDayLimit:${it.allDayLimit}"
                    lockHistory.lockType = 1
                    lockHistory.startTime = currentMills
                    lockHistory.timeLength = leftLength

                    lockHistory.trueStartTime = lockHistory.startTime
                    lockHistory.trueTimeLength = lockHistory.timeLength

                    lockViewModel.createLockHistory(lockHistory)
//                    TheApplication.getInstance().globalParams.runState = RunState.UNKNOWN//-1
                } else {
                    initNotification(
                        "屏幕使用时间监督",
                        "今天已使用${TimeUtil.formatHHMMSimple((usedLength / 60).toInt())}，剩余${TimeUtil.formatHHMMSimple((it.allDayLimit / 60 - usedLength / 60).toInt())}"
                    )
//                    initNotification(AppUtils.getAppName(), "监督：今日剩余可用时长 " + TimeUtil.formatHHMM((it.allDayLimit / 60 - usedLength / 60).toInt()))
                }
            } else {
                refreshScreenUsedTime(it)
            }
        }
    }

    private suspend fun checkSchedule(): Boolean {
        //强制解锁1分钟内不再定时锁机
        val lastLockTime = MMKVUtils.getLong(MyConstants.SP_KEY_FORCE_UNLOCK_TIME_LAST, 0)
        val now = System.currentTimeMillis()
        val delta = 60 * 1000 //1分钟
        if (now > lastLockTime - delta && now < lastLockTime + delta) return false

        if (!PermissionUtil.hasMustPermission(this@BackgroundService)) return false
//        LogUtils.d("checkSchedule")
        var nn = 0

        schedulesWithSub.forEachIndexed { index, scheduleWithSub ->

            if (scheduleWithSub.schedule.validate && scheduleWithSub.schedule.jumpDate != MyUtil.getTodayCalendarString()) {
                var timeLen = 0
                var str = "定时锁机"
                if (scheduleWithSub.schedule.title.isNotEmpty()) {
                    str += "『${scheduleWithSub.schedule.title}』"
                }

                if (scheduleWithSub.schedule.useTomato) {
                    if (scheduleWithSub.schedule.isRecycle) {
                        nn = 7
                    } else {
                        nn = 1
                    }
                    if (scheduleWithSub.tomatoWithSub != null) {
                        for (i in 1..scheduleWithSub.tomatoWithSub!!.tomato.tomatoCount) {
                            timeLen += scheduleWithSub.tomatoWithSub!!.tomato.tomatoWorkLength
                            if (i < scheduleWithSub.tomatoWithSub!!.tomato.tomatoCount) {
                                if (scheduleWithSub.tomatoWithSub!!.tomato.tomatoLongRestPerCount > 0 && (i % scheduleWithSub.tomatoWithSub!!.tomato.tomatoLongRestPerCount == 0)) {
                                    timeLen += scheduleWithSub.tomatoWithSub!!.tomato.tomatoLongRestLength
                                } else {
                                    timeLen += scheduleWithSub.tomatoWithSub!!.tomato.tomatoRestLength
                                }
                            }
                        }

                        str += "：${
                            TimeUtil.formatHHMM(
                                scheduleWithSub.schedule.startHour, scheduleWithSub.schedule.startMinute
                            )
                        } — 执行【🍅${scheduleWithSub.tomatoWithSub!!.tomato.title}】"
                    }
                } else {
                    if (scheduleWithSub.schedule.startHour * 60 + scheduleWithSub.schedule.startMinute < scheduleWithSub.schedule.endHour * 60 + scheduleWithSub.schedule.endMinute) {
                        //起始时间小于结束时间
                        nn = 1
                        timeLen =
                            scheduleWithSub.schedule.endHour * 60 + scheduleWithSub.schedule.endMinute - scheduleWithSub.schedule.startHour * 60 - scheduleWithSub.schedule.startMinute
                        str += "：${TimeUtil.formatHHMM(scheduleWithSub.schedule.startHour, scheduleWithSub.schedule.startMinute)} — ${
                            TimeUtil.formatHHMM(
                                scheduleWithSub.schedule.endHour, scheduleWithSub.schedule.endMinute
                            )
                        }"
                    } else {
                        nn = 2
                        timeLen =
                            scheduleWithSub.schedule.endHour * 60 + scheduleWithSub.schedule.endMinute + 24 * 60 - scheduleWithSub.schedule.startHour * 60 - scheduleWithSub.schedule.startMinute
                        str += "：${TimeUtil.formatHHMM(scheduleWithSub.schedule.startHour, scheduleWithSub.schedule.startMinute)} — 次日 ${
                            TimeUtil.formatHHMM(
                                scheduleWithSub.schedule.endHour, scheduleWithSub.schedule.endMinute
                            )
                        }"
                    }

                }
                //秒为单位
                val startTime = scheduleWithSub.schedule.startHour * 60 * 60 + scheduleWithSub.schedule.startMinute * 60

                val c = Calendar.getInstance()
                val hour = c.get(Calendar.HOUR_OF_DAY)
                val minute = c.get(Calendar.MINUTE)
                val second = c.get(Calendar.SECOND)

                val current = hour * 60 * 60 + minute * 60 + second
                val currentWithMill = current * 1000 + c.get(Calendar.MILLISECOND)
                val currentMill = System.currentTimeMillis()

                for (n in 0 until nn) {
                    val date = Calendar.getInstance()
                    date.set(Calendar.DATE, date.get(Calendar.DATE) - n)

                    if (weekValid(date.get(Calendar.DAY_OF_WEEK), scheduleWithSub.schedule) || (!scheduleWithSub.schedule.isRecycle)) {

//                        LogUtils.d("date.get(Calendar.DAY_OF_WEEK)=${date.get(Calendar.DAY_OF_WEEK)} scheduleWithSub.schedule=${scheduleWithSub.schedule} weekValid=${weekValid(date.get(Calendar.DAY_OF_WEEK), scheduleWithSub.schedule)} scheduleWithSub.schedule.isRecycle=${scheduleWithSub.schedule.isRecycle}")
                        val scheduleNotify = MMKVUtils.getInt(MyConstants.SP_KEY_SCHEDULE_NOTIFY, 5)
                        if (scheduleNotify > 0) {
                            if (startTime + 24 * 60 * 60 * n - current - MyUtil.getScheduleNotifyLength(scheduleNotify) in IntRange(0, 60)) {
//                                LogUtils.d("startTime=${startTime} current=${current} scheduleNotify=${MyUtil.getScheduleNotifyLength(scheduleNotify)}")
                                withContext(Dispatchers.Main) {
                                    scheduleNotifyWindow.showAndHide("${MyUtil.getScheduleNotifyLengthString(scheduleNotify)}后定时锁机", str)
                                }
                            }

                        }
                        //24 * 60 * 60 * n为跨天时间差
                        if ((current - startTime + 24 * 60 * 60 * n < timeLen * 60) && (current - startTime + 24 * 60 * 60 * n >= 0)) {
                            val lockHistory = LockHistory()

                            lockHistory.scheduleIndexId = scheduleWithSub.schedule.scheduleIndexId
                            lockHistory.title = str
                            lockHistory.lockType = 3

//                            if (ScreenUtils.isScreenLock()) {
                            lockHistory.startTime = (startTime - 24 * 60 * 60 * n) * 1000.toLong() + currentMill - currentWithMill
                            lockHistory.timeLength = (timeLen * 60 * 1000).toLong()
//                            } else {
//                                lockHistory.startTime = currentMill
//                                lockHistory.timeLength = (timeLen * 60 * 1000).toLong() + (startTime - 24 * 60 * 60 * n) * 1000.toLong() - current * 1000.toLong()
//                            }


//                            if (ScreenUtils.isScreenLock()) {
//                                lockHistory.trueStartTime = lockHistory.startTime
//                                lockHistory.trueTimeLength = lockHistory.timeLength
//                            } else {
                            lockHistory.trueStartTime = currentMill
                            lockHistory.trueTimeLength = lockHistory.timeLength - (currentMill - lockHistory.startTime)
//                            }

                            lockViewModel.createLockHistory(lockHistory)
//                            TheApplication.getInstance().globalParams.runState = RunState.UNKNOWN//-1
                            return true
                        }
                    }
                }
            }
        }
        return false
    }


    //强制退出
    private fun forceUnlock(forceUnlockType: Int) {
        val current = System.currentTimeMillis()
//        LogUtils.d("aaaaaaaaaaaaaaaaaaaaaaaaa" + forceUnlockType)

        MMKVUtils.put(MyConstants.SP_KEY_FORCE_UNLOCK_TIME_LAST, System.currentTimeMillis())

        GlobalScope.launch(Dispatchers.Main) {
            lockFloatWindow.refreshUnlockRadioButton()
        }

        val dayLimit = monitorViewModel.getTodayLimitsImmediately(MyDateUtil.getTodayName())
        when (forceUnlockType) {
            1 -> {
                MMKVUtils.put(MyConstants.SP_KEY_FORCE_UNLOCK_PAUSE, current + 5 * 60 * 1000)
            }

            2 -> {
                MMKVUtils.put(MyConstants.SP_KEY_FORCE_UNLOCK_PAUSE, current + 15 * 60 * 1000)
            }

            3 -> {
                MMKVUtils.put(MyConstants.SP_KEY_FORCE_UNLOCK_PAUSE, current + 30 * 60 * 1000)
            }

            4 -> {
                MMKVUtils.put(MyConstants.SP_KEY_FORCE_UNLOCK_PAUSE, current + 60 * 60 * 1000)
            }

            5 -> {
                forceUnlockSub()
            }

            6 -> {
                if (runningLockHistory.lockType == 3) {
                    runningScheduleWithSub.schedule.jumpDate = MyUtil.getTodayCalendarString()
                    lockViewModel.updateSchedule(runningScheduleWithSub.schedule)
                }
                forceUnlockSub()
            }

            7 -> {
                if (runningLockHistory.lockType == 3) {
                    runningScheduleWithSub.schedule.validate = false
                    lockViewModel.updateSchedule(runningScheduleWithSub.schedule)
                }
                forceUnlockSub()
            }

            8 -> {
                //监督锁机
                if (runningLockHistory.lockType == 1 && runningLockHistory.simpleLockLength == -1) {
                    dayLimit?.let {
                        it.jumpDate = MyUtil.getTodayCalendarString()
                        monitorViewModel.updateDayLimit(it)
                    }
                }
                forceUnlockSub()
            }

            9 -> {
                //监督锁机
                if (runningLockHistory.lockType == 1 && runningLockHistory.simpleLockLength == -1) {
                    dayLimit?.let {
                        it.allDayLimit = -1//23 * 60 * 60 + 59 * 60
                        monitorViewModel.updateDayLimit(it)
                    }
                }
                forceUnlockSub()
            }

            else -> {//用于开发者解锁
                if (runningLockHistory.lockType == 1 && runningLockHistory.simpleLockLength == -1) {
                    dayLimit?.let {
                        it.allDayLimit = -1//23 * 60 * 60 + 59 * 60
                        monitorViewModel.updateDayLimit(it)
                    }
                } else if (runningLockHistory.lockType == 3) {
                    runningScheduleWithSub.schedule.validate = false
                    lockViewModel.updateSchedule(runningScheduleWithSub.schedule)
                }
                forceUnlockSub()
            }
        }
    }

    private fun forceUnlockSub() {
        runningLockHistory.timeLength = 0
        runningLockHistory.trueTimeLength = System.currentTimeMillis() - runningLockHistory.trueStartTime
        runningLockHistory.isForceQuit = true

        LogUtils.d("forceUnlockSub")
    }


    private fun startNotify() {
        val i = runningLockConfig.startVoiceNotify
        if (i > 0) {
            notifyPlayerList[i - 1].start()
        }
    }

    private fun startShake() {
        val i = runningLockConfig.startShakeNotify
        if (i > 0) {
            val intent = Intent(application, VibratorActivity::class.java)
            intent.putExtra("len", i)
            intent.addFlags(Intent.FLAG_ACTIVITY_NEW_TASK)
            application.startActivity(intent)
        }
    }

    private fun endNotify() {
        val i = runningLockConfig.endVoiceNotify
        if (i > 0) {
            notifyPlayerList[i - 1].start()
        }
    }

    private fun endShake() {
        val i = runningLockConfig.endShakeNotify
        if (i > 0) {
            val intent = Intent(application, VibratorActivity::class.java)
            intent.putExtra("len", i)
            intent.addFlags(Intent.FLAG_ACTIVITY_NEW_TASK)
            application.startActivity(intent)
        }
    }


    internal inner class ScheduleWithSubObserver : Observer<MutableList<ScheduleWithSub>> {
        override fun onChanged(value: MutableList<ScheduleWithSub>) {
            schedulesWithSub = value
//            index = max
            TheApplication.getInstance().globalParams.lastMinute = -1
//            LogUtils.d("ScheduleWithSubObserver")
        }
    }

    internal inner class LockHistoryObserver : Observer<LockHistory?> {
        override fun onChanged(t: LockHistory?) {
            LogUtils.d("LockHistory = ${t.toString()}")
            if (TheApplication.getInstance().globalParams.runState == RunState.LOCKING) {
                return
            }
            lockHistory = t
//            runState = RUN_STATE_CHECK_HISTORY
//            }
//            index = max

            GlobalScope.launch(Dispatchers.IO) {
                if (checkLockHistory()) {
                    if (TheApplication.getInstance().globalParams.runState != RunState.LOCKING) {
                        TheApplication.getInstance().globalParams.runState = RunState.LOCKING
//                        ifShouldShowDenyWindow = MMKVUtils.getBoolean(MyConstants.SP_SETTING_DENY_DROPDOWN, false)
                        TheApplication.getInstance().globalParams.isRemoveNotify = runningLockConfig.isRemoveNotification
//                        TheApplication.getInstance().globalParams.isDenyDropDown = MMKVUtils.getBoolean(MyConstants.SP_SETTING_DENY_DROPDOWN, false)

                        if (MMKVUtils.getBoolean(MyConstants.SP_KEY_PERFORM_HOME_WHEN_LOCK_START, true)) {
                            performHome(this@BackgroundService, "开始锁机")//防止锁机时正在使用白名单软件
                        }

                        //ljqljqljq 锁机开始时先不杀死页面了，而是放入后台
                        if (!MMKVUtils.getBoolean(MyConstants.SP_KEY_KEEP_FOREGROUND_ACTIVITY, false)) {
                            LiveEventBus.get(LiveBus.CLOSE_MAIN_ACTIVITY, String::class.java).post("")
                        }

                        initNotification(AppUtils.getAppName(), runningLockHistory.title)

                        try {
                            MyRetrofitClient.service.syncOnline()
                        } catch (e: Exception) {

                        }
                    }
                } else {
                    TheApplication.getInstance().globalParams.runState = RunState.CHECK_SCHEDULE
                    TheApplication.getInstance().globalParams.lastMinute = -1
                }
            }
        }
    }


    internal inner class InnerReceiver : BroadcastReceiver() {
        private val SYSTEM_DIALOG_REASON_KEY = "reason"
        private val SYSTEM_DIALOG_REASON_RECENT_APPS = "recentapps"
        private val SYSTEM_DIALOG_REASON_HOME_KEY = "homekey"
        private val SYSTEM_DIALOG_REASON_GESTURE_KEY = "fs_gesture" //小米（moto可能有）独有，上划发送home和它，上划悬停发送它，不发送recentapp
        private val SYSTEM_DIALOG_REASON_ASSIST = "assist"
        override fun onReceive(context: Context, intent: Intent) {
//            LogUtils.d("!!!!!!!" + intent.action + " " + intent.getStringExtra(SYSTEM_DIALOG_REASON_KEY))
            when (intent.action) {
                Intent.ACTION_CLOSE_SYSTEM_DIALOGS -> {
                    if (TheApplication.getInstance().globalParams.runState == RunState.LOCKING
                        && TheApplication.getInstance().globalParams.workState == WorkState.WORKING
                        && MMKVUtils.getLong(MyConstants.SP_KEY_FORCE_UNLOCK_PAUSE, 0L) <= System.currentTimeMillis()
                    ) {
                        when (intent.getStringExtra(SYSTEM_DIALOG_REASON_KEY)) {
                            SYSTEM_DIALOG_REASON_RECENT_APPS, SYSTEM_DIALOG_REASON_GESTURE_KEY -> {
                                if ("redmi" != Build.BRAND.toLowerCase()) {
                                    if (!MyUtil.isHarmonyOS() && !RomUtils.isMotorola()) { //鸿蒙2.0如果加上下面的，会弹回白名单页面，因为鸿蒙home发的也是recentapp广播
                                        performHome(this@BackgroundService, "ACTION_CLOSE_SYSTEM_DIALOGS")
                                    }

                                    lockFloatWindow.showLockWindow(1000)
                                }
                            }
//                            SYSTEM_DIALOG_REASON_GESTURE_KEY -> {
//                                if (!RomUtils.isMotorola()) { //
//                                    performHome(this@BackgroundService, "5")
//                                }
////                                if (!PermissionUtil.isAccessibilitySettingsOn(MyConstants.ACCESS_ABILITY_NAME, applicationContext)) {
//
//                                GlobalScope.launch(Dispatchers.Main) {
//                                    lockFloatWindow.showLockWindow(1000)
//                                    TheApplication.getInstance().globalParams.lastClickHomeOrRecent = System.currentTimeMillis()
//                                }
//
////                                }
//                            }
                            SYSTEM_DIALOG_REASON_HOME_KEY -> {
//                                GlobalScope.launch(Dispatchers.Main) {
                                lockFloatWindow.showLockWindow(1000)
                                TheApplication.getInstance().globalParams.lastClickHomeTime = System.currentTimeMillis()
//                                TheApplication.getInstance().globalParams.lastClickHomeOrRecent = System.currentTimeMillis()
//                                }
                            }

                            SYSTEM_DIALOG_REASON_ASSIST -> {
                                performHome(this@BackgroundService, "SYSTEM_DIALOG_REASON_ASSIST")
//                                if (!PermissionUtil.isAccessibilitySettingsOn(MyConstants.ACCESS_ABILITY_NAME, applicationContext)) {
//                                GlobalScope.launch(Dispatchers.Main) {
                                lockFloatWindow.showLockWindow(1000)
//                                TheApplication.getInstance().globalParams.lastClickHomeOrRecent = System.currentTimeMillis()
//                                }
//                                }
                            }
                        }
                    }
                }

                Intent.ACTION_TIME_TICK -> {
//                    index = max
//                    isScreenOn = isScreenOn()
//                    isScreenOn = !ScreenUtils.isScreenLock()
//                    LogUtils.d("aaaaaaaaaaaaaaaa isScreenOn=${isScreenOn} isLockRunning()=${isLockRunning()} hasOpenMinute=${hasOpenMinute}")
                    if (isScreenOn() && !isLockRunning() && MMKVUtils.getBoolean(MyConstants.SP_KEY_LIMIT_SETTING_TIRED_NOTIFY, false)) {
                        hasOpenMinute += 1
                        val tiredNotifyLength = MMKVUtils.getInt(MyConstants.SP_KEY_LIMIT_SETTING_TIRED_NOTIFY_LENGTH, 30)
                        val ifTiredNeedLock = MMKVUtils.getBoolean(MyConstants.SP_KEY_LIMIT_SETTING_TIRED_AUTO_LOCK, false)
                        val autoLockLength = MMKVUtils.getInt(MyConstants.SP_KEY_LIMIT_SETTING_TIRED_AUTO_LOCK_LENGTH, 1)
                        //疲劳提醒
//                        if (ifTiredNeedLock && !isLockRunning() && hasOpenMinute == tiredNotifyLength - 1) {
//                            //疲劳锁机前一分钟提醒
//                            tiredNotifyWindow.showAndHide("一分钟后将触发疲劳锁机${TimeUtil.formatHHMMSimple(autoLockLength)}")
//                        } else

                        if (!isLockRunning() && hasOpenMinute >= tiredNotifyLength) {
//                            var showContent = ""
//                            if (ifTiredLock && !isLockRunning()) {
//                                showContent = "即将触发疲劳锁机${TimeUtil.formatHHMMSimple(autoLockLength)}"
//                            } else {
//                                showContent = "已连续使用手机${TimeUtil.formatHHMMSimple(hasOpenMinute)}"
//                            }
//                            tiredNotifyWindow.showAndHide(showContent)

                            if (ifTiredNeedLock) {
                                if (MMKVUtils.getBoolean(MyConstants.SP_KEY_LIMIT_SETTING_TIRED_AUTO_LOCK, false) && !isLockRunning()) {
//                                Handler(Looper.getMainLooper()).postDelayed(Runnable {
                                    hasOpenMinute = -1
                                    if (!isLockRunning()) {
                                        val timeLength = autoLockLength * 60 * 1000.toLong()
                                        val current = System.currentTimeMillis()
                                        val lockHistory = LockHistory(
                                            0,
                                            "疲劳锁机：${TimeUtil.formatHHMM(autoLockLength)}",
                                            "疲劳锁机：${TimeUtil.formatHHMM(autoLockLength)}",
//                                            current, current, timeLength, timeLength, 1, autoLockLength,
                                            current,
                                            current,
                                            timeLength,
                                            timeLength,
                                            1,
                                            -2,
                                            "",
                                            "",
                                            isFinish = false,
                                            isForceQuit = false,
                                            isSynced = false,
                                            isGeneratedCard = false,
                                            deleteWhiteAppTemp = "[]"
                                        )
                                        lockViewModel.createLockHistory(lockHistory)
//                                        MyToastUtil.showInfo("锁机即将启动")
                                        tiredNotifyWindow.showAndHide("开始疲劳锁机${TimeUtil.formatHHMMSimple(autoLockLength)}")
                                    }

//                                }, 10000)

                                }
                            } else {
                                tiredNotifyWindow.showAndHide("已连续使用手机${TimeUtil.formatHHMMSimple(hasOpenMinute)}")
                            }

                        }


                    }

                }

                Intent.ACTION_SCREEN_ON, Intent.ACTION_USER_PRESENT -> {
//                    index = max
//                    isScreenOn = true
                }

                Intent.ACTION_SCREEN_OFF -> {
//                    isScreenOn = false

                    hasOpenMinute = -1
                }

                else -> LogUtils.d("what broadcast?  " + intent.action)
            }
        }
    }


}


fun weekValid(dayOfWeek: Int, schedule: Schedule): Boolean {
    when (dayOfWeek) {
        Calendar.MONDAY -> {
            if (schedule.monday) {
                return true
            }
        }

        Calendar.TUESDAY -> {
            if (schedule.tuesday) {
                return true
            }
        }

        Calendar.WEDNESDAY -> {
            if (schedule.wednesday) {
                return true
            }
        }

        Calendar.THURSDAY -> {
            if (schedule.thursday) {
                return true
            }
        }

        Calendar.FRIDAY -> {
            if (schedule.friday) {
                return true
            }
        }

        Calendar.SATURDAY -> {
            if (schedule.saturday) {
                return true
            }
        }

        Calendar.SUNDAY -> {
            if (schedule.sunday) {
                return true
            }
        }
    }
    return false
}