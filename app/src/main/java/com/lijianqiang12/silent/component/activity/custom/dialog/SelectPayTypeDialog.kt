package com.lijianqiang12.silent.component.activity.custom.dialog

import android.graphics.Color
import android.graphics.drawable.ColorDrawable
import android.os.Bundle
import android.util.DisplayMetrics
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.view.WindowManager
import androidx.appcompat.app.AppCompatActivity
import androidx.fragment.app.Fragment
import com.lijianqiang12.silent.DIALOG_WIDTH_PERCENT_BIG
import com.lijianqiang12.silent.R
import com.lijianqiang12.silent.component.activity.base.BaseDialogFragment
import kotlinx.android.synthetic.main.dialog_select_pay_type.view.*

const val PAY_TYPE_ALIPAY = 0
const val PAY_TYPE_WXPAY = 1

class SelectPayTypeDialog() : BaseDialogFragment() {

    constructor(fragment: Fragment) : this() {
        this.fragment = fragment
    }

    constructor(activity: AppCompatActivity) : this() {
        this.activity = activity
    }

    private var payTypeListener: OnPayTypeListener? = null
    private lateinit var v: View
    private var fragment: Fragment? = null
    private var activity: AppCompatActivity? = null


    override fun onCreateView(inflater: LayoutInflater, container: ViewGroup?, savedInstanceState: Bundle?): View? {
        dialog!!.window!!.setBackgroundDrawable(ColorDrawable(Color.TRANSPARENT))
        v = inflater.inflate(R.layout.dialog_select_pay_type, container, false)


        return v
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)

        v.btn_pay_type_alipay.setOnClickListener {
            payTypeListener?.apply {
                onChoose(PAY_TYPE_ALIPAY)
            }
            <EMAIL>()
        }
        v.btn_pay_type_wx.setOnClickListener {
            payTypeListener?.apply {
                onChoose(PAY_TYPE_WXPAY)
            }
            <EMAIL>()
        }
    }


    fun show() {
        activity?.apply {
            super.show(this.supportFragmentManager, "NormalDialog")
        }

        fragment?.apply {
            super.show(fragment!!.requireFragmentManager(), "NormalDialog")
        }
    }

    override fun onStart() {
        val params = dialog!!.window!!.attributes
        val dm: DisplayMetrics = resources.displayMetrics
        val width = dm.widthPixels
        params.width = (width * DIALOG_WIDTH_PERCENT_BIG).toInt()//ViewGroup.LayoutParams.MATCH_PARENT
        dialog!!.window!!.attributes = params as WindowManager.LayoutParams
        super.onStart()
    }


    fun setOnPayTypeListener(payTypeListener: OnPayTypeListener) {
        this.payTypeListener = payTypeListener
    }


    interface OnPayTypeListener {
        fun onChoose(type: Int)
    }
}