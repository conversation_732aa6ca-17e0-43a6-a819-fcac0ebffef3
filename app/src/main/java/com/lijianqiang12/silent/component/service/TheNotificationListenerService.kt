package com.lijianqiang12.silent.component.service

import android.service.notification.NotificationListenerService
import android.service.notification.StatusBarNotification
import android.util.Log
import com.blankj.utilcode.util.LogUtils
import com.lijianqiang12.silent.utils.isLockRunning
import com.lijianqiang12.silent.utils.isRemoveNotify
import com.lijianqiang12.silent.utils.isWorkState

class TheNotificationListenerService : NotificationListenerService() {
    override fun onNotificationPosted(sbn: StatusBarNotification) {
//        Log.i("NotificationListener", "Notification posted ===================================")
//        Log.i("NotificationListener", "Notification posted sbn : " + sbn.toString() + "。。。")
//        Log.i("NotificationListener", "Notification posted sbn.key : " + sbn.key + "。。。")
//        Log.i("NotificationListener", "Notification posted sbn.packageName : " + sbn.packageName + "。。。")
//        Log.i("NotificationListener", "Notification posted sbn.isClearable : " + sbn.isClearable + "。。。")
//        Log.i("NotificationListener", "Notification posted sbn.isOngoing : " + sbn.isOngoing + "。。。")
//        Log.i("NotificationListener", "Notification posted ===================================")

//        if ((sbn.key == "0|android|0|com.android.server.wm.AlertWindowNotification - com.lijianqiang12.silent|1000"
//                    && sbn.packageName == "android") && !sbn.isClearable && sbn.isOngoing
//        ) {
        if (sbn.key == "0|android|0|com.android.server.wm.AlertWindowNotification - com.lijianqiang12.silent|1000") {
            try {
                cancelNotification(sbn.key)
                LogUtils.d("remove notification " + sbn.key)
            }catch (e:Exception){

            }
        }

        if (isLockRunning() && isWorkState() && isRemoveNotify()) {
            cancelAllNotifications()
        }
    }

    override fun onNotificationRemoved(sbn: StatusBarNotification) {
        Log.i("NotificationListener", "Notification removed")
    }

    override fun onListenerConnected() {
        super.onListenerConnected()
        Log.i("NotificationListener", "onListenerConnected")
    }

    override fun onListenerDisconnected() {
        super.onListenerDisconnected()
        Log.i("NotificationListener", "onListenerDisconnected")
    }
}