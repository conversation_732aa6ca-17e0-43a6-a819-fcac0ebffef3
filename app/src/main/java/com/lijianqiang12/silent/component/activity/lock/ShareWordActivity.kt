package com.lijianqiang12.silent.component.activity.lock

import android.os.Bundle
import android.view.View
import com.lijianqiang12.silent.utils.MMKVUtils
import com.bumptech.glide.Glide
import com.bumptech.glide.load.engine.DiskCacheStrategy
import com.lijianqiang12.silent.MyConstants
import com.lijianqiang12.silent.R
import com.lijianqiang12.silent.component.activity.base.BaseActivity
import com.lijianqiang12.silent.utils.ImageUtil
import kotlinx.android.synthetic.main.activity_share_word.*
import java.text.SimpleDateFormat
import java.util.*

class ShareWordActivity : BaseActivity() {

    val weeks = mutableListOf("周日", "周一", "周二", "周三", "周四", "周五", "周六")
    val months = mutableListOf("Jan", "Feb", "Mar", "Apr", "May", "Jun", "Jul", "Aug", "Sep", "Oct", "Nov", "Dec")


    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        setContentView(R.layout.activity_share_word)
        iv_share_return.setOnClickListener { finish() }

        val calendar = Calendar.getInstance()

        Glide.with(this).load(MMKVUtils.getString(MyConstants.SP_KEY_WELL_KNOW_WORD_IMG, ""))
            //.transition(DrawableTransitionOptions.withCrossFade())
            .diskCacheStrategy(DiskCacheStrategy.AUTOMATIC)
            .into(iv_share_img)

        val nextYear = calendar.get(Calendar.YEAR) + 1
        val destinyDate = MMKVUtils.getString(MyConstants.SP_KEY_SETTING_DESTINY_DATE, "01/01/${nextYear}")
        val fmt = SimpleDateFormat("MM/dd/yyyy")
        val date = fmt.parse(destinyDate)
        val restDats = (date.time - calendar.time.time + calendar.get(Calendar.HOUR_OF_DAY) * 1000 * 60 * 60 +
                calendar.get(Calendar.MINUTE) * 1000 * 60 + calendar.get(Calendar.SECOND) * 1000 + calendar.get(Calendar.MILLISECOND)) / 1000 / 60 / 60 / 24
        if (restDats > 0) {
            tv_share_destiny.text = "距离${MMKVUtils.getString(MyConstants.SP_KEY_SETTING_DESTINY_NAME, "${nextYear}")}还剩${restDats}天"
        } else if (restDats == 0L) {
            tv_share_destiny.text = "今天${MMKVUtils.getString(MyConstants.SP_KEY_SETTING_DESTINY_NAME, "${nextYear}")}"
        } else {
            tv_share_destiny.text = "${MMKVUtils.getString(MyConstants.SP_KEY_SETTING_DESTINY_NAME, "${nextYear}")}已过去${restDats * -1}天"
        }

        tv_share_word.text = MMKVUtils.getString(MyConstants.SP_KEY_WELL_KNOW_WORD, "")
        tv_share_author.text = "——${MMKVUtils.getString(MyConstants.SP_KEY_WELL_KNOW_WORD_AUTHOR, "")}"
        tv_share_week.text = "${weeks[calendar.get(Calendar.DAY_OF_WEEK) - 1]}"
        tv_share_time.text = "${months[calendar.get(Calendar.MONTH)]} ${calendar.get(Calendar.DAY_OF_MONTH)}, ${calendar.get(Calendar.YEAR)}"


        cb_show_qr.setOnCheckedChangeListener { buttonView, isChecked ->
            if (isChecked) {
                iv_share_qr.visibility = View.VISIBLE
                textView6.visibility = View.VISIBLE
            } else {
                iv_share_qr.visibility = View.GONE
                textView6.visibility = View.GONE
            }
        }
        cb_show_destiny.setOnCheckedChangeListener { buttonView, isChecked ->
            if (isChecked) {
                tv_share_destiny.visibility = View.VISIBLE
            } else {
                tv_share_destiny.visibility = View.GONE
            }
        }

        btn_share_share.setOnClickListener {
            val afterWaterBitmap = ImageUtil.loadBitmapFromView(cl_share_card)
            ImageUtil.shareImage(this, afterWaterBitmap)

//            val image = UMImage(this@ShareWordActivity, afterWaterBitmap)//bitmap文件
//            val thumb = UMImage(this@ShareWordActivity, afterWaterBitmap)
//            image.setThumb(thumb)
//
//            val shareBoardConfig = ShareBoardConfig()
//            shareBoardConfig.setIndicatorVisibility(false)
//            ShareAction(this@ShareWordActivity).withMedia(image).withText("${months[calendar.get(Calendar.MONTH)]} ${calendar.get(Calendar.DAY_OF_MONTH)}, ${calendar.get(Calendar.YEAR)}")
//                .setDisplayList(SHARE_MEDIA.QQ, SHARE_MEDIA.QZONE, SHARE_MEDIA.WEIXIN, SHARE_MEDIA.WEIXIN_CIRCLE, SHARE_MEDIA.SINA, SHARE_MEDIA.MORE)
//                .open(shareBoardConfig)
        }
    }

//    override fun onActivityResult(requestCode: Int, resultCode: Int, data: Intent?) {
//        super.onActivityResult(requestCode, resultCode, data)
//        UMShareAPI.get(this).onActivityResult(requestCode, resultCode, data)//QQ分享回调需要本activity或子fragment都在这里实现
//    }
//
//    override fun onDestroy() {
//        UMShareAPI.get(this).release()
//        super.onDestroy()
//    }
}