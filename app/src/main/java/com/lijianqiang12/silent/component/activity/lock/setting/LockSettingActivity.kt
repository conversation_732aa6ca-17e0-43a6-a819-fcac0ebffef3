package com.lijianqiang12.silent.component.activity.lock.setting

import android.appwidget.AppWidgetManager
import android.content.ClipData
import android.content.ClipboardManager
import android.content.Context
import android.content.Intent
import android.content.res.ColorStateList
import android.graphics.Color
import android.os.Bundle
import android.text.InputType
import android.view.View
import androidx.core.content.ContextCompat
import androidx.lifecycle.Observer
import com.afollestad.date.dayOfMonth
import com.afollestad.date.month
import com.afollestad.date.year
import com.afollestad.materialdialogs.MaterialDialog
import com.afollestad.materialdialogs.datetime.datePicker
import com.afollestad.materialdialogs.input.input
import com.afollestad.materialdialogs.list.listItems
import com.blankj.utilcode.util.AppUtils
import com.bumptech.glide.Glide
import com.bumptech.glide.load.engine.DiskCacheStrategy
import com.lijianqiang12.silent.DEFAULT_LOCK_BG
import com.lijianqiang12.silent.MyConstants
import com.lijianqiang12.silent.R
import com.lijianqiang12.silent.RING_NAME_ARRAY
import com.lijianqiang12.silent.component.activity.base.BaseActivity
import com.lijianqiang12.silent.component.activity.custom.dialog.*
import com.lijianqiang12.silent.component.activity.lock.BgBottomSheetDialogFragment
import com.lijianqiang12.silent.component.activity.me.vip.FROM_WHERE
import com.lijianqiang12.silent.component.activity.me.vip.VIP2Activity
import com.lijianqiang12.silent.component.activity.widget.DenyUninstallAppWidget
import com.lijianqiang12.silent.component.activity.widget.DenyUninstallAppWidget3
import com.lijianqiang12.silent.data.viewmodel.LockViewModel
import com.lijianqiang12.silent.utils.DialogUtil
import dagger.hilt.android.AndroidEntryPoint
import javax.inject.Inject
import com.lijianqiang12.silent.utils.MMKVUtils
import com.lijianqiang12.silent.utils.MyToastUtil
import com.lijianqiang12.silent.utils.MyUtil
import com.lijianqiang12.silent.utils.PermissionUtil
import com.lijianqiang12.silent.utils.getColorFromTheme
import com.lijianqiang12.silent.utils.secondToSimpleHm
import kotlinx.android.synthetic.main.activity_lock_setting.*
import kotlinx.android.synthetic.main.widget_edit_time_limit.tv_edit_time_limit
import java.util.*

@AndroidEntryPoint
class LockSettingActivity : BaseActivity() {

    //    private val SAMPLE_CROPPED_IMAGE_NAME = "backgroundImage.png"
//    val requestPicCode = 10002
//    lateinit var settingSp: SharedPreferences

    @Inject
    lateinit var viewModel: LockViewModel

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
//        settingSp = MyUtil.getSettingSP(applicationContext)
        setContentView(R.layout.activity_lock_setting)
        // ViewModel 现在通过 Hilt 自动注入


        iv_setting_return.setOnClickListener { finish() }

        var limitTimeStart = MMKVUtils.getInt(MyConstants.SP_KEY_LIMIT_TIME_START_SETTING, -1)
        var limitTimeEnd = MMKVUtils.getInt(MyConstants.SP_KEY_LIMIT_TIME_END_SETTING, -1)
        tv_edit_time_limit.text =
            if (limitTimeStart == -1 || limitTimeEnd == -1) "限制修改" else "${secondToSimpleHm(limitTimeStart)}-${secondToSimpleHm(limitTimeEnd)}可改"
        tv_edit_time_limit.setOnClickListener {
            if (!MyUtil.isCurrentInTimeRange(limitTimeStart.toLong(), limitTimeEnd.toLong())) {
                MyToastUtil.showWarning("您设置了仅允许在${secondToSimpleHm(limitTimeStart)}-${secondToSimpleHm(limitTimeEnd)}修改")
                return@setOnClickListener
            }
            LimitTimeEditDialog(this).apply {
                setTitle("在以下时间段可修改罚金解锁、密码解锁、好友解锁、屏蔽页面")
                setLimitTime(limitTimeStart, limitTimeEnd)
                setOnOKClickListener(object : LimitTimeEditDialog.OnOKLimitTimeEditListener {
                    override fun onclick(start: Int, end: Int) {
                        limitTimeStart = start
                        limitTimeEnd = end
                        MMKVUtils.put(MyConstants.SP_KEY_LIMIT_TIME_START_SETTING, start)
                        MMKVUtils.put(MyConstants.SP_KEY_LIMIT_TIME_END_SETTING, end)
                        this@LockSettingActivity.tv_edit_time_limit.text = "${secondToSimpleHm(start)}-${secondToSimpleHm(end)}可改"
                    }

                })
                setOnCancelClickListener(object : LimitTimeEditDialog.OnCancelLimitTimeEditListener {
                    override fun onclick() {
                        MMKVUtils.put(MyConstants.SP_KEY_LIMIT_TIME_START_SETTING, -1)
                        MMKVUtils.put(MyConstants.SP_KEY_LIMIT_TIME_END_SETTING, -1)
                        this@LockSettingActivity.tv_edit_time_limit.text = "限制修改"
                    }
                })
                show()
            }
        }

        //锁机语
        val word = MMKVUtils.getString(MyConstants.SP_KEY_SETTING_WORD, "")
        if (word.isEmpty()) {
            tv_my_word.text = "跟随云端"
        } else {
            tv_my_word.text = word
        }
        btn_my_word.setOnClickListener {
            MaterialDialog(this)
                .cornerRadius(8.0f)
                .title(text = "个性锁机语")
                .input(allowEmpty = true, inputType = InputType.TYPE_TEXT_FLAG_MULTI_LINE) { dialog, input ->
                    if (input.toString().isEmpty()) {
                        tv_my_word.text = "跟随云端"
                        MMKVUtils.put(MyConstants.SP_KEY_SETTING_WORD, "")
                    } else {
                        tv_my_word.text = input.toString()
                        MMKVUtils.put(MyConstants.SP_KEY_SETTING_WORD, input.toString())
                    }

                    DenyUninstallAppWidget.updateAppWidget(this, AppWidgetManager.getInstance(this))
                }.positiveButton(text = "确定")
                .negativeButton(text = "取消")
                .show()
        }

        //背景图片
        var currentUrl = MMKVUtils.getString(MyConstants.SP_KEY_SETTING_BG, DEFAULT_LOCK_BG)
        val bg = iv_background
        Glide.with(this).load(currentUrl)
            //.transition(DrawableTransitionOptions.withCrossFade())
            .diskCacheStrategy(DiskCacheStrategy.AUTOMATIC)
            .into(iv_background)
        btn_background.setOnClickListener {
            BgBottomSheetDialogFragment.newInstance(currentUrl, "锁机背景图片").apply {
                setOnBgSelectListener(object : BgBottomSheetDialogFragment.OnBgSelectListener {
                    override fun onSelect(imgUrl: String) {
                        currentUrl = imgUrl
                        MMKVUtils.put(MyConstants.SP_KEY_SETTING_BG, currentUrl)
                        Glide.with(this@LockSettingActivity).load(currentUrl)
                            //.transition(DrawableTransitionOptions.withCrossFade())
                            .diskCacheStrategy(DiskCacheStrategy.AUTOMATIC).into(bg)
                    }
                })
                show(supportFragmentManager, "BgBottomSheetDialogFragment")
            }
        }


//        val path = settingSp.getString(Constants.SP_SETTING_BACKGROUND, "")
//        if (File(path!!).exists()) {
//            iv_background.setImageDrawable(Drawable.createFromPath(path))
//        }

//        val colorText = settingSp.getInt(Constants.SP_SETTING_TEXT_COLOR, LTGRAY)
//        iv_word_color.imageTintList = ColorStateList.valueOf(colorText)


//        switch_use_accessibility.isChecked = settingSp.getBoolean(Constants.SP_SETTING_ACCESSIBILITY, false)
//        switch_show_online_number.isChecked = settingSp.getBoolean("online", true)


//        switch_date_time.isChecked = MMKVUtils.getBoolean(Constants.SP_SETTING_DATE_TIME, true)

        //是否显示倒数日
        switch_daoshuri.isChecked = MMKVUtils.getBoolean(MyConstants.SP_KEY_SETTING_DESTINY, false)
        if (switch_daoshuri.isChecked) {
            btn_daoshuri_name.visibility = View.VISIBLE
            btn_daoshuri_date.visibility = View.VISIBLE
        } else {
            btn_daoshuri_name.visibility = View.GONE
            btn_daoshuri_date.visibility = View.GONE
        }
        val calendar = Calendar.getInstance()
        val nextYear = calendar.get(Calendar.YEAR) + 1
        tv_daoshuri_name.text = MMKVUtils.getString(MyConstants.SP_KEY_SETTING_DESTINY_NAME, "$nextYear")
        tv_daoshuri_date.text = MMKVUtils.getString(MyConstants.SP_KEY_SETTING_DESTINY_DATE, "01/01/${nextYear}")
        switch_daoshuri.setOnCheckedChangeListener { buttonView, isChecked ->
            MMKVUtils.put(MyConstants.SP_KEY_SETTING_DESTINY, isChecked)
            if (isChecked) {
                btn_daoshuri_name.visibility = View.VISIBLE
                btn_daoshuri_date.visibility = View.VISIBLE
            } else {
                btn_daoshuri_name.visibility = View.GONE
                btn_daoshuri_date.visibility = View.GONE
            }
        }
        btn_daoshuri_name.setOnClickListener {
            MaterialDialog(this)
                .cornerRadius(8.0f)
                .title(text = "倒数日名称")
                .input(allowEmpty = false) { dialog, input ->
                    tv_daoshuri_name.text = input.toString()
                    MMKVUtils.put(MyConstants.SP_KEY_SETTING_DESTINY_NAME, input.toString())
                    DenyUninstallAppWidget3.updateAppWidget(this, AppWidgetManager.getInstance(this))
                }.positiveButton(text = "确定")
                .negativeButton(text = "取消")
                .show()
        }
        btn_daoshuri_date.setOnClickListener {
            MaterialDialog(this)
                .cornerRadius(8.0f).show {
                    datePicker { dialog, date ->
                        val time = "${date.month + 1}/${date.dayOfMonth}/${date.year}"
                        this@LockSettingActivity.tv_daoshuri_date.text = time
                        MMKVUtils.put(MyConstants.SP_KEY_SETTING_DESTINY_DATE, time)
                        DenyUninstallAppWidget3.updateAppWidget(this@LockSettingActivity, AppWidgetManager.getInstance(this@LockSettingActivity))
                    }
                }
        }
//        switch_allow_click_notify.isChecked = settingSp.getBoolean("open_from_notification", false)
//        tv_force_quit_time.text = "${settingSp.getInt("unlock_time", 5)}次"


        //允许罚金解锁
        if (MMKVUtils.getBoolean(MyConstants.SP_KEY_ALLOW_FINANCIAL_PUNISH, true)) {
            switch_allow_financial_punish.isChecked = true
            btn_financial_punish.visibility = View.VISIBLE
            if (MMKVUtils.getInt(MyConstants.SP_KEY_FORCE_UNLOCK_PUNISH, 5) == 0) {
                btn_max_unlock_count.visibility = View.VISIBLE
            } else {
                btn_max_unlock_count.visibility = View.GONE
            }
        } else {
            switch_allow_financial_punish.isChecked = false
            btn_financial_punish.visibility = View.GONE
            btn_max_unlock_count.visibility = View.GONE
        }
        switch_allow_financial_punish.setOnCheckedChangeListener { buttonView, isChecked ->
            val limitTimeStart = MMKVUtils.getInt(MyConstants.SP_KEY_LIMIT_TIME_START_SETTING, -1)
            val limitTimeEnd = MMKVUtils.getInt(MyConstants.SP_KEY_LIMIT_TIME_END_SETTING, -1)
            if (!MyUtil.isCurrentInTimeRange(limitTimeStart.toLong(), limitTimeEnd.toLong())) {
                MyToastUtil.showWarning("您设置了仅允许在${secondToSimpleHm(limitTimeStart)}-${secondToSimpleHm(limitTimeEnd)}修改")
                switch_allow_financial_punish.isChecked = !isChecked
                return@setOnCheckedChangeListener
            }
            MMKVUtils.put(MyConstants.SP_KEY_ALLOW_FINANCIAL_PUNISH, isChecked)
            if (isChecked) {
                btn_financial_punish.visibility = View.VISIBLE
                if (MMKVUtils.getInt(MyConstants.SP_KEY_FORCE_UNLOCK_PUNISH, 5) == 0) {
                    btn_max_unlock_count.visibility = View.VISIBLE
                } else {
                    btn_max_unlock_count.visibility = View.GONE
                }
            } else {
                btn_financial_punish.visibility = View.GONE
                btn_max_unlock_count.visibility = View.GONE

                checkAllowUnlock() {
                    switch_allow_financial_punish.isChecked = true
                    btn_financial_punish.visibility = View.VISIBLE
                    if (MMKVUtils.getInt(MyConstants.SP_KEY_FORCE_UNLOCK_PUNISH, 5) == 0) {
                        btn_max_unlock_count.visibility = View.VISIBLE
                    } else {
                        btn_max_unlock_count.visibility = View.GONE
                    }
                    MMKVUtils.put(MyConstants.SP_KEY_ALLOW_FINANCIAL_PUNISH, true)
                }
            }
        }
        //强制解锁罚金
        tv_financial_punish.text = "${MMKVUtils.getInt(MyConstants.SP_KEY_FORCE_UNLOCK_PUNISH, 5)}元"
        btn_financial_punish.setOnClickListener {
            val limitTimeStart = MMKVUtils.getInt(MyConstants.SP_KEY_LIMIT_TIME_START_SETTING, -1)
            val limitTimeEnd = MMKVUtils.getInt(MyConstants.SP_KEY_LIMIT_TIME_END_SETTING, -1)
            if (!MyUtil.isCurrentInTimeRange(limitTimeStart.toLong(), limitTimeEnd.toLong())) {
                MyToastUtil.showWarning("您设置了仅允许在${secondToSimpleHm(limitTimeStart)}-${secondToSimpleHm(limitTimeEnd)}修改")
                return@setOnClickListener
            }
            MyUtil.forceShowChooseMoneyDialog(this, object : MyUtil.Companion.OnMoneySelectListener {
                override fun onMoneySelect(money: Int) {
                    MMKVUtils.put(MyConstants.SP_KEY_SELECT_MONEY, true)
                    MMKVUtils.put(MyConstants.SP_KEY_FORCE_UNLOCK_PUNISH, money)
                    tv_financial_punish.text = "${money}元"

                    if (money == 0) {
                        btn_max_unlock_count.visibility = View.VISIBLE
                    } else {
                        btn_max_unlock_count.visibility = View.GONE
                    }
                }
            })

        }
        //每月最大解锁次数
        if (MMKVUtils.getBoolean(MyConstants.SP_KEY_ALLOW_FINANCIAL_PUNISH, true) &&
            MMKVUtils.getInt(MyConstants.SP_KEY_FORCE_UNLOCK_PUNISH, 5) == 0
        ) {
            btn_max_unlock_count.visibility = View.VISIBLE
        } else {
            btn_max_unlock_count.visibility = View.GONE
        }

        var count = MMKVUtils.getInt(MyConstants.SP_KEY_MAX_UNLOCK_COUNT, 3)
        val currentMonth = "${calendar.get(Calendar.YEAR)}-${calendar.get(Calendar.MONTH) + 1}"
        val hasUnlockedTime = MMKVUtils.getInt(currentMonth, 0)
        tv_max_unlock_used.text = "已用${hasUnlockedTime}次"
        tv_max_unlock_count.text = "${count}次"
        var max = 0
        if (MyUtil.isVIP()) {
            tv_vip_flag_unlock.visibility = View.GONE
            max = 10
        } else {
            tv_vip_flag_unlock.visibility = View.VISIBLE
            tv_vip_flag_unlock.setOnClickListener {
                val intent = Intent(this, VIP2Activity::class.java)
                intent.putExtra(FROM_WHERE, "LockSettingActivityCardUnlockCount")
                startActivity(intent)
            }
            max = 3
        }
        val tvText = tv_max_unlock_count
        btn_max_unlock_count.setOnClickListener {
            val limitTimeStart = MMKVUtils.getInt(MyConstants.SP_KEY_LIMIT_TIME_START_SETTING, -1)
            val limitTimeEnd = MMKVUtils.getInt(MyConstants.SP_KEY_LIMIT_TIME_END_SETTING, -1)
            if (!MyUtil.isCurrentInTimeRange(limitTimeStart.toLong(), limitTimeEnd.toLong())) {
                MyToastUtil.showWarning("您设置了仅允许在${secondToSimpleHm(limitTimeStart)}-${secondToSimpleHm(limitTimeEnd)}修改")
                return@setOnClickListener
            }
            CountSelectDialog(this).apply {
                setMax(max)
                setStart(0)
                setInitCount(count)
                setOnOKListener(object : CountSelectDialog.OnOKListener {
                    override fun onclick(newCount: Int) {
                        count = newCount
                        MMKVUtils.put(MyConstants.SP_KEY_MAX_UNLOCK_COUNT, newCount)
                        tvText.text = "${newCount}次"
                    }
                })
                show()
            }
        }


        //允许使用密码解锁
        if (MMKVUtils.getBoolean(MyConstants.SP_KEY_ALLOW_PWD_UNLOCK, false)) {
            switch_allow_pwd.isChecked = true
            btn_force_unlock_pwd.visibility = View.VISIBLE
        } else {
            switch_allow_pwd.isChecked = false
            btn_force_unlock_pwd.visibility = View.GONE
        }
        switch_allow_pwd.setOnCheckedChangeListener { buttonView, isChecked ->
            val limitTimeStart = MMKVUtils.getInt(MyConstants.SP_KEY_LIMIT_TIME_START_SETTING, -1)
            val limitTimeEnd = MMKVUtils.getInt(MyConstants.SP_KEY_LIMIT_TIME_END_SETTING, -1)
            if (!MyUtil.isCurrentInTimeRange(limitTimeStart.toLong(), limitTimeEnd.toLong())) {
                MyToastUtil.showWarning("您设置了仅允许在${secondToSimpleHm(limitTimeStart)}-${secondToSimpleHm(limitTimeEnd)}修改")
                switch_allow_pwd.isChecked = !isChecked
                return@setOnCheckedChangeListener
            }
            MMKVUtils.put(MyConstants.SP_KEY_ALLOW_PWD_UNLOCK, isChecked)
            if (isChecked) {
                btn_force_unlock_pwd.visibility = View.VISIBLE
            } else {
                btn_force_unlock_pwd.visibility = View.GONE

                checkAllowUnlock() {
                    switch_allow_pwd.isChecked = true
                    btn_force_unlock_pwd.visibility = View.VISIBLE
                    MMKVUtils.put(MyConstants.SP_KEY_ALLOW_PWD_UNLOCK, true)
                }
            }
        }

        //强制解锁密码
        btn_force_unlock_pwd.setOnClickListener {
            val limitTimeStart = MMKVUtils.getInt(MyConstants.SP_KEY_LIMIT_TIME_START_SETTING, -1)
            val limitTimeEnd = MMKVUtils.getInt(MyConstants.SP_KEY_LIMIT_TIME_END_SETTING, -1)
            if (!MyUtil.isCurrentInTimeRange(limitTimeStart.toLong(), limitTimeEnd.toLong())) {
                MyToastUtil.showWarning("您设置了仅允许在${secondToSimpleHm(limitTimeStart)}-${secondToSimpleHm(limitTimeEnd)}修改")
                return@setOnClickListener
            }
            NormalDialog(this).apply {
                setTitle("温馨提示")
                setContent("请选择您想要的操作")
                setOnNormalOKClickListener("分享密码", object : OnOKClickListener {
                    override fun onclick() {
                        shareLockPwd(MMKVUtils.getString(MyConstants.SP_KEY_FORCE_QUITE_PWD, "OFFPHONE"))
                    }
                })

                setOnNormalCancelClickListener("更改密码", object : OnCancelClickListener {
                    override fun onclick() {

                        MyUtil.checkLoginAndDo(this@LockSettingActivity) {

                            MaterialDialog(this@LockSettingActivity)
                                .cornerRadius(8.0f)
                                .title(text = "设置解锁密码")
                                .input(
                                    allowEmpty = false,
                                    hint = "仅支持英文字母和数字",
                                    inputType = InputType.TYPE_TEXT_VARIATION_VISIBLE_PASSWORD,
                                    maxLength = 128
                                ) { dialog, input ->
                                    viewModel.refreshForceUnlockPwd(input.toString())
                                }.positiveButton(text = "确定")
                                .negativeButton(text = "随机生成") {
                                    viewModel.refreshForceUnlockPwd("")
                                }
                                .show()
                        }
                    }
                })

                showDialog()
            }

        }
        viewModel.forceUnlockPwd.observe(this, Observer {
            if (it.state == 0) {
//                tv_force_unlock_pwd.text = it.data!!.pwd
                MMKVUtils.put(MyConstants.SP_KEY_FORCE_QUITE_PWD, it.data!!.pwd)
                NormalDialog(this).apply {
                    setTitle("温馨提示")
                    setContent("密码已设置为『${it.data.pwd}』，该密码仅展示一次，请立刻保存，建议发送给朋友帮忙保存，是否现在发送？")
                    setOnNormalOKClickListener("发送密码", object : OnOKClickListener {
                        override fun onclick() {
                            shareLockPwd(it.data.pwd)
                        }
                    })

                    setOnNormalCancelClickListener("复制", object : OnCancelClickListener {
                        override fun onclick() {
                            val clipboard: ClipboardManager = applicationContext.getSystemService(Context.CLIPBOARD_SERVICE) as ClipboardManager
                            val clipData = ClipData.newPlainText(null, it.data.pwd)
                            clipboard.setPrimaryClip(clipData)

                            MyToastUtil.showSuccess("密码复制成功")
                        }
                    })
                    showDialog()
                }
            }
        })

        //允许使用好友解锁
        switch_allow_friend.isChecked = MMKVUtils.getBoolean(MyConstants.SP_KEY_ALLOW_FRIEND_UNLOCK, true)
        switch_allow_friend.setOnCheckedChangeListener { buttonView, isChecked ->
            val limitTimeStart = MMKVUtils.getInt(MyConstants.SP_KEY_LIMIT_TIME_START_SETTING, -1)
            val limitTimeEnd = MMKVUtils.getInt(MyConstants.SP_KEY_LIMIT_TIME_END_SETTING, -1)
            if (!MyUtil.isCurrentInTimeRange(limitTimeStart.toLong(), limitTimeEnd.toLong())) {
                MyToastUtil.showWarning("您设置了仅允许在${secondToSimpleHm(limitTimeStart)}-${secondToSimpleHm(limitTimeEnd)}修改")
                switch_allow_friend.isChecked = !isChecked
                return@setOnCheckedChangeListener
            }
            MMKVUtils.put(MyConstants.SP_KEY_ALLOW_FRIEND_UNLOCK, isChecked)

            checkAllowUnlock() {
                switch_allow_friend.isChecked = true
                MMKVUtils.put(MyConstants.SP_KEY_ALLOW_FRIEND_UNLOCK, true)
            }
        }


        //锁机时屏蔽部分页面
        if (MyUtil.isVIP()) {
            tv_vip_flag_deny_page.visibility = View.GONE
        } else {
            tv_vip_flag_deny_page.visibility = View.VISIBLE
            tv_vip_flag_deny_page.setOnClickListener {
                val intent = Intent(this, VIP2Activity::class.java)
                intent.putExtra(FROM_WHERE, "LockSettingActivityCardDenyPage")
                startActivity(intent)
            }
        }

        btn_deny_page.setOnClickListener {
            val limitTimeStart = MMKVUtils.getInt(MyConstants.SP_KEY_LIMIT_TIME_START_SETTING, -1)
            val limitTimeEnd = MMKVUtils.getInt(MyConstants.SP_KEY_LIMIT_TIME_END_SETTING, -1)
            if (!MyUtil.isCurrentInTimeRange(limitTimeStart.toLong(), limitTimeEnd.toLong())) {
                MyToastUtil.showWarning("您设置了仅允许在${secondToSimpleHm(limitTimeStart)}-${secondToSimpleHm(limitTimeEnd)}修改")
                return@setOnClickListener
            }
            startActivity(Intent(this, DenyPageSettingActivity::class.java))

        }


        //定时任务到达前提醒
        if (MyUtil.isVIP()) {
            tv_vip_flag_schedule_notify.visibility = View.GONE
        } else {
            tv_vip_flag_schedule_notify.visibility = View.VISIBLE
        }
        val tvScheduleNotify = tv_schedule_notify
        val scheduleNotify = MMKVUtils.getInt(MyConstants.SP_KEY_SCHEDULE_NOTIFY, 5)
        tvScheduleNotify.text = MyUtil.getScheduleNotifyLengthString(scheduleNotify)
        btn_schedule_notify.setOnClickListener {
            if (MyUtil.isVIP()) {
                val myItems = listOf(
                    MyUtil.getScheduleNotifyLengthString(0),
                    MyUtil.getScheduleNotifyLengthString(1),
                    MyUtil.getScheduleNotifyLengthString(2),
                    MyUtil.getScheduleNotifyLengthString(3),
                    MyUtil.getScheduleNotifyLengthString(4),
                    MyUtil.getScheduleNotifyLengthString(5),
                    MyUtil.getScheduleNotifyLengthString(6),
                )
                MaterialDialog(this)
                    .cornerRadius(8.0f).show {
                        listItems(items = myItems) { dialog, index, text ->
                            MMKVUtils.put(MyConstants.SP_KEY_SCHEDULE_NOTIFY, index)
                            tvScheduleNotify.text = MyUtil.getScheduleNotifyLengthString(index)
                        }
                    }
            } else {
                DialogUtil.showVIPDialog(this, null, content = "自定义提前提醒时间为VIP专享功能，开通后，即可根据自身习惯更改提前提醒时间。", "定时或监督前提醒")

            }
        }

        //白名单检测方式
        val defaultMethod = MMKVUtils.getInt(MyConstants.SP_KEY_DEFAULT_CHECK_TOP_APP_METHOD, 1)
        val checkMethod = MMKVUtils.getInt(MyConstants.SP_KEY_CHECK_TOP_APP_METHOD, defaultMethod) //0旧方法，1新方法
        val showListSimple = arrayListOf("保守", "激进")
        val showList = arrayListOf("保守（无法检测小窗与分屏）", "激进（可检测小窗与分屏，但可能误锁或误屏蔽）")
        tv_check_top_app_mode.text = showListSimple[checkMethod]

        btn_check_top_app_mode.setOnClickListener {
            val bottomDialog = BottomSingleSelectDialogFragment.newInstance()
            bottomDialog.setShowList(showList)
            bottomDialog.setValueList(arrayListOf(0, 1))
            bottomDialog.setOnValueSelectListener(object : BottomSingleSelectDialogFragment.OnValueSelectListener {
                override fun onSelect(value: Long, show: String) {
                    MMKVUtils.put(MyConstants.SP_KEY_CHECK_TOP_APP_METHOD, value)
                    this@LockSettingActivity.tv_check_top_app_mode.text = showListSimple[value.toInt()]
                }
            })
            bottomDialog.show(supportFragmentManager, "")
        }

        //显示横屏切换开关
        switch_show_landscape.isChecked = MMKVUtils.getBoolean(MyConstants.SP_KEY_SHOW_LANDSCAPE, true)
        switch_show_landscape.setOnCheckedChangeListener { buttonView, isChecked ->
            MMKVUtils.put(MyConstants.SP_KEY_SHOW_LANDSCAPE, isChecked)
        }


        //开始提示音
        val tvVoiceStart = tv_start_ringtone
        var startVoice = MMKVUtils.getInt(MyConstants.SP_KEY_START_VOICE, 3)
        tvVoiceStart.text = RING_NAME_ARRAY[startVoice]
        btn_start_ringtone.setOnClickListener {
            RingChooseDialog(this).apply {
                setIndex(startVoice)
                setOnRingSelectClickListener(object : RingChooseDialog.OnRingSelectListener {
                    override fun onSelect(index: Int) {
                        startVoice = index
                        tvVoiceStart.text = RING_NAME_ARRAY[startVoice]
                        MMKVUtils.put(MyConstants.SP_KEY_START_VOICE, startVoice)
                    }
                })
                show()
            }
        }

        //结束提示音
        val tvEndStart = tv_end_ringtone
        var endVoice = MMKVUtils.getInt(MyConstants.SP_KEY_END_VOICE, 3)
        tvEndStart.text = RING_NAME_ARRAY[endVoice]
        btn_end_ringtone.setOnClickListener {
            RingChooseDialog(this).apply {
                setIndex(endVoice)
                setOnRingSelectClickListener(object : RingChooseDialog.OnRingSelectListener {
                    override fun onSelect(index: Int) {
                        endVoice = index
                        tvEndStart.text = RING_NAME_ARRAY[endVoice]
                        MMKVUtils.put(MyConstants.SP_KEY_END_VOICE, endVoice)
                    }
                })
                show()
            }
        }

        //开始震动
        tv_start_shake.text = "${MMKVUtils.getLong(MyConstants.SP_KEY_START_SHAKE, 0L) / 1000f}秒"
        btn_start_shake.setOnClickListener {
            val myItems = listOf("0秒", "0.1秒", "0.2秒", "0.5秒", "1.0秒", "2.0秒", "3.0秒", "4.0秒", "5.0秒")
            MaterialDialog(this)
                .cornerRadius(8.0f).show {
                    listItems(items = myItems) { dialog, index, text ->
                        this@LockSettingActivity.tv_start_shake.text = text
                        val length = mutableListOf<Int>(0, 100, 200, 500, 1000, 2000, 3000, 4000, 5000)[index].toLong()
                        MMKVUtils.put(MyConstants.SP_KEY_START_SHAKE, length)
//                        MMKVUtils.put(MyConstants.SP_KEY_START_SHAKE, (index * 1000).toLong())
                    }
                }
        }

        //结束震动
        tv_end_shake.text = "${MMKVUtils.getLong(MyConstants.SP_KEY_END_SHAKE, 0L) / 1000f}秒"
        btn_end_shake.setOnClickListener {
            val myItems = listOf("0秒", "0.1秒", "0.2秒", "0.5秒", "1.0秒", "2.0秒", "3.0秒", "4.0秒", "5.0秒")
            MaterialDialog(this)
                .cornerRadius(8.0f).show {
                    listItems(items = myItems) { dialog, index, text ->
                        this@LockSettingActivity.tv_end_shake.text = text
                        val length = mutableListOf<Int>(0, 100, 200, 500, 1000, 2000, 3000, 4000, 5000)[index].toLong()
                        MMKVUtils.put(MyConstants.SP_KEY_END_SHAKE, length)
//                        MMKVUtils.put(MyConstants.SP_KEY_END_SHAKE, (index * 1000).toLong())
                    }
                }
        }


        //屏蔽通知

        rg_global_notification.setOnCheckedChangeListener { group, checkedId ->
            when (checkedId) {
                R.id.rb_global_notification_1 -> {
                    if (PermissionUtil.isNotificationListenersEnabled(this)) {
                        MMKVUtils.put(MyConstants.SP_KEY_REMOVE_NOTIFICATION, true)
                    } else {
                        NormalDialog(this).run {
                            setTitle("缺少权限")
                            setContent("需要您授予“通知权限”，点击下方“确定”，跳转权限授予页面")
                            setOnNormalOKClickListener(object : OnOKClickListener {
                                override fun onclick() {
                                    PermissionUtil.gotoNotificationAccessSetting(this@LockSettingActivity)
                                }
                            })
                            showDialog()
                        }
                    }

                }

                R.id.rb_global_notification_2 -> {
                    MMKVUtils.put(MyConstants.SP_KEY_REMOVE_NOTIFICATION, false)

                }
            }
        }

        //静音

        rg_global_silent.setOnCheckedChangeListener { group, checkedId ->
            when (checkedId) {
                R.id.rb_global_silent_1 -> {
                    if (PermissionUtil.hasPermissionVolume(this)) {
                        MMKVUtils.put(MyConstants.SP_KEY_SILENT, true)
                    } else {
                        NormalDialog(this).run {
                            setTitle("缺少权限")
                            setContent("需要您授予“免打扰权限”，点击下方“确定”，跳转权限授予页面")
                            setOnNormalOKClickListener(object : OnOKClickListener {
                                override fun onclick() {
                                    PermissionUtil.openPermissionVolume(this@LockSettingActivity)
                                }
                            })
                            showDialog()
                        }
                    }

                }

                R.id.rb_global_silent_2 -> {
                    MMKVUtils.put(MyConstants.SP_KEY_SILENT, false)

                }
            }
        }

        //允许长按删除白名单app
        switch_show_delete_white_app.isChecked = MMKVUtils.getBoolean(MyConstants.SP_KEY_ALLOW_DELETE_WHITE_APP, true)
        switch_show_delete_white_app.setOnCheckedChangeListener { buttonView, isChecked ->
            MMKVUtils.put(MyConstants.SP_KEY_ALLOW_DELETE_WHITE_APP, isChecked)
        }

        //允许显示跳转到设置页面按钮
        val showSystemSettings = MMKVUtils.getBoolean(MyConstants.SP_KEY_SHOW_SYSTEM_SETTINGS, false)
        switch_show_system_settings.isChecked = showSystemSettings
        btn_show_system_settings_sub.visibility = if (showSystemSettings) View.VISIBLE else View.GONE
        switch_show_system_settings.setOnCheckedChangeListener { buttonView, isChecked ->
            btn_show_system_settings_sub.visibility = if (isChecked) View.VISIBLE else View.GONE
            MMKVUtils.put(MyConstants.SP_KEY_SHOW_SYSTEM_SETTINGS, isChecked)
        }

        // wifi按钮
        val color = if (MMKVUtils.getBoolean(MyConstants.SP_KEY_SHOW_SYSTEM_SETTINGS1, true)) getColorFromTheme(
            this, R.attr.custom_attr_app_fg
        ) else getColorFromTheme(this, R.attr.custom_attr_app_text_5)
        iv_lock_view_home1.imageTintList = ColorStateList.valueOf(color)
        iv_lock_view_home1.setOnClickListener {
            MMKVUtils.put(MyConstants.SP_KEY_SHOW_SYSTEM_SETTINGS1, !MMKVUtils.getBoolean(MyConstants.SP_KEY_SHOW_SYSTEM_SETTINGS1, true))
            val color = if (MMKVUtils.getBoolean(MyConstants.SP_KEY_SHOW_SYSTEM_SETTINGS1, true)) getColorFromTheme(
                this, R.attr.custom_attr_app_fg
            ) else getColorFromTheme(
                this,
                R.attr.custom_attr_app_text_5
            )
            iv_lock_view_home1.imageTintList = ColorStateList.valueOf(color)
        }

        // 流量按钮
        val color2 = if (MMKVUtils.getBoolean(MyConstants.SP_KEY_SHOW_SYSTEM_SETTINGS2, true)) getColorFromTheme(
            this, R.attr.custom_attr_app_fg
        ) else getColorFromTheme(this, R.attr.custom_attr_app_text_5)
        iv_lock_view_home2.imageTintList = ColorStateList.valueOf(color2)
        iv_lock_view_home2.setOnClickListener {
            MMKVUtils.put(MyConstants.SP_KEY_SHOW_SYSTEM_SETTINGS2, !MMKVUtils.getBoolean(MyConstants.SP_KEY_SHOW_SYSTEM_SETTINGS2, true))
            val color = if (MMKVUtils.getBoolean(MyConstants.SP_KEY_SHOW_SYSTEM_SETTINGS2, true)) getColorFromTheme(
                this, R.attr.custom_attr_app_fg
            ) else getColorFromTheme(
                this,
                R.attr.custom_attr_app_text_5
            )
            iv_lock_view_home2.imageTintList = ColorStateList.valueOf(color)
        }
        
        // 蓝牙按钮
        val color3 = if (MMKVUtils.getBoolean(MyConstants.SP_KEY_SHOW_SYSTEM_SETTINGS3, true)) getColorFromTheme(
            this, R.attr.custom_attr_app_fg
        ) else getColorFromTheme(this, R.attr.custom_attr_app_text_5)
        iv_lock_view_home3.imageTintList = ColorStateList.valueOf(color3)
        iv_lock_view_home3.setOnClickListener {

            MMKVUtils.put(MyConstants.SP_KEY_SHOW_SYSTEM_SETTINGS3, !MMKVUtils.getBoolean(MyConstants.SP_KEY_SHOW_SYSTEM_SETTINGS3, true))
            val color = if (MMKVUtils.getBoolean(MyConstants.SP_KEY_SHOW_SYSTEM_SETTINGS3, true)) getColorFromTheme(
                this, R.attr.custom_attr_app_fg
            ) else getColorFromTheme(
                this,
                R.attr.custom_attr_app_text_5
            )
            iv_lock_view_home3.imageTintList = ColorStateList.valueOf(color)
        }
        
        // 亮度调整按钮
        val color4 = if (MMKVUtils.getBoolean(MyConstants.SP_KEY_SHOW_SYSTEM_SETTINGS4, true)) getColorFromTheme(
            this, R.attr.custom_attr_app_fg
        ) else getColorFromTheme(this, R.attr.custom_attr_app_text_5)
        iv_lock_view_home4.imageTintList = ColorStateList.valueOf(color4)
        iv_lock_view_home4.setOnClickListener {
            MMKVUtils.put(MyConstants.SP_KEY_SHOW_SYSTEM_SETTINGS4, !MMKVUtils.getBoolean(MyConstants.SP_KEY_SHOW_SYSTEM_SETTINGS4, true))
            val color = if (MMKVUtils.getBoolean(MyConstants.SP_KEY_SHOW_SYSTEM_SETTINGS4, true)) getColorFromTheme(
                this, R.attr.custom_attr_app_fg
            ) else getColorFromTheme(
                this,
                R.attr.custom_attr_app_text_5
            )
            iv_lock_view_home4.imageTintList = ColorStateList.valueOf(color)
        }
        
        // 飞行模式按钮
        val color5 = if (MMKVUtils.getBoolean(MyConstants.SP_KEY_SHOW_SYSTEM_SETTINGS5, true)) getColorFromTheme(
            this, R.attr.custom_attr_app_fg
        ) else getColorFromTheme(this, R.attr.custom_attr_app_text_5)
        iv_lock_view_home5.imageTintList = ColorStateList.valueOf(color5)
        iv_lock_view_home5.setOnClickListener {
            MMKVUtils.put(MyConstants.SP_KEY_SHOW_SYSTEM_SETTINGS5, !MMKVUtils.getBoolean(MyConstants.SP_KEY_SHOW_SYSTEM_SETTINGS5, true))
            val color = if (MMKVUtils.getBoolean(MyConstants.SP_KEY_SHOW_SYSTEM_SETTINGS5, true)) getColorFromTheme(
                this, R.attr.custom_attr_app_fg
            ) else getColorFromTheme(
                this,
                R.attr.custom_attr_app_text_5
            )
            iv_lock_view_home5.imageTintList = ColorStateList.valueOf(color)
        }
        
        // GPS按钮
        val color6 = if (MMKVUtils.getBoolean(MyConstants.SP_KEY_SHOW_SYSTEM_SETTINGS6, true)) getColorFromTheme(
            this, R.attr.custom_attr_app_fg
        ) else getColorFromTheme(this, R.attr.custom_attr_app_text_5)
        iv_lock_view_home6.imageTintList = ColorStateList.valueOf(color6)
        iv_lock_view_home6.setOnClickListener {
            MMKVUtils.put(MyConstants.SP_KEY_SHOW_SYSTEM_SETTINGS6, !MMKVUtils.getBoolean(MyConstants.SP_KEY_SHOW_SYSTEM_SETTINGS6, true))
            val color = if (MMKVUtils.getBoolean(MyConstants.SP_KEY_SHOW_SYSTEM_SETTINGS6, true)) getColorFromTheme(
                this, R.attr.custom_attr_app_fg
            ) else getColorFromTheme(
                this,
                R.attr.custom_attr_app_text_5
            )
            iv_lock_view_home6.imageTintList = ColorStateList.valueOf(color)
        }
        
        // 热点按钮
        val color7 = if (MMKVUtils.getBoolean(MyConstants.SP_KEY_SHOW_SYSTEM_SETTINGS7, true)) getColorFromTheme(
            this, R.attr.custom_attr_app_fg
        ) else getColorFromTheme(this, R.attr.custom_attr_app_text_5)
        iv_lock_view_home7.imageTintList = ColorStateList.valueOf(color7)
        iv_lock_view_home7.setOnClickListener {
            MMKVUtils.put(MyConstants.SP_KEY_SHOW_SYSTEM_SETTINGS7, !MMKVUtils.getBoolean(MyConstants.SP_KEY_SHOW_SYSTEM_SETTINGS7, true))
            val color = if (MMKVUtils.getBoolean(MyConstants.SP_KEY_SHOW_SYSTEM_SETTINGS7, true)) getColorFromTheme(
                this, R.attr.custom_attr_app_fg
            ) else getColorFromTheme(
                this,
                R.attr.custom_attr_app_text_5
            )
            iv_lock_view_home7.imageTintList = ColorStateList.valueOf(color)
        }
        
        // NFC按钮
        val color8 = if (MMKVUtils.getBoolean(MyConstants.SP_KEY_SHOW_SYSTEM_SETTINGS8, true)) getColorFromTheme(
            this, R.attr.custom_attr_app_fg
        ) else getColorFromTheme(this, R.attr.custom_attr_app_text_5)
        iv_lock_view_home8.imageTintList = ColorStateList.valueOf(color8)
        iv_lock_view_home8.setOnClickListener {
            MMKVUtils.put(MyConstants.SP_KEY_SHOW_SYSTEM_SETTINGS8, !MMKVUtils.getBoolean(MyConstants.SP_KEY_SHOW_SYSTEM_SETTINGS8, true))
            val color = if (MMKVUtils.getBoolean(MyConstants.SP_KEY_SHOW_SYSTEM_SETTINGS8, true)) getColorFromTheme(
                this, R.attr.custom_attr_app_fg
            ) else getColorFromTheme(
                this,
                R.attr.custom_attr_app_text_5
            )
            iv_lock_view_home8.imageTintList = ColorStateList.valueOf(color)
        }

        //锁机时暂停音乐
        switch_stop_music_settings.isChecked = MMKVUtils.getBoolean(MyConstants.SP_KEY_STOP_MUSIC_SETTINGS, true)
        switch_stop_music_settings.setOnCheckedChangeListener { buttonView, isChecked ->
            MMKVUtils.put(MyConstants.SP_KEY_STOP_MUSIC_SETTINGS, isChecked)
        }

        //锁机时执行PerformHome
        switch_perform_home_settings.isChecked = MMKVUtils.getBoolean(MyConstants.SP_KEY_PERFORM_HOME_WHEN_LOCK_START, false)//防止用户正在使用白名单，弹出后打断工作

        switch_perform_home_settings.setOnCheckedChangeListener { buttonView, isChecked ->
            MMKVUtils.put(MyConstants.SP_KEY_PERFORM_HOME_WHEN_LOCK_START, isChecked)
        }

        //允许弹出远离手机
        if (MMKVUtils.getBoolean(MyConstants.SP_KEY_ALLOW_AUTO_LAUNCH, true)) {
            switch_allow_auto_launch.isChecked = true
            btn_allow_notify_punch.visibility = View.VISIBLE
        } else {
            switch_allow_auto_launch.isChecked = false
            btn_allow_notify_punch.visibility = View.GONE
        }

        switch_allow_auto_launch.setOnCheckedChangeListener { buttonView, isChecked ->
            MMKVUtils.put(MyConstants.SP_KEY_ALLOW_AUTO_LAUNCH, isChecked)
            if (isChecked) {
                btn_allow_notify_punch.visibility = View.VISIBLE
            } else {
                btn_allow_notify_punch.visibility = View.GONE
            }
        }

        switch_allow_notify_punch.isChecked = MMKVUtils.getBoolean(MyConstants.SP_KEY_ALLOW_NOTIFY_PUNCH, true)
        switch_allow_notify_punch.setOnCheckedChangeListener { buttonView, isChecked ->
            MMKVUtils.put(MyConstants.SP_KEY_ALLOW_NOTIFY_PUNCH, isChecked)
        }

//        var default = true
//        if (RomUtils.isVivo()) {
//            default = false
//        }
//        switch_compatible.isChecked = MMKVUtils.getBoolean(MyConstants.SP_KEY_OPEN_WHITE_COMPATIBLE, default)
//        switch_compatible.setOnCheckedChangeListener { buttonView, isChecked ->
//            MMKVUtils.put(MyConstants.SP_KEY_OPEN_WHITE_COMPATIBLE, isChecked)
//            if (isChecked && RomUtils.isVivo()) {
//                NormalDialog(this).apply {
//                    setTitle("警告")
//                    setContent("开启兼容模式后，可能需要为所有白名单软件开启“允许关联启动”才能启动白名单。")
//                    isCancelable = false
//                    setOnNormaOKlClickListener("我知道了", object : OnOKClickListener {
//                        override fun onclick() {
//                        }
//                    })
//                    show()
//                }
//            }
//        }
    }


    private fun checkAllowUnlock(callback: () -> Unit) {
        val allowPunish = MMKVUtils.getBoolean(MyConstants.SP_KEY_ALLOW_FINANCIAL_PUNISH, true)
        val allowPwd = MMKVUtils.getBoolean(MyConstants.SP_KEY_ALLOW_PWD_UNLOCK, false)
        val allowFriend = MMKVUtils.getBoolean(MyConstants.SP_KEY_ALLOW_FRIEND_UNLOCK, true)

        if (!allowPunish && !allowPwd && !allowFriend) {
            NormalDialog(this).apply {
                setTitle("⚠️警告⚠️")
                setContent("1.您未开启罚金解锁、密码解锁、好友解锁中的任何一种，将无法强制解锁，即便开发者也没有办法，强烈建议您保留至少一项。\n\n2.如果一定要关闭，请确保白名单等功能您已经熟悉并正常使用了一周以上。\n\n是否要继续关闭？")
                isCancelable = false
                setOnNormalOKClickListener("取消", object : OnOKClickListener {
                    override fun onclick() {
                        callback()
                    }
                })
                setOnNormalCancelClickListener("继续关闭", object : OnCancelClickListener {
                    override fun onclick() {
                    }
                })
                showDialog()
            }
        }
    }

    override fun onResume() {
        super.onResume()
        if (MMKVUtils.getBoolean(MyConstants.SP_KEY_REMOVE_NOTIFICATION, false) && PermissionUtil.isNotificationListenersEnabled(this)) {
            rb_global_notification_1.isChecked = true
        } else {
            rb_global_notification_2.isChecked = true
        }

        if (MMKVUtils.getBoolean(MyConstants.SP_KEY_SILENT, false) && PermissionUtil.hasPermissionVolume(this)) {
            rb_global_silent_1.isChecked = true
        } else {
            rb_global_silent_2.isChecked = true
        }
    }

    private fun shareLockPwd(pwd: String) {
        val sharedIntent = Intent()
        //设置动作为Intent.ACTION_SEND
        sharedIntent.action = Intent.ACTION_SEND
        //设置为文本类型
        sharedIntent.type = "text/*"
        sharedIntent.putExtra(
            Intent.EXTRA_TEXT,
            "Hi,朋友，我正在用【${AppUtils.getAppName()}】https://a.app.qq.com/o/simple.jsp?pkgname=${AppUtils.getAppPackageName()} 管理手机使用时间，请帮我保管解锁密码：${pwd}，谢谢！"

        )

        //设置要分享的内容
        startActivity(Intent.createChooser(sharedIntent, "分享给朋友"))
    }

}
