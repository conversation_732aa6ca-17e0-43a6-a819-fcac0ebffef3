package com.lijianqiang12.silent.component.activity.me.vip

import android.content.Intent
import android.os.Bundle
import android.os.Handler
import android.os.Message
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.LinearLayout
import androidx.core.view.children
import androidx.fragment.app.viewModels
import androidx.lifecycle.Observer
import androidx.lifecycle.lifecycleScope
import com.alipay.sdk.app.PayTask
import com.blankj.utilcode.util.ConvertUtils
import com.blankj.utilcode.util.LogUtils
import com.lijianqiang12.silent.utils.MMKVUtils
import com.google.android.material.bottomsheet.BottomSheetBehavior
import com.jeremyliao.liveeventbus.LiveEventBus
import com.lijianqiang12.silent.LiveBus
import com.lijianqiang12.silent.MyConstants
import com.lijianqiang12.silent.R
import com.lijianqiang12.silent.data.model.net.pojos.Money
import com.lijianqiang12.silent.component.activity.TheWebViewActivity
import com.lijianqiang12.silent.component.activity.base.BaseBottomSheetDialogFragment
import com.lijianqiang12.silent.component.activity.custom.dialog.MyProgressDialog
import com.lijianqiang12.silent.component.activity.custom.dialog.NormalDialog
import com.lijianqiang12.silent.component.activity.custom.dialog.OnCancelClickListener
import com.lijianqiang12.silent.component.activity.custom.dialog.OnOKClickListener
import com.lijianqiang12.silent.component.activity.custom.dialog.PAY_TYPE_WXPAY
import com.lijianqiang12.silent.data.viewmodel.LoginViewModel
import com.lijianqiang12.silent.data.viewmodel.VIPViewModel
import com.lijianqiang12.silent.utils.MyToastUtil
import com.lijianqiang12.silent.utils.MyUtil
import com.lijianqiang12.silent.utils.PayResult
import dagger.hilt.android.AndroidEntryPoint
import javax.inject.Inject
import com.tencent.mm.opensdk.openapi.IWXAPI
import com.tencent.mm.opensdk.openapi.WXAPIFactory
import kotlinx.android.synthetic.main.bottom_sheet_vip.view.*
import kotlinx.android.synthetic.main.item_money.view.*
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.launch
import kotlinx.coroutines.withContext

@AndroidEntryPoint
class VIPBottomSheetDialogFragment() : BaseBottomSheetDialogFragment() {
    private val TAG = "VIPBottomSheetDialogFragment"
    private lateinit var mBehavior: BottomSheetBehavior<View>
    private lateinit var customView: View
    private lateinit var dialog: MyProgressDialog
    private var moneyList = mutableListOf<Money>()

    private var vipIndex = -1
    private var notifyForever = false
    private lateinit var api: IWXAPI
    private val SDK_PAY_FLAG = 1


    @Inject
    lateinit var viewModel: VIPViewModel

    @Inject
    lateinit var loginViewModel: LoginViewModel

    companion object {
        @JvmStatic
        fun newInstance() = VIPBottomSheetDialogFragment()
    }

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        api = WXAPIFactory.createWXAPI(requireContext(), MyConstants.WX_APP_ID)
//        vipIndex = 3
        dialog = MyProgressDialog(this)
    }

    override fun onCreateView(inflater: LayoutInflater, container: ViewGroup?, savedInstanceState: Bundle?): View? {
        customView = View.inflate(requireContext(), R.layout.bottom_sheet_vip, null)
        return customView
    }

    override fun onStart() {
        super.onStart()
        val parentView = customView.parent as View
        parentView.setBackgroundColor(resources.getColor(R.color.colorTranslate))

        //设置初始状态为填充满
        mBehavior = BottomSheetBehavior.from(parentView)
        mBehavior.state = BottomSheetBehavior.STATE_EXPANDED
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        LiveEventBus.get(LiveBus.PAY_FOR_VIP_SUCCEED, Boolean::class.java).observe(viewLifecycleOwner, Observer {
            if (it) {
                paySucceed(PAY_TYPE_WXPAY)
            } else {
                MyToastUtil.showInfo("支付失败")
            }
        })
//        LiveBus.getInstance().with(String::class.java).observe(this, Observer {
//            if (it == LiveBus.PAY_FOR_VIP_SUCCEED) {
//                paySucceed()
//            }
//        }, false)
        val rgMoneys = customView.rg_money

        viewModel.VIPMoneyLiveData.observe(viewLifecycleOwner, androidx.lifecycle.Observer { networkState ->
            LogUtils.d(networkState.toString())
            if (networkState.state == 0) {
                if (vipIndex == -1) {
                    vipIndex = networkState.data!!.selectIndex
                }
                notifyForever = networkState.data!!.notifyForever
                moneyList = networkState.data!!.moneyList
                rgMoneys.removeAllViews()
                networkState.data.moneyList.forEachIndexed { index, money ->
                    val item = LayoutInflater.from(requireContext()).inflate(R.layout.item_money, null)
                    item.tv_days.text = money.title
                    if (money.text.isEmpty()) {
                        item.tv_text_item.visibility = View.INVISIBLE
                    } else {
                        item.tv_text_item.text = money.text
                    }
                    item.tv_money_item.text = "${money.currentPrice}"

                    item.setOnClickListener {
                        vipIndex = index
                        chooseMoneyItem(vipIndex)
                    }

                    val lp = LinearLayout.LayoutParams(LinearLayout.LayoutParams.WRAP_CONTENT, LinearLayout.LayoutParams.WRAP_CONTENT, 1f)
                    lp.marginStart = ConvertUtils.dp2px(6f)
                    lp.marginEnd = ConvertUtils.dp2px(6f)
                    rgMoneys.addView(item, lp)
                }

                chooseMoneyItem(vipIndex)

            } else {
                this.dismiss()
            }

        })

        customView.textView59.setOnClickListener {
            val intent = Intent(requireContext(), TheWebViewActivity::class.java)
            intent.putExtra("title", "")
            intent.putExtra("url", "https://help-offphone.shuge888.com/protocal/vip_agreement")
            requireContext().startActivity(intent)
        }

        customView.btn_vip_alipay.setOnClickListener {
            MMKVUtils.put(MyConstants.SP_KEY_PAY_TYPE, 0)
            if (MyUtil.checkPackageInstalled(requireContext(), "com.eg.android.AlipayGphone", MyConstants.URL_ALIPAY)) {
                if (vipIndex >= 0) {

                    lifecycleScope.launch(Dispatchers.IO) {
                        try {
                            val result = viewModel.vipRepository.makeAlipayOrderVIP(
                                moneyList[vipIndex].currentPrice,
                                moneyList[vipIndex].originalPrice, "", "", ""
                            )
                            withContext(Dispatchers.Main) {
                                if (result.code == 200) {
                                    val payRunnable = Runnable {
                                        val alipay = PayTask(requireActivity())
                                        val payResult = alipay.payV2(result.data!!.order, true) as Map<String, String>
                                        val msg = Message()
                                        msg.what = SDK_PAY_FLAG
                                        msg.obj = payResult
                                        mHandler.sendMessage(msg)
                                    }
                                    val payThread = Thread(payRunnable)
                                    payThread.start()
                                } else {
                                    MyToastUtil.showError(result.msg)
                                }
                            }
                        } catch (e: Exception) {
                            MyToastUtil.showInfo(e.message)
                        }
                    }
                } else {
                    MyToastUtil.showError("正在拉取VIP价格，请稍候...")
                }
            }
        }

        customView.btn_vip_wxpay.setOnClickListener {
            MMKVUtils.put(MyConstants.SP_KEY_PAY_TYPE, 0)
            if (MyUtil.checkPackageInstalled(requireContext(), "com.tencent.mm", MyConstants.URL_WXPAY)) {
                if (vipIndex >= 0) {

                    lifecycleScope.launch(Dispatchers.IO) {
                        try {
                            val result = viewModel.vipRepository.makeWXOrderVIP(
                                moneyList[vipIndex].currentPrice,
                                moneyList[vipIndex].originalPrice, "", "", ""
                            )
                            withContext(Dispatchers.Main) {
                                if (result.code == 200) {
                                    val request = com.tencent.mm.opensdk.modelpay.PayReq()
                                    request.appId = result.data!!.appId
                                    request.partnerId = result.data.partnerId
                                    request.prepayId = result.data.prepayId
                                    request.packageValue = result.data.packageValue
                                    request.nonceStr = result.data.nonceStr
                                    request.timeStamp = result.data.timeStamp
                                    request.sign = result.data.sign
                                    api.sendReq(request)
                                } else {
                                    MyToastUtil.showError(result.msg)
                                }
                            }
                        } catch (e: Exception) {
                            MyToastUtil.showInfo(e.message)
                        }
                    }
                } else {
                    MyToastUtil.showError("正在拉取VIP价格，请稍候...")
                }
            }
        }
    }


    private fun chooseMoneyItem(vipIndex: Int) {
        var myVipIndex = 0
        val rgMoneys = customView.rg_money
        myVipIndex = if (vipIndex > rgMoneys.children.count()) {
            rgMoneys.children.count()
        } else {
            vipIndex
        }

        rgMoneys.children.forEachIndexed { index, view ->
            if (myVipIndex == index) {
                view.cl_vip_money_item.setBackgroundResource(R.drawable.shape_vip_money_select)
            } else {
                view.cl_vip_money_item.setBackgroundResource(R.drawable.shape_vip_money_not_select)
            }
        }
    }

    private val mHandler = Handler {
        if (it.what == SDK_PAY_FLAG) {
            val payResult = PayResult(it.obj as Map<String, String>)
            when (payResult.resultStatus) {
                "6001" -> {
                    MyToastUtil.showInfo("支付失败")
                }

                else -> {
                    paySucceed(SDK_PAY_FLAG)
                }
            }
        }
        true
    }

    override fun onResume() {
        super.onResume()
        viewModel.refreshVIPMoney()
        LogUtils.d("refresh")
    }

    private fun paySucceed(payType: Int) {

        lifecycleScope.launch(Dispatchers.IO) {
            try {
                val result = viewModel.vipRepository.queryOrder()
                if (result.code == 200) {
                    withContext(Dispatchers.Main) {
                        MyToastUtil.showInfo("支付成功")
                    }
                    val result2 = loginViewModel.loginRepository.refreshState()
                    withContext(Dispatchers.Main) {
                        if (result2.code == 200) {
                            MMKVUtils.put(MyConstants.SP_KEY_USERNAME, result2.data!!.username)
                            MMKVUtils.put(MyConstants.SP_KEY_VIP_STATE, result2.data.vipState)
                            MMKVUtils.put(MyConstants.SP_KEY_VIP_END_TIME, result2.data.vipEndTime)
                            MMKVUtils.put(MyConstants.SP_KEY_AVATAR, result2.data.avatar)
                            MMKVUtils.put(MyConstants.SP_KEY_FORCE_QUITE_PWD, result2.data.unlockPwd)
                            MMKVUtils.put(MyConstants.SP_KEY_BIND_MOBILE, result2.data.bindMobile)

                            LiveEventBus.get(LiveBus.PAY_FOR_VIP_FINISH, String::class.java).post("")
                        } else {
                            MyToastUtil.showError(result.msg)
                        }
                        if (vipIndex == moneyList.size - 1 || !notifyForever) {
                            <EMAIL>()
                        } else {
                            NormalDialog(this@VIPBottomSheetDialogFragment).apply {
                                setTitle("温馨提示")
                                setContent("购买后，1小时之内可补差价升级到永久版，是否现在升级？")
                                isCancelable = false
                                setOnNormalOKClickListener("现在升级", object : OnOKClickListener {
                                    override fun onclick() {
                                        vipIndex = 3
                                        viewModel.refreshVIPMoney()
                                    }
                                })
                                setOnNormalCancelClickListener("我再想想", object : OnCancelClickListener {
                                    override fun onclick() {
                                        <EMAIL>()
                                    }
                                })
                                showDialog()
                            }
                        }

                    }
                } else {
                    withContext(Dispatchers.Main) {
                        MyToastUtil.showError(result.msg)
                    }
                }
            } catch (e: Exception) {
                MyToastUtil.showInfo(e.message)
            }
        }
    }

}