package com.lijianqiang12.silent.component.activity.me.invite_gift

import android.animation.AnimatorSet
import android.animation.ObjectAnimator
import android.animation.ValueAnimator
import android.content.Intent
import android.graphics.*
import android.os.Bundle
import android.view.View
import android.view.animation.AccelerateDecelerateInterpolator
import androidx.lifecycle.lifecycleScope
import androidx.recyclerview.widget.LinearLayoutManager
import androidx.recyclerview.widget.RecyclerView
import com.lijianqiang12.silent.utils.MMKVUtils
import com.lijianqiang12.silent.MyConstants
import com.lijianqiang12.silent.R
import com.lijianqiang12.silent.data.model.net.MyRetrofitClient
import com.lijianqiang12.silent.component.activity.TheWebViewActivity
import com.lijianqiang12.silent.component.activity.base.BaseActivity
import com.lijianqiang12.silent.component.activity.custom.dialog.MyProgressDialog
import com.lijianqiang12.silent.utils.ImageUtil
import com.lijianqiang12.silent.utils.MyToastUtil
import com.lijianqiang12.silent.utils.dpToPixel
import com.yzq.zxinglibrary.encode.CodeCreator
import kotlinx.android.synthetic.main.activity_invite_gift.*
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.launch
import kotlinx.coroutines.withContext
import kotlin.collections.ArrayList


class InviteGiftActivity : BaseActivity() {

    private lateinit var myDialog: MyProgressDialog
    private val animatorSetsuofang = AnimatorSet() //组合动画
    private lateinit var recyclerview: RecyclerView
    private lateinit var mAdapter: InviteHistoryAdapter

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        setContentView(R.layout.activity_invite_gift)
        iv_invite_gift_return.setOnClickListener { finish() }

        myDialog = MyProgressDialog(activity = this)
        cl_invite_gift_rule.setOnClickListener {
            val intent = Intent(this, TheWebViewActivity::class.java)
            intent.putExtra("url", "https://www.shuge888.com/produce/offphone/offphonesharerule")
            startActivity(intent)
        }

        val scaleX: ObjectAnimator = ObjectAnimator.ofFloat(btn_invite_gift_share, "scaleX", 1f, 1.2f, 1f) //后几个参数是放大的倍数
        val scaleY: ObjectAnimator = ObjectAnimator.ofFloat(btn_invite_gift_share, "scaleY", 1f, 1.2f, 1f)
        scaleX.repeatCount = ValueAnimator.INFINITE //永久循环
        scaleY.repeatCount = ValueAnimator.INFINITE
        animatorSetsuofang.duration = 2000 //时间
        animatorSetsuofang.interpolator = AccelerateDecelerateInterpolator()
        animatorSetsuofang.play(scaleX).with(scaleY) //两个动画同时开始

        animatorSetsuofang.start() //开始

        recyclerview = rv_invite_history
        recyclerview.layoutManager = LinearLayoutManager(this)
        mAdapter = InviteHistoryAdapter(R.layout.item_invite_history, mutableListOf())
//        mAdapter.animationEnable = true
        recyclerview.adapter = mAdapter

        getData()

        btn_invite_gift_share.setOnClickListener {
//            XXPermissions.with(this) // 申请安装包权限
//                .permission(com.hjq.permissions.Permission.MANAGE_EXTERNAL_STORAGE)
//                .request(object : OnPermissionCallback {
//                    override fun onGranted(permissions: List<String>, all: Boolean) {
            val userId = MMKVUtils.getInt(MyConstants.SP_KEY_USER_ID, -1)
            val codePic = CodeCreator.createQRCode("https://shareoffphone.shuge888.com?code=${userId}&isInvite=1", 184, 184, null)
            val topPic = BitmapFactory.decodeResource(<EMAIL>, R.drawable.ic_invite_gift_share_top)
            val iconPic = BitmapFactory.decodeResource(<EMAIL>, R.drawable.silent_120h)

            val bgWidth = topPic.width
            val bgHeight = topPic.height

            val newBitmap = Bitmap.createBitmap(bgWidth, bgHeight + 232, Bitmap.Config.ARGB_8888)
            val cv = Canvas(newBitmap)
            cv.drawColor(resources.getColor(R.color.colorWhiteBackground))
            cv.drawBitmap(topPic, 0f, 0f, null) //在 0，0坐标开始画入bg
            cv.drawBitmap(codePic, 24f, bgHeight.toFloat() + 24f, null) //在 0，0坐标开始画入fg ，可以从任意位置画入
            cv.drawBitmap(iconPic, bgWidth - 184f, bgHeight.toFloat() + 56, null) //在 0，0坐标开始画入fg ，可以从任意位置画入

            val textPaint = Paint(Paint.ANTI_ALIAS_FLAG)
            textPaint.textSize = dpToPixel(10f)
            textPaint.textAlign = Paint.Align.LEFT
            //写字
            textPaint.color = Color.parseColor("#88000000")
            textPaint.style = Paint.Style.FILL
            cv.drawText("扫描左侧二维码下载体验", 224f, bgHeight + 164f, textPaint)

            textPaint.textSize = dpToPixel(14f)
            textPaint.color = Color.parseColor("#dd000000")
            cv.drawText("一键锁机，专注工作、学习", 224f, bgHeight + 96f, textPaint)


            ImageUtil.shareImage(this, newBitmap)

//            val image = UMImage(this@InviteGiftActivity, newBitmap)//bitmap文件
//            val thumb = UMImage(this@InviteGiftActivity, newBitmap)
//            image.setThumb(thumb)
//            val shareBoardConfig = ShareBoardConfig()
//            shareBoardConfig.setIndicatorVisibility(false)
//            ShareAction(this@InviteGiftActivity).withMedia(image).withText("")
//                .setDisplayList(
//                    SHARE_MEDIA.QQ, SHARE_MEDIA.QZONE, SHARE_MEDIA.WEIXIN, SHARE_MEDIA.WEIXIN_CIRCLE,
//                    SHARE_MEDIA.SINA, SHARE_MEDIA.MORE
//                )
//                .open(shareBoardConfig)

        }


    }

    override fun onResume() {
        super.onResume()
        tv_invite_gift_who.startViewAnimator()
        animatorSetsuofang.resume() //开始
    }

    override fun onPause() {
        super.onPause()
        tv_invite_gift_who.stopViewAnimator()
        animatorSetsuofang.pause()
    }
//    override fun onStop() {
//        super.onStop()
//    }

    override fun onDestroy() {
        super.onDestroy()
//        UMShareAPI.get(this).release()
        animatorSetsuofang.cancel()
    }


//    override fun onActivityResult(requestCode: Int, resultCode: Int, data: Intent?) {
//        super.onActivityResult(requestCode, resultCode, data)
//        UMShareAPI.get(this).onActivityResult(requestCode, resultCode, data)//QQ分享回调需要本activity或子fragment都在这里实现
//    }


    private fun getData() {
        myDialog.show()
        lifecycleScope.launch(Dispatchers.IO) {
            try {
                val result = MyRetrofitClient.service.getInvitePageInfo()
                withContext(Dispatchers.Main) {
                    if (result.code == 200) {
                        result.data?.let {
                            tv_days.text = it.myInviteDays.toString()
                            tv_my_invite_days_left.text = it.myInviteDaysLeft

                            if (it.resultMyInviteHistoryList.size > 0) {
                                cl_invite_empty_view.visibility = View.GONE
                                cl_invite_history.visibility = View.VISIBLE
                                mAdapter.setNewInstance(it.resultMyInviteHistoryList)
                            } else {
                                cl_invite_empty_view.visibility = View.VISIBLE
                                cl_invite_history.visibility = View.GONE
                            }

                            //设置数据
                            val list: MutableList<String> = ArrayList()

                            it.resultInviteExampleList.forEach { inviteExampleInfo ->
                                list.add(inviteExampleInfo.inviteExampleInfo)
                            }
//                            list.add("liqing  已获得30天会员")
//                            list.add("l****g  已获得30天会员")
//                            list.add("我****啊  已获得130天会员")
//                            list.add("山****呢  已获得2230天会员")
//                            list.add("Charles  已获得30天会员")
                            //调用setDatas(List<String>)方法后,TextBannerView自动开始轮播
                            //注意：此方法目前只接受List<String>类型
                            tv_invite_gift_who.setDatas(list)
//        val drawable: Drawable = resources.getDrawable(R.mipmap.ic_launcher)

                            /**这里可以设置带图标的数据（1.0.2新方法），比setDatas方法多了带图标参数；
                             * 第一个参数：数据 。
                             * 第二参数：drawable.
                             * 第三参数:drawable尺寸。
                             * 第四参数:图标位置仅支持Gravity.LEFT、Gravity.TOP、Gravity.RIGHT、Gravity.BOTTOM
                             */
//        tv_invite_gift_who.setDatasWithDrawableIcon(list, drawable, 18, Gravity.CENTER)


                            //设置TextBannerView点击监听事件，返回点击的data数据, 和position位置


                            //设置TextBannerView点击监听事件，返回点击的data数据, 和position位置
//        tv_invite_gift_who.setItemOnClickListener { data, position ->
//            LogUtils.i("点击了：", "$position>>$data")
//        }
                        }
                    } else {
                        MyToastUtil.showInfo(result.msg)
                    }
                }
            } catch (e: Exception) {
                withContext(Dispatchers.Main) {
                    MyToastUtil.showInfo(e.message)
                }
            } finally {
                withContext(Dispatchers.Main) {
                    myDialog.dismiss()
                }
            }
        }
    }
}