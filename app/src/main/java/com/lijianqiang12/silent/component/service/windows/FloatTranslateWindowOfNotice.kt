package com.lijianqiang12.silent.component.service.windows

import android.app.Service
import android.content.Context
import android.content.pm.ActivityInfo
import android.graphics.PixelFormat
import android.os.Build
import android.view.Gravity
import android.view.LayoutInflater
import android.view.View
import android.view.WindowManager
import android.widget.TextView
import com.blankj.utilcode.util.LogUtils
import com.blankj.utilcode.util.ScreenUtils
import com.lijianqiang12.silent.MyConstants
import com.lijianqiang12.silent.R
import com.lijianqiang12.silent.TheApplication
import com.lijianqiang12.silent.utils.MyScreenUtils
import com.lijianqiang12.silent.utils.MyToastUtil
import com.lijianqiang12.silent.utils.MyWindowUtil
import com.lijianqiang12.silent.utils.PermissionUtil
import kotlinx.coroutines.Job

class FloatTranslateWindowOfNotice(val context: Context, val callback: () -> Unit) {

    //    private var windowCreated = false
//    private var windowShowing = false
    private lateinit var params: WindowManager.LayoutParams
    private lateinit var clickableParams: WindowManager.LayoutParams

    //    private lateinit var params0: WindowManager.LayoutParams
//    private lateinit var params1: WindowManager.LayoutParams
//    private lateinit var params2: WindowManager.LayoutParams
    private lateinit var windowManager: WindowManager
    private var layout: View? = null
    private var clickableLayout: View? = null
//    private var layout0: View? = null
//    private var layout1: View? = null
//    private var layout2: View? = null

//    var job: Job? = null

    private fun createClickableWindow() {

        clickableParams = WindowManager.LayoutParams()
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.LOLLIPOP_MR1
            && PermissionUtil.isAccessibilitySettingsOn(MyConstants.ACCESS_ABILITY_NAME, context.applicationContext)
            && TheApplication.getInstance().globalParams.accessibilityService != null
        ) {
            clickableParams.type = WindowManager.LayoutParams.TYPE_ACCESSIBILITY_OVERLAY
            windowManager = TheApplication.getInstance().globalParams.accessibilityService!!.getSystemService(Context.WINDOW_SERVICE) as WindowManager
        } else if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
            clickableParams.type = WindowManager.LayoutParams.TYPE_APPLICATION_OVERLAY
            windowManager = context.applicationContext.getSystemService(Context.WINDOW_SERVICE) as WindowManager
        } else {
            clickableParams.type = WindowManager.LayoutParams.TYPE_SYSTEM_ERROR
            windowManager = context.applicationContext.getSystemService(Context.WINDOW_SERVICE) as WindowManager
        }

        clickableParams.format = PixelFormat.TRANSLUCENT

        clickableParams.flags = clickableParams.flags or WindowManager.LayoutParams.FLAG_FULLSCREEN or
                WindowManager.LayoutParams.FLAG_LAYOUT_IN_SCREEN or
                WindowManager.LayoutParams.FLAG_LAYOUT_NO_LIMITS

        clickableParams.gravity = Gravity.START or Gravity.TOP
        clickableParams.x = 0
        clickableParams.y = 0
        clickableParams.screenOrientation = if (MyScreenUtils.isScreenOrientationPortrait(context as Service)) {
            ActivityInfo.SCREEN_ORIENTATION_PORTRAIT
        } else {
            ActivityInfo.SCREEN_ORIENTATION_LANDSCAPE
        }
        clickableParams.width = ScreenUtils.getScreenWidth()
        clickableParams.height = ScreenUtils.getScreenHeight()



        clickableLayout = LayoutInflater.from(context).inflate(R.layout.layout_deny_fast_open_notice, null)


        clickableLayout?.let {
            it.findViewById<TextView>(R.id.tv_notice_active).setOnClickListener {
                callback()
            }
        }


//        windowCreated = true
    }

    private fun createWindow() {

        params = WindowManager.LayoutParams()
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.LOLLIPOP_MR1
            && PermissionUtil.isAccessibilitySettingsOn(MyConstants.ACCESS_ABILITY_NAME, context.applicationContext)
            && TheApplication.getInstance().globalParams.accessibilityService != null
        ) {
            params.type = WindowManager.LayoutParams.TYPE_ACCESSIBILITY_OVERLAY
            windowManager = TheApplication.getInstance().globalParams.accessibilityService!!.getSystemService(Context.WINDOW_SERVICE) as WindowManager
        } else if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
            params.type = WindowManager.LayoutParams.TYPE_APPLICATION_OVERLAY
            windowManager = context.applicationContext.getSystemService(Context.WINDOW_SERVICE) as WindowManager
        } else {
            params.type = WindowManager.LayoutParams.TYPE_SYSTEM_ERROR
            windowManager = context.applicationContext.getSystemService(Context.WINDOW_SERVICE) as WindowManager
        }

        params.format = PixelFormat.TRANSLUCENT

        params.flags = params.flags or WindowManager.LayoutParams.FLAG_FULLSCREEN or
                WindowManager.LayoutParams.FLAG_LAYOUT_IN_SCREEN or
                WindowManager.LayoutParams.FLAG_LAYOUT_NO_LIMITS or
                WindowManager.LayoutParams.FLAG_NOT_TOUCHABLE or
                WindowManager.LayoutParams.FLAG_NOT_FOCUSABLE

        params.gravity = Gravity.START or Gravity.TOP
        params.x = 0
        params.y = 0
        params.screenOrientation = if (MyScreenUtils.isScreenOrientationPortrait(context as Service)) {
            ActivityInfo.SCREEN_ORIENTATION_PORTRAIT
        } else {
            ActivityInfo.SCREEN_ORIENTATION_LANDSCAPE
        }
        params.width = ScreenUtils.getScreenWidth()
        params.height = ScreenUtils.getScreenHeight()



        layout = LayoutInflater.from(context).inflate(R.layout.layout_deny_fast_open_notice, null)


        layout?.let {
            it.findViewById<TextView>(R.id.tv_notice_active).visibility = View.GONE
        }


//        windowCreated = true
    }

    fun showDenyWindow(text: String) {
        LogUtils.d("showDenyWindow")
        if (layout == null) {
            createWindow()
        }
        if (clickableLayout == null) {
            createClickableWindow()
        }

//        layout?.let {
//            it.findViewById<TextView>(R.id.tv_notice_active).setOnClickListener {
//                LogUtils.d("DENY_NOTICE_CLOSE 1")
//                hideDenyWindow()
//                callback()
//            }
//        }

        layout!!.findViewById<TextView>(R.id.tv_notice_text).text = text
        if (!MyWindowUtil.isWindowShowing(layout!!)) {
            try {
                windowManager.addView(layout!!, params)
            } catch (e: Exception) {
                LogUtils.d("NoticeFloatWindow未成功获取到悬浮窗权限，请到系统设置或安全管家中重新授予，若已经授予，则可能是系统BUG，请重启手机${e.toString()}")
                MyToastUtil.showError("NoticeFloatWindow未成功获取到悬浮窗权限，请到系统设置或安全管家中重新授予，若已经授予，则可能是系统BUG，请重启手机${e.toString()}")
            }

//            job = GlobalScope.launch(Dispatchers.IO) {
//                delay(delay)
//                withContext(Dispatchers.Main) {
//                    callback()
//                    hideDenyWindow()
//                }
//            }
        }

    }

    fun showClickableDenyWindow(text: String, showButton: Boolean) {
        LogUtils.d("showDenyWindow")
        if (layout == null) {
            createWindow()
        }
        if (clickableLayout == null) {
            createClickableWindow()
        }

//        layout?.let {
//            it.findViewById<TextView>(R.id.tv_notice_active).setOnClickListener {
//                LogUtils.d("DENY_NOTICE_CLOSE 1")
//                hideDenyWindow()
//                callback()
//            }
//        }

        if (showButton) {
            clickableLayout!!.findViewById<TextView>(R.id.tv_notice_active).visibility = View.VISIBLE
        } else {
            clickableLayout!!.findViewById<TextView>(R.id.tv_notice_active).visibility = View.GONE
        }

        clickableLayout!!.findViewById<TextView>(R.id.tv_notice_text).text = text
        if (!MyWindowUtil.isWindowShowing(clickableLayout!!)) {
            try {
                windowManager.addView(clickableLayout!!, clickableParams)
            } catch (e: Exception) {
                LogUtils.d("NoticeFloatWindow未成功获取到悬浮窗权限，请到系统设置或安全管家中重新授予，若已经授予，则可能是系统BUG，请重启手机${e.toString()}")
                MyToastUtil.showError("NoticeFloatWindow未成功获取到悬浮窗权限，请到系统设置或安全管家中重新授予，若已经授予，则可能是系统BUG，请重启手机${e.toString()}")
            }

//            job = GlobalScope.launch(Dispatchers.IO) {
//                delay(delay)
//                withContext(Dispatchers.Main) {
//                    callback()
//                    hideDenyWindow()
//                }
//            }
        }

    }

    fun hideDenyWindow() {
        if (layout != null) {
            if (MyWindowUtil.isWindowShowing(layout!!)) {
                try {
                    windowManager.removeView(layout)
                } catch (e: Exception) {
                    MyToastUtil.showError(e.message!!)
                }
            }
        }
    }

    fun hideClickableDenyWindow() {
        if (clickableLayout != null) {
            if (MyWindowUtil.isWindowShowing(clickableLayout!!)) {
                try {
                    windowManager.removeView(clickableLayout)
                } catch (e: Exception) {
                    MyToastUtil.showError(e.message!!)
                }
            }
        }
    }
}