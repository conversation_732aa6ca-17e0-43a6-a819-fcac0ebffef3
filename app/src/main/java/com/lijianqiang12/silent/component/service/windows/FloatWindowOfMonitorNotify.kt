package com.lijianqiang12.silent.component.service.windows

import android.animation.ObjectAnimator
import android.annotation.SuppressLint
import android.content.Context
import android.graphics.PixelFormat
import android.graphics.drawable.Drawable
import android.os.Build
import android.os.Handler
import android.view.Gravity
import android.view.LayoutInflater
import android.view.View
import android.view.WindowManager
import android.widget.ImageView
import android.widget.TextView
import com.blankj.utilcode.util.ConvertUtils
import com.blankj.utilcode.util.ScreenUtils
import com.lijianqiang12.silent.R
import com.lijianqiang12.silent.utils.MyToastUtil
import com.lijianqiang12.silent.utils.MyWindowUtil

class FloatWindowOfMonitorNotify(val context: Context) {

    //    private var windowCreated = false
//    private var windowShowing = false
    private lateinit var params: WindowManager.LayoutParams
    private lateinit var windowManager: WindowManager
    private var layout: View? = null

    @SuppressLint("ClickableViewAccessibility")
    private fun createRestView() {
        params = WindowManager.LayoutParams()

        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
            params.type = WindowManager.LayoutParams.TYPE_APPLICATION_OVERLAY
        } else {
            params.type = WindowManager.LayoutParams.TYPE_SYSTEM_ALERT
        }

        windowManager = context.applicationContext.getSystemService(Context.WINDOW_SERVICE) as WindowManager

        params.format = PixelFormat.TRANSLUCENT

        params.flags = params.flags or WindowManager.LayoutParams.FLAG_NOT_FOCUSABLE or
                WindowManager.LayoutParams.FLAG_NOT_TOUCH_MODAL or
                WindowManager.LayoutParams.FLAG_HARDWARE_ACCELERATED
        params.gravity = Gravity.START or Gravity.TOP
        params.x = 0
        params.y = ConvertUtils.dp2px(40f)//ScreenUtils.getScreenHeight() / 8
        params.width = WindowManager.LayoutParams.WRAP_CONTENT
        params.height = WindowManager.LayoutParams.WRAP_CONTENT

        layout = LayoutInflater.from(context).inflate(R.layout.layout_lock_view_monitor, null)

    }

    fun showWindow(text: String, icon: Drawable) {
        showWindow(text, icon, false)
    }

    fun hideWindow() {
        hideWindow(false)
    }

    fun showWindow(text: String, icon: Drawable, animate: Boolean) {
        if (layout == null) {
            createRestView()
        }

        //是否显示app限时提醒
        if (!MyWindowUtil.isWindowShowing(layout!!)) {
//            windowShowing = true

            layout?.let {
                it.findViewById<TextView>(R.id.tv_lock_view_monitor).text = text
                it.findViewById<ImageView>(R.id.civ_monitor_app_icon).setImageDrawable(icon)

                try {
                    windowManager.addView(it, params)

                } catch (e: Exception) {
                    MyToastUtil.showError("MonitorNotifyFloatWindow未成功获取到悬浮窗权限，请到系统设置或安全管家中重新授予，若已经授予，则可能是系统BUG，请重启手机")
//                    windowShowing = false

                }
                if (animate) {
                ObjectAnimator.ofFloat(it, "translationX", ScreenUtils.getScreenWidth() * -1f, 0f)
                    .setDuration(2000)
                    .start()

            }
            }

        }
    }

    fun hideWindow(animate: Boolean) {
        if (layout != null) {
            if (MyWindowUtil.isWindowShowing(layout!!)) {
                try {
                    if (animate) {
                        ObjectAnimator.ofFloat(layout!!, "translationX", 0f, ScreenUtils.getScreenWidth() * -1f)
                            .setDuration(2000)
                            .start()
                        Handler().postDelayed(kotlinx.coroutines.Runnable {
                            try {
                                windowManager.removeView(layout!!)
                            }catch (e:Exception){

                            }
//                            windowShowing = false
                        }, 2500)
                    } else {
                        windowManager.removeView(layout!!)
                    }
                } catch (e: Exception) {
                    MyToastUtil.showError("未成功获取到悬浮窗权限，请到系统设置或安全管家中重新授予，若已经授予，则可能是系统BUG，请重启手机")

                }
            }
        }
    }

    fun showAndHide(text: String, icon: Drawable) {
//        if (layout == null) {
//            createRestView()
//        }
//        if (windowShowing) return
        showWindow(text, icon, true)
        Handler().postDelayed(kotlinx.coroutines.Runnable {
            hideWindow(true)
        }, 8000)
    }
}