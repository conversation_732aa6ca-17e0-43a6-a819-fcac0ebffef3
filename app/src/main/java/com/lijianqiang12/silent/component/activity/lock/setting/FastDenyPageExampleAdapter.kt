package com.lijianqiang12.silent.component.activity.lock.setting

import android.view.View
import android.widget.TextView
import androidx.constraintlayout.widget.ConstraintLayout
import com.blankj.utilcode.util.LogUtils
import com.bumptech.glide.Glide
import com.bumptech.glide.load.engine.DiskCacheStrategy
import com.chad.library.adapter.base.BaseQuickAdapter
import com.chad.library.adapter.base.module.LoadMoreModule
import com.chad.library.adapter.base.viewholder.BaseViewHolder
import com.lijianqiang12.silent.R
import com.lijianqiang12.silent.data.model.net.pojos.FastDenyPageExample
import com.lijianqiang12.silent.utils.MyGsonUtil


class FastDenyPageExampleAdapter(layoutRes: Int, list: MutableList<FastDenyPageExample>)
    : BaseQuickAdapter<FastDenyPageExample, BaseViewHolder>(layoutRes, list), LoadMoreModule {

    override fun convert(holder: BaseViewHolder, item: FastDenyPageExample) {
        Glide.with(context).load(item.icon)
            //.transition(DrawableTransitionOptions.withCrossFade())
            .diskCacheStrategy(DiskCacheStrategy.AUTOMATIC)
            .into(holder.getView(R.id.iv_item_fast_deny_page_example_icon))
        holder.getView<TextView>(R.id.tv_item_fast_deny_page_example_name).text = item.name

        //格式化的好看一点
//        val gson = GsonBuilder().setPrettyPrinting().create()
//        val formatPagesString = gson.toJson(item.pages)
//        holder.getView<TextView>(R.id.tv_item_fast_deny_page_example_pkg).text = formatPagesString


        val denyPageList = MyGsonUtil.getDenyPageList()
        LogUtils.d()
        var ifSelect = false
        denyPageList.forEach {
            if (it.id == item.id && it.valid) {
                ifSelect = true
            }
        }

        if (ifSelect) {
            holder.getView<ConstraintLayout>(R.id.cl_fast_deny_page_example_cover).visibility = View.VISIBLE
        } else {
            holder.getView<ConstraintLayout>(R.id.cl_fast_deny_page_example_cover).visibility = View.GONE
        }


//        viewHolder.getView<TextView>(R.id.tv_name).text = item.username
//
//
//        viewHolder.getView<TextView>(R.id.tv_word).text = item.word
//
//        when (item.vipState) {
//            0 -> {
//                viewHolder.getView<TextView>(R.id.tv_vip).visibility = View.INVISIBLE
//            }
//            1 -> {
//                viewHolder.getView<TextView>(R.id.tv_vip).visibility = View.VISIBLE
//                viewHolder.getView<TextView>(R.id.tv_vip).text = "VIP"
//                viewHolder.getView<TextView>(R.id.tv_vip).setTextColor(context.resources.getColor(R.color.colorBackText))
//                viewHolder.getView<TextView>(R.id.tv_vip).setBackgroundResource(R.drawable.shape_get_vip_gradient_2dp)
//            }
//            2 -> {
//                viewHolder.getView<TextView>(R.id.tv_vip).visibility = View.VISIBLE
//                viewHolder.getView<TextView>(R.id.tv_vip).text = "SVIP"
//                viewHolder.getView<TextView>(R.id.tv_vip).setTextColor(context.resources.getColor(R.color.colorWhiteText))
//                viewHolder.getView<TextView>(R.id.tv_vip).setBackgroundResource(R.drawable.shape_get_vip_black_gradient_2dp)
//            }
//        }
//
//
//        viewHolder.getView<TextView>(R.id.tv_brand).text = "${item.brand}  ${item.model}"
//        viewHolder.getView<TextView>(R.id.tv_punch_time).text = TimeUtil.formatTimeLikeQQ(item.time)
//
//        when (item.gender) {
//            0 -> {
//                viewHolder.getView<ImageView>(R.id.iv_gender).visibility = View.GONE
//            }
//            1 -> {
//                viewHolder.getView<ImageView>(R.id.iv_gender).visibility = View.VISIBLE
//                viewHolder.getView<ImageView>(R.id.iv_gender).setImageResource(R.drawable.ic_male)
//
//            }
//            2 -> {
//                viewHolder.getView<ImageView>(R.id.iv_gender).visibility = View.VISIBLE
//                viewHolder.getView<ImageView>(R.id.iv_gender).setImageResource(R.drawable.ic_female)
//            }
//        }
    }
}