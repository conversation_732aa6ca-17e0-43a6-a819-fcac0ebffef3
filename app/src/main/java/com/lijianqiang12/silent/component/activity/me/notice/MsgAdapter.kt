package com.lijianqiang12.silent.component.activity.me.notice

import android.widget.TextView
import com.chad.library.adapter.base.BaseQuickAdapter
import com.chad.library.adapter.base.module.LoadMoreModule
import com.chad.library.adapter.base.viewholder.BaseViewHolder
import com.lijianqiang12.silent.R
import com.lijianqiang12.silent.data.model.net.pojos.MyMsg

class MsgAdapter(layoutRes: Int, list: MutableList<MyMsg>) : BaseQuickAdapter<MyMsg, BaseViewHolder>(layoutRes, list), LoadMoreModule {

    override fun convert(holder: BaseViewHolder, item: MyMsg) {
        holder.getView<TextView>(R.id.tv_time).text = item.updateTime
        holder.getView<TextView>(R.id.tv_content).text = item.msgContent
    }
}