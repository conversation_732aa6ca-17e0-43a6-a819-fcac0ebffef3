package com.lijianqiang12.silent.component.service.windows

import android.app.Service
import android.content.Context
import android.content.Intent
import android.content.pm.ActivityInfo
import android.graphics.PixelFormat
import android.graphics.Rect
import android.os.Build
import android.view.Gravity
import android.view.LayoutInflater
import android.view.View
import android.view.WindowManager
import android.widget.TextView
import com.blankj.utilcode.util.AppUtils
import com.blankj.utilcode.util.ScreenUtils
import com.lijianqiang12.silent.MyConstants
import com.lijianqiang12.silent.R
import com.lijianqiang12.silent.TheApplication
import com.lijianqiang12.silent.component.activity.AppLimitUnlockActivity
import com.lijianqiang12.silent.component.activity.TheMainActivity
import com.lijianqiang12.silent.component.service.MyAccessibilityService
import com.lijianqiang12.silent.data.model.db.AppLimit
import com.lijianqiang12.silent.data.viewmodel.MonitorViewModel
import com.lijianqiang12.silent.utils.*
import com.lijianqiang12.silent.utils.MyUtil.Companion.getAppLimitTitle
import kotlinx.coroutines.*

class FloatWindowOfNotice(val context: Context, private val monitorViewModel: MonitorViewModel? = null) {

    private lateinit var params: WindowManager.LayoutParams
    private lateinit var windowManager: WindowManager
    private var layout: View? = null
    private var job: Job? = null
    var appLimit: AppLimit? = null
    private var isShowing = false
    private var isSmallWindow = false


    private fun createWindow() {


        params = WindowManager.LayoutParams()

        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.LOLLIPOP_MR1 && MyAccessibilityService.isAccessibilityActive() && context is MyAccessibilityService) {
            params.type = WindowManager.LayoutParams.TYPE_ACCESSIBILITY_OVERLAY
        } else if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
            params.type = WindowManager.LayoutParams.TYPE_APPLICATION_OVERLAY
        } else {
            params.type = WindowManager.LayoutParams.TYPE_SYSTEM_ERROR
        }
        windowManager = context.getSystemService(Context.WINDOW_SERVICE) as WindowManager

        params.format = PixelFormat.TRANSLUCENT

        params.flags = params.flags or WindowManager.LayoutParams.FLAG_FULLSCREEN or
                WindowManager.LayoutParams.FLAG_LAYOUT_IN_SCREEN or
                WindowManager.LayoutParams.FLAG_LAYOUT_NO_LIMITS

        params.gravity = Gravity.START or Gravity.TOP
        params.x = 0
        params.y = 0
        params.screenOrientation = if (MyScreenUtils.isScreenOrientationPortrait(context as Service)) {
            ActivityInfo.SCREEN_ORIENTATION_PORTRAIT
        } else {
            ActivityInfo.SCREEN_ORIENTATION_LANDSCAPE
        }
        params.width = ScreenUtils.getScreenWidth()
        params.height = ScreenUtils.getScreenHeight()


        layout = LayoutInflater.from(context).inflate(R.layout.layout_deny_notice, null)
        layout?.let {
            it.findViewById<TextView>(R.id.tv_notice_back).setOnClickListener {
                job?.cancel()

                GlobalScope.launch(Dispatchers.Main) {
                    closeSmallWindow()
                    withContext(Dispatchers.IO) {
                        delay(200)
                    }
                    hideDenyWindow()
                }

            }
            it.findViewById<TextView>(R.id.tv_notice_active).setOnClickListener {

                job?.cancel()

                GlobalScope.launch(Dispatchers.Main) {
                    closeSmallWindow()
                    withContext(Dispatchers.IO) {
                        delay(200)
                    }
                    if (appLimit == null || MyUtil.isCurrentInTimeRange(appLimit!!.editStartTime, appLimit!!.editEndTime)) {
                        MyToastUtil.showInfo("您可以通过修改对应限时来解除限制")
                        MMKVUtils.put(MyConstants.SP_KEY_PAGE_INDEX, 1)
                        startActivityCompatible(
                            TheApplication.getInstance(),
                            AppUtils.getAppPackageName(),
                            TheMainActivity::class.java.canonicalName!!.toString()
                        )

//                    } else if (MyUtil.isCurrentInTimeRange(appLimit!!.editStartTime, appLimit!!.editEndTime)) {
//                        //在时间发内内，直接解锁
//                        if (monitorViewModel == null) {
//                            MyToastUtil.showSuccess("解除失败，原因：未获取到MonitorViewModel")
//                        } else {
//                            DialogUtil.showFloatingDialog(
//                                title = "解除『${getAppLimitTitle(appLimit!!)}』限时",
//                                content = "是否确定立刻解除该限时？",
//                                positiveButtonText = "确定解除",
//                                positiveButtonListener = {
//                                    appLimit!!.valid = false
//                                    monitorViewModel.updateAppLimit(appLimit!!)
//                                    MyToastUtil.showSuccess("解除成功")
//                                })
//                        }
                    } else {
                        val intent = Intent(TheApplication.getInstance(), AppLimitUnlockActivity::class.java)
                        intent.putExtra("appPkg", appLimit!!.appPkg)
                        intent.putExtra("title", getAppLimitTitle(appLimit!!))
                        intent.putExtra("uuid", appLimit!!.uuid)
                        intent.putExtra("editMoney", appLimit!!.editMoney)

                        startActivityCompatible(
                            TheApplication.getInstance(),
                            AppUtils.getAppPackageName(),
                            AppLimitUnlockActivity::class.java.canonicalName!!.toString(),
                            intent
                        )
                    }

                    hideDenyWindow()
                }


            }
        }

    }

    private fun closeSmallWindow() {
        //不弹出，方便用户点击小窗
        if (!((MyRomUtils.isOppo() || MyRomUtils.isVivo()) && isSmallWindow)) {
            performHome(context, "FloatWindowOfNotice closeSmallOrLargeWindow", useAccessibilityHome = !MyRomUtils.isHuaweiSeries(), launchTransparentActivity = true)
        }

    }


    fun showDenyWindow(text: String, delay: Long, ifShowButton: Boolean = false, appLimit: AppLimit? = null, allowSmallWindowToast: Boolean = true, callback: () -> Unit) {
        if (isShowing) return

        TheApplication.getInstance().globalParams.accessibilityService?.rootInActiveWindow?.let { nodeInfo ->
            val bounds = Rect()
            nodeInfo.getBoundsInScreen(bounds)
            isSmallWindow = allowSmallWindowToast && bounds.height() < ScreenUtils.getScreenHeight()
        }

//        LogUtils.d("showDenyWindow")

        if (layout == null) {
            createWindow()
        }
        layout!!.findViewById<TextView>(R.id.tv_notice_text).text = text

        if (!MyWindowUtil.isWindowShowing(layout!!)) {
            if (ifShowButton) {
                layout!!.findViewById<TextView>(R.id.tv_notice_back).visibility = View.VISIBLE
                layout!!.findViewById<TextView>(R.id.tv_notice_active).visibility = View.VISIBLE
                this.appLimit = appLimit
            } else {
                layout!!.findViewById<TextView>(R.id.tv_notice_back).visibility = View.GONE
                layout!!.findViewById<TextView>(R.id.tv_notice_active).visibility = View.GONE
            }
            try {
                windowManager.addView(layout!!, params)
                isShowing = true
            } catch (e: Exception) {
                MyToastUtil.showError("NoticeFloatWindow未成功获取到悬浮窗权限，请到系统设置或安全管家中重新授予，若已经授予，则可能是系统BUG，请重启手机$e")

            }

            job = GlobalScope.launch(Dispatchers.IO) {
                delay(delay - 500)

                //开始就弹出会导致锁机页面无法显示屏蔽提示
                //结束后再弹出会导致结束时再弹一次屏蔽提示

                closeSmallWindow()

                delay(500)
                withContext(Dispatchers.Main) {
                    hideDenyWindow()
                    callback()
                }


            }
        }

    }

    /**
     * delay:关闭后多久重置标志位
     */
    private suspend fun hideDenyWindow() {
        if (layout != null) {
            if (MyWindowUtil.isWindowShowing(layout!!)) {
                try {
                    windowManager.removeView(layout)

                    if ((MyRomUtils.isOppo() || MyRomUtils.isVivo()) && isSmallWindow) {
                        MyToastUtil.showInfo("请关闭小窗口\n切勿最小化\n否则会一直弹出屏蔽提醒")
                        withContext(Dispatchers.IO) {
                            delay(3000L)
                        }
                    }

                    isShowing = false
                } catch (e: Exception) {
//                    MyToastUtil.showError(e.message!!)
                    MyToastUtil.showError(e.toString())
                }
            }
        }
    }
}