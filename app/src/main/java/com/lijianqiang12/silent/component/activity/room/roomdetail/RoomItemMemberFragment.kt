package com.lijianqiang12.silent.component.activity.room.roomdetail

import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.fragment.app.Fragment
import androidx.lifecycle.lifecycleScope
import androidx.recyclerview.widget.LinearLayoutManager
import androidx.recyclerview.widget.RecyclerView
import com.lijianqiang12.silent.MAX_ID
import com.lijianqiang12.silent.R
import com.lijianqiang12.silent.data.model.net.MyRetrofitClient
import com.lijianqiang12.silent.data.model.net.pojos.RoomDetailMember
import com.lijianqiang12.silent.component.activity.custom.dialog.NormalDialog
import com.lijianqiang12.silent.component.activity.custom.dialog.OnCancelClickListener
import com.lijianqiang12.silent.component.activity.custom.dialog.OnOKClickListener
import com.lijianqiang12.silent.utils.MyToastUtil
import kotlinx.android.synthetic.main.fragment_member.*
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.launch
import kotlinx.coroutines.withContext

// TODO: Rename parameter arguments, choose names that match
// the fragment initialization parameters, e.g. ARG_ITEM_NUMBER
private const val ARG_PARAM1 = "param1"
private const val ARG_PARAM2 = "param2"

private lateinit var recyclerview: RecyclerView
private lateinit var adapter: RoomMemberAdapter

/**
 * A simple [Fragment] subclass.
 * Use the [RoomItemMemberFragment.newInstance] factory method to
 * create an instance of this fragment.
 */
class RoomItemMemberFragment : Fragment() {
    // TODO: Rename and change types of parameters
    private var param1: String? = null
    private var param2: String? = null

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        arguments?.let {
            param1 = it.getString(ARG_PARAM1)
            param2 = it.getString(ARG_PARAM2)
        }
    }

    override fun onCreateView(inflater: LayoutInflater, container: ViewGroup?,
                              savedInstanceState: Bundle?): View? {
        // Inflate the layout for this fragment
        return inflater.inflate(R.layout.fragment_member, container, false)
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        recyclerview = rv_room_member
        recyclerview.layoutManager = LinearLayoutManager(requireContext())
        adapter = RoomMemberAdapter(R.layout.item_room_member, mutableListOf())
        adapter.animationEnable = true
        adapter.loadMoreModule.setOnLoadMoreListener {
            if (adapter.data.size == 0) {
                getNewData(MAX_ID)
            } else {
                getNewData(adapter.data[adapter.data.size - 1].memberTableId.toLong())
            }
        }

        recyclerview.adapter = adapter

        adapter.setOnItemClickListener { adapter, view, position ->

        }

        adapter.setOnItemLongClickListener { adapter, view, position ->
            if (ifMine) {
                NormalDialog(this).apply {
                    setTitle("剔除该用户")
                    setContent("您是否要将此用户从房间中剔除？")
                    setOnNormalOKClickListener("剔除该成员", object : OnOKClickListener {
                        override fun onclick() {
                            <EMAIL>(Dispatchers.IO) {
                                try {
                                    val result = MyRetrofitClient.service.removeMember(currentRoomId.toInt(),
                                            (adapter.data[position] as RoomDetailMember).memberId)
                                    withContext(Dispatchers.Main) {
                                        if (result.code == 200) {
                                            MyToastUtil.showInfo(result.msg)
                                            getNewData(MAX_ID)
                                        } else {
                                            MyToastUtil.showInfo(result.msg)
                                        }
                                    }
                                } catch (e: Exception) {
                                    withContext(Dispatchers.Main) {
                                        MyToastUtil.showInfo(e.message)
                                    }
                                }
                            }
                        }
                    })
                    setOnNormalCancelClickListener(object : OnCancelClickListener {
                        override fun onclick() {

                        }
                    })
                    showDialog()
                }
            }

            true
        }


        getNewData(MAX_ID)

    }

    private fun getNewData(lastId: Long) {
        lifecycleScope.launch(Dispatchers.IO) {
            try {
                val result = MyRetrofitClient.service.getRoomMemberInfo(lastId, currentRoomId)
                withContext(Dispatchers.Main) {
                    if (result.code == 200) {
                        result.data?.let {
                            if (lastId == MAX_ID) {
                                adapter.setNewInstance(it)
                                adapter.loadMoreModule.loadMoreComplete()
                            } else {
                                if (it.isEmpty()) {
                                    adapter.loadMoreModule.loadMoreEnd()
                                } else {
                                    adapter.addData(it)
                                    adapter.loadMoreModule.loadMoreComplete()
                                }
                            }
                            adapter.notifyDataSetChanged()
                        }
                    } else {
                        MyToastUtil.showInfo(result.msg)
                    }
                }
            } catch (e: Exception) {
                withContext(Dispatchers.Main) {
                    MyToastUtil.showInfo(e.message)
                }
            } finally {
//                withContext(Dispatchers.Main) {
//                    srl_punch_card.isRefreshing = false
//                }
            }
        }
    }

    companion object {
        /**
         * Use this factory method to create a new instance of
         * this fragment using the provided parameters.
         *
         * @param param1 Parameter 1.
         * @param param2 Parameter 2.
         * @return A new instance of fragment MemberFragment.
         */
        // TODO: Rename and change types and number of parameters
        @JvmStatic
        fun newInstance(param1: String, param2: String) =
                RoomItemMemberFragment().apply {
                    arguments = Bundle().apply {
                        putString(ARG_PARAM1, param1)
                        putString(ARG_PARAM2, param2)
                    }
                }
    }
}