package com.lijianqiang12.silent.component.activity.me.userinfo

import android.app.ProgressDialog
import android.content.Intent
import android.net.Uri
import android.os.Bundle
import android.provider.MediaStore
import android.text.InputType
import android.widget.Toast
import androidx.lifecycle.Observer
import androidx.lifecycle.lifecycleScope
import com.afollestad.materialdialogs.MaterialDialog
import com.afollestad.materialdialogs.input.input
import com.afollestad.materialdialogs.list.listItems
import com.blankj.utilcode.util.AppUtils
import com.blankj.utilcode.util.LogUtils
import com.lijianqiang12.silent.utils.MMKVUtils
import com.bumptech.glide.Glide
import com.bumptech.glide.load.engine.DiskCacheStrategy
import com.bumptech.glide.request.RequestOptions
import com.jeremyliao.liveeventbus.LiveEventBus
import com.lijianqiang12.silent.LiveBus
import com.lijianqiang12.silent.MyConstants
import com.lijianqiang12.silent.R
import com.lijianqiang12.silent.component.activity.TheLoginActivity
import com.lijianqiang12.silent.component.activity.base.BaseActivity
import com.lijianqiang12.silent.component.activity.custom.dialog.LimitTimeEditDialog
import com.lijianqiang12.silent.component.activity.custom.dialog.NormalDialog
import com.lijianqiang12.silent.component.activity.custom.dialog.OnCancelClickListener
import com.lijianqiang12.silent.component.activity.custom.dialog.OnOKClickListener
import com.lijianqiang12.silent.data.model.net.pojos.AllQQUserInfo
import com.lijianqiang12.silent.data.model.net.pojos.WXUserInfo
import com.lijianqiang12.silent.data.viewmodel.AccountViewModel
import com.lijianqiang12.silent.data.viewmodel.InjectorUtils
import com.lijianqiang12.silent.data.viewmodel.LoginViewModel
import com.lijianqiang12.silent.qqapi.QQLoginListener
import com.lijianqiang12.silent.utils.MyToastUtil
import com.lijianqiang12.silent.utils.MyUtil
import com.lijianqiang12.silent.utils.logoutAll
import com.lijianqiang12.silent.utils.secondToSimpleHm
import com.tencent.cos.xml.CosXmlService
import com.tencent.cos.xml.CosXmlServiceConfig
import com.tencent.cos.xml.exception.CosXmlClientException
import com.tencent.cos.xml.exception.CosXmlServiceException
import com.tencent.cos.xml.listener.CosXmlResultListener
import com.tencent.cos.xml.model.CosXmlRequest
import com.tencent.cos.xml.model.CosXmlResult
import com.tencent.cos.xml.transfer.COSXMLUploadTask.COSXMLUploadTaskResult
import com.tencent.cos.xml.transfer.TransferConfig
import com.tencent.cos.xml.transfer.TransferManager
import com.tencent.mm.opensdk.modelmsg.SendAuth
import com.tencent.mm.opensdk.openapi.WXAPIFactory
import com.tencent.qcloud.core.auth.SessionCredentialProvider
import com.tencent.qcloud.core.http.HttpRequest
import com.tencent.tauth.Tencent
import com.yalantis.ucrop.UCrop
import kotlinx.android.synthetic.main.activity_user_info.*
import kotlinx.android.synthetic.main.widget_edit_time_limit.tv_edit_time_limit
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.launch
import kotlinx.coroutines.withContext
import java.io.File
import java.net.URL


class UserInfoActivity : BaseActivity() {

    private val requestPicCode = 1000001
    private val SAMPLE_CROPPED_IMAGE_NAME = "avatarImage.png"
    private lateinit var dialog: ProgressDialog
    private lateinit var viewModel: AccountViewModel
    private lateinit var loginViewModel: LoginViewModel
    private lateinit var mTencent: Tencent
    private val qqLoginListener = QQLoginListener()


    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        setContentView(R.layout.activity_user_info)

        LiveEventBus.get(LiveBus.QQ_USER_INFO, AllQQUserInfo::class.java).observe(this) {

            lifecycleScope.launch(Dispatchers.IO) {
                try {
                    val result = viewModel.accountRepository.bindQQ(it.openid)
                    withContext(Dispatchers.Main) {
                        if (result.code == 200) {
                            MyToastUtil.showSuccess("绑定成功")
                            viewModel.refreshUserInfo()
                        } else {
                            MyToastUtil.showError(result.msg)
                        }
                    }
                } catch (e: Exception) {
                    MyToastUtil.showInfo(e.message)
                }
            }
        }

        LiveEventBus.get(LiveBus.WX_USER_INFO, WXUserInfo::class.java).observe(this) {
            lifecycleScope.launch(Dispatchers.IO) {
                try {
                    val result = viewModel.accountRepository.bindWX(it.unionid)
                    withContext(Dispatchers.Main) {
                        if (result.code == 200) {
                            MyToastUtil.showSuccess("绑定成功")
                            viewModel.refreshUserInfo()
                        } else {
                            MyToastUtil.showError(result.msg)
                        }
                    }
                } catch (e: Exception) {
                    MyToastUtil.showInfo(e.message)
                }
            }

        }

        var limitTimeStart = MMKVUtils.getInt(MyConstants.SP_KEY_LIMIT_TIME_START_ACCOUNT, -1)
        var limitTimeEnd = MMKVUtils.getInt(MyConstants.SP_KEY_LIMIT_TIME_END_ACCOUNT, -1)
        tv_edit_time_limit.text =
            if (limitTimeStart == -1 || limitTimeEnd == -1) "限制退出账号" else "${secondToSimpleHm(limitTimeStart)}-${secondToSimpleHm(limitTimeEnd)}可改"
        tv_edit_time_limit.setOnClickListener {
            if (!MyUtil.isCurrentInTimeRange(limitTimeStart.toLong(), limitTimeEnd.toLong())) {
                MyToastUtil.showWarning("您设置了仅允许在${secondToSimpleHm(limitTimeStart)}-${secondToSimpleHm(limitTimeEnd)}修改")
                return@setOnClickListener
            }
            LimitTimeEditDialog(this).apply {
                setTitle("在以下时间段可退出登录、注销账号")
                setLimitTime(limitTimeStart, limitTimeEnd)
                setOnOKClickListener(object : LimitTimeEditDialog.OnOKLimitTimeEditListener {
                    override fun onclick(start: Int, end: Int) {
                        limitTimeStart = start
                        limitTimeEnd = end
                        MMKVUtils.put(MyConstants.SP_KEY_LIMIT_TIME_START_ACCOUNT, start)
                        MMKVUtils.put(MyConstants.SP_KEY_LIMIT_TIME_END_ACCOUNT, end)
                        this@UserInfoActivity.tv_edit_time_limit.text = "${secondToSimpleHm(start)}-${secondToSimpleHm(end)}可改"
                    }

                })
                setOnCancelClickListener(object : LimitTimeEditDialog.OnCancelLimitTimeEditListener {
                    override fun onclick() {
                        MMKVUtils.put(MyConstants.SP_KEY_LIMIT_TIME_START_ACCOUNT, -1)
                        MMKVUtils.put(MyConstants.SP_KEY_LIMIT_TIME_END_ACCOUNT, -1)
                        this@UserInfoActivity.tv_edit_time_limit.text = "限制退出账号"
                    }
                })
                show()
            }
        }

        dialog = ProgressDialog(this)
        viewModel = InjectorUtils.provideAccountViewModelFactory(applicationContext).create(AccountViewModel::class.java)
        loginViewModel = InjectorUtils.provideLoginViewModelFactory(applicationContext).create(LoginViewModel::class.java)
        viewModel.userInfoLiveData.observe(this, Observer {
            if (it.state == 0 && it.data != null) {
                val options = RequestOptions()
                    .placeholder(R.drawable.ic_default_head)
                    .fallback(R.drawable.ic_empty)
                    .error(R.drawable.ic_err)
                Glide.with(this)
                    .load(it.data.avatar)
                    //.transition(DrawableTransitionOptions.withCrossFade())
                    .diskCacheStrategy(DiskCacheStrategy.AUTOMATIC)
                    .apply(options)
                    .into(profile_image)
                tv_username.text = it.data.username
                when (it.data.gender) {
                    "男" -> iv_gender.setImageResource(R.drawable.ic_male)
                    "女" -> iv_gender.setImageResource(R.drawable.ic_female)
                    else -> iv_gender.setImageResource(R.drawable.ic_gender)
                }
                tv_word.text = it.data.word
                if (it.data.mobile) {
                    tv_mobile.text = it.data.mobileNumber
                    tv_mobile.background = resources.getDrawable(R.drawable.shape_green_card_4dp)
                    btn_phone.tag = "bind"
                } else {
                    tv_mobile.text = "未绑定"
                    tv_mobile.background = resources.getDrawable(R.drawable.shape_grey_card_4dp)
                    btn_phone.tag = "unbind"
                }
                if (it.data.qq) {
                    tv_qq.text = "已绑定"
                    tv_qq.background = resources.getDrawable(R.drawable.shape_green_card_4dp)
                    btn_qq.tag = "bind"
                } else {
                    tv_qq.text = "未绑定"
                    tv_qq.background = resources.getDrawable(R.drawable.shape_grey_card_4dp)
                    btn_qq.tag = "unbind"
                }
                if (it.data.wx) {
                    tv_wx.text = "已绑定"
                    tv_wx.background = resources.getDrawable(R.drawable.shape_green_card_4dp)
                    btn_wechat.tag = "bind"
                } else {
                    tv_wx.text = "未绑定"
                    tv_wx.background = resources.getDrawable(R.drawable.shape_grey_card_4dp)
                    btn_wechat.tag = "unbind"
                }
                if (it.data.sina) {
                    tv_sina.text = "已绑定"
                    tv_sina.background = resources.getDrawable(R.drawable.shape_green_card_4dp)
                    btn_sina.tag = "bind"
                } else {
                    tv_sina.text = "未绑定"
                    tv_sina.background = resources.getDrawable(R.drawable.shape_grey_card_4dp)
                    btn_sina.tag = "unbind"
                }
            }
        })


        iv_return_user.setOnClickListener { finish() }
        btn_avatar.setOnClickListener {
            changeAvatar()
        }

        btn_username.setOnClickListener {
            MaterialDialog(this)
                .cornerRadius(8.0f)
                .title(text = "修改用户名")
                .input(hint = "用户名", maxLength = 8, allowEmpty = false) { dialog, input ->

                    lifecycleScope.launch(Dispatchers.IO) {
                        try {
                            val result = viewModel.accountRepository.updateUsername(input.toString())
                            withContext(Dispatchers.Main) {
                                if (result.code == 200) {
                                    MyToastUtil.showSuccess("修改成功")
                                    viewModel.refreshUserInfo()
                                } else {
                                    MyToastUtil.showError(result.msg)
                                }
                            }
                        } catch (e: Exception) {
                            MyToastUtil.showInfo(e.message)
                        }
                    }
                }
                .positiveButton(text = "确定")
                .negativeButton(text = "取消")
                .show()
        }

        btn_gender.setOnClickListener {
            val myItems = listOf("男", "女")
            MaterialDialog(this)
                .cornerRadius(8.0f)
                .listItems(items = myItems) { dialog, index, text ->

                    lifecycleScope.launch(Dispatchers.IO) {
                        try {
                            val result = viewModel.accountRepository.updateGender(text.toString())
                            withContext(Dispatchers.Main) {
                                if (result.code == 200) {
                                    MyToastUtil.showSuccess("修改成功")
                                    viewModel.refreshUserInfo()
                                } else {
                                    MyToastUtil.showError(result.msg)
                                }
                            }
                        } catch (e: Exception) {
                            MyToastUtil.showInfo(e.message)
                        }
                    }
                }
                .show()
        }

        btn_word.setOnClickListener {
            MaterialDialog(this)
                .cornerRadius(8.0f)
                .title(text = "修改个性签名")
                .input(hint = "个性签名", maxLength = 50, allowEmpty = false) { dialog, input ->

                    lifecycleScope.launch(Dispatchers.IO) {
                        try {
                            val result = viewModel.accountRepository.updateWord(input.toString())
                            withContext(Dispatchers.Main) {
                                if (result.code == 200) {
                                    MyToastUtil.showSuccess("修改成功")
                                    viewModel.refreshUserInfo()
                                } else {
                                    MyToastUtil.showError(result.msg)
                                }
                            }
                        } catch (e: Exception) {
                            MyToastUtil.showInfo(e.message)
                        }
                    }
                }
                .positiveButton(text = "确定")
                .negativeButton(text = "取消")
                .show()
        }

        btn_phone.setOnClickListener {
            MaterialDialog(this)
                .cornerRadius(8.0f)
                .title(text = "绑定手机号")
                .input(hint = "手机号", maxLength = 11, allowEmpty = false, inputType = InputType.TYPE_CLASS_PHONE) { dialog, input ->

                    lifecycleScope.launch(Dispatchers.IO) {
                        try {
                            val result = loginViewModel.loginRepository.getVerifyCode(1, input.toString())
                            withContext(Dispatchers.Main) {
                                if (result.code == 200) {
                                    MyToastUtil.showSuccess("短信验证码已发送")
                                    MaterialDialog(this@UserInfoActivity)
                                        .cornerRadius(8.0f)
                                        .title(text = "输入验证码")
                                        .input(hint = "验证码", inputType = InputType.TYPE_CLASS_PHONE, maxLength = 4, allowEmpty = false) { dialog, input2 ->

                                            lifecycleScope.launch(Dispatchers.IO) {
                                                try {
                                                    val result2 = viewModel.accountRepository.updateMobile(input.toString(), input2.toString())
                                                    withContext(Dispatchers.Main) {
                                                        if (result2.code == 200) {
                                                            MyToastUtil.showSuccess("修改成功")
                                                            viewModel.refreshUserInfo()
                                                        } else {
                                                            MyToastUtil.showError(result2.msg)
                                                        }
                                                    }
                                                } catch (e: Exception) {
                                                    MyToastUtil.showInfo(e.message)
                                                }
                                            }
                                        }
                                        .positiveButton(text = "确定")
                                        .negativeButton(text = "取消")
                                        .show()

                                } else {
                                    MyToastUtil.showError(result.msg)
                                }
                            }
                        } catch (e: Exception) {
                            MyToastUtil.showInfo(e.message)
                        }
                    }
                }
                .positiveButton(text = "确定")
                .negativeButton(text = "取消")
                .show()
        }

//        val config = UMShareConfig()
//        config.isNeedAuthOnGetUserInfo(true)
//        UMShareAPI.get(this@UserInfoActivity).setShareConfig(config)

        btn_qq.setOnClickListener {
            if (btn_qq.tag == "bind") {
                var notice = "是否要解绑QQ？"
                if (btn_phone.tag != "bind" && btn_wechat.tag != "bind" && btn_sina.tag != "bind" && btn_lianyun.tag != "bind") {
                    notice = "当帐号未绑定任何一种登录方式时，将无法再次登录，确定解绑吗？"
                }
                MaterialDialog(this)
                    .cornerRadius(8.0f)
                    .title(text = "解绑")
                    .message(text = notice)
                    .positiveButton(text = "是") {
                        deleteQQ()
                    }
                    .negativeButton(text = "否")
                    .show()
            } else {
                if (MyUtil.checkPackageInstalled(this, "com.tencent.mobileqq", MyConstants.URL_QQ)) {
                    mTencent = Tencent.createInstance(MyConstants.QQ_APP_ID, this.applicationContext)
                    mTencent.login(this, "get_user_info", qqLoginListener)
                } else {
                    MyToastUtil.showWarning("您的设备没有安装QQ")
                }
            }
        }

        btn_wechat.setOnClickListener {
            if (btn_wechat.tag == "bind") {
                var notice = "是否要解绑微信？"
                if (btn_phone.tag != "bind" && btn_qq.tag != "bind" && btn_sina.tag != "bind" && btn_lianyun.tag != "bind") {
                    notice = "当帐号未绑定任何一种登录方式时，将无法再次登录，确定解绑吗？"
                }
                MaterialDialog(this)
                    .cornerRadius(8.0f)
                    .title(text = "解绑")
                    .message(text = notice)
                    .positiveButton(text = "是") {
                        deleteWX()
                    }
                    .negativeButton(text = "否")
                    .show()
            } else {
                if (MyUtil.checkPackageInstalled(this, "com.tencent.mm", MyConstants.URL_WX)) {
                    val api = WXAPIFactory.createWXAPI(this, MyConstants.WX_APP_ID)
                    val req: SendAuth.Req = SendAuth.Req()
                    req.scope = "snsapi_userinfo" // 只能填 snsapi_userinfo
                    req.state = "wechat_sdk_mindfulness"
                    api.sendReq(req)


                } else {
                    MyToastUtil.showWarning("您的设备没有安装微信")
                }
            }
        }

//        btn_sina.setOnClickListener {
//            if (btn_sina.tag == "bind") {
//                var notice = "是否要解绑微博？"
//                if (btn_phone.tag != "bind" && btn_qq.tag != "bind" && btn_wechat.tag != "bind" && btn_lianyun.tag != "bind") {
//                    notice = "当帐号未绑定任何一种登录方式时，将无法再次登录，确定解绑吗？"
//                }
//                MaterialDialog(this)
//                    .cornerRadius(8.0f)
//                    .title(text = "解绑")
//                    .message(text = notice)
//                    .positiveButton(text = "是") {
//                        deleteSINA()
//                    }
//                    .negativeButton(text = "否")
//                    .show()
//            } else {
//                if (UMShareAPI.get(this).isInstall(this, SHARE_MEDIA.SINA)) {
//                    UMShareAPI.get(this).getPlatformInfo(this, SHARE_MEDIA.SINA, authListener)
//                } else {
//                    MyToastUtil.showWarning("您的设备没有安装微博")
//                }
//            }
//        }

        btn_logout.setOnClickListener {
            var limitTimeStart = MMKVUtils.getInt(MyConstants.SP_KEY_LIMIT_TIME_START_ACCOUNT, -1)
            var limitTimeEnd = MMKVUtils.getInt(MyConstants.SP_KEY_LIMIT_TIME_END_ACCOUNT, -1)
            if (!MyUtil.isCurrentInTimeRange(limitTimeStart.toLong(), limitTimeEnd.toLong()) &&
                MMKVUtils.getInt(MyConstants.SP_KEY_USER_ID, -1) != -1
            ) {
                MyToastUtil.showWarning("您设置了仅允许在${secondToSimpleHm(limitTimeStart)}-${secondToSimpleHm(limitTimeEnd)}退出登录、注销账号")
                return@setOnClickListener
            }


            NormalDialog(this).apply {
                setTitle("温馨提示")
                setContent("确定要退出登录吗？")
                setOnNormalOKClickListener(object : OnOKClickListener {
                    override fun onclick() {
                        <EMAIL>(Dispatchers.Main) {
                            MyToastUtil.showSuccess("已退出登录")

//                            val intent = Intent(requireContext(), TheLoginActivity::class.java)
//                            if (MMKVUtils.getBoolean(MyConstants.SP_KEY_RECENT, false)) {
//                                intent.flags = intent.flags or Intent.FLAG_ACTIVITY_NEW_TASK
//                                intent.flags = intent.flags or Intent.FLAG_ACTIVITY_EXCLUDE_FROM_RECENTS
//                            }
//                            <EMAIL>(intent)
//                            LiveEventBus.get(LiveBus.CLOSE_MAIN_ACTIVITY, String::class.java).post("")
//                            withContext(Dispatchers.IO) {
//                                logoutAll()
//                            }

                            logoutAll()
                            <EMAIL>()
                        }
                    }
                })
                setOnNormalCancelClickListener(object : OnCancelClickListener {
                    override fun onclick() {
                    }
                })
                showDialog()
            }
        }

        btn_delete_account.setOnClickListener {

            var limitTimeStart = MMKVUtils.getInt(MyConstants.SP_KEY_LIMIT_TIME_START_ACCOUNT, -1)
            var limitTimeEnd = MMKVUtils.getInt(MyConstants.SP_KEY_LIMIT_TIME_END_ACCOUNT, -1)
            if (!MyUtil.isCurrentInTimeRange(limitTimeStart.toLong(), limitTimeEnd.toLong()) &&
                MMKVUtils.getInt(MyConstants.SP_KEY_USER_ID, -1) != -1
            ) {
                MyToastUtil.showWarning("您设置了仅允许在${secondToSimpleHm(limitTimeStart)}-${secondToSimpleHm(limitTimeEnd)}退出登录、注销账号")
                return@setOnClickListener
            }

            NormalDialog(this).apply {
                setTitle("严重警告")
                setContent("必读！！！注销账号操作会清空该账号下所有数据，请确认您已了解后果。")
                setOnNormalOKClickListener("确定注销", object : OnOKClickListener {
                    override fun onclick() {
                        NormalDialog(this@UserInfoActivity).apply {
                            setTitle("第二次警告")
                            setContent("必读！！！该操作不可逆请您确认真的不再需要本软件。")
                            setOnNormalOKClickListener("确定注销", object : OnOKClickListener {
                                override fun onclick() {
                                    NormalDialog(this@UserInfoActivity).apply {
                                        setTitle("最后一次提示")
                                        setContent("即将清空您的账号数据，感谢您一直以来对『${AppUtils.getAppName()}』的支持，来日方长，我们有缘再见。")
                                        setOnNormalOKClickListener("确定注销", object : OnOKClickListener {
                                            override fun onclick() {
                                                <EMAIL>(Dispatchers.IO) {
                                                    try {
                                                        val result = viewModel.accountRepository.deleteAllAccount()
                                                        withContext(Dispatchers.Main) {
                                                            if (result.code == 200) {
                                                                MyToastUtil.showSuccess("注销成功")

//                                                                val intent = Intent(this@UserInfoActivity, TheLoginActivity::class.java)
//                                                                if (MMKVUtils.getBoolean(MyConstants.SP_KEY_RECENT, false)) {
//                                                                    intent.flags = intent.flags or Intent.FLAG_ACTIVITY_NEW_TASK
//                                                                    intent.flags = intent.flags or Intent.FLAG_ACTIVITY_EXCLUDE_FROM_RECENTS
//                                                                }
//                                                                <EMAIL>(intent)
//                                                                LiveEventBus.get(LiveBus.CLOSE_MAIN_ACTIVITY, String::class.java).post("")
//                                                                withContext(Dispatchers.IO) {
//                                                                    logoutAll()
//                                                                }

                                                                logoutAll()
                                                                <EMAIL>()
                                                            } else {
                                                                MyToastUtil.showError(result.msg)
                                                            }
                                                        }
                                                    } catch (e: Exception) {
                                                        MyToastUtil.showInfo(e.message)
                                                    }
                                                }
                                            }
                                        })
                                        setOnNormalCancelClickListener("我再想想", object : OnCancelClickListener {
                                            override fun onclick() {
                                            }
                                        })
                                        showDialog()
                                    }
                                }
                            })
                            setOnNormalCancelClickListener("我再想想", object : OnCancelClickListener {
                                override fun onclick() {
                                }
                            })
                            showDialog()
                        }
                    }
                })
                setOnNormalCancelClickListener("我再想想", object : OnCancelClickListener {
                    override fun onclick() {
                    }
                })
                showDialog()
            }


//            NormalDialog(this).apply {
//                setTitle("温馨提示")
//                setContent("注销账号后原来的数据无法保留，请认真思考后再注销。")
//                setOnNormaOKlClickListener(object : OnOKClickListener {
//                    override fun onclick() {
//                        lifecycleScope.launch(Dispatchers.IO) {
//                            val result = viewModel.accountRepository.deleteSINA()
//                            withContext(Dispatchers.Main) {
//                                if (result.code == 200) {
//                                    MyToastUtil.showSuccess(<EMAIL>, "解绑成功")
//                                    viewModel.refreshUserInfo()
//                                } else {
//                                    MyToastUtil.showError(<EMAIL>, result.msg)
//                                }
//                            }
//                        }
//
//
//                    }
//                })
//                setOnNormaCancelClickListener(object : OnCancelClickListener {
//                    override fun onclick() {
//                    }
//                })
//                show()
//            }
        }


        viewModel.refreshUserInfo()
    }

//    override fun onRequestPermissionsResult(requestCode: Int, permissions: Array<String>,
//                                            grantResults: IntArray) {
//        super.onRequestPermissionsResult(requestCode, permissions, grantResults)
//        imagePicker.onRequestPermissionsResult(this, requestCode, permissions, grantResults)
//    }

    override fun onResume() {
        super.onResume()
    }

    private fun changeAvatar() {
        val intent = Intent(Intent.ACTION_PICK, MediaStore.Images.Media.EXTERNAL_CONTENT_URI)
        startActivityForResult(intent, requestPicCode)
//        XXPermissions.with(this) // 申请安装包权限
//            .permission(com.hjq.permissions.Permission.MANAGE_EXTERNAL_STORAGE)
//            .request(object : OnPermissionCallback {
//                override fun onGranted(permissions: List<String>, all: Boolean) {
//                    val intent = Intent(Intent.ACTION_PICK, MediaStore.Images.Media.EXTERNAL_CONTENT_URI)
//                    startActivityForResult(intent, requestPicCode)
//                }
//
//                override fun onDenied(permissions: List<String>, never: Boolean) {
//                    MyToastUtil.showInfo("该功能需要读写存储权限")
//                }
//            })


    }

    fun uploadAvatar(imageUri: Uri) {

        LogUtils.d("imageUri=" + imageUri.toString())
//        val file = UriUtil.getFileByUri(this@UserInfoActivity, imageUri)
        MyUtil.showDialog(dialog, "正在上传，请勿操作...")

//        val appid = "**********"
        val region = "ap-beijing"
        //创建 CosXmlServiceConfig 对象，根据需要修改默认的配置参数
        val cosXmlServiceConfig = CosXmlServiceConfig.Builder()
            .setRegion(region)
            .isHttps(true)
            .builder()

        /**
         * 获取授权服务的 url 地址
         */
//        var url: URL? = null // 后台授权服务的 url 地址
        val url = URL("https", "offphone.shuge888.com", 33333, "/other/tempCos/v1")

        /**
         * 初始化 {@link QCloudCredentialProvider} 对象，来给 SDK 提供临时密钥。
         */
        val qCloudCredentialProvider = SessionCredentialProvider(
            HttpRequest.Builder<String>()
                .url(url)
                .method("GET")
                .build()
        )
        val cosXmlService = CosXmlService(this@UserInfoActivity, cosXmlServiceConfig, qCloudCredentialProvider)
        val bucket = "offphone-avatar-**********" // cos v5 的 bucket格式为：xxx-appid, 如 test-**********
        val cosPath = "${MMKVUtils.getInt(MyConstants.SP_KEY_USER_ID, -1)}/${System.currentTimeMillis()}.png"


        //新版本
        // 初始化 TransferConfig，这里使用默认配置，如果需要定制，请参考 SDK 接口文档
        val transferConfig = TransferConfig.Builder().build()
        val transferManager = TransferManager(cosXmlService, transferConfig) // 初始化 TransferManager
//        val srcPath = File(context.cacheDir, "exampleobject").toString() //本地文件的绝对路径

        // 若存在初始化分块上传的 UploadId，则赋值对应的 uploadId 值用于续传；否则，赋值 null。
        // 当次上传任务的 uploadid 可以在 TransferStateListener 的回调中拿到
        var uploadId: String? = null

        // 上传文件
        val cosXmlUploadTask = transferManager.upload(bucket, cosPath, imageUri, uploadId)

        //设置上传进度回调，这里可以拿到 uploadId 用于续传
        cosXmlUploadTask.setCosXmlProgressListener { complete, target -> // todo Do something to update progress...
            uploadId = cosXmlUploadTask.uploadId
        }
        //设置返回结果回调
        cosXmlUploadTask.setCosXmlResultListener(object : CosXmlResultListener {
            override fun onSuccess(request: CosXmlRequest?, result: CosXmlResult?) {
                val cOSXMLUploadTaskResult = result as COSXMLUploadTaskResult?
                cosXmlService.release()
                cOSXMLUploadTaskResult?.let {
                    lifecycleScope.launch(Dispatchers.IO) {
                        try {
                            val uploadResult = viewModel.accountRepository.updateAvatar(it.accessUrl)
                            withContext(Dispatchers.Main) {
                                MyUtil.hideDialog(dialog)
                            }
                            if (uploadResult.code == 200) {
                                withContext(Dispatchers.Main) {
                                    MyToastUtil.showSuccess("更改成功")
                                }
                                viewModel.refreshUserInfo()
                            } else {
                                withContext(Dispatchers.Main) {
                                    MyToastUtil.showError(uploadResult.msg)
                                }
                            }

                        } catch (e: Exception) {
                            MyToastUtil.showInfo(e.message)
                        }
                    }
                }
            }


            override fun onFail(request: CosXmlRequest?, clientException: CosXmlClientException?, serviceException: CosXmlServiceException?) {
                if (clientException != null) {
                    clientException.printStackTrace()
                    MyToastUtil.showError(clientException.message!!)
                } else {
                    serviceException?.printStackTrace()
                    MyToastUtil.showError(serviceException?.message!!)
                }
                cosXmlService.release()
                MyUtil.hideDialog(dialog)
            }
        })
        //设置任务状态回调, 可以查看任务过程
        cosXmlUploadTask.setTransferStateListener {
            // todo notify transfer state
        }

        //老版本
//        val putObjectRequest = PutObjectRequest(bucket, cosPath, file.inputStream())
//
//        putObjectRequest.progressListener = CosXmlProgressListener { progress, max ->
//            LogUtils.d("${progress}/${max}")
//        }
//        LogUtils.d("cosXmlService start")
//        // 使用异步回调上传
//        cosXmlService.putObjectAsync(putObjectRequest, object : CosXmlResultListener {
//
//            @SuppressLint("CheckResult")
//            override fun onSuccess(cosXmlRequest: CosXmlRequest, cosXmlResult: CosXmlResult) {
//                LogUtils.d("cosXmlService onSuccess")
//                cosXmlService.release()
//                lifecycleScope.launch(Dispatchers.IO) {
//                    try {
//                        val result = viewModel.accountRepository.updateAvatar(cosXmlResult.accessUrl)
//                        withContext(Dispatchers.Main) {
//                            MyUtil.hideDialog(dialog)
//                        }
//                        if (result.code == 200) {
//                            withContext(Dispatchers.Main) {
//                                MyToastUtil.showSuccess("更改成功")
//                            }
//                            viewModel.refreshUserInfo()
//                        } else {
//                            withContext(Dispatchers.Main) {
//                                MyToastUtil.showError(result.msg)
//                            }
//                        }
//
//                    } catch (e: Exception) {
//                        MyToastUtil.showInfo(e.message)
//                    }
//                }
//            }
//
//            override fun onFail(cosXmlRequest: CosXmlRequest, clientException: CosXmlClientException, serviceException: CosXmlServiceException) {
//                MyToastUtil.showError(clientException.message!!)
//                cosXmlService.release()
//                MyUtil.hideDialog(dialog)
//            }
//
//        })
    }

    private fun startCrop(uri: Uri) {
        val destinationFileName = SAMPLE_CROPPED_IMAGE_NAME

        val options = UCrop.Options()
//        options.setCompressionFormat(Bitmap.CompressFormat.PNG)
        options.setHideBottomControls(true)
        options.setToolbarColor(resources.getColor(R.color.colorWhiteBackground))
        options.setStatusBarColor(resources.getColor(R.color.colorWhiteBackground))
//        options.withMaxResultSize(128, 128)
        options.setCompressionQuality(10)
        options.withAspectRatio(1f, 1f)
        options.setToolbarWidgetColor(resources.getColor(R.color.colorBlackBackground))
        options.setCircleDimmedLayer(true)
        options.setToolbarCancelDrawable(R.drawable.ic_return)
        options.setShowCropGrid(false)
        options.setShowCropFrame(false)

        val uCrop = UCrop.of(uri, Uri.fromFile(File(cacheDir, destinationFileName)))
            .withOptions(options)

        uCrop.start(this)
    }


    override fun onActivityResult(requestCode: Int, resultCode: Int, data: Intent?) {
        super.onActivityResult(requestCode, resultCode, data)
//        UMShareAPI.get(this).onActivityResult(requestCode, resultCode, data)

//        LogUtils.d("requestCode=$requestCode,resultCode=$resultCode,data=$data")

//        Tencent.onActivityResultData(requestCode, resultCode, data, qqLoginListener)

        if (requestCode == 11101) {
            if (resultCode == -1) {
                Tencent.handleResultData(data, qqLoginListener)
            } else {
                MyToastUtil.showError("resultCode=${resultCode},data=${data}")
            }
        }

        //选择或裁剪图片
        if (resultCode == RESULT_OK) {
            if (requestCode == requestPicCode) {
                val selectedUri = data!!.data
                if (selectedUri != null) {
                    startCrop(selectedUri)
                } else {
                    Toast.makeText(this, "未获取到图片，请确认已授予读写存储权限", Toast.LENGTH_LONG).show()
                }
            } else if (requestCode == UCrop.REQUEST_CROP) {
                val resultUri = UCrop.getOutput(data!!)
                if (resultUri != null) {
                    uploadAvatar(resultUri)
                } else {
                    Toast.makeText(this, "无法获取裁剪后图片", Toast.LENGTH_LONG).show()
                }
            }
        } else if (resultCode == UCrop.RESULT_ERROR) {
            val cropError = UCrop.getError(data!!)
            if (cropError != null) {
                Toast.makeText(this, cropError.message, Toast.LENGTH_LONG).show()
            } else {
                Toast.makeText(this, "未知错误", Toast.LENGTH_SHORT).show()
            }
        }

    }

//    private var authListener = object : UMAuthListener {
//        /**
//         * @desc 授权开始的回调
//         * @param platform 平台名称
//         */
//        override fun onStart(platform: SHARE_MEDIA) {
//        }
//
//        /**
//         * @desc 授权成功的回调
//         * @param platform 平台名称
//         * @param action 行为序号，开发者用不上
//         * @param data 用户资料返回
//         */
//        override fun onComplete(platform: SHARE_MEDIA, action: Int, data: Map<String, String>) {
//            lifecycleScope.launch(Dispatchers.IO) {
//                when (platform) {
//                    SHARE_MEDIA.QQ -> {
//                        val result = viewModel.accountRepository.bindQQ(data["uid"]!!)
//                        withContext(Dispatchers.Main) {
//                            if (result.code == 200) {
//                                MyToastUtil.showSuccess("绑定成功")
//                                viewModel.refreshUserInfo()
//                            } else {
//                                MyToastUtil.showError(result.msg)
//                            }
//                        }
//                    }
//
//                    SHARE_MEDIA.WEIXIN -> {
//                        val result = viewModel.accountRepository.bindWX(data["uid"]!!)
//                        withContext(Dispatchers.Main) {
//                            if (result.code == 200) {
//                                MyToastUtil.showSuccess("绑定成功")
//                                viewModel.refreshUserInfo()
//                            } else {
//                                MyToastUtil.showError(result.msg)
//                            }
//                        }
//                    }
//
//                    SHARE_MEDIA.SINA -> {
//                        val result = viewModel.accountRepository.bindSINA(data["uid"]!!)
//                        withContext(Dispatchers.Main) {
//                            if (result.code == 200) {
//                                MyToastUtil.showSuccess("绑定成功")
//                                viewModel.refreshUserInfo()
//                            } else {
//                                MyToastUtil.showError(result.msg)
//                            }
//                        }
//                    }
//
//                    else -> MyToastUtil.showError("未知平台")
//                }
//            }
//
//        }
//
//        /**
//         * @desc 授权失败的回调
//         * @param platform 平台名称
//         * @param action 行为序号，开发者用不上
//         * @param t 错误原因
//         */
//        override fun onError(platform: SHARE_MEDIA, action: Int, t: Throwable) {
//            MyToastUtil.showError("失败：" + t.message)
//        }
//
//        /**
//         * @desc 授权取消的回调
//         * @param platform 平台名称
//         * @param action 行为序号，开发者用不上
//         */
//        override fun onCancel(platform: SHARE_MEDIA, action: Int) {
//            MyToastUtil.showError("取消了")
//        }
//    }

    private fun deleteQQ() {

        lifecycleScope.launch(Dispatchers.IO) {
            try {
                val result = viewModel.accountRepository.deleteQQ()
                withContext(Dispatchers.Main) {
                    if (result.code == 200) {
                        MyToastUtil.showSuccess("解绑成功")
                        viewModel.refreshUserInfo()
                    } else {
                        MyToastUtil.showError(result.msg)
                    }
                }
            } catch (e: Exception) {
                MyToastUtil.showInfo(e.message)
            }
        }
    }

    private fun deleteWX() {

        lifecycleScope.launch(Dispatchers.IO) {
            try {
                val result = viewModel.accountRepository.deleteWX()
                withContext(Dispatchers.Main) {
                    if (result.code == 200) {
                        MyToastUtil.showSuccess("解绑成功")
                        viewModel.refreshUserInfo()
                    } else {
                        MyToastUtil.showError(result.msg)
                    }
                }
            } catch (e: Exception) {
                MyToastUtil.showInfo(e.message)
            }
        }
    }

    private fun deleteSINA() {

        lifecycleScope.launch(Dispatchers.IO) {
            try {
                val result = viewModel.accountRepository.deleteSINA()
                withContext(Dispatchers.Main) {
                    if (result.code == 200) {
                        MyToastUtil.showSuccess("解绑成功")
                        viewModel.refreshUserInfo()
                    } else {
                        MyToastUtil.showError(result.msg)
                    }
                }
            } catch (e: Exception) {
                MyToastUtil.showInfo(e.message)
            }
        }
    }

}
