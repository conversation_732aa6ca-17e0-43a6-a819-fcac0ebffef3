package com.lijianqiang12.silent.component.activity.custom.dialog

import android.graphics.Color
import android.graphics.Paint
import android.graphics.drawable.ColorDrawable
import android.os.Bundle
import android.text.SpannableStringBuilder
import android.text.method.LinkMovementMethod
import android.util.DisplayMetrics
import android.view.*
import androidx.fragment.app.Fragment
import androidx.fragment.app.FragmentActivity
import com.lijianqiang12.silent.DIALOG_WIDTH_PERCENT_MIDDLE
import com.lijianqiang12.silent.R
import com.lijianqiang12.silent.component.activity.base.BaseDialogFragment
import kotlinx.android.synthetic.main.dialog_normal.view.*


class NormalDialog private constructor() : BaseDialogFragment() {
    constructor(fragment: Fragment) : this() {
        this.fragment = fragment
    }

//    constructor(activity: AppCompatActivity) : this() {
//        this.activity = activity
//    }

    constructor(fragmentActivity: FragmentActivity) : this() {
        this.fragmentActivity = fragmentActivity
    }



    private var okListener: OnOKClickListener? = null
    private var cancelListener: OnCancelClickListener? = null
    private var closeListener: OnCloseClickListener? = null
    private var warningListener: OnWarningClickListener? = null
    private lateinit var v: View
    private var title = ""
    private var content = ""
    private var warning = ""
    private var contentStringBuilder: SpannableStringBuilder? = null
    private var fragment: Fragment? = null
//    private var activity: AppCompatActivity? = null
    private var fragmentActivity: FragmentActivity? = null
    private var okText = "确定"
    private var cancelText = "取消"
    private var gravity = Gravity.CENTER

//    private var cancelable:Boolean = true


    override fun onCreateView(inflater: LayoutInflater, container: ViewGroup?, savedInstanceState: Bundle?): View {
        v = inflater.inflate(R.layout.dialog_normal, container, false)
        return v
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)

        dialog!!.window!!.setBackgroundDrawable(ColorDrawable(Color.TRANSPARENT))
        if (savedInstanceState != null) {
            title = savedInstanceState.get("title") as String
            content = savedInstanceState.get("content") as String
        }


        v.tv_dialog_normal_title.text = title
        v.tv_dialog_normal_content.text = content
        if(warning.isNotEmpty()){
            v.tv_dialog_warning_content.visibility = View.VISIBLE
            v.tv_dialog_warning_content.text = warning

            if(warningListener != null){
                v.tv_dialog_warning_content.paintFlags = v.tv_dialog_warning_content.paintFlags or Paint.UNDERLINE_TEXT_FLAG
                v.tv_dialog_warning_content.setOnClickListener {
                    warningListener?.apply {
                        onclick()
                    }
//                    <EMAIL>()
                }
            }

        }
        v.tv_dialog_normal_content.gravity = gravity

        v.tv_dialog_normal_ok.text = okText
        v.tv_dialog_normal_cancel.text = cancelText
        contentStringBuilder?.apply {
            v.tv_dialog_normal_content.text = contentStringBuilder
            v.tv_dialog_normal_content.movementMethod = LinkMovementMethod.getInstance()
        }
        okListener?.apply {
            v.tv_dialog_normal_ok.visibility = View.VISIBLE
        }
        cancelListener?.apply {
            v.tv_dialog_normal_cancel.visibility = View.VISIBLE
        }
        closeListener?.apply {
            v.iv_normal_dialog_close.visibility = View.VISIBLE
        }
        v.tv_dialog_normal_ok.setOnClickListener {
            okListener?.apply {
                onclick()
            }
            <EMAIL>()
        }
        v.tv_dialog_normal_cancel.setOnClickListener {
            cancelListener?.apply {
                onclick()
            }
            <EMAIL>()
        }
        v.iv_normal_dialog_close.setOnClickListener {
            closeListener?.apply {
                onclick()
            }
            <EMAIL>()
        }
    }

    fun setGravity(gravity: Int) {
        this.gravity = gravity
    }

    fun setTitle(arg: String) {
        title = arg
    }

    fun setContent(arg: String) {
        content = arg
    }

    fun setWarning(arg: String) {
        warning  = arg
    }

    fun setContentStringBuilder(arg: SpannableStringBuilder) {
        contentStringBuilder = arg
    }

    fun showDialog() {
//        this.show(childFragmentManager, "NormalDialog")
//        activity?.apply {
//            show(this.supportFragmentManager, "NormalDialog")
//        }
        fragmentActivity?.apply {
            show(this.supportFragmentManager, "NormalDialog")
        }
        fragment?.apply {
            show(this.parentFragmentManager, "NormalDialog")
        }
    }

    override fun onStart() {
        val params = dialog!!.window!!.attributes
        val dm: DisplayMetrics = resources.displayMetrics
//        val density = dm.density
        val width = dm.widthPixels
        val height = dm.heightPixels
        params.width = (height.coerceAtMost(width) * DIALOG_WIDTH_PERCENT_MIDDLE).toInt()//ViewGroup.LayoutParams.MATCH_PARENT
//        params.width = width.coerceAtMost(height) * 3 / 4//ViewGroup.LayoutParams.MATCH_PARENT
        dialog!!.window!!.attributes = params as WindowManager.LayoutParams
        super.onStart()
    }

    fun setOnNormalOKClickListener(okListener: OnOKClickListener) {
        this.okListener = okListener
    }

    fun setOnNormalCancelClickListener(cancelListener: OnCancelClickListener) {
        this.cancelListener = cancelListener
    }

    fun setOnNormalCloseClickListener(closeListener: OnCloseClickListener) {
        this.closeListener = closeListener
    }

    fun setOnWarningClickListener(warningListener: OnWarningClickListener) {
        this.warningListener = warningListener
    }

    fun setOnNormalOKClickListener(okText: String, okListener: OnOKClickListener) {
        this.okText = okText
        this.okListener = okListener
    }

    fun setOnNormalCancelClickListener(cancelText: String, cancelListener: OnCancelClickListener) {
        this.cancelText = cancelText
        this.cancelListener = cancelListener
    }


    override fun onSaveInstanceState(outState: Bundle) {
        super.onSaveInstanceState(outState)
        outState.putString("title", title)
        outState.putString("content", content)
    }

}