package com.lijianqiang12.silent.component.activity.me.vip

import android.os.Bundle
import androidx.activity.viewModels
import androidx.lifecycle.Observer
import androidx.recyclerview.widget.LinearLayoutManager
import com.lijianqiang12.silent.R
import com.lijianqiang12.silent.component.activity.base.BaseActivity
import com.lijianqiang12.silent.data.viewmodel.VIPViewModel
import com.lijianqiang12.silent.utils.MyToastUtil
import dagger.hilt.android.AndroidEntryPoint
import kotlinx.android.synthetic.main.activity_buy_history.*
import javax.inject.Inject

@AndroidEntryPoint
class BuyHistoryActivity : BaseActivity() {

    private lateinit var mAdapter: BuyHistoryAdapter
    private lateinit var mLayoutManager: androidx.recyclerview.widget.RecyclerView.LayoutManager

    @Inject
    lateinit var viewModel: VIPViewModel


    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        setContentView(R.layout.activity_buy_history)

        iv_return_buy_history.setOnClickListener { finish() }

        mLayoutManager = LinearLayoutManager(this)
        rv_buy_history.layoutManager = mLayoutManager
        mAdapter = BuyHistoryAdapter(R.layout.item_buy_history, mutableListOf())
        rv_buy_history.adapter = mAdapter

//        mAdapter.addChildClickViewIds(R.id.btn_apply_delivery)
//        mAdapter.setOnItemChildClickListener { adapter, view, position ->
//            VIPApplyDeliveryInfoDialog(this).apply {
//                setOnApplyDeliveryInfoListener(object : VIPApplyDeliveryInfoDialog.OnApplyDeliveryInfoListener {
//                    override fun onFinish(name: String, phone: String, address: String) {
//                        <EMAIL>(Dispatchers.IO) {
//                            try {
//                                val result = MyRetrofitClient.service.applyDelivery(mAdapter.data[position].buyHistoryId, name, phone, address)
//                                if (result.code == 200) {
//                                    MyToastUtil.showInfo("申请成功")
//                                    viewModel.getBuyHistory()
//                                } else {
//                                    MyToastUtil.showInfo(result.msg)
//                                }
//                            } catch (e: Exception) {
//                                MyToastUtil.showInfo(e.message)
//                            }
//
//                        }
//                    }
//                })
//                show()
//            }
//        }

        mAdapter.animationEnable = true

        viewModel.buyHistoryLiveData.observe(this, Observer {
            if (it.state == 0) {
                mAdapter.setNewInstance(it.data!!)
                mAdapter.notifyDataSetChanged()
            } else {
                MyToastUtil.showInfo("网络异常")
            }

            wrl_buy_history.isRefreshing = false
        })

        wrl_buy_history.setOnRefreshListener {
            viewModel.getBuyHistory()
        }
        wrl_buy_history.isRefreshing = true
        viewModel.getBuyHistory()
    }
}