package com.lijianqiang12.silent.component.activity.lock.setting

import android.os.Parcel
import android.os.Parcelable
import com.lijianqiang12.silent.data.model.net.pojos.Page

const val DENY_PAGE_TYPE_FAST = 0
const val DENY_PAGE_TYPE_CUSTOM = 1

data class TheDenyPage2(val id: Long, var name: String, var pages: List<Page>, var valid: Boolean, var version: Int, val type: Int) : Parcelable {

    constructor() : this(-1, "", mutableListOf(), true, 0, DENY_PAGE_TYPE_FAST)


    constructor(parcel: Parcel) : this(
        parcel.readLong(),
        parcel.readString()!!,
        parcel.createTypedArrayList(Page)!!,
        parcel.readByte() != 0.toByte(),
        parcel.readInt(),
        parcel.readInt(),
    ) {
    }


    override fun writeToParcel(parcel: Parcel, flags: Int) {
        parcel.writeLong(id)
        parcel.writeString(name)
        parcel.writeTypedList(pages)
        parcel.writeByte(if (valid) 1 else 0)
        parcel.writeInt(version)
        parcel.writeInt(type)
    }

    override fun describeContents(): Int {
        return 0
    }


    companion object CREATOR : Parcelable.Creator<TheDenyPage2> {
        override fun createFromParcel(parcel: Parcel): TheDenyPage2 {
            return TheDenyPage2(parcel)
        }

        override fun newArray(size: Int): Array<TheDenyPage2?> {
            return arrayOfNulls(size)
        }
    }

    //因为快速添加的id与云端相等，而自定义添加的id全是-1，因此不对比id
    override fun equals(other: Any?): Boolean {
        val newObj = other as TheDenyPage2
        if (newObj.name != this.name) return false
        if (newObj.pages != this.pages) return false
        if (newObj.valid != this.valid) return false
        if (newObj.version != this.version) return false
        if (newObj.type != this.type) return false
        return true
    }

    override fun hashCode(): Int {
        var result = id.hashCode()
        result = 31 * result + name.hashCode()
        result = 31 * result + pages.hashCode()
        result = 31 * result + (if (valid) 1 else 0)
        result = 31 * result + version
        result = 31 * result + type
        return result
    }

}
