package com.lijianqiang12.silent.component.activity.me.developer_unlock

import android.os.Bundle
import androidx.lifecycle.lifecycleScope
import com.lijianqiang12.silent.R
import com.lijianqiang12.silent.data.model.net.MyRetrofitClient
import com.lijianqiang12.silent.component.activity.base.BaseActivity
import com.lijianqiang12.silent.utils.MyToastUtil
import kotlinx.android.synthetic.main.activity_developer_unlock.btn_unlock_user_query
import kotlinx.android.synthetic.main.activity_developer_unlock.btn_unlock_user_unlock
import kotlinx.android.synthetic.main.activity_developer_unlock.et_unlock_user_id
import kotlinx.android.synthetic.main.activity_developer_unlock.tv_unlock_user_info
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.launch
import kotlinx.coroutines.withContext

class DeveloperUnlockActivity : BaseActivity() {

    private var lastQueryUserId = -1L
    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        setContentView(R.layout.activity_developer_unlock)


        btn_unlock_user_query.setOnClickListener {
            this.lifecycleScope.launch(Dispatchers.IO) {
                try {
                    val result = MyRetrofitClient.service.getDeveloperUnlockInfo(et_unlock_user_id.text.toString().toLong())
                    withContext(Dispatchers.Main) {
                        if (result.code == 200) {
                            result.data?.let {
                                lastQueryUserId = it.userId
                                tv_unlock_user_info.text = """
                                    用户ID：${it.userId}
                                    解锁状态：${if (it.ifUnlock) "解锁中" else "未解锁"}
                                    已解锁次数：${it.unlockCount}
                                    上次解锁时间：${it.updateTime}
                                """.trimIndent()

//                                "用户ID：${it.userId}\n解锁状态${arrayListOf("未解锁","解锁中")[ it.ifUnlock]}\n${it.unlockCount}\n${it.updateTime}"

                                btn_unlock_user_unlock.isEnabled = !it.ifUnlock
                            }
                        } else {
                            withContext(Dispatchers.Main) {
                                MyToastUtil.showInfo(result.msg)
                            }
                            btn_unlock_user_unlock.isEnabled = false
                        }
                    }
                } catch (e: Exception) {
                    withContext(Dispatchers.Main) {
                        MyToastUtil.showInfo("异常，原因：" + e.message)
                        btn_unlock_user_unlock.isEnabled = false
                    }
                }
            }
        }


        btn_unlock_user_unlock.setOnClickListener {
            if (lastQueryUserId != et_unlock_user_id.text.toString().toLong()) {
                MyToastUtil.showError("请先查询用户信息")
            }
            this.lifecycleScope.launch(Dispatchers.IO) {
                try {
                    val result = MyRetrofitClient.service.developerUnlock(lastQueryUserId)
                    withContext(Dispatchers.Main) {
                        if (result.code == 200) {
                            MyToastUtil.showSuccess(result.msg)
                            btn_unlock_user_unlock.isEnabled = false
                        } else {
                            MyToastUtil.showInfo(result.msg)
                        }
                    }
                } catch (e: Exception) {
                    withContext(Dispatchers.Main) {
                        MyToastUtil.showInfo("异常，原因：" + e.message)
                    }
                }
            }
        }
    }


}