package com.lijianqiang12.silent.component.activity

import android.content.Intent
import android.os.Bundle
import com.lijianqiang12.silent.component.activity.base.BaseActivity

class StartApp1PxActivity : BaseActivity() {

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        try {
            val pkg = intent.getStringExtra("pkg")
            if (pkg != null) {
                val i = packageManager.getLaunchIntentForPackage(pkg)
                if (i != null) {
                    val intent = Intent(i)
                    intent.addFlags(Intent.FLAG_ACTIVITY_NEW_TASK)
                    startActivity(intent)
                }
            }
        } catch (e: Exception) {

        }
//        finish()
    }


    override fun onResume() {
        super.onResume()
        finish()
    }


}
