package com.lijianqiang12.silent.component.activity.custom.dialog

import android.graphics.Color
import android.graphics.drawable.ColorDrawable
import android.os.Bundle
import android.util.DisplayMetrics
import android.view.*
import android.widget.LinearLayout
import androidx.appcompat.app.AppCompatActivity
import androidx.fragment.app.Fragment
import androidx.fragment.app.FragmentActivity
import cn.jzvd.Jzvd
import com.danikula.videocache.HttpProxyCacheServer
import com.lijianqiang12.silent.DIALOG_WIDTH_PERCENT
import com.lijianqiang12.silent.DIALOG_WIDTH_PERCENT_BIG
import com.lijianqiang12.silent.TheApplication
import com.lijianqiang12.silent.R
import com.lijianqiang12.silent.component.activity.base.BaseDialogFragment
import com.lijianqiang12.silent.utils.dpToPixel
import kotlinx.android.synthetic.main.dialog_normal.view.iv_normal_dialog_close
import kotlinx.android.synthetic.main.dialog_normal.view.tv_dialog_normal_cancel
import kotlinx.android.synthetic.main.dialog_normal.view.tv_dialog_normal_ok
import kotlinx.android.synthetic.main.dialog_normal.view.tv_dialog_normal_title
import kotlinx.android.synthetic.main.dialog_video.view.*


class VideoDialog() : BaseDialogFragment() {

    constructor(fragment: Fragment) : this() {
        this.fragment = fragment
    }

    constructor(activity: AppCompatActivity) : this() {
        this.activity = activity
    }


    constructor(activity2: FragmentActivity) : this() {
        this.activity2 = activity2
    }

//    constructor(activity: Activity) : this() {
//        this.activity3 = activity
//    }

    private var okListener: OnOKClickListener? = null
    private var cancelListener: OnCancelClickListener? = null
    private var closeListener: OnCloseClickListener? = null
    private lateinit var v: View
    private var title = ""
    private var content = ""
    private var fragment: Fragment? = null
    private var activity: AppCompatActivity? = null
    private var activity2: FragmentActivity? = null

    //    private var activity3: Activity? = null
    private var okText = "确定"
    private var cancelText = "取消"

    //    private var cancelable:Boolean = true
    private var proxy: HttpProxyCacheServer? = null


    override fun onCreateView(inflater: LayoutInflater, container: ViewGroup?, savedInstanceState: Bundle?): View {
        v = inflater.inflate(R.layout.dialog_video, container, false)
        return v
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)

        dialog!!.window!!.setBackgroundDrawable(ColorDrawable(Color.TRANSPARENT))
        if (savedInstanceState != null) {
            title = savedInstanceState.get("title") as String
            content = savedInstanceState.get("content") as String
        }




        v.video_player.setUp(
//            "https://jzvd.nathen.cn/c6e3dc12a1154626b3476d9bf3bd7266/6b56c5f0dc31428083757a45764763b0-5287d2089db37e62345123a1be272f8b.mp4", title
            proxyUrl(content), ""
        )

        val lp: LinearLayout.LayoutParams = v.video_player.totalTimeTextView.layoutParams as LinearLayout.LayoutParams
        lp.marginEnd = dpToPixel(16.0f).toInt()
        v.video_player.totalTimeTextView.layoutParams = lp
//        v.video_player.fullscreenButton.removeonclick
        v.video_player.fullscreenButton.visibility = View.GONE
//        v.video_player.posterImageView.set("https://p.qpic.cn/videoyun/0/2449_43b6f696980311e59ed467f22794e792_1/640")
        v.video_player.startVideoAfterPreloading() //如果预加载完会开始播放，如果未加载则开始加载


        v.tv_dialog_normal_title.text = title
//        v.tv_dialog_normal_content.text = content


        v.tv_dialog_normal_ok.text = okText
        v.tv_dialog_normal_cancel.text = cancelText
        okListener?.apply {
            v.tv_dialog_normal_ok.visibility = View.VISIBLE
        }
        cancelListener?.apply {
            v.tv_dialog_normal_cancel.visibility = View.VISIBLE
        }
        closeListener?.apply {
            v.iv_normal_dialog_close.visibility = View.VISIBLE
        }
        v.tv_dialog_normal_ok.setOnClickListener {
            okListener?.apply {
                onclick()
            }
            <EMAIL>()
        }
        v.tv_dialog_normal_cancel.setOnClickListener {
            cancelListener?.apply {
                onclick()
            }
            <EMAIL>()
        }
        v.iv_normal_dialog_close.setOnClickListener {
            closeListener?.apply {
                onclick()
            }
            <EMAIL>()
        }
    }


    fun setTitle(arg: String) {
        title = arg
    }

    fun setContent(arg: String) {
        content = arg
    }

    fun proxyUrl(url: String): String {
        val proxyUrl = proxy?.getProxyUrl(url) ?: ""
        return proxyUrl
    }

    fun show() {
        activity?.apply {
            proxy = TheApplication.getProxy(this)
            super.show(this.supportFragmentManager, "NormalDialog")
        }
        activity2?.apply {
            proxy = TheApplication.getProxy(this)
            super.show(this.supportFragmentManager, "NormalDialog")
        }
//        activity3?.apply {
//            val manager: android.app.FragmentManager? = this.fragmentManager
//            val transaction: android.app.FragmentTransaction? = manager!!.beginTransaction()
//            //创建fragment但是不绘制UI
//            //创建fragment但是不绘制UI
//            transaction!!.add(this@VideoDialog, "")
////            super.show(this.fragmentManager, "NormalDialog")
//        }
        fragment?.apply {
            proxy = TheApplication.getProxy(this.requireContext())
            super.show(fragment!!.requireFragmentManager(), "NormalDialog")
        }
    }

    override fun onStart() {
        val params = dialog!!.window!!.attributes
        val dm: DisplayMetrics = resources.displayMetrics
//        val density = dm.density
        val width = dm.widthPixels
        val height = dm.heightPixels
        params.width = (width * DIALOG_WIDTH_PERCENT_BIG).toInt()//ViewGroup.LayoutParams.MATCH_PARENT
        params.height = (height * DIALOG_WIDTH_PERCENT).toInt()//ViewGroup.LayoutParams.MATCH_PARENT
//        params.width = width.coerceAtMost(height) * 3 / 4//ViewGroup.LayoutParams.MATCH_PARENT
        dialog!!.window!!.attributes = params as WindowManager.LayoutParams
        super.onStart()
    }

    fun setOnNormalOKClickListener(okListener: OnOKClickListener) {
        this.okListener = okListener
    }

    fun setOnNormalCancelClickListener(cancelListener: OnCancelClickListener) {
        this.cancelListener = cancelListener
    }

    fun setOnNormalCloseClickListener(closeListener: OnCloseClickListener) {
        this.closeListener = closeListener
    }

    fun setOnNormalOKClickListener(okText: String, okListener: OnOKClickListener) {
        this.okText = okText
        this.okListener = okListener
    }

    fun setOnNormalCancelClickListener(cancelText: String, cancelListener: OnCancelClickListener) {
        this.cancelText = cancelText
        this.cancelListener = cancelListener
    }

    override fun onPause() {
        super.onPause()
        Jzvd.releaseAllVideos()
    }

    override fun onSaveInstanceState(outState: Bundle) {
        super.onSaveInstanceState(outState)
        outState.putString("title", title)
        outState.putString("content", content)
    }

}