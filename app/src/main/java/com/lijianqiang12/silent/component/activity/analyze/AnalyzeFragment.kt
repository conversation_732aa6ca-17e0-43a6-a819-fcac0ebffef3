package com.lijianqiang12.silent.component.activity.analyze


import android.content.Intent
import android.graphics.drawable.GradientDrawable
import android.os.Bundle
import android.util.Log
import android.view.Gravity
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.LinearLayout
import androidx.fragment.app.viewModels
import androidx.lifecycle.Observer
import androidx.palette.graphics.Palette
import com.bumptech.glide.Glide
import com.bumptech.glide.load.engine.DiskCacheStrategy
import com.github.mikephil.charting.charts.LineChart
import com.github.mikephil.charting.components.XAxis
import com.github.mikephil.charting.data.Entry
import com.github.mikephil.charting.data.LineData
import com.github.mikephil.charting.data.LineDataSet
import com.github.mikephil.charting.formatter.ValueFormatter
import com.github.mikephil.charting.interfaces.datasets.ILineDataSet
import com.lijianqiang12.silent.MyConstants
import com.lijianqiang12.silent.R
import com.lijianqiang12.silent.component.activity.PermissionActivity
import com.lijianqiang12.silent.component.activity.TheLoginActivity
import com.lijianqiang12.silent.component.activity.analyze.analyzesetting.AnalyzeSettingActivity
import com.lijianqiang12.silent.component.activity.base.BaseFragment
import com.lijianqiang12.silent.component.activity.custom.dialog.NormalDialog
import com.lijianqiang12.silent.component.activity.custom.dialog.OnCancelClickListener
import com.lijianqiang12.silent.component.activity.custom.dialog.OnOKClickListener
import com.lijianqiang12.silent.component.activity.analyze.appusage.AppUsageAnalyzeActivity
import com.lijianqiang12.silent.component.activity.analyze.todayusage.TodayAppUsageActivity
import com.lijianqiang12.silent.component.activity.analyze.todaytrend.TodayLockTrendActivity
import com.lijianqiang12.silent.data.viewmodel.AnalyzeViewModel
import com.lijianqiang12.silent.utils.*
import dagger.hilt.android.AndroidEntryPoint
import kotlinx.android.synthetic.main.fragment_analyze.*
import kotlinx.android.synthetic.main.fragment_analyze.view.*
import kotlinx.android.synthetic.main.item_analyze_app_used.view.*
import kotlinx.android.synthetic.main.item_analyze_today.view.*
import kotlinx.android.synthetic.main.item_today_length.view.*
import java.text.SimpleDateFormat
import java.util.*
import javax.inject.Inject


private const val ARG_PARAM1 = "param1"
private const val ARG_PARAM2 = "param2"

@AndroidEntryPoint
class AnalyzeFragment : BaseFragment() {

    private val TAG = "AnalyzeFragment"

    private var param1: String? = null
    private var param2: String? = null

    private lateinit var lineChart: LineChart
    private lateinit var v: View

    private val viewModel: AnalyzeViewModel by viewModels()

    private var deltaWeek = 0
    private var selectWeekText = "上周"
    private var selectAppUsageDays = 1

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        arguments?.let {
            param1 = it.getString(ARG_PARAM1)
            param2 = it.getString(ARG_PARAM2)
        }
    }

    override fun onCreateView(inflater: LayoutInflater, container: ViewGroup?, savedInstanceState: Bundle?): View? {
        v = inflater.inflate(R.layout.fragment_analyze, container, false)
        return v
    }

    override fun lazyInit() {
        val swipeRefreshLayout = v.srl_analyze
        swipeRefreshLayout.isRefreshing = true

        v.iv_monitor_setting.setOnClickListener {
            startActivity(Intent(requireContext(), AnalyzeSettingActivity::class.java))
        }

        v.tv_my_invite_days_left.setOnClickListener {
            NormalDialog(this).apply {
                setGravity(Gravity.START)
                setTitle("为何时间记录会不全？")
                setContent("手机关闭屏幕后或处于省电模式下时，会限制App后台运行并杀后台，因此会造成定时锁机启动比预设时间晚几分钟到几小时，也会造成锁机中途不断退出然后自启，这些情况都会导致时间记录不全，您可以跟随我们的“防止异常退出”提示来修改部分系统设置，以减少此类情况的发生。")
                setOnNormalOKClickListener("去设置", object : OnOKClickListener {
                    override fun onclick() {
                        startActivity(Intent(requireContext(), PermissionActivity::class.java))
                    }
                })
                setOnNormalCancelClickListener("知道了", object : OnCancelClickListener {
                    override fun onclick() {

                    }
                })
                showDialog()
            }
        }

        initChart()
        viewModel.yesterdayTopLiveData.observe(viewLifecycleOwner, Observer {
            for (i in 0..2) {
                if (it.size > i) {
                    when (i) {
                        0 -> {
                            v.mcv_analyze_yesterday_1.visibility = View.VISIBLE
                            v.civ_analyze_yesterday_1.visibility = View.VISIBLE
                            v.tv_analyze_name_yesterday_1.text = it[i].username
                            v.tv_time_name_yesterday_1.text = TimeUtil.formatHHMM(it[i].length / 60)
                            v.tv_time_brand_yesterday_1.text = it[i].brand
                            Glide.with(this).load(it[i].avatar)
                                //.transition(DrawableTransitionOptions.withCrossFade())
                                .diskCacheStrategy(DiskCacheStrategy.AUTOMATIC)
                                .into(civ_analyze_yesterday_1)
                        }

                        1 -> {
                            v.mcv_analyze_yesterday_2.visibility = View.VISIBLE
                            v.civ_analyze_yesterday_2.visibility = View.VISIBLE
                            tv_analyze_name_yesterday_2.text = it[i].username
                            tv_time_name_yesterday_2.text = TimeUtil.formatHHMM(it[i].length / 60)
                            tv_time_brand_yesterday_2.text = it[i].brand
                            Glide.with(this).load(it[i].avatar)
                                //.transition(DrawableTransitionOptions.withCrossFade())
                                .diskCacheStrategy(DiskCacheStrategy.AUTOMATIC)
                                .into(civ_analyze_yesterday_2)
                        }

                        2 -> {
                            v.mcv_analyze_yesterday_3.visibility = View.VISIBLE
                            v.civ_analyze_yesterday_3.visibility = View.VISIBLE
                            tv_analyze_name_yesterday_3.text = it[i].username
                            tv_time_name_yesterday_3.text = TimeUtil.formatHHMM(it[i].length / 60)
                            tv_time_brand_yesterday_3.text = it[i].brand
                            Glide.with(this).load(it[i].avatar)
                                //.transition(DrawableTransitionOptions.withCrossFade())
                                .diskCacheStrategy(DiskCacheStrategy.AUTOMATIC)
                                .into(civ_analyze_yesterday_3)
                        }
                    }
                }
            }
            swipeRefreshLayout.isRefreshing = false
        })

        viewModel.todayTopLiveData.observe(viewLifecycleOwner, Observer {
            for (i in 0..2) {
                if (it.size > i) {
                    when (i) {
                        0 -> v.template_analyze_today_1.visibility = View.VISIBLE
                        1 -> v.template_analyze_today_2.visibility = View.VISIBLE
                        2 -> v.template_analyze_today_3.visibility = View.VISIBLE
                    }
                    val container = v.ll_today_top.getChildAt(i)
                    container.tv_analyze_today_name.text = it[i].username
                    container.tv_analyze_today_time.text = TimeUtil.formatHHMM(it[i].length / 60)
                    container.tv_time_brand_today.text = it[i].brand
                    container.tv_trend.text = "${i + 1}."
                    Glide.with(this).load(it[i].avatar)
                        //.transition(DrawableTransitionOptions.withCrossFade())
                        .diskCacheStrategy(DiskCacheStrategy.AUTOMATIC)
                        .into(container.civ_item_analyze_today)
                    when (it[i].vipState) {
                        0 -> {
                            container.tv_time_vip_today.visibility = View.INVISIBLE
                        }

                        1 -> {
                            container.tv_time_vip_today.visibility = View.VISIBLE
                            container.tv_time_vip_today.text = "VIP"
                            container.tv_time_vip_today.setTextColor(resources.getColor(R.color.colorBackText))
                            container.tv_time_vip_today.setBackgroundResource(R.drawable.shape_get_vip_gradient_2dp)
                        }

                        2 -> {
                            container.tv_time_vip_today.visibility = View.VISIBLE
                            container.tv_time_vip_today.text = "SVIP"
                            container.tv_time_vip_today.setTextColor(resources.getColor(R.color.colorWhiteText))
                            container.tv_time_vip_today.setBackgroundResource(R.drawable.shape_get_vip_black_gradient_2dp)
                        }
                    }


                }
            }

        })



        viewModel.myTrendLiveData.observe(viewLifecycleOwner) {
            var result = "${it.myTrendNo}"
            if (it.myTrendNo > 999) result = "999+"
            tv_my_trend.text = "No.${result}"
        }

        if (MMKVUtils.getInt(MyConstants.SP_KEY_USER_ID, -1) == -1) {
            tv_my_trend_yesterday.visibility = View.GONE
        } else {
            tv_my_trend_yesterday.visibility = View.VISIBLE
            viewModel.myTrendYesterdayLiveData.observe(viewLifecycleOwner) {
                var result = "${it.myTrendNo}"
                if (it.myTrendNo > 999) result = "999+"
                tv_my_trend_yesterday.text = "Me : No.${result}"
            }
        }
        fun deltaWeek2DayRange(deltaWeek: Int): String {
            if (deltaWeek == 0) {
                return "上周"
            } else {
                // 获取当前日期
                val currentDate = Calendar.getInstance()

                // 打印上周周一到周日的日期
                val sdf = SimpleDateFormat("M月d日")

                //把周日到周六1～7转换为周一到周日1～7
                var dayOfWeek = currentDate.get(Calendar.DAY_OF_WEEK) - 1
                if (dayOfWeek == 0) dayOfWeek = 7


                // 推导前第n周的周一到周日的日期
                currentDate.add(Calendar.DAY_OF_MONTH, -(dayOfWeek - 1) - 7 * (deltaWeek + 1))
                val mondayOfNWeek = currentDate.time

                currentDate.add(Calendar.DAY_OF_MONTH, 6)
                val sundayOfNWeek = currentDate.time


                return "${sdf.format(mondayOfNWeek)}～${sdf.format(sundayOfNWeek)}"
            }
        }

        iv_today_lock_length_previous.setOnClickListener {
            deltaWeek++
            selectWeekText = deltaWeek2DayRange(deltaWeek)
            tv_today_lock_length_time_range.text = selectWeekText
            viewModel.refreshLockLength(deltaWeek)
        }

        iv_today_lock_length_next.setOnClickListener {
            if (deltaWeek <= 0) {
                MyToastUtil.showWarning("到头啦")
                return@setOnClickListener
            }
            deltaWeek--
            selectWeekText = deltaWeek2DayRange(deltaWeek)
            tv_today_lock_length_time_range.text = selectWeekText
            viewModel.refreshLockLength(deltaWeek)
        }

        viewModel.myLockLengthLiveData.observe(viewLifecycleOwner, Observer {
            if (it.isEmpty()) return@Observer

            val chartDataA = ArrayList<Entry>()
            for (i in 0..6) {
                chartDataA.add(Entry(i.toFloat(), (it[i] / 60).toFloat()))
            }
            val dataSetA = LineDataSet(chartDataA, selectWeekText)
            dataSetA.color = getColorFromTheme(requireContext(), R.attr.custom_attr_app_text_5)
            dataSetA.setCircleColor(getColorFromTheme(requireContext(), R.attr.custom_attr_app_text_5))   //可以设置Entry节点的颜色
            dataSetA.circleSize = 4f  //设置节点的大小
            dataSetA.mode = LineDataSet.Mode.CUBIC_BEZIER
            dataSetA.setDrawCircleHole(false) //是否定制节点圆心的颜色，若为false，则节点为单一的同色点，若为true则可以设置节点圆心的颜色
            dataSetA.setDrawFilled(true)//设置是否开启填充，默认为false
            dataSetA.fillColor = getColorFromTheme(requireContext(), R.attr.custom_attr_app_text_7)//设置填充颜色
            dataSetA.fillAlpha = 85//设置填充区域透明度，默认值为85
            dataSetA.valueFormatter = object : ValueFormatter() {
                override fun getFormattedValue(value: Float): String {
                    return if (value.toInt() / 60 > 0) {
//                        "${value.toInt() / 60}小时"
                        "${value.toInt() / 60}.${value.toInt() % 60 / 6}小时"
                    } else {
                        "${value.toInt() % 60}分钟"
                    }
                }
            }
            dataSetA.valueTextSize = 8f
            dataSetA.valueTextColor = getColorFromTheme(requireContext(), R.attr.custom_attr_app_text_3)

            val chartDataB = ArrayList<Entry>()
            for (i in 0..it.size - 8) {
                chartDataB.add(Entry(i.toFloat(), it[i + 7] / 60.toFloat()))
            }
            val dataSetB = LineDataSet(chartDataB, "本周")
            dataSetB.color = getColorFromTheme(requireContext(), R.attr.custom_attr_app_fg_dark)
            dataSetB.setCircleColor(getColorFromTheme(requireContext(), R.attr.custom_attr_app_fg_dark))    //可以设置Entry节点的颜色
            dataSetB.circleSize = 4f  //设置节点的大小
            dataSetB.mode = LineDataSet.Mode.CUBIC_BEZIER
            dataSetB.setDrawCircleHole(false) //是否定制节点圆心的颜色，若为false，则节点为单一的同色点，若为true则可以设置节点圆心的颜色
            dataSetB.setDrawFilled(true)//设置是否开启填充，默认为false
            dataSetB.fillColor = getColorFromTheme(requireContext(), R.attr.custom_attr_app_fg)//设置填充颜色
            dataSetB.fillAlpha = 85//设置填充区域透明度，默认值为85
            dataSetB.valueFormatter = object : ValueFormatter() {
                override fun getFormattedValue(value: Float): String {
                    return if (value.toInt() / 60 > 0) {
                        "${value.toInt() / 60}.${value.toInt() % 60 / 6}小时"
                    } else {
                        "${value.toInt() % 60}分钟"
                    }
                }
            }
            dataSetB.valueTextSize = 8f
            dataSetB.valueTextColor = getColorFromTheme(requireContext(), R.attr.custom_attr_app_fg_dark)

            val dataSets = ArrayList<ILineDataSet>()
            dataSets.add(dataSetA)
            dataSets.add(dataSetB)
            lineChart.data = LineData(dataSets)
            lineChart.invalidate()
        })

        rg_app_usage_time.setOnCheckedChangeListener { _, checkedId ->
            when (checkedId) {
                R.id.rb_day -> {
                    selectAppUsageDays = 1
                    viewModel.refreshAppUsageTimeToday(1)
                }

                R.id.rb_week -> {
                    selectAppUsageDays = 7
                    viewModel.refreshAppUsageTimeToday(7)
                }
            }
        }

        viewModel.appUsageTimeTodayLiveData.observe(viewLifecycleOwner) {
            var longest = 0L
            it.forEach { appTime ->
                longest += appTime.timeLength
            }
            for (i in 0..2) {
                if (it.size > i) {
                    val appTime = it[i]
                    val container = ll_data_today_length.getChildAt(i)
                    container.iv_today_length_item_icon.setImageDrawable(appTime.icon)
                    container.tv_today_length_item_appname.text = appTime.name
                    container.tv_today_length_item_time.text = secondToHm(appTime.timeLength / 1000)

//                    val calendar = Calendar.getInstance()
//                    val longest = calendar.get(Calendar.HOUR_OF_DAY) * 1000 * 60 * 60 + calendar.get(Calendar.MINUTE) * 1000 * 60 + calendar.get(Calendar.SECOND) * 1000 + calendar.get(Calendar.MILLISECOND)
                    val lp = container.tv_today_length_item_line_empty.layoutParams
                    (lp as LinearLayout.LayoutParams).weight = (longest.toFloat() - appTime.timeLength.toFloat()) / appTime.timeLength.toFloat()
                    container.tv_today_length_item_line_empty.layoutParams = lp

                    val tvLine = container.tv_today_length_item_line
                    appTime.icon?.apply {
                        Palette.from(drawableToBitmap(this)).generate { pat ->
                            pat?.apply {
                                tvLine.setBackgroundResource(R.drawable.shape_item_line1)
                                val myGrad = tvLine.background as GradientDrawable
                                dominantSwatch?.let { swatch ->
                                    myGrad.setColor(swatch.rgb)
                                }

                            }
                        }
                    }

                }
            }
        }
        viewModel.refreshAppUsageTimeToday(1)
        viewModel.appAnalyzeTodayLiveData.observe(viewLifecycleOwner) {
            for (i in 0..2) {
                if (it.size > i) {
                    val appAnalyze = it[i]
                    val container = ll_data_app_usage_change.getChildAt(i)
                    container.iv_analyze_item_icon.setImageDrawable(appAnalyze.icon)
                    container.tv_analyze_item_appname.text = appAnalyze.name
                    container.tv_analyze_app_usage_3days.text = "近24小时：${secondToHm(appAnalyze.timeLengthPer3Days / 1000)}"
                    container.tv_analyze_app_usage_7days.text = "近7天日均：${secondToHm(appAnalyze.timeLengthPer7Days / 1000)}"
                    container.iv_analyze_is_up.setImageResource(
                        if (appAnalyze.isUp) {
                            R.drawable.ic_go_up
                        } else {
                            R.drawable.ic_go_down
                        }
                    )
                    container.tv_analyze_app_usage_change_time.text = secondToHm(appAnalyze.changeLength / 1000)
                }
            }

        }

        swipeRefreshLayout.setOnRefreshListener {
            refreshAll()
        }

        v.tv_analyze_today_see_all.setOnClickListener {
            startActivity(Intent(requireActivity(), TodayLockTrendActivity::class.java))
        }

        v.tv_analyze_app_time_see_all.setOnClickListener {
            val intent = Intent(requireActivity(), TodayAppUsageActivity::class.java)
            intent.putExtra("selectAppUsageDays", selectAppUsageDays)
            startActivity(intent)
        }

        v.tv_analyze_app_usage_see_all.setOnClickListener {
            startActivity(Intent(requireActivity(), AppUsageAnalyzeActivity::class.java))
        }
    }

    override fun onResume() {
//        MobclickAgent.onPageStart("AnalyzeFragment")
        super.onResume()
        Log.d(TAG, "onResume")
        refreshAll()
    }

    private fun refreshAll() {
        if (MMKVUtils.getBoolean(MyConstants.SP_KEY_ANALYZE_SETTING_SHOW_NETWORK_SORT, true)) {
            materialCardView3.visibility = View.VISIBLE
            materialCardView4.visibility = View.VISIBLE

            if (MMKVUtils.getInt(MyConstants.SP_KEY_USER_ID, -1) != -1) {
                materialCardView42.visibility = View.VISIBLE
            } else {
                materialCardView42.visibility = View.GONE
            }
            viewModel.refreshYesterdayTop()
            viewModel.refreshTodayTop()
            viewModel.refreshMyTrend()
            viewModel.refreshMyTrendYesterday()
        } else {
            materialCardView3.visibility = View.GONE
            materialCardView4.visibility = View.GONE
            materialCardView42.visibility = View.GONE
            v.srl_analyze.isRefreshing = false
        }
        viewModel.refreshLockLength(deltaWeek)
        viewModel.refreshAppUsageTimeToday(selectAppUsageDays)
        viewModel.refreshAppAnalyzeToday()

        if (MMKVUtils.getInt(MyConstants.SP_KEY_USER_ID, -1) != -1) {
            cl_analyze_need_login.visibility = View.GONE
        } else {
            cl_analyze_need_login.visibility = View.VISIBLE
            cl_analyze_need_login.setOnClickListener {
                startActivity(Intent(requireContext(), TheLoginActivity::class.java))
            }
        }
    }

    private fun initChart() {
        lineChart = v.trend_chart_time
        val mXAxis = lineChart.xAxis
        val mLeftYAxis = lineChart.axisLeft
        val mRightYAxis = lineChart.axisRight

        lineChart.description.text = ""
//        lineChart.description.text = "锁机时长"
//        lineChart.description.xOffset = 15f
//        lineChart.description.yOffset = 10f
//        lineChart.description.textColor = getColorFromTheme(requireContext(), R.attr.custom_attr_app_text_2)
        lineChart.legend.textColor = getColorFromTheme(requireContext(), R.attr.custom_attr_app_text_2)
        lineChart.animateY(1500)
        lineChart.setTouchEnabled(false)

        mLeftYAxis.isEnabled = false
        mRightYAxis.isEnabled = false

        mXAxis.position = XAxis.XAxisPosition.BOTTOM
        mXAxis.textColor = getColorFromTheme(requireContext(), R.attr.custom_attr_app_text_2)
        mXAxis.setDrawGridLines(false)
        mXAxis.axisLineColor = getColorFromTheme(requireContext(), R.attr.custom_attr_app_text_2)
        mXAxis.valueFormatter = object : ValueFormatter() {
            override fun getFormattedValue(value: Float): String {
                return when (value) {
                    0f -> "一"
                    1f -> "二"
                    2f -> "三"
                    3f -> "四"
                    4f -> "五"
                    5f -> "六"
                    6f -> "日"
                    else -> ""
                }
            }
        }

    }


    companion object {
        @JvmStatic
        fun newInstance(param1: String, param2: String) =
            AnalyzeFragment().apply {
                arguments = Bundle().apply {
                    putString(ARG_PARAM1, param1)
                    putString(ARG_PARAM2, param2)
                }
            }
    }


//    override fun onPause() {
//        MobclickAgent.onPageEnd("AnalyzeFragment")
//        super.onPause()
//    }
}
