package com.lijianqiang12.silent.component.activity.me.setting

import android.content.Intent
import android.os.Bundle
import android.view.View
import com.lijianqiang12.silent.utils.MMKVUtils
import com.lijianqiang12.silent.MyConstants
import com.lijianqiang12.silent.R
import com.lijianqiang12.silent.component.activity.base.BaseActivity
import com.lijianqiang12.silent.component.activity.me.vip.FROM_WHERE
import com.lijianqiang12.silent.component.activity.me.vip.VIP2Activity
import com.lijianqiang12.silent.utils.DialogUtil
import com.lijianqiang12.silent.utils.MyToastUtil
import com.lijianqiang12.silent.utils.MyUtil
import kotlinx.android.synthetic.main.activity_lock_setting.iv_setting_return
import kotlinx.android.synthetic.main.activity_settings.*


class SettingsActivity : BaseActivity() {


    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        setContentView(R.layout.activity_settings)


        iv_setting_return.setOnClickListener { finish() }

        //显示统计标签
        switch_show_statistic.isChecked = MMKVUtils.getBoolean(MyConstants.SP_KEY_SHOW_STATISTIC, true)
        switch_show_statistic.setOnCheckedChangeListener { buttonView, isChecked ->
            if (MyUtil.isVIP()) {
                MMKVUtils.put(MyConstants.SP_KEY_SHOW_STATISTIC, isChecked)
                MyToastUtil.showSuccess("重启软件后生效")
            } else {
                switch_show_statistic.isChecked = !isChecked
                DialogUtil.showVIPDialog(this, null, content = "定制模块显示为VIP专享功能，开通后，即可根据自身喜好定制显示模块。", "设置")

            }

        }

        //显示房间标签
        switch_show_room.isChecked = MMKVUtils.getBoolean(MyConstants.SP_KEY_SHOW_ROOM, true)
        switch_show_room.setOnCheckedChangeListener { buttonView, isChecked ->
            if (MyUtil.isVIP()) {
                MMKVUtils.put(MyConstants.SP_KEY_SHOW_ROOM, isChecked)
                MyToastUtil.showSuccess("重启软件后生效")
            } else {
                switch_show_room.isChecked = !isChecked
                DialogUtil.showVIPDialog(this, null, content = "定制模块显示为VIP专享功能，开通后，即可根据自身喜好定制显示模块。", "设置")

            }

        }

        //显示支付宝红包提醒
        switch_show_alimoney.isChecked = MMKVUtils.getBoolean(MyConstants.SP_KEY_SHOW_ALI, true)
        switch_show_alimoney.setOnCheckedChangeListener { buttonView, isChecked ->
            MMKVUtils.put(MyConstants.SP_KEY_SHOW_ALI, isChecked)
        }


        if (MyUtil.isVIP()) {
            tv_vip_show_statistic.visibility = View.GONE
        } else {
            tv_vip_show_statistic.visibility = View.VISIBLE
            tv_vip_show_statistic.setOnClickListener {
                val intent = Intent(this, VIP2Activity::class.java)
                intent.putExtra(FROM_WHERE, "SettingsActivity")
                startActivity(intent)
            }
        }

        if (MyUtil.isVIP()) {
            tv_vip_show_room.visibility = View.GONE
        } else {
            tv_vip_show_room.visibility = View.VISIBLE
            tv_vip_show_room.setOnClickListener {
                val intent = Intent(this, VIP2Activity::class.java)
                intent.putExtra(FROM_WHERE, "SettingsActivity")
                startActivity(intent)
            }
        }

    }

    override fun onResume() {
        super.onResume()
        if (MMKVUtils.getBoolean(MyConstants.SP_KEY_SHOW_ALI_SERVER, false)) {
            btn_show_alimoney.visibility = View.VISIBLE
        } else {
            btn_show_alimoney.visibility = View.GONE
        }
    }


}

