package com.lijianqiang12.silent.component.activity.analyze.todayusage

import android.graphics.drawable.GradientDrawable
import android.widget.ImageView
import android.widget.LinearLayout
import android.widget.TextView
import androidx.palette.graphics.Palette
import com.blankj.utilcode.util.LogUtils
import com.chad.library.adapter.base.BaseQuickAdapter
import com.chad.library.adapter.base.module.LoadMoreModule
import com.chad.library.adapter.base.viewholder.BaseViewHolder
import com.lijianqiang12.silent.R
import com.lijianqiang12.silent.component.activity.analyze.AppTime
import com.lijianqiang12.silent.utils.drawableToBitmap
import com.lijianqiang12.silent.utils.secondToHm

class TodayAppUsageAdapter( var longest: Long, layoutRes: Int, item: MutableList<AppTime>)
    : BaseQuickAdapter<AppTime, BaseViewHolder>(layoutRes, item) , LoadMoreModule {

    override fun convert(holder: BaseViewHolder, item: AppTime) {

        LogUtils.d("AppTime:${item.toString()}")
        holder.getView<ImageView>(R.id.iv_today_length_item_icon).setImageDrawable(item.icon)
        holder.getView<TextView>(R.id.tv_today_length_item_appname).text = item.name

        if (item.timeLength < 1000 * 60) {
            holder.getView<TextView>(R.id.tv_today_length_item_time).text = "小于1分钟"
        } else {
            holder.getView<TextView>(R.id.tv_today_length_item_time).text = secondToHm(item.timeLength / 1000)
        }
        val tvEmpty = holder.getView<TextView>(R.id.tv_today_length_item_line_empty)
        val tvLine = holder.getView<TextView>(R.id.tv_today_length_item_line)

//        val calendar = Calendar.getInstance()
//        val longest = calendar.get(Calendar.HOUR_OF_DAY) * 1000 * 60 * 60 + calendar.get(Calendar.MINUTE) * 1000 * 60 + calendar.get(Calendar.SECOND) * 1000 + calendar.get(Calendar.MILLISECOND)
        val lp = tvEmpty.layoutParams
        (lp as LinearLayout.LayoutParams).weight = (longest.toFloat() - item.timeLength.toFloat()) / item.timeLength.toFloat()
        tvEmpty.layoutParams = lp

        item.icon?.apply {
            Palette.from(drawableToBitmap(this)).generate {
                it?.apply {
                    tvLine.setBackgroundResource(R.drawable.shape_item_line1)
                    val myGrad = tvLine.background as GradientDrawable
                    dominantSwatch?.let { swatch ->
                        myGrad.setColor(swatch.rgb)
                    }

                }
            }
        }

    }
}