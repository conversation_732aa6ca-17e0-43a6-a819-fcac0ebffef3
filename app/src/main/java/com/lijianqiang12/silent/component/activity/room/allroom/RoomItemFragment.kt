package com.lijianqiang12.silent.component.activity.room.allroom

import android.content.Intent
import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.fragment.app.viewModels
import androidx.lifecycle.Observer
import androidx.lifecycle.lifecycleScope
import androidx.recyclerview.widget.DiffUtil
import androidx.recyclerview.widget.GridLayoutManager
import com.lijianqiang12.silent.MAX_ID
import com.lijianqiang12.silent.R
import com.lijianqiang12.silent.data.model.net.MyRetrofitClient
import com.lijianqiang12.silent.data.model.net.pojos.AllRoom
import com.lijianqiang12.silent.component.activity.base.BaseFragment
import com.lijianqiang12.silent.component.activity.custom.dialog.EditTextDialog
import com.lijianqiang12.silent.component.activity.room.roomdetail.DetailRoomActivity
import com.lijianqiang12.silent.data.viewmodel.RoomViewModel
import com.lijianqiang12.silent.utils.MyToastUtil
import dagger.hilt.android.AndroidEntryPoint
import kotlinx.android.synthetic.main.fragment_room_item.view.*
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.launch
import kotlinx.coroutines.withContext
import javax.inject.Inject

private const val ARG_PARAM1 = "position"

@AndroidEntryPoint
class RoomItemFragment : BaseFragment() {
    private var param1: Int = 0
    private var type: Int = 0
    private lateinit var v: View
    private lateinit var mAdapter: RoomAllAdapter
    private lateinit var mLayoutManager: androidx.recyclerview.widget.RecyclerView.LayoutManager
    private var allRooms: MutableList<AllRoom> = mutableListOf()

    @Inject
    lateinit var viewModel: RoomViewModel

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        arguments?.let {
            param1 = it.getInt(ARG_PARAM1)
            type = when (param1) {
                0 -> 100
                1 -> 101
                2 -> 1
                3 -> 2
                4 -> 17
                5 -> 3
                6 -> 5
                7 -> 4
                8 -> 6
                9 -> -1
                else -> -1
            }
        }
    }

    override fun onCreateView(inflater: LayoutInflater, container: ViewGroup?, savedInstanceState: Bundle?): View? {
        v = inflater.inflate(R.layout.fragment_room_item, container, false)
        return v
    }

    override fun lazyInit() {
        mLayoutManager = GridLayoutManager(requireContext(), 3)
        val recyclerview = v.rv_room_item
        recyclerview.layoutManager = mLayoutManager
        mAdapter = RoomAllAdapter(R.layout.item_room_all, mutableListOf())
        mAdapter.animationEnable = false
        mAdapter.loadMoreModule.setOnLoadMoreListener {
            viewModel.refreshAllRooms(type, 1, allRooms.last().roomId, allRooms.last().levelCount)
        }

        recyclerview.adapter = mAdapter

        mAdapter.setOnItemClickListener { adapter, view, position ->
            if ((adapter.data[position] as AllRoom).isJoined || !(adapter.data[position] as AllRoom).hasLock) {
                val intent = Intent(requireContext(), DetailRoomActivity::class.java)
                intent.putExtra("roomId", (adapter.data[position] as AllRoom).roomId)
                startActivity(intent)
            } else {
                EditTextDialog(this).apply {
                    setTitle("请输入房间密码")
                    isCancelable = false
                    setOnOKListener(object : EditTextDialog.OnOKListener {
                        override fun onclick(text: String) {

                            <EMAIL>(Dispatchers.IO) {
                                try {
                                    val result = MyRetrofitClient.service.verifyRoomPwd((adapter.data[position] as AllRoom).roomId, text)
                                    if (result.code == 200) {
                                        withContext(Dispatchers.Main) {
                                            MyToastUtil.showInfo(result.msg)
                                            val intent = Intent(<EMAIL>(), DetailRoomActivity::class.java)
                                            intent.putExtra("roomId", (adapter.data[position] as AllRoom).roomId)
                                            intent.putExtra("pwd", text)
                                            <EMAIL>().startActivity(intent)
                                        }
                                    } else {
                                        withContext(Dispatchers.Main) {
                                            MyToastUtil.showInfo(result.msg)
                                        }

                                    }
                                } catch (e: Exception) {

                                }
                            }

                        }
                    })

                    show()
                }
            }


        }

        val swipeRefreshLayout = v.srl_room_item
        swipeRefreshLayout.setOnRefreshListener {
            viewModel.refreshAllRooms(type, 0, MAX_ID, MAX_ID)
        }
        swipeRefreshLayout.isRefreshing = true
        viewModel.allRoomsLiveData.observe(viewLifecycleOwner, Observer {

            lifecycleScope.launch(Dispatchers.Default) {
                val diffResult = DiffUtil.calculateDiff(DiffCallBack(allRooms, it.data!!), true)
                withContext(Dispatchers.Main) {
                    (recyclerview.adapter as RoomAllAdapter).setNewInstance(it.data)
                    allRooms = it.data
                    diffResult.dispatchUpdatesTo(recyclerview.adapter as RoomAllAdapter)
                    when (it.state) {
                        0 -> mAdapter.loadMoreModule.loadMoreComplete()
                        1 -> mAdapter.loadMoreModule.loadMoreEnd()
                    }
                }
            }

            swipeRefreshLayout.isRefreshing = false
        })
        viewModel.refreshAllRooms(type, 0, MAX_ID, MAX_ID)
    }

    companion object {
        @JvmStatic
        fun newInstance(param1: Int) = RoomItemFragment().apply {
            arguments = Bundle().apply {
                putInt(ARG_PARAM1, param1)
            }
        }
    }

    private class DiffCallBack(val oldList: MutableList<AllRoom>, val newList: MutableList<AllRoom>) : DiffUtil.Callback() {

        override fun areItemsTheSame(oldItemPosition: Int, newItemPosition: Int): Boolean {
            return (oldList[oldItemPosition].roomId == newList[newItemPosition].roomId)
        }

        override fun getOldListSize(): Int {
            return oldList.size
        }

        override fun getNewListSize(): Int {
            return newList.size
        }

        override fun areContentsTheSame(oldItemPosition: Int, newItemPosition: Int): Boolean {
            return (oldList[oldItemPosition].roomName == newList[newItemPosition].roomName)
                    && (oldList[oldItemPosition].roomOnlineNumbers == newList[newItemPosition].roomOnlineNumbers)
                    && (oldList[oldItemPosition].roomCoverUrl == newList[newItemPosition].roomCoverUrl)
                    && (oldList[oldItemPosition].isJoined == newList[newItemPosition].isJoined)
        }

    }

//    override fun onResume() {
//        super.onResume()
//        MobclickAgent.onPageStart("RoomItemFragment")
//    }
//
//
//    override fun onPause() {
//        super.onPause()
//        MobclickAgent.onPageEnd("RoomItemFragment")
//    }
}