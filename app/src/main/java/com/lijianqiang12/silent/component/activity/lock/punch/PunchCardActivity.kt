package com.lijianqiang12.silent.component.activity.lock.punch

import android.content.Intent
import android.os.Bundle
import android.view.View
import androidx.lifecycle.lifecycleScope
import androidx.recyclerview.widget.LinearLayoutManager
import androidx.recyclerview.widget.RecyclerView
import com.lijianqiang12.silent.utils.MMKVUtils
import com.bumptech.glide.Glide
import com.bumptech.glide.load.engine.DiskCacheStrategy
import com.lijianqiang12.silent.MAX_ID
import com.lijianqiang12.silent.MyConstants
import com.lijianqiang12.silent.R
import com.lijianqiang12.silent.data.model.net.MyRetrofitClient
import com.lijianqiang12.silent.data.model.net.pojos.PunchCardMsg
import com.lijianqiang12.silent.component.activity.base.BaseActivity
import com.lijianqiang12.silent.component.activity.custom.dialog.*
import com.lijianqiang12.silent.utils.ImageUtil
import com.lijianqiang12.silent.utils.MyToastUtil
import com.lijianqiang12.silent.utils.TimeUtil
import kotlinx.android.synthetic.main.activity_punch_card.*
import kotlinx.android.synthetic.main.dialog_punch_card.view.iv_punch_card_img
import kotlinx.android.synthetic.main.dialog_punch_card.view.tv_punch_card_1_1
import kotlinx.android.synthetic.main.dialog_punch_card.view.tv_punch_card_2_1
import kotlinx.android.synthetic.main.dialog_punch_card.view.tv_punch_card_3_1
import kotlinx.android.synthetic.main.dialog_punch_card.view.tv_punch_card_lock_number
import kotlinx.android.synthetic.main.share_punch_card.view.*
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.delay
import kotlinx.coroutines.launch
import kotlinx.coroutines.withContext


class PunchCardActivity : BaseActivity() {

    private lateinit var recyclerview: RecyclerView
    private lateinit var adapter: PunchCardAdapter

    private lateinit var myDialog: MyProgressDialog

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        setContentView(R.layout.activity_punch_card)
        myDialog = MyProgressDialog(activity = this)

        if (MMKVUtils.getBoolean(MyConstants.SP_KEY_SHOW_PUNCH_CARD_RULE, true)) {
            NormalDialog(this).apply {
                setTitle("协议")
                isCancelable = false
                setContent("请勿在打卡信息中发布色情低俗、暴力血腥、政治谣言等各类违反法律法规及相关政策规定的信息，一旦发现，我们将严厉打击和处理，严重者将进行封号处理。净化网络环境，从你我做起。")
                setOnNormalOKClickListener("同意", object : OnOKClickListener {
                    override fun onclick() {
                        MMKVUtils.put(MyConstants.SP_KEY_SHOW_PUNCH_CARD_RULE, false)
                    }
                })
                setOnNormalCancelClickListener("不同意", object : OnCancelClickListener {
                    override fun onclick() {
                        <EMAIL>()
                    }
                })
                showDialog()
            }
        }

        iv_punch_card_return.setOnClickListener { finish() }

        btn_punch_card_record.setOnClickListener {
            startActivity(Intent(this, MyPunchCardActivity::class.java))
        }

        if (intent.getBooleanExtra("showDialog", false)) {
            val shareLayout = share_punch_card
            val cardTemple = card_template
            PunchCardDialog(this).apply {
                isCancelable = false
                setLength(intent.getLongExtra("length", 0L))
                setOnNormalOKClickListener(object : PunchCardDialog.OnPunchCardListener {
                    override fun onClick(punchCardMsg: PunchCardMsg, word: String, length: Long, isShare: Boolean) {
                        uploadCardMsg(punchCardMsg, word, length)
                        if (isShare) {

//                            XXPermissions.with(this@PunchCardActivity) // 申请权限
//                                    .permission(com.hjq.permissions.Permission.MANAGE_EXTERNAL_STORAGE)
//                                    .request(object : OnPermissionCallback {
//                                        override fun onGranted(permissions: List<String>, all: Boolean) {
                                                <EMAIL>(Dispatchers.IO) {
                                                    withContext(Dispatchers.Main) {
                                                        myDialog.show()
                                                        cardTemple.visibility = View.VISIBLE
                                                        shareLayout.tv_punch_card_lock_number.text = "第${punchCardMsg.lockNumber}次锁机"
                                                        Glide.with(this@PunchCardActivity).load(punchCardMsg.imgUrl)
                                                            //.transition(DrawableTransitionOptions.withCrossFade())
                                                            .diskCacheStrategy(DiskCacheStrategy.AUTOMATIC)
                                                            .into(shareLayout.iv_punch_card_img)
                                                        shareLayout.tv_punch_card_1_1.text = "${punchCardMsg.goOnLock}天"
                                                        shareLayout.tv_punch_card_2_1.text = TimeUtil.formatHHMMSimpleEn((length / 1000).toInt())//分钟
                                                        when {
                                                            punchCardMsg.totalLength / 60 / 60 > 0 -> {
                                                                shareLayout.tv_punch_card_3_1.text = "${punchCardMsg.totalLength / 60 / 60}h"
                                                            }
                                                            punchCardMsg.totalLength / 60 > 0 -> {
                                                                shareLayout.tv_punch_card_3_1.text = "${punchCardMsg.totalLength / 60}m"
                                                            }
                                                            else -> {
                                                                shareLayout.tv_punch_card_3_1.text = "${punchCardMsg.totalLength}s"
                                                            }
                                                        }
                                                        if (word.isEmpty()) {
                                                            shareLayout.tv_punch_card_word.text = MMKVUtils.getString(MyConstants.SP_KEY_WELL_KNOW_WORD, "")
                                                        } else {
                                                            shareLayout.tv_punch_card_word.text = word
                                                        }
                                                    }
//                                            ToastUtils.showLong()
                                                    delay(1000)
                                                    withContext(Dispatchers.Main) {
                                                        val afterWaterBitmap = ImageUtil.loadBitmapFromView(shareLayout)

                                                        ImageUtil.shareImage(this@PunchCardActivity, afterWaterBitmap)


//                                                        val image = UMImage(this@PunchCardActivity, afterWaterBitmap)//bitmap文件
//                                                        val thumb = UMImage(this@PunchCardActivity, afterWaterBitmap)
//                                                        image.setThumb(thumb)
//                                                        val shareBoardConfig = ShareBoardConfig()
//                                                        shareBoardConfig.setIndicatorVisibility(false)
//                                                        ShareAction(this@PunchCardActivity).withMedia(image).withText("")
//                                                                .setDisplayList(SHARE_MEDIA.QQ, SHARE_MEDIA.QZONE, SHARE_MEDIA.WEIXIN, SHARE_MEDIA.WEIXIN_CIRCLE,
//                                                                        SHARE_MEDIA.SINA, SHARE_MEDIA.MORE)
//                                                                .open(shareBoardConfig)
                                                        cardTemple.visibility = View.GONE
                                                        myDialog.dismiss()
                                                    }
                                                }
                                                <EMAIL>(Dispatchers.IO) {
                                                    try {
                                                        MyRetrofitClient.service.shareCard()
                                                    } catch (e: Exception) {

                                                    }
                                                }
//                                        }
//
//                                        override fun onDenied(permissions: List<String>, never: Boolean) {
//                                            MyToastUtil.showInfo("该功能需要读写存储权限")
//                                        }
//                                    })

                        }
                    }
                })
                setOnNormalCancelClickListener(object : OnCancelClickListener {
                    override fun onclick() {
                    }
                })
                show()
            }
        }


        recyclerview = rv_punch_card
        recyclerview.layoutManager = LinearLayoutManager(this)
        adapter = PunchCardAdapter(R.layout.item_punch_card, mutableListOf())
        adapter.animationEnable = true
        adapter.loadMoreModule.setOnLoadMoreListener {
            if (adapter.data.size == 0) {
                getNewData(MAX_ID)
            } else {
                getNewData(adapter.data[adapter.data.size - 1].punchCardId)
            }
        }

        recyclerview.adapter = adapter

        adapter.setOnItemClickListener { adapter, view, position ->

        }

        srl_punch_card.setOnRefreshListener {
            getNewData(MAX_ID)
        }

        srl_punch_card.isRefreshing = true
        getNewData(MAX_ID)

//        viewModel.lockBgLiveData.observe(viewLifecycleOwner, androidx.lifecycle.Observer {
//            lifecycleScope.launch(Dispatchers.Default) {
//                val diffResult = DiffUtil.calculateDiff(BgBottomSheetDialogFragment.DiffCallBack(bgList, it.data!!), true)
//                withContext(Dispatchers.Main) {
//                    (recyclerview.adapter as LockBgAdapter).setNewInstance(it.data)
//                    bgList = it.data
//                    diffResult.dispatchUpdatesTo(recyclerview.adapter as LockBgAdapter)
//                    when (it.state) {
//                        0 -> adapter.loadMoreComplete()
//                        1 -> adapter.loadMoreEnd()
//                    }
//                }
//            }
//        })
//        viewModel.refreshLockBg(0, MAX_ID)
    }


    private fun getNewData(lastId: Long) {
        lifecycleScope.launch(Dispatchers.IO) {
            try {
                val result = MyRetrofitClient.service.getPunchCards(lastId, false)
                withContext(Dispatchers.Main) {
                    if (result.code == 200) {
                        result.data?.let {
                            if (lastId == MAX_ID) {
                                adapter.setNewInstance(it)
                                adapter.loadMoreModule.loadMoreComplete()
                            } else {
                                if (it.isEmpty()) {
                                    adapter.loadMoreModule.loadMoreEnd()
                                } else {
                                    adapter.addData(it)
                                    adapter.loadMoreModule.loadMoreComplete()
                                }
                            }
                            adapter.notifyDataSetChanged()
                        }
                    } else {
                        MyToastUtil.showInfo(result.msg)
                    }
                }
            } catch (e: Exception) {
                withContext(Dispatchers.Main) {
                    MyToastUtil.showInfo(e.message)
                }
            } finally {
                withContext(Dispatchers.Main) {
                    srl_punch_card.isRefreshing = false
                }
            }
        }
    }


    fun uploadCardMsg(punchCardMsg: PunchCardMsg, word: String, length: Long) {
        lifecycleScope.launch(Dispatchers.IO) {
            val result = MyRetrofitClient.service.punchCard(punchCardMsg.lockNumber, punchCardMsg.goOnLock,
                    punchCardMsg.totalLength, length / 1000, punchCardMsg.imgUrl, word)
            when (result.code) {
                200 -> {
                    MyToastUtil.showInfo("打卡成功")
                    getNewData(MAX_ID)
                }
                60002 -> {
                    NormalDialog(this@PunchCardActivity).apply {
                        setTitle("警告")
                        isCancelable = false
                        setContent(result.msg)
                        setOnNormalOKClickListener("我知道了", object : OnOKClickListener {
                            override fun onclick() {

                            }
                        })

                        showDialog()
                    }

                }
                else -> {
                    MyToastUtil.showInfo(result.msg)
                }
            }

        }
    }

//    override fun onActivityResult(requestCode: Int, resultCode: Int, data: Intent?) {
//        super.onActivityResult(requestCode, resultCode, data)
//        UMShareAPI.get(this).onActivityResult(requestCode, resultCode, data)//QQ分享回调需要本activity或子fragment都在这里实现
//    }
//
//    override fun onDestroy() {
//        UMShareAPI.get(this).release()
//        super.onDestroy()
//    }
}