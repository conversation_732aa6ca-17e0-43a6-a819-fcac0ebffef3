package com.lijianqiang12.silent.component.activity.me

import android.content.ComponentName
import android.content.Intent
import android.graphics.Bitmap
import android.graphics.drawable.Drawable
import android.os.Bundle
import android.provider.MediaStore
import com.blankj.utilcode.util.AppUtils
import com.bumptech.glide.Glide
import com.bumptech.glide.load.engine.DiskCacheStrategy
import com.bumptech.glide.request.target.SimpleTarget
import com.bumptech.glide.request.transition.Transition
import com.lijianqiang12.silent.R
import com.lijianqiang12.silent.component.activity.base.BaseActivity
import com.lijianqiang12.silent.component.activity.custom.dialog.MyProgressDialog
import com.lijianqiang12.silent.databinding.ActivityWxactivityBinding
import com.lijianqiang12.silent.utils.MyToastUtil
import java.io.FileNotFoundException

class WXActivity : BaseActivity() {

    private lateinit var binding: ActivityWxactivityBinding

    private lateinit var progressDialog: MyProgressDialog
    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
//        enableEdgeToEdge()

        binding = ActivityWxactivityBinding.inflate(layoutInflater)
        setContentView(binding.root)

//        ViewCompat.setOnApplyWindowInsetsListener(binding.main) { v, insets ->
//            val systemBars = insets.getInsets(WindowInsetsCompat.Type.systemBars())
//            v.setPadding(systemBars.left, systemBars.top, systemBars.right, systemBars.bottom)
//            insets
//        }
        progressDialog = MyProgressDialog(null, this)
        binding.ivWxReturn.setOnClickListener {
            finish()
        }

        binding.btnSaveToGallery.setOnClickListener {

            Glide.with(this).asBitmap()
                .diskCacheStrategy(DiskCacheStrategy.AUTOMATIC)
                .load(R.drawable.qr_wechat_group)
                .into(object : SimpleTarget<Bitmap>() {
                    override fun onResourceReady(resource: Bitmap, transition: Transition<in Bitmap>?) {
                        progressDialog.dismiss()
                        try {
                            val fileName = "${AppUtils.getAppName()}微信群二维码-${System.currentTimeMillis()}.webp"
                            MediaStore.Images.Media.insertImage(
                                contentResolver,
                                resource,
                                fileName,
                                null
                            )

                            MyToastUtil.showInfo("保存成功")
                            val intent = Intent()
                            val cmp = ComponentName("com.tencent.mm", "com.tencent.mm.ui.LauncherUI")
                            intent.action = Intent.ACTION_MAIN
                            intent.addCategory(Intent.CATEGORY_LAUNCHER)
                            intent.addFlags(Intent.FLAG_ACTIVITY_NEW_TASK)
                            intent.component = cmp
                            startActivity(intent)
                        } catch (e: FileNotFoundException) {
                            e.printStackTrace()
                            progressDialog.dismiss()
                            MyToastUtil.showInfo("出问题啦")
                        }

                    }

                    override fun onLoadStarted(placeholder: Drawable?) {
                        super.onLoadStarted(placeholder)
                        progressDialog.show()
                    }

                    override fun onLoadFailed(errorDrawable: Drawable?) {
                        super.onLoadFailed(errorDrawable)
                        progressDialog.dismiss()
                        MyToastUtil.showInfo("保存失败")
                    }
                })

        }
    }
}