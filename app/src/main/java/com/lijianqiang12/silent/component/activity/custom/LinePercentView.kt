package com.lijianqiang12.silent.component.activity.custom

import android.content.Context
import android.graphics.Canvas
import android.graphics.Color
import android.graphics.Paint
import android.util.AttributeSet
import android.view.View
import com.lijianqiang12.silent.R
import com.lijianqiang12.silent.utils.dpToPixel
import com.lijianqiang12.silent.utils.getColorFromTheme


class LinePercentView : View {

    private var progress = -1f
    private val paint = Paint(Paint.ANTI_ALIAS_FLAG)
    private val paint2 = Paint(Paint.ANTI_ALIAS_FLAG)
    private val textPaint = Paint(Paint.ANTI_ALIAS_FLAG)

    init {
        textPaint.textSize = dpToPixel(14f)
    }

    constructor(context: Context) : super(context)
    constructor(context: Context, attrs: AttributeSet?) : super(context, attrs)
    constructor(context: Context, attrs: AttributeSet?, defStyleAttr: Int) : super(
        context,
        attrs,
        defStyleAttr
    )

    fun getProgress(): Float {
        return progress
    }

    fun setProgress(progress: Float) {
        this.progress = progress
        invalidate()
    }

    override fun onDraw(canvas: Canvas) {
        super.onDraw(canvas)

        //画外圈
        val centerX = width / 2.toFloat()
        val centerY = height / 2.toFloat()
        val marginHorizontal = height / 2.toFloat()

        paint.color = getColorFromTheme(context, R.attr.custom_attr_app_outer_line)//ContextCompat.getColor(context, R.color.colorUsageTimeBg)
        paint.style = Paint.Style.FILL
        paint.strokeCap = Paint.Cap.ROUND
        paint.strokeWidth = height.toFloat()
        canvas.drawLine(
            marginHorizontal,
            centerY,
            width.toFloat() - marginHorizontal,
            centerY,
            paint
        )

        if (progress == -1f) {
            //画内圈

            val padding = height / 8f

            paint2.color = getColorFromTheme(context, R.attr.custom_attr_app_inner_line)//ContextCompat.getColor(context, R.color.colorUsageTimeFg)
            paint2.style = Paint.Style.FILL
            paint2.strokeCap = Paint.Cap.ROUND
            paint2.strokeWidth = (height - 2 * padding).toFloat()
            canvas.drawLine(
                marginHorizontal,
                centerY,
                marginHorizontal,
                centerY,
                paint2
            )


            //写字
            textPaint.color = Color.WHITE
            textPaint.style = Paint.Style.FILL
            textPaint.textAlign = Paint.Align.CENTER
            canvas.drawText(
                "   Loading...",
                centerX,
                centerY - (textPaint.ascent() + textPaint.descent()) / 2,
                textPaint
            )
        } else if (progress == -2f) {
            //画内圈

            val padding = height / 8f

            paint2.color = getColorFromTheme(context, R.attr.custom_attr_app_inner_line)//ContextCompat.getColor(context, R.color.colorUsageTimeFg)
            paint2.style = Paint.Style.FILL
            paint2.strokeCap = Paint.Cap.ROUND
            paint2.strokeWidth = (height - 2 * padding).toFloat()
            canvas.drawLine(
                marginHorizontal,
                centerY,
                marginHorizontal,
                centerY,
                paint2
            )


            //写字
            textPaint.color = Color.WHITE
            textPaint.style = Paint.Style.FILL
            textPaint.textAlign = Paint.Align.CENTER
            canvas.drawText(
                "无限制",
                centerX,
                centerY - (textPaint.ascent() + textPaint.descent()) / 2,
                textPaint
            )
        } else {
            //画内圈
            //防止progress出现小于0的小数
            if (progress < 0) progress = 0f
            val padding = height / 8f

            paint2.color = getColorFromTheme(context, R.attr.custom_attr_app_inner_line)//ContextCompat.getColor(context, R.color.colorUsageTimeFg)
            paint2.style = Paint.Style.FILL
            paint2.strokeCap = Paint.Cap.ROUND
            paint2.strokeWidth = (height - 2 * padding).toFloat()
            canvas.drawLine(
                marginHorizontal,
                centerY,
                (width.toFloat() - 2 * marginHorizontal) * progress / 100 + marginHorizontal,
                centerY,
                paint2
            )

//            Log.d("LinePercentView", marginHorizontal.toString() + "   " + (width.toFloat() - marginHorizontal) * progress / 100)

            //写字
            textPaint.color = Color.WHITE
            textPaint.style = Paint.Style.FILL
            textPaint.textAlign = Paint.Align.CENTER
            canvas.drawText(
                "  " + progress.toInt().toString() + "%",
                centerX,
                centerY - (textPaint.ascent() + textPaint.descent()) / 2,
                textPaint
            )
        }

    }
}