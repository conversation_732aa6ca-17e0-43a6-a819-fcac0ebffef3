package com.lijianqiang12.silent.component.activity

import android.animation.ObjectAnimator
import android.content.Intent
import android.os.Bundle
import android.view.View
import com.lijianqiang12.silent.utils.MMKVUtils
import com.lijianqiang12.silent.MyConstants
import com.lijianqiang12.silent.R
import com.lijianqiang12.silent.component.activity.base.BaseActivity
import com.yl.lib.sentry.hook.PrivacySentry
import kotlinx.android.synthetic.main.activity_first_start.*


class StartActivity : BaseActivity() {
    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        setContentView(R.layout.activity_first_start)


        ObjectAnimator.ofFloat(cl_start_page_1, "alpha", 0.0f, 1.0f).setDuration(600).start()
        cl_start_page_1.visibility = View.VISIBLE

        fib_start_page_1.setOnClickListener {
            ObjectAnimator.ofFloat(cl_start_page_1, "alpha", 1.0f, 0.0f).setDuration(600).start()
            cl_start_page_1.visibility = View.GONE
            ObjectAnimator.ofFloat(cl_start_page_2, "alpha", 0.0f, 1.0f).setDuration(600).start()
            cl_start_page_2.visibility = View.VISIBLE
        }

        fib_start_page_2.setOnClickListener {
            ObjectAnimator.ofFloat(cl_start_page_2, "alpha", 1.0f, 0.0f).setDuration(600).start()
            cl_start_page_2.visibility = View.GONE
            ObjectAnimator.ofFloat(cl_start_page_3, "alpha", 0.0f, 1.0f).setDuration(600).start()
            cl_start_page_3.visibility = View.VISIBLE
        }

        btn_start_page_3.setOnClickListener {

            PrivacyBottomSheetDialogFragment.newInstance().apply {
                setOnOKSelectListener(object : PrivacyBottomSheetDialogFragment.OnOKSelectListener {
                    override fun onSelect() {
                        MMKVUtils.put(MyConstants.SP_KEY_SHOW_START_PAGE, false)
                        val intent = Intent(this@StartActivity, TheMainActivity::class.java)
                        <EMAIL>(intent)

                        val intent2 = Intent(this@StartActivity, TheLoginActivity::class.java)
                        <EMAIL>(intent2)

                        <EMAIL>()
                    }
                })
                setOnCancelSelectListener(object : PrivacyBottomSheetDialogFragment.OnCancelSelectListener {
                    override fun onSelect() {
                    }
                })
                show(supportFragmentManager, "PrivacyBottomSheetDialogFragment")
            }


        }
    }

//    override fun onResume() {
//        super.onResume()
//        LogUtils.d("ljq=======onResume=======StartActivity")
//    }
//    override fun onDestroy() {
//        super.onDestroy()
//
//        LogUtils.d("ljq=======onDestroy=======StartActivity")
//    }

}