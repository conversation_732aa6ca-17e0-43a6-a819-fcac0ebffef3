package com.lijianqiang12.silent.component.service.windows

import android.accessibilityservice.AccessibilityService
import android.app.Service
import android.content.Context
import android.content.pm.ActivityInfo
import android.graphics.PixelFormat
import android.os.Build
import android.view.Gravity
import android.view.LayoutInflater
import android.view.View
import android.view.WindowManager
import android.widget.TextView
import com.blankj.utilcode.util.ScreenUtils
import com.lijianqiang12.silent.MyConstants
import com.lijianqiang12.silent.R
import com.lijianqiang12.silent.TheApplication
import com.lijianqiang12.silent.component.service.MyAccessibilityService
import com.lijianqiang12.silent.utils.MyScreenUtils
import com.lijianqiang12.silent.utils.MyToastUtil
import com.lijianqiang12.silent.utils.MyWindowUtil
import com.lijianqiang12.silent.utils.PermissionUtil

/**
 * Created by li on 2018/10/29.
 * 屏蔽页面的悬浮窗
 */
class FloatWindowOfDenyPageNotice(val context: Context) {

    //    private var windowCreated = false
//    var windowShowing = false
    private lateinit var params: WindowManager.LayoutParams
    private lateinit var windowManager: WindowManager
    private var layout: View? = null

    private fun createView() {
        params = WindowManager.LayoutParams()
//        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
//            params.type = WindowManager.LayoutParams.TYPE_APPLICATION_OVERLAY
//        } else {
//            params.type = WindowManager.LayoutParams.TYPE_SYSTEM_ALERT
//        }
//        windowManager = context.applicationContext.getSystemService(Context.WINDOW_SERVICE) as WindowManager
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.LOLLIPOP_MR1
            && PermissionUtil.isAccessibilitySettingsOn(MyConstants.ACCESS_ABILITY_NAME, context.applicationContext)
            && TheApplication.getInstance().globalParams.accessibilityService != null
        ) {
            params.type = WindowManager.LayoutParams.TYPE_ACCESSIBILITY_OVERLAY
            windowManager = TheApplication.getInstance().globalParams.accessibilityService!!.getSystemService(Context.WINDOW_SERVICE) as WindowManager
        } else if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
            params.type = WindowManager.LayoutParams.TYPE_APPLICATION_OVERLAY
            windowManager = context.applicationContext.getSystemService(Context.WINDOW_SERVICE) as WindowManager
        } else {
            params.type = WindowManager.LayoutParams.TYPE_SYSTEM_ERROR
            windowManager = context.applicationContext.getSystemService(Context.WINDOW_SERVICE) as WindowManager
        }

        params.format = PixelFormat.TRANSLUCENT

        params.flags = params.flags or WindowManager.LayoutParams.FLAG_NOT_FOCUSABLE or
                WindowManager.LayoutParams.FLAG_NOT_TOUCH_MODAL or
                WindowManager.LayoutParams.FLAG_HARDWARE_ACCELERATED or
                WindowManager.LayoutParams.FLAG_FULLSCREEN or
                WindowManager.LayoutParams.FLAG_LAYOUT_IN_SCREEN or
                WindowManager.LayoutParams.FLAG_LAYOUT_NO_LIMITS


        params.gravity = Gravity.START or Gravity.TOP
        params.x = 0
        params.y = 0
        params.screenOrientation = if (MyScreenUtils.isScreenOrientationPortrait(context as Service)) {
            ActivityInfo.SCREEN_ORIENTATION_PORTRAIT
        } else {
            ActivityInfo.SCREEN_ORIENTATION_LANDSCAPE
        }
        params.width = ScreenUtils.getScreenWidth()
        params.height = ScreenUtils.getScreenHeight()

        layout = LayoutInflater.from(context).inflate(R.layout.layout_deny_page_notice, null)
//        windowCreated = true

        if (PermissionUtil.isAccessibilitySettingsOn(MyConstants.ACCESS_ABILITY_NAME, context.applicationContext)
            && TheApplication.getInstance().globalParams.accessibilityService != null
        ) {

            layout?.findViewById<TextView>(R.id.tv_deny_page_home)?.setOnClickListener {
                MyAccessibilityService.getInstance()?.apply {
                    performGlobalAction(AccessibilityService.GLOBAL_ACTION_HOME)
                }
            }

            layout?.findViewById<TextView>(R.id.tv_deny_page_back)?.setOnClickListener {
                MyAccessibilityService.getInstance()?.apply {
                    performGlobalAction(AccessibilityService.GLOBAL_ACTION_BACK)
                }
            }

            layout?.findViewById<TextView>(R.id.tv_notice_text3)?.visibility = View.GONE
            layout?.findViewById<TextView>(R.id.tv_deny_page_home)?.visibility = View.VISIBLE
            layout?.findViewById<TextView>(R.id.tv_deny_page_back)?.visibility = View.VISIBLE
        } else {

            layout?.findViewById<TextView>(R.id.tv_notice_text3)?.visibility = View.VISIBLE
            layout?.findViewById<TextView>(R.id.tv_deny_page_home)?.visibility = View.GONE
            layout?.findViewById<TextView>(R.id.tv_deny_page_back)?.visibility = View.GONE
        }

    }

    fun showWindow(name: String) {
        if (layout == null) {
            createView()
        }
        layout?.findViewById<TextView>(R.id.tv_notice_text)?.text = "您设置了锁机时屏蔽『$name』"
        if (!MyWindowUtil.isWindowShowing(layout!!)) {
//            windowShowing = true
            try {
                windowManager.addView(layout!!, params)
            } catch (e: Exception) {
                MyToastUtil.showError("DenyPageNoticeFloatWindow未成功获取到悬浮窗权限，请到系统设置或安全管家中重新授予，若已经授予，则可能是系统BUG，请重启手机")
//                windowShowing = false

            }
        }
    }


    fun hideWindow() {
        if (layout != null) {
            if (MyWindowUtil.isWindowShowing(layout!!)) {
                try {

                    windowManager.removeView(layout!!)
//                    windowShowing = false
                } catch (e: Exception) {
                    MyToastUtil.showError("未成功获取到悬浮窗权限，请到系统设置或安全管家中重新授予，若已经授予，则可能是系统BUG，请重启手机")
                }
            }
        }
    }


}