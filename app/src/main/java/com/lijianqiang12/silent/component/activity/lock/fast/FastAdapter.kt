package com.lijianqiang12.silent.component.activity.lock.fast

import android.widget.TextView
import com.chad.library.adapter.base.BaseQuickAdapter
import com.chad.library.adapter.base.module.LoadMoreModule
import com.chad.library.adapter.base.viewholder.BaseViewHolder
import com.lijianqiang12.silent.R
import com.lijianqiang12.silent.data.model.db.Fast
import com.lijianqiang12.silent.utils.TimeUtil

class FastAdapter(layoutRes: Int, list: MutableList<Fast>)
    : BaseQuickAdapter<Fast, BaseViewHolder>(layoutRes, list), LoadMoreModule {

    override fun convert(viewHolder: BaseViewHolder, item: Fast) {
        viewHolder.getView<TextView>(R.id.item_lock_fast_drag_title).text = "${TimeUtil.formatHHMMSimple(item.length)}"

    }
}