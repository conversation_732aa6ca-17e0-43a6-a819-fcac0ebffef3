package com.lijianqiang12.silent.component.activity.monitor.applimitcreate

import android.content.Intent
import android.os.Bundle
import android.view.Gravity
import android.view.LayoutInflater
import android.view.View
import androidx.activity.viewModels
import androidx.gridlayout.widget.GridLayout
import androidx.lifecycle.lifecycleScope
import com.blankj.utilcode.util.AppUtils
import com.lijianqiang12.silent.R
import com.lijianqiang12.silent.SAVE_MODE_CREATE
import com.lijianqiang12.silent.SAVE_MODE_UPDATE
import com.lijianqiang12.silent.data.model.db.AppLimit
import com.lijianqiang12.silent.component.activity.base.BaseActivity
import com.lijianqiang12.silent.component.activity.custom.dialog.*
import com.lijianqiang12.silent.component.activity.monitor.AppBottomSheetDialogFragment
import com.lijianqiang12.silent.component.activity.me.vip.FROM_WHERE
import com.lijianqiang12.silent.component.activity.me.vip.VIP2Activity
import com.lijianqiang12.silent.data.viewmodel.MonitorViewModel
import com.lijianqiang12.silent.utils.*
import dagger.hilt.android.AndroidEntryPoint
import javax.inject.Inject
import kotlinx.android.synthetic.main.activity_app_limit_create.*
import kotlinx.android.synthetic.main.activity_app_limit_create.rb_active_1
import kotlinx.android.synthetic.main.activity_app_limit_create.rb_active_2
import kotlinx.android.synthetic.main.activity_app_limit_create.tv_week_select_1
import kotlinx.android.synthetic.main.activity_app_limit_create.tv_week_select_2
import kotlinx.android.synthetic.main.activity_app_limit_create.tv_week_select_3
import kotlinx.android.synthetic.main.activity_app_limit_create.tv_week_select_4
import kotlinx.android.synthetic.main.activity_app_limit_create.tv_week_select_5
import kotlinx.android.synthetic.main.activity_app_limit_create.tv_week_select_6
import kotlinx.android.synthetic.main.activity_app_limit_create.tv_week_select_7
import kotlinx.android.synthetic.main.item_bottom_sheet_edit_selected.view.*
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.launch
import kotlinx.coroutines.withContext

@AndroidEntryPoint
class AppLimitCreateActivity : BaseActivity() {

    private var saveMode = SAVE_MODE_CREATE
    private var changed = false
    private lateinit var appLimit: AppLimit

    private val viewModel: MonitorViewModel by viewModels()
    var pkgList = mutableListOf<String>()

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        setContentView(R.layout.activity_app_limit_create)

        if (null == intent.getParcelableExtra<AppLimit>("appLimit")) {
            saveMode = SAVE_MODE_CREATE
            appLimit = AppLimit()
        } else {
            saveMode = SAVE_MODE_UPDATE
            appLimit = intent.getParcelableExtra<AppLimit>("appLimit")!!
        }

        iv_app_limit_return.setOnClickListener {
            back()
        }

        iv_edit_app_limit_save.setOnClickListener {
            save()
        }

        tv_vip_flag_app_limit.setOnClickListener {
            val intent = Intent(this, VIP2Activity::class.java)
            intent.putExtra(FROM_WHERE, "AppLimitCreate软件组个数限制")
            startActivity(intent)
        }


        /*初始化显示*/
        //任务名
        val tvAppLimitTitle = et_app_limit_title
        tvAppLimitTitle.setOnClickListener {
            val dialog = EditTextDialog(this)
            dialog.run {
                setCanEmpty(true)
                setOnOKListener(object : EditTextDialog.OnOKListener {
                    override fun onclick(text: String) {
                        changed = true
                        appLimit.title = text
                        refreshPage()
                    }
                })
                show()
            }
        }

        //是否激活
        if (appLimit.valid) {
            rb_active_1.isChecked = true
            rb_active_2.isChecked = false
        } else {
            rb_active_1.isChecked = false
            rb_active_2.isChecked = true
        }
        rg_active_active.setOnCheckedChangeListener { group, checkedId ->
            changed = true
            when (checkedId) {
                R.id.rb_active_1 -> {
                    appLimit.valid = true
                }

                R.id.rb_active_2 -> {
                    appLimit.valid = false
                }
            }
        }

        //按星期循环
        tv_week_select_1.setOnClickListener {
            changed = true
            appLimit.monday = !appLimit.monday

            refreshPage()
        }
        tv_week_select_2.setOnClickListener {
            changed = true
            appLimit.tuesday = !appLimit.tuesday

            refreshPage()
        }
        tv_week_select_3.setOnClickListener {
            changed = true
            appLimit.wednesday = !appLimit.wednesday

            refreshPage()
        }
        tv_week_select_4.setOnClickListener {
            changed = true
            appLimit.thursday = !appLimit.thursday

            refreshPage()
        }
        tv_week_select_5.setOnClickListener {
            changed = true
            appLimit.friday = !appLimit.friday

            refreshPage()
        }
        tv_week_select_6.setOnClickListener {
            changed = true
            appLimit.saturday = !appLimit.saturday

            refreshPage()
        }
        tv_week_select_7.setOnClickListener {
            changed = true
            appLimit.sunday = !appLimit.sunday

            refreshPage()
        }


        //限时开始时间
        val tvAppLimitStartTime = tv_app_limit_start_time
        tvAppLimitStartTime.setOnClickListener {
            TimeSelectDialog(this).run {
                setTitle("请选择开始时间")
                setHour((appLimit.startTime).toInt() / 3600)
                setMinute((appLimit.startTime).toInt() % 3600 / 60)
                setOnOKListener(object : TimeSelectDialog.OnOKListener {
                    override fun onclick(length: Int) {
                        appLimit.startTime = length * 60.toLong()
                        refreshPage()
                        changed = true
                    }
                })
                show()
            }
        }

        //限时结束时间
        val tvAppLimitEndTime = tv_app_limit_end_time
        tvAppLimitEndTime.setOnClickListener {
            TimeSelectDialog(this).run {
                setTitle("请选择结束时间")
                setHour((appLimit.endTime).toInt() / 3600)
                setMinute((appLimit.endTime).toInt() % 3600 / 60)
                setOnOKListener(object : TimeSelectDialog.OnOKListener {
                    override fun onclick(length: Int) {
                        appLimit.endTime = length * 60.toLong()
                        refreshPage()
                        changed = true
                    }
                })
                show()
            }
        }

        //限时时长
        val tvAppLimitLength = tv_app_limit_length
        tvAppLimitLength.setOnClickListener {
            TimeSelectDialog(this).run {
                setTitle("请选择时长")
                setHour((appLimit.limitLength).toInt() / 3600)
                setMinute((appLimit.limitLength).toInt() % 3600 / 60)
                setOnOKListener(object : TimeSelectDialog.OnOKListener {
                    override fun onclick(length: Int) {
                        appLimit.limitLength = length * 60.toLong()
                        refreshPage()
                        changed = true
                    }
                })
                show()
            }
        }

        //允许修改开始时间
        val tvEditStartTime = tv_edit_start_time
        tvEditStartTime.setOnClickListener {
            TimeSelectDialog(this).run {
                setTitle("请选择开始时间")
                setHour((appLimit.editStartTime).toInt() / 3600)
                setMinute((appLimit.editStartTime).toInt() % 3600 / 60)
                setOnOKListener(object : TimeSelectDialog.OnOKListener {
                    override fun onclick(length: Int) {
                        appLimit.editStartTime = length * 60.toLong()
                        refreshPage()
                        changed = true
                    }
                })
                show()
            }
        }

        //允许修改结束时间
        val tvEditEndTime = tv_edit_end_time
        tvEditEndTime.setOnClickListener {
            TimeSelectDialog(this).run {
                setTitle("请选择结束时间")
                setHour((appLimit.editEndTime).toInt() / 3600)
                setMinute((appLimit.editEndTime).toInt() % 3600 / 60)
                setOnOKListener(object : TimeSelectDialog.OnOKListener {
                    override fun onclick(length: Int) {
                        appLimit.editEndTime = length * 60.toLong()
                        refreshPage()
                        changed = true
                    }
                })
                show()
            }
        }

        //罚金
        val tvAppLimitPay = tv_app_limit_pay
        tvAppLimitPay.setOnClickListener {
            showSelectMoney("设置强制更改所需罚金")
        }

        refreshPage()
    }

    private fun showSelectMoney(title: String) {
        var initMoney = appLimit.editMoney.toInt()
        if (initMoney < 0) {
            initMoney = 5
        } else {
            initMoney /= 100
        }
        MyUtil.appLimitShowChooseMoneyDialog(this, title, initMoney, object : MyUtil.Companion.OnMoneySelectListener {
            override fun onMoneySelect(money: Int) {
                appLimit.editMoney = money * 100L
                refreshPage()
                changed = true

            }
        })
    }

//    private fun showSelectMoney() {
//        val bottomDialog = BottomSingleSelectDialogFragment.newInstance()
//        bottomDialog.setShowList(arrayListOf("不开启", "2元", "5元", "10元", "20元", "50元", "100元"))
//        bottomDialog.setValueList(arrayListOf(0, 200, 500, 1000, 2000, 5000, 10000))
//        bottomDialog.setOnValueSelectListener(object : BottomSingleSelectDialogFragment.OnValueSelectListener {
//            override fun onSelect(value: Long, show: String) {
//                if (value == 0L) {
//                    NormalDialog(this@AppLimitCreateActivity).apply {
//                        setTitle("警告")
//                        setGravity(Gravity.START)
//                        setContent("若不开启强制更改，您将无法在允许修改配置时间段外更改设置，为避免突发状况给您带来的不便，建议开启此功能。")
//                        setOnNormalOKClickListener("去开启", object : OnOKClickListener {
//                            override fun onclick() {
//                                showSelectMoney()
//                            }
//                        })
//                        setOnNormalCancelClickListener("不开启", object : OnCancelClickListener {
//                            override fun onclick() {
//                                appLimit.editMoney = value
//                                refreshPage()
//                                changed = true
//                            }
//                        })
//                        showDialog()
//                    }
//                } else {
//                    appLimit.editMoney = value
//                    refreshPage()
//                    changed = true
//                }
//            }
//        })
//        bottomDialog.show(supportFragmentManager, "")
//    }

    private fun refreshPage() {
        /*初始化显示*/
        //任务名
        val tvAppLimitTitle = et_app_limit_title
        tvAppLimitTitle.text = if (appLimit.title.isEmpty()) {
            "未命名"
        } else {
            appLimit.title
        }

        //被限制软件组
        pkgList = MyUtil.jsonToPkgList(appLimit.appPkg)
        val gridLayout = gridLayout
        gridLayout.removeAllViews()
        pkgList.forEachIndexed { index, appPkg ->
            val itemView = LayoutInflater.from(this).inflate(R.layout.item_gridlayout_selected_big, null)

            lifecycleScope.launch(Dispatchers.IO) {
                val appIcon = getAppIcon(appPkg, "")
                withContext(Dispatchers.Main) {
                    itemView.iv_app_icon.setImageDrawable(appIcon)
                }
            }

            val rowSpec: GridLayout.Spec = GridLayout.spec(index / 7, 1.0f)
            val columnSpec: GridLayout.Spec = GridLayout.spec(index % 7, 1.0f)
            val params: GridLayout.LayoutParams = GridLayout.LayoutParams(rowSpec, columnSpec)
            params.width = 0

            gridLayout.addView(itemView, params)

            lifecycleScope.launch(Dispatchers.IO) {
                val appName = getAppName(appPkg, "")
                withContext(Dispatchers.Main) {
                    itemView.setOnClickListener {
                        NormalDialog(this@AppLimitCreateActivity).apply {
                            setTitle("温馨提示")
//                            setTitle(appName)
                            setContent("请选择您想要进行的操作")
                            setGravity(Gravity.CENTER)
                            setOnNormalOKClickListener("删除", object : OnOKClickListener {
                                override fun onclick() {
                                    pkgList.remove(appPkg)
                                    appLimit.appPkg = MyUtil.pkgListToJson(pkgList)
                                    refreshPage()
                                    changed = true
                                }
                            })
                            setOnNormalCancelClickListener("取消", object : OnCancelClickListener {
                                override fun onclick() {

                                }
                            })
                            showDialog()
                        }
                    }
                }
            }


        }

        val itemViewAdd = LayoutInflater.from(this).inflate(R.layout.item_gridlayout_selected_big, null)
        itemViewAdd.iv_app_icon.setImageDrawable(resources.getDrawable(R.drawable.ic_add_rect))
        val rowSpecAdd: GridLayout.Spec = GridLayout.spec((pkgList.size) / 7, 1.0f)
        val columnSpecAdd: GridLayout.Spec = GridLayout.spec((pkgList.size) % 7, 1.0f)
        val paramsAdd: GridLayout.LayoutParams = GridLayout.LayoutParams(rowSpecAdd, columnSpecAdd)
        paramsAdd.width = 0

        gridLayout.addView(itemViewAdd, paramsAdd)
        itemViewAdd.setOnClickListener {

            if (MyUtil.isVIP() || pkgList.size < 1) {
//                val whiteBottomSheetFragment = WhiteBottomSheetDialogFragment.newInstance("")
//                whiteBottomSheetFragment.show(this.supportFragmentManager, "whiteBottomSheetFragment")

                val appBottomSheetFragment = AppBottomSheetDialogFragment.newInstance()

                appBottomSheetFragment.setOnAppSelectListener(object : AppBottomSheetDialogFragment.OnAppSelectListener {
                    override fun onclick(pkg: String, appName: String) {
                        if (pkg == AppUtils.getAppPackageName()) {
                            MyToastUtil.showWarning("为防止死锁，请勿为『${AppUtils.getAppName()}』设置限时")
                        } else if (pkgList.contains(pkg)) {
                            MyToastUtil.showInfo("无需重复添加")
                        } else {
                            pkgList.add(pkg)
                            appLimit.appPkg = MyUtil.pkgListToJson(pkgList)
                            refreshPage()
                            changed = true
                        }
                    }
                })
                appBottomSheetFragment.show(this.supportFragmentManager, "appBottomSheetFragment")
            } else {
                DialogUtil.showVIPDialog(this, null, "VIP用户可为每个应用限时任务添加多个app，开通后，享受软件组联合限时。", "3AppLimitCreate")
            }
        }

        for (i in 1..(6 - pkgList.size)) {
            val itemViewEmpty = LayoutInflater.from(this).inflate(R.layout.item_gridlayout_selected_big, null)
            val rowSpecEmpty: GridLayout.Spec = GridLayout.spec((i + pkgList.size) / 7, 1.0f)
            val columnSpecEmpty: GridLayout.Spec = GridLayout.spec((i + pkgList.size) % 7, 1.0f)
            val paramsEmpty: GridLayout.LayoutParams = GridLayout.LayoutParams(rowSpecEmpty, columnSpecEmpty)
            paramsEmpty.width = 0

            gridLayout.addView(itemViewEmpty, paramsEmpty)
        }


        //按星期循环
        if (appLimit.monday) {
            tv_week_select_1.setBackgroundResource(R.drawable.bg_week_select)
        } else {
            tv_week_select_1.setBackgroundResource(R.drawable.bg_week_not_select)
        }
        if (appLimit.tuesday) {
            tv_week_select_2.setBackgroundResource(R.drawable.bg_week_select)
        } else {
            tv_week_select_2.setBackgroundResource(R.drawable.bg_week_not_select)
        }
        if (appLimit.wednesday) {
            tv_week_select_3.setBackgroundResource(R.drawable.bg_week_select)
        } else {
            tv_week_select_3.setBackgroundResource(R.drawable.bg_week_not_select)
        }
        if (appLimit.thursday) {
            tv_week_select_4.setBackgroundResource(R.drawable.bg_week_select)
        } else {
            tv_week_select_4.setBackgroundResource(R.drawable.bg_week_not_select)
        }
        if (appLimit.friday) {
            tv_week_select_5.setBackgroundResource(R.drawable.bg_week_select)
        } else {
            tv_week_select_5.setBackgroundResource(R.drawable.bg_week_not_select)
        }
        if (appLimit.saturday) {
            tv_week_select_6.setBackgroundResource(R.drawable.bg_week_select)
        } else {
            tv_week_select_6.setBackgroundResource(R.drawable.bg_week_not_select)
        }
        if (appLimit.sunday) {
            tv_week_select_7.setBackgroundResource(R.drawable.bg_week_select)
        } else {
            tv_week_select_7.setBackgroundResource(R.drawable.bg_week_not_select)
        }


        //限时开始时间
        val tvAppLimitStartTime = tv_app_limit_start_time
        tvAppLimitStartTime.text = TimeUtil.formatHHMMEn((appLimit.startTime / 60).toInt())


        //限时结束时间
        val tvAppLimitEndTime = tv_app_limit_end_time
        var text1 = ""
        if (appLimit.startTime >= appLimit.endTime) {
            text1 += "次日"
        }
        tvAppLimitEndTime.text = "${text1}${TimeUtil.formatHHMMEn((appLimit.endTime / 60).toInt())}"


        //限时时长
        val tvAppLimitLength = tv_app_limit_length
        tvAppLimitLength.text = TimeUtil.formatHHMMWithHM((appLimit.limitLength).toInt())


        //允许修改开始时间
        val tvEditStartTime = tv_edit_start_time
        tvEditStartTime.text = TimeUtil.formatHHMMEn((appLimit.editStartTime / 60).toInt())


        //允许修改结束时间
        val tvEditEndTime = tv_edit_end_time
        var text2 = ""
        if (appLimit.editStartTime >= appLimit.editEndTime) {
            text2 += "次日"
        }
        tvEditEndTime.text = "${text2}${TimeUtil.formatHHMMEn((appLimit.editEndTime / 60).toInt())}"


        //罚金
        val tvAppLimitPay = tv_app_limit_pay
        tvAppLimitPay.text = if (appLimit.editMoney < 0L) {
            "未设置"
        } else if (appLimit.editMoney == 0L) {
            "禁止更改"
        } else {
            "${appLimit.editMoney / 100}元"
        }

    }

    private fun save() {

        if (pkgList.size <= 0) {
            MyToastUtil.showWarning("请至少添加一个被限时的软件")
            return
        } else if (!appLimit.sunday && !appLimit.monday && !appLimit.tuesday && !appLimit.wednesday && !appLimit.thursday && !appLimit.friday && !appLimit.saturday) {
            MyToastUtil.showWarning("请至少选中一周中的一天")
            return
        }

        if (appLimit.editMoney < 0) {
            showSelectMoney("尚未设置强制更改所需罚金")
            return
        }

        if (!MyUtil.isCurrentInTimeRange(appLimit.editStartTime, appLimit.editEndTime)) {
            NormalDialog(this).apply {
                setTitle("温馨提示")
                setContent(
                    "当前时间未在您设置的允许更改配置时间（${
                        MyUtil.getTimeRangeString(
                            appLimit.editStartTime,
                            appLimit.editEndTime
                        )
                    }）内，一旦确定，需要等到允许更改配置时间才能更改。"
                )
                setGravity(Gravity.START)
                setOnNormalOKClickListener("确定保存", object : OnOKClickListener {
                    override fun onclick() {
                        realSave()
                    }
                })
                setOnNormalCancelClickListener("取消", object : OnCancelClickListener {
                    override fun onclick() {

                    }
                })
                showDialog()
            }
            return
        }

        realSave()

    }

    fun realSave() {
        if (saveMode == SAVE_MODE_CREATE) {
            viewModel.createAppLimit(appLimit)
        } else {
            viewModel.updateAppLimit(appLimit)
        }
        finish()
    }

    override fun onBackPressed() {
        back()
    }

    private fun back() {
        if (changed) {
            NormalDialog(this).run {
                setTitle("警告")
                setContent("您有数据尚未保存，是否先保存？")
                setOnNormalOKClickListener("保存并退出", object : OnOKClickListener {
                    override fun onclick() {
                        save()
                    }
                })
                setOnNormalCancelClickListener("直接退出", object : OnCancelClickListener {
                    override fun onclick() {
                        finish()
                    }
                })
                showDialog()
            }
        } else {
            finish()
        }
    }

    override fun onResume() {
        super.onResume()
        if (MyUtil.isVIP()) {
            tv_vip_flag_app_limit.visibility = View.GONE
        } else {
            tv_vip_flag_app_limit.visibility = View.VISIBLE
        }
    }
}