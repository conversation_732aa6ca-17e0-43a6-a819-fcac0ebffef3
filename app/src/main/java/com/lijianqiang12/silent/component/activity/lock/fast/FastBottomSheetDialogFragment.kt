package com.lijianqiang12.silent.component.activity.lock.fast

import android.content.Intent
import android.os.Bundle
import android.view.Gravity
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.fragment.app.viewModels
import androidx.recyclerview.widget.ItemTouchHelper
import androidx.recyclerview.widget.LinearLayoutManager
import androidx.recyclerview.widget.RecyclerView
import com.afollestad.materialdialogs.MaterialDialog
import com.afollestad.materialdialogs.callbacks.onDismiss
import com.afollestad.materialdialogs.customview.customView
import com.google.android.material.bottomsheet.BottomSheetBehavior
import com.google.android.material.chip.Chip
import com.lijianqiang12.silent.MyConstants
import com.lijianqiang12.silent.R
import com.lijianqiang12.silent.SAVE_MODE_CREATE
import com.lijianqiang12.silent.component.activity.PermissionActivity
import com.lijianqiang12.silent.component.activity.base.BaseBottomSheetDialogFragment
import com.lijianqiang12.silent.component.activity.custom.dialog.NormalDialog
import com.lijianqiang12.silent.component.activity.custom.dialog.OnCancelClickListener
import com.lijianqiang12.silent.component.activity.custom.dialog.OnOKClickListener
import com.lijianqiang12.silent.component.activity.me.vip.FROM_WHERE
import com.lijianqiang12.silent.component.activity.me.vip.VIP2Activity
import com.lijianqiang12.silent.data.model.db.Fast
import com.lijianqiang12.silent.data.model.db.LockHistory
import com.lijianqiang12.silent.data.model.db.WhiteApp
import com.lijianqiang12.silent.data.viewmodel.InjectorUtils
import com.lijianqiang12.silent.data.viewmodel.LockViewModel
import com.lijianqiang12.silent.utils.*
import kotlinx.android.synthetic.main.bottom_sheet_lock_fast.view.*
import kotlinx.android.synthetic.main.dialog_lock_fast.view.*
import kotlinx.android.synthetic.main.dialog_lock_fast_drag.view.*
import java.util.*


class FastBottomSheetDialogFragment : BaseBottomSheetDialogFragment() {

    private lateinit var mBehavior: BottomSheetBehavior<View>
    private lateinit var customView: View
    private var fastList: MutableList<Fast> = mutableListOf()
    private var globalWhiteList: MutableList<WhiteApp> = mutableListOf()
//    var swipeState = ItemTouchHelper.ACTION_STATE_IDLE

    private val viewModel: LockViewModel by viewModels {
        InjectorUtils.provideLockViewModelFactory(requireContext())
    }

    companion object {
        @JvmStatic
        fun newInstance(): FastBottomSheetDialogFragment {
            return FastBottomSheetDialogFragment()
        }
    }


    override fun onCreateView(inflater: LayoutInflater, container: ViewGroup?, savedInstanceState: Bundle?): View {
        customView = View.inflate(requireContext(), R.layout.bottom_sheet_lock_fast, null)
        return customView
    }


    override fun onStart() {
        super.onStart()
        val parentView = customView.parent as View
        parentView.setBackgroundColor(resources.getColor(R.color.colorTranslate))
        mBehavior = BottomSheetBehavior.from(parentView)

        mBehavior.state = BottomSheetBehavior.STATE_EXPANDED
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
//        viewModel.setUserId(MMKVUtils.getInt(MyConstants.SP_KEY_USER_ID, -1))
        viewModel.globalWhiteAppsLiveData.observe(viewLifecycleOwner) {
            globalWhiteList = it
        }

        viewModel.fastsLiveData.observe(viewLifecycleOwner) {
            fastList = it
            customView.cg_lock_fast.removeAllViews()
            it.forEachIndexed { index, fast ->
                val chip = Chip(requireContext())
                chip.setChipBackgroundColorResource(R.color.colorLockFastChipGroup)
                chip.setTextColor(resources.getColor(R.color.colorLockFastChip))
                chip.text = "${TimeUtil.formatHHMMSimple(fast.length)}"

                if (index >= 3 && !MyUtil.isVIP()) {
                    chip.alpha = 0.3f
                    chip.text = chip.text.toString() + " (VIP可用)"
                    chip.setOnClickListener {
                        DialogUtil.showVIPDialog(
                            null, this, "VIP用户可使用3个以上简单锁机，开通后，享受无限锁机设置。", "3FastLimit",
                            title = "VIP已过期",
                            positiveText = "续订VIP",
                            negativeText = "删除该项",
                            onNegativeListener = object : OnCancelClickListener {
                                override fun onclick() {
                                    viewModel.deleteFast(fast)
                                    MyToastUtil.showInfo("删除成功")
                                }
                            },
                        )
                    }
                } else {
                    chip.setOnClickListener {
                        showFastDialog(fast, 1)
                    }
                }

                customView.cg_lock_fast.addView(chip)

            }

        }

        customView.btn_lock_fast_new.setOnClickListener {
            if ((fastList.size >= 3) && (!MyUtil.isVIP())) {
//                val intent = Intent(requireContext(), VIP2Activity::class.java)
//                intent.putExtra(FROM_WHERE, "3FastLimit")
//                requireContext().startActivity(intent)
//                MyToastUtil.showInfo(requireContext().applicationContext, "VIP用户可创建3个以上快捷锁机")
                DialogUtil.showVIPDialog(null, this, "VIP用户可创建3个以上简单锁机，开通后，享受无限锁机设置。", "3FastLimit")

            } else {
                showFastDialog(Fast(1), SAVE_MODE_CREATE)
            }
        }


        customView.iv_lock_fast_sort.setOnClickListener {
            val customView = LayoutInflater.from(requireContext()).inflate(R.layout.dialog_lock_fast_drag, null)
            val dialog = MaterialDialog(requireContext()).customView(R.layout.dialog_lock_fast_drag, customView, false).cornerRadius(8.0f)
            val mLayoutManager = LinearLayoutManager(activity)
            customView.rv_lock_fast_drag.layoutManager = mLayoutManager
            val mAdapter = FastAdapter(R.layout.item_lock_fast_drag, mutableListOf())
            mAdapter.animationEnable = true
            customView.rv_lock_fast_drag.adapter = mAdapter
            val itemTouchHelper = ItemTouchHelper(FastItemCallback(this, viewModel))
            itemTouchHelper.attachToRecyclerView(customView.rv_lock_fast_drag)
            mAdapter.addData(fastList)
            mAdapter.notifyDataSetChanged()
            dialog.onDismiss {
            }
            dialog.show()
        }

        if (MyUtil.isVIP()) {
            customView.tv_vip_flag_fast.visibility = View.GONE
        } else {
            customView.tv_vip_flag_fast.visibility = View.VISIBLE
            customView.tv_vip_flag_fast.setOnClickListener {
                val intent = Intent(requireActivity(), VIP2Activity::class.java)
                intent.putExtra(FROM_WHERE, "3FastCard")
                startActivity(intent)
            }

        }
    }

    private fun getLeftTimeText(dayTemp: Int, hourTemp: Int, minuteTemp: Int): String {
        val current = System.currentTimeMillis()


        return "预计结束时间：${
            formatRelativeTime(
                Calendar.getInstance(),
                current,
                current + (dayTemp * 60 * 24 + hourTemp * 60 + minuteTemp).toLong() * 60 * 1000
            )
        }"


    }

    /**
     * type 0:新增 1:修改
     */
    private fun showFastDialog(fast: Fast, type: Int) {

        fast.let {
            var dayTemp = it.length / 60 / 24
            var hourTemp = it.length / 60 % 24
            var minuteTemp = it.length % 60
            val customView = LayoutInflater.from(requireContext()).inflate(R.layout.dialog_lock_fast, null)

            if (MyUtil.isVIP()) {
                customView.tv_vip_flag_day.visibility = View.GONE
            } else {
                customView.tv_vip_flag_day.visibility = View.VISIBLE
                customView.tv_vip_flag_day.setOnClickListener {
                    val intent = Intent(requireContext(), VIP2Activity::class.java)
                    intent.putExtra(FROM_WHERE, "Fast100Hour")
                    startActivity(intent)
                }
            }

            customView.tv_fast_end_time.text = getLeftTimeText(dayTemp, hourTemp, minuteTemp)
            val dialog = MaterialDialog(requireContext()).customView(R.layout.dialog_lock_fast, customView, false).cornerRadius(8.0f)

            val dataDay: MutableList<String> = mutableListOf()
            for (i in 0..99) {
                if (i < 10) {
                    dataDay.add("0$i")
                } else {
                    dataDay.add("$i")
                }
            }
            customView!!.tpv_lock_fast_day.setData(dataDay)
            customView.tpv_lock_fast_day.setOnSelectListener { day ->
                dayTemp = day.toInt()
                customView.tv_fast_end_time.text = getLeftTimeText(dayTemp, hourTemp, minuteTemp)
            }

            val dataHour: MutableList<String> = mutableListOf()
            for (i in 0..23) {
                if (i < 10) {
                    dataHour.add("0$i")
                } else {
                    dataHour.add("$i")
                }
            }
            customView!!.tpv_lock_fast_hour.setData(dataHour)
            customView.tpv_lock_fast_hour.setOnSelectListener { hour ->
                hourTemp = hour.toInt()
                customView.tv_fast_end_time.text = getLeftTimeText(dayTemp, hourTemp, minuteTemp)
            }

            val dataMinute: MutableList<String> = mutableListOf()
            for (i in 0..59) {
                if (i < 10) {
                    dataMinute.add("0$i")
                } else {
                    dataMinute.add("$i")
                }
            }
            customView.tpv_lock_fast_minute.setData(dataMinute)
            customView.tpv_lock_fast_minute.setOnSelectListener { minute ->
                minuteTemp = minute.toInt()
                customView.tv_fast_end_time.text = getLeftTimeText(dayTemp, hourTemp, minuteTemp)

            }

            customView.tpv_lock_fast_day.setSelected(fast.length / 60 / 24)
            customView.tpv_lock_fast_hour.setSelected(fast.length / 60 % 24)
            customView.tpv_lock_fast_minute.setSelected(fast.length % 60)

            customView.btn_dialog_lock_fast_save.setOnClickListener {
                val lengthTemp = dayTemp * 60 * 24 + hourTemp * 60 + minuteTemp
                if (lengthTemp == 0) {
                    MyToastUtil.showInfo("时间不能设置为0")
                } else if (!MyUtil.isVIP() && lengthTemp >= 60 * 24) {
                    DialogUtil.showVIPDialog(null, this, "VIP用户可设置24小时以上锁机时长，开通后，享受最大99天锁机。", "lock100DaySetting")
                } else {
                    fast.length = lengthTemp

                    if (type == SAVE_MODE_CREATE) {
                        viewModel.createFast(fast)
                        MyToastUtil.showInfo("创建成功")
                    } else {
                        viewModel.updateFast(fast)
                        MyToastUtil.showInfo("修改成功")
                    }
                    dialog.dismiss()
                }

            }

            if (type == SAVE_MODE_CREATE) {
                customView.btn_dialog_lock_fast_delete.visibility = View.GONE
            } else {
                customView.btn_dialog_lock_fast_delete.setOnClickListener {
//                    viewModel.deleteFast(fast)
//                    dialog.dismiss()

                    NormalDialog(this).apply {
                        setTitle("警告")
                        setContent("确定删除该简单锁机吗？")
                        setGravity(Gravity.CENTER)
                        setOnNormalOKClickListener("删除", object : OnOKClickListener {
                            override fun onclick() {
                                viewModel.deleteFast(fast)
                                MyToastUtil.showInfo("删除成功")
                                dialog.dismiss()
                            }
                        })

                        setOnNormalCancelClickListener("取消", object : OnCancelClickListener {
                            override fun onclick() {
                                MyToastUtil.showInfo("取消删除")
                            }
                        })

                        showDialog()
                    }
                }
            }

            customView.btn_dialog_lock_fast_do.setOnClickListener {


//                val intent = Intent(requireContext(), VIP2Activity::class.java)
//                val pendingIntent: PendingIntent = PendingIntent.getActivity(
//                    requireContext(),
//                    11111, intent, PendingIntent.FLAG_UPDATE_CURRENT
//                )
//                val notification: Notification = Notification.Builder(requireContext(), "test")
//                    .setSmallIcon(R.drawable.ic_like)
//                    .setContentTitle("Incoming call")
//                    .setContentText("(919) 555-1234")
//                    .setPriority(Notification.PRIORITY_HIGH)
//                    .setCategory(NotificationCompat.CATEGORY_ALARM) //设置全屏通知后，发送通知直接启动Activity
//                    .setFullScreenIntent(pendingIntent, true)
//                    .build()
//                val manager: NotificationManager? = getSystemService(requireContext(),NotificationManager::class.java)
//                manager?.notify(10, notification)


                if (PermissionUtil.hasAllPermission(requireActivity())) {
//                    if (!MyUtil.showChooseMoneyDialog(requireContext())) {
                    if (isLockRunning()) {
                        MyToastUtil.showInfo("有一个锁机任务还在运行中")
                    } else {
                        val timeLength = (dayTemp * 60 * 24 + hourTemp * 60 + minuteTemp).toLong() * 60 * 1000
//                        LogUtils.d("${(dayTemp * 60 * 24 + hourTemp * 60 + minuteTemp)}!!!!!!!!!" + timeLength)
                        if (timeLength == 0L) {
                            MyToastUtil.showInfo("时间不能设置为0")
                        } else if (!MyUtil.isVIP() && timeLength >= 60 * 60 * 24 * 1000) {
                            DialogUtil.showVIPDialog(null, this, "VIP用户可设置24小时以上锁机时长，开通后，享受最大99天锁机。", "lock100DaySetting")
                        } else {
                            val current = System.currentTimeMillis()
                            val lockHistory = LockHistory(
                                0,
                                "简单锁机：${TimeUtil.formatHHMM(dayTemp * 60 * 24 + hourTemp * 60 + minuteTemp)}",
                                "",
                                current, current, timeLength, timeLength, 1, dayTemp * 60 * 24 + hourTemp * 60 + minuteTemp,
                                "", "", isFinish = false, isForceQuit = false, isSynced = false, isGeneratedCard = false,
                                deleteWhiteAppTemp = "[]"
                            )
                            viewModel.createLockHistory(lockHistory)
                            dialog.dismiss()
                            this.dismiss()
                            MyToastUtil.showInfo("锁机即将启动")
//                            requireActivity().finish()
                        }
                    }
//                    }
                } else {
                    MyToastUtil.showError("有未授予的权限")
                    requireActivity().startActivity(Intent(requireContext(), PermissionActivity::class.java))
                }
            }

            dialog.show()

        }

    }

    private class FastItemCallback(private val fragment: FastBottomSheetDialogFragment, val viewModel: LockViewModel) : ItemTouchHelper.Callback() {
        override fun getMovementFlags(p0: androidx.recyclerview.widget.RecyclerView, p1: androidx.recyclerview.widget.RecyclerView.ViewHolder): Int {
            return makeMovementFlags(ItemTouchHelper.UP or ItemTouchHelper.DOWN, 0)
        }

        override fun onMove(recycler: RecyclerView, holder1: RecyclerView.ViewHolder, holder2: RecyclerView.ViewHolder): Boolean {

            val fromPosition: Int = holder1.getBindingAdapterPosition()
            val toPosition: Int = holder2.getBindingAdapterPosition()

            if (fromPosition < (recycler.adapter as FastAdapter).data.size &&
                toPosition < (recycler.adapter as FastAdapter).data.size
            ) {

                if (fromPosition < toPosition) {
                    for (i in fromPosition until toPosition) {
                        swapData(recycler, i, i + 1)
                    }
                } else {
                    for (i in fromPosition downTo toPosition + 1) {
                        swapData(recycler, i, i - 1)
                    }
                }

            }

            return true
        }


        override fun onSwiped(recycler: RecyclerView.ViewHolder, p1: Int) {

        }

        override fun isLongPressDragEnabled(): Boolean {
            return true
        }

        fun swapData(recycler: RecyclerView, position1: Int, position2: Int) {
            Collections.swap((recycler.adapter as FastAdapter).data, position1, position2)
            (recycler.adapter as FastAdapter).notifyItemMoved(position1, position2)

            swapDataTrendWithMinimalImpact((recycler.adapter as FastAdapter).data, position1, position2)
        }

        fun swapDataTrendWithMinimalImpact(dataList: MutableList<Fast>, pos1: Int, pos2: Int) {
            if (pos1 !in dataList.indices || pos2 !in dataList.indices) return

            val item1 = dataList[pos1]
            val item2 = dataList[pos2]

            //这里修改pos1的trend，因为拖动的是pos2，这样如果trend都是0，拖一圈就能全改了。
            if (item1.trend == item2.trend) {
                val newTrendForItem1 = generateNearestUniqueTrend(dataList, item1.trend)
                item1.trend = newTrendForItem1
            } else {
                // 如果trend不相等，交换trend值
                item1.trend = item2.trend.also { item2.trend = item1.trend }
            }
            viewModel.updateFast(item1, sync = false)
            viewModel.updateFast(item2, sync = false)

        }

        private fun generateNearestUniqueTrend(dataList: List<Fast>, baseTrend: Int): Int {
            var newTrend = baseTrend + 1
            while (dataList.any { it.trend == newTrend }) {
                newTrend += 1
            }
            return newTrend
        }

//        override fun onSelectedChanged(viewHolder: RecyclerView.ViewHolder?, actionState: Int) {
//            super.onSelectedChanged(viewHolder, actionState)
//            LogUtils.d("onSelectedChanged actionState=${actionState}")
//            fragment.swipeState = actionState
//        }

    }

}