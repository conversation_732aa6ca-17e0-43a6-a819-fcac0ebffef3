package com.lijianqiang12.silent.component.activity.me.room_request

import android.content.Context
import android.widget.TextView
import com.bumptech.glide.Glide
import com.bumptech.glide.load.engine.DiskCacheStrategy
import com.chad.library.adapter.base.BaseQuickAdapter
import com.chad.library.adapter.base.module.LoadMoreModule
import com.chad.library.adapter.base.viewholder.BaseViewHolder
import com.lijianqiang12.silent.R
import com.lijianqiang12.silent.data.model.net.pojos.RoomRequestBean

class RoomRequestAdapter(context: Context, layoutRes: Int, list: MutableList<RoomRequestBean>) :
    BaseQuickAdapter<RoomRequestBean, BaseViewHolder>(layoutRes, list),
    LoadMoreModule {

    override fun convert(viewHolder: BaseViewHolder, item: RoomRequestBean) {


        Glide.with(context).load(item.imageUrl)
            //.transition(DrawableTransitionOptions.withCrossFade())
            .diskCacheStrategy(DiskCacheStrategy.AUTOMATIC)
            .into(viewHolder.getView(R.id.iv_room_bg))
        viewHolder.getView<TextView>(R.id.tv_room_creator_id).text = "创建者ID：${item.creatorId}"
        viewHolder.getView<TextView>(R.id.tv_room_name).text = "房间名：${item.roomName}"
        viewHolder.getView<TextView>(R.id.tv_room_desc).text = "房间简介：${item.roomDesc}"
        viewHolder.getView<TextView>(R.id.tv_room_password).text = "房间密码：${item.password}"
        viewHolder.getView<TextView>(R.id.tv_room_type).text = "房间类型：${getRoomTypeName(item.roomType)}"
        viewHolder.getView<TextView>(R.id.tv_time).text = "申请时间：${item.requestTime}"

        viewHolder.getView<TextView>(R.id.tv_change_type).text = "${arrayListOf("创建", "修改")[item.changeType]}"
        viewHolder.getView<TextView>(R.id.tv_change_type)
            .setTextColor(arrayListOf(context.resources.getColor(R.color.colorDialogSave), context.resources.getColor(R.color.colorRed))[item.changeType])


    }


    fun getRoomTypeName(index: Int): String {
        return when (index) {
            1 -> "考研"
            2 -> "高考"
            17 -> "中考"
            3 -> "英语"
            5 -> "早睡"
            4 -> "情侣"
            6 -> "班级"
            -1 -> "未分类"
            else -> "未知"
        }


    }
}