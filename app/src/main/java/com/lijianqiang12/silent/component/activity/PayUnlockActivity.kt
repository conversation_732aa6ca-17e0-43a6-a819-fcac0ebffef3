package com.lijianqiang12.silent.component.activity

import android.os.Bundle
import android.os.Handler
import android.os.Message
import android.text.method.LinkMovementMethod
import androidx.lifecycle.Observer
import androidx.lifecycle.lifecycleScope
import com.alipay.sdk.app.PayTask
import com.lijianqiang12.silent.utils.MMKVUtils
import com.jeremyliao.liveeventbus.LiveEventBus
import com.lijianqiang12.silent.LiveBus
import com.lijianqiang12.silent.MyConstants
import com.lijianqiang12.silent.R
import com.lijianqiang12.silent.TheApplication
import com.lijianqiang12.silent.component.activity.base.BaseActivity
import com.lijianqiang12.silent.data.viewmodel.VIPViewModel
import com.lijianqiang12.silent.utils.MyToastUtil
import com.lijianqiang12.silent.utils.MyUtil
import com.lijianqiang12.silent.utils.PayResult
import com.tencent.mm.opensdk.openapi.IWXAPI
import com.tencent.mm.opensdk.openapi.WXAPIFactory
import dagger.hilt.android.AndroidEntryPoint
import androidx.activity.viewModels
import kotlinx.android.synthetic.main.activity_pay_force_unlock.*
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.launch
import kotlinx.coroutines.withContext

@AndroidEntryPoint
class PayUnlockActivity : BaseActivity() {

    private val viewModel: VIPViewModel by viewModels()
    private lateinit var api: IWXAPI
    private val SDK_PAY_FLAG = 1


    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        setContentView(R.layout.activity_pay_force_unlock)

        val str =
            "1.罚金解锁需要联网。\n2.强制解锁后会关闭正在执行的定时任务或全天监督。\n3.罚金解锁金额可在“锁机设置-罚金”中调整。\n4.缴纳罚金后在1小时内可补差价购买永久版vip。"

        tv_pay_notice.text = str
        //一定要记得设置这个方法  不是不起作用
        tv_pay_notice.movementMethod = LinkMovementMethod.getInstance()



        // ViewModel 现在通过 Hilt 自动注入
        api = WXAPIFactory.createWXAPI(this, MyConstants.WX_APP_ID)



        LiveEventBus.get(LiveBus.FORCE_UNLOCK_PAGE_FINISH, String::class.java).observe(this, Observer {
            finish()
        })



        btn_vip_alipay.setOnClickListener {

            MyUtil.checkLoginAndDo(this) {

                MMKVUtils.put(MyConstants.SP_KEY_PAY_TYPE, 1)
                if (MyUtil.checkPackageInstalled(applicationContext, "com.eg.android.AlipayGphone", MyConstants.URL_ALIPAY)) {

                    lifecycleScope.launch(Dispatchers.IO) {
                        try {
                            val result = viewModel.vipRepository.makeAlipayOrderForceUnlock(
                                MyUtil.getForceUnlockMoney(),
                                "${
                                    getUnlockInfo(MMKVUtils.getInt(MyConstants.SP_KEY_FORCE_UNLOCK_TYPE, 0))
                                }，${TheApplication.getInstance().globalParams.currentRunningLockWorkInfo}"
                            )
                            withContext(Dispatchers.Main) {
                                if (result.code == 200) {
                                    LiveEventBus.get(LiveBus.USER_FORCE_UNLOCK, String::class.java).post("payStart")
                                    val payRunnable = Runnable {
                                        val alipay = PayTask(this@PayUnlockActivity)
                                        val payResult = alipay.payV2(result.data!!.order, true) as Map<String, String>
                                        val msg = Message()
                                        msg.what = SDK_PAY_FLAG
                                        msg.obj = payResult
                                        mHandler.sendMessage(msg)
                                    }
                                    val payThread = Thread(payRunnable)
                                    payThread.start()
                                } else {
                                    MyToastUtil.showError(result.msg)
                                }
                            }
                        } catch (e: Exception) {
                            MyToastUtil.showInfo(e.message)
                        }
                    }
                }
            }
        }

        btn_vip_wxpay.setOnClickListener {

            MyUtil.checkLoginAndDo(this) {

                MMKVUtils.put(MyConstants.SP_KEY_PAY_TYPE, 1)
                if (MyUtil.checkPackageInstalled(this, "com.tencent.mm", MyConstants.URL_WXPAY)) {

                    lifecycleScope.launch(Dispatchers.IO) {
                        try {
                            LiveEventBus.get(LiveBus.USER_FORCE_UNLOCK, String::class.java).post("payStart")
                            val result = viewModel.vipRepository.makeWXOrderForceUnlock(
//                            MMKVUtils.getInt(MyConstants.SP_KEY_FORCE_UNLOCK_PUNISH, 5),
                                MyUtil.getForceUnlockMoney(),
                                "${
                                    getUnlockInfo(MMKVUtils.getInt(MyConstants.SP_KEY_FORCE_UNLOCK_TYPE, 0))
                                }，${TheApplication.getInstance().globalParams.currentRunningLockWorkInfo}"
                            )
                            withContext(Dispatchers.Main) {
                                if (result.code == 200) {
                                    val request = com.tencent.mm.opensdk.modelpay.PayReq()
                                    request.appId = result.data!!.appId
                                    request.partnerId = result.data.partnerId
                                    request.prepayId = result.data.prepayId
                                    request.packageValue = result.data.packageValue
                                    request.nonceStr = result.data.nonceStr
                                    request.timeStamp = result.data.timeStamp
                                    request.sign = result.data.sign
                                    api.sendReq(request)
                                } else {
                                    MyToastUtil.showError(result.msg)
                                }
                            }
                        } catch (e: Exception) {
                            MyToastUtil.showInfo(e.message)
                        }
                    }
                }
            }
        }
    }


    private val mHandler = Handler {
        if (it.what == SDK_PAY_FLAG) {
            val payResult = PayResult(it.obj as Map<String, String>)
            when (payResult.resultStatus) {
                "6001" -> {//支付取消
                    MyToastUtil.showInfo("支付失败")
                }

                else -> {
                    LiveEventBus.get(LiveBus.USER_FORCE_UNLOCK, String::class.java).post("payFinish")

//                    paySucceed()
//                    viewModel.refreshOrderSuccess()
                }
            }
        }
        true
    }


}

fun getUnlockInfo(forceUnlockType: Int): String {
    return when (forceUnlockType) {
        1 -> {
            "暂停5分钟"
        }

        2 -> {
            "暂停15分钟"
        }

        3 -> {
            "暂停30分钟"
        }

        4 -> {
            "暂停1小时"
        }

        5 -> {
            "直接解锁"
        }

        6 -> {
            "跳过今天"
        }

        7 -> {
            "直接解锁并关闭"
        }

        8 -> {
            "跳过今天"
        }

        9 -> {
            "直接解锁并关闭"
        }

        else -> {
            "$forceUnlockType"
        }
    }
}