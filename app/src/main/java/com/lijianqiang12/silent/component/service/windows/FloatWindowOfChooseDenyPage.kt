package com.lijianqiang12.silent.component.service.windows

import android.app.Service
import android.content.Context
import android.content.pm.ActivityInfo
import android.graphics.PixelFormat
import android.graphics.drawable.Drawable
import android.os.Build
import android.view.*
import com.blankj.utilcode.util.ConvertUtils
import com.blankj.utilcode.util.GsonUtils
import com.lijianqiang12.silent.utils.MMKVUtils
import com.blankj.utilcode.util.ScreenUtils
import com.lijianqiang12.silent.MyConstants
import com.lijianqiang12.silent.R
import com.lijianqiang12.silent.TheApplication
import com.lijianqiang12.silent.component.activity.lock.setting.DENY_PAGE_TYPE_CUSTOM
import com.lijianqiang12.silent.component.activity.lock.setting.TheDenyPage2
import com.lijianqiang12.silent.data.model.net.pojos.Page
import com.lijianqiang12.silent.utils.*
import kotlinx.android.synthetic.main.layout_deny_page_pick.view.*
import kotlin.math.sqrt

class FloatWindowOfChooseDenyPage(val context: Context) {

    private lateinit var params: WindowManager.LayoutParams
    private lateinit var windowManager: WindowManager
    private var layout: View? = null
    private var isMoving = false
    private var name = ""
    private var pkg = ""
    private var activity = ""
    private var isShowing = false

//    companion object {
//        val instance by lazy(LazyThreadSafetyMode.SYNCHRONIZED) {
//            FloatWindowOfChooseDenyPage(TheApplication.getInstance())
//        }
//    }

    private fun createView() {
        params = WindowManager.LayoutParams()
//        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.LOLLIPOP_MR1 && MyAccessibilityService.isAccessibilityActive() && context is MyAccessibilityService) {
//            params.type = WindowManager.LayoutParams.TYPE_ACCESSIBILITY_OVERLAY
//        } else
//        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
//            params.type = WindowManager.LayoutParams.TYPE_APPLICATION_OVERLAY
//        } else {
//            params.type = WindowManager.LayoutParams.TYPE_SYSTEM_ALERT
//        }

        // 使用无障碍模式，方便屏蔽系统设置中的页面
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.LOLLIPOP_MR1
            && PermissionUtil.isAccessibilitySettingsOn(MyConstants.ACCESS_ABILITY_NAME, context.applicationContext)
            && TheApplication.getInstance().globalParams.accessibilityService != null
        ) {
            params.type = WindowManager.LayoutParams.TYPE_ACCESSIBILITY_OVERLAY
            windowManager = TheApplication.getInstance().globalParams.accessibilityService!!.getSystemService(Context.WINDOW_SERVICE) as WindowManager
        } else if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
            params.type = WindowManager.LayoutParams.TYPE_APPLICATION_OVERLAY
            windowManager = context.applicationContext.getSystemService(Context.WINDOW_SERVICE) as WindowManager
        } else {
            params.type = WindowManager.LayoutParams.TYPE_SYSTEM_ERROR
            windowManager = context.applicationContext.getSystemService(Context.WINDOW_SERVICE) as WindowManager
        }


        params.format = PixelFormat.TRANSLUCENT

        params.flags = params.flags or WindowManager.LayoutParams.FLAG_NOT_FOCUSABLE or
                WindowManager.LayoutParams.FLAG_NOT_TOUCH_MODAL or
                WindowManager.LayoutParams.FLAG_HARDWARE_ACCELERATED
        params.gravity = Gravity.START or Gravity.TOP
        params.x = 0
        params.y = ConvertUtils.dp2px(40f)//ScreenUtils.getScreenHeight() / 8
        params.screenOrientation = if (MyScreenUtils.isScreenOrientationPortrait(context as Service)) {
            ActivityInfo.SCREEN_ORIENTATION_PORTRAIT
        } else {
            ActivityInfo.SCREEN_ORIENTATION_LANDSCAPE
        }
        params.width = WindowManager.LayoutParams.WRAP_CONTENT
        params.height = WindowManager.LayoutParams.WRAP_CONTENT

        layout = LayoutInflater.from(context).inflate(R.layout.layout_deny_page_pick, null)
        layout?.let {
            it.tv_deny_page_ok.setOnClickListener { v ->
                if (it.et_deny_page_title.text.isEmpty()) {
                    MyToastUtil.showWarning("请先输入页面名称")
                } else if (pkg == UsageUtil.getLauncherPackageName(context.applicationContext)) {
                    MyToastUtil.showWarning("不能屏蔽系统桌面")
                } else {
                    if (MyUtil.isVIP() || getDenyPageValidCount() < 3) {

                        val denyPageList = MyGsonUtil.getDenyPageList()

                        var ifContains = false
                        var valid = true

                        run denyPageListLoop@{
                            denyPageList.forEach { theDenyPage ->
                                if (theDenyPage.type == DENY_PAGE_TYPE_CUSTOM) {
                                    if (theDenyPage.pages.isNotEmpty()) {
                                        val page = theDenyPage.pages.first()
                                        if (page.pkg == pkg && page.activity == activity) {
                                            ifContains = true
                                            if (!theDenyPage.valid) {
                                                valid = false
                                                theDenyPage.valid = true
                                                MyToastUtil.showSuccess("激活成功")

                                                MMKVUtils.put(MyConstants.SP_KEY_DENY_PAGE_V2, GsonUtils.toJson(denyPageList))
                                                changeToSmallView()
                                                it.et_deny_page_title.text.clear()

                                            }
                                            return@denyPageListLoop
                                        }
                                    }
                                }
                            }
                        }

                        if (ifContains) {
                            if (valid) {
                                MyToastUtil.showError("该屏蔽设置已存在，无需重复添加")
                            }
                        } else {
                            MyToastUtil.showSuccess("添加成功")

                            val existDenyPageList = MyGsonUtil.getDenyPageList()
                            var id = -1L
                            existDenyPageList.forEach { page ->
                                if (page.id < id) id = page.id - 1
                            }

                            val newDenyPage = TheDenyPage2(
                                id, it.et_deny_page_title.text.toString(), mutableListOf(Page(pkg, activity)), true, 0,
                                DENY_PAGE_TYPE_CUSTOM
                            )
                            denyPageList.add(newDenyPage)

                            MMKVUtils.put(MyConstants.SP_KEY_DENY_PAGE_V2, GsonUtils.toJson(denyPageList))
                            changeToSmallView()
                            it.et_deny_page_title.text.clear()
                        }
                    } else {
                        MyToastUtil.showInfo("VIP可添加3个以上屏蔽页面")
                    }

                }
            }
            it.tv_deny_page_cancel.setOnClickListener {
                hideWindow()
            }
            it.iv_change_small.setOnClickListener {
                changeToSmallView()
            }
            it.iv_change_big.setOnClickListener {
                changeToBigView()
            }
//            layout.tv_lock_view_monitor.setOnClickListener {
//                changeToBigView()
//            }


            var lastX = 0f
            var lastY = 0f
            var startX = 0f
            var startY = 0f
            it.setOnTouchListener { v, event ->

                when (event.action) {
                    MotionEvent.ACTION_DOWN -> {
                        it.setBackgroundResource(R.drawable.shape_lock_view_rest_16)
                        lastX = event.rawX
                        lastY = event.rawY
                        startX = event.rawX
                        startY = event.rawY
                        isMoving = true
                    }

                    MotionEvent.ACTION_MOVE -> {
                        params.x += (event.rawX - lastX).toInt()
                        params.y += (event.rawY - lastY).toInt()

                        windowManager.updateViewLayout(it, params)
                        lastX = event.rawX
                        lastY = event.rawY

                    }

                    MotionEvent.ACTION_UP -> {
                        if (it.width / 2 - event.x + event.rawX < ScreenUtils.getScreenWidth() / 2) {//left
                            params.x = 0
                            it.setBackgroundResource(R.drawable.shape_lock_view_rest_left_16)
                        } else {
                            params.x = ScreenUtils.getScreenWidth() - it.width
                            it.setBackgroundResource(R.drawable.shape_lock_view_rest_right_16)
                        }
                        windowManager.updateViewLayout(it, params)
                        isMoving = false

                        if (sqrt((event.rawX - startX).toDouble()) + sqrt((event.rawY - startY).toDouble()) < 1) {
                            if (it.cl_deny_page_big.visibility == View.VISIBLE) {
                                changeToSmallView()
                            } else {
                                changeToBigView()
                            }
                        }
                    }
                }
                false

            }
        }


//        windowCreated = true
    }

    private fun changeToSmallView() {
        layout?.let {
            it.cl_deny_page_big.visibility = View.GONE
            it.cl_deny_page_small.visibility = View.VISIBLE

            params.flags = params.flags or WindowManager.LayoutParams.FLAG_NOT_FOCUSABLE
            windowManager.updateViewLayout(it, params)
        }

    }

    private fun changeToBigView() {
        layout?.let {
            it.cl_deny_page_big.visibility = View.VISIBLE
            it.cl_deny_page_small.visibility = View.GONE

            params.flags = params.flags xor WindowManager.LayoutParams.FLAG_NOT_FOCUSABLE
            windowManager.updateViewLayout(it, params)
        }
    }

    fun showWindow() {
        if (isShowing) return

        if (layout == null) {
            createView()
        }
        if (!MyWindowUtil.isWindowShowing(layout!!)) {
//            windowShowing = true
            try {
                windowManager.addView(layout!!, params)
                isShowing = true
            } catch (e: Exception) {
                MyToastUtil.showError(e.toString())
//                MyToastUtil.showError("DenyPageFloatWindow未成功获取到悬浮窗权限，请到系统设置或安全管家中重新授予，若已经授予，则可能是系统BUG，请重启手机")
//                windowShowing = false
//                LogUtils.e(e)
            }
        }
    }

    fun refreshWindow(icon: Drawable, name: String, pkg: String, activity: String) {
        if (isShowing) {
            this.name = name
            this.pkg = pkg
            this.activity = activity
            layout?.let {
                it.iv_deny_page_app_icon.setImageDrawable(icon)
                it.tv_deny_page_app_name.text = name
                it.tv_deny_page_info.text = "${pkg}\n${activity}"
            }

        }
    }

    fun hideWindow() {
        if (layout != null) {
            if (MyWindowUtil.isWindowShowing(layout!!)) {
                try {

                    windowManager.removeView(layout)
                    isShowing = false
                    changeToSmallView()
//                    windowShowing = false
                } catch (e: Exception) {
//                    MyToastUtil.showError("未成功获取到悬浮窗权限，请到系统设置或安全管家中重新授予，若已经授予，则可能是系统BUG，请重启手机")

                    MyToastUtil.showError(e.toString())
                }
            }
        }
    }

    fun isShowing(): Boolean {
        return isShowing
//        return layout != null && MyWindowUtil.isWindowShowing(layout!!)
    }

//    fun showAndHide(text: String, icon: Drawable) {
//        if (windowShowing) return
//        showWindow(text, icon, true)
//        Handler().postDelayed(kotlinx.coroutines.Runnable {
//            hideWindow(true)
//        }, 8000)
//    }
}