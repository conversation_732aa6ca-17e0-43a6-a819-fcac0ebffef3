package com.lijianqiang12.silent.component.activity.me

import android.app.ProgressDialog
import android.content.ClipData
import android.content.ClipboardManager
import android.content.Context
import android.os.Bundle
import android.view.View
import androidx.lifecycle.lifecycleScope
import com.lijianqiang12.silent.R
import com.lijianqiang12.silent.component.activity.base.BaseActivity
import com.lijianqiang12.silent.data.model.net.MyRetrofitClient
import com.lijianqiang12.silent.utils.MyToastUtil
import com.lijianqiang12.silent.utils.MyUtil
import kotlinx.android.synthetic.main.activity_unlock_friend.*
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.launch
import kotlinx.coroutines.withContext

class UnlockFriendActivity : BaseActivity() {

    var mSubUnlockCode = ""
    lateinit var dialog: ProgressDialog
    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        setContentView(R.layout.activity_unlock_friend)

        dialog = ProgressDialog(this)

        iv_return_unlock_friend.setOnClickListener { finish() }

        iv_copy_sub_unlock_code.setOnClickListener {
            if (mSubUnlockCode.isEmpty()) {
                MyToastUtil.showError("获取解锁码碎片失败")
                return@setOnClickListener
            }
            val clipboard: ClipboardManager = this.applicationContext.getSystemService(Context.CLIPBOARD_SERVICE) as ClipboardManager
            val clipData = ClipData.newPlainText(null, mSubUnlockCode)
            clipboard.setPrimaryClip(clipData)
            MyToastUtil.showSuccess("解锁码碎片复制成功")
        }

        et_user_code.setOnTextChangeListener { text, isComplete ->

            if (isComplete) {
                MyUtil.showDialog(dialog, "正在获取...")

                lifecycleScope.launch(Dispatchers.IO) {
                    try {
                        val result = MyRetrofitClient.service.getUnlockCode(text)
                        if (result.code == 200) {
                            withContext(Dispatchers.Main) {
                                result.data?.apply {
                                    MyToastUtil.showSuccess("获取成功")
                                    mSubUnlockCode = result.data.subUnlockCode
                                    cl_sub_unlock_code.visibility = View.VISIBLE
                                    tv_sub_unlock_code.text = subUnlockCode
                                }
                            }

                        } else {
                            MyToastUtil.showError(result.msg)
                        }
                        MyUtil.hideDialog(dialog)
                    } catch (e: Exception) {
                        MyToastUtil.showError(e.message)
                        MyUtil.hideDialog(dialog)
                    }
                }
            }
        }
    }
}