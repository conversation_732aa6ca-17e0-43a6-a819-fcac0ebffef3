package com.lijianqiang12.silent.component.activity.lock.whiteapp

import android.content.Context
import android.content.Intent
import android.graphics.Color
import android.os.Bundle
import android.util.TypedValue
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.TextView
import androidx.appcompat.app.AppCompatActivity
import androidx.appcompat.widget.SearchView
import androidx.fragment.app.viewModels
import androidx.lifecycle.lifecycleScope
import androidx.recyclerview.widget.DiffUtil
import androidx.recyclerview.widget.GridLayoutManager
import androidx.recyclerview.widget.ItemTouchHelper
import androidx.recyclerview.widget.RecyclerView
import com.blankj.utilcode.util.AppUtils
import com.google.android.material.bottomsheet.BottomSheetBehavior
import com.jeremyliao.liveeventbus.LiveEventBus
import com.lijianqiang12.silent.LiveBus
import com.lijianqiang12.silent.MyConstants
import com.lijianqiang12.silent.R
import com.lijianqiang12.silent.component.activity.base.BaseBottomSheetDialogFragment
import com.lijianqiang12.silent.component.activity.custom.dialog.LimitTimeEditDialog
import com.lijianqiang12.silent.component.activity.custom.dialog.LockAppLimitDialog
import com.lijianqiang12.silent.component.activity.me.vip.FROM_WHERE
import com.lijianqiang12.silent.component.activity.me.vip.VIP2Activity
import com.lijianqiang12.silent.data.model.db.WhiteApp
import com.lijianqiang12.silent.data.viewmodel.InjectorUtils
import com.lijianqiang12.silent.data.viewmodel.LockViewModel
import com.lijianqiang12.silent.utils.DialogUtil
import com.lijianqiang12.silent.utils.MMKVUtils
import com.lijianqiang12.silent.utils.MyToastUtil
import com.lijianqiang12.silent.utils.MyUtil
import com.lijianqiang12.silent.utils.dpToPixel
import com.lijianqiang12.silent.utils.secondToSimpleHm
import kotlinx.android.synthetic.main.bottom_sheet_lock_white.view.liv_white
import kotlinx.android.synthetic.main.bottom_sheet_lock_white.view.rv_lock_global_white
import kotlinx.android.synthetic.main.bottom_sheet_lock_white.view.rv_lock_global_white_selected
import kotlinx.android.synthetic.main.bottom_sheet_lock_white.view.searchView
import kotlinx.android.synthetic.main.bottom_sheet_lock_white.view.tv_vip_flag_white
import kotlinx.android.synthetic.main.bottom_sheet_lock_white.view.white_edit_time_limit
import kotlinx.android.synthetic.main.item_bottom_sheet_edit_selected.view.tv_app_limit
import kotlinx.android.synthetic.main.widget_edit_time_limit.view.tv_edit_time_limit
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.launch
import kotlinx.coroutines.withContext
import java.util.Collections


private const val ARG_PARAM1 = "tomatoId"

class WhiteBottomSheetDialogFragment() : BaseBottomSheetDialogFragment() {

    private val TAG = "WhiteBottomSheet"
    private var param1: String = ""
    private lateinit var mBehavior: BottomSheetBehavior<View>
    private lateinit var customView: View
    private lateinit var recyclerview: RecyclerView
    private lateinit var recyclerview2: RecyclerView
    private var whiteList: MutableList<WhiteApp> = mutableListOf()
    private var appInfoList: MutableList<AppInfo> = mutableListOf()


    private val viewModel: LockViewModel by viewModels {
        InjectorUtils.provideLockViewModelFactory(requireContext())

    }

    companion object {
        @JvmStatic
        fun newInstance(param1: String) =
            WhiteBottomSheetDialogFragment().apply {
                arguments = Bundle().apply {
                    putString(ARG_PARAM1, param1)
                }
            }
    }

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        arguments?.let {
            param1 = it.getString(ARG_PARAM1)!!
        }
    }

    override fun onCreateView(inflater: LayoutInflater, container: ViewGroup?, savedInstanceState: Bundle?): View? {
        customView = View.inflate(requireContext(), R.layout.bottom_sheet_lock_white, null)
        return customView
    }


    override fun onStart() {
        super.onStart()
        val parentView = customView.parent as View
        parentView.setBackgroundColor(resources.getColor(R.color.colorTranslate))

        //设置父窗口为屏幕高度
        val layoutParams = parentView.layoutParams
        layoutParams.height = (requireActivity() as AppCompatActivity).findViewById<ViewGroup>(android.R.id.content).height - dpToPixel(
            MMKVUtils.getFloat(MyConstants.SP_KEY_STATUS_BAR_HEIGHT, 32f)
        ).toInt()

        //设置初始状态为填充满
        mBehavior = BottomSheetBehavior.from(parentView)
        mBehavior.state = BottomSheetBehavior.STATE_EXPANDED

    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)


//        val header = LayoutInflater.from(requireContext()).inflate(R.layout.header_bottom_sheet_global_white, null)
//        val foot = LayoutInflater.from(requireContext()).inflate(R.layout.footer_bottom_sheet_global_white, null)
        //全部app
        recyclerview = customView.rv_lock_global_white
        recyclerview.layoutManager = GridLayoutManager(requireContext(), 6)
        val allAppAdapter = AppInfoAdapter(R.layout.item_bottom_sheet_edit, mutableListOf())
        allAppAdapter.animationEnable = true
        recyclerview.adapter = allAppAdapter

        //已选中白名单
        recyclerview2 = customView.rv_lock_global_white_selected
        recyclerview2.layoutManager = GridLayoutManager(requireContext(), MyConstants.WHITE_APP_COLUMN)
//        recyclerview2.layoutManager = LinearLayoutManager(requireContext(), LinearLayoutManager.HORIZONTAL, false)
        val selectedAppAdapter = WhiteAppAdapter(this, R.layout.item_bottom_sheet_edit_selected, mutableListOf())
        selectedAppAdapter.animationEnable = true
        val itemTouchHelper = ItemTouchHelper(WhiteItemCallback(requireContext(), viewModel))
        itemTouchHelper.attachToRecyclerView(recyclerview2)
        recyclerview2.adapter = selectedAppAdapter

        (recyclerview.adapter as AppInfoAdapter).setOnItemClickListener { adapter, view, position ->

            if ((whiteList.size >= 6) && (!MyUtil.isVIP())) {
                DialogUtil.showVIPDialog(null, this, "VIP用户可创建6个以上白名单软件，开通后，享受无限白名单设置。", "6WhiteAppLimit")
            } else {
                var hasJoined = false
                val appInfo = (adapter.data[position] as AppInfo)
                if (mutableListOf("com.huawei.meetime", "com.huawei.hinote").contains(appInfo.pkg)) {
                    MyToastUtil.showWarning("由于系统限制,『${AppUtils.getAppName(appInfo.pkg)}』无法作为白名单应用，请勿添加")
                    return@setOnItemClickListener
                }
                if (appInfo.pkg == AppUtils.getAppPackageName()) {
                    MyToastUtil.showWarning("为防止异常，请勿添加『${AppUtils.getAppName()}』为白名单")
                    return@setOnItemClickListener
                }

                run whiteListLoop@{
                    whiteList.forEach {
                        if (it.pkg == appInfo.pkg && it.mainActivity == appInfo.mainActivity) {
                            MyToastUtil.showInfo(appInfo.appName + "无需重复添加")
                            hasJoined = true
                            return@whiteListLoop
                        }
                    }
                }

                if (!hasJoined) {
                    val newWhiteApp = WhiteApp(appInfo.pkg, appInfo.mainActivity, -1)
                    viewModel.createWhiteApp(newWhiteApp)
                    MyToastUtil.showInfo(appInfo.appName + "添加成功")
                }
            }
        }


        (recyclerview2.adapter as WhiteAppAdapter).setOnItemClickListener { adapter, view, position ->
            val whiteApp = adapter.data[position] as WhiteApp
            LockAppLimitDialog(this).run {
                setWhiteApp(whiteApp)
                setOnDeleteWhiteListener(object : LockAppLimitDialog.OnDeleteWhiteListener {
                    override fun onclick() {
                        viewModel.deleteGlobalWhiteApp(adapter.data[position] as WhiteApp)
                    }

                })
                setOnDeleteLimitListener(object : LockAppLimitDialog.OnDeleteLimitListener {
                    override fun onclick() {
                        whiteApp.maxLen = -1

                        view.tv_app_limit.visibility = View.GONE

                        viewModel.updateWhiteApp(whiteApp)
                    }
                })
                setOnOKListener(object : LockAppLimitDialog.OnOKListener {
                    override fun onclick(length: Int) {
                        whiteApp.maxLen = length

                        view.tv_app_limit.visibility = View.VISIBLE
                        view.tv_app_limit.text = secondToSimpleHm(length * 60)

                        viewModel.updateWhiteApp(whiteApp)
                    }
                })
                show()
            }

        }


        viewModel.getAllAppsInfo.observe(viewLifecycleOwner) {
            lifecycleScope.launch(Dispatchers.Default) {
                val diffResult = DiffUtil.calculateDiff(DiffCallBack1(appInfoList, it), true)
                withContext(Dispatchers.Main) {
                    allAppAdapter.setNewInstance(it)
                    appInfoList = it
                    diffResult.dispatchUpdatesTo(allAppAdapter)
                    customView.liv_white.hide()
                }
            }
        }

//        viewModel.setUserId(MMKVUtils.getInt(MyConstants.SP_KEY_USER_ID, -1))
        viewModel.globalWhiteAppsLiveData.observe(viewLifecycleOwner) {
            if (whiteList.size != it.size) (
                    lifecycleScope.launch(Dispatchers.Default) {
                        val diffResult = DiffUtil.calculateDiff(DiffCallBack2(whiteList, it), true)
                        withContext(Dispatchers.Main) {
                            selectedAppAdapter.setNewInstance(it)
                            whiteList = it
                            diffResult.dispatchUpdatesTo(selectedAppAdapter)
                        }
                    }
                    )
        }

        if (MyUtil.isVIP()) {
            customView.tv_vip_flag_white.visibility = View.GONE
        } else {
            customView.tv_vip_flag_white.visibility = View.VISIBLE
            customView.tv_vip_flag_white.setOnClickListener {
                val intent = Intent(requireContext(), VIP2Activity::class.java)
                intent.putExtra(FROM_WHERE, "6WhiteCard")
                startActivity(intent)
            }
        }

        customView.white_edit_time_limit.visibility = View.VISIBLE
        var limitTimeStart = MMKVUtils.getInt(MyConstants.SP_KEY_LIMIT_TIME_START_WHITE, -1)
        var limitTimeEnd = MMKVUtils.getInt(MyConstants.SP_KEY_LIMIT_TIME_END_WHITE, -1)
        customView.tv_edit_time_limit.text =
            if (limitTimeStart == -1 || limitTimeEnd == -1) "限制修改" else "${secondToSimpleHm(limitTimeStart)}-${secondToSimpleHm(limitTimeEnd)}可改"
        customView.tv_edit_time_limit.setOnClickListener {
//            if (!MyUtil.isCurrentfInTimeRange(limitTimeStart.toLong(), limitTimeEnd.toLong())) {
//                MyToastUtil.showWarning("您设置了仅允许在${secondToSimpleHm(limitTimeStart)}-${secondToSimpleHm(limitTimeEnd)}修改")
//                return@setOnClickListener
//            }
            LimitTimeEditDialog(this).apply {
                setTitle("在以下时间段可修改全局白名单")
                setLimitTime(limitTimeStart, limitTimeEnd)
                setOnOKClickListener(object : LimitTimeEditDialog.OnOKLimitTimeEditListener {
                    override fun onclick(start: Int, end: Int) {
                        limitTimeStart = start
                        limitTimeEnd = end
                        MMKVUtils.put(MyConstants.SP_KEY_LIMIT_TIME_START_WHITE, start)
                        MMKVUtils.put(MyConstants.SP_KEY_LIMIT_TIME_END_WHITE, end)
                        customView.tv_edit_time_limit.text = "${secondToSimpleHm(start)}-${secondToSimpleHm(end)}可改"
                    }

                })
                setOnCancelClickListener(object : LimitTimeEditDialog.OnCancelLimitTimeEditListener {
                    override fun onclick() {
                        MMKVUtils.put(MyConstants.SP_KEY_LIMIT_TIME_START_WHITE, -1)
                        MMKVUtils.put(MyConstants.SP_KEY_LIMIT_TIME_END_WHITE, -1)
                        customView.tv_edit_time_limit.text = "限制修改"
                    }
                })
                show()
            }
        }

        customView.searchView.maxWidth = Int.MAX_VALUE//宽度铺满屏幕

        customView.searchView.findViewById<View>(androidx.appcompat.R.id.search_plate)?.setBackgroundColor(Color.TRANSPARENT)//删除底部横线
        customView.searchView.findViewById<TextView>(androidx.appcompat.R.id.search_src_text)?.setTextSize(TypedValue.COMPLEX_UNIT_SP, 16f)
        customView.searchView.setOnQueryTextListener(object : SearchView.OnQueryTextListener {
            override fun onQueryTextSubmit(query: String?): Boolean {
                return true
            }

            override fun onQueryTextChange(newText: String?): Boolean {
                var result = ""
                if (newText != null) {
                    result = newText
                }
                viewModel.getAllAppsInfoWithText(result)
                return true
            }

        })
    }


    private class DiffCallBack1(val oldList: MutableList<AppInfo>, val newList: MutableList<AppInfo>) : DiffUtil.Callback() {

        override fun areItemsTheSame(oldItemPosition: Int, newItemPosition: Int): Boolean {
            return (oldList[oldItemPosition].pkg == newList[newItemPosition].pkg)
                    && (oldList[oldItemPosition].mainActivity == newList[newItemPosition].mainActivity)
        }

        override fun getOldListSize(): Int {
            return oldList.size
        }

        override fun getNewListSize(): Int {
            return newList.size
        }

        override fun areContentsTheSame(oldItemPosition: Int, newItemPosition: Int): Boolean {
            return (oldList[oldItemPosition].pkg == newList[newItemPosition].pkg)
                    && (oldList[oldItemPosition].mainActivity == newList[newItemPosition].mainActivity)
        }

    }

    private class DiffCallBack2(val oldList: MutableList<WhiteApp>, val newList: MutableList<WhiteApp>) : DiffUtil.Callback() {

        override fun areItemsTheSame(oldItemPosition: Int, newItemPosition: Int): Boolean {
            return (oldList[oldItemPosition].whiteAppIndexId == newList[newItemPosition].whiteAppIndexId)
        }

        override fun getOldListSize(): Int {
            return oldList.size
        }

        override fun getNewListSize(): Int {
            return newList.size
        }

        override fun areContentsTheSame(oldItemPosition: Int, newItemPosition: Int): Boolean {
            return (oldList[oldItemPosition].pkg == newList[newItemPosition].pkg)
                    && (oldList[oldItemPosition].mainActivity == newList[newItemPosition].mainActivity)
                    && (oldList[oldItemPosition].maxLen == newList[newItemPosition].maxLen)
        }

    }

    private class WhiteItemCallback(val context: Context, val viewModel: LockViewModel) : ItemTouchHelper.Callback() {
        override fun getMovementFlags(p0: RecyclerView, p1: RecyclerView.ViewHolder): Int {
            return makeMovementFlags(ItemTouchHelper.UP or ItemTouchHelper.DOWN or ItemTouchHelper.LEFT or ItemTouchHelper.RIGHT, 0)
        }

        override fun onMove(recycler: RecyclerView, holder1: RecyclerView.ViewHolder, holder2: RecyclerView.ViewHolder): Boolean {
//            Log.d("WhiteItemCallback start", holder1.toString() + "\n" + holder2.toString())
            val fromPosition: Int = holder1.getBindingAdapterPosition()
            val toPosition: Int = holder2.getBindingAdapterPosition()

            if (fromPosition < (recycler.adapter as WhiteAppAdapter).data.size &&
                toPosition < (recycler.adapter as WhiteAppAdapter).data.size
            ) {

                if (fromPosition < toPosition) {
                    for (i in fromPosition until toPosition) {
                        swapData(recycler, i, i + 1)
                    }
                } else {
                    for (i in fromPosition downTo toPosition + 1) {
                        swapData(recycler, i, i - 1)
                    }
                }


//                Log.d("WhiteItemCallback end", white1.toString() + "\n" + white2.toString())
            }
            return true
        }

        override fun onSwiped(recycler: RecyclerView.ViewHolder, p1: Int) {

        }

        override fun isLongPressDragEnabled(): Boolean {
            return true
        }

        fun swapData(recycler: RecyclerView, position1: Int, position2: Int) {
            Collections.swap((recycler.adapter as WhiteAppAdapter).data, position1, position2)
            (recycler.adapter as WhiteAppAdapter).notifyItemMoved(position1, position2)

            swapAppLimitTrendWithMinimalImpact((recycler.adapter as WhiteAppAdapter).data, position1, position2)

//            val white1 = (recycler.adapter as WhiteAppAdapter).data[position1]
//            val white2 = (recycler.adapter as WhiteAppAdapter).data[position2]
//            val temp = white1.trend
//            white1.trend = white2.trend
//            white2.trend = temp
//            viewModel.updateWhiteApp(white1, sync = false)
//            viewModel.updateWhiteApp(white2, sync = false)
        }

        fun swapAppLimitTrendWithMinimalImpact(dataList: MutableList<WhiteApp>, pos1: Int, pos2: Int) {
            if (pos1 !in dataList.indices || pos2 !in dataList.indices) return

            val item1 = dataList[pos1]
            val item2 = dataList[pos2]

            //这里修改pos1的trend，因为拖动的是pos2，这样如果trend都是0，拖一圈就能全改了。
            if (item1.trend == item2.trend) {
                val newTrendForItem1 = generateNearestUniqueTrend(dataList, item1.trend)
                item1.trend = newTrendForItem1
            } else {
                // 如果trend不相等，交换trend值
                item1.trend = item2.trend.also { item2.trend = item1.trend }
            }
            viewModel.updateWhiteApp(item1, sync = false)
            viewModel.updateWhiteApp(item2, sync = false)

        }

        private fun generateNearestUniqueTrend(appLimits: List<WhiteApp>, baseTrend: Int): Int {
            var newTrend = baseTrend + 1
            while (appLimits.any { it.trend == newTrend }) {
                newTrend += 1
            }
            return newTrend
        }

//        override fun onSelectedChanged(viewHolder: RecyclerView.ViewHolder?, actionState: Int) {
//            super.onSelectedChanged(viewHolder, actionState)
//            if (actionState == ItemTouchHelper.ACTION_STATE_IDLE) {
//                // 拖动结束
//            }
//            LogUtils.d("onSelectedChanged")
//        }
//
//        override fun clearView(recyclerView: RecyclerView, viewHolder: RecyclerView.ViewHolder) {
//            super.clearView(recyclerView, viewHolder)
//            LogUtils.d("clearView")
//        }
    }


    override fun onStop() {
        super.onStop()

        LiveEventBus.get(LiveBus.START_SYNC_WHITE_APP, String::class.java).post("")
    }

}