package com.lijianqiang12.silent.component.activity.me

import android.content.Intent
import android.net.Uri
import android.os.Bundle
import com.blankj.utilcode.util.AppUtils
import com.lijianqiang12.silent.R
import com.lijianqiang12.silent.component.activity.TheWebViewActivity
import com.lijianqiang12.silent.component.activity.base.BaseActivity
import com.lijianqiang12.silent.utils.checkUpdate
import kotlinx.android.synthetic.main.activity_about.btn_check_update
import kotlinx.android.synthetic.main.activity_about.cl_about_agreement
import kotlinx.android.synthetic.main.activity_about.cl_about_privacy
import kotlinx.android.synthetic.main.activity_about.cl_icp
import kotlinx.android.synthetic.main.activity_about.iv_return_about
import kotlinx.android.synthetic.main.activity_about.tv_about_version
import kotlinx.android.synthetic.main.activity_about.tv_copyright
import java.util.Calendar


class AboutActivity : BaseActivity() {
    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        setContentView(R.layout.activity_about)
        tv_about_version.text = AppUtils.getAppVersionName()
        iv_return_about.setOnClickListener { finish() }



        btn_check_update.setOnClickListener {
            checkUpdate(this, true)
        }
        cl_about_privacy.setOnClickListener {
            val intent = Intent(this, TheWebViewActivity::class.java)
            intent.putExtra("title", "")
            intent.putExtra("url", "https://help-offphone.shuge888.com/protocal/privacy")
            this.startActivity(intent)
        }
        cl_about_agreement.setOnClickListener {
            val intent = Intent(this, TheWebViewActivity::class.java)
            intent.putExtra("title", "")
            intent.putExtra("url", "https://help-offphone.shuge888.com/protocal/agreement")
            this.startActivity(intent)
        }

        cl_icp.setOnClickListener {
            val uri = Uri.parse("https://beian.miit.gov.cn")
            val intent = Intent(Intent.ACTION_VIEW, uri)
            startActivity(intent)
//            val intent = Intent(this, TheWebViewActivity::class.java)
//            intent.putExtra("url", "https://beian.miit.gov.cn")
//            this.startActivity(intent)
        }

        tv_copyright.text = "Copyright © 2018-${Calendar.getInstance().get(Calendar.YEAR)} 薯格科技"
    }
}