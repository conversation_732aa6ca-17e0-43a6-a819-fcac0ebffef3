package com.lijianqiang12.silent.component.activity.custom.dialog

import android.graphics.Color
import android.graphics.drawable.ColorDrawable
import android.os.Bundle
import android.util.DisplayMetrics
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.view.WindowManager
import androidx.appcompat.app.AppCompatActivity
import androidx.fragment.app.Fragment
import com.lijianqiang12.silent.DIALOG_WIDTH_PERCENT_MIDDLE
import com.lijianqiang12.silent.R
import com.lijianqiang12.silent.component.activity.base.BaseDialogFragment
import kotlinx.android.synthetic.main.dialog_lock_app_limit_time.view.*

class TimeSelectDialog() : BaseDialogFragment() {

    constructor(fragment: Fragment) : this() {
        this.fragment = fragment
    }

    constructor(activity: AppCompatActivity) : this() {
        this.myActivity = activity
    }

    private var okListener: OnOKListener? = null
    private lateinit var v: View
    private var myActivity: AppCompatActivity? = null
    private var fragment: Fragment? = null
    private var timeLimitHour = 23
    private var timeLimitMinute = 59
    private var title = "请选择时间"

    fun setHour(hour: Int) {
        timeLimitHour = hour
    }

    fun setMinute(minute: Int) {
        timeLimitMinute = minute
    }

    fun setTitle(title: String) {
        this.title = title
    }

    override fun onCreateView(inflater: LayoutInflater, container: ViewGroup?, savedInstanceState: Bundle?): View? {
        dialog!!.window!!.setBackgroundDrawable(ColorDrawable(Color.TRANSPARENT))

        v = inflater.inflate(R.layout.dialog_select_time, container, false)
        v.tv_dialog_home_app_title.text = title
        val dataHour: MutableList<String> = mutableListOf()
        for (i in 0..23) {
            if (i < 10) {
                dataHour.add("0$i")
            } else {
                dataHour.add("$i")
            }
        }

        val dataMinute: MutableList<String> = mutableListOf()
        for (i in 0..59) {
            if (i < 10) {
                dataMinute.add("0$i")
            } else {
                dataMinute.add("$i")
            }
        }

        v.tpv_lock_fast_hour.setData(dataHour)
        v.tpv_lock_fast_minute.setData(dataMinute)

        v.tpv_lock_fast_hour.setSelected(timeLimitHour)
        v.tpv_lock_fast_minute.setSelected(timeLimitMinute)

        v.tpv_lock_fast_hour.setOnSelectListener { text -> timeLimitHour = text!!.toInt() }

        v.tpv_lock_fast_minute.setOnSelectListener { text -> timeLimitMinute = text!!.toInt() }


        v.btn_ok.setOnClickListener {
            okListener?.onclick(timeLimitHour * 60 + timeLimitMinute)
            dismiss()
        }

        return v
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)

    }


    fun show() {
        fragment?.let {
            super.show(fragment!!.requireFragmentManager(), "TimeSelectDialog")
        }
        this.myActivity?.let {
            super.show(it.supportFragmentManager, "TimeSelectDialog")
        }
    }

    override fun onStart() {
        val params = dialog!!.window!!.attributes
        val dm: DisplayMetrics = resources.displayMetrics
        val width = dm.widthPixels
        params.width = (width * DIALOG_WIDTH_PERCENT_MIDDLE).toInt()//ViewGroup.LayoutParams.MATCH_PARENT
        dialog!!.window!!.attributes = params as WindowManager.LayoutParams
        super.onStart()
    }


    fun setOnOKListener(listener: OnOKListener) {
        this.okListener = listener
    }


    interface OnOKListener {
        fun onclick(length: Int)
    }


}