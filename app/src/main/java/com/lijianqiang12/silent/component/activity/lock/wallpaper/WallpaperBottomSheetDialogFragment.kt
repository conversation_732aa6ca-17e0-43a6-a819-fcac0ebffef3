package com.lijianqiang12.silent.component.activity.lock.wallpaper

import android.app.Activity
import android.content.Intent
import android.net.Uri
import android.os.Bundle
import android.provider.MediaStore
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.Toast
import androidx.appcompat.app.AppCompatActivity
import androidx.lifecycle.lifecycleScope
import com.lijianqiang12.silent.utils.MMKVUtils
import com.blankj.utilcode.util.ScreenUtils
import com.lijianqiang12.silent.utils.MyToastUtil
import com.google.android.material.bottomsheet.BottomSheetBehavior
import com.google.android.material.tabs.TabLayoutMediator
import com.lijianqiang12.silent.MyConstants
import com.lijianqiang12.silent.R
import com.lijianqiang12.silent.component.activity.REQUEST_CODE_SELECT_SYSTEM_WALLPAPER
import com.lijianqiang12.silent.component.activity.REQUEST_CODE_SET_WALLPAPER
import com.lijianqiang12.silent.component.activity.base.BaseBottomSheetDialogFragment
import com.lijianqiang12.silent.component.activity.custom.dialog.MyProgressDialog
import com.lijianqiang12.silent.utils.*
import com.yalantis.ucrop.UCrop
import kotlinx.android.synthetic.main.bottom_sheet_choose_wallpaper.view.*
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.launch
import kotlinx.coroutines.withContext
import java.io.File
import kotlin.math.max
import kotlin.math.min

const val requestPicCode = 10002
const val SAMPLE_CROPPED_IMAGE_NAME = "wallpaper.png"

class WallpaperBottomSheetDialogFragment() : BaseBottomSheetDialogFragment() {

    private lateinit var mBehavior: BottomSheetBehavior<View>
    private lateinit var customView: View
    private lateinit var dialog: MyProgressDialog

    companion object {
        @JvmStatic
        fun newInstance() = WallpaperBottomSheetDialogFragment()

    }

    override fun onCreateView(inflater: LayoutInflater, container: ViewGroup?, savedInstanceState: Bundle?): View? {
        customView = View.inflate(requireContext(), R.layout.bottom_sheet_choose_wallpaper, null)
        return customView
    }

    override fun onStart() {
        super.onStart()
        val parentView = customView.parent as View
        parentView.setBackgroundColor(resources.getColor(R.color.colorTranslate))

        //设置父窗口为屏幕高度
        val layoutParams = parentView.layoutParams
        layoutParams.height = (requireActivity() as AppCompatActivity).findViewById<ViewGroup>(android.R.id.content).height - dpToPixel(MMKVUtils.getFloat(MyConstants.SP_KEY_STATUS_BAR_HEIGHT, 32f)).toInt()

        //设置初始状态为填充满
        mBehavior = BottomSheetBehavior.from(parentView)
        mBehavior.state = BottomSheetBehavior.STATE_EXPANDED
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        dialog = MyProgressDialog(fragment = this)
        customView.vp_choose_wallpaper.adapter = ChooseWallpaperViewPagerAdapter(requireActivity())
        TabLayoutMediator(customView.tab_choose_wallpaper, customView.vp_choose_wallpaper) { tab, position ->
            tab.text = when (position) {
                0 -> "当前"
                1 -> "网络"
                else -> "Error"
            }

        }.attach()
        customView.iv_close_chooser.setOnClickListener {
            dismiss()
        }
        customView.btn_local_pic.setOnClickListener {
//            val intent = Intent(Intent.ACTION_GET_CONTENT)
//                    .setType("image/*")
//                    .addCategory(Intent.CATEGORY_OPENABLE)
//            val mimeTypes = arrayOf("image/jpeg", "image/png")
//            intent.putExtra(Intent.EXTRA_MIME_TYPES, mimeTypes)
//
//            startActivityForResult(Intent.createChooser(intent, "选择图片"), requestPicCode)
            val intent = Intent(Intent.ACTION_PICK, MediaStore.Images.Media.EXTERNAL_CONTENT_URI)
            startActivityForResult(intent, requestPicCode)
        }

    }


    override fun onActivityResult(requestCode: Int, resultCode: Int, data: Intent?) {
        super.onActivityResult(requestCode, resultCode, data)
        if (resultCode == Activity.RESULT_OK) {
            if (requestCode == requestPicCode) {
                val selectedUri = data!!.data
                if (selectedUri != null) {
                    startCrop(selectedUri)
                } else {
                    Toast.makeText(requireContext(), "未获取到图片，请确认已授予读写存储权限", Toast.LENGTH_LONG).show()
                }
            } else if (requestCode == UCrop.REQUEST_CROP) {
                val resultUri = UCrop.getOutput(data!!)
                if (resultUri != null) {
                    dialog.show()
                    lifecycleScope.launch(Dispatchers.IO) {
//                        saveImage(requireActivity(), resultUri)
                        MMKVUtils.put(MyConstants.SP_KEY_SETTING_WALLPAPER_PATH, UriUtil.getRealFilePath(requireContext(), resultUri))
                        withContext(Dispatchers.Main) {
                            if (wallpaperIsUsed(requireContext().applicationContext)) {
                                MyToastUtil.showInfo("设置成功")
                            } else {
                                setLiveWallpaper(requireContext().applicationContext, requireActivity(), REQUEST_CODE_SET_WALLPAPER)
                            }
                            dialog.dismiss()
                        }
                    }
                } else {
                    Toast.makeText(requireContext(), "无法获取裁剪后图片", Toast.LENGTH_LONG).show()
                }
            } else if (requestCode == REQUEST_CODE_SET_WALLPAPER) {
                if (resultCode == Activity.RESULT_OK) {
                    Toast.makeText(requireContext(), "设置动态壁纸成功", Toast.LENGTH_SHORT).show()
                } else {
                    Toast.makeText(requireContext(), "取消设置动态壁纸", Toast.LENGTH_SHORT).show()
                }
            } else if (requestCode == REQUEST_CODE_SELECT_SYSTEM_WALLPAPER) {
                if (resultCode == Activity.RESULT_OK) {
                    Toast.makeText(requireContext(), "设置系统壁纸成功", Toast.LENGTH_SHORT).show()
                } else {
                    Toast.makeText(requireContext(), "取消设置系统壁纸", Toast.LENGTH_SHORT).show()
                }
            }
        } else if (resultCode == UCrop.RESULT_ERROR) {
            val cropError = UCrop.getError(data!!)
            if (cropError != null) {
                Toast.makeText(requireContext(), cropError.message, Toast.LENGTH_LONG).show()
            } else {
                Toast.makeText(requireContext(), "未知错误", Toast.LENGTH_SHORT).show()
            }
        }
    }

    fun startCrop(uri: Uri) {
        val destinationFileName = "wallpaper" + System.currentTimeMillis() + ".png"//SAMPLE_CROPPED_IMAGE_NAME
        val width = ScreenUtils.getScreenWidth()
        val height = ScreenUtils.getScreenHeight()

        val options = UCrop.Options()

        options.setHideBottomControls(true)
        options.setToolbarColor(resources.getColor(R.color.colorWhiteBackground))
        options.setStatusBarColor(resources.getColor(R.color.colorWhiteBackground))
//        options.setCompressionQuality(100)
        options.withAspectRatio(width.toFloat(), height.toFloat())
        options.withMaxResultSize(width, height)
        options.setToolbarWidgetColor(resources.getColor(R.color.custom_color_app_text_1_default))
        options.setCircleDimmedLayer(false)
        options.setToolbarCancelDrawable(R.drawable.ic_return)
        options.setShowCropFrame(true)
        options.setShowCropGrid(true)
//        options.

        val uCrop = UCrop.of(uri, Uri.fromFile(File(requireContext().cacheDir, destinationFileName)))
                .withOptions(options)

//        uCrop.start(requireContext() as AppCompatActivity)
        uCrop.start(requireContext(), this)
    }


}