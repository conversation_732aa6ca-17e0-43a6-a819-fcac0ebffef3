package com.lijianqiang12.silent.component.activity.me.qa

import android.content.Intent
import android.net.Uri
import android.os.Build
import android.os.Bundle
import com.blankj.utilcode.util.AppUtils
import com.lijianqiang12.silent.utils.MMKVUtils
import com.lijianqiang12.silent.MyConstants
import com.lijianqiang12.silent.R
import com.lijianqiang12.silent.component.activity.TheWebViewActivity
import com.lijianqiang12.silent.component.activity.base.BaseActivity
import com.lijianqiang12.silent.utils.MyToastUtil
import com.lijianqiang12.silent.utils.MyUtil
import kotlinx.android.synthetic.main.activity_qa.*

class QaActivity : BaseActivity() {
    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        setContentView(R.layout.activity_qa)

        iv_edit_qa_return.setOnClickListener { finish() }

//        btn_developer_mode.setOnClickListener {
//            if (MyAccessibilityService.isAccessibilityActive()) {
//                NormalDialog(this).apply {
//                    setTitle("调试模式")
//                    setContent("调试模式会临时记录当前一段时间的手机操作日志，直到关闭该模式。为保护您的隐私，此信息不会上传到云端，需要您把主动把信息提供给客服，是否开启？")
//                    setOnNormalOKClickListener("开启", object : OnOKClickListener {
//                        override fun onclick() {
//                            MyToastUtil.showSuccess("已开启调试模式")
//                        }
//                    })
//                    setOnNormalCancelClickListener("取消", object : OnCancelClickListener {
//                        override fun onclick() {
//                        }
//                    })
//                    showDialog()
//                }
//            } else {
//                NormalDialog(this).apply {
//                    setTitle("缺少权限")
//                    setContent("调试模式需要开启无障碍服务，是否前往开启？")
//                    setOnNormalOKClickListener("前往", object : OnOKClickListener {
//                        override fun onclick() {
//                            PermissionUtil.openAccessibility(this@QaActivity)
//                        }
//                    })
//                    setOnNormalCancelClickListener("取消", object : OnCancelClickListener {
//                        override fun onclick() {
//                        }
//                    })
//                    showDialog()
//
//                }
//            }
//        }

        cl_email.setOnClickListener {
            try {
                MyToastUtil.showSuccess("正在打开邮箱程序并发送给********************")
                val data = Intent(Intent.ACTION_SENDTO)
                data.data = Uri.parse("mailto:<EMAIL>")
                data.putExtra(
                    Intent.EXTRA_SUBJECT,
                    "『" + AppUtils.getAppName() + "』 " + "ID:" + MMKVUtils.getInt(
                        MyConstants.SP_KEY_USER_ID,
                        -1
                    ) + " " + Build.BRAND + " " + Build.MODEL + " Android" + Build.VERSION.RELEASE + " AppVersion" + AppUtils
                        .getAppVersionName() + " state" + MMKVUtils.getInt(MyConstants.SP_KEY_VIP_STATE, -1)
                )
                data.putExtra(
                    Intent.EXTRA_TEXT,
                    "1.您是从哪里了解到『" + AppUtils.getAppName() + "』并下载的？\n\n\n\n2.您的问题或建议（最好有截图或录屏，以便我们定位问题）？\n"
                )
                startActivity(data)
            } catch (e: Exception) {
                e.printStackTrace()
                MyToastUtil.showError("请先安装邮箱程序")
            }
        }

        cl_other.setOnClickListener {
            MyUtil.openWechatAssistant()
        }

        cl_qa_title.setOnClickListener {
            val intent = Intent(this, TheWebViewActivity::class.java)
            intent.putExtra("title", "常见问题")
            intent.putExtra("url", "https://help-offphone.shuge888.com/problem")
            this.startActivity(intent)
        }
    }
}