package com.lijianqiang12.silent.component.activity.lock


import AliMoneyFragment
import Lock<PERSON>hiteAppAdapter
import android.animation.AnimatorSet
import android.animation.ObjectAnimator
import android.appwidget.AppWidgetManager
import android.content.*
import android.content.pm.PackageManager
import android.content.pm.ResolveInfo
import android.net.Uri
import android.os.Bundle
import android.provider.MediaStore
import android.util.Log
import android.view.Gravity
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.view.animation.AccelerateDecelerateInterpolator
import androidx.fragment.app.viewModels
import androidx.lifecycle.lifecycleScope
import androidx.recyclerview.widget.DiffUtil
import androidx.recyclerview.widget.GridLayoutManager
import androidx.recyclerview.widget.RecyclerView
import co.mobiwise.materialintro.shape.Focus
import co.mobiwise.materialintro.shape.FocusGravity
import co.mobiwise.materialintro.shape.ShapeType
import co.mobiwise.materialintro.view.MaterialIntroView
import com.blankj.utilcode.util.ActivityUtils
import com.blankj.utilcode.util.AppUtils
import com.blankj.utilcode.util.LogUtils
import com.bumptech.glide.Glide
import com.bumptech.glide.load.engine.DiskCacheStrategy
import com.bumptech.glide.load.resource.drawable.DrawableTransitionOptions
import com.jeremyliao.liveeventbus.LiveEventBus
import com.lijianqiang12.silent.LiveBus
import com.lijianqiang12.silent.MyConstants
import com.lijianqiang12.silent.R
import com.lijianqiang12.silent.TheApplication
import com.lijianqiang12.silent.VIP_STATE_FOREVER
import com.lijianqiang12.silent.VIP_STATE_FREE
import com.lijianqiang12.silent.component.activity.PermissionActivity
import com.lijianqiang12.silent.component.activity.TheWebViewActivity
import com.lijianqiang12.silent.component.activity.base.BaseActivity
import com.lijianqiang12.silent.component.activity.base.BaseFragment
import com.lijianqiang12.silent.component.activity.custom.dialog.*
import com.lijianqiang12.silent.component.activity.lock.fast.FastBottomSheetDialogFragment
import com.lijianqiang12.silent.component.activity.lock.punch.PunchCardActivity
import com.lijianqiang12.silent.component.activity.lock.schedule.ScheduleBottomSheetDialogFragment
import com.lijianqiang12.silent.component.activity.lock.setting.LockSettingActivity
import com.lijianqiang12.silent.component.activity.lock.tomato.TomatoBottomSheetDialogFragment
import com.lijianqiang12.silent.component.activity.lock.whiteapp.WhiteBottomSheetDialogFragment
import com.lijianqiang12.silent.component.activity.me.vip.FROM_WHERE
import com.lijianqiang12.silent.component.activity.me.vip.VIP2Activity
import com.lijianqiang12.silent.component.activity.widget.DenyUninstallAppWidget
import com.lijianqiang12.silent.data.model.db.AppDatabase
import com.lijianqiang12.silent.data.model.db.LockHistory
import com.lijianqiang12.silent.data.model.db.WhiteApp
import com.lijianqiang12.silent.data.model.net.MyRetrofitClient
import com.lijianqiang12.silent.data.viewmodel.InjectorUtils
import com.lijianqiang12.silent.data.viewmodel.LockViewModel
import com.lijianqiang12.silent.utils.*
import kotlinx.android.synthetic.main.fragment_lock.rv_lock_white
import kotlinx.android.synthetic.main.fragment_lock.view.*
import kotlinx.coroutines.*
import java.util.*


private const val ARG_PARAM1 = "param1"
private const val ARG_PARAM2 = "param2"

class LockFragment : BaseFragment() {
    private val TAG = "LockFragment"
    private var param1: String? = null
    private var param2: String? = null
    private lateinit var v: View
//    private var globalWhiteList: MutableList<WhiteApp>? = null

    private var work: Job? = null

    //    private var allAppInfoList: MutableList<AppInfo>? = null
    private var isStar = false
    private var star = 0
    private var share = 0
    private var wordId = -1

    private val animatorSetSuofang = AnimatorSet() //组合动画
    private var oldWhiteList: MutableList<WhiteApp> = mutableListOf()

    private lateinit var recyclerview: RecyclerView

    private val viewModel: LockViewModel by viewModels {
        InjectorUtils.provideLockViewModelFactory(requireContext())
    }


    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        arguments?.let {
            param1 = it.getString(ARG_PARAM1)
            param2 = it.getString(ARG_PARAM2)
        }

        LiveEventBus.get(LiveBus.LOGIN, Boolean::class.java).observe(this) {
            viewModel.setUserId(MMKVUtils.getInt(MyConstants.SP_KEY_USER_ID, -1))
        }
    }

    override fun onCreateView(inflater: LayoutInflater, container: ViewGroup?, savedInstanceState: Bundle?): View {
        v = inflater.inflate(R.layout.fragment_lock, container, false)
        return v
    }

    override fun lazyInit() {
        val scaleX: ObjectAnimator = ObjectAnimator.ofFloat(v.iv_lock_share, "scaleX", 1f, 1.4f, 1f) //后几个参数是放大的倍数
        val scaleY: ObjectAnimator = ObjectAnimator.ofFloat(v.iv_lock_share, "scaleY", 1f, 1.4f, 1f)
        scaleX.repeatCount = 2//ValueAnimator.INFINITE //永久循环
        scaleY.repeatCount = 2//ValueAnimator.INFINITE
        animatorSetSuofang.duration = 2000 //时间
        animatorSetSuofang.interpolator = AccelerateDecelerateInterpolator()
        animatorSetSuofang.play(scaleX).with(scaleY) //两个动画同时开始

//        animatorSetsuofang.start() //开始


        if (!MMKVUtils.contains(MyConstants.SP_KEY_NOTIFY_STAR)) {
            MMKVUtils.put(MyConstants.SP_KEY_NOTIFY_STAR, System.currentTimeMillis() + 2 * 24 * 60 * 60 * 1000L)
        }

        if (!MMKVUtils.contains(MyConstants.SP_KEY_NOTIFY_SHARE)) {
            MMKVUtils.put(MyConstants.SP_KEY_NOTIFY_SHARE, System.currentTimeMillis() + 1 * 24 * 60 * 60 * 1000L)
        }

//        val wallpaperPath = MMKVUtils.getString(Constants.SP_KEY_SETTING_WALLPAPER_PATH, "")
//        Glide.with(this).load(wallpaperPath).into(v.imageView8)

//        v.iv_kil_notify.setOnClickListener {
//            v.cl_kil_notify.visibility = View.GONE
//            MMKVUtils.put(MyConstants.SP_KEY_SHOW_KILL_NOTIFY, false)
//        }
//        v.cl_kil_notify.setOnClickListener {
//            MMKVUtils.put(MyConstants.SP_KEY_SHOW_KILL_NOTIFY, false)
//            startActivity(Intent(requireContext(), PermissionActivity::class.java))
//        }


        v.iv_punch_card.setOnClickListener {
            MyUtil.checkLoginAndDo(requireActivity()) {
                startActivity(Intent(requireContext(), PunchCardActivity::class.java))
            }
        }

        v.tv_lock_punch.setOnClickListener {
            toastTodo()
        }

        v.iv_lock_like.setOnClickListener {
            MyUtil.checkLoginAndDo(requireActivity()) {
                lifecycleScope.launch(Dispatchers.IO) {
                    try {
                        val result = viewModel.lockRepository.wellKnowWordStar(wordId)
                        withContext(Dispatchers.Main) {
                            if (result.code == 200) {
                                if (isStar) {
                                    v.tv_lock_like_count.text = "${star - 1}"
                                    v.iv_lock_like.setColorFilter(resources.getColor(R.color.colorWhiteBackground))
                                    MyToastUtil.showInfo("已取消点赞")
                                } else {
                                    v.tv_lock_like_count.text = "${star + 1}"
                                    v.iv_lock_like.setColorFilter(resources.getColor(R.color.colorRed))
                                    MyToastUtil.showInfo("已点赞")
                                }
                                refreshWellKnowWord()
                            } else {
                                MyToastUtil.showError(result.msg)
                            }
                        }
                    } catch (e: Exception) {
                        MyToastUtil.showInfo(e.message)
                    }
                }
            }
        }

        v.iv_lock_share.setOnClickListener {

            lifecycleScope.launch(Dispatchers.IO) {
                try {
                    val result = viewModel.lockRepository.wellKnowWordShare(wordId)
                    withContext(Dispatchers.Main) {
                        if (result.code == 200) {
                            v.tv_lock_share_count.text = "${share + 1}"
                            refreshWellKnowWord()
                        } else {
                            MyToastUtil.showInfo(result.msg)
                        }
                    }
                } catch (e: Exception) {
                    MyToastUtil.showInfo(e.message)
                }
            }
//            ActivityUtils.startActivity(ShareWordActivity::class.java)
            startActivity(Intent(requireActivity(), ShareWordActivity::class.java))
        }

        v.tv_check_permission.setOnClickListener {
            if (checkRunState()) {
                val limitTimeStart = MMKVUtils.getInt(MyConstants.SP_KEY_LIMIT_TIME_START_PERMISSION, -1)
                val limitTimeEnd = MMKVUtils.getInt(MyConstants.SP_KEY_LIMIT_TIME_END_PERMISSION, -1)
                if (!MyUtil.isCurrentInTimeRange(limitTimeStart.toLong(), limitTimeEnd.toLong())) {
                    MyToastUtil.showWarning("您设置了仅允许在${secondToSimpleHm(limitTimeStart)}-${secondToSimpleHm(limitTimeEnd)}修改")
                    return@setOnClickListener
                }
//                ActivityUtils.startActivity(PermissionActivity::class.java)
                startActivity(Intent(requireActivity(), PermissionActivity::class.java))
            }
        }

        v.cl_lock_fast.setOnClickListener {
            if (checkRunState()) {
                when (PermissionUtil.hasAllPermission2(requireActivity(), true)) {
                    0, 2 -> {
                        val fastBottomSheetFragment = FastBottomSheetDialogFragment.newInstance()//不能复用，否则不会重新加载viewmodal，而viewmodal使用一次机会被调用onclear
                        fastBottomSheetFragment.show(requireFragmentManager(), "fastBottomSheetFragment")
                    }

                    1 -> {
                        startActivity(Intent(requireActivity(), PermissionActivity::class.java))

                    }

                }


            }
        }

        v.cl_lock_tomato.setOnClickListener {
            if (checkRunState()) {
                when (PermissionUtil.hasAllPermission2(requireActivity(), true)) {
                    0, 2 -> {
                        val tomatoBottomSheetFragment = TomatoBottomSheetDialogFragment.newInstance()
                        tomatoBottomSheetFragment.show(requireFragmentManager(), "tomatoBottomSheetFragment")
                    }

                    1 -> {
                        startActivity(Intent(requireActivity(), PermissionActivity::class.java))

                    }

                }

            }
        }

        v.cl_lock_schedule.setOnClickListener {
            if (checkRunState()) {
                when (PermissionUtil.hasAllPermission2(requireActivity(), true)) {
                    0, 2 -> {
                        val scheduleBottomSheetFragment = ScheduleBottomSheetDialogFragment.newInstance()
                        scheduleBottomSheetFragment.show(requireFragmentManager(), "scheduleBottomSheetFragment")
                    }

                    1 -> {
                        startActivity(Intent(requireActivity(), PermissionActivity::class.java))

                    }
                }
            }
        }

        v.cl_lock_white_click.setOnClickListener {
            if (checkRunState()) {
                val limitTimeStart = MMKVUtils.getInt(MyConstants.SP_KEY_LIMIT_TIME_START_WHITE, -1)
                val limitTimeEnd = MMKVUtils.getInt(MyConstants.SP_KEY_LIMIT_TIME_END_WHITE, -1)
                if (!MyUtil.isCurrentInTimeRange(limitTimeStart.toLong(), limitTimeEnd.toLong())) {
                    MyToastUtil.showWarning("您设置了仅允许在${secondToSimpleHm(limitTimeStart)}-${secondToSimpleHm(limitTimeEnd)}修改")
                    return@setOnClickListener
                }
                val whiteBottomSheetFragment = WhiteBottomSheetDialogFragment.newInstance("")
                whiteBottomSheetFragment.show(requireFragmentManager(), "whiteBottomSheetFragment")
            }
        }

        recyclerview = rv_lock_white
        recyclerview.layoutManager = GridLayoutManager(requireContext(), MyConstants.WHITE_APP_COLUMN)
        val whiteAppAdapter = LockWhiteAppAdapter(this, R.layout.item_gridlayout_selected, mutableListOf())
        whiteAppAdapter.animationEnable = true
        recyclerview.adapter = whiteAppAdapter

//        viewModel.setUserId(MMKVUtils.getInt(MyConstants.SP_KEY_USER_ID, -1))
        var dataVersion = 0
        viewModel.globalWhiteAppsLiveData.observe(viewLifecycleOwner) {
            LogUtils.d("new globalWhiteAppsLiveData ${it.size}")
            if (it.isEmpty()) {
                v.tv_white_empty_notice.visibility = View.VISIBLE
            } else {
                v.tv_white_empty_notice.visibility = View.GONE
            }

            lifecycleScope.launch(Dispatchers.Default) {
                //防抖，只更新最后一个，否则每次排序完都要闪半天
                dataVersion += 1
                val thisDataVersion = dataVersion
                delay(300)
                if (thisDataVersion == dataVersion) {
                    val diffResult = DiffUtil.calculateDiff(DiffCallBack(oldWhiteList, it), true)
                    oldWhiteList = it
                    withContext(Dispatchers.Main) {
                        whiteAppAdapter.setNewInstance(it)
                        diffResult.dispatchUpdatesTo(whiteAppAdapter)
                    }
                }
            }
        }

//        viewModel.globalWhiteAppsLiveData.observe(viewLifecycleOwner) {
//
//            if (it.size == 0) {
//                v.tv_white_empty_notice.visibility = View.VISIBLE
//            } else {
//                v.tv_white_empty_notice.visibility = View.GONE
//            }
//            globalWhiteList = it
//            val gridLayout = v.gl_lock_white
//            gridLayout.removeAllViews()
//
//            val isVIP = MyUtil.isVIP()
//            it.forEachIndexed { index, whiteApp ->
//                val itemView = LayoutInflater.from(requireContext()).inflate(R.layout.item_gridlayout_selected, null)
//                if (!isVIP && index >= 6) {
//                    itemView.alpha = 0.3f
//                }
//                lifecycleScope.launch(Dispatchers.IO) {
//                    val appIcon = getAppIcon(whiteApp.pkg, whiteApp.mainActivity)
//                    withContext(Dispatchers.Main) {
//                        itemView.iv_app_icon.setImageDrawable(appIcon)
//                    }
//                }
//
//
//                val rowSpec: GridLayout.Spec = GridLayout.spec(index / MyConstants.WHITE_APP_COLUMN, 1.0f)
//                val columnSpec: GridLayout.Spec = GridLayout.spec(index % MyConstants.WHITE_APP_COLUMN, 1.0f)
//                val params: GridLayout.LayoutParams = GridLayout.LayoutParams(rowSpec, columnSpec)
//                params.width = 0
//
//                gridLayout.addView(itemView, params)
//            }
//
//            for (i in 0..(6 - it.size)) {
//                val itemView = LayoutInflater.from(requireContext()).inflate(R.layout.item_gridlayout_selected, null)
//                val rowSpec: GridLayout.Spec = GridLayout.spec((i + it.size) / MyConstants.WHITE_APP_COLUMN, 1.0f)
//                val columnSpec: GridLayout.Spec = GridLayout.spec((i + it.size) % MyConstants.WHITE_APP_COLUMN, 1.0f)
//                val params: GridLayout.LayoutParams = GridLayout.LayoutParams(rowSpec, columnSpec)
//                params.width = 0
//
//                gridLayout.addView(itemView, params)
//            }
//
//        }


        v.tv_lock_settings.setOnClickListener {
            if (checkRunState()) {
                startActivity(Intent(requireContext(), LockSettingActivity::class.java))
            }
        }

        v.cl_vip_lock.setOnClickListener {
            val intent = Intent(requireContext(), VIP2Activity::class.java)
            intent.putExtra(FROM_WHERE, "LockFragmentCard")
            startActivity(intent)
            MMKVUtils.put(MyConstants.SP_KEY_CLICK_VIP_LOCK, true)
        }

        v.chip_pause_info.setOnClickListener {
            NormalDialog(this).apply {
                setTitle("跳过暂停")
                setContent("是否跳过本次暂停，继续未完成的锁机？")
                setOnNormalOKClickListener("跳过", object : OnOKClickListener {
                    override fun onclick() {
                        MMKVUtils.put(MyConstants.SP_KEY_FORCE_UNLOCK_PAUSE, 0L)
                    }
                })
                setOnNormalCancelClickListener("取消", object : OnCancelClickListener {
                    override fun onclick() {
                    }
                })
                showDialog()
            }
        }
    }


    companion object {
        @JvmStatic
        fun newInstance(param1: String, param2: String) =
            LockFragment().apply {
                arguments = Bundle().apply {
                    putString(ARG_PARAM1, param1)
                    putString(ARG_PARAM2, param2)
                }
            }
    }

    //华为一进入app会弹出通知授权，关闭弹窗后会再次调用onResume，导致显示两层此intro
    var showIntro1 = false
    private fun checkOpen() {
        lifecycleScope.launch(Dispatchers.IO) {
            if ((!MMKVUtils.getBoolean(MyConstants.SP_KEY_INTRO_1, false)) && (!PermissionUtil.hasMustPermission(requireContext()))) {
                if (!showIntro1) {
                    showIntro1 = true
                    withContext(Dispatchers.Main) {
                        MaterialIntroView.Builder(requireActivity())
                            .enableDotAnimation(true)
                            .enableIcon(false)
                            .setFocusGravity(FocusGravity.CENTER)
                            .setFocusType(Focus.MINIMUM)
                            .setDelayMillis(0)
                            .setShape(ShapeType.RECTANGLE)
//                .setIdempotent(true)
                            .enableFadeAnimation(true)
                            .performClick(true)
                            .setInfoText("使用远离手机需要先授予一些必要权限。")
//                .setShapeType(ShapeType.CIRCLE)
                            .setTarget(v.tv_check_permission)
                            .setUsageId(MyConstants.SP_KEY_INTRO_1) //THIS SHOULD BE UNIQUE ID
                            .setListener {
                                showIntro1 = false
                                MMKVUtils.put(MyConstants.SP_KEY_INTRO_1, true)
                            }
                            .show()
                    }
                }
            } else if (!MMKVUtils.getBoolean(MyConstants.SP_KEY_INTRO_2, false)) {

                MMKVUtils.put(MyConstants.SP_KEY_INTRO_3, true)
                MMKVUtils.put(MyConstants.SP_KEY_INTRO_4, true)
                withContext(Dispatchers.Main) {
                    MaterialIntroView.Builder(requireActivity())
                        .enableDotAnimation(true)
                        .enableIcon(false)
                        .setFocusGravity(FocusGravity.CENTER)
                        .setFocusType(Focus.MINIMUM)
                        .setDelayMillis(0)
                        .setShape(ShapeType.RECTANGLE)
//                .setIdempotent(true)
                        .enableFadeAnimation(true)
                        .performClick(false)
                        .setInfoText("先来体验1分钟锁机吧！")
//                .setShapeType(ShapeType.CIRCLE)
                        .setTarget(v.cl_lock_fast)
                        .setUsageId(MyConstants.SP_KEY_INTRO_2) //THIS SHOULD BE UNIQUE ID
                        .setListener {
                            MMKVUtils.put(MyConstants.SP_KEY_INTRO_2, true)
                            NormalDialog(this@LockFragment).apply {
                                setTitle("即将进入1分钟锁机")
                                isCancelable = false
                                setContent("如果您使用过本软件，并了解软件的工作原理，可以选择跳过体验。")
                                setOnNormalOKClickListener("开始体验", object : OnOKClickListener {
                                    override fun onclick() {
                                        //加入默认白名单
                                        <EMAIL>(Dispatchers.IO) {
                                            insertDefaultWhiteApp()

                                            val current = System.currentTimeMillis()
                                            val timeLength = (60 * 1000).toLong()
                                            val lockHistory = LockHistory(
                                                0,
                                                "体验锁机：${TimeUtil.formatHHMM(1)}",
                                                "",
                                                current, current, timeLength, timeLength, 1, 1,
                                                "", "", isFinish = false, isForceQuit = false, isSynced = false, isGeneratedCard = false,
                                                deleteWhiteAppTemp = "[]"
                                            )
                                            viewModel.createLockHistory(lockHistory)
                                        }

                                    }
                                })
                                setOnNormalCancelClickListener("跳过体验", object : OnCancelClickListener {
                                    override fun onclick() {
//                                        insertDefaultWhiteApp()
                                        MMKVUtils.put(MyConstants.SP_KEY_FLAG_HAS_LOCKED, true)
                                        MMKVUtils.put(MyConstants.SP_KEY_INTRO_8, true)
                                        checkOpen()
                                    }
                                })
                                showDialog()
                            }
                        }
                        .show()
                }
            } else if (!MMKVUtils.getBoolean(MyConstants.SP_KEY_FLAG_HAS_LOCKED, false)) {
//                MyToastUtil.showError("tezt7")
            } else if (!MMKVUtils.getBoolean(MyConstants.SP_KEY_INTRO_8, false)) {
//                MyToastUtil.showError("tezt6")
                withContext(Dispatchers.Main) {
                    NormalDialog(this@LockFragment).apply {
                        setTitle("恭喜")
                        setContent("您已经完成了第一次锁机\n感觉怎么样？")
                        isCancelable = false
                        setOnNormalOKClickListener("还不错", object : OnOKClickListener {
                            override fun onclick() {
                                MMKVUtils.put(MyConstants.SP_KEY_INTRO_8, true)
                                NormalDialog(this@LockFragment).apply {
                                    setTitle("来自开发者的请求")
                                    setContent("如果您觉得我们的产品还不错，能否请您给我们一个五星好评，然后说说您使用本软件的用途，或对我们的期待。\n\n如：“想在写作业的时候使用它来戒手机”或者“如果有清单功能就更好了”等等，您的反馈与评价决定我们以后的优化方向。")
                                    isCancelable = false
                                    setGravity(Gravity.START)
                                    setOnNormalOKClickListener("给个好评", object : OnOKClickListener {
                                        override fun onclick() {
                                            MMKVUtils.put(MyConstants.SP_KEY_INTRO_8, true)
                                            MMKVUtils.put(MyConstants.SP_KEY_FEEDBACK_OR_STAR, true)
                                            openFromMarket(
                                                requireContext() as BaseActivity,
                                                AppUtils.getAppPackageName(),
                                                "https://a.app.qq.com/o/simple.jsp?pkgname=${AppUtils.getAppPackageName()}"
                                            )
                                        }
                                    })
                                    setOnNormalCancelClickListener("不给", object : OnCancelClickListener {
                                        override fun onclick() {
                                            MMKVUtils.put(MyConstants.SP_KEY_INTRO_8, true)
//                                            showWelcome()
//                                            if (MMKVUtils.getInt(MyConstants.SP_KEY_VIP_STATE, VIP_STATE_FREE) != VIP_STATE_FOREVER) {
//                                                v.mcv_vip_lock.visibility = View.VISIBLE
//                                            }
                                            checkOpen()
                                        }
                                    })
                                    showDialog()
                                }
                            }
                        })
                        setOnNormalCancelClickListener("一般般", object : OnCancelClickListener {
                            override fun onclick() {
                                MMKVUtils.put(MyConstants.SP_KEY_INTRO_8, true)
//                                showWelcome()
//                                if (MMKVUtils.getInt(MyConstants.SP_KEY_VIP_STATE, VIP_STATE_FREE) != VIP_STATE_FOREVER) {
//                                    v.mcv_vip_lock.visibility = View.VISIBLE
//                                }
                                checkOpen()
                            }
                        })
                        showDialog()
                    }
                }
            } else if (!MMKVUtils.getBoolean(MyConstants.SP_KEY_INTRO_12, false)) {
//                MyToastUtil.showError("tezt5")
                withContext(Dispatchers.Main) {
                    MMKVUtils.put(MyConstants.SP_KEY_INTRO_12, true)
                    NormalDialog(this@LockFragment).apply {
                        setTitle("恭喜")
                        setContent("恭喜您完成了新手引导\n现在去尝试一下其它功能吧！")
                        isCancelable = false
                        setOnNormalOKClickListener("好的", object : OnOKClickListener {
                            override fun onclick() {
                                checkOpen()
                            }
                        })
                        showDialog()
                    }
                }
            } else if (!MMKVUtils.getBoolean(MyConstants.SP_KEY_SELECT_MONEY, false)) {
//                MyToastUtil.showError("tezt4")
                withContext(Dispatchers.Main) {
                    MyUtil.forceShowChooseMoneyDialog(requireContext(), object : MyUtil.Companion.OnMoneySelectListener {
                        override fun onMoneySelect(money: Int) {
                            MMKVUtils.put(MyConstants.SP_KEY_SELECT_MONEY, true)
                            MMKVUtils.put(MyConstants.SP_KEY_FORCE_UNLOCK_PUNISH, money)
//                            v.tv_financial_punish.text = "${money}元"

                        }
                    })
                }
            } else {
//                MyToastUtil.showError("tezt3")
                val c = Calendar.getInstance()
                val date = "${c.get(Calendar.YEAR)}-${c.get(Calendar.MONTH) + 1}-${c.get(Calendar.DAY_OF_MONTH)}"
                //推广
                if (!MMKVUtils.getBoolean(MyConstants.SP_KEY_CLICK_VIP_LOCK, false)
                    && MMKVUtils.getInt(MyConstants.SP_KEY_VIP_STATE, VIP_STATE_FREE) != VIP_STATE_FOREVER
                ) {
                    withContext(Dispatchers.Main) {
                        v.cl_vip_lock.visibility = View.VISIBLE
                        val giftPicUrl = MMKVUtils.getString(MyConstants.SP_KEY_CONFIG_GIFT_PIC_URL, "")
                        if (giftPicUrl.isEmpty()) {
                            v.iv_vip_item_gift_img2.visibility = View.GONE
                            v.tv_vip_item_gift_text2.visibility = View.GONE
                            v.iv_zuanshi.visibility = View.VISIBLE
                        } else {
                            v.iv_vip_item_gift_img2.visibility = View.VISIBLE
                            v.tv_vip_item_gift_text2.visibility = View.VISIBLE
                            v.iv_zuanshi.visibility = View.GONE
                            Glide.with(requireActivity())
                                .load(giftPicUrl)
                                //.transition(DrawableTransitionOptions.withCrossFade())
                                .diskCacheStrategy(DiskCacheStrategy.AUTOMATIC)
                                .into(v.iv_vip_item_gift_img2)
                            v.tv_vip_item_gift_text2.text = MMKVUtils.getString(MyConstants.SP_KEY_CONFIG_GIFT_TEXT, "")
                        }
                    }
                } else {
                    withContext(Dispatchers.Main) { v.cl_vip_lock.visibility = View.GONE }
                }

                //是否付费解锁了
                if (MMKVUtils.getBoolean(MyConstants.SP_KEY_PAY_UNLOCK, false)
                    && (MMKVUtils.getInt(MyConstants.SP_KEY_VIP_STATE, VIP_STATE_FREE) != VIP_STATE_FOREVER)
                ) {
//                    MyToastUtil.showError("tezt2")
                    withContext(Dispatchers.Main) {
                        NormalDialog(this@LockFragment).apply {
                            setTitle("温馨提示")
                            setContent("您刚刚使用了罚金解锁，为降低您的损失，已缴纳的罚金在1小时以内可用于升级永久版VIP，如有需要，可前往升级。")
                            isCancelable = false
                            setGravity(Gravity.START)
                            setOnNormalOKClickListener("去升级", object : OnOKClickListener {
                                override fun onclick() {
                                    val intent = Intent(requireContext(), VIP2Activity::class.java)
                                    intent.putExtra(FROM_WHERE, "LockFragment罚金解锁后提示升级SVIP")
                                    startActivity(intent)
                                }
                            })
                            setOnNormalCancelClickListener("再等等", object : OnCancelClickListener {
                                override fun onclick() {

                                }
                            })
                            showDialog()
                        }
                    }
//                }else if (true
                } else if (System.currentTimeMillis() > MMKVUtils.getLong(MyConstants.SP_KEY_NOTIFY_SHARE, 0L)
                ) {//显示的时间点
//                    MyToastUtil.showError("tezt1")
                    withContext(Dispatchers.Main) {
                        NormalDialog(this@LockFragment).apply {
                            isCancelable = false
                            setTitle("嗨~")
                            setContent("亲爱的用户：\n        如果您觉得『${AppUtils.getAppName()}』还不错，何不分享给好友或同学共同进步呢？\n\n        知道我们的人越多，我们也会越有更新动力哒。")
                            setGravity(Gravity.START)
                            setOnNormalOKClickListener("立刻分享", object : OnOKClickListener {
                                override fun onclick() {
                                    MMKVUtils.put(MyConstants.SP_KEY_NOTIFY_SHARE, System.currentTimeMillis() + 1000 * 24 * 60 * 60 * 1000L)

                                    val sharedIntent = Intent()
                                    //设置动作为Intent.ACTION_SEND
                                    sharedIntent.action = Intent.ACTION_SEND
                                    //设置为文本类型
                                    sharedIntent.type = "text/*"
                                    sharedIntent.putExtra(
                                        Intent.EXTRA_TEXT,
                                        "发现一个自律神器APP【${AppUtils.getAppName()}】，一起告别沉迷手机。"
                                                + "https://a.app.qq.com/o/simple.jsp?pkgname=${AppUtils.getAppPackageName()}"
                                    )

                                    //设置要分享的内容
                                    startActivity(Intent.createChooser(sharedIntent, "分享到"))

//                                    openFromMarket(
//                                        requireContext() as BaseActivity,
//                                        AppUtils.getAppPackageName(),
//                                        "https://a.app.qq.com/o/simple.jsp?pkgname=${AppUtils.getAppPackageName()}"
//                                    )
                                }
                            })
                            setOnNormalCancelClickListener("以后再说", object : OnCancelClickListener {
                                override fun onclick() {
                                    MMKVUtils.put(MyConstants.SP_KEY_NOTIFY_SHARE, System.currentTimeMillis() + 100 * 24 * 60 * 60 * 1000L)
                                }
                            })
                            showDialog()
                        }
                    }


                } else if (!MMKVUtils.getBoolean(MyConstants.SP_KEY_FEEDBACK_OR_STAR, false)
                    && System.currentTimeMillis() > MMKVUtils.getLong(MyConstants.SP_KEY_NOTIFY_STAR, 0L)
                ) {//显示的时间点
//                    MyToastUtil.showError("tezt1")
                    withContext(Dispatchers.Main) {
                        NormalDialog(this@LockFragment).apply {
                            isCancelable = false
                            setTitle("来自开发者的问候")
                            setContent("亲爱的用户：\n        您已经使用『${AppUtils.getAppName()}』一段时间了，不只感觉如何？如果您觉得软件对您有用，可否请您给我们一个5星好评，这不仅会给我们巨大的动力，更会使我们被更多的人看到，让我们更好地发展。\n        赠人玫瑰，手有余香。\n\n        如果产品有让您不满意的地方，欢迎向我们提建议或吐槽，我们会尊重每一个用户的意见。")
                            setGravity(Gravity.START)
                            setOnNormalOKClickListener("五星好评", object : OnOKClickListener {
                                override fun onclick() {
                                    MMKVUtils.put(MyConstants.SP_KEY_FEEDBACK_OR_STAR, true)
                                    openFromMarket(
                                        requireContext() as BaseActivity,
                                        AppUtils.getAppPackageName(),
                                        "https://a.app.qq.com/o/simple.jsp?pkgname=${AppUtils.getAppPackageName()}"
                                    )
                                }
                            })
                            setOnNormalCloseClickListener(object : OnCloseClickListener {
                                override fun onclick() {
                                    MMKVUtils.put(MyConstants.SP_KEY_FEEDBACK_OR_STAR, true)
                                }
                            })
                            showDialog()
                        }
                    }


                } else if (MMKVUtils.getString(MyConstants.SP_KEY_ALI, "") != date
                    && MMKVUtils.getBoolean(MyConstants.SP_KEY_SHOW_ALI, true)
                    && MMKVUtils.getBoolean(MyConstants.SP_KEY_SHOW_ALI_SERVER, false)
                ) {
//                } else if (true) {
//                    MyToastUtil.showError("tezt")
                    AliMoneyFragment.newInstance().apply {
                        this.show(<EMAIL>().supportFragmentManager, "AliMoneyFragment")
                    }
                } else {
//                    MyToastUtil.showError("tezt`")
                    lifecycleScope.launch(Dispatchers.IO) {
                        try {
                            val result = MyRetrofitClient.service.getLaunchDialog()
                            withContext(Dispatchers.Main) {
                                when (result.code) {
                                    200 -> {
                                        val launchDialog = result.data!!
                                        if (launchDialog.id > MMKVUtils.getLong(MyConstants.SP_LAUNCH_DIALOG_ID, -1)) {
                                            ImageDialog(this@LockFragment).apply {
                                                setTitle(launchDialog.title)
                                                setImageUrl(launchDialog.image)
//                                        setGravity(Gravity.START)
                                                isCancelable = false
                                                setOnNormalOKClickListener("去看看", object : OnOKClickListener {
                                                    override fun onclick() {
                                                        requireActivity().lifecycleScope.launch(Dispatchers.IO) {
                                                            readLaunchMsg(result.data.id)
                                                        }
//                                                        MMKVUtils.put(MyConstants.SP_LAUNCH_DIALOG_ID, launchDialog.id)
                                                        when (launchDialog.type) {
                                                            0 -> {
                                                                val uri = Uri.parse(launchDialog.param1)
                                                                startActivity(Intent(Intent.ACTION_VIEW, uri))
                                                            }

                                                            1 -> {
                                                                val intent = Intent(requireContext(), TheWebViewActivity::class.java)
                                                                intent.putExtra("title", "")
                                                                intent.putExtra("url", launchDialog.param1)
                                                                <EMAIL>(intent)
                                                            }

                                                            2 -> {
                                                                openFromMarket(requireContext(), launchDialog.param1, "")
//                                                        val ii = Intent(Intent.ACTION_VIEW)
//                                                        ii.data = Uri.parse("market://details?id=" + launchDialog.param1)
//                                                        <EMAIL>(ii)
                                                            }

                                                            3 -> {
                                                                ActivityUtils.startActivity(launchDialog.param1, launchDialog.param2)
                                                            }

                                                            4 -> {
                                                                val clipboard: ClipboardManager =
                                                                    requireContext().applicationContext.getSystemService(Context.CLIPBOARD_SERVICE) as ClipboardManager
                                                                val clipData = ClipData.newPlainText(null, "${launchDialog.param1}")
                                                                clipboard.setPrimaryClip(clipData)
                                                                MyToastUtil.showSuccess("${launchDialog.param2}")
                                                                try {
                                                                    val intent = Intent()
                                                                    val cmp = ComponentName("com.tencent.mm", "com.tencent.mm.ui.LauncherUI")
                                                                    intent.action = Intent.ACTION_MAIN
                                                                    intent.addCategory(Intent.CATEGORY_LAUNCHER)
                                                                    intent.addFlags(Intent.FLAG_ACTIVITY_NEW_TASK)
                                                                    intent.component = cmp
                                                                    startActivity(intent)
                                                                } catch (e: Exception) {

                                                                }
                                                            }

                                                            else -> {
                                                                MyToastUtil.showInfo("未知消息类型，请先升级客户端")
                                                            }
                                                        }
                                                    }
                                                })
                                                setOnNormalCancelClickListener("知道了", object : OnCancelClickListener {
                                                    override fun onclick() {
                                                        requireActivity().lifecycleScope.launch(Dispatchers.IO) {
                                                            readLaunchMsg(result.data.id)
                                                        }
//                                                        MMKVUtils.put(MyConstants.SP_LAUNCH_DIALOG_ID, launchDialog.id)
                                                    }
                                                })
                                                show()
                                            }
                                        }
                                    }

                                    80001 -> {
//                                        animatorSetsuofang.start()

                                    }

                                    else -> {
                                        MyToastUtil.showInfo(result.msg)
                                    }
                                }
//                                MyToastUtil.showInfo(result.toString())
                            }

                        } catch (e: Exception) {
                            MyToastUtil.showInfo(e.toString())
                        }
                    }

                }
                MMKVUtils.put(MyConstants.SP_KEY_PAY_UNLOCK, false)
            }

        }


    }

    private fun canAddWhiteApp(pkg: String, body: () -> Unit) {
        oldWhiteList.forEach {
            if (it.pkg == pkg) {
                return
            }
        }
        body.invoke()
    }

    private suspend fun insertDefaultWhiteApp() {
        val addWhiteList = mutableListOf<WhiteApp>()
        withContext(Dispatchers.Main) {
            val intentDial = Intent(Intent.ACTION_DIAL)
            val resolveInfoDial: ResolveInfo? = requireContext().packageManager.resolveActivity(intentDial, PackageManager.MATCH_DEFAULT_ONLY)
            resolveInfoDial?.let {
                val packageName = it.activityInfo.packageName
                val className = it.activityInfo.name
                Log.d("MainActivity dial", "packageName=${packageName},className=${className}")
                canAddWhiteApp(packageName) {
                    addWhiteList.add(WhiteApp(packageName, className))
                }
            }


            val intentMsg = Intent(Intent.ACTION_VIEW)
            intentMsg.setType("vnd.android-dir/mms-sms")
            val resolveInfoMsg: ResolveInfo? = requireContext().packageManager.resolveActivity(intentMsg, PackageManager.MATCH_DEFAULT_ONLY)
            resolveInfoMsg?.let {
                val packageName = it.activityInfo.packageName
                val className = it.activityInfo.name
                Log.d("MainActivity msg", "packageName=${packageName},className=${className}")
                canAddWhiteApp(packageName) {
                    addWhiteList.add(WhiteApp(packageName, className))
                }
            }

            val intentCamera = Intent(MediaStore.ACTION_IMAGE_CAPTURE)
            val resolveInfoCamera: ResolveInfo? = requireContext().packageManager.resolveActivity(intentCamera, PackageManager.MATCH_DEFAULT_ONLY)
            resolveInfoCamera?.let {
                val packageName = it.activityInfo.packageName
                val className = it.activityInfo.name
                Log.d("MainActivity camera", "packageName=${packageName},className=${className}")
                canAddWhiteApp(packageName) {
                    addWhiteList.add(WhiteApp(packageName, className))
                }
            }

            if (MyUtil.checkPackageInstalled(requireActivity(), "com.tencent.mm", MyConstants.URL_WXPAY)) {
                canAddWhiteApp("com.tencent.mm") {
                    addWhiteList.add(WhiteApp("com.tencent.mm", "com.tencent.mm.ui.LauncherUI"))
                }
            }
            if (MyUtil.checkPackageInstalled(requireActivity(), "com.eg.android.AlipayGphone", MyConstants.URL_ALIPAY)) {
                canAddWhiteApp("com.eg.android.AlipayGphone") {
                    addWhiteList.add(WhiteApp("com.eg.android.AlipayGphone", "com.eg.android.AlipayGphone.AlipayLogin"))
                }
            }
        }

        viewModel.createWhiteAppList(addWhiteList)

//        AppDatabase.getInstance(TheApplication.getInstance()).whiteAppDao().insertWhiteApps(addWhiteList)

//        val whiteList = db.whiteAppDao().getGlobalWhiteAppsImmediate()
//
//        addWhiteList.forEachIndexed { _, addWhite ->
//            var hasJoined = false
//            whiteList.forEach {
//                if (addWhite.pkg == it.pkg && addWhite.mainActivity == it.mainActivity) {
//                    hasJoined = true
//                    return@forEach
//                }
//            }
//
//            if (!hasJoined) {
//                db.whiteAppDao().insertWhiteAppImmediate(addWhite)
//            }
//        }
    }

    //已读开始弹窗消息
    suspend fun readLaunchMsg(launchDialogMsgId: Long) {
        if (MMKVUtils.getInt(MyConstants.SP_KEY_USER_ID, -1) == -1) {
            MMKVUtils.put(MyConstants.SP_LAUNCH_DIALOG_ID, launchDialogMsgId)
            return
        }

        try {
            val result = MyRetrofitClient.service.readLaunchDialogMsg(launchDialogMsgId)
            when (result.code) {
                200 -> {
                }

                else -> {
                    MyToastUtil.showInfo(result.msg)
                }
            }
        } catch (e: Exception) {

        }
    }

    override fun onResume() {
        super.onResume()

        Log.d(TAG, "onResume")

//        lifecycleScope.launch(Dispatchers.IO) {
//            insertDefaultWhiteApp()
//
//            val current = System.currentTimeMillis()
//            val timeLength = (60 * 1000).toLong()
//            val lockHistory = LockHistory(
//                0, "体验锁机：${TimeUtil.formatHHMM(1)}",
//                current, current, timeLength, timeLength, 1, 1,
//                "", "", isFinish = false, isForceQuit = false, isSynced = false, isGeneratedCard = false,
//                deleteWhiteAppTemp = "[]"
//            )
//            viewModel.createLockHistory(lockHistory)
//        }


//        MobclickAgent.onPageStart("LockFragment")
        refreshWellKnowWord()

        val calendar = Calendar.getInstance()
        when (calendar.get(Calendar.HOUR_OF_DAY) * 60 + calendar.get(Calendar.MINUTE)) {
            in 240..600 -> {//早晨
                v.iv_punch_card.setImageResource(R.drawable.ic_punch_card_morning)
            }

            in 600..1200 -> {//白天
                v.iv_punch_card.setImageResource(R.drawable.ic_punch_card_normal)
            }

            in 0..240, in 1200..1440 -> {//夜晚
                v.iv_punch_card.setImageResource(R.drawable.ic_punch_card_night)
            }
        }

        checkOpen()
        checkForceUnlockPause()
    }


    private fun checkForceUnlockPause() {
        work = lifecycleScope.launch(Dispatchers.IO) {

            while (MMKVUtils.getLong(MyConstants.SP_KEY_FORCE_UNLOCK_PAUSE, 0L) > System.currentTimeMillis()) {
                withContext(Dispatchers.Main) {
                    v.chip_pause_info.visibility = View.VISIBLE
                    v.chip_pause_info.text =
                        "锁机暂停中 ${formatHHMMSS((MMKVUtils.getLong(MyConstants.SP_KEY_FORCE_UNLOCK_PAUSE, 0L) - System.currentTimeMillis()) / 1000)}"
                }
                delay(100)
            }
            withContext(Dispatchers.Main) {
                v.chip_pause_info.visibility = View.GONE
            }
        }
    }

    override fun onPause() {
        super.onPause()
//        MobclickAgent.onPageEnd("LockFragment")

        work?.cancel()

    }

    override fun onDestroy() {
        super.onDestroy()

        animatorSetSuofang.cancel()
    }

    private fun refreshWellKnowWord() {

        lifecycleScope.launch(Dispatchers.IO) {
            try {
                val result = viewModel.lockRepository.getWellKnowWord()
                withContext(Dispatchers.Main) {
                    if (result.code == 200) {
                        result.data?.let {

                            wordId = it.wellKnowWordRepositoryId
                            isStar = it.isStar
                            star = it.star
                            share = it.share
                            if (isStar) {
                                v.iv_lock_like.setColorFilter(resources.getColor(R.color.colorRed))
                            } else {
                                v.iv_lock_like.setColorFilter(resources.getColor(R.color.colorWhiteBackground))
                            }
                            if (it.hasJoinedDays == 0) {
                                v.tv_lock_card1_title.visibility = View.INVISIBLE
                            } else {
                                v.tv_lock_card1_title.visibility = View.VISIBLE
                                v.tv_lock_card1_title.text = "已加入${it.hasJoinedDays}天"
                            }

                            v.tv_lock_how_many.text = "${it.onlineNumber}人锁机中"
                            v.tv_lock_words.text = it.word
                            v.tv_lock_words_author.text = "——${it.author}"
                            v.tv_lock_like_count.text = "${it.star}"
                            v.tv_lock_share_count.text = "${it.share}"
                            Glide.with(this@LockFragment)
                                .load(it.imgUrl)
                                .transition(DrawableTransitionOptions.withCrossFade())
                                .diskCacheStrategy(DiskCacheStrategy.AUTOMATIC)
                                .into(v.imageView8)
                            MMKVUtils.put(MyConstants.SP_KEY_WELL_KNOW_WORD, it.word)
                            MMKVUtils.put(MyConstants.SP_KEY_WELL_KNOW_WORD_AUTHOR, it.author)
                            MMKVUtils.put(MyConstants.SP_KEY_WELL_KNOW_WORD_IMG, it.imgUrl)

                            DenyUninstallAppWidget.updateAppWidget(requireContext(), AppWidgetManager.getInstance(requireContext()))
                        }
                    } else {
                        MyToastUtil.showInfo(result.msg)
                    }
                }
            } catch (e: Exception) {
                MyToastUtil.showInfo(e.message)
            }
        }

    }

    private class DiffCallBack(val oldList: MutableList<WhiteApp>, val newList: MutableList<WhiteApp>) : DiffUtil.Callback() {

        override fun areItemsTheSame(oldItemPosition: Int, newItemPosition: Int): Boolean {
            return (oldList[oldItemPosition].whiteAppIndexId == newList[newItemPosition].whiteAppIndexId)
        }

        override fun getOldListSize(): Int {
            return oldList.size
        }

        override fun getNewListSize(): Int {
            return newList.size
        }

        override fun areContentsTheSame(oldItemPosition: Int, newItemPosition: Int): Boolean {
            return (oldList[oldItemPosition].pkg == newList[newItemPosition].pkg)
                    && (oldList[oldItemPosition].mainActivity == newList[newItemPosition].mainActivity)
                    && (oldList[oldItemPosition].maxLen == newList[newItemPosition].maxLen)
        }

    }
}


