package com.lijianqiang12.silent.component.activity.room.createroom

import android.annotation.SuppressLint
import android.app.ProgressDialog
import android.content.Intent
import android.net.Uri
import android.os.Bundle
import android.provider.MediaStore
import android.view.Gravity
import android.widget.Toast
import androidx.lifecycle.lifecycleScope
import com.blankj.utilcode.util.ActivityUtils
import com.blankj.utilcode.util.LogUtils
import com.lijianqiang12.silent.utils.MMKVUtils
import com.bumptech.glide.Glide
import com.bumptech.glide.load.engine.DiskCacheStrategy
import com.lijianqiang12.silent.MyConstants
import com.lijianqiang12.silent.R
import com.lijianqiang12.silent.data.model.net.MyRetrofitClient
import com.lijianqiang12.silent.component.activity.base.BaseActivity
import com.lijianqiang12.silent.component.activity.custom.dialog.BottomSingleSelectDialogFragment
import com.lijianqiang12.silent.component.activity.custom.dialog.NormalDialog
import com.lijianqiang12.silent.component.activity.custom.dialog.OnCancelClickListener
import com.lijianqiang12.silent.component.activity.custom.dialog.OnOKClickListener
import com.lijianqiang12.silent.component.activity.me.userinfo.UserInfoActivity
import com.lijianqiang12.silent.utils.DialogUtil
import com.lijianqiang12.silent.utils.MyToastUtil
import com.lijianqiang12.silent.utils.MyUtil
import com.lijianqiang12.silent.utils.UriUtil
import com.tencent.cos.xml.CosXmlService
import com.tencent.cos.xml.CosXmlServiceConfig
import com.tencent.cos.xml.exception.CosXmlClientException
import com.tencent.cos.xml.exception.CosXmlServiceException
import com.tencent.cos.xml.listener.CosXmlProgressListener
import com.tencent.cos.xml.listener.CosXmlResultListener
import com.tencent.cos.xml.model.CosXmlRequest
import com.tencent.cos.xml.model.CosXmlResult
import com.tencent.cos.xml.model.`object`.PutObjectRequest
import com.tencent.qcloud.core.auth.SessionCredentialProvider
import com.tencent.qcloud.core.http.HttpRequest
import com.yalantis.ucrop.UCrop
import kotlinx.android.synthetic.main.activity_create_room.*
import kotlinx.android.synthetic.main.activity_room_all.iv_back
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.launch
import kotlinx.coroutines.withContext
import java.io.File
import java.net.MalformedURLException
import java.net.URL

class CreateRoomActivity : BaseActivity() {
    private val requestPicCode = 1000001
    private lateinit var dialog: ProgressDialog
    private var bgUri: Uri? = null
    private var bgString: String = ""
    private var selectItmIndex = 7
    var changeType = 0
    var roomId = 0
    val roomTypeList = arrayListOf("考研", "高考", "中考", "英语", "早睡", "情侣", "班级", "其它")

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)


        dialog = ProgressDialog(this)

        setContentView(R.layout.activity_create_room)

        changeType = intent.getIntExtra("changeType", 0)

        if (changeType == 1) {
            tv_create_toom_title.text = "申请修改房间信息"
            roomId = intent.getIntExtra("roomId", 0)
//            LogUtils.d("ljq=======11${intent.getStringExtra("bgString")}")
            bgString = intent.getStringExtra("bgString")!!
            Glide.with(this).load(bgString)
                //.transition(DrawableTransitionOptions.withCrossFade())
                .diskCacheStrategy(DiskCacheStrategy.AUTOMATIC)
                .into(iv_create_room_bg)
            selectItmIndex = roomType2Index(intent.getIntExtra("roomType", -1))
            tv_room_type_name.text = roomTypeList[selectItmIndex]
            et_room_name.setText(intent.getStringExtra("roomName"))
            et_room_desc.setText(intent.getStringExtra("roomDesc"))
        }


        if (!MMKVUtils.getBoolean(MyConstants.SP_AGREE_ROOM, false)) {
            NormalDialog(this).apply {
                isCancelable = false
                setGravity(Gravity.START)
                setTitle("必读")
                setContent("1.为避免房间过多，我们做了一些限制，非VIP不能创建房间，普通VIP能创建3个房间，永久VIP能创建10个房间；\n2.仅允许创建自律、学习等相关房间；\n3.请勿创建色情低俗、犯罪、政治敏感相关房间；\n4.如有违反，审核人员有权进行封号处罚。")
                setOnNormalOKClickListener("同意", object : OnOKClickListener {
                    override fun onclick() {
                        MMKVUtils.put(MyConstants.SP_AGREE_ROOM, true)
                    }
                })
                setOnNormalCancelClickListener("拒绝", object : OnCancelClickListener {
                    override fun onclick() {
                        <EMAIL>()
                    }
                })
                showDialog()
            }
        }

        iv_back.setOnClickListener {
            goBack()
        }

        //选择图片
        cl_select_room_bg.setOnClickListener {
            changeRoomBg()
        }

        //申请创建
        btn_room_create_ok.setOnClickListener {
            if (et_room_pwd.text.isEmpty()) {
                NormalDialog(this).apply {
                    setTitle("温馨提示")
                    setContent("您未设置房间密码，任何人都可以加入该房间。")
                    setOnNormalOKClickListener("确定", object : OnOKClickListener {
                        override fun onclick() {
                            saveResult()
                        }
                    })
                    setOnNormalCancelClickListener("返回", object : OnCancelClickListener {
                        override fun onclick() {
                        }
                    })
                    showDialog()
                }
            } else {
                saveResult()
            }


        }

        //选择类型
        cl_choose_create_room_type.setOnClickListener {

            val bottomDialog = BottomSingleSelectDialogFragment.newInstance()
            bottomDialog.setShowList(roomTypeList)
            bottomDialog.setValueList(arrayListOf(0, 1, 2, 3, 4, 5, 6, 7))
            bottomDialog.setOnValueSelectListener(object : BottomSingleSelectDialogFragment.OnValueSelectListener {
                override fun onSelect(value: Long, show: String) {
                    selectItmIndex = value.toInt()
                    this@CreateRoomActivity.tv_room_type_name.text = show
                }
            })
            bottomDialog.show(supportFragmentManager, "")
        }
    }

    fun saveResult() {
        when {
            bgUri == null && bgString.isEmpty() -> {
                MyToastUtil.showInfo("请先选择房间背景图")
            }
            et_room_name.text.isEmpty() -> {
                MyToastUtil.showInfo("请先填写房间名")
            }
            et_room_desc.text.isEmpty() -> {
                MyToastUtil.showInfo("请先填写房间简介")
            }
            else -> {
                uploadBg(bgUri, bgString)
            }
        }
    }

    fun index2RoomType(index: Int): Int {
        return when (index) {
            0 -> 1
            1 -> 2
            2 -> 17
            3 -> 3
            4 -> 5
            5 -> 4
            6 -> 6
            else -> -1
        }
    }

    fun roomType2Index(roomType: Int): Int {
        return when (roomType) {
            1 -> 0
            2 -> 1
            17 -> 2
            3 -> 3
            5 -> 4
            4 -> 5
            6 -> 6
            else -> 7
        }
    }

    fun changeRoomBg() {
        val intent = Intent(Intent.ACTION_PICK, MediaStore.Images.Media.EXTERNAL_CONTENT_URI)
        startActivityForResult(intent, requestPicCode)
//        XXPermissions.with(this) // 申请安装包权限
//            .permission(com.hjq.permissions.Permission.MANAGE_EXTERNAL_STORAGE)
//            .request(object : OnPermissionCallback {
//                override fun onGranted(permissions: List<String>, all: Boolean) {
//                    val intent = Intent(Intent.ACTION_PICK, MediaStore.Images.Media.EXTERNAL_CONTENT_URI)
//                    startActivityForResult(intent, requestPicCode)
//                }
//
//                override fun onDenied(permissions: List<String>, never: Boolean) {
//                    NormalDialog(this@CreateRoomActivity).apply {
//                        setTitle("温馨提示")
//                        setContent("该功能需要读写存储权限，请授予。")
//                        setOnNormalOKClickListener("授予", object : OnOKClickListener {
//                            override fun onclick() {
//                                changeRoomBg()
//                            }
//                        })
//                        setOnNormalCancelClickListener(object : OnCancelClickListener {
//                            override fun onclick() {
//                            }
//                        })
//                        show()
//                    }
//                }
//            })
    }

    fun uploadBg(imageUri: Uri?, bgString: String) {
        if (imageUri == null) {
            request(bgString)
        } else {

            LogUtils.d("imageUri=" + imageUri.toString())
            val file = UriUtil.getFileByUri(this, imageUri)
            MyUtil.showDialog(dialog, "正在上传，请勿操作...")

//        val appid = "**********"
            val region = "ap-beijing"
            //创建 CosXmlServiceConfig 对象，根据需要修改默认的配置参数
            val cosXmlServiceConfig = CosXmlServiceConfig.Builder()
                .setRegion(region)
                .isHttps(true)
                .builder()

            /**
             * 获取授权服务的 url 地址
             */
            var url: URL? = null // 后台授权服务的 url 地址
            try {
                url = URL("https", "offphone.shuge888.com", 33333, "/other/tempCosBg/v1")
            } catch (e: MalformedURLException) {
                e.printStackTrace()
            }
            /**
             * 初始化 {@link QCloudCredentialProvider} 对象，来给 SDK 提供临时密钥。
             */
            val qCloudCredentialProvider = SessionCredentialProvider(
                HttpRequest.Builder<String>()
                    .url(url)
                    .method("GET")
                    .build()
            )
            val cosXmlService = CosXmlService(this, cosXmlServiceConfig, qCloudCredentialProvider)
            val bucket = "offphone-room-bg-**********" // cos v5 的 bucket格式为：xxx-appid, 如 test-**********
            val cosPath = "${MMKVUtils.getInt(MyConstants.SP_KEY_USER_ID, -1)}/${System.currentTimeMillis()}"

            val putObjectRequest = PutObjectRequest(bucket, cosPath, file.inputStream())

            putObjectRequest.progressListener = CosXmlProgressListener { progress, max ->
                LogUtils.d("${progress}/${max}")
            }
            LogUtils.d("cosXmlService start")
            // 使用异步回调上传
            cosXmlService.putObjectAsync(putObjectRequest, object : CosXmlResultListener {

                @SuppressLint("CheckResult")
                override fun onSuccess(cosXmlRequest: CosXmlRequest, cosXmlResult: CosXmlResult) {
                    LogUtils.d("cosXmlService onSuccess")
                    cosXmlService.release()
                    request(cosXmlResult.accessUrl)
                }

                override fun onFail(cosXmlRequest: CosXmlRequest?, clientException: CosXmlClientException?, serviceException: CosXmlServiceException?) {
                    MyToastUtil.showError(clientException?.message!!)
                    cosXmlService.release()
                    MyUtil.hideDialog(dialog)
                }

            })
        }
    }

    fun request(bgUrl: String) {
        lifecycleScope.launch(Dispatchers.IO) {
            try {
                val result = MyRetrofitClient.service.requestRoom(
                    roomId,
                    index2RoomType(selectItmIndex),
                    bgUrl,
                    this@CreateRoomActivity.et_room_name.text.toString(),
                    this@CreateRoomActivity.et_room_desc.text.toString(),
                    this@CreateRoomActivity.et_room_pwd.text.toString(),
                    changeType = changeType,
                )
                withContext(Dispatchers.Main) {
                    MyUtil.hideDialog(dialog)
                }
                when (result.code) {
                    200 -> {
                        withContext(Dispatchers.Main) {
                            NormalDialog(this@CreateRoomActivity).apply {
                                isCancelable = false
                                setTitle("提示")
                                setContent("申请已提交，我们会在24小时内进行人工审核，请耐心等待，待审核完成后才能进行下一次申请，请勿同时提交多个申请。")
                                setOnNormalOKClickListener(object : OnOKClickListener {
                                    override fun onclick() {
                                        <EMAIL>()
                                    }
                                })
                                showDialog()
                            }
                        }

                    }
                    70007 -> {
                        withContext(Dispatchers.Main) {
                            DialogUtil.showVIPDialog(this@CreateRoomActivity, null, result.msg, "CreateRoom")

//                            val intent = Intent(this@CreateRoomActivity, VIP2Activity::class.java)
//                            intent.putExtra(FROM_WHERE, "createRoom")
//                            startActivity(intent)
//                            MyToastUtil.showInfo(this@CreateRoomActivity, result.msg)
                        }
                    }
                    700071 -> {
                        withContext(Dispatchers.Main) {
//                            DialogUtil.showVIPDialog(this@CreateRoomActivity, null, result.msg, "CreateRoom")

//                            val intent = Intent(this@CreateRoomActivity, VIP2Activity::class.java)
//                            intent.putExtra(FROM_WHERE, "createRoom")
//                            startActivity(intent)
                            MyToastUtil.showInfo(result.msg)
                        }
                    }
                    70012 -> {
                        withContext(Dispatchers.Main) {
                            ActivityUtils.startActivity(Intent(this@CreateRoomActivity, UserInfoActivity::class.java))
                            MyToastUtil.showInfo(result.msg)
                        }
                    }
                    else -> {
                        withContext(Dispatchers.Main) {
                            MyToastUtil.showError(result.msg)
                        }
                    }
                }

            } catch (e: Exception) {
                MyToastUtil.showInfo(e.message)
            }
        }
    }

    private fun startCrop(uri: Uri) {

        val destinationFileName = "roomBgImage${System.currentTimeMillis()}.png"

        val options = UCrop.Options()
//        options.setCompressionFormat(Bitmap.CompressFormat.PNG)
        options.setHideBottomControls(true)
        options.setToolbarColor(resources.getColor(R.color.colorWhiteBackground))
        options.setStatusBarColor(resources.getColor(R.color.colorWhiteBackground))
//        options.withMaxResultSize(128, 128)
        options.setCompressionQuality(10)
        options.withAspectRatio(1080f, 1920f)
        options.setToolbarWidgetColor(resources.getColor(R.color.colorBlackBackground))
//        options.setCircleDimmedLayer(true)
        options.setToolbarCancelDrawable(R.drawable.ic_return)
        options.setShowCropGrid(false)
        options.setShowCropFrame(false)

        val uCrop = UCrop.of(uri, Uri.fromFile(File(cacheDir, destinationFileName)))
            .withOptions(options)

        uCrop.start(this)
    }

    override fun onActivityResult(requestCode: Int, resultCode: Int, data: Intent?) {
        super.onActivityResult(requestCode, resultCode, data)
        if (resultCode == RESULT_OK) {
            if (requestCode == requestPicCode) {
                val selectedUri = data!!.data
                if (selectedUri != null) {
                    startCrop(selectedUri)
                } else {
                    Toast.makeText(this, "未获取到图片，请确认已授予读写存储权限", Toast.LENGTH_LONG).show()
                }
            } else if (requestCode == UCrop.REQUEST_CROP) {
                val resultUri = UCrop.getOutput(data!!)
                if (resultUri != null) {
//                    uploadAvatar(resultUri)
                    Glide.with(this).load(resultUri)
                        //.transition(DrawableTransitionOptions.withCrossFade())
                        .diskCacheStrategy(DiskCacheStrategy.AUTOMATIC)
                        .into(iv_create_room_bg)
                    bgUri = resultUri
                } else {
                    Toast.makeText(this, "无法获取裁剪后图片", Toast.LENGTH_LONG).show()
                }
            }
        } else if (resultCode == UCrop.RESULT_ERROR) {
            val cropError = UCrop.getError(data!!)
            if (cropError != null) {
                Toast.makeText(this, cropError.message, Toast.LENGTH_LONG).show()
            } else {
                Toast.makeText(this, "未知错误", Toast.LENGTH_SHORT).show()
            }
        }
    }

    override fun onBackPressed() {
        goBack()
    }

    fun goBack() {
        if (bgUri != null || et_room_name.text.isNotEmpty() || et_room_desc.text.isNotEmpty()) {
            NormalDialog(this).apply {
                setTitle("警告")
                setContent("您有内容尚未保存，退出后将无法恢复，确定要退出吗？")
                setOnNormalOKClickListener(object : OnOKClickListener {
                    override fun onclick() {
                        finish()
                    }
                })
                setOnNormalCancelClickListener(object : OnCancelClickListener {
                    override fun onclick() {

                    }
                })
                showDialog()
            }
        } else {

            finish()
        }
    }
}