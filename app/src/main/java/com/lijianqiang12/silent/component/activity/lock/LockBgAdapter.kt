package com.lijianqiang12.silent.component.activity.lock

import android.view.View
import com.bumptech.glide.Glide
import com.bumptech.glide.load.engine.DiskCacheStrategy
import com.chad.library.adapter.base.BaseQuickAdapter
import com.chad.library.adapter.base.module.LoadMoreModule
import com.chad.library.adapter.base.viewholder.BaseViewHolder
import com.google.android.material.card.MaterialCardView
import com.lijianqiang12.silent.R
import com.lijianqiang12.silent.data.model.net.pojos.LockBg

class LockBgAdapter(layoutRes: Int, list: MutableList<LockBg>, private val currentImg: String)
    : BaseQuickAdapter<LockBg, BaseViewHolder>(layoutRes, list), LoadMoreModule {

    override fun convert(holder: BaseViewHolder, item: LockBg) {
        Glide.with(context).load(item.imgUrl)
            //.transition(DrawableTransitionOptions.withCrossFade())
            .diskCacheStrategy(DiskCacheStrategy.AUTOMATIC)
            .into(holder.getView(R.id.iv_item_lock_bg))

        if (currentImg == item.imgUrl) {
            holder.getView<MaterialCardView>(R.id.mcv_is_current_img).visibility = View.VISIBLE
        } else {
            holder.getView<MaterialCardView>(R.id.mcv_is_current_img).visibility = View.GONE
        }
    }
}