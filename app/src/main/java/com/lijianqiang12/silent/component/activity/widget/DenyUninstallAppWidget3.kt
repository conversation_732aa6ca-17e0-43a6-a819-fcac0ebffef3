package com.lijianqiang12.silent.component.activity.widget

import android.app.PendingIntent
import android.appwidget.AppWidgetManager
import android.appwidget.AppWidgetProvider
import android.content.ComponentName
import android.content.Context
import android.content.Intent
import android.widget.RemoteViews
import com.blankj.utilcode.util.AppUtils
import com.lijianqiang12.silent.utils.MMKVUtils
import com.lijianqiang12.silent.MyConstants
import com.lijianqiang12.silent.R
import com.lijianqiang12.silent.component.activity.IconSplashActivity
import java.text.SimpleDateFormat
import java.util.*


/**
 * Implementation of App Widget functionality.
 */
class DenyUninstallAppWidget3 : AppWidgetProvider() {
    override fun onUpdate(context: Context, appWidgetManager: AppWidgetManager, appWidgetIds: IntArray) {
//        for (appWidgetId in appWidgetIds) {
        updateAppWidget(context, appWidgetManager)
//        }
    }

    override fun onEnabled(context: Context) {
        // Enter relevant functionality for when the first widget is created
        MMKVUtils.put(MyConstants.SP_HOME_WIDGET_EXIST_3,true)
    }

    override fun onDisabled(context: Context) {
        // Enter relevant functionality for when the last widget is disabled
        MMKVUtils.put(MyConstants.SP_HOME_WIDGET_EXIST_3,false)
    }


    companion object {
        fun updateAppWidget(context: Context, appWidgetManager: AppWidgetManager) {

            var destinyName = ""
            var destinyTime = ""

            val calendar = Calendar.getInstance()
            val nextYear = calendar.get(Calendar.YEAR) + 1
            val destinyDate = MMKVUtils.getString(MyConstants.SP_KEY_SETTING_DESTINY_DATE, "01/01/${nextYear}")
            val fmt = SimpleDateFormat("MM/dd/yyyy")
            val date = fmt.parse(destinyDate)
            val restDats = (date.time - calendar.time.time + calendar.get(Calendar.HOUR_OF_DAY) * 1000 * 60 * 60 +
                    calendar.get(Calendar.MINUTE) * 1000 * 60 + calendar.get(Calendar.SECOND) * 1000 + calendar.get(Calendar.MILLISECOND)) / 1000 / 60 / 60 / 24
            when {
                restDats > 0 -> {
                    destinyName = "距离${MMKVUtils.getString(MyConstants.SP_KEY_SETTING_DESTINY_NAME, "$nextYear")}还剩"
                    destinyTime = "${restDats}"
                }
                restDats == 0L -> {
                    destinyName = "${MMKVUtils.getString(MyConstants.SP_KEY_SETTING_DESTINY_NAME, "$nextYear")}"
                    destinyTime = "today"
                }
                else -> {
                    destinyName = "${MMKVUtils.getString(MyConstants.SP_KEY_SETTING_DESTINY_NAME, "$nextYear")}已过去"
                    destinyTime = "${restDats * -1}"
                }
            }

            val views = RemoteViews(context.packageName, R.layout.deny_uninstall_app_widget3)
            views.setTextViewText(R.id.tv_widget_destiny_name, destinyName)
            views.setTextViewText(R.id.tv_widget_destiny_time, destinyTime)


            val intent = Intent(Intent.ACTION_MAIN)
            intent.component = ComponentName(AppUtils.getAppPackageName(), IconSplashActivity::class.java.canonicalName!!)

            val pendingIntent = PendingIntent.getActivity(context, 0, intent, PendingIntent.FLAG_IMMUTABLE)
            views.setOnClickPendingIntent(R.id.rl_widget3, pendingIntent)


            val appWidgetIds = appWidgetManager.getAppWidgetIds(ComponentName(context, DenyUninstallAppWidget3::class.java))
            for (appWidgetId in appWidgetIds) {
                appWidgetManager.updateAppWidget(appWidgetId, views)
            }

        }
    }

}


