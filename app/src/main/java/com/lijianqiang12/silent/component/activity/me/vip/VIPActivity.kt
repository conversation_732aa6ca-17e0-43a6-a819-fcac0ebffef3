package com.lijianqiang12.silent.component.activity.me.vip

import android.annotation.SuppressLint
import android.content.Intent
import android.os.Bundle
import android.view.Gravity
import androidx.lifecycle.Observer
import androidx.lifecycle.lifecycleScope
import com.afollestad.materialdialogs.MaterialDialog
import com.afollestad.materialdialogs.input.input
import com.lijianqiang12.silent.utils.MMKVUtils
import com.jeremyliao.liveeventbus.LiveEventBus
import com.lijianqiang12.silent.*
import com.lijianqiang12.silent.data.model.net.MyRetrofitClient
import com.lijianqiang12.silent.component.activity.TheLoginActivity
import com.lijianqiang12.silent.component.activity.base.BaseActivity
import com.lijianqiang12.silent.component.activity.custom.dialog.NormalDialog
import com.lijianqiang12.silent.component.activity.custom.dialog.OnCancelClickListener
import com.lijianqiang12.silent.component.activity.custom.dialog.OnOKClickListener
import com.lijianqiang12.silent.utils.MyToastUtil
import com.lijianqiang12.silent.utils.isLockRunning
import com.lijianqiang12.silent.utils.logoutAll
import kotlinx.android.synthetic.main.activity_vip.*
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.launch
import kotlinx.coroutines.withContext

class VIPActivity : BaseActivity() {


    @SuppressLint("CheckResult")
    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        setContentView(R.layout.activity_vip)

//        val vipBottomSheetFragment = VIPBottomSheetDialogFragment.newInstance()
        tv_miss_vip.setOnClickListener {
            NormalDialog(this@VIPActivity).apply {
                setTitle("温馨提示")
                setContent("VIP与账号绑定，几乎不会丢失，丢失的原因99%都是用户登录错账号，请您尝试使用其他登录方式，查看VIP是否存在，若四种登录方式都已尝试，仍无VIP，请回忆一下是否这期间更换过手机号或QQ、微信号，若依然没有，请到“问题反馈中”按照里面的联系方式联系官方处理。")
                setGravity(Gravity.START)
                setOnNormalOKClickListener("重新登录", object : OnOKClickListener {
                    override fun onclick() {
                        if (isLockRunning()) {
                            MyToastUtil.showInfo("锁机结束后才可退出登录")
                        } else {
                            <EMAIL>(Dispatchers.Main) {
                                withContext(Dispatchers.IO) {
                                    logoutAll()
                                }
                                <EMAIL>(Intent(this@VIPActivity, TheLoginActivity::class.java))
//                                LiveEventBus.get(LiveBus.CLOSE_MAIN_ACTIVITY, String::class.java).post("")
                                <EMAIL>()
                            }
                        }
                    }
                })
                setOnNormalCancelClickListener("联系官方", object : OnCancelClickListener {
                    override fun onclick() {
                        NormalDialog(this@VIPActivity).apply {
                            setTitle("联系方式")
                            setContent("微信：yuanlishoujiAPP")
                            setOnNormalOKClickListener(object : OnOKClickListener {
                                override fun onclick() {
                                }
                            })
                            showDialog()
                        }
                    }
                })
                showDialog()
            }
        }

        btn_vip_open.setOnClickListener {
            if (MMKVUtils.getInt(MyConstants.SP_KEY_VIP_STATE, VIP_STATE_FREE) == VIP_STATE_FOREVER) {
                MyToastUtil.showInfo("您已经是尊贵的永久版用户，无需重复开通")
            } else {
                val vipBottomSheetFragment = VIPBottomSheetDialogFragment.newInstance()
                vipBottomSheetFragment.show(supportFragmentManager, "VIPBottomSheetDialogFragment")
            }
        }

        iv_return_vip.setOnClickListener {
            finish()
        }

        tv_duihuanma.setOnClickListener {
            when (MMKVUtils.getInt(MyConstants.SP_KEY_VIP_STATE, VIP_STATE_FREE)) {
                VIP_STATE_FREE, VIP_STATE_NORMAL -> {
                    MaterialDialog(this)
                            .cornerRadius(8.0f)
                            .title(text = "礼品码兑换")
                            .input(hint = "请输入兑换码") { dialog, input ->
                                lifecycleScope.launch(Dispatchers.IO) {
                                    try {
                                        val result = MyRetrofitClient.service.duihuanma(input.toString())
                                        if (result.code == 200) {
                                            withContext(Dispatchers.Main) {
                                                MyToastUtil.showInfo("兑换成功")
                                            }
                                            val result2 = MyRetrofitClient.service.refreshState()
                                            withContext(Dispatchers.Main) {
                                                if (result2.code == 200) {
                                                    MMKVUtils.put(MyConstants.SP_KEY_USERNAME, result2.data!!.username)
                                                    MMKVUtils.put(MyConstants.SP_KEY_VIP_STATE, result2.data.vipState)
                                                    MMKVUtils.put(MyConstants.SP_KEY_VIP_END_TIME, result2.data.vipEndTime)
                                                    MMKVUtils.put(MyConstants.SP_KEY_AVATAR, result2.data.avatar)
                                                    MMKVUtils.put(MyConstants.SP_KEY_FORCE_QUITE_PWD, result2.data.unlockPwd)
                                                    MMKVUtils.put(MyConstants.SP_KEY_FORCE_QUITE_PWD, result2.data.unlockPwd)
                                                    MMKVUtils.put(MyConstants.SP_KEY_BIND_MOBILE, result2.data.bindMobile)
                                                } else {
                                                    MyToastUtil.showError(result.msg)
                                                }
                                            }
                                        } else {
                                            withContext(Dispatchers.Main) {
                                                MyToastUtil.showInfo(result.msg)
                                            }
                                        }
                                    } catch (e: Exception) {
                                        withContext(Dispatchers.Main) {
                                            MyToastUtil.showInfo(e.message)
                                        }
                                    }
                                }
                            }.positiveButton(text = "兑换").negativeButton(text = "取消").show()
                }
                VIP_STATE_FOREVER -> MyToastUtil.showInfo("您已经是尊贵的永久版用户，无需重复开通")
            }

        }

        LiveEventBus.get(LiveBus.PAY_FOR_VIP_FINISH, String::class.java).observe(this, Observer {
            refreshVipPage()
        })

    }


    override fun onResume() {
        super.onResume()
        refreshVipPage()
    }

    private fun refreshVipPage() {
        when (MMKVUtils.getInt(MyConstants.SP_KEY_VIP_STATE, VIP_STATE_FREE)) {
            VIP_STATE_FREE -> btn_vip_open.text = "立即开通"
            VIP_STATE_NORMAL -> btn_vip_open.text = "立即续费"
            VIP_STATE_FOREVER -> btn_vip_open.text = "已开通"
        }
    }

}
