package com.lijianqiang12.silent.component.activity.analyze.todaytrend

import android.view.View
import android.widget.TextView
import com.bumptech.glide.Glide
import com.bumptech.glide.load.engine.DiskCacheStrategy
import com.chad.library.adapter.base.BaseQuickAdapter
import com.chad.library.adapter.base.module.LoadMoreModule
import com.chad.library.adapter.base.viewholder.BaseViewHolder
import com.lijianqiang12.silent.R
import com.lijianqiang12.silent.data.model.net.pojos.OffTimeDetail
import com.lijianqiang12.silent.utils.TimeUtil

class TodayLockTrendAdapter( layoutRes: Int, item: MutableList<OffTimeDetail>)
    : BaseQuickAdapter<OffTimeDetail, BaseViewHolder>(layoutRes, item) , LoadMoreModule {

    override fun convert(viewHolder: BaseViewHolder, item: OffTimeDetail) {
        viewHolder.getView<TextView>(R.id.tv_analyze_today_name).text = item.username
        if (item.length < 60) {
            viewHolder.getView<TextView>(R.id.tv_analyze_today_time).text = "小于1分钟"
        } else {
            viewHolder.getView<TextView>(R.id.tv_analyze_today_time).text = TimeUtil.formatHHMM(item.length / 60)
        }
        viewHolder.getView<TextView>(R.id.tv_time_brand_today).text = item.brand
        viewHolder.getView<TextView>(R.id.tv_trend).text = "${viewHolder.getBindingAdapterPosition() + 1}."

        Glide.with(context).load(item.avatar)
            //.transition(DrawableTransitionOptions.withCrossFade())
            .diskCacheStrategy(DiskCacheStrategy.AUTOMATIC)
            .into(viewHolder.getView(R.id.civ_item_analyze_today))
        when (item.vipState) {
            0 -> {
                viewHolder.getView<TextView>(R.id.tv_time_vip_today).visibility = View.INVISIBLE
            }
            1 -> {
                viewHolder.getView<TextView>(R.id.tv_time_vip_today).visibility = View.VISIBLE
                viewHolder.getView<TextView>(R.id.tv_time_vip_today).text = "VIP"
                viewHolder.getView<TextView>(R.id.tv_time_vip_today).setTextColor(context.resources.getColor(R.color.colorBackText))
                viewHolder.getView<TextView>(R.id.tv_time_vip_today).setBackgroundResource(R.drawable.shape_get_vip_gradient_2dp)
            }
            2 -> {
                viewHolder.getView<TextView>(R.id.tv_time_vip_today).visibility = View.VISIBLE
                viewHolder.getView<TextView>(R.id.tv_time_vip_today).text = "SVIP"
                viewHolder.getView<TextView>(R.id.tv_time_vip_today).setTextColor(context.resources.getColor(R.color.colorWhiteText))
                viewHolder.getView<TextView>(R.id.tv_time_vip_today).setBackgroundResource(R.drawable.shape_get_vip_black_gradient_2dp)
            }
        }
    }
}