package com.lijianqiang12.silent.component.service.windows

import android.animation.ObjectAnimator
import android.annotation.SuppressLint
import android.content.Context
import android.graphics.PixelFormat
import android.graphics.drawable.Drawable
import android.os.Build
import android.os.Handler
import android.view.*
import android.widget.ImageView
import android.widget.TextView
import com.blankj.utilcode.util.ConvertUtils
import com.blankj.utilcode.util.LogUtils
import com.blankj.utilcode.util.ScreenUtils
import com.lijianqiang12.silent.R
import com.lijianqiang12.silent.utils.MyToastUtil
import com.lijianqiang12.silent.utils.MyWindowUtil

class FloatWindowOfAppMonitor(val context: Context, val index: Int) {

    //    private var windowCreated = false
//    private var windowShowing = false
    private lateinit var params: WindowManager.LayoutParams
    private lateinit var windowManager: WindowManager
    private var layout: View? = null
    private var isMoving = false

    @SuppressLint("ClickableViewAccessibility")
    private fun createRestView() {
        params = WindowManager.LayoutParams()

        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
            params.type = WindowManager.LayoutParams.TYPE_APPLICATION_OVERLAY
        } else {
            params.type = WindowManager.LayoutParams.TYPE_SYSTEM_ALERT
        }

        windowManager = context.applicationContext.getSystemService(Context.WINDOW_SERVICE) as WindowManager

        params.format = PixelFormat.TRANSLUCENT

        params.flags = params.flags or WindowManager.LayoutParams.FLAG_NOT_FOCUSABLE or
                WindowManager.LayoutParams.FLAG_NOT_TOUCH_MODAL or
                WindowManager.LayoutParams.FLAG_HARDWARE_ACCELERATED
        params.gravity = Gravity.START or Gravity.TOP
        params.x = 0
        params.y = ConvertUtils.dp2px(40f + index * 40)//ScreenUtils.getScreenHeight() / 8
        params.width = WindowManager.LayoutParams.WRAP_CONTENT
        params.height = WindowManager.LayoutParams.WRAP_CONTENT

        layout = LayoutInflater.from(context).inflate(R.layout.layout_lock_view_monitor, null)
        layout?.let {
            var lastX = 0f
            var lastY = 0f
            it.setOnTouchListener { v, event ->

                when (event.action) {
                    MotionEvent.ACTION_DOWN -> {
                        it.setBackgroundResource(R.drawable.shape_lock_view_rest)
                        lastX = event.rawX
                        lastY = event.rawY
                        isMoving = true
                    }

                    MotionEvent.ACTION_MOVE -> {
                        params.x += (event.rawX - lastX).toInt()
                        params.y += (event.rawY - lastY).toInt()

                        LogUtils.d("ACTION_MOVE, params.x=" + params.x + ", params.x=" + params.x + ",rawX=" + event.rawX + ",rawY=" + event.rawY + ",lastX=" + lastX + ",lastY=" + lastY)

                        windowManager.updateViewLayout(it, params)
                        lastX = event.rawX
                        lastY = event.rawY

                    }

                    MotionEvent.ACTION_UP -> {
                        if (it.width / 2 - event.x + event.rawX < ScreenUtils.getScreenWidth() / 2) {//left
                            params.x = 0
                            it.setBackgroundResource(R.drawable.shape_lock_view_rest_left)
                        } else {
                            params.x = ScreenUtils.getScreenWidth() - it.width
                            it.setBackgroundResource(R.drawable.shape_lock_view_rest_right)
                        }
                        windowManager.updateViewLayout(it, params)
                        isMoving = false
                    }
                }
                false

            }
        }
//        windowCreated = true
    }

    fun showWindow(text: String, icon: Drawable) {
        showWindow(text, icon, false)
    }

    fun hideWindow() {
        hideWindow(false)
    }

    fun showWindow(text: String, icon: Drawable, animate: Boolean) {
        //是否显示app限时提醒

        if (layout == null) {
            createRestView()
        }
        if (!isMoving) {
            layout?.let {
                it.findViewById<TextView>(R.id.tv_lock_view_monitor).text = text
                it.findViewById<ImageView>(R.id.civ_monitor_app_icon).setImageDrawable(icon)
            }

        }
        if (!MyWindowUtil.isWindowShowing(layout!!)) {
//            windowShowing = true

            try {
                windowManager.addView(layout!!, params)

            } catch (e: Exception) {
                MyToastUtil.showError("MonitorFloatWindow未成功获取到悬浮窗权限，请到系统设置或安全管家中重新授予，若已经授予，则可能是系统BUG，请重启手机")
//                windowShowing = false

            }
            if (animate) {
                ObjectAnimator.ofFloat(layout!!, "translationX", ScreenUtils.getScreenWidth() * -1f, 0f)
                    .setDuration(2000)
                    .start()

            }
        }
    }

    fun hideWindow(animate: Boolean) {
        if (layout != null) {
            if (MyWindowUtil.isWindowShowing(layout!!)) {
                try {
                    if (animate) {
                        ObjectAnimator.ofFloat(layout!!, "translationX", 0f, ScreenUtils.getScreenWidth() * -1f)
                            .setDuration(2000)
                            .start()
                        Handler().postDelayed(kotlinx.coroutines.Runnable {

                            try {
                                windowManager.removeView(layout!!)
                            } catch (e: Exception) {

                            }

//                            windowShowing = false
                        }, 2500)
                    } else {
                        windowManager.removeView(layout!!)
//                        windowShowing = false
                    }
                } catch (e: Exception) {
                    MyToastUtil.showError("未成功获取到悬浮窗权限，请到系统设置或安全管家中重新授予，若已经授予，则可能是系统BUG，请重启手机")
                }
            }
        }
    }

//    fun showAndHide(text: String, icon: Drawable) {
//        if (windowShowing) return
//        showWindow(text, icon, true)
//        Handler().postDelayed(kotlinx.coroutines.Runnable {
//            hideWindow(true)
//        }, 8000)
//    }
}