package com.lijianqiang12.silent.component.activity

//import com.yl.lib.sentry.hook.PrivacySentry
//import com.yl.lib.sentry.hook.PrivacySentry
import android.os.Bundle
import android.text.SpannableStringBuilder
import android.text.Spanned
import android.text.method.LinkMovementMethod
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import com.blankj.utilcode.util.AppUtils
import com.google.android.material.bottomsheet.BottomSheetBehavior
import com.lijianqiang12.silent.MyConstants
import com.lijianqiang12.silent.R
import com.lijianqiang12.silent.component.activity.base.BaseBottomSheetDialogFragment
import com.lijianqiang12.silent.utils.MMKVUtils
import com.yl.lib.sentry.hook.PrivacySentry
import kotlinx.android.synthetic.main.dialog_privacy.view.btn_privacy_agree
import kotlinx.android.synthetic.main.dialog_privacy.view.btn_privacy_disagree
import kotlinx.android.synthetic.main.dialog_privacy.view.tv_content_privacy


class PrivacyBottomSheetDialogFragment() : BaseBottomSheetDialogFragment() {

    private val TAG = "PrivacyBottomSheetDialogFragment"
    private var onOKSelectListener: OnOKSelectListener? = null
    private var onCancelSelectListener: OnCancelSelectListener? = null
    private lateinit var mBehavior: BottomSheetBehavior<View>
    private lateinit var customView: View


    companion object {
        @JvmStatic
        fun newInstance() = PrivacyBottomSheetDialogFragment().apply {}
    }

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
    }

    override fun onCreateView(inflater: LayoutInflater, container: ViewGroup?, savedInstanceState: Bundle?): View {
        customView = View.inflate(requireContext(), R.layout.dialog_privacy, null)
        return customView
    }

    override fun onStart() {
        super.onStart()
        val parentView = customView.parent as View
        parentView.setBackgroundColor(resources.getColor(R.color.colorTranslate))

        //设置父窗口为屏幕高度
//        val layoutParams = parentView.layoutParams
//        layoutParams.height = (requireActivity() as AppCompatActivity).findViewById<ViewGroup>(android.R.id.content).height - dpToPixel(
//            MMKVUtils.getFloat(MyConstants.SP_KEY_STATUS_BAR_HEIGHT, 32f)
//        ).toInt()
//
//        //设置初始状态为填充满
//        mBehavior = BottomSheetBehavior.from(parentView)
//        mBehavior.state = BottomSheetBehavior.STATE_EXPANDED
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)

        val tvContent = customView.tv_content_privacy
        val str =
            "亲爱的用户，欢迎您信任并使用${AppUtils.getAppName()}。\n\n我们非常重视您的个人信息和隐私保护。为了更好的保障您的个人权益，在使用我们的服务前，请务必打开链接并审慎阅读《用户协议》" +
                    "和《隐私政策》的全部内容，同意并接受全部条款后方可使用我们的服务。"

        val stringBuilder = SpannableStringBuilder(str)
        val span1 = TheLoginActivity.TextViewSpan1(requireContext())
        stringBuilder.setSpan(span1, 75, 81, Spanned.SPAN_EXCLUSIVE_EXCLUSIVE)

        val span2 = TheLoginActivity.TextViewSpan2(requireContext())
        stringBuilder.setSpan(span2, 82, 88, Spanned.SPAN_EXCLUSIVE_EXCLUSIVE)
        tvContent.text = stringBuilder
        //一定要记得设置这个方法  不是不起作用
        tvContent.movementMethod = LinkMovementMethod.getInstance()

        customView.btn_privacy_agree.setOnClickListener {
            PrivacySentry.Privacy.updatePrivacyShow()
            MMKVUtils.put(MyConstants.SP_AGREE_PRIVACY, true)
            this.dismiss()
            onOKSelectListener?.onSelect()
        }

        customView.btn_privacy_disagree.setOnClickListener {
            this.dismiss()
            onCancelSelectListener?.onSelect()
        }
    }

    fun onBackPressed() {
        // 不调用 super.onBackPressed(); 来禁止响应后退键
    }


    fun setOnOKSelectListener(listener: OnOKSelectListener) {
        this.onOKSelectListener = listener
    }

    fun setOnCancelSelectListener(listener: OnCancelSelectListener) {
        this.onCancelSelectListener = listener
    }

    interface OnOKSelectListener {
        fun onSelect()
    }

    interface OnCancelSelectListener {
        fun onSelect()
    }
}