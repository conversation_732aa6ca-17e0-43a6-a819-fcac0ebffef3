package com.lijianqiang12.silent.component.activity.custom.dialog

import android.graphics.Color
import android.graphics.drawable.ColorDrawable
import android.os.Bundle
import android.util.DisplayMetrics
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.view.WindowManager
import androidx.fragment.app.Fragment
import com.lijianqiang12.silent.DIALOG_WIDTH_PERCENT_MIDDLE
import com.lijianqiang12.silent.R
import com.lijianqiang12.silent.component.activity.base.BaseDialogFragment
import com.lijianqiang12.silent.data.model.db.DayLimit
import kotlinx.android.synthetic.main.dialog_home_day_limit_time.view.*
import kotlinx.android.synthetic.main.dialog_home_white_app_time.view.tpv_lock_fast_hour
import kotlinx.android.synthetic.main.dialog_home_white_app_time.view.tpv_lock_fast_minute
import kotlinx.android.synthetic.main.dialog_home_white_app_time.view.tv_dialog_home_all_time_cancel
import kotlinx.android.synthetic.main.dialog_home_white_app_time.view.tv_dialog_home_all_time_content
import kotlinx.android.synthetic.main.dialog_home_white_app_time.view.tv_dialog_home_all_time_ok
import kotlinx.android.synthetic.main.dialog_home_white_app_time.view.tv_dialog_home_all_time_title


class DayMonitorSelectTimeDialog() : BaseDialogFragment() {

    constructor(fragment: Fragment) : this() {
        this.fragment = fragment
    }

    private var okListener: OnTimeLimitListener? = null
    private var cancelListener: OnCancelClickListener? = null
    private lateinit var v: View
    private var fragment: Fragment? = null

    private var title = ""
    private var content = ""
    private var timeLimitHour = 23
    private var timeLimitMinute = 59
    private var isIncludeWhite = false
    private var isDenyChange = false
    private var denyChangeLength = 20
    private var monday = false
    private var tuesday = false
    private var wednesday = false
    private var thursday = false
    private var friday = false
    private var saturday = false
    private var sunday = false
    private lateinit var dayLimit: DayLimit


    override fun onCreateView(inflater: LayoutInflater, container: ViewGroup?, savedInstanceState: Bundle?): View {
        dialog!!.window!!.setBackgroundDrawable(ColorDrawable(Color.TRANSPARENT))
//        if (savedInstanceState != null) {
//            title = savedInstanceState.get("title") as String
//            content = savedInstanceState.get("content") as String
//            timeLimitHour = savedInstanceState.get("timeLimitHour") as Int
//            timeLimitMinute = savedInstanceState.get("timeLimitMinute") as Int
//        }
        v = inflater.inflate(R.layout.dialog_home_day_limit_time, container, false)

        if (title.isNotEmpty()) {
            v.tv_dialog_home_all_time_title.text = title
        }
        if (content.isNotEmpty()) {
            v.tv_dialog_home_all_time_content.text = content
        }



        if (monday) {
            v.tv_edit_dialog_week_select_1.setBackgroundResource(R.drawable.bg_week_select_thin)
        } else {
            v.tv_edit_dialog_week_select_1.setBackgroundResource(R.drawable.bg_week_not_select_thin)
        }
        if (tuesday) {
            v.tv_edit_dialog_week_select_2.setBackgroundResource(R.drawable.bg_week_select_thin)
        } else {
            v.tv_edit_dialog_week_select_2.setBackgroundResource(R.drawable.bg_week_not_select_thin)
        }
        if (wednesday) {
            v.tv_edit_dialog_week_select_3.setBackgroundResource(R.drawable.bg_week_select_thin)
        } else {
            v.tv_edit_dialog_week_select_3.setBackgroundResource(R.drawable.bg_week_not_select_thin)
        }
        if (thursday) {
            v.tv_edit_dialog_week_select_4.setBackgroundResource(R.drawable.bg_week_select_thin)
        } else {
            v.tv_edit_dialog_week_select_4.setBackgroundResource(R.drawable.bg_week_not_select_thin)
        }
        if (friday) {
            v.tv_edit_dialog_week_select_5.setBackgroundResource(R.drawable.bg_week_select_thin)
        } else {
            v.tv_edit_dialog_week_select_5.setBackgroundResource(R.drawable.bg_week_not_select_thin)
        }
        if (saturday) {
            v.tv_edit_dialog_week_select_6.setBackgroundResource(R.drawable.bg_week_select_thin)
        } else {
            v.tv_edit_dialog_week_select_6.setBackgroundResource(R.drawable.bg_week_not_select_thin)
        }
        if (sunday) {
            v.tv_edit_dialog_week_select_7.setBackgroundResource(R.drawable.bg_week_select_thin)
        } else {
            v.tv_edit_dialog_week_select_7.setBackgroundResource(R.drawable.bg_week_not_select_thin)
        }

        v.tv_edit_dialog_week_select_1.setOnClickListener {
            monday = !monday
            if (monday) {
                v.tv_edit_dialog_week_select_1.setBackgroundResource(R.drawable.bg_week_select_thin)
            } else {
                v.tv_edit_dialog_week_select_1.setBackgroundResource(R.drawable.bg_week_not_select_thin)
            }
        }
        v.tv_edit_dialog_week_select_2.setOnClickListener {
            tuesday = !tuesday
            if (tuesday) {
                v.tv_edit_dialog_week_select_2.setBackgroundResource(R.drawable.bg_week_select_thin)
            } else {
                v.tv_edit_dialog_week_select_2.setBackgroundResource(R.drawable.bg_week_not_select_thin)
            }
        }
        v.tv_edit_dialog_week_select_3.setOnClickListener {
            wednesday = !wednesday
            if (wednesday) {
                v.tv_edit_dialog_week_select_3.setBackgroundResource(R.drawable.bg_week_select_thin)
            } else {
                v.tv_edit_dialog_week_select_3.setBackgroundResource(R.drawable.bg_week_not_select_thin)
            }
        }
        v.tv_edit_dialog_week_select_4.setOnClickListener {
            thursday = !thursday
            if (thursday) {
                v.tv_edit_dialog_week_select_4.setBackgroundResource(R.drawable.bg_week_select_thin)
            } else {
                v.tv_edit_dialog_week_select_4.setBackgroundResource(R.drawable.bg_week_not_select_thin)
            }
        }
        v.tv_edit_dialog_week_select_5.setOnClickListener {
            friday = !friday
            if (friday) {
                v.tv_edit_dialog_week_select_5.setBackgroundResource(R.drawable.bg_week_select_thin)
            } else {
                v.tv_edit_dialog_week_select_5.setBackgroundResource(R.drawable.bg_week_not_select_thin)
            }
        }
        v.tv_edit_dialog_week_select_6.setOnClickListener {
            saturday = !saturday
            if (saturday) {
                v.tv_edit_dialog_week_select_6.setBackgroundResource(R.drawable.bg_week_select_thin)
            } else {
                v.tv_edit_dialog_week_select_6.setBackgroundResource(R.drawable.bg_week_not_select_thin)
            }
        }
        v.tv_edit_dialog_week_select_7.setOnClickListener {
            sunday = !sunday
            if (sunday) {
                v.tv_edit_dialog_week_select_7.setBackgroundResource(R.drawable.bg_week_select_thin)
            } else {
                v.tv_edit_dialog_week_select_7.setBackgroundResource(R.drawable.bg_week_not_select_thin)
            }
        }

        val dataHour: MutableList<String> = mutableListOf()
        for (i in 0..23) {
            if (i < 10) {
                dataHour.add("0$i")
            } else {
                dataHour.add("$i")
            }
        }

        val dataMinute: MutableList<String> = mutableListOf()
        for (i in 0..59) {
            if (i < 10) {
                dataMinute.add("0$i")
            } else {
                dataMinute.add("$i")
            }
        }

        v.tpv_lock_fast_hour.setData(dataHour)
        v.tpv_lock_fast_minute.setData(dataMinute)



        v.tpv_lock_fast_hour.setSelected(timeLimitHour)
        v.tpv_lock_fast_minute.setSelected(timeLimitMinute)
        v.tpv_lock_fast_hour.setOnSelectListener { text -> timeLimitHour = text!!.toInt() }
        v.tpv_lock_fast_minute.setOnSelectListener { text -> timeLimitMinute = text!!.toInt() }

        v.cb_monitor_if_include_white.isChecked = isIncludeWhite
        v.cb_monitor_if_include_white.setOnCheckedChangeListener { buttonView, isChecked ->
            isIncludeWhite = isChecked
        }

        v.cb_monitor_if_deny_change.isChecked = isDenyChange
        v.cb_monitor_if_deny_change.setOnCheckedChangeListener { buttonView, isChecked ->
            isDenyChange = isChecked
        }

        v.tv_monitor_deny_length.text = "${denyChangeLength}分钟"
        v.tv_monitor_deny_length.setOnClickListener {
            val bottomDialog = BottomSingleSelectDialogFragment.newInstance()
            bottomDialog.setShowList(arrayListOf("5分钟", "10分钟", "20分钟", "30分钟", "45分钟", "60分钟", "120分钟", "180分钟", "240分钟", "300分钟"))
            bottomDialog.setValueList(arrayListOf(5, 10, 20, 30, 45, 60, 120, 180, 240, 300))
            bottomDialog.setOnValueSelectListener(object : BottomSingleSelectDialogFragment.OnValueSelectListener {
                override fun onSelect(value: Long, show: String) {
                    denyChangeLength = value.toInt()
                    v.tv_monitor_deny_length.text = show
                }
            })
            bottomDialog.show(requireFragmentManager(), "")
        }


        v.tv_dialog_home_all_time_ok.setOnClickListener {
            okListener?.apply {
                dayLimit.allDayLimit = (timeLimitHour * 60 + timeLimitMinute) * 60L
                dayLimit.isDenyChange = isDenyChange
                dayLimit.denyChangeLength = denyChangeLength
                dayLimit.isIncludeWhite = isIncludeWhite
                dayLimit.monday = monday
                dayLimit.tuesday = tuesday
                dayLimit.wednesday = wednesday
                dayLimit.thursday = thursday
                dayLimit.friday = friday
                dayLimit.saturday = saturday
                dayLimit.sunday = sunday
                onclick(dayLimit)
            }
            <EMAIL>()
        }
        v.tv_dialog_home_all_time_cancel.setOnClickListener {
            cancelListener?.apply {
                onclick()
            }
            <EMAIL>()
        }
        return v
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)


    }

    fun setTitle(arg: String) {
        title = arg
    }

    fun setContent(arg: String) {
        content = arg
    }

    fun setDayLimit(dayLimit: DayLimit) {
        this.dayLimit = dayLimit.copy()
        val temp = if (dayLimit.allDayLimit == -1L) {
            480
        } else if (dayLimit.allDayLimit / 60 > 60 * 24 - 1) {
            60 * 24 - 1
        } else {
            dayLimit.allDayLimit.toInt() / 60
        }
        timeLimitHour = temp / 60
        timeLimitMinute = temp % 60
        isDenyChange = dayLimit.isDenyChange
        denyChangeLength = dayLimit.denyChangeLength
        isIncludeWhite = dayLimit.isIncludeWhite
        monday = dayLimit.monday
        tuesday = dayLimit.tuesday
        wednesday = dayLimit.wednesday
        thursday = dayLimit.thursday
        friday = dayLimit.friday
        saturday = dayLimit.saturday
        sunday = dayLimit.sunday
    }


    fun show() {
        super.show(fragment!!.requireFragmentManager(), "NormalDialog")
    }

    override fun onStart() {
        val params = dialog!!.window!!.attributes
        val dm: DisplayMetrics = resources.displayMetrics
//        val density = dm.density
        val width = dm.widthPixels
//        val height = dm.heightPixels
        params.width = (width * DIALOG_WIDTH_PERCENT_MIDDLE).toInt()//ViewGroup.LayoutParams.MATCH_PARENT
//        params.width = width.coerceAtMost(height) * 3 / 4//ViewGroup.LayoutParams.MATCH_PARENT
        dialog!!.window!!.attributes = params as WindowManager.LayoutParams
        super.onStart()
    }

    fun setOnOKClickListener(okListener: OnTimeLimitListener) {
        this.okListener = okListener
    }

    fun setOnCancelClickListener(cancelListener: OnCancelClickListener) {
        this.cancelListener = cancelListener
    }

    interface OnTimeLimitListener {
        fun onclick(dayLimit: DayLimit)
    }

    override fun onSaveInstanceState(outState: Bundle) {
        super.onSaveInstanceState(outState)
        outState.putString("title", title)
        outState.putString("content", content)
        outState.putInt("timeLimitHour", timeLimitHour)
        outState.putInt("timeLimitMinute", timeLimitMinute)
        outState.putBoolean("isDenyChange", isDenyChange)
        outState.putBoolean("isIncludeWhite", isIncludeWhite)
        outState.putBoolean("monday", monday)
        outState.putBoolean("tuesday", tuesday)
        outState.putBoolean("wednesday", wednesday)
        outState.putBoolean("thursday", thursday)
        outState.putBoolean("friday", friday)
        outState.putBoolean("saturday", saturday)
        outState.putBoolean("sunday", sunday)
    }

}