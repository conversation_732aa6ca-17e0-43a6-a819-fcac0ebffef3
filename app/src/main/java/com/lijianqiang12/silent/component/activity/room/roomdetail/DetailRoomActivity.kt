package com.lijianqiang12.silent.component.activity.room.roomdetail

import android.content.Intent
import android.os.Bundle
import android.view.View
import androidx.lifecycle.lifecycleScope
import co.mobiwise.materialintro.shape.Focus
import co.mobiwise.materialintro.shape.FocusGravity
import co.mobiwise.materialintro.shape.ShapeType
import co.mobiwise.materialintro.view.MaterialIntroView
import com.blankj.utilcode.util.AppUtils
import com.lijianqiang12.silent.utils.MMKVUtils
import com.bumptech.glide.Glide
import com.bumptech.glide.load.engine.DiskCacheStrategy
import com.google.android.material.appbar.AppBarLayout.OnOffsetChangedListener
import com.google.android.material.tabs.TabLayoutMediator
import com.lijianqiang12.silent.MyConstants
import com.lijianqiang12.silent.R
import com.lijianqiang12.silent.data.model.net.MyRetrofitClient
import com.lijianqiang12.silent.component.activity.base.BaseActivity
import com.lijianqiang12.silent.component.activity.custom.dialog.*
import com.lijianqiang12.silent.component.activity.room.createroom.CreateRoomActivity
import com.lijianqiang12.silent.component.activity.me.userinfo.UserInfoActivity
import com.lijianqiang12.silent.utils.MyToastUtil
import com.lijianqiang12.silent.utils.secondToHmSimpleSimple
import kotlinx.android.synthetic.main.activity_detail_room.*
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.launch
import kotlinx.coroutines.withContext
import kotlin.math.abs

var currentRoomId = -1L
var ifMine = false
var pwd: String = ""

class DetailRoomActivity : BaseActivity(false) {

    private lateinit var progressDialog: MyProgressDialog
    private var theRoomCode = ""

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        setContentView(R.layout.activity_detail_room)
        initScroll()
        setSupportActionBar(toolbar)
        progressDialog = MyProgressDialog(activity = this)
        currentRoomId = intent.getLongExtra("roomId", -1)

        pwd = intent.getStringExtra("pwd") ?: ""
        toolbar.setNavigationOnClickListener {
            finish()
        }


        if (MMKVUtils.getBoolean(MyConstants.SP_KEY_CONFIG_SHOW_BOARD, false)) {
            tv_write_board.visibility = View.VISIBLE

            tv_write_board.setOnClickListener {
                if (!MMKVUtils.getBoolean(MyConstants.SP_KEY_BIND_MOBILE, false)) {
                    NormalDialog(this).apply {
                        setTitle("温馨提示")
                        isCancelable = true
                        setContent("根据相关法律要求，您需要先绑定手机号才能完成该操作。")
                        setOnNormalOKClickListener("去绑定", object : OnOKClickListener {
                            override fun onclick() {
                                val intent = Intent(this@DetailRoomActivity, UserInfoActivity::class.java)
                                startActivity(intent)
                            }
                        })
                        setOnNormalCancelClickListener("下次吧", object : OnCancelClickListener {
                            override fun onclick() {
                            }
                        })
                        showDialog()
                    }
                    return@setOnClickListener
                }

                EditTextDialog(this).run {
                    isCancelable = false
                    setTitle("请输入留言")
                    setOnOKListener(object : EditTextDialog.OnOKListener {
                        override fun onclick(text: String) {
                            if (text.length >= 5) {
                                progressDialog.show()
                                <EMAIL>(Dispatchers.IO) {
                                    try {
                                        val result = MyRetrofitClient.service.postBoard(currentRoomId, text)
                                        withContext(Dispatchers.Main) {
                                            if (result.code == 60002) {

                                                NormalDialog(this@DetailRoomActivity).apply {
                                                    setTitle("警告")
                                                    isCancelable = false
                                                    setContent(result.msg)
                                                    setOnNormalOKClickListener("我知道了", object : OnOKClickListener {
                                                        override fun onclick() {

                                                        }
                                                    })

                                                    showDialog()
                                                }


                                            } else {
                                                MyToastUtil.showInfo(result.msg)
                                            }

                                            progressDialog.dismiss()
                                        }
                                    } catch (e: Exception) {
                                        withContext(Dispatchers.Main) {
                                            MyToastUtil.showInfo(e.message)
                                            progressDialog.dismiss()
                                        }
                                    } finally {
                                    }
                                }
                            } else {
                                MyToastUtil.showInfo("请至少输入5个字符")
                            }

                        }
                    })

                    show()
                }
            }
        } else {

            tv_write_board.visibility = View.GONE
        }


        vp_room_detail.adapter = RoomDetailViewPagerAdapter(this)
        TabLayoutMediator(toolbar_tab, vp_room_detail) { tab, position ->
            tab.text = when (position) {
                0 -> "简介"
                1 -> "打卡"
                2 -> "成员"
                3 -> "留言板"
                else -> "Error"
            }

        }.attach()

        iv_share.setOnClickListener {
            if (theRoomCode.isEmpty()) {
                MyToastUtil.showInfo("为获取到房间ID，重新进入本页面")
            } else {
                val sharedIntent = Intent()
                //设置动作为Intent.ACTION_SEND
                sharedIntent.action = Intent.ACTION_SEND
                //设置为文本类型
                sharedIntent.type = "text/*"
                sharedIntent.putExtra(
                    Intent.EXTRA_TEXT,
                    "我在APP【${AppUtils.getAppName()}】中创建了房间学习，邀请你加入，房间暗号：${theRoomCode}。"
                            + "https://a.app.qq.com/o/simple.jsp?pkgname=${AppUtils.getAppPackageName()}"
                )

                //设置要分享的内容
                startActivity(Intent.createChooser(sharedIntent, "邀请朋友"))
            }
        }




//        iv_back.setOnClickListener {
//            finish()
//        }
//        val url = "https://offphone-1252369707.file.myqcloud.com/2020-02-24-ruben-rodriguez-IXTvnOOSTyU-unsplash.jpg"


//                .apply(RequestOptions.bitmapTransform(BlurTransformation((25 * percent).toInt(), 3)))
//                .into(object : SimpleTarget<Drawable>() {
//                    override fun onResourceReady(resource: Drawable, transition: Transition<in Drawable>?) {
//                        room_detail_bg.setImageDrawable(resource)
//                    }
//                })


        getData()
    }

    private fun initScroll() {
        app_bar_layout.addOnOffsetChangedListener(OnOffsetChangedListener { appBarLayout, verticalOffset ->
            val percent = (abs(verticalOffset)).toFloat() / appBarLayout.totalScrollRange
//            LogUtils.d("" + verticalOffset + ":" + appBarLayout.totalScrollRange)
            title_img.alpha = percent
            title_text.alpha = percent

            tv_room_name.alpha = 1 - percent
            tv_room_code.alpha = 1 - percent
            iv_room_detail_1.alpha = 1 - percent
            tv_room_detail_member.alpha = 1 - percent
            iv_room_detail_2.alpha = 1 - percent
            tv_room_detail_time.alpha = 1 - percent
            tv_slogan.alpha = 1 - percent

            v_filter.alpha = (0.1 + percent / 2).toFloat()

        })
    }

    private fun getData() {
        lifecycleScope.launch(Dispatchers.IO) {
            try {
                val result = MyRetrofitClient.service.getRoomDesc(currentRoomId.toInt())
                if (result.code == 200) {
                    withContext(Dispatchers.Main) {
                        result.data?.apply {
                            theRoomCode = this.roomCode
                            ifMine = this.creatorId == MMKVUtils.getInt(MyConstants.SP_KEY_USER_ID, -1)
                            Glide.with(this@DetailRoomActivity).load(this.roomImageUrl)
                                //.transition(DrawableTransitionOptions.withCrossFade())
                                .diskCacheStrategy(DiskCacheStrategy.AUTOMATIC)
                                .into(title_img)
                            Glide.with(this@DetailRoomActivity).load(this.roomImageUrl)
                                //.transition(DrawableTransitionOptions.withCrossFade())
                                .diskCacheStrategy(DiskCacheStrategy.AUTOMATIC)
                                .into(room_detail_bg)
                            Glide.with(this@DetailRoomActivity).load(this.creatorAvatar)
                                //.transition(DrawableTransitionOptions.withCrossFade())
                                .diskCacheStrategy(DiskCacheStrategy.AUTOMATIC)
                                .into(iv_room_detail_creator_avatar)
                            this@DetailRoomActivity.title_text.text = this.roomName
                            this@DetailRoomActivity.tv_room_name.text = this.roomName
                            this@DetailRoomActivity.tv_room_code.text = "房间暗号：${this.roomCode}"
                            this@DetailRoomActivity.tv_room_detail_creator_name.text = "${this.creatorName}·创建于·${this.roomCreateTime}"
                            this@DetailRoomActivity.tv_room_detail_member.text = "共${this.roomMemberCount}名成员"
                            this@DetailRoomActivity.tv_room_detail_time.text = "累计时长${secondToHmSimpleSimple(this.roomTimeLength.toLong())}"

                            if (this.isJoined) {
                                MaterialIntroView.Builder(this@DetailRoomActivity)
                                    .enableDotAnimation(true)
                                    .enableIcon(false)
                                    .setFocusGravity(FocusGravity.CENTER)
                                    .setFocusType(Focus.MINIMUM)
                                    .setDelayMillis(0)
                                    .setShape(ShapeType.RECTANGLE)
                                    .enableFadeAnimation(true)
                                    .performClick(true)
                                    .setInfoText("您可以点击这里来邀请朋友加入此房间。")
                                    .setTarget(iv_share)
                                    .setUsageId(MyConstants.SP_KEY_ROOM_INVITE) //THIS SHOULD BE UNIQUE ID
                                    .setListener {

                                    }
                                    .show()
                            }

                            //是自己创建的房间
                            if (MMKVUtils.getInt(MyConstants.SP_KEY_USER_ID, -1) == this.creatorId) {
                                iv_edit.visibility = View.VISIBLE
                                iv_edit.setOnClickListener {
                                    val intent = Intent(this@DetailRoomActivity, CreateRoomActivity::class.java)
                                    intent.putExtra("changeType", 1)
                                    intent.putExtra("roomId", currentRoomId.toInt())
                                    intent.putExtra("bgString", this.roomImageUrl)
                                    intent.putExtra("roomType", this.roomType)
                                    intent.putExtra("roomName", this.roomName)
                                    intent.putExtra("roomDesc", this.roomDesc)

                                    <EMAIL>(intent)
                                }
                            }
                        }
                    }
                } else {
                    MyToastUtil.showInfo(result.msg)
                }
            } catch (e: Exception) {

            }
        }
    }

}