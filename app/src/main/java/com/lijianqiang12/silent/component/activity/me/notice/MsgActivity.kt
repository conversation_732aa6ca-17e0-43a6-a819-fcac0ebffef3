package com.lijianqiang12.silent.component.activity.me.notice

import android.os.Bundle
import androidx.lifecycle.lifecycleScope
import androidx.recyclerview.widget.LinearLayoutManager
import com.lijianqiang12.silent.MAX_ID
import com.lijianqiang12.silent.R
import com.lijianqiang12.silent.data.model.net.MyRetrofitClient
import com.lijianqiang12.silent.component.activity.base.BaseActivity
import com.lijianqiang12.silent.utils.MyToastUtil
import kotlinx.android.synthetic.main.activity_notice_me.*
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.launch
import kotlinx.coroutines.withContext

class MsgActivity : BaseActivity() {

    private lateinit var adapter: MsgAdapter

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        setContentView(R.layout.activity_notice_me)
        iv_return_msg.setOnClickListener { finish() }



        recycler_msg.layoutManager = LinearLayoutManager(this)
        adapter = MsgAdapter(R.layout.item_msg, mutableListOf())
        adapter.animationEnable = true
        adapter.loadMoreModule.setOnLoadMoreListener {
            if (adapter.data.size == 0) {
                getNewData(MAX_ID)
            } else {
                getNewData(adapter.data[adapter.data.size - 1].msgId)
            }
        }

        recycler_msg.adapter = adapter

        adapter.setOnItemClickListener { adapter, view, position ->

        }


        msg_refresh.isRefreshing = true
        msg_refresh.setOnRefreshListener {
            getNewData(MAX_ID)
        }

        getNewData(MAX_ID)
    }

    private fun getNewData(lastId: Long) {
        lifecycleScope.launch(Dispatchers.IO) {
            try {
                val result = MyRetrofitClient.service.msgList(lastId)
                withContext(Dispatchers.Main) {
                    if (result.code == 200) {
                        this@MsgActivity.msg_refresh.isRefreshing = false

                        result.data?.let {
                            if (lastId == MAX_ID) {
                                adapter.setNewInstance(it)
                                adapter.loadMoreModule.loadMoreComplete()
                            } else {
                                if (it.isEmpty()) {
                                    adapter.loadMoreModule.loadMoreEnd()
                                } else {
                                    adapter.addData(it)
                                    adapter.loadMoreModule.loadMoreComplete()
                                }
                            }
                            adapter.notifyDataSetChanged()
                        }
                    } else {
                        MyToastUtil.showInfo(result.msg)
                    }
                }
            } catch (e: Exception) {
                withContext(Dispatchers.Main) {
                    MyToastUtil.showInfo(e.message)
                    this@MsgActivity.msg_refresh.isRefreshing = false
                }
            }
        }
    }
}

