package com.lijianqiang12.silent.component.activity.room.roomdetail

import android.os.Bundle
import android.view.Gravity
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.fragment.app.Fragment
import androidx.lifecycle.lifecycleScope
import com.lijianqiang12.silent.utils.MMKVUtils
import com.bumptech.glide.Glide
import com.bumptech.glide.load.engine.DiskCacheStrategy
import com.jeremyliao.liveeventbus.LiveEventBus
import com.lijianqiang12.silent.LiveBus
import com.lijianqiang12.silent.MyConstants
import com.lijianqiang12.silent.R
import com.lijianqiang12.silent.component.activity.custom.dialog.MyProgressDialog
import com.lijianqiang12.silent.component.activity.custom.dialog.NormalDialog
import com.lijianqiang12.silent.component.activity.custom.dialog.OnCancelClickListener
import com.lijianqiang12.silent.component.activity.custom.dialog.OnOKClickListener
import com.lijianqiang12.silent.data.model.net.MyRetrofitClient
import com.lijianqiang12.silent.utils.DialogUtil
import com.lijianqiang12.silent.utils.MyToastUtil
import com.lijianqiang12.silent.utils.getColorFromTheme
import com.lijianqiang12.silent.utils.openFromWebsite
import kotlinx.android.synthetic.main.fragment_room_item_info.*
import kotlinx.android.synthetic.main.fragment_room_item_info.view.cl_board_notice
import kotlinx.android.synthetic.main.fragment_room_item_info.view.iv_close_board
import kotlinx.android.synthetic.main.fragment_room_item_info.view.tv_add_qq_board
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.launch
import kotlinx.coroutines.withContext

private const val ARG_PARAM1 = "param1"
private const val ARG_PARAM2 = "param2"

class RoomItemInfoFragment : Fragment() {
    private var param1: String? = null
    private var param2: String? = null

    private var hasJoined = false
    private var isCreator = false
    private lateinit var mDialog: MyProgressDialog


    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        arguments?.let {
            param1 = it.getString(ARG_PARAM1)
            param2 = it.getString(ARG_PARAM2)
        }
    }

    override fun onCreateView(inflater: LayoutInflater, container: ViewGroup?, savedInstanceState: Bundle?): View? {
        return inflater.inflate(R.layout.fragment_room_item_info, container, false)
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        mDialog = MyProgressDialog(this)
        getData()

        if (MMKVUtils.getBoolean(MyConstants.SP_KEY_CONFIG_SHOW_NO_BOARD_NOTICE, false) && MMKVUtils.getBoolean(
                MyConstants.SP_KEY_CONFIG_SHOW_BOARD_USER,
                true
            )
        ) {
            view.cl_board_notice.visibility = View.VISIBLE
            view.tv_add_qq_board.setOnClickListener {
                openFromWebsite(requireContext(), "${MMKVUtils.getString(MyConstants.SP_KEY_CONFIG_QQ_LINK, "")}")
            }
            view.iv_close_board.setOnClickListener {
                MMKVUtils.put(MyConstants.SP_KEY_CONFIG_SHOW_BOARD_USER, false)
                view.cl_board_notice.visibility = View.GONE
            }
        } else {
            view.cl_board_notice.visibility = View.GONE
        }

        btn_add_or_quit_room.setOnClickListener {
            lifecycleScope.launch(Dispatchers.IO) {
                try {
                    if (isCreator) {
                        withContext(Dispatchers.Main) {
                            NormalDialog(requireActivity()).apply {
                                setTitle("警告！！！")
                                setGravity(Gravity.START)
                                setContent("解散后，房间将无法恢复，确定要解散吗？")
                                isCancelable = false
                                setOnNormalOKClickListener("解散", object : OnOKClickListener {
                                    override fun onclick() {
                                        mDialog.show()
                                        <EMAIL>(Dispatchers.IO) {
                                            try {
                                                val result = MyRetrofitClient.service.deleteRoom(currentRoomId.toInt())
                                                if (result.code == 200) {
                                                    withContext(Dispatchers.Main) {

                                                        LiveEventBus.get(LiveBus.REFRESH_JOINED_ROOM_PAGE, Boolean::class.java).post(true)
                                                        mDialog.dismiss()
                                                        MyToastUtil.showSuccess(result.msg)
                                                        <EMAIL>().finish()
                                                    }
                                                } else {
                                                    withContext(Dispatchers.Main) {
                                                        mDialog.dismiss()
                                                        MyToastUtil.showError(result.msg)
                                                    }
                                                }
                                            } catch (e: Exception) {
                                                mDialog.dismiss()
                                            }
                                        }

                                    }
                                })
                                setOnNormalCancelClickListener("取消", object : OnCancelClickListener {
                                    override fun onclick() {

                                    }
                                })
                                showDialog()
                            }
                        }
                    } else if (hasJoined) {

                        mDialog.show()
                        val result = MyRetrofitClient.service.quitRoom(currentRoomId.toInt())
                        if (result.code == 200) {
                            getData()
                            withContext(Dispatchers.Main) {
                                mDialog.dismiss()
                                MyToastUtil.showInfo(result.msg)
                            }
                        } else {
                            withContext(Dispatchers.Main) {
                                mDialog.dismiss()
                                MyToastUtil.showInfo(result.msg)
                            }
                        }
                    } else {

                        mDialog.show()
                        val result = MyRetrofitClient.service.joinRoom(currentRoomId.toInt(), pwd)
                        when (result.code) {
                            200 -> {
                                getData()
                                withContext(Dispatchers.Main) {
                                    mDialog.dismiss()
                                    MyToastUtil.showInfo(result.msg)
                                }
                            }

                            700061 -> {
                                withContext(Dispatchers.Main) {
                                    mDialog.dismiss()
//                                    val intent = Intent(<EMAIL>(), VIP2Activity::class.java)
//                                    intent.putExtra(FROM_WHERE, "joinRoom")
//                                    startActivity(intent)
                                    MyToastUtil.showInfo(result.msg)
//                                    DialogUtil.showVIPDialog(null, this@RoomItemInfoFragment, result.msg, "joinRoom")

                                }
                            }

                            70006 -> {
                                withContext(Dispatchers.Main) {
                                    mDialog.dismiss()
//                                    val intent = Intent(<EMAIL>(), VIP2Activity::class.java)
//                                    intent.putExtra(FROM_WHERE, "joinRoom")
//                                    startActivity(intent)
//                                    MyToastUtil.showInfo(<EMAIL>().applicationContext, result.msg)
                                    DialogUtil.showVIPDialog(null, this@RoomItemInfoFragment, result.msg, "joinRoom")

                                }
                            }

                            else -> {
                                withContext(Dispatchers.Main) {
                                    mDialog.dismiss()
                                    MyToastUtil.showInfo(result.msg)
                                }
                            }
                        }
                    }


                } catch (e: Exception) {
                    mDialog.dismiss()
                }
            }


        }
    }

    companion object {
        @JvmStatic
        fun newInstance(param1: String, param2: String) =
            RoomItemInfoFragment().apply {
                arguments = Bundle().apply {
                    putString(ARG_PARAM1, param1)
                    putString(ARG_PARAM2, param2)
                }
            }
    }

    private fun getData() {
        lifecycleScope.launch(Dispatchers.IO) {
            try {
                val result = MyRetrofitClient.service.getRoomDesc(currentRoomId.toInt())
                if (result.code == 200) {
                    withContext(Dispatchers.Main) {
                        result.data?.apply {

                            Glide.with(requireActivity()).load(this.ownerAvatar)
                                //.transition(DrawableTransitionOptions.withCrossFade())
                                .diskCacheStrategy(DiskCacheStrategy.AUTOMATIC)
                                .into(requireActivity().iv_room_detail_owner_avatar)
                            requireActivity().tv_room_detail_desc.text = this.roomDesc
                            requireActivity().tv_room_detail_owner_name.text = this.ownerName

                            hasJoined = this.isJoined
                            isCreator = MMKVUtils.getInt(MyConstants.SP_KEY_USER_ID, -1) == this.creatorId
                            requireActivity().btn_add_or_quit_room.isEnabled = true
                            if (isCreator) {
                                requireActivity().btn_add_or_quit_room.text = "解散该房间"
                                requireActivity().btn_add_or_quit_room.setBackgroundColor(resources.getColor(R.color.colorRed))
                            } else if (this.isJoined) {
                                requireActivity().btn_add_or_quit_room.text = "退出该房间"
                                requireActivity().btn_add_or_quit_room.setBackgroundColor(getColorFromTheme(requireContext(), R.attr.custom_attr_app_text_3))
                            } else {
                                requireActivity().btn_add_or_quit_room.text = "加入该房间"
                                requireActivity().btn_add_or_quit_room.setBackgroundColor(getColorFromTheme(requireContext(), R.attr.custom_attr_app_accent))
                            }

                            when (this.ownerGender) {
                                0 -> {
                                    requireActivity().iv_gender.visibility = View.GONE
                                }

                                1 -> {
                                    requireActivity().iv_gender.visibility = View.VISIBLE
                                    requireActivity().iv_gender.setImageResource(R.drawable.ic_male)

                                }

                                2 -> {
                                    requireActivity().iv_gender.visibility = View.VISIBLE
                                    requireActivity().iv_gender.setImageResource(R.drawable.ic_female)
                                }
                            }
                        }
                    }
                } else {
                    withContext(Dispatchers.Main) {
                        MyToastUtil.showInfo(result.msg)
                    }
                }
            } catch (e: Exception) {

            }
        }
    }
}