package com.lijianqiang12.silent.component.activity

import android.app.ProgressDialog
import android.content.ClipData
import android.content.ClipboardManager
import android.content.Context
import android.os.Bundle
import androidx.lifecycle.Observer
import androidx.lifecycle.lifecycleScope
import com.jeremyliao.liveeventbus.LiveEventBus
import com.lijianqiang12.silent.LiveBus
import com.lijianqiang12.silent.R
import com.lijianqiang12.silent.component.activity.base.BaseActivity
import com.lijianqiang12.silent.data.model.net.MyRetrofitClient
import com.lijianqiang12.silent.utils.MyToastUtil
import com.lijianqiang12.silent.utils.MyUtil
import kotlinx.android.synthetic.main.activity_friend_force_unlock.*
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.launch
import kotlinx.coroutines.withContext


class FriendUnlockActivity : BaseActivity() {

    //    private var forceUnlockType = ""
    lateinit var dialog: ProgressDialog
    var mUserCode = ""

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        setContentView(R.layout.activity_friend_force_unlock)
        dialog = ProgressDialog(this)

        LiveEventBus.get(LiveBus.FORCE_UNLOCK_PAGE_FINISH, String::class.java).observe(this, Observer {
            finish()
        })

        iv_copy_user_code.setOnClickListener {
            if (mUserCode.isEmpty()) {
                MyToastUtil.showError("获取解锁码失败")
                return@setOnClickListener
            }
            val clipboard: ClipboardManager = this.applicationContext.getSystemService(Context.CLIPBOARD_SERVICE) as ClipboardManager
            val clipData = ClipData.newPlainText(null, mUserCode)
            clipboard.setPrimaryClip(clipData)
            MyToastUtil.showSuccess("用户码复制成功")
        }

        MNPasswordEditText.setOnTextChangeListener { text, isComplete ->

            if (isComplete) {
                MyUtil.showDialog(dialog, "正在验证...")

                lifecycleScope.launch(Dispatchers.IO) {
                    try {
                        val result = MyRetrofitClient.service.friendUnlock(mUserCode, text)
                        if (result.code == 200) {
                            LiveEventBus.get(LiveBus.FORCE_QUIT, String::class.java).post("")
                        } else {
                            MyToastUtil.showError(result.msg)
                            MNPasswordEditText.text.clear()
                        }
                        MyUtil.hideDialog(dialog)
                    } catch (e: Exception) {
                        MyToastUtil.showError(e.message)
                        MNPasswordEditText.text.clear()
                        MyUtil.hideDialog(dialog)
                    }
                }
            }
        }


//        btn_friend_force_unlock_pwd.setOnClickListener {


//            if (et_friend_force_unlock_pwd.text.isEmpty()) {
//                et_friend_force_unlock_pwd.error = "解锁码不能为空"
////                MyToastUtil.showError("密码不能为空")
//            } else {
//                val rightPwd = MMKVUtils.getString(MyConstants.SP_KEY_FORCE_QUITE_PWD, "jlkjkljklj2kljkl2jkl2")
//                if (rightPwd == et_friend_force_unlock_pwd.text.toString()) {
//                    LiveEventBus.get(LiveBus.FORCE_QUIT, String::class.java).post("")
////                    LiveBus.getInstance().with(String::class.java).postValue(LiveBus.FORCE_QUIT)
//                    finish()
//                } else {
//                    et_friend_force_unlock_pwd.error = "解锁码错误"
//                }
//            }
//        }
    }

    override fun onResume() {
        super.onResume()

        tv_user_code.text = "获取中..."
        lifecycleScope.launch(Dispatchers.IO) {
            try {
                val result = MyRetrofitClient.service.getUserCode()
                if (result.code == 200) {
                    withContext(Dispatchers.Main) {
                        result.data?.apply {
                            tv_user_code.text = this.userCode
                            mUserCode = this.userCode
                        }
                    }
                } else {
                    MyToastUtil.showError(result.msg)
                    tv_user_code.text = "获取失败"
                    mUserCode = ""
                }
            } catch (e: Exception) {
                MyToastUtil.showError(e.message)
                tv_user_code.text = "获取失败"
                mUserCode = ""
            }
        }
    }
}