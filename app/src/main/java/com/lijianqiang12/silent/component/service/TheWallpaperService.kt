package com.lijianqiang12.silent.component.service

import android.content.Intent
import android.graphics.Bitmap
import android.graphics.Canvas
import android.graphics.Paint
import android.graphics.drawable.Drawable
import android.service.wallpaper.WallpaperService
import android.view.SurfaceHolder
import com.blankj.utilcode.util.*
import com.jeremyliao.liveeventbus.LiveEventBus
import com.lijianqiang12.silent.LiveBus
import com.lijianqiang12.silent.MyConstants
import com.lijianqiang12.silent.TheApplication
import com.lijianqiang12.silent.component.service.background_service.BackgroundServiceMinimal
import com.lijianqiang12.silent.component.service.windows.FloatWindowOfProtectStart
import com.lijianqiang12.silent.utils.MMKVUtils
import com.lijianqiang12.silent.utils.ServiceUtil
import com.lijianqiang12.silent.utils.getDefaultWallpaper
import kotlinx.coroutines.*
import java.io.File


class TheWallpaperService : WallpaperService() {

    private lateinit var liveWallpaperEngine: LiveWallpaperEngine
    private lateinit var paint: Paint
    private lateinit var mStartFloat: FloatWindowOfProtectStart
    private var isDenyDropDown = true
    private var isDenyDropDownCount = 0

    override fun onCreate() {
        super.onCreate()
        LogUtils.d("onCreate")
        mStartFloat = FloatWindowOfProtectStart(this)




        if (!TheApplication.getInstance().globalParams.checkServiceInitOk) {
            mStartFloat.showProtectWindow()
            GlobalScope.launch(Dispatchers.IO) {
                delay(15000)
                withContext(Dispatchers.Main) {
                    mStartFloat.hideProtectWindow()
                }
            }

//            GlobalScope.launch(Dispatchers.IO) {
//                while (isDenyDropDown) {
//                    BackgroundServiceUtil.collapsingNotification(<EMAIL>)
//                    delay(100)
//                    isDenyDropDownCount++
//                    if (isDenyDropDownCount > 150) {
//                        isDenyDropDown = false
//                    }
//                }
//            }
        }

        LiveEventBus.get(LiveBus.START_OVER, String::class.java).observeStickyForever {
            mStartFloat.hideProtectWindow()
            isDenyDropDown = false
        }
    }

    override fun onStartCommand(intent: Intent, flags: Int, startId: Int): Int {
        return START_STICKY
    }

    override fun onCreateEngine(): Engine {
        LogUtils.d("onCreateEngine")
        ServiceUtil.checkAndStartService(this, BackgroundServiceMinimal::class.java)
        liveWallpaperEngine = LiveWallpaperEngine()
        return liveWallpaperEngine
    }

    override fun onDestroy() {
//        LiveBus.getInstance().with(String::class.java).removeObserver(liveBusObserver)
        super.onDestroy()
        LogUtils.d("onDestroy")
//        liveWallpaperEngine = null
    }

    inner class LiveWallpaperEngine : Engine() {

        init {
            paint = Paint()
        }

        override fun onSurfaceCreated(holder: SurfaceHolder?) {
            super.onSurfaceCreated(holder)

            LogUtils.d("onSurfaceCreated")
            changeSurface(holder)

        }

        override fun onSurfaceChanged(holder: SurfaceHolder?, format: Int, width: Int, height: Int) {
            super.onSurfaceChanged(holder, format, width, height)
            LogUtils.d("onSurfaceChanged")
            changeSurface(holder)
        }

        override fun onSurfaceDestroyed(holder: SurfaceHolder?) {
            super.onSurfaceDestroyed(holder)
            LogUtils.d("onSurfaceDestroyed")
        }

        override fun onVisibilityChanged(visible: Boolean) {
            super.onVisibilityChanged(visible)
            LogUtils.d("onVisibilityChanged")
            changeSurface(this.surfaceHolder)
        }

        private fun changeSurface(holder: SurfaceHolder?) {
            holder?.let {
                synchronized(this) {//因为lockCanvas和unlockCanvasAndPost必须成对，如果有哆线程竞争，会崩溃
                    var bitmap: Bitmap?
                    val wallpaperPath = MMKVUtils.getString(MyConstants.SP_KEY_SETTING_WALLPAPER_PATH, "")

                    bitmap = if (wallpaperPath.isNotEmpty() && File(wallpaperPath).exists()) {
                        ConvertUtils.drawable2Bitmap(Drawable.createFromPath(wallpaperPath))
                    } else {
                        getDefaultWallpaper(applicationContext)
                    }

                    bitmap?.apply {
                        if (bitmap!!.width != ScreenUtils.getScreenWidth()) {
                            bitmap = ImageUtils.scale(bitmap!!, ScreenUtils.getScreenWidth(), ScreenUtils.getScreenHeight())
                        }

                        if (it.surface.isValid) {
                            val canvas: Canvas? = it.lockCanvas()
                            canvas?.apply {
                                drawBitmap(bitmap!!, 0f, 0f, paint)
                                surfaceHolder?.unlockCanvasAndPost(this)
                            }
                        }
                    }
                }
            }
        }

    }


}
