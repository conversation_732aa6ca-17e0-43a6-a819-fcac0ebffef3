package com.lijianqiang12.silent.component.activity.lock

import android.app.Activity
import android.content.Intent
import android.graphics.Bitmap
import android.graphics.drawable.Drawable
import android.net.Uri
import android.os.Bundle
import android.provider.MediaStore
import android.view.Gravity
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.Toast
import androidx.appcompat.app.AppCompatActivity
import androidx.fragment.app.viewModels
import androidx.lifecycle.lifecycleScope
import androidx.recyclerview.widget.DiffUtil
import androidx.recyclerview.widget.GridLayoutManager
import androidx.recyclerview.widget.RecyclerView
import com.blankj.utilcode.util.AppUtils
import com.blankj.utilcode.util.LogUtils
import com.blankj.utilcode.util.ScreenUtils
import com.bumptech.glide.Glide
import com.bumptech.glide.load.engine.DiskCacheStrategy
import com.bumptech.glide.request.target.SimpleTarget
import com.bumptech.glide.request.transition.Transition
import com.google.android.material.bottomsheet.BottomSheetBehavior
import com.lijianqiang12.silent.DEFAULT_LOCK_BG
import com.lijianqiang12.silent.MAX_ID
import com.lijianqiang12.silent.MyConstants
import com.lijianqiang12.silent.R
import com.lijianqiang12.silent.component.activity.base.BaseBottomSheetDialogFragment
import com.lijianqiang12.silent.component.activity.custom.dialog.MyProgressDialog
import com.lijianqiang12.silent.component.activity.custom.dialog.NormalDialog
import com.lijianqiang12.silent.component.activity.custom.dialog.OnCancelClickListener
import com.lijianqiang12.silent.component.activity.custom.dialog.OnOKClickListener
import com.lijianqiang12.silent.component.activity.lock.wallpaper.requestPicCode
import com.lijianqiang12.silent.data.model.net.pojos.LockBg
import com.lijianqiang12.silent.data.viewmodel.LockViewModel
import com.lijianqiang12.silent.utils.*
import dagger.hilt.android.AndroidEntryPoint
import javax.inject.Inject
import com.yalantis.ucrop.UCrop
import kotlinx.android.synthetic.main.bottom_sheet_lock_bg.*
import kotlinx.android.synthetic.main.bottom_sheet_lock_bg.view.*
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.launch
import kotlinx.coroutines.withContext
import java.io.File
import java.io.FileNotFoundException
import kotlin.math.max
import kotlin.math.min


private const val ARG_PARAM1 = "imgUrl"
private const val ARG_PARAM2 = "title"

@AndroidEntryPoint
class BgBottomSheetDialogFragment() : BaseBottomSheetDialogFragment() {

    private val TAG = "WhiteBottomSheetDialogFragment"
    private var param1: String = DEFAULT_LOCK_BG
    private var param2: String = "锁机背景图片"
    private var listener: OnBgSelectListener? = null
    private lateinit var mBehavior: BottomSheetBehavior<View>
    private lateinit var customView: View
    private lateinit var recyclerview: RecyclerView
    private var bgList: MutableList<LockBg> = mutableListOf()
    private lateinit var progressDialog: MyProgressDialog


    @Inject
    lateinit var viewModel: LockViewModel

    companion object {
        @JvmStatic
        fun newInstance(param1: String, param2: String) =
            BgBottomSheetDialogFragment().apply {
                arguments = Bundle().apply {
                    putString(ARG_PARAM1, param1)
                    putString(ARG_PARAM2, param2)
                }
            }
    }

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        arguments?.let {
            param1 = it.getString(ARG_PARAM1)!!
            param2 = it.getString(ARG_PARAM2)!!
        }
    }

    override fun onCreateView(inflater: LayoutInflater, container: ViewGroup?, savedInstanceState: Bundle?): View? {
        customView = View.inflate(requireContext(), R.layout.bottom_sheet_lock_bg, null)
        return customView
    }

    override fun onStart() {
        super.onStart()
        val parentView = customView.parent as View
        parentView.setBackgroundColor(resources.getColor(R.color.colorTranslate))

        //设置父窗口为屏幕高度
        val layoutParams = parentView.layoutParams
        layoutParams.height = (requireActivity() as AppCompatActivity).findViewById<ViewGroup>(android.R.id.content).height - dpToPixel(
            MMKVUtils.getFloat(
                MyConstants.SP_KEY_STATUS_BAR_HEIGHT,
                32f
            )
        ).toInt()

        //设置初始状态为填充满
        mBehavior = BottomSheetBehavior.from(parentView)
        mBehavior.state = BottomSheetBehavior.STATE_EXPANDED
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        progressDialog = MyProgressDialog(this)
        customView.tv_select_bg_title.text = param2
        btn_local_pic.setOnClickListener {
            if (MyUtil.isVIP()) {
                val intent = Intent(Intent.ACTION_PICK, MediaStore.Images.Media.EXTERNAL_CONTENT_URI)
                startActivityForResult(intent, requestPicCode)
//                val intent = Intent(Intent.ACTION_GET_CONTENT)
//                        .setType("image/*")
//                        .addCategory(Intent.CATEGORY_OPENABLE)
//                val mimeTypes = arrayOf("image/jpeg", "image/png")
//                intent.putExtra(Intent.EXTRA_MIME_TYPES, mimeTypes)
//
//                startActivityForResult(Intent.createChooser(intent, "选择图片"), requestPicCode)
            } else {
//                val intent = Intent(requireContext(), VIP2Activity::class.java)
//                intent.putExtra(FROM_WHERE, "BottomSheetLocalPicture")
//                startActivity(intent)
//                MyToastUtil.showInfo(requireContext().applicationContext, "VIP用户可使用本地图片做背景")
                DialogUtil.showVIPDialog(null, this, content = "VIP用户可使用本地图片作为锁机背景。", "BottomSheetLocalPicture")

            }

        }

        recyclerview = customView.rv_lock_bg
        recyclerview.layoutManager = GridLayoutManager(requireContext(), 3)
        val adapter = LockBgAdapter(R.layout.item_lock_bg, mutableListOf(), param1)

        adapter.loadMoreModule.setOnLoadMoreListener {
            viewModel.refreshLockBg(0, bgList.last().imgId)
        }

        recyclerview.adapter = adapter

        (recyclerview.adapter as LockBgAdapter).setOnItemClickListener { adapter, view, position ->


            NormalDialog(this).apply {
                setTitle("提示")
                setContent("请选择需要的操作")
                setGravity(Gravity.CENTER)
                isCancelable = true
                setOnNormalOKClickListener("设为背景图", object : OnOKClickListener {
                    override fun onclick() {
                        MyToastUtil.showInfo("设置成功")
                        listener?.onSelect((adapter.data[position] as LockBg).imgUrl)
                        <EMAIL>()

                    }
                })
                setOnNormalCancelClickListener("下载到本地", object : OnCancelClickListener {
                    override fun onclick() {

                        if (MyUtil.isVIP()) {
//                            XXPermissions.with(this@BgBottomSheetDialogFragment)
//                                .permission(com.hjq.permissions.Permission.MANAGE_EXTERNAL_STORAGE)
//                                .request(object : OnPermissionCallback {
//                                    override fun onGranted(permissions: List<String>, all: Boolean) {
                            Glide.with(this@BgBottomSheetDialogFragment).asBitmap()
                                .diskCacheStrategy(DiskCacheStrategy.AUTOMATIC)
                                .load((adapter.data[position] as LockBg).imgUrl)
                                .into(object : SimpleTarget<Bitmap>() {
                                    override fun onResourceReady(resource: Bitmap, transition: Transition<in Bitmap>?) {
                                        progressDialog.dismiss()
                                        MyToastUtil.showInfo("下载成功")
                                        try {
                                            val fileName = "${AppUtils.getAppName()}壁纸-${System.currentTimeMillis()}.png"
                                            MediaStore.Images.Media.insertImage(
                                                <EMAIL>().contentResolver,
                                                resource,
                                                fileName,
                                                null
                                            )
                                        } catch (e: FileNotFoundException) {
                                            e.printStackTrace()
                                            progressDialog.dismiss()
                                            MyToastUtil.showInfo("出问题啦")
                                        }

                                    }

                                    override fun onLoadStarted(placeholder: Drawable?) {
                                        super.onLoadStarted(placeholder)
                                        progressDialog.show()
                                    }

                                    override fun onLoadFailed(errorDrawable: Drawable?) {
                                        super.onLoadFailed(errorDrawable)
                                        progressDialog.dismiss()
                                        MyToastUtil.showInfo("下载失败")
                                    }
                                })
//                                    }
//
//                                    override fun onDenied(permissions: List<String>, never: Boolean) {
//                                        MyToastUtil.showInfo("该功能需要读写存储权限")
//                                    }
//                                })


                        } else {
                            DialogUtil.showVIPDialog(null, this@BgBottomSheetDialogFragment, content = "VIP用户可下载图片到本地。", "BottomSheetDownloadPicture")

                        }


                    }
                })
                showDialog()
            }

        }

        viewModel.lockBgLiveData.observe(viewLifecycleOwner) {
            lifecycleScope.launch(Dispatchers.Default) {
                val diffResult = DiffUtil.calculateDiff(DiffCallBack(bgList, it.data!!), true)
                withContext(Dispatchers.Main) {
                    (recyclerview.adapter as LockBgAdapter).setNewInstance(it.data)
                    bgList = it.data
                    diffResult.dispatchUpdatesTo(recyclerview.adapter as LockBgAdapter)
//                    LogUtils.d("lockBgLiveData", it.state)
                    when (it.state) {
                        0 -> adapter.loadMoreModule.loadMoreComplete()
                        1 -> adapter.loadMoreModule.loadMoreEnd()
                    }
                }
            }
        }
        viewModel.refreshLockBg(0, MAX_ID)

    }

    override fun onActivityResult(requestCode: Int, resultCode: Int, data: Intent?) {
        super.onActivityResult(requestCode, resultCode, data)
        if (resultCode == Activity.RESULT_OK) {
            if (requestCode == requestPicCode) {
                val selectedUri = data!!.data
                if (selectedUri != null) {
                    startCrop(selectedUri)
                } else {
                    Toast.makeText(requireContext(), "未获取到图片，请确认已授予读写存储权限", Toast.LENGTH_LONG).show()
                }
            } else if (requestCode == UCrop.REQUEST_CROP) {
                val resultUri = UCrop.getOutput(data!!)
                if (resultUri != null) {
                    val d = Drawable.createFromStream(requireActivity().contentResolver.openInputStream(resultUri), null)
                    val path = ImageUtil.saveBgImage(requireActivity(), d!!)
                    listener?.onSelect(path)
                    dismiss()
                } else {
                    Toast.makeText(requireContext(), "无法获取裁剪后图片", Toast.LENGTH_LONG).show()
                }
            }
        } else if (resultCode == UCrop.RESULT_ERROR) {
            val cropError = UCrop.getError(data!!)
            if (cropError != null) {
                Toast.makeText(requireContext(), cropError.message, Toast.LENGTH_LONG).show()
            } else {
                Toast.makeText(requireContext(), "未知错误", Toast.LENGTH_SHORT).show()
            }
        }
    }

    private fun startCrop(uri: Uri) {
        val destinationFileName = "bg" + System.currentTimeMillis() + ".png"//SAMPLE_CROPPED_IMAGE_NAME

        //有的机型上会显示扁平的裁剪区域，原因未知，暂时这样处理
        val width = ScreenUtils.getScreenWidth()
        val height = ScreenUtils.getScreenHeight()
//        val width = min(ScreenUtils.getScreenHeight(), ScreenUtils.getScreenWidth())
//        val height = max(ScreenUtils.getScreenHeight(), ScreenUtils.getScreenWidth())

        val options = UCrop.Options()
        options.setHideBottomControls(true)
        options.setToolbarColor(resources.getColor(R.color.colorWhiteBackground))
        options.setStatusBarColor(resources.getColor(R.color.colorWhiteBackground))
        options.withAspectRatio(width.toFloat(), height.toFloat())
        options.withMaxResultSize(width, height)
        options.setToolbarWidgetColor(resources.getColor(R.color.custom_color_app_text_1_default))
        options.setCircleDimmedLayer(false)
        options.setToolbarCancelDrawable(R.drawable.ic_return)
        options.setShowCropFrame(true)
        options.setShowCropGrid(true)

        val uCrop = UCrop.of(uri, Uri.fromFile(File(requireContext().cacheDir, destinationFileName))).withOptions(options)
        uCrop.start(requireContext(), this)
    }


    private class DiffCallBack(val oldList: MutableList<LockBg>, val newList: MutableList<LockBg>) : DiffUtil.Callback() {

        override fun areItemsTheSame(oldItemPosition: Int, newItemPosition: Int): Boolean {
            return (oldList[oldItemPosition].imgId == newList[newItemPosition].imgId)
        }

        override fun getOldListSize(): Int {
            return oldList.size
        }

        override fun getNewListSize(): Int {
            return newList.size
        }

        override fun areContentsTheSame(oldItemPosition: Int, newItemPosition: Int): Boolean {
            return (oldList[oldItemPosition].imgUrl == newList[newItemPosition].imgUrl)
        }

    }

    fun setOnBgSelectListener(listener: OnBgSelectListener) {
        this.listener = listener
    }

    interface OnBgSelectListener {
        fun onSelect(imgUrl: String)
    }

}