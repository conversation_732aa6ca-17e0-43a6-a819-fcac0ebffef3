package com.lijianqiang12.silent.component.activity.lock.tomato

import android.widget.TextView
import com.chad.library.adapter.base.BaseQuickAdapter
import com.chad.library.adapter.base.module.LoadMoreModule
import com.chad.library.adapter.base.viewholder.BaseViewHolder
import com.lijianqiang12.silent.R
import com.lijianqiang12.silent.data.model.db.TomatoWithSub

class TomatoAdapter( layoutRes: Int, list: MutableList<TomatoWithSub>)
    : BaseQuickAdapter<TomatoWithSub, BaseViewHolder>(layoutRes, list), LoadMoreModule {

    override fun convert(viewHolder: BaseViewHolder, item: TomatoWithSub) {
        viewHolder.getView<TextView>(R.id.item_lock_fast_drag_title).text = item.tomato.title
    }

}