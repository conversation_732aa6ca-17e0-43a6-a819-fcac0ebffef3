package com.lijianqiang12.silent.component.activity

import android.app.Activity
import android.app.admin.DevicePolicyManager
import android.content.ComponentName
import android.content.Context
import android.content.Intent
import android.content.pm.PackageManager
import android.graphics.PixelFormat
import android.net.Uri
import android.os.Build
import android.os.Bundle
import android.provider.Settings
import android.view.Gravity
import android.view.View
import android.view.WindowManager
import android.widget.Toast
import androidx.annotation.RequiresApi
import co.mobiwise.materialintro.shape.Focus
import co.mobiwise.materialintro.shape.FocusGravity
import co.mobiwise.materialintro.shape.ShapeType
import co.mobiwise.materialintro.view.MaterialIntroView
import com.blankj.utilcode.util.AppUtils
import com.hjq.permissions.Permission.MANAGE_EXTERNAL_STORAGE
import com.hjq.permissions.Permission.READ_MEDIA_IMAGES
import com.hjq.permissions.Permission.READ_MEDIA_VISUAL_USER_SELECTED
import com.hjq.permissions.XXPermissions
import com.lijianqiang12.silent.MyConstants
import com.lijianqiang12.silent.R
import com.lijianqiang12.silent.component.activity.base.BaseActivity
import com.lijianqiang12.silent.component.activity.custom.dialog.LimitTimeEditDialog
import com.lijianqiang12.silent.component.activity.custom.dialog.NormalDialog
import com.lijianqiang12.silent.component.activity.custom.dialog.OnCancelClickListener
import com.lijianqiang12.silent.component.activity.custom.dialog.OnOKClickListener
import com.lijianqiang12.silent.component.activity.lock.wallpaper.WallpaperBottomSheetDialogFragment
import com.lijianqiang12.silent.component.receiver.DeviceManagerReceiver
import com.lijianqiang12.silent.databinding.DialogPermissionBinding
import com.lijianqiang12.silent.utils.*
import com.lijianqiang12.silent.utils.floatwindowUtils.FloatWindowManager
import ezy.assist.compat.SettingsCompat
import kotlinx.android.synthetic.main.dialog_permission.*
import kotlinx.android.synthetic.main.widget_edit_time_limit.tv_edit_time_limit

val REQUEST_CODE_SET_WALLPAPER = 0x001
val REQUEST_CODE_SELECT_SYSTEM_WALLPAPER = 0x002

class PermissionActivity : BaseActivity() {

    private lateinit var lockParams: WindowManager.LayoutParams
    private lateinit var lockWindowManager: WindowManager
    private lateinit var containerLayout: View
    private val requestCode = 1
    private lateinit var mDevicePolicyManager: DevicePolicyManager
    private lateinit var adminReceiver: ComponentName
    private var hasDraw = false
    private lateinit var binding: DialogPermissionBinding

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        binding = DialogPermissionBinding.inflate(layoutInflater)
        setContentView(binding.root)
//        setContentView(R.layout.dialog_permission)


        mDevicePolicyManager = applicationContext.getSystemService(DEVICE_POLICY_SERVICE) as DevicePolicyManager
        adminReceiver = ComponentName(this, DeviceManagerReceiver::class.java)

        iv_return_setting.setOnClickListener { finish() }

        var limitTimeStart = MMKVUtils.getInt(MyConstants.SP_KEY_LIMIT_TIME_START_PERMISSION, -1)
        var limitTimeEnd = MMKVUtils.getInt(MyConstants.SP_KEY_LIMIT_TIME_END_PERMISSION, -1)

        if (!MyUtil.isCurrentInTimeRange(limitTimeStart.toLong(), limitTimeEnd.toLong())) {
            MyToastUtil.showWarning("您设置了仅允许在${secondToSimpleHm(limitTimeStart)}-${secondToSimpleHm(limitTimeEnd)}修改")
            finish()
        }

        tv_edit_time_limit.text =
            if (limitTimeStart == -1 || limitTimeEnd == -1) "限制修改" else "${secondToSimpleHm(limitTimeStart)}-${secondToSimpleHm(limitTimeEnd)}可改"
        tv_edit_time_limit.setOnClickListener {
            NormalDialog(this).apply {
                setTitle("警告")
                setContent("请确保您已正常使用本软件超过三天，且当前无以下问题：\n\n· 锁机未到时间自动失效\n· 定时锁机未自动按时运行\n· app限时功能失效\n· 无障碍权限经常丢失\n")
                setGravity(Gravity.START)
                setOnNormalOKClickListener("无上述问题", object : OnOKClickListener {
                    override fun onclick() {
                        LimitTimeEditDialog(this@PermissionActivity).apply {
                            setTitle("在以下时间段可修改本页权限")
                            setLimitTime(limitTimeStart, limitTimeEnd)
                            setOnOKClickListener(object : LimitTimeEditDialog.OnOKLimitTimeEditListener {
                                override fun onclick(start: Int, end: Int) {
                                    limitTimeStart = start
                                    limitTimeEnd = end
                                    MMKVUtils.put(MyConstants.SP_KEY_LIMIT_TIME_START_PERMISSION, start)
                                    MMKVUtils.put(MyConstants.SP_KEY_LIMIT_TIME_END_PERMISSION, end)
                                    this@PermissionActivity.tv_edit_time_limit.text = "${secondToSimpleHm(start)}-${secondToSimpleHm(end)}可改"
                                }

                            })
                            setOnCancelClickListener(object : LimitTimeEditDialog.OnCancelLimitTimeEditListener {
                                override fun onclick() {
                                    MMKVUtils.put(MyConstants.SP_KEY_LIMIT_TIME_START_PERMISSION, -1)
                                    MMKVUtils.put(MyConstants.SP_KEY_LIMIT_TIME_END_PERMISSION, -1)
                                    this@PermissionActivity.tv_edit_time_limit.text = "限制修改"
                                }
                            })
                            show()
                        }
                    }
                })
                setOnNormalCancelClickListener("取消", object : OnCancelClickListener {
                    override fun onclick() {
                    }
                })
                showDialog()
            }

        }

        //悬浮窗
        constraintLayout2.setOnClickListener {
            if (SettingsCompat.canDrawOverlays(applicationContext)) {
                NormalDialog(this).apply {
                    setTitle("温馨提示")
                    setContent("您已授予过该权限，无需重复设置")
                    setGravity(Gravity.CENTER)
                    setOnNormalOKClickListener("我知道了", object : OnOKClickListener {
                        override fun onclick() {
                        }
                    })
                    showDialog()
                }
                return@setOnClickListener
            }
            when {
                Build.VERSION.SDK_INT >= Build.VERSION_CODES.R -> {//sdk30以后，悬浮窗只能跳转到列表，用户非常难找，所以跳转详情页
                    when {
//                        RomUtils.isHuawei() -> {
//
//                        }
                        MyRomUtils.isXiaomi() -> {
                            PermissionUtil.openMiuiPrivacyPermissionEditor(this)
                            MyToastUtil.showInfo("请授予悬浮窗权限")
                        }

                        // ColorOS15要跳转4次才能找到
//                        MyRomUtils.isOppo() -> {
//                            PermissionUtil.openAppDetailSetting(this)
//                            //无法用页面提醒，会导致权限页打不开，未知原因
////                            MyToastUtil.showInfo("请授予悬浮窗权限") //跳转权限页后，返回回来Toast仍然会显示一次，体验不好
//                        }

                        else -> {
                            PermissionUtil.openOverlyPermission(this)
                        }
                    }
                }

                Build.VERSION.SDK_INT >= Build.VERSION_CODES.O -> {
                    PermissionUtil.openOverlyPermission(this)
                }

                else -> {
                    try {
                        SettingsCompat.manageDrawOverlays(applicationContext)
                    } catch (e: Exception) {
                        try {
                            FloatWindowManager.applyPermission(applicationContext)
                        } catch (e: Exception) {
                            MyToastUtil.showError("跳转失败，请到设置或安全管家中授予悬浮窗权限")
                        }
                    }
                }
            }
        }

        //查看使用情况权限
        constraintLayout3.setOnClickListener {
            if (PermissionUtil.switched(applicationContext)) {
                NormalDialog(this).apply {
                    setTitle("温馨提示")
                    setContent("您已授予过该权限，无需重复设置")
                    setGravity(Gravity.CENTER)
                    setOnNormalOKClickListener("我知道了", object : OnOKClickListener {
                        override fun onclick() {
                        }
                    })
                    showDialog()
                }
                return@setOnClickListener
            }
            PermissionUtil.openUsagePermission(applicationContext)
        }

        //壁纸守护
        if (MMKVUtils.getBoolean(MyConstants.SP_KEY_CONFIG_SHOW_WALLPAPER, false)) {
            constraintLayout5wallpaper.visibility = View.VISIBLE
            constraintLayout5wallpaper.setOnClickListener {
                val requestPermissions = mutableListOf<String>()
                if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.UPSIDE_DOWN_CAKE) {
                    requestPermissions.add(READ_MEDIA_IMAGES)
                    requestPermissions.add(READ_MEDIA_VISUAL_USER_SELECTED)
                    requestPermissions.add(MANAGE_EXTERNAL_STORAGE)
                } else if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.TIRAMISU) {
                    requestPermissions.add(READ_MEDIA_IMAGES)
                    requestPermissions.add(MANAGE_EXTERNAL_STORAGE)
                } else {
                    requestPermissions.add(MANAGE_EXTERNAL_STORAGE)
                }

                if (XXPermissions.isGranted(this, requestPermissions)) {
                    NormalDialog(this@PermissionActivity).apply {
                        setTitle("温馨提示")
                        setContent("为避免锁机异常中断，我们为您提供了壁纸守护功能，该功能会修改当前壁纸，您也可自行选择喜欢的壁纸。\n\n注意：重装或升级应用后需要重新授予。")
                        setOnNormalOKClickListener("自定义", object : OnOKClickListener {
                            override fun onclick() {
                                WallpaperBottomSheetDialogFragment.newInstance().apply {
                                    show(supportFragmentManager, "WallpaperBottomSheetDialogFragment")
                                }
                            }
                        })
                        setOnNormalCancelClickListener("当前壁纸", object : OnCancelClickListener {
                            override fun onclick() {
                                if (wallpaperIsUsed(requireContext().applicationContext)) {
                                    MyToastUtil.showInfo("设置成功")
                                } else {
                                    setLiveWallpaper(applicationContext, this@PermissionActivity, REQUEST_CODE_SET_WALLPAPER)
                                    MyToastUtil.showInfo("若设置与预期不符，请选择自定义壁纸")
                                }
                            }
                        })
                        showDialog()
                    }
                } else {
                    XXPermissions.with(this@PermissionActivity).permission(requestPermissions).request(null)
                }

            }
        } else {
            constraintLayout5wallpaper.visibility = View.GONE
        }


        //小米后台弹出
        if (MyRomUtils.isXiaomi()) {
            constraintLayout61miui.visibility = View.VISIBLE
            constraintLayout61miui.setOnClickListener {
                try {
                    MyToastUtil.showInfo("下划，找到后台弹出界面权限并开启。")
                    PermissionUtil.openMiuiPrivacyPermissionEditor(this.applicationContext)
                } catch (e: Exception) {
                    MyToastUtil.showError("跳转失败，请自行到设置中授予远离手机后台弹出权限。")
                }
            }
        } else {
            constraintLayout61miui.visibility = View.GONE
        }


        //电池优化
        constraintLayout4.setOnClickListener {
            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.M) {
                if (PermissionUtil.ignoreBatteryOptimization(this)) {
                    NormalDialog(this).apply {
                        setTitle("温馨提示")
                        setContent("您已授予过该权限，无需重复设置")
                        setGravity(Gravity.CENTER)
                        setOnNormalOKClickListener("我知道了", object : OnOKClickListener {
                            override fun onclick() {
                            }
                        })
                        showDialog()
                    }
                    return@setOnClickListener
                }

                try {
                    val intent = Intent(Settings.ACTION_REQUEST_IGNORE_BATTERY_OPTIMIZATIONS)
                    intent.data = Uri.parse("package:" + packageName)
                    startActivity(intent)
                } catch (e: Exception) {
                    MyToastUtil.showError("无法打开电池优化页面")
                }
            } else {
                MyToastUtil.showWarning("Android M以下系统无需设置该项")
            }
        }

        //通知
        constraintLayout5.setOnClickListener {

            if (NotificationUtil.ifNotificationEnable(applicationContext)) {
                NormalDialog(this).apply {
                    setTitle("温馨提示")
                    setContent("您已授予过该权限，无需重复设置")
                    setGravity(Gravity.CENTER)
                    setOnNormalOKClickListener("我知道了", object : OnOKClickListener {
                        override fun onclick() {
                        }
                    })
                    showDialog()
                }
                return@setOnClickListener
            }

            NotificationUtil.goToNotificationSetting(this)
        }

        //自启动
        tv_auto_start.setOnClickListener {
            when {
                MyRomUtils.isHuaweiSeries() -> {
                    ImageUtil.openVideo(this, "https://offphone-video-1252369707.file.myqcloud.com/huawei_auto_start.mp4")
                }

                MyRomUtils.isXiaomi() -> {
                    ImageUtil.openVideo(this, "https://offphone-video-1252369707.file.myqcloud.com/xiaomi_auto_start.mp4")
                }

                MyRomUtils.isOppo() -> {
                    ImageUtil.openVideo(this, "https://offphone-video-1252369707.file.myqcloud.com/oppo_auto_start.mp4")
                }

                MyRomUtils.isVivo() -> {
                    ImageUtil.openVideo(this, "https://offphone-video-1252369707.file.myqcloud.com/vivo_auto_start2.mp4")
                }

                else -> {
                    tv_auto_start.visibility = View.GONE
                }
            }
        }
        constraintLayout61.setOnClickListener {
            PermissionUtil.openAutoStartPage(this)
        }

        //防止异常退出
        constraintLayout6.setOnClickListener {
            PermissionUtil.showAliveDialog(this)
        }


        //无障碍
        tv_accessibility.setOnClickListener {
            when {
                MyRomUtils.isHuaweiSeries() -> {
                    ImageUtil.openVideo(this, "https://offphone-video-1252369707.file.myqcloud.com/huawei_accessibility.mp4")
                }

                MyRomUtils.isXiaomi() -> {
                    ImageUtil.openVideo(this, "https://offphone-video-1252369707.file.myqcloud.com/xiaomi_accessibility.mp4")
                }

                MyRomUtils.isOppo() -> {
                    ImageUtil.openVideo(this, "https://offphone-video-1252369707.file.myqcloud.com/oppo_accessibility.mp4")
                }

                MyRomUtils.isVivo() -> {
                    ImageUtil.openVideo(this, "https://offphone-video-1252369707.file.myqcloud.com/vivo_accessibility.mp4")
                }

                else -> {
                    tv_accessibility.visibility = View.GONE
                }
            }
        }
        constraintLayout4access.setOnClickListener {
            PermissionUtil.openAccessibility(this)
        }


        //屏蔽正在应用上层显示通知
        if (MyRomUtils.isXiaomi() && Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
            constraintLayoutDeleteNotify.visibility = View.VISIBLE
            constraintLayoutDeleteNotify.setOnClickListener {
                val intent = Intent(Settings.ACTION_APP_NOTIFICATION_SETTINGS)
                intent.putExtra(Settings.EXTRA_APP_PACKAGE, "android") //因为“Android系统”进程的包名就叫 android
                startActivity(intent)
            }
        } else {
            constraintLayoutDeleteNotify.visibility = View.GONE
        }

        // “其它”分组标题
        if(MyRomUtils.isHuaweiSeries()|| MyRomUtils.isXiaomi()){
            tv_permission_title_hack2.visibility = View.VISIBLE
        }else{
            tv_permission_title_hack2.visibility = View.GONE
        }

        //加入设备管理器
        if (MyRomUtils.isHuaweiSeries()) {
            constraintLayout81.visibility = View.VISIBLE
            constraintLayout81.setOnClickListener {
                if (!checkAdmin()) {
                    requestLockAdmins()
                } else {
//                    disableAdmin()
                    MyToastUtil.showWarning("防卸载已开启，请勿重复开启。")
                }
            }
        } else {
            constraintLayout81.visibility = View.GONE
        }


        //隐藏桌面图标
        if (MyRomUtils.isXiaomi() || Build.VERSION.SDK_INT <= Build.VERSION_CODES.Q) {
            constraintLayout8.visibility = View.VISIBLE
            constraintLayout8.setOnClickListener {
                val isOpen = MMKVUtils.getBoolean(MyConstants.SP_KEY_UNINSTALL, false)

                if (isOpen) {

                    NormalDialog(this).apply {
                        setTitle("提示")
                        setContent("确定取消隐藏桌面图标吗？")
                        setGravity(Gravity.CENTER)
                        setOnNormalOKClickListener("取消隐藏", object : OnOKClickListener {
                            override fun onclick() {
                                <EMAIL>(
                                    ComponentName(AppUtils.getAppPackageName(), TheSplashActivity::class.java.canonicalName!!),
                                    PackageManager.COMPONENT_ENABLED_STATE_ENABLED, PackageManager.DONT_KILL_APP
                                )
                                MMKVUtils.put(MyConstants.SP_KEY_UNINSTALL, false)
                                MyToastUtil.showSuccess("已取消隐藏桌面图标，约10秒后生效")
                                this@PermissionActivity.tv_icon_is_open.setImageResource(R.drawable.ic_error)
                            }
                        })

                        setOnNormalCancelClickListener("我再想想", object : OnCancelClickListener {
                            override fun onclick() {
                            }
                        })

                        showDialog()
                    }
                } else {
                    if (MMKVUtils.getBoolean(MyConstants.SP_HOME_WIDGET_EXIST_1, false)
                        || MMKVUtils.getBoolean(MyConstants.SP_HOME_WIDGET_EXIST_2, false)
                        || MMKVUtils.getBoolean(MyConstants.SP_HOME_WIDGET_EXIST_3, false)
                        || MMKVUtils.getBoolean(MyConstants.SP_HOME_WIDGET_EXIST_4, false)
                    ) {
                        NormalDialog(this).apply {
                            setTitle("提示")
                            setContent("确定隐藏桌面图标吗？")
                            setGravity(Gravity.CENTER)
                            setOnNormalOKClickListener("隐藏", object : OnOKClickListener {
                                override fun onclick() {
                                    <EMAIL>(
                                        ComponentName(AppUtils.getAppPackageName(), TheSplashActivity::class.java.canonicalName!!),
                                        PackageManager.COMPONENT_ENABLED_STATE_DISABLED, PackageManager.DONT_KILL_APP
                                    )
                                    MMKVUtils.put(MyConstants.SP_KEY_UNINSTALL, true)
                                    MyToastUtil.showSuccess("已隐藏桌面图标，约10秒后生效")
                                    this@PermissionActivity.tv_icon_is_open.setImageResource(R.drawable.ic_ok)
                                }
                            })

                            setOnNormalCancelClickListener("我再想想", object : OnCancelClickListener {
                                override fun onclick() {
                                }
                            })

                            showDialog()
                        }

                    } else {
                        NormalDialog(this).apply {
                            setTitle("警告")
                            setContent(
                                "1.您需要先在桌面添加远离手机的小部件，以便隐藏图标后通过桌面小部件进入APP。" +
                                        "\n\n方法：长按桌面空白处或双指捏合，点击添加小部件或组件，从中找到远离手机并添加。" +
                                        "\n\n2.若隐藏图标失败，点击桌面图标将直接跳转卸载页面，如遇这种情况，请取消隐藏！！！"
                            )

                            setGravity(Gravity.START)
                            setOnNormalOKClickListener("我知道了", object : OnOKClickListener {
                                override fun onclick() {
                                }
                            })

                            setOnNormalCancelClickListener("放弃", object : OnCancelClickListener {
                                override fun onclick() {
                                }
                            })

                            showDialog()
                        }
                    }
                }
            }
        } else {
            constraintLayout8.visibility = View.GONE
        }


        //在最近任务中隐藏
        constraintLayout9.setOnClickListener {
            val isOpen = MMKVUtils.getBoolean(MyConstants.SP_KEY_RECENT, false)

            if (isOpen) {

                NormalDialog(this).apply {
                    setTitle("提示")
                    setContent(
                        "确定取消隐藏吗？"
                    )
                    setGravity(Gravity.CENTER)
                    setOnNormalOKClickListener("取消隐藏", object : OnOKClickListener {
                        override fun onclick() {
                            MMKVUtils.put(MyConstants.SP_KEY_RECENT, false)
                            MyToastUtil.showSuccess("已取消隐藏，重启软件后生效")
                            this@PermissionActivity.tv_duorenwu_is_open.setImageResource(R.drawable.ic_error)
                        }
                    })

                    setOnNormalCancelClickListener("我再想想", object : OnCancelClickListener {
                        override fun onclick() {
                        }
                    })

                    showDialog()
                }


            } else {
                NormalDialog(this).apply {
                    setTitle("提示")
                    setContent(
                        "确定要从多任务界面隐藏吗？"
                    )
                    setGravity(Gravity.CENTER)
                    setOnNormalOKClickListener("隐藏", object : OnOKClickListener {
                        override fun onclick() {
                            MMKVUtils.put(MyConstants.SP_KEY_RECENT, true)
                            MyToastUtil.showSuccess("已隐藏，重启软件后生效")
                            this@PermissionActivity.tv_duorenwu_is_open.setImageResource(R.drawable.ic_ok)
                        }
                    })

                    setOnNormalCancelClickListener("我再想想", object : OnCancelClickListener {
                        override fun onclick() {
                        }
                    })

                    showDialog()
                }

            }

        }

        //屏蔽卸载操作
        switch_uninstall.isChecked = MMKVUtils.getBoolean(MyConstants.SP_SETTING_DENY_UNINSTALL, false)
        switch_uninstall.setOnCheckedChangeListener { buttonView, isChecked ->
            MMKVUtils.put(MyConstants.SP_SETTING_DENY_UNINSTALL, isChecked)
        }

        //屏蔽语音助手
        when (MMKVUtils.getInt(MyConstants.SP_SETTING_DENY_ASSIST_NEW, 0)) {
            0 -> binding.rbAssistantNone.isChecked = true
            1 -> binding.rbAssistantLock.isChecked = true
            2 -> binding.rbAssistantAll.isChecked = true
        }

        binding.rgAssistant.setOnCheckedChangeListener { _, checkedId ->
            when (checkedId) {
                R.id.rb_assistant_none -> MMKVUtils.put(MyConstants.SP_SETTING_DENY_ASSIST_NEW, 0)
                R.id.rb_assistant_lock -> MMKVUtils.put(MyConstants.SP_SETTING_DENY_ASSIST_NEW, 1)
                R.id.rb_assistant_all -> MMKVUtils.put(MyConstants.SP_SETTING_DENY_ASSIST_NEW, 2)
            }
        }

        //因为只测试了这几个机型
        if (MyRomUtils.isHuaweiSeries() || MyRomUtils.isXiaomi() || MyRomUtils.isOppo() || MyRomUtils.isVivo()) {
            //屏蔽关机键
            when (MMKVUtils.getInt(MyConstants.SP_SETTING_DENY_SHUTDOWN_NEW, 0)) {
                0 -> binding.rbShutdownNone.isChecked = true
                1 -> binding.rbShutdownLock.isChecked = true
                2 -> binding.rbShutdownAll.isChecked = true
            }

            binding.rgShutdown.setOnCheckedChangeListener { _, checkedId ->
                when (checkedId) {
                    R.id.rb_shutdown_none -> MMKVUtils.put(MyConstants.SP_SETTING_DENY_SHUTDOWN_NEW, 0)
                    R.id.rb_shutdown_lock -> MMKVUtils.put(MyConstants.SP_SETTING_DENY_SHUTDOWN_NEW, 1)
                    R.id.rb_shutdown_all -> MMKVUtils.put(MyConstants.SP_SETTING_DENY_SHUTDOWN_NEW, 2)
                }
            }

            btn_deny_shutdown.visibility = View.VISIBLE
        } else {
            btn_deny_shutdown.visibility = View.GONE
        }

        //禁止上拉菜单栏
        if (MyRomUtils.isVivo()) {
            switch_up_deny.isChecked = MMKVUtils.getBoolean(MyConstants.SP_SETTING_DENY_UP, false)
            switch_up_deny.setOnCheckedChangeListener { buttonView, isChecked ->
                MMKVUtils.put(MyConstants.SP_SETTING_DENY_UP, isChecked)
            }
            btn_deny_drop_up.visibility = View.VISIBLE
        } else {
            btn_deny_drop_up.visibility = View.GONE
        }

        //禁止下拉状态栏
        when (MMKVUtils.getInt(MyConstants.SP_SETTING_DENY_DROPDOWN_NEW, 0)) {
            0 -> binding.rbDenyDropNone.isChecked = true
            1 -> binding.rbDenyDropLock.isChecked = true
            2 -> binding.rbDenyDropAll.isChecked = true
        }

        binding.rgDenyDrop.setOnCheckedChangeListener { _, checkedId ->
            when (checkedId) {
                R.id.rb_deny_drop_none -> MMKVUtils.put(MyConstants.SP_SETTING_DENY_DROPDOWN_NEW, 0)
                R.id.rb_deny_drop_lock -> MMKVUtils.put(MyConstants.SP_SETTING_DENY_DROPDOWN_NEW, 1)
                R.id.rb_deny_drop_all -> MMKVUtils.put(MyConstants.SP_SETTING_DENY_DROPDOWN_NEW, 2)
            }
        }
        //miui无法屏蔽下拉动作
        if (MyRomUtils.isXiaomi()) {
            binding.rbDenyDropLock.visibility = View.GONE
        }


        cl_accessibility_notice.setOnClickListener {
            PermissionUtil.openAccessibility(this)
        }

        //屏蔽侧边栏
//        switch_deny_fast_open.isChecked = MMKVUtils.getBoolean(MyConstants.SP_SETTING_DENY_FAST_OPEN, false)
//        switch_deny_fast_open.setOnCheckedChangeListener { buttonView, isChecked ->
//            if (isChecked) {
//                if (PermissionUtil.isAccessibilitySettingsOn(MyConstants.ACCESS_ABILITY_NAME, applicationContext)) {
//                    MMKVUtils.put(MyConstants.SP_SETTING_DENY_FAST_OPEN, isChecked)
//                } else {
//                    switch_deny_fast_open.isChecked = false
//                    NormalDialog(this).apply {
//                        setTitle("提示")
//                        setContent(
//                            "该功能需要无障碍辅助权限，是否授予权限并开启？"
//                        )
//                        setGravity(Gravity.CENTER)
//                        setOnNormalOKClickListener("去授予", object : OnOKClickListener {
//                            override fun onclick() {
//                                PermissionUtil.openAccessibility(this@PermissionActivity)
//                            }
//                        })
//
//                        setOnNormalCancelClickListener("我再想想", object : OnCancelClickListener {
//                            override fun onclick() {
//                            }
//                        })
//
//                        showDialog()
//                    }
//                }
//            } else {
//                MMKVUtils.put(MyConstants.SP_SETTING_DENY_FAST_OPEN, isChecked)
//            }
//        }


        //屏蔽小窗
        switch_deny_small_window.isChecked = MMKVUtils.getBoolean(MyConstants.SP_SETTING_DENY_SMALL_WINDOW, false)
        switch_deny_small_window.setOnCheckedChangeListener { buttonView, isChecked ->
            if (isChecked) {
                if (PermissionUtil.isAccessibilitySettingsOn(MyConstants.ACCESS_ABILITY_NAME, applicationContext)) {
                    MMKVUtils.put(MyConstants.SP_SETTING_DENY_SMALL_WINDOW, isChecked)
                } else {
                    switch_deny_small_window.isChecked = false
                    NormalDialog(this).apply {
                        setTitle("提示")
                        setContent(
                            "该功能需要无障碍辅助权限，是否授予权限并开启？"
                        )
                        setGravity(Gravity.CENTER)
                        setOnNormalOKClickListener("去授予", object : OnOKClickListener {
                            override fun onclick() {
                                PermissionUtil.openAccessibility(this@PermissionActivity)
                            }
                        })

                        setOnNormalCancelClickListener("我再想想", object : OnCancelClickListener {
                            override fun onclick() {
                            }
                        })

                        showDialog()
                    }
                }
            } else {
                MMKVUtils.put(MyConstants.SP_SETTING_DENY_SMALL_WINDOW, isChecked)
            }
        }
//
//        tv_lost_permission_small_window.setOnClickListener {
//            PermissionUtil.openAccessibility(this)
//        }

        //屏蔽编辑快捷开关
        when (MMKVUtils.getInt(MyConstants.SP_SETTING_DENY_EDIT_BUTTON_NEW, 0)) {
            0 -> binding.rbDenyEditButtonNone.isChecked = true
            1 -> binding.rbDenyEditButtonLock.isChecked = true
            2 -> binding.rbDenyEditButtonAll.isChecked = true
        }

        binding.rgDenyEditButton.setOnCheckedChangeListener { _, checkedId ->
            when (checkedId) {
                R.id.rb_deny_edit_button_none -> MMKVUtils.put(MyConstants.SP_SETTING_DENY_EDIT_BUTTON_NEW, 0)
                R.id.rb_deny_edit_button_lock -> MMKVUtils.put(MyConstants.SP_SETTING_DENY_EDIT_BUTTON_NEW, 1)
                R.id.rb_deny_edit_button_all -> MMKVUtils.put(MyConstants.SP_SETTING_DENY_EDIT_BUTTON_NEW, 2)
            }
        }


        //屏蔽关闭正在运行的任务
        if (MyRomUtils.isOppo() || MyRomUtils.isVivo()) {
            when (MMKVUtils.getInt(MyConstants.SP_SETTING_DENY_CLOSE_RUNNING_SERVICE_NEW, 0)) {
                0 -> binding.rbDenyCloseRunningServiceNone.isChecked = true
                1 -> binding.rbDenyCloseRunningServiceLock.isChecked = true
                2 -> binding.rbDenyCloseRunningServiceAll.isChecked = true
            }

            binding.rgDenyCloseRunningService.setOnCheckedChangeListener { _, checkedId ->
                when (checkedId) {
                    R.id.rb_deny_close_running_service_none -> MMKVUtils.put(MyConstants.SP_SETTING_DENY_CLOSE_RUNNING_SERVICE_NEW, 0)
                    R.id.rb_deny_close_running_service_lock -> MMKVUtils.put(MyConstants.SP_SETTING_DENY_CLOSE_RUNNING_SERVICE_NEW, 1)
                    R.id.rb_deny_close_running_service_all -> MMKVUtils.put(MyConstants.SP_SETTING_DENY_CLOSE_RUNNING_SERVICE_NEW, 2)
                }
            }
            binding.btnDenyCloseRunningService.visibility = View.VISIBLE
        } else {
            binding.btnDenyCloseRunningService.visibility = View.GONE
        }


        //锁机时保持activity
        switch_keep_foreground_activity.isChecked = MMKVUtils.getBoolean(MyConstants.SP_KEY_KEEP_FOREGROUND_ACTIVITY, false)
        switch_keep_foreground_activity.setOnCheckedChangeListener { buttonView, isChecked ->
            if (isChecked) {
                NormalDialog(this).apply {
                    setTitle("警告")
                    setContent("1.若不理解“Activity”与“前台”的概念，请勿开启此项\n\n2.若不开启此项也能打开白名单且后台运行正常，请勿开启此项")
                    setGravity(Gravity.START)
                    isCancelable = false
                    setOnNormalOKClickListener("取消", object : OnOKClickListener {
                        override fun onclick() {
                            this@PermissionActivity.switch_keep_foreground_activity.isChecked = false
                            MMKVUtils.put(MyConstants.SP_KEY_KEEP_FOREGROUND_ACTIVITY, false)
                        }
                    })
                    setOnNormalCancelClickListener("坚持开启", object : OnCancelClickListener {
                        override fun onclick() {
                            this@PermissionActivity.switch_keep_foreground_activity.isChecked = true
                            MMKVUtils.put(MyConstants.SP_KEY_KEEP_FOREGROUND_ACTIVITY, true)
                        }
                    })
                    showDialog()
                }
            } else {
                MMKVUtils.put(MyConstants.SP_KEY_KEEP_FOREGROUND_ACTIVITY, false)
            }
        }

    }

    @RequiresApi(Build.VERSION_CODES.O)
    private fun create1pxView() {
        lockParams = WindowManager.LayoutParams()
        lockParams.type = WindowManager.LayoutParams.TYPE_APPLICATION_OVERLAY
        lockWindowManager = applicationContext.getSystemService(Context.WINDOW_SERVICE) as WindowManager
        lockParams.format = PixelFormat.TRANSLUCENT
        lockParams.flags = lockParams.flags or WindowManager.LayoutParams.FLAG_NOT_TOUCHABLE or WindowManager.LayoutParams.FLAG_NOT_FOCUSABLE
        lockParams.gravity = Gravity.START or Gravity.TOP
        lockParams.x = 0
        lockParams.y = 0

        lockParams.width = 1
        lockParams.height = 1
        containerLayout = View(this)

    }


    override fun onDestroy() {
        if (hasDraw) {
            lockWindowManager.removeView(containerLayout)
            hasDraw = false
        }

        super.onDestroy()
    }

    override fun onResume() {
        super.onResume()
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
            if (SettingsCompat.canDrawOverlays(applicationContext) && !hasDraw) {
                create1pxView()
                lockWindowManager.addView(containerLayout, lockParams)
                hasDraw = true
            }
        }



        if (SettingsCompat.canDrawOverlays(applicationContext)) {
            iv_xuanfuchuang_is_ok.setImageResource(R.drawable.ic_ok)
        } else {
            iv_xuanfuchuang_is_ok.setImageResource(R.drawable.ic_error)
        }

        if (PermissionUtil.switched(applicationContext)) {
            iv_app_usage_is_ok.setImageResource(R.drawable.ic_ok)
        } else {
            iv_app_usage_is_ok.setImageResource(R.drawable.ic_error)
        }

        if (PermissionUtil.isMiuiBackgroundAllowed(this)) {
            iv_app_miui_bg_is_ok.setImageResource(R.drawable.ic_ok)
        } else {
            iv_app_miui_bg_is_ok.setImageResource(R.drawable.ic_error)
        }

//        if (MMKVUtils.getBoolean(MyConstants.SP_KEY_MIUI_BACK, false)) {
//            iv_app_miui_bg_is_ok.setImageResource(R.drawable.ic_ok)
//        } else {
//            iv_app_miui_bg_is_ok.setImageResource(R.drawable.ic_error)
//        }

        if (wallpaperIsUsed(applicationContext)) {
            iv_wallpaper_is_ok.setImageResource(R.drawable.ic_ok)
        } else {
            iv_wallpaper_is_ok.setImageResource(R.drawable.ic_error)
        }

//        Log.d("PermissionActivity", "Accessibility: " + PermissionUtil.isAccessibilityServiceEnabled(this, AccessibilityService::class.java))
//        Log.d("PermissionActivity", "Accessibility: " + PermissionUtil.isAccessibilitySettingsOn(this, MyConstants.ACCESS_ABILITY_NAME))
//        Log.d("PermissionActivity", "Accessibility: " + PermissionUtil.isAccessibilitySettingsOn(MyConstants.ACCESS_ABILITY_NAME, applicationContext))

        if (PermissionUtil.isAccessibilitySettingsOn(MyConstants.ACCESS_ABILITY_NAME, applicationContext)) {
            iv_access_is_ok.setImageResource(R.drawable.ic_ok)
        } else {
            iv_access_is_ok.setImageResource(R.drawable.ic_error)
        }

        if (PermissionUtil.ignoreBatteryOptimization(this)) {
            iv_battery_is_ok.setImageResource(R.drawable.ic_ok)
        } else {
            iv_battery_is_ok.setImageResource(R.drawable.ic_error)
        }


        if (NotificationUtil.ifNotificationEnable(applicationContext)) {
            iv_notification_is_ok.setImageResource(R.drawable.ic_ok)
        } else {
            iv_notification_is_ok.setImageResource(R.drawable.ic_error)
        }


//        if (PermissionUtil.isNotificationListenersEnabled(applicationContext)) {
//            iv_delete_notification_is_ok.setImageResource(R.drawable.ic_ok)
//        } else {
//            iv_delete_notification_is_ok.setImageResource(R.drawable.ic_error)
//        }

        if (checkAdmin()) {
            tv_icon_is_open_1.setImageResource(R.drawable.ic_ok)
        } else {
            tv_icon_is_open_1.setImageResource(R.drawable.ic_error)
        }


        val isOpenIcon = MMKVUtils.getBoolean(MyConstants.SP_KEY_UNINSTALL, false)
        if (isOpenIcon) {
            tv_icon_is_open.setImageResource(R.drawable.ic_ok)
        } else {
            tv_icon_is_open.setImageResource(R.drawable.ic_error)
        }

        val isOpen = MMKVUtils.getBoolean(MyConstants.SP_KEY_RECENT, false)
        if (isOpen) {
            tv_duorenwu_is_open.setImageResource(R.drawable.ic_ok)
        } else {
            tv_duorenwu_is_open.setImageResource(R.drawable.ic_error)
        }

        if (PermissionUtil.isAccessibilitySettingsOn(MyConstants.ACCESS_ABILITY_NAME, applicationContext)) {
            cl_accessibility_notice.visibility = View.GONE
        } else {
            cl_accessibility_notice.visibility = View.VISIBLE
        }

        if (!MMKVUtils.getBoolean(MyConstants.SP_KEY_INTRO_3, false)) {
            MaterialIntroView.Builder(this)
                .enableDotAnimation(true)
                .enableIcon(false)
                .setFocusGravity(FocusGravity.CENTER)
                .setFocusType(Focus.MINIMUM)
                .setDelayMillis(0)
                .setShape(ShapeType.RECTANGLE)
//                .setIdempotent(true)
                .enableFadeAnimation(true)
                .performClick(true)
                .setInfoText("显示锁机界面需要为远离手机开启“悬浮窗权限”。")
//                .setShapeType(ShapeType.CIRCLE)
                .setTarget(constraintLayout2)
                .setUsageId(MyConstants.SP_KEY_INTRO_3) //THIS SHOULD BE UNIQUE ID
                .setListener {
                    MMKVUtils.put(MyConstants.SP_KEY_INTRO_3, true)
                }
                .show()
        } else if (!MMKVUtils.getBoolean(MyConstants.SP_KEY_INTRO_4, false)) {
            MaterialIntroView.Builder(this)
                .enableDotAnimation(true)
                .enableIcon(false)
                .setFocusGravity(FocusGravity.CENTER)
                .setFocusType(Focus.MINIMUM)
                .setDelayMillis(0)
                .setShape(ShapeType.RECTANGLE)
//                .setIdempotent(true)
                .enableFadeAnimation(true)
                .performClick(true)
                .setInfoText("检测白名单或应用时长需要为远离手机开启“查看使用情况权限”。")
//                .setShapeType(ShapeType.CIRCLE)
                .setTarget(constraintLayout3)
                .setUsageId(MyConstants.SP_KEY_INTRO_4) //THIS SHOULD BE UNIQUE ID
                .setListener {
                    MMKVUtils.put(MyConstants.SP_KEY_INTRO_4, true)
                }
                .show()
        } else if (MyRomUtils.isXiaomi() && (!MMKVUtils.getBoolean(MyConstants.SP_KEY_INTRO_10, false))) {
            MaterialIntroView.Builder(this)
                .enableDotAnimation(true)
                .enableIcon(false)
                .setFocusGravity(FocusGravity.CENTER)
                .setFocusType(Focus.MINIMUM)
                .setDelayMillis(0)
                .setShape(ShapeType.RECTANGLE)
//                .setIdempotent(true)
                .enableFadeAnimation(true)
                .performClick(true)
                .setInfoText("小米手机需要允许“后台弹出界面”才能打开白名单。")
//                .setShapeType(ShapeType.CIRCLE)
                .setTarget(constraintLayout61miui)
                .setUsageId(MyConstants.SP_KEY_INTRO_10) //THIS SHOULD BE UNIQUE ID
                .setListener {
                    MMKVUtils.put(MyConstants.SP_KEY_INTRO_10, true)
                }
                .show()
        } else if (MyRomUtils.isXiaomi() && (!PermissionUtil.isMiuiBackgroundAllowed(this))) {
            showInfo1()
        } else if (!MMKVUtils.getBoolean(MyConstants.SP_KEY_INTRO_5, false)) {
            showInfo2()
        }
//        else if (!MMKVUtils.getBoolean(MyConstants.SP_KEY_INTRO_6, false)) {
//            showInfo3()
//        }
        else if (!MMKVUtils.getBoolean(MyConstants.SP_KEY_INTRO_7, false)) {
            showInfo4()
        }
    }


    private fun showInfo1() {
        val miuiBg = constraintLayout61miui
        val miuiOk = iv_app_miui_bg_is_ok
        NormalDialog(this).apply {
            setTitle("警告")
            setGravity(Gravity.START)
            setContent("请确认您已经在系统设置页面打开了“后台弹出界面”，否则您将无法从锁机界面打开白名单。\n\n如果您使用的不是MIUI系统，请点击下方的“忽略”按钮。")
            isCancelable = false
            setOnNormalOKClickListener("去开启", object : OnOKClickListener {
                override fun onclick() {
                    miuiBg.performClick()
                }
            })
            setOnNormalCancelClickListener("忽略", object : OnCancelClickListener {
                override fun onclick() {
                    MMKVUtils.put(MyConstants.SP_KEY_MIUI_BACK, true)
                    miuiOk.setImageResource(R.drawable.ic_ok)
                    showInfo2()
                }
            })
            showDialog()
        }
    }

    private fun showInfo2() {
        MaterialIntroView.Builder(this)
            .enableDotAnimation(true)
            .enableIcon(false)
            .setFocusGravity(FocusGravity.CENTER)
            .setFocusType(Focus.MINIMUM)
            .setDelayMillis(0)
            .setShape(ShapeType.RECTANGLE)
//                .setIdempotent(true)
            .enableFadeAnimation(true)
            .performClick(false)
            .setInfoText("必读！！！\n\n①如果锁机过程中异常退出或定时任务不起作用，您需要将这组权限也开启。\n\n②如果您能够通过一定方法破解锁机，请尝试开启本页底部（防破解）组权限。")
//                .setShapeType(ShapeType.CIRCLE)
            .setTarget(textView541)
            .setUsageId(MyConstants.SP_KEY_INTRO_5) //THIS SHOULD BE UNIQUE ID
            .setListener {
                MMKVUtils.put(MyConstants.SP_KEY_INTRO_5, true)
                showInfo4()
            }
            .show()
    }

//    private fun showInfo3() {
//        MaterialIntroView.Builder(this)
//                .enableDotAnimation(true)
//                .enableIcon(false)
//                .setFocusGravity(FocusGravity.CENTER)
//                .setFocusType(Focus.MINIMUM)
//                .setDelayMillis(0)
//                .setShape(ShapeType.RECTANGLE)
////                .setIdempotent(true)
//                .enableFadeAnimation(true)
//                .performClick(false)
//                .setInfoText("如果您能够通过一定方法破解锁机，请尝试开启这组权限。")
////                .setShapeType(ShapeType.CIRCLE)
//                .setTarget(textView57)
//                .setUsageId(MyConstants.SP_KEY_INTRO_6) //THIS SHOULD BE UNIQUE ID
//                .setListener {
//                    MMKVUtils.put(MyConstants.SP_KEY_INTRO_6, true)
//                    showInfo4()
//                }
//                .show()
//    }


    private fun showInfo4() {
        MaterialIntroView.Builder(this)
            .enableDotAnimation(true)
            .enableIcon(false)
            .setFocusGravity(FocusGravity.CENTER)
            .setFocusType(Focus.MINIMUM)
            .setDelayMillis(0)
            .setShape(ShapeType.RECTANGLE)
//                .setIdempotent(true)
            .enableFadeAnimation(true)
            .performClick(true)
            .setInfoText("您现在可以去尝试锁机了。")
//                .setShapeType(ShapeType.CIRCLE)
            .setTarget(iv_return_setting)
            .setUsageId(MyConstants.SP_KEY_INTRO_7) //THIS SHOULD BE UNIQUE ID
            .setListener {
                MMKVUtils.put(MyConstants.SP_KEY_INTRO_7, true)
            }
            .show()
    }


    override fun onActivityResult(requestCode: Int, resultCode: Int, data: Intent?) {
        super.onActivityResult(requestCode, resultCode, data)
        if (requestCode == REQUEST_CODE_SET_WALLPAPER) {
            if (resultCode == Activity.RESULT_OK) {
                Toast.makeText(this, "设置动态壁纸成功", Toast.LENGTH_SHORT).show()
            } else {
                Toast.makeText(this, "取消设置动态壁纸，code=${resultCode}", Toast.LENGTH_SHORT).show()
            }
        } else if (requestCode == REQUEST_CODE_SELECT_SYSTEM_WALLPAPER) {
            if (resultCode == Activity.RESULT_OK) {
                Toast.makeText(this, "设置系统壁纸成功", Toast.LENGTH_SHORT).show()
            } else {
                Toast.makeText(this, "取消设置系统壁纸，code=${resultCode}", Toast.LENGTH_SHORT).show()
            }
        }
    }


    /**
     *申请设备管理员权限
     */
    private fun requestLockAdmins() {
        //检查是否已经获取设备管理权限
        val active = mDevicePolicyManager.isAdminActive(adminReceiver)
        if (!active) {
            //打开DevicePolicyManager管理器，授权页面
            val intent = Intent()
            //授权页面Action -->  DevicePolicyManager.ACTION_ADD_DEVICE_ADMIN
            intent.action = DevicePolicyManager.ACTION_ADD_DEVICE_ADMIN
            //设置DEVICE_ADMIN，告诉系统申请管理者权限的Component/DeviceAdminReceiver
            intent.putExtra(DevicePolicyManager.EXTRA_DEVICE_ADMIN, adminReceiver)
            //设置 提示语--可不添加
            intent.putExtra(DevicePolicyManager.EXTRA_ADD_EXPLANATION, "激活设备管理器")
            startActivityForResult(intent, requestCode)
        } else {
//            Toast.makeText(this, "已经获取的DevicePolicyManager管理器的授权", Toast.LENGTH_LONG).show()
        }
    }

    /**
     * 查看是否已经获得管理者的权限
     * @return resualt
     */
    private fun checkAdmin(): Boolean {
        return mDevicePolicyManager.isAdminActive(adminReceiver)
    }

    private fun disableAdmin() {
        mDevicePolicyManager.removeActiveAdmin(adminReceiver)
        tv_icon_is_open_1.setImageResource(R.drawable.ic_error)
    }
}
