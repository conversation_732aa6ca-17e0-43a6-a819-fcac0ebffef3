package com.lijianqiang12.silent.component.activity.monitor

import android.animation.ObjectAnimator
import android.graphics.drawable.Drawable
import android.view.View
import android.widget.ImageView
import android.widget.TextView
import androidx.constraintlayout.widget.ConstraintLayout
import androidx.lifecycle.lifecycleScope
import com.chad.library.adapter.base.BaseQuickAdapter
import com.chad.library.adapter.base.module.LoadMoreModule
import com.chad.library.adapter.base.viewholder.BaseViewHolder
import com.lijianqiang12.silent.DURATION
import com.lijianqiang12.silent.R
import com.lijianqiang12.silent.data.model.db.AppLimit
import com.lijianqiang12.silent.component.activity.custom.CirclePercentView
import com.lijianqiang12.silent.utils.MyUtil
import com.lijianqiang12.silent.utils.getAppIcon
import com.lijianqiang12.silent.utils.secondToHmEnglish
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.launch
import kotlinx.coroutines.withContext
import java.util.*

class AppLimitAdapter(val fragment: MonitorFragment, layoutRes: Int, items: MutableList<AppLimit>) :
    BaseQuickAdapter<AppLimit, BaseViewHolder>(layoutRes, items), LoadMoreModule {

    override fun convert(holder: BaseViewHolder, item: AppLimit) {
        if (item.id == -1L) {
            holder.getView<ConstraintLayout>(R.id.cl_item_add).visibility = View.VISIBLE
            holder.getView<ConstraintLayout>(R.id.cl_content).visibility = View.INVISIBLE
        } else if (item.id == -2L) {
            holder.getView<ConstraintLayout>(R.id.cl_item_add).visibility = View.GONE
            holder.getView<ConstraintLayout>(R.id.cl_content).visibility = View.VISIBLE
        } else {
            holder.getView<ConstraintLayout>(R.id.cl_item_add).visibility = View.GONE
            holder.getView<ConstraintLayout>(R.id.cl_content).visibility = View.VISIBLE
        }
        fragment.lifecycleScope.launch(Dispatchers.IO) {
            val pkgList = MyUtil.jsonToPkgList(item.appPkg)
            var appLength = 0L
            var inRange = false
            val calendar = Calendar.getInstance()
            val today = calendar.get(Calendar.DAY_OF_WEEK)
            calendar.add(Calendar.DAY_OF_YEAR, -1)
            val yesterday = calendar.get(Calendar.DAY_OF_WEEK)
            if (MyUtil.isCurrentInTimeRange(
                    item.startTime,
                    item.endTime,
                    todayValid = MyUtil.getDayValid(today, item),
                    yesterdayValid = MyUtil.getDayValid(yesterday, item)
                )
            ) {
//                appLength = UsageUtil.getPkgListUsageLength(fragment.requireContext().applicationContext, pkgList, item.startTime, item.endTime)
                appLength = fragment.viewModel.getAppsUsageLength(pkgList, item.startTime, item.endTime)
                inRange = true
            }
            val title = MyUtil.getAppLimitTitle(item)

            var appIcon1: Drawable? = null
            var appIcon2: Drawable? = null
            var appIcon3: Drawable? = null
            var appIcon4: Drawable? = null
            var appIcon5: Drawable? = null
            var appIcon6: Drawable? = null
            val pkgListSize = pkgList.size
            if (pkgListSize > 0) {
                appIcon1 = getAppIcon(pkgList[0], "")
            }
            if (pkgListSize > 1) {
                appIcon2 = getAppIcon(pkgList[1], "")
            }
            if (pkgListSize > 2) {
                appIcon3 = getAppIcon(pkgList[2], "")
            }
            if (pkgListSize > 3) {
                appIcon4 = getAppIcon(pkgList[3], "")
            }
            if (pkgListSize > 4) {
                appIcon5 = getAppIcon(pkgList[4], "")
            }
            if (pkgListSize > 5) {
                appIcon6 = getAppIcon(pkgList[5], "")
            }
            if (pkgListSize > 6) {
                appIcon6 = fragment.requireContext().resources.getDrawable(R.drawable.ic_more_app)
            }

            withContext(Dispatchers.Main) {
                if (inRange) {
                    holder.getView<ConstraintLayout>(R.id.cl_app_limit_cover).visibility = View.GONE
                } else {
                    holder.getView<ConstraintLayout>(R.id.cl_app_limit_cover).visibility = View.VISIBLE
                }
                holder.getView<TextView>(R.id.tv_item_app_name).text = title

                if (item.valid) {
                    holder.getView<TextView>(R.id.tv_item_active).text = "已激活"
                    holder.getView<TextView>(R.id.tv_item_active).background = context.resources.getDrawable(R.drawable.shape_analyze_active_bg)
                } else {
                    holder.getView<TextView>(R.id.tv_item_active).text = "已暂停"
                    holder.getView<TextView>(R.id.tv_item_active).background = context.resources.getDrawable(R.drawable.shape_analyze_detactive_bg)
                }

                holder.getView<ImageView>(R.id.iv_item_app_icon_1).setImageDrawable(appIcon1)
                holder.getView<ImageView>(R.id.iv_item_app_icon_2).setImageDrawable(appIcon2)
                holder.getView<ImageView>(R.id.iv_item_app_icon_3).setImageDrawable(appIcon3)
                holder.getView<ImageView>(R.id.iv_item_app_icon_4).setImageDrawable(appIcon4)
                holder.getView<ImageView>(R.id.iv_item_app_icon_5).setImageDrawable(appIcon5)
                holder.getView<ImageView>(R.id.iv_item_app_icon_6).setImageDrawable(appIcon6)
                holder.getView<TextView>(R.id.tv_item_app_time_range).text = MyUtil.getTimeRangeString(item.startTime, item.endTime)
                holder.getView<TextView>(R.id.tv_item_app_time_used).text = "已用：${secondToHmEnglish(appLength)}"
                if (item.limitLength > appLength) {
                    holder.getView<TextView>(R.id.tv_item_app_time_left).text = "剩余：${secondToHmEnglish((item.limitLength / 60 - appLength / 60) * 60)}"
                } else {
                    holder.getView<TextView>(R.id.tv_item_app_time_left).text = "已用完"
                }
                var percent = 0f
                percent = if (item.limitLength > appLength) {
                    appLength * 100f / item.limitLength
                } else {
                    100f
                }

                val animator: ObjectAnimator = ObjectAnimator.ofFloat(holder.getView<CirclePercentView>(R.id.circlePercentView), "progress", 0f, percent)
                animator.duration = DURATION
                animator.start()

                //按星期循环
                if (item.monday) {
                    holder.getView<TextView>(R.id.tv_app_limit_week_select_1).setBackgroundResource(R.drawable.bg_week_select_thin)
                } else {
                    holder.getView<TextView>(R.id.tv_app_limit_week_select_1).setBackgroundResource(R.drawable.bg_week_not_select_thin)
                }
                if (item.tuesday) {
                    holder.getView<TextView>(R.id.tv_app_limit_week_select_2).setBackgroundResource(R.drawable.bg_week_select_thin)
                } else {
                    holder.getView<TextView>(R.id.tv_app_limit_week_select_2).setBackgroundResource(R.drawable.bg_week_not_select_thin)
                }
                if (item.wednesday) {
                    holder.getView<TextView>(R.id.tv_app_limit_week_select_3).setBackgroundResource(R.drawable.bg_week_select_thin)
                } else {
                    holder.getView<TextView>(R.id.tv_app_limit_week_select_3).setBackgroundResource(R.drawable.bg_week_not_select_thin)
                }
                if (item.thursday) {
                    holder.getView<TextView>(R.id.tv_app_limit_week_select_4).setBackgroundResource(R.drawable.bg_week_select_thin)
                } else {
                    holder.getView<TextView>(R.id.tv_app_limit_week_select_4).setBackgroundResource(R.drawable.bg_week_not_select_thin)
                }
                if (item.friday) {
                    holder.getView<TextView>(R.id.tv_app_limit_week_select_5).setBackgroundResource(R.drawable.bg_week_select_thin)
                } else {
                    holder.getView<TextView>(R.id.tv_app_limit_week_select_5).setBackgroundResource(R.drawable.bg_week_not_select_thin)
                }
                if (item.saturday) {
                    holder.getView<TextView>(R.id.tv_app_limit_week_select_6).setBackgroundResource(R.drawable.bg_week_select_thin)
                } else {
                    holder.getView<TextView>(R.id.tv_app_limit_week_select_6).setBackgroundResource(R.drawable.bg_week_not_select_thin)
                }
                if (item.sunday) {
                    holder.getView<TextView>(R.id.tv_app_limit_week_select_7).setBackgroundResource(R.drawable.bg_week_select_thin)
                } else {
                    holder.getView<TextView>(R.id.tv_app_limit_week_select_7).setBackgroundResource(R.drawable.bg_week_not_select_thin)
                }
            }

        }


    }
}
