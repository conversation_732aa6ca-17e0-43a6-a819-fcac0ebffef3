package com.lijianqiang12.silent.component.activity.custom.dialog

import android.graphics.Color
import android.graphics.drawable.ColorDrawable
import android.os.Bundle
import android.util.DisplayMetrics
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.view.WindowManager
import androidx.appcompat.app.AppCompatActivity
import androidx.fragment.app.Fragment
import com.lijianqiang12.silent.DIALOG_WIDTH_PERCENT_PROGRESS
import com.lijianqiang12.silent.R
import com.lijianqiang12.silent.component.activity.base.BaseDialogFragment


class MyProgressDialog(val fragment: Fragment? = null,
                       val activity: AppCompatActivity? = null,
                       val cancelable: Boolean = true) : BaseDialogFragment() {

//    constructor(fragment: Fragment) : this() {
//        this.fragment = fragment
//    }
//
//    constructor(activity: AppCompatActivity) : this() {
//        this.activity = activity
//    }

    private lateinit var v: View
//    private var fragment: Fragment? = null
//    private var activity: AppCompatActivity? = null


    override fun onCreateView(inflater: LayoutInflater, container: ViewGroup?, savedInstanceState: Bundle?): View? {
        dialog!!.window!!.setBackgroundDrawable(ColorDrawable(Color.TRANSPARENT))
        v = inflater.inflate(R.layout.dialog_progress, container, false)
        this.isCancelable = cancelable

        return v
    }

    fun show() {
        this.activity?.apply {
            super.show(this.supportFragmentManager, "MyProgressDialog")
        }
        this.fragment?.apply {
            super.show(this.requireFragmentManager(), "MyProgressDialog")
        }

    }


    override fun dismiss() {
        try {
            super.dismiss()
        } catch (e: Exception) {
        }
    }

    override fun onStart() {
        val params = dialog!!.window!!.attributes
        val dm: DisplayMetrics = resources.displayMetrics
        val width = dm.widthPixels
        params.width = (width * DIALOG_WIDTH_PERCENT_PROGRESS).toInt()//ViewGroup.LayoutParams.MATCH_PARENT
        params.height = params.width
        dialog!!.window!!.attributes = params as WindowManager.LayoutParams
        super.onStart()
    }


}