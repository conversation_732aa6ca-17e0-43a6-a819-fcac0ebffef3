package com.lijianqiang12.silent.component.activity

//import com.yl.lib.sentry.hook.PrivacySentry
//import com.yl.lib.sentry.hook.PrivacySentry
import android.annotation.SuppressLint
import android.content.Context
import android.content.Intent
import android.graphics.Color
import android.os.Bundle
import android.text.SpannableStringBuilder
import android.text.Spanned
import android.text.TextPaint
import android.text.method.LinkMovementMethod
import android.text.style.ClickableSpan
import android.view.View
import android.view.WindowManager
import androidx.lifecycle.Observer
import androidx.lifecycle.lifecycleScope
import com.blankj.utilcode.util.*
import com.jeremyliao.liveeventbus.LiveEventBus
import com.lijianqiang12.silent.LiveBus
import com.lijianqiang12.silent.MyConstants
import com.lijianqiang12.silent.R
import com.lijianqiang12.silent.TheApplication
import com.lijianqiang12.silent.component.activity.base.BaseActivity
import com.lijianqiang12.silent.component.activity.custom.dialog.MyProgressDialog
import com.lijianqiang12.silent.data.model.db.AppDatabase
import com.lijianqiang12.silent.data.model.net.pojos.AllQQUserInfo
import com.lijianqiang12.silent.data.model.net.pojos.ApiResponse
import com.lijianqiang12.silent.data.model.net.pojos.LoginResponse
import com.lijianqiang12.silent.data.model.net.pojos.WXUserInfo
import com.lijianqiang12.silent.data.model.repository.LoginRepository
import com.lijianqiang12.silent.data.viewmodel.LoginViewModel
import com.lijianqiang12.silent.initInitial
import dagger.hilt.android.AndroidEntryPoint
import javax.inject.Inject
import com.lijianqiang12.silent.qqapi.QQLoginListener
import com.lijianqiang12.silent.utils.*
import com.tencent.bugly.crashreport.CrashReport
import com.tencent.mm.opensdk.modelmsg.SendAuth
import com.tencent.mm.opensdk.openapi.WXAPIFactory
import com.tencent.tauth.Tencent
import com.yl.lib.sentry.hook.PrivacySentry
import kotlinx.android.synthetic.main.activity_login.*
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.ObsoleteCoroutinesApi
import kotlinx.coroutines.channels.ticker
import kotlinx.coroutines.launch
import kotlinx.coroutines.withContext

//const val LOGIN = 0
//const val REGISTER = 1

@AndroidEntryPoint
class TheLoginActivity : BaseActivity() {
    @ObsoleteCoroutinesApi
    private val tickerChannel = ticker(delayMillis = 30, initialDelayMillis = 0)

    @ObsoleteCoroutinesApi
    private val tickerChannelCode = ticker(delayMillis = 1000, initialDelayMillis = 0)
    private var isMainPage = true
    private lateinit var dialog: MyProgressDialog

    @Inject
    lateinit var viewModel: LoginViewModel

    private lateinit var repository: LoginRepository
    private var moveBackground = false

    private var inviteCode = -1

    private var privacyChecked = false
    private lateinit var mTencent: Tencent
    private val qqLoginListener = QQLoginListener()

    @SuppressLint("ClickableViewAccessibility")

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)

        window.clearFlags(WindowManager.LayoutParams.FLAG_TRANSLUCENT_STATUS or WindowManager.LayoutParams.FLAG_TRANSLUCENT_NAVIGATION);
        window.decorView.systemUiVisibility =
            View.SYSTEM_UI_FLAG_LAYOUT_FULLSCREEN or View.SYSTEM_UI_FLAG_LAYOUT_HIDE_NAVIGATION or View.SYSTEM_UI_FLAG_LAYOUT_STABLE;
        window.addFlags(WindowManager.LayoutParams.FLAG_DRAWS_SYSTEM_BAR_BACKGROUNDS)
        window.statusBarColor = Color.TRANSPARENT
        window.navigationBarColor = Color.TRANSPARENT

        setContentView(R.layout.activity_login)
        btn_skip.setOnClickListener { finish() }
        LiveEventBus.get(LiveBus.QQ_USER_INFO, AllQQUserInfo::class.java).observe(this) {
            val headimgurl = it.qqUserInfo.figureurl_qq_2.ifEmpty {
                it.qqUserInfo.figureurl_qq_1
            }
            login(1, it.openid, it.qqUserInfo.nickname, headimgurl, "")
        }
        LiveEventBus.get(LiveBus.WX_USER_INFO, WXUserInfo::class.java).observe(this) {
            login(2, it.unionid, it.nickname, it.headimgurl, "")
        }

//        if (!MMKVUtils.getBoolean(MyConstants.SP_AGREE_PRIVACY, false)) {
//            PrivacyBottomSheetDialogFragment.newInstance().apply {
//                setOnOKSelectListener(object : PrivacyBottomSheetDialogFragment.OnOKSelectListener {
//                    override fun onSelect() {
//                        PrivacySentry.Privacy.updatePrivacyShow()
////                        privacyChecked = true
////                        MMKVUtils.put(MyConstants.SP_AGREE_PRIVACY, true)
//                        this@TheLoginActivity.rb_agreement.setImageDrawable(resources.getDrawable(R.drawable.ic_check_box))
////                        initInitial()
////                        getInviteInfo()
//                    }
//                })
//                setOnCancelSelectListener(object : PrivacyBottomSheetDialogFragment.OnCancelSelectListener {
//                    override fun onSelect() {
//                    }
//                })
//                show(supportFragmentManager, "PrivacyBottomSheetDialogFragment")
//            }
//
//        }

//        if (MMKVUtils.getBoolean(MyConstants.SP_AGREE_PRIVACY, false)) {
////        if (privacyChecked) {
//            rb_agreement.setImageDrawable(resources.getDrawable(R.drawable.ic_check_box))
//        } else {
//            rb_agreement.setImageDrawable(resources.getDrawable(R.drawable.ic_unchecked_box))
//        }
        rb_agreement.setOnClickListener {
//            val result = !MMKVUtils.getBoolean(MyConstants.SP_AGREE_PRIVACY, false)
//            MMKVUtils.put(MyConstants.SP_AGREE_PRIVACY, result)
            privacyChecked = !privacyChecked
            if (privacyChecked) {
//                PrivacySentry.Privacy.updatePrivacyShow()
//                MMKVUtils.put(MyConstants.SP_AGREE_PRIVACY, true)
//                initInitial()
//                getInviteInfo()
//                initUMShare()
                rb_agreement.setImageDrawable(resources.getDrawable(R.drawable.ic_check_box))
            } else {
                rb_agreement.setImageDrawable(resources.getDrawable(R.drawable.ic_unchecked_box))
            }
        }


        repository = LoginRepository.getInstance(applicationContext)

        // ViewModel 现在通过 Hilt 自动注入
        dialog = MyProgressDialog(activity = this)


        hsv_login.setOnTouchListener { _, _ ->
            true
        }

        lifecycleScope.launch(Dispatchers.Default) {
            var index = 1
            var offset = 1
            repeat(100000000) {
                tickerChannel.receive()
                if (moveBackground) {
                    withContext(Dispatchers.Main) {
                        hsv_login.smoothScrollTo(offset, 0)
                        if (offset > ScreenUtils.getScreenWidth()) {
                            index = -1
                        }
                        if (offset < 1) {
                            index = 1
                        }
                        offset += index
                    }
                }
            }
        }


        btn_login_change.setOnClickListener {

//            rb_agreement.setImageDrawable(resources.getDrawable(R.drawable.ic_unchecked_box))
//            privacyChecked = false

            if (isMainPage) {
                showAlpha(cardView2, 0.8f)
                showAlpha(btn_login, 0.8f)
//                showAlpha(btn_get_voice_verify_code, 0.8f)
                hideAlpha(btn_login_wx, 0.8f)
                hideAlpha(btn_login_qq, 0.8f)
                hideAlpha(textView76, 1.0f)
                isMainPage = false
            } else {
                hideAlpha(cardView2, 0.8f)
                hideAlpha(btn_login, 0.8f)
//                hideAlpha(btn_get_voice_verify_code, 0.8f)
                showAlpha(btn_login_wx, 0.8f)
                showAlpha(btn_login_qq, 0.8f)
                showAlpha(textView76, 1.0f)
                isMainPage = true
            }
        }

        val tvContent = tv_login_privacy
        val str = "我已阅读并同意《用户协议》和《隐私政策》"
        val stringBuilder = SpannableStringBuilder(str)
        val span1 = TextViewSpan1(this)
        stringBuilder.setSpan(span1, 7, 13, Spanned.SPAN_EXCLUSIVE_EXCLUSIVE)

        val span2 = TextViewSpan2(this)
        stringBuilder.setSpan(span2, 14, 20, Spanned.SPAN_EXCLUSIVE_EXCLUSIVE)
        tvContent.text = stringBuilder
        tvContent.movementMethod = LinkMovementMethod.getInstance()//一定要记得设置这个方法  不是不起作用

//        val config = UMShareConfig()
//        config.isNeedAuthOnGetUserInfo(true)
//        UMShareAPI.get(this@LoginActivity).setShareConfig(config)

//        if (RomUtils.checkIsHuaweiRom()) {
//            iv_huawei.setOnClickListener {
//
//                if (checkIfAgree()) {
//                    dialog.show()
//                    HMSAgent.Hwid.signIn(true, SignInHandler { rst, result ->
//                        if (rst == HMSAgent.AgentResultCode.HMSAGENT_SUCCESS && result != null) {
//                            val gender = when (result.gender) {
//                                0 -> "男"
//                                1 -> "女"
//                                else -> ""
//                            }
//                            login(4, result.openId, result.displayName, result.photoUrl, gender)
//                        } else {
//                            dialog.dismiss()
//                            MyToastUtil.showError("授权失败：" + rst)
//                        }
//                    })
//                }
//            }
//        }

        btn_login_qq.setOnClickListener {
            checkPrivacyAndDo {
                if (MyUtil.checkPackageInstalled(this, "com.tencent.mobileqq", MyConstants.URL_QQ)) {
                    LogUtils.d("ljq==============QQ登录")
                    mTencent = Tencent.createInstance(MyConstants.QQ_APP_ID, this.applicationContext)
                    LogUtils.d("ljq==============QQ登录 createInstance")
                    mTencent.login(this, "all", qqLoginListener)
//                    mTencent.login(this, "get_user_info", qqLoginListener)
                    LogUtils.d("ljq==============QQ登录 login")
                } else {
                    MyToastUtil.showWarning("您的设备没有安装QQ")
                }
            }
        }
        btn_login_wx.setOnClickListener {
            checkPrivacyAndDo {
                if (MyUtil.checkPackageInstalled(this, "com.tencent.mm", MyConstants.URL_WX)) {
                    val api = WXAPIFactory.createWXAPI(this, MyConstants.WX_APP_ID)
                    val req: SendAuth.Req = SendAuth.Req()
                    req.scope = "snsapi_userinfo" // 只能填 snsapi_userinfo
                    req.state = "wechat_sdk_mindfulness"
                    api.sendReq(req)
                } else {
                    MyToastUtil.showWarning("您的设备没有安装微信")
                }
            }
        }
//        iv_weibo.setOnClickListener {
//            if (checkIfAgree()) {
//                if (UMShareAPI.get(this).isInstall(this, SHARE_MEDIA.SINA)) {
//                    UMShareAPI.get(this).getPlatformInfo(this, SHARE_MEDIA.SINA, authListener)
//                } else {
//                    MyToastUtil.showWarning("您的设备没有安装微博")
//                }
//            }
//        }

        btn_get_sms_verify_code.setOnClickListener {

            checkPrivacyAndDo {
                if (et_phone.text.isEmpty()) {
                    MyToastUtil.showInfo("手机号码不能为空")
                } else {
                    dialog.show()
                    getCode(1, et_phone.text.toString())
                }
            }
        }

        btn_get_voice_verify_code.setOnClickListener {

            checkPrivacyAndDo {
                if (et_phone.text.isEmpty()) {
                    MyToastUtil.showInfo("手机号码不能为空")
                } else {
                    dialog.show()
                    getCode(2, et_phone.text.toString())
                }
            }
        }

        btn_login.setOnClickListener {

            checkPrivacyAndDo {
                when {
                    StringUtils.isEmpty(et_phone.text.toString()) -> {
                        MyToastUtil.showWarning("手机号不能为空")
                    }

                    StringUtils.isEmpty(et_verify.text.toString()) -> {
                        MyToastUtil.showWarning("验证码不能为空")
                    }

                    else -> {
                        dialog.show()
                        viewModel.refreshVerifyCode(et_phone.text.toString(), et_verify.text.toString(), inviteCode)
                    }
                }
            }
        }

        viewModel.getVerifyCodeLiveData.observe(this, Observer {
            dialog.dismiss()
            if (it.code == 200) {
                MyToastUtil.showInfo("验证码发送成功")
                btn_get_sms_verify_code.isEnabled = false
                btn_get_voice_verify_code.isEnabled = false
                timeClick()
            }
        })

        viewModel.verifyCodeLiveData.observe(this, Observer {
            if (it.code == 200) {
                loginRegisterSucceed(it)
            } else {
                dialog.dismiss()
            }
        })

        viewModel.socialAccountLoginLiveData.observe(this, Observer {
            if (it.code == 200) {
                loginRegisterSucceed(it)
            } else {
                dialog.dismiss()
            }
        })

    }

    private fun getCode(type: Int, phone: String) {
        viewModel.refreshGetVerifyCode(type, phone)
    }

    private fun loginRegisterSucceed(it: ApiResponse<LoginResponse>) {
        MMKVUtils.put(MyConstants.SP_KEY_USER_ID, it.data!!.userId)
        MMKVUtils.put(MyConstants.SP_KEY_USERNAME, it.data!!.username)
        MMKVUtils.put(MyConstants.SP_KEY_TOKEN, it.data!!.token)
        MMKVUtils.put(MyConstants.SP_KEY_VIP_STATE, it.data!!.vipState)
        MMKVUtils.put(MyConstants.SP_KEY_VIP_END_TIME, it.data!!.vipEndTime)
        MMKVUtils.put(MyConstants.SP_KEY_AVATAR, it.data!!.avatar)
        MMKVUtils.put(MyConstants.SP_KEY_FORCE_QUITE_PWD, it.data!!.unlockPwd)
        MMKVUtils.put(MyConstants.SP_KEY_BIND_MOBILE, it.data!!.bindMobile)

        CrashReport.setUserSceneTag(TheApplication.getInstance(), it.data!!.userId)
        updateDBUserId(it.data!!.userId)


        LiveEventBus.get(LiveBus.LOGIN, Boolean::class.java).post(true)
        LiveEventBus.get(LiveBus.REFRESH_JOINED_ROOM_PAGE, Boolean::class.java).post(true)

        dialog.dismiss()
        finish()
    }

    private fun updateDBUserId(userId: Int) {
        val db = AppDatabase.getInstance(TheApplication.getInstance())
        db.fastDao().updateUserId(userId)
        db.tomatoDao().updateUserId(userId)
        db.scheduleDao().updateUserId(userId)
        db.whiteAppDao().updateUserId(userId)
        db.appLimitDao().updateUserId(userId)
        db.dayLimitDao().updateUserId(userId)

    }


    @ObsoleteCoroutinesApi
    fun timeClick() {
        lifecycleScope.launch(Dispatchers.Default) {
            var index = 60
            repeat(61) {
                tickerChannelCode.receive()
                withContext(Dispatchers.Main) {
                    if (index <= 0) {
                        btn_get_sms_verify_code.isEnabled = true
                        btn_get_sms_verify_code.text = "获取验证码"

                        btn_get_voice_verify_code.isEnabled = true
                        btn_get_voice_verify_code.text = "发送语音验证码"
                    } else {
                        btn_get_sms_verify_code.text = "${index}s"
                        btn_get_voice_verify_code.text = "${index}s"
                        index--
                    }
                }
            }
        }

    }

    override fun onActivityResult(requestCode: Int, resultCode: Int, data: Intent?) {
        super.onActivityResult(requestCode, resultCode, data)
        if (requestCode == 11101) {
            if (resultCode == -1) {
                Tencent.handleResultData(data, qqLoginListener)
            } else {
                MyToastUtil.showError("resultCode=${resultCode},data=${data}")
            }
        }
//        if (requestCode == Tencent.REQUEST_LOGIN) {
//        Tencent.onActivityResultData(requestCode, resultCode, data, qqLoginListener)
//        }
//        UMShareAPI.get(this).onActivityResult(requestCode, resultCode, data)
    }

//    private var authListener = object : UMAuthListener {
//        override fun onStart(platform: SHARE_MEDIA) {
//            dialog.show()
//        }
//
//        override fun onComplete(platform: SHARE_MEDIA, action: Int, data: Map<String, String>) {
//            when (platform) {
//                SHARE_MEDIA.QQ -> login(1, data["uid"]!!, data["name"]!!, data["iconurl"]!!, data["gender"]!!)
//                SHARE_MEDIA.WEIXIN -> login(2, data["uid"]!!, data["name"]!!, data["iconurl"]!!, data["gender"]!!)
//                SHARE_MEDIA.SINA -> login(3, data["uid"]!!, data["name"]!!, data["iconurl"]!!, data["gender"]!!)
//                else -> {
//                    dialog.dismiss()
//                    MyToastUtil.showError("未知平台")
//                }
//            }
//
//        }
//
//        override fun onError(platform: SHARE_MEDIA, action: Int, t: Throwable) {
//            dialog.dismiss()
//            MyToastUtil.showError("失败：" + t.message)
//        }
//
//        override fun onCancel(platform: SHARE_MEDIA, action: Int) {
//            dialog.dismiss()
//            MyToastUtil.showError("取消了")
//        }
//    }

    class TextViewSpan1(val context: Context) : ClickableSpan() {
        override fun updateDrawState(ds: TextPaint) {
            ds.color = getColorFromTheme(context, R.attr.custom_attr_app_text_link)
            //设置是否有下划线
            ds.isUnderlineText = false
        }

        override fun onClick(widget: View) {
            val intent = Intent(context, TheWebViewActivity::class.java)
            intent.putExtra("title", "")
            intent.putExtra("url", "https://help-offphone.shuge888.com/protocal/agreement")
            context.startActivity(intent)
        }
    }

    class TextViewSpan2(val context: Context) : ClickableSpan() {
        override fun updateDrawState(ds: TextPaint) {
            ds.color = getColorFromTheme(context, R.attr.custom_attr_app_text_link)
            //设置是否有下划线
            ds.isUnderlineText = false
        }

        override fun onClick(widget: View) {
            val intent = Intent(context, TheWebViewActivity::class.java)
            intent.putExtra("title", "")
            intent.putExtra("url", "https://help-offphone.shuge888.com/protocal/privacy")
            context.startActivity(intent)
        }
    }


    /**
     * 登录方式
     * 1：QQ
     * 2：微信
     * 3：微博
     * 4：华为
     * 5：vivo
     */
    fun login(loginType: Int, uid: String, username: String, avatar: String, gender: String) {
        viewModel.refreshSocialAccountLogin(loginType, uid, username, avatar, gender, inviteCode)
    }

    override fun onResume() {
        super.onResume()
        moveBackground = true
    }

    override fun onPause() {
        super.onPause()
        moveBackground = false
    }

    @ObsoleteCoroutinesApi
    override fun onDestroy() {
//        UMShareAPI.get(this).release()
        tickerChannel.cancel()
        tickerChannelCode.cancel()
        super.onDestroy()
    }

    private fun getInviteInfo() {
//        ShareTrace.getInstallTrace(object : ShareTraceInstallListener {
//            override fun onInstall(data: AppData) {
//
//                LogUtils.i("ljq==============appData=$data")
//
//                data.paramsData?.let { paramsData ->
//                    val tempList = paramsData.split("&")
//                    val dataResultMap = HashMap<String, String>()
//
//                    tempList.forEach {
//                        val tempData = it.split("=")
//                        dataResultMap[tempData[0]] = tempData[1]
//                    }
//
////                    LogUtils.i("ljq==============dataResultMap=${dataResultMap.toString()}")
//
//                    if (dataResultMap.contains("code")) {
//                        inviteCode = dataResultMap["code"]!!.toInt()
////                        LogUtils.i("ljq==============inviteCode=${inviteCode}")
//                    }
//                }
//
//            }
//
//            override fun onError(code: Int, msg: String) {
//                LogUtils.e("ljq==============Get install trace info error. code=$code,msg=$msg")
//            }
//        })
    }

//    private fun checkPrivacyAndDo(doLogin: () -> Unit) {
////        doLogin.invoke()
//        return
//        if (MMKVUtils.getBoolean(MyConstants.SP_AGREE_PRIVACY, false)) {
//            doLogin.invoke()
//        } else {
//
//            PrivacyBottomSheetDialogFragment.newInstance().apply {
//                setOnOKSelectListener(object : PrivacyBottomSheetDialogFragment.OnOKSelectListener {
//                    override fun onSelect() {
//                        doLogin.invoke()
////                        PrivacySentry.Privacy.updatePrivacyShow()
////                        privacyChecked = true
////                        MMKVUtils.put(MyConstants.SP_AGREE_PRIVACY, true)
//                        this@TheLoginActivity.rb_agreement.setImageDrawable(resources.getDrawable(R.drawable.ic_check_box))
////                        initInitial()
////                        getInviteInfo()
//                    }
//                })
//                setOnCancelSelectListener(object : PrivacyBottomSheetDialogFragment.OnCancelSelectListener {
//                    override fun onSelect() {
//                    }
//                })
//                show(supportFragmentManager, "PrivacyBottomSheetDialogFragment")
//            }
//
//
//        }
//    }


    private fun checkPrivacyAndDo(doLogin: () -> Unit) {
//        return true

        if (privacyChecked) {
            doLogin.invoke()
        } else {
            val shake = android.view.animation.AnimationUtils.loadAnimation(this, R.anim.shake)
            cl_login_bottom_privacy.startAnimation(shake)
            MyToastUtil.showInfo("需要先阅读并勾选本页底部同意《用户协议》和《隐私政策》才能登录")
        }
    }
}
