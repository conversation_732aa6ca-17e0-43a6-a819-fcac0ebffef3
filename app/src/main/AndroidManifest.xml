<?xml version="1.0" encoding="utf-8"?>
<manifest xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools">

    <uses-permission android:name="android.permission.REORDER_TASKS" /> <!-- <uses-permission android:name="android.permission.USE_FULL_SCREEN_INTENT"/> -->
    <uses-permission
        android:name="android.permission.PACKAGE_USAGE_STATS"
        tools:ignore="ProtectedPermissions" /> <!-- <uses-permission android:name="android.permission.READ_PHONE_STATE" /> -->
    <uses-permission android:name="android.permission.WRITE_EXTERNAL_STORAGE" />
    <uses-permission android:name="android.permission.MANAGE_EXTERNAL_STORAGE" /> <!-- Devices running Android 12L (API level 32) or lower -->
    <uses-permission
        android:name="android.permission.READ_EXTERNAL_STORAGE"
        android:maxSdkVersion="32" /> <!-- Devices running Android 13 (API level 33) or higher -->
    <uses-permission android:name="android.permission.READ_MEDIA_IMAGES" /> <!-- To handle the reselection within the app on Android 14 (API level 34) -->
    <uses-permission android:name="android.permission.READ_MEDIA_VISUAL_USER_SELECTED" />
    <uses-permission android:name="android.permission.REQUEST_INSTALL_PACKAGES" />
    <uses-permission android:name="android.permission.INTERNET" />
    <uses-permission android:name="android.permission.ACCESS_NETWORK_STATE" />
    <uses-permission android:name="android.permission.ACCESS_WIFI_STATE" />
    <uses-permission android:name="android.permission.SYSTEM_ALERT_WINDOW" />
    <uses-permission android:name="android.permission.SYSTEM_OVERLAY_WINDOW" />
    <uses-permission android:name="android.permission.RECEIVE_BOOT_COMPLETED" />
    <uses-permission android:name="android.permission.VIBRATE" />
    <uses-permission android:name="android.permission.ACCESS_NOTIFICATION_POLICY" />
    <uses-permission android:name="android.permission.REQUEST_IGNORE_BATTERY_OPTIMIZATIONS" />
    <uses-permission android:name="android.permission.FOREGROUND_SERVICE" />
    <uses-permission android:name="com.android.launcher.permission.WRITE_SETTINGS" />
    <uses-permission android:name="android.permission.WAKE_LOCK" />
    <uses-permission android:name="android.permission.SET_WALLPAPER" />
    <uses-permission android:name="android.permission.EXPAND_STATUS_BAR" />
    <uses-permission android:name="android.permission.POST_NOTIFICATIONS" />
    <uses-permission android:name="android.permission.FOREGROUND_SERVICE_SPECIAL_USE" /> <!-- <uses-permission android:name="android.permission.QUERY_ALL_PACKAGES"/> -->
    <!-- <uses-permission android:name="huawei.android.permission.HW_SIGNATURE_OR_SYSTEM"/> -->
    <!-- <uses-permission android:name="com.huawei.permission.external_app_settings.USE_COMPONENT"/> -->
    <uses-permission
        android:name="android.permission.READ_PHONE_STATE"
        tools:node="remove" />
    <uses-permission
        android:name="android.permission.CAMERA"
        tools:node="remove" />

    <uses-feature
        android:name="android.hardware.camera"
        android:required="false" />

    <uses-permission
        android:name="android.permission.INSTALL_PACKAGES"
        tools:ignore="ProtectedPermissions"
        tools:node="remove" />
    <uses-permission
        android:name="android.permission.WRITE_SETTINGS"
        tools:ignore="ProtectedPermissions"
        tools:node="remove" />
    <uses-permission
        android:name="android.permission.ACCESS_COARSE_LOCATION"
        tools:node="remove" />
    <uses-permission
        android:name="android.permission.ACCESS_FINE_LOCATION"
        tools:node="remove" />

    <queries>
        <intent>
            <action android:name="android.intent.action.MAIN" />
        </intent>
    </queries>

    <application
        android:name=".TheApplication"
        android:allowBackup="false"
        android:hardwareAccelerated="true"
        android:icon="@mipmap/ic_launcher"
        android:label="@string/app_name"
        android:largeHeap="true"
        android:extractNativeLibs="true"
        android:persistent="true"
        android:requestLegacyExternalStorage="true"
        android:usesCleartextTraffic="true"
        android:roundIcon="@mipmap/ic_launcher_round"
        android:supportsRtl="true"
        android:theme="@style/Theme.App.Starting"
        tools:replace="allowBackup,icon,label,theme">
        <activity
            android:name=".component.activity.AppLimitUnlockActivity"
            android:configChanges="keyboard|keyboardHidden|orientation|screenSize|smallestScreenSize|screenLayout"
            android:excludeFromRecents="true"
            android:exported="true"
            android:launchMode="singleInstance"
            android:taskAffinity="com.lijianqiang12.silent.app_limit_unlock" />
        <activity
            android:name=".component.activity.me.WXActivity"
            android:configChanges="keyboard|keyboardHidden|orientation|screenSize|smallestScreenSize|screenLayout"
            android:exported="false" />
        <activity
            android:name=".component.activity.analyze.analyzesetting.AnalyzeSettingActivity"
            android:configChanges="keyboard|keyboardHidden|orientation|screenSize|smallestScreenSize|screenLayout"
            android:exported="false" /> <!-- <meta-data -->
        <!-- android:name="UMENG_CHANNEL" -->
        <!-- android:value="${UMENG_CHANNEL_VALUE}" /> -->
        <activity
            android:name=".component.activity.me.invite_gift.InviteGiftActivity"
            android:configChanges="keyboard|keyboardHidden|orientation|screenSize|smallestScreenSize|screenLayout"
            android:exported="true" /> <!-- 明确引用org.apache.http.legacy库，避免QQ官方open sdk在Android 9上报错 -->
        <!-- <uses-library -->
        <!-- android:name="org.apache.http.legacy" -->
        <!-- android:required="false" /> -->
        <activity
            android:name=".component.activity.lock.setting.FastDenyPageSettingActivity"
            android:configChanges="keyboard|keyboardHidden|orientation|screenSize|smallestScreenSize|screenLayout"
            android:exported="true" />
        <activity
            android:name=".component.activity.lock.setting.DenyPageSettingActivity"
            android:configChanges="keyboard|keyboardHidden|orientation|screenSize|smallestScreenSize|screenLayout"
            android:exported="true" />
        <activity
            android:name=".component.activity.me.qa.QaActivity"
            android:configChanges="keyboard|keyboardHidden|orientation|screenSize|smallestScreenSize|screenLayout"
            android:exported="true" />
        <activity
            android:name=".component.activity.monitor.applimitcreate.AppLimitCreateActivity"
            android:configChanges="keyboard|keyboardHidden|orientation|screenSize|smallestScreenSize|screenLayout"
            android:exported="true" />
        <activity
            android:name=".component.activity.monitor.applimitsetting.AppLimitSettingActivity"
            android:configChanges="keyboard|keyboardHidden|orientation|screenSize|smallestScreenSize|screenLayout"
            android:exported="true" />
        <activity
            android:name=".component.activity.me.developer_unlock.DeveloperUnlockActivity"
            android:configChanges="keyboard|keyboardHidden|orientation|screenSize|smallestScreenSize|screenLayout"
            android:exported="true" />
        <activity
            android:name=".component.activity.me.room_request.VerifyRoomRequestActivity"
            android:configChanges="keyboard|keyboardHidden|orientation|screenSize|smallestScreenSize|screenLayout"
            android:exported="true" />
        <activity
            android:name=".component.activity.me.notice.MsgActivity"
            android:configChanges="keyboard|keyboardHidden|orientation|screenSize|smallestScreenSize|screenLayout"
            android:exported="true" />
        <activity
            android:name=".component.activity.me.vip.BuyHistoryActivity"
            android:configChanges="keyboard|keyboardHidden|orientation|screenSize|smallestScreenSize|screenLayout"
            android:exported="true" />
        <activity
            android:name=".component.activity.me.vip.VIP2Activity"
            android:configChanges="keyboard|keyboardHidden|orientation|screenSize|smallestScreenSize|screenLayout"
            android:exported="true" />
        <activity
            android:name=".component.activity.lock.punch.MyPunchCardActivity"
            android:configChanges="keyboard|keyboardHidden|orientation|screenSize|smallestScreenSize|screenLayout"
            android:exported="true" />
        <activity
            android:name=".component.activity.lock.punch.PunchCardActivity"
            android:configChanges="keyboard|keyboardHidden|orientation|screenSize|smallestScreenSize|screenLayout"
            android:exported="true" />
        <activity
            android:name=".component.activity.StartActivity"
            android:autoRemoveFromRecents="true"
            android:configChanges="keyboard|keyboardHidden|orientation|screenSize|smallestScreenSize|screenLayout"
            android:exported="true" />
        <activity
            android:name=".component.activity.me.theme.ThemeActivity"
            android:configChanges="keyboard|keyboardHidden|orientation|screenSize|smallestScreenSize|screenLayout"
            android:exported="true" />
        <activity
            android:name=".component.activity.analyze.appusage.AppUsageAnalyzeActivity"
            android:configChanges="keyboard|keyboardHidden|orientation|screenSize|smallestScreenSize|screenLayout"
            android:exported="true" />
        <activity
            android:name=".component.activity.analyze.todayusage.TodayAppUsageActivity"
            android:configChanges="keyboard|keyboardHidden|orientation|screenSize|smallestScreenSize|screenLayout"
            android:exported="true" />
        <activity
            android:name=".component.activity.analyze.todaytrend.TodayLockTrendActivity"
            android:configChanges="keyboard|keyboardHidden|orientation|screenSize|smallestScreenSize|screenLayout"
            android:exported="true" />
        <activity
            android:name=".component.activity.me.AboutActivity"
            android:configChanges="keyboard|keyboardHidden|orientation|screenSize|smallestScreenSize|screenLayout"
            android:exported="true" />
        <activity
            android:name=".component.activity.lock.ShareWordActivity"
            android:configChanges="keyboard|keyboardHidden|orientation|screenSize|smallestScreenSize|screenLayout"
            android:exported="true" /> <!-- <service -->
        <!-- android:name="com.didichuxing.doraemonkit.kit.colorpick.ScreenRecorderService" -->
        <!-- android:enabled="true" -->
        <!-- android:foregroundServiceType="mediaProjection" -->
        <!-- tools:targetApi="q" /> -->
        <service
            android:name=".component.service.TheWallpaperService"
            android:enabled="true"
            android:exported="true"
            android:label="@string/app_name"
            android:permission="android.permission.BIND_WALLPAPER">
            <intent-filter>
                <action android:name="android.service.wallpaper.WallpaperService" />
            </intent-filter>

            <meta-data
                android:name="android.service.wallpaper"
                android:resource="@xml/livewallpaper" />
        </service>

        <activity
            android:name=".component.activity.PasswordUnlockActivity"
            android:configChanges="keyboard|keyboardHidden|orientation|screenSize|smallestScreenSize|screenLayout"
            android:excludeFromRecents="true"
            android:exported="true"
            android:launchMode="singleInstance"
            android:taskAffinity="com.lijianqiang12.silent.password_unlock" />
        <activity
            android:name=".component.activity.FriendUnlockActivity"
            android:configChanges="keyboard|keyboardHidden|orientation|screenSize|smallestScreenSize|screenLayout"
            android:excludeFromRecents="true"
            android:exported="true"
            android:launchMode="singleInstance"
            android:taskAffinity="com.lijianqiang12.silent.friend_unlock" />
        <activity
            android:name=".component.activity.PayUnlockActivity"
            android:configChanges="keyboard|keyboardHidden|orientation|screenSize|smallestScreenSize|screenLayout"
            android:excludeFromRecents="true"
            android:exported="true"
            android:launchMode="singleInstance"
            android:taskAffinity="com.lijianqiang12.silent.pay_unlock" />
        <activity
            android:name=".component.activity.room.roomdetail.DetailRoomActivity"
            android:configChanges="keyboard|keyboardHidden|orientation|screenSize|smallestScreenSize|screenLayout"
            android:exported="true" />
        <activity
            android:name=".component.activity.me.UnlockFriendActivity"
            android:configChanges="keyboard|keyboardHidden|orientation|screenSize|smallestScreenSize|screenLayout"
            android:exported="true" />
        <activity
            android:name=".component.activity.room.createroom.CreateRoomActivity"
            android:configChanges="keyboard|keyboardHidden|orientation|screenSize|smallestScreenSize|screenLayout"
            android:exported="true" />
        <activity
            android:name=".component.activity.room.allroom.RoomAllActivity"
            android:configChanges="keyboard|keyboardHidden|orientation|screenSize|smallestScreenSize|screenLayout"
            android:exported="true" />
        <activity
            android:name=".component.activity.me.baozang.HutuiActivity"
            android:configChanges="keyboard|keyboardHidden|orientation|screenSize|smallestScreenSize|screenLayout"
            android:exported="true"
            android:theme="@style/LightAppTheme" />

        <service
            android:name=".component.service.MyAccessibilityService"
            android:exported="true"
            android:label="@string/accessibility_label"
            android:permission="android.permission.BIND_ACCESSIBILITY_SERVICE">
            <intent-filter>
                <action android:name="android.accessibilityservice.AccessibilityService" />
            </intent-filter>

            <meta-data
                android:name="android.accessibilityservice"
                android:resource="@xml/accessibility" />
        </service>

        <receiver
            android:name=".component.activity.widget.DenyUninstallAppWidget"
            android:exported="true">
            <intent-filter>
                <action android:name="android.appwidget.action.APPWIDGET_UPDATE" />
                <action android:name="com.lijianqiang12.silent.mvvm.view.widget.DenyUninstallAppWidget" />
            </intent-filter>

            <meta-data
                android:name="android.appwidget.provider"
                android:resource="@xml/deny_uninstall_app_widget_info" />
        </receiver>
        <receiver
            android:name=".component.activity.widget.DenyUninstallAppWidget2"
            android:exported="true">
            <intent-filter>
                <action android:name="android.appwidget.action.APPWIDGET_UPDATE" />
                <action android:name="com.lijianqiang12.silent.mvvm.view.widget.DenyUninstallAppWidget2" />
            </intent-filter>

            <meta-data
                android:name="android.appwidget.provider"
                android:resource="@xml/deny_uninstall_app_widget2_info" />
        </receiver>
        <receiver
            android:name=".component.activity.widget.DenyUninstallAppWidget3"
            android:exported="true">
            <intent-filter>
                <action android:name="android.appwidget.action.APPWIDGET_UPDATE" />
                <action android:name="com.lijianqiang12.silent.mvvm.view.widget.DenyUninstallAppWidget3" />
            </intent-filter>

            <meta-data
                android:name="android.appwidget.provider"
                android:resource="@xml/deny_uninstall_app_widget3_info" />
        </receiver>
        <receiver
            android:name=".component.activity.widget.DenyUninstallAppWidget4"
            android:exported="true">
            <intent-filter>
                <action android:name="android.appwidget.action.APPWIDGET_UPDATE" />
                <action android:name="com.lijianqiang12.silent.mvvm.view.widget.DenyUninstallAppWidget4" />
            </intent-filter>

            <meta-data
                android:name="android.appwidget.provider"
                android:resource="@xml/deny_uninstall_app_widget4_info" />
        </receiver>

        <activity
            android:name=".component.activity.PermissionActivity"
            android:configChanges="keyboard|keyboardHidden|orientation|screenSize|smallestScreenSize|screenLayout"
            android:exported="true"
            android:launchMode="singleTop" /> <!-- <activity -->
        <activity
            android:name=".component.activity.TheSplashActivity"
            android:configChanges="keyboard|keyboardHidden|orientation|screenSize|smallestScreenSize|screenLayout"
            android:excludeFromRecents="${excludeFromRecents}"
            android:exported="true"
            android:theme="@style/Theme.App.Starting">
            <intent-filter>
                <action android:name="android.intent.action.MAIN" />

                <category android:name="android.intent.category.LAUNCHER" />
            </intent-filter>
            <intent-filter>
                <action android:name="android.intent.action.VIEW" />

                <category android:name="android.intent.category.DEFAULT" />
                <category android:name="android.intent.category.BROWSABLE" />

                <data android:scheme="stdb6e3d30da71495d" />
            </intent-filter>

            <meta-data
                android:name="android.app.shortcuts"
                android:resource="@xml/shortcuts" />
        </activity>
        <activity
            android:name=".component.activity.IconSplashActivity"
            android:configChanges="keyboard|keyboardHidden|orientation|screenSize|smallestScreenSize|screenLayout"
            android:excludeFromRecents="${excludeFromRecents}"
            android:exported="true"
            android:theme="@style/Theme.App.Starting" />
        <activity
            android:name=".component.activity.TheMainActivity"
            android:autoRemoveFromRecents="true"
            android:configChanges="keyboard|keyboardHidden|orientation|screenSize|smallestScreenSize|screenLayout"
            android:exported="true"
            android:launchMode="singleTop" />
        <activity
            android:name=".component.activity.TheLoginActivity"
            android:autoRemoveFromRecents="true"
            android:configChanges="keyboard|keyboardHidden|orientation|screenSize|smallestScreenSize|screenLayout"
            android:exported="true"
            android:launchMode="singleTop" />
        <activity
            android:name=".component.activity.me.setting.SettingsActivity"
            android:autoRemoveFromRecents="true"
            android:configChanges="keyboard|keyboardHidden|orientation|screenSize|smallestScreenSize|screenLayout"
            android:exported="true"
            android:launchMode="singleTop" />
        <activity
            android:name=".component.activity.TheWebViewActivity"
            android:configChanges="keyboard|keyboardHidden|orientation|screenSize|smallestScreenSize|screenLayout"
            android:exported="true"
            android:theme="@style/LightAppTheme" />
        <activity
            android:name=".component.activity.lock.tomato.EditTomatoActivity"
            android:configChanges="keyboard|keyboardHidden|orientation|screenSize|smallestScreenSize|screenLayout"
            android:exported="true" />
        <activity
            android:name=".component.activity.lock.schedule.EditScheduleActivity"
            android:configChanges="keyboard|keyboardHidden|orientation|screenSize|smallestScreenSize|screenLayout"
            android:exported="true" />
        <activity
            android:name=".component.activity.lock.setting.LockSettingActivity"
            android:configChanges="keyboard|keyboardHidden|orientation|screenSize|smallestScreenSize|screenLayout"
            android:exported="true"
            android:theme="@style/LightAppTheme" />
        <activity
            android:name=".utils.VibratorActivity"
            android:exported="true"
            android:theme="@android:style/Theme.Translucent.NoTitleBar" />
        <activity
            android:name=".component.activity.me.userinfo.UserInfoActivity"
            android:configChanges="keyboard|keyboardHidden|orientation|screenSize|smallestScreenSize|screenLayout"
            android:exported="true" />
        <activity
            android:name=".component.activity.me.vip.VIPActivity"
            android:configChanges="keyboard|keyboardHidden|orientation|screenSize|smallestScreenSize|screenLayout"
            android:exported="true"
            android:theme="@android:style/Theme.Translucent" />
        <activity
            android:name=".wxapi.WXEntryActivity"
            android:configChanges="keyboardHidden|orientation|screenSize"
            android:exported="true"
            android:theme="@android:style/Theme.Translucent.NoTitleBar" />
        <activity
            android:name=".wxapi.WXPayEntryActivity"
            android:configChanges="keyboardHidden|orientation|screenSize"
            android:exported="true"
            android:launchMode="singleTop"
            android:theme="@style/NoticeTransparent" />
        <activity
            android:name="com.yalantis.ucrop.UCropActivity"
            android:exported="true"
            android:theme="@style/AppTheme" />
        <activity
            android:name="com.alipay.sdk.app.H5PayActivity"
            android:configChanges="keyboard|keyboardHidden|orientation|screenSize|smallestScreenSize|screenLayout|navigation"
            android:exported="false"
            android:screenOrientation="behind"
            android:windowSoftInputMode="adjustResize|stateHidden" />
        <activity
            android:name="com.alipay.sdk.app.H5AuthActivity"
            android:configChanges="keyboard|keyboardHidden|orientation|screenSize|smallestScreenSize|screenLayout|navigation"
            android:exported="false"
            android:screenOrientation="behind"
            android:windowSoftInputMode="adjustResize|stateHidden" />
        <activity
            android:name="com.tencent.tauth.AuthActivity"
            android:exported="true"
            android:launchMode="singleTask"
            android:noHistory="true">
            <intent-filter>
                <action android:name="android.intent.action.VIEW" />

                <category android:name="android.intent.category.DEFAULT" />
                <category android:name="android.intent.category.BROWSABLE" />

                <data android:scheme="tencent1106233322" />
            </intent-filter>
        </activity>
        <activity
            android:name="com.tencent.connect.common.AssistActivity"
            android:configChanges="orientation|keyboardHidden|screenSize"
            android:screenOrientation="behind"
            android:theme="@android:style/Theme.Translucent.NoTitleBar" />


        <activity
            android:name=".component.activity.ToastNoticeActivity"
            android:exported="true"
            android:theme="@style/NoticeTransparent" />
        <activity
            android:name=".component.activity.StartApp1PxActivity"
            android:configChanges="keyboard|keyboardHidden|orientation|screenSize|smallestScreenSize|screenLayout|navigation"
            android:excludeFromRecents="true"
            android:exported="true"
            android:finishOnTaskLaunch="false"
            android:launchMode="singleInstance"
            android:theme="@style/OnePxActivityStyle" />

        <meta-data
            android:name="android.max_aspect"
            android:value="2.2" />
        <meta-data
            android:name="com.huawei.hms.client.appid"
            android:value="appid=100425597" />
        <meta-data
            android:name="com.sharetrace.APP_KEY"
            android:value="db6e3d30da71495d" /> <!-- <activity -->
        <!-- android:name="com.huawei.hms.activity.BridgeActivity" -->
        <!-- android:configChanges="orientation|locale|screenSize|layoutDirection|fontScale" -->
        <!-- android:excludeFromRecents="true" -->
        <!-- android:exported="false" -->
        <!-- android:hardwareAccelerated="true" -->
        <!-- android:theme="@android:style/Theme.Translucent"> -->
        <!-- <meta-data -->
        <!-- android:name="hwc-theme" -->
        <!-- android:value="androidhwext:style/Theme.Emui.Translucent" /> -->
        <!-- </activity> &lt;!&ndash; <provider &ndash;&gt; -->
        <!-- android:name=".MyFileProvider" -->
        <!-- android:authorities="${applicationId}.fixConflict.fileProvider" -->
        <!-- android:exported="false" -->
        <!-- android:grantUriPermissions="true"> -->
        <!-- <meta-data -->
        <!-- android:name="android.support.FILE_PROVIDER_PATHS" -->
        <!-- android:resource="@xml/file_paths" /> -->
        <!-- </provider> -->
        <provider
            android:name="androidx.core.content.FileProvider"
            android:authorities="${applicationId}.fileprovider"
            android:exported="false"
            android:grantUriPermissions="true">
            <meta-data
                android:name="android.support.FILE_PROVIDER_PATHS"
                android:resource="@xml/file_paths"
                tools:replace="android:resource" />
        </provider>

        <receiver
            android:name=".component.receiver.RebootReceiver"
            android:enabled="true"
            android:exported="true">
            <intent-filter android:priority="**********">
                <action android:name="android.intent.action.SIG_STR" />
            </intent-filter>
            <intent-filter android:priority="**********">
                <action android:name="android.intent.action.SERVICE_STATE" />
            </intent-filter>
            <intent-filter android:priority="**********">
                <action android:name="android.intent.action.AIRPLANE_MODE" />
            </intent-filter>
            <intent-filter android:priority="**********">
                <action android:name="android.intent.action.BATTERY_LOW" />
                <action android:name="android.intent.action.BATTERY_OKAY" />
                <action android:name="android.intent.action.ACTION_POWER_CONNECTED" />
                <action android:name="android.intent.action.ACTION_POWER_DISCONNECTED" />
                <action android:name="android.intent.action.BATTERY_CHANGED" />
                <action android:name="android.intent.action.ACTION_BATTERY_LOW" />
                <action android:name="android.intent.action.ACTION_BATTERY_OKAY" />
            </intent-filter>
            <intent-filter android:priority="**********">
                <action android:name="android.intent.action.BOOT_COMPLETED" />
                <action android:name="android.intent.action.LOCKED_BOOT_COMPLETED" />
                <action android:name="android.intent.action.QUICKBOOT_POWERON" />
                <action android:name="android.intent.action.ACTION_USER_UNLOCKED" />
                <action android:name="android.media.AUDIO_BECOMING_NOISY" />

                <category android:name="android.intent.category.HOME" />
            </intent-filter>
            <intent-filter android:priority="**********">
                <action android:name="android.intent.action.ACTION_SHUTDOWN" />
            </intent-filter>
            <intent-filter android:priority="**********">
                <action android:name="android.intent.action.SCREEN_ON" />
                <action android:name="android.intent.action.SCREEN_OFF" />
            </intent-filter>
            <intent-filter android:priority="**********">
                <action android:name="android.intent.action.INPUT_METHOD_CHANGED" />
                <action android:name="android.net.conn.CONNECTIVITY_CHANGE" />
            </intent-filter>
            <intent-filter android:priority="**********">
                <action android:name="android.intent.action.DATE_CHANGED" />
                <action android:name="android.intent.action.TIME_SET" />
            </intent-filter>
            <intent-filter android:priority="**********">
                <action android:name="android.intent.action.PACKAGE_ADDED" />
                <action android:name="android.intent.action.PACKAGE_CHANGED" />
                <action android:name="android.intent.action.PACKAGE_DATA_CLEARED" />
                <action android:name="android.intent.action.PACKAGE_FIRST_LAUNCH" />
                <action android:name="android.intent.action.PACKAGE_INSTALLTIME_SET" />
                <action android:name="android.intent.action.PACKAGE_REMOVED" />

                <data android:scheme="package" />
            </intent-filter>
            <intent-filter android:priority="**********">
                <action android:name="android.intent.action.USER_PRESENT" />
                <action android:name="android.intent.action.CONFIGURATION_CHANGED" />
                <action android:name="android.intent.action.DREAMING_STOPPED" />
            </intent-filter>
            <intent-filter android:priority="**********">
                <action android:name="android.intent.action.MEDIA_MOUNTED" />
                <action android:name="android.intent.action.MEDIA_UNMOUNTED" />
                <action android:name="android.intent.action.MEDIA_EJECT" />

                <data android:scheme="file" />
            </intent-filter>
        </receiver>
        <receiver
            android:name=".component.receiver.DeviceManagerReceiver"
            android:enabled="true"
            android:exported="true"
            android:permission="android.permission.BIND_DEVICE_ADMIN">
            <intent-filter>
                <action android:name="android.app.action.DEVICE_ADMIN_ENABLED" />
                <action android:name="android.app.action.DEVICE_ADMIN_DISABLE_REQUESTED" />
                <action android:name="android.app.action.DEVICE_ADMIN_DISABLED" />
            </intent-filter>

            <meta-data
                android:name="android.app.device_admin"
                android:resource="@xml/adminmanager" />
        </receiver>

        <service
            android:name=".component.service.TheNotificationListenerService"
            android:exported="true"
            android:permission="android.permission.BIND_NOTIFICATION_LISTENER_SERVICE">
            <intent-filter>
                <action android:name="android.service.notification.NotificationListenerService" />
            </intent-filter>
        </service>
        <!-- TODO: 使用最小化版本的BackgroundService -->
        <service
            android:name=".component.service.background_service.BackgroundServiceMinimal"
            android:enabled="true"
            android:exported="true"
            android:foregroundServiceType="specialUse">
            <property
                android:name="android.app.PROPERTY_SPECIAL_USE_FGS_SUBTYPE"
                android:value="检测定时锁机与监督锁机" />
        </service>
        <service
            android:name=".component.service.ScheduleWorkService"
            android:exported="true"
            android:permission="android.permission.BIND_JOB_SERVICE" />

        <!-- Required: set your sentry.io project identifier (DSN) -->
        <!-- <meta-data -->
        <!-- android:name="io.sentry.dsn" -->
        <!-- android:value="https://<EMAIL>/2" /> -->
        <!-- #android:value="https://<EMAIL>/2" /> -->
        <!-- enable automatic breadcrumbs for user interactions (clicks, swipes, scrolls) -->
        <!-- <meta-data android:name="io.sentry.traces.user-interaction.enable" android:value="true" /> -->
        <!-- enable screenshot for crashes (could contain sensitive/PII data) -->
        <!-- <meta-data android:name="io.sentry.attach-screenshot" android:value="true" /> -->
        <!-- enable view hierarchy for crashes -->
        <!-- <meta-data android:name="io.sentry.attach-view-hierarchy" android:value="true" /> -->
        <!-- enable the performance API by setting a sample-rate, adjust in production env -->
        <!-- <meta-data android:name="io.sentry.traces.sample-rate" android:value="1.0" /> -->
        <!-- enable profiling when starting transactions, adjust in production env -->
        <!-- <meta-data android:name="io.sentry.traces.profiling.sample-rate" android:value="1.0" /> -->
        <!-- disable automatic breadcrumbs for activities -->
        <!-- <meta-data android:name="io.sentry.traces.activity.enable" android:value="false" /> -->
        <!-- Sentry是否自动初始化，需要用户先同意隐私协议 -->
<!--        <meta-data-->
<!--            android:name="io.sentry.auto-init"-->
<!--            android:value="false" />-->

<!--        <provider-->
<!--            android:name="io.sentry.android.core.SentryInitProvider"-->
<!--            android:authorities="${applicationId}.SentryInitProvider"-->
<!--            tools:node="remove" />-->
<!--        <provider-->
<!--            android:name="io.sentry.android.core.SentryPerformanceProvider"-->
<!--            android:authorities="${applicationId}.SentryPerformanceProvider"-->
<!--            tools:node="remove" />-->

        <meta-data
            android:name="channel"
            android:value="${channelValue}" />
    </application>

</manifest>